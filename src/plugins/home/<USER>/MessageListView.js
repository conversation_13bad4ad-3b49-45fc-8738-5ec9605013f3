import React, { PureComponent } from 'react'
import { app, app as api } from '@ekuaibao/whispered'
import { toast } from '../../../lib/util'
import isEmpty from 'lodash/isEmpty'
import MessageListBottom from './MessageListBottom'
import styles from './MessageListView.module.less'
import { handlePrint } from './util'
import <PERSON>hanceTitleHook from '../../../lib/EnhanceTitleHook'
import { getV } from '../../../lib/help'

const title = props => {
  const { params } = props
  const { type } = params
  if (type === 'printList') return i18n.get('需打印单据')
  if (!window.isNewHome) return i18n.get('需确认单据')
}

@EnhanceTitleHook(title)
export default class MessageListView extends PureComponent {
  constructor(props) {
    super(props)
    const { params } = this.props
    const { type } = params
    this.state = {
      type,
      selectedDataMap: {},
      selectedAll: false,
      dataList: [],
      paidList: [],
      printList: [],
      start: 0,
      billListCount: 0,
      showSkeleton: true,
      showPrintBtn: false
    }

    this.BillListWrapper = api.require('@bill/bill-content/BillListWrapper')
  }

  componentWillMount() {
    api.watch('refresh:messageListView', this._getDataList)
    this._getDataList()
    api.invokeService('@bill:get:show:print:invoice').then(result => {
      this.setState({ showPrintBtn: result?.value || false })
    })
  }

  componentWillUnmount() {
    api.un('refresh:messageListView', this._getDataList)
  }

  _getDataList = (filterBy = '', selectedKeys = []) => {
    const { type, start, dataList } = this.state
    this.setState({ showSkeleton: start === 0 ? true : false }, () => {
      if (type === 'printList') {
        return api.invokeService('@common:get:print:bill', { limit: { start, count: 20 } }).then(data => {
          const { items = [] } = data
          this.setState({ billListCount: data?.count, start: start + items?.length })
          let dataConcat = dataList.concat(items)
          this._updateData(dataConcat)
        })
      }
      return api
        .invokeService('@common:search:bill', {
          hasArchived: true,
          limit: { start, count: 20 },
          filterBy: filterBy || 'state == "paid"',
          orderBy: [{ value: 'updateTime', order: 'DESC' }]
        })
        .then(result => {
          const arr = result.items || []
          this.setState({ billListCount: result?.count, start: start + arr?.length })
          const paidList = [...arr]
          paidList.forEach(item => {
            const id1 = getV(item, 'id', '')
            const id2 = getV(item, 'flow.id', '')
            const id = id1 || id2
            item.id = id
          })
          let data = dataList.concat(paidList)
          if (selectedKeys?.length) {
            // 过滤掉已经确认过的
            const selectedKeyMap = selectedKeys.reduce((result, key) => {
              result[key] = key
              return result
            }, {})
            data = data?.filter(item => !selectedKeyMap[item?.flow?.id])
          }
          this._updateData(data)
        })
    })
  }

  _updateData = dataList => {
    this.setState({ dataList: dataList, selectedDataMap: {}, selectedAll: false, showSkeleton: false }, () =>
      this._updateTitle()
    )
  }

  _updateTitle = () => {
    const { dataList } = this.state
    const { params } = this.props
    const { type } = params
    let title = i18n.get('需确认单据')
    if (type === 'printList') title = i18n.get('需打印单据')
    title = title + i18n.get('(共{__k0}条)', { __k0: dataList.length })
    api.invokeService('@layout:change:header:title', title)
    if (window.isNewHome && type !== 'printList') {
      api.invokeService('@layout:change:header:title', i18n.get('已完成单据'))
      api.emit('refresh:segment:count', `${i18n.get('未确认单据')} ${dataList.length === 0 ? '' : dataList.length}`)
    }
  }

  _itemCheckBoxChange = value => {
    const { checked, data } = value
    const { dataList, selectedDataMap, selectedAll } = this.state
    const dataMap = { ...selectedDataMap }
    let selected = selectedAll
    if (checked) {
      dataMap[data.id] = data
      selected = Object.keys(dataMap).length === dataList.length
    } else {
      selected = false
      delete dataMap[data.id]
    }
    this.setState({ selectedDataMap: dataMap, selectedAll: selected })
  }

  _handleClickItem = flow => {
    const {pageType} = this.props
    if (pageType === 'unConfirmed'){
      api.invokeService('@bill:update:flow:append:info', { needConfigButton: true })
    }
    const { handleBillClickItem } = app.require('@home5/util/billItemHelper')
    handleBillClickItem({
      data: flow,
      id: flow.id,
      formType: flow.formType,
      state: flow?.flow?.state,
      isAlwaysPrint: true
    })
  }

  _handleSelectAllClick = selectAll => {
    let dataMap = {}
    if (selectAll) {
      const { dataList } = this.state
      dataList.forEach(element => (dataMap[element.id] = element))
    }
    this.setState({ selectedAll: selectAll, selectedDataMap: dataMap })
  }

  _handleButtonAction = () => {
    const { selectedDataMap, type } = this.state
    if (isEmpty(selectedDataMap)) {
      toast.info(i18n.get('请选择单据'))
      return
    }
    const ids = Object.keys(selectedDataMap)
    if (type === 'printList') {
      return this._printBill(ids, selectedDataMap, '0')
    }
    return this._confirmBill(ids)
  }

  _handleButtonActionInvoice = () => {
    const { selectedDataMap, type } = this.state
    if (isEmpty(selectedDataMap)) {
      toast.info(i18n.get('请选择单据'))
      return
    }
    const ids = Object.keys(selectedDataMap)
    if (type === 'printList') {
      return this._printBill(ids, selectedDataMap, '1')
    }
    return this._confirmBill(ids)
  }

  _confirmBill = selectedKeys => {
    api.invokeService('@approve:do:confirm', selectedKeys, { name: 'freeflow.archive' }).then(_ => {
      this._getDataList(undefined, selectedKeys)
    })
  }

  _printBill = (selectedKeys, selectedMap, printInvoice) => {
    handlePrint(selectedKeys, selectedMap, () => this._getDataList.call(this), printInvoice)
  }

  handleScroll = () => {
    const { start, billListCount } = this.state
    if (start >= billListCount) {
      return null
    }
    this._getDataList()
  }

  render() {
    const { type, dataList, selectedDataMap, selectedAll, showSkeleton, showPrintBtn } = this.state
    const { isAlwaysPrint } = this.props
    const count = Object.keys(selectedDataMap).length
    const label = type === 'printList' ? i18n.get('打印单据') : i18n.get('确 认')
    const current_showPrintBtn = showPrintBtn && type === 'printList' ? true : false
    return (
      <div className={styles.message_list_wrap}>
        <this.BillListWrapper
          style={{ paddingTop: 8 }}
          selectAble={true}
          isAlwaysPrint={isAlwaysPrint}
          className={styles.list_wrapper}
          selectedDataMap={selectedDataMap}
          checkBoxChange={this._itemCheckBoxChange}
          itemClick={this._handleClickItem}
          showSkeleton={showSkeleton}
          billList={dataList}
          needOperatorFormPlan={type === 'printList'}
          onEndReachedThreshold={68}
          onEndReached={this.handleScroll}
          hideAvatar={true}
          flowType={'flow'}
          useNewStyle={true}
        />
        <MessageListBottom
          type={type}
          selectedAll={selectedAll}
          buttonAction={this._handleButtonAction}
          label={label}
          count={count}
          selectAllClick={this._handleSelectAllClick}
          showPrintBtn={current_showPrintBtn}
          buttonActionInvoice={this._handleButtonActionInvoice}
        />
      </div>
    )
  }
}
