import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './home-create-modal.module.less'
import { app as api } from '@ekuaibao/whispered'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import SpecGroupList from '../elements/SpecGroupList'

const i18n = window.i18n

@EnhanceConnect(state => ({
  specObj: state['@home'].expenseSpecAfterFiltered
}))
@EnhanceTitleHook(i18n.get('选择单据模板'))
export default class HomeCreateModal extends PureComponent {
  handleOK = spec => {
    //从新建走，要存到reducer里
    //从组件中进来，直接返回模板信息
    const { fromComponent } = this.props
    api.invokeService('@home:save:specification', spec).then(_ => {
      if (fromComponent) {
        this.props.layer.emitOk(spec)
      } else {
        api
          .invokeService('@home:save:specification:after:filtered', {
            type: 'filterFormType',
            formType: spec.type,
            specifications: []
          })
          .then(_ => {
            api.go(`/bill/${spec.type}`)
            this.props.layer.emitCancel()
          })
      }
    })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  renderContent(data) {
    return (
      <div className="homeCreactModal-content">
        <SpecGroupList
          data={data}
          fnSubmit={this.handleOK}
          currentSpecification={this.props.currentSpecification}
        />
      </div>
    )
  }

  renderFooter() {
    return (
      <div className="homeCreactModal-footer" onClick={this.handleCancel}>
        <svg className="icon" aria-hidden="true">
          <use xlinkHref="#EDico-close-default" />
        </svg>
      </div>
    )
  }

  fnFilterSpec = (data, specIds, formType) => {
    if (formType) {
      return data.map(section => {
        section.specifications = section.specifications.filter(spe => spe.type === formType)
        return section
      })
    }
    return data.map(section => {
      section.specifications = section.specifications.filter(spe => !!~specIds.indexOf(spe.originalId))
      return section
    })
  }

  render() {
    const { data, specObj } = this.props
    let specArr = data.map(section => {
      section.specifications = section.specifications.filter(spe => !spe?.id?.includes('system:对账单'))
      return section
    })
    if (specObj.type === 'byExpenseLink') {
      specArr = this.fnFilterSpec(data, specObj.specifications)
    }
    if (specObj.type === 'filterFormType') {
      specArr = this.fnFilterSpec(data, null, specObj.formType)
    }
    if (specObj.type === 'bySupply') {
      specArr = this.fnFilterSpec(data, specObj.specifications)
    }


    return (
      <div className={styles['homeCreactModal-wrapper']}>
        {this.renderContent(specArr)}
        {this.renderFooter()}
      </div>
    )
  }
}
