import { app as api } from '@ekuaibao/whispered'
import { Dialog } from '@hose/eui-mobile'
import get from 'lodash/get'

export function handlePrint(selectedKeys, selectedData, done, printInvoice) {
  api.invokeService('@approve:batch:print:bills', selectedKeys, selectedData, done, printInvoice)
}

//扫描发票
export function fnScanClick() {
  // window.Intercom && window.Intercom('trackEvent', 'scan-qr-code')
  api.loadPoint('@invoice', 'export:utils').then(_utils => {
    const { fnScanFapiao, fnCheckCorporationInfo, fnInvoiceDataResult } = _utils[0]
    fnScanFapiao({
      fnCheckCorporationInfo,
      fnInvoiceDataResult,
      enterType: 'check',
      hiddenOCR: true,
      unmount: () => {
        api.invokeService('@layout:set:header:icon', { showIcon: true })
      },
      mount: () => {
        api.invokeService('@layout:set:header:icon', { showIcon: false })
      }
    })
  })
}

// 检查企业开票信息
export function checkPayerInfo(fn) {
  const payerInfo = api.getState()['@common'].payerInfo
  const me_info = api.getState()['@common'].me_info
  if (payerInfo.length <= 0) {
    const isAdmin = !!me_info.permissions.find(el => el === 'SYS_ADMIN')
    if (isAdmin) {
      Dialog.confirm({
        title: i18n.get('无法验证'),
        content: i18n.get('无法使用验证发票功能，请前往设置开票信息'),
        cancelText: i18n.get('取消'),
        confirmText: i18n.get('去设置'),
        onPress: () => api.go('/mine/corporation-info')
      })
    } else {
      Dialog.alert({
        title: i18n.get('无法验证'),
        content: i18n.get('无法使用验证发票功能，请联系管理员设置企业开票信息'),
        confirmText: i18n.get('确定')
      })
    }
    return false
  }
  fn && fn()
}

export const fnCreateRecordExpends = () => {
  api.go('/record-expends', false)
}

//私车公用跳转逻辑
export function fnClickMyCarBusiness(data) {
  const myCarBusinessPower = api.getState()['@mycarbusiness'].myCarBusinessPower
  const privateData = get(data, 'detail.data')

  const { isFrist, state, hasRecord, haveStartPower, message } = privateData || myCarBusinessPower
  if (isFrist) {
    //说明是第一次进来
    //然后可以在公告页面拿note进行展示
    api.go('/routenote')
  } else if (state === 'RUNNING' || state === 'END') {
    const wayPointsConfig = privateData?.wayPointsConfig || myCarBusinessPower?.wayPointsConfig
    if (haveStartPower !== undefined && !haveStartPower && !wayPointsConfig) {
      setTimeout(() => {
        Dialog.alert({ title: i18n.get('提示'), content: message })
      }, 200)
      return
    }
    //未生成明细的的，去进行行程记录调整
    api.go('recordingtrip')
  } else {
    //去记录列表(要判断能否产生新的记录 haveStartPower)
    if (!hasRecord) return api.go('/recordingtrip')
    api.go('routelist')
  }
}
