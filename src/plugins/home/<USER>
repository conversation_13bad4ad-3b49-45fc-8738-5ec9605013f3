import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by nanyuantingfeng on 8/16/16 17:42.
 **************************************************/
import { Resource } from '@ekuaibao/fetch'
import { Actions, action, node, reducer } from '@ekuaibao/store'
import { ID } from './key'
import { QuerySelect } from 'ekbc-query-builder'
// import { remunerationSetting } from '../../elements/puppet/Remuneration/actions'
// const { remunerationSetting } = app.require('@elements/puppet/Remuneration/actions')

const loanAction = new Resource('/api/v1/loan/loanInfo')
const repaymentAction = new Resource('/api/v1/loan/repayment')
const print = new Resource('/api/v1/print/template')
const printCount = new Resource('/api/v1/print/remind/count')
const specification = new Resource('/api/form/v2/specificationGroups')
const associationGroupData = new Resource('/api/v1/association/group/list')
const associationSpeciafications = new Resource('/api/form/v2/specifications')

class HomeAction extends Actions {
  static pluginName = ID

  @reducer(action => {
    let specification_list = []
    let specification_name_map = {}
    const specification_map = {}
    const specification_EN_map = {}
    let specification_group_list =
      action && action.payload && action.payload.items
        ? action.payload.items.map(group => {
            specification_name_map[group.id] = group.name
            specification_EN_map[group.id] = group.enName || group.name
            specification_list = specification_list.concat(group.specifications)
            const name = i18n.currentLocale === 'en-US' && group.enName ? group.enName : group.name
            return { label: name, enName: group.enName , type: group.id, checkVisible: true }
          })
        : []
    specification_list.forEach(sp => {
      specification_map[sp.originalId] = sp
    })
    return {
      specification_group: action?.payload?.items || [],
      specification_group_list,
      specification_name_map,
      specification_list,
      specification_map,
      specification_EN_map
    }
  })
  @node('specificationWithVersion', {
    specification_group: [],
    specification_group_list: [],
    specification_name_map: {},
    specification_list: []
  })
  @action
  getSpecificationWithVersion() {
    const data = {
      join: `specifications.components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`,
      join$1: `specifications.components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`,
      join$2: `specifications.components.appletAssignmentRule,appletAssignmentRule,/v1/mapping/fieldMapping`
    }
    return {
      payload: specification.GET('/withSpecificationVersioned', data)
    }
  }

  @reducer(action => {
    let specification_list = []
    action?.payload?.items?.forEach(group => {
      specification_list = specification_list.concat(group.specifications)
    })
    return {
      specification_list,
      specification_group: action?.payload?.items || []
    }
  })
  @node('delegationSpecificationWithVersion', {
    specification_list: [],
    specification_group: [],
  })
  @action
  getDelegationSpecificationWithVersion(params={}) {
    const { delegation = false } = params
    const data = {
      join: `specifications.components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`,
      join$1: `specifications.components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`,
      delegation
    }
    return {
      payload: specification.GET('/withSpecificationVersioned', data),
    }
  }

  @reducer(action => {
    let specification_list = []
    let specification_name_map = {}
    const specification_map = {}
    let specification_group_list =
      action && action.payload && action.payload.items
        ? action.payload.items.map(group => {
            specification_name_map[group.id] = group.name
            specification_list = specification_list.concat(group.specifications)
            return { label: group.name, type: group.id, checkVisible: true }
          })
        : []
    specification_list.forEach(sp => {
      specification_map[sp.originalId] = sp
    })
    return {
      requi_specification_group: action?.payload?.items || [],
      requi_specification_group_list: specification_group_list,
      requi_specification_name_map: specification_name_map,
      requi_specification_list: specification_list,
      requi_specification_map: specification_map
    }
  })
  @node('requiSpecificationWithVersion', {
    specification_group: [],
    specification_group_list: [],
    specification_name_map: {},
    specification_list: []
  })
  @action
  getRequiSpecificationWithVersion(param = {}) {
    const data = {
      ...param,
      join: `specifications.components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`,
      join$1: `specifications.components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`
    }
    return {
      payload: specification.GET('/withRequisitionSpecificationVersioned', data)
    }
  }

  @reducer(action => action.payload.items || [])
  @node('specification_recently')
  @action
  getSpecificationRecently() {
    const data = {
      join: `components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`,
      join$1: `components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`
    }
    return { payload: specification.GET('/recentlyUsedSpecification', data) }
  }

  @reducer(action => action.payload.items || [])
  @node('requi_specification_recently')
  @action
  getRequiSpecificationRecently(param) {
    const data = {
      ...param,
      join: `components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`,
      join$1: `components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`
    }
    return { payload: specification.GET('/recentlyUsedRequisitionSpecification', data) }
  }

  @reducer(action => action.payload)
  @node('expenseSpecAfterFiltered', {})
  @action
  saveSpecAfterFiltered(specIds) {
    return { payload: specIds }
  }

  @reducer(action => action.payload)
  @node('specification_current')
  @action
  saveCurrentSpecification(spec) {
    return { payload: spec }
  }

  @reducer(action => action.payload.value)
  @node('loanMoney')
  @action
  getOwnBalanceAmount() {
    return {
      payload: loanAction.GET('/mine/balance')
    }
  }

  @action
  listLoanPackage(param) {
    const { state = 'REPAID', start = 0, count = 999 } = param || {}
    const data = new QuerySelect()
      .limit(start, count)
      .filterBy(`state.in("${state}")`)
      .value()
    return {
      payload: loanAction.POST('/mine/byState', data)
    }
  }

  @action
  getRepaymentRecordByState(state) {
    return {
      payload: repaymentAction.GET('/mine/byState', { state })
    }
  }

  @reducer(action => action.payload)
  @node('billType')
  @action
  setSelectBillsType(type) {
    return { payload: type }
  }

  @action
  getPrintTemplateList(ids, done) {
    return {
      payload: print.GET('/[ids]', { ids }),
      done
    }
  }

  @action
  getPrintRemindCount() {
    return { payload: printCount.GET() }
  }

  @reducer(action => action.payload)
  @node('remunerationSetting', {})
  @action
  getRemunerationSetting() {
    const { remunerationSetting } = app.require('@elements/puppet/Remuneration/actions')
    return { payload: remunerationSetting() }
  }

  @reducer(action => {
    return action?.payload?.items
  })
  @node('assiciationSpecifications', [])
  @action
  getAssociationSpecifications(type) {
    const data = {
      type,
      join: `components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`,
      join$1: `components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`
    }
    return {
      payload: associationSpeciafications.GET(`/getSpecificationVersionListByType/$type`, data)
    }
  }

  @reducer(action => {
    return action?.payload?.value
  })
  @node('assiciationGroupData', {})
  @action
  getAssociationGroupData() {
    return {
      payload: associationGroupData.GET('')
    }
  }

  @reducer(action => {
    return action?.payload?.items || []
  })
  @node('specificationGroupsByType', {})
  @action
  getSpecificationGroupsByType(type) {
    const params = {
      join: `specifications.components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`,
      join$1: `specifications.components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`,
      type
    }
    return {
      payload: specification.GET('/getCorporatebankingSpecification', params)
    }
  }
}

const homeAction = new HomeAction()

export default homeAction
