/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/15 下午3:32
 */

import React, { PureComponent } from 'react'

import { app as api } from '@ekuaibao/whispered'
import { getV } from '../../lib/help'
const BillList = api.require('@bill/bill-content/bill-list')
import styles from './home-confirm.module.less'
import get from 'lodash/get'

import <PERSON>hanceTitleHook from '../../lib/EnhanceTitleHook'

@EnhanceTitleHook(i18n.get('最近通过'))
class HomeConfirm extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      start: 0,
      paidList: [],
      billListCount: 0
    }
  }

  componentWillMount() {
    this.getPaidList()
  }

  getPaidList = () => {
    const { start, paidList } = this.state
    api
      .invokeService('@common:search:bill', {
        hasArchived: true,
        limit: { start, count: 20 },
        filterBy: 'state == "paid"',
        orderBy: [{ value: 'updateTime', order: 'DESC' }]
      })
      .then(result => {
        const arr = result?.items || []
        this.setState({ paidList: paidList.concat(arr), billListCount: result?.count, start: start + arr?.length })
      })
  }

  changePaidToArchived = paidList => {
    //改变单据状态
    const ids = paidList.map(line => {
      return line.id || getV(line, 'flow.id', '')
    })
    if (ids.length > 0) {
      api.invokeService('@approve:do:confirm', ids, { name: 'freeflow.archive' }).then(api.go(-1))
    }
  }

  handleClickItem = flow => {
    api.invokeService('@home:click:bill', flow)
  }

  handleLoadMore = () => {
    this.getPaidList()
  }

  render() {
    let { paidList, billListCount } = this.state
    return (
      <div className={styles.home_paid_wrap}>
        <div className={styles.home_paid_content}>
          <BillList
            onHandleClick={this.handleClickItem.bind(this)}
            dataSource={paidList}
            customLoadMore={this.handleLoadMore}
            dataSourceTotal={billListCount}
          />
        </div>
        <div className={styles.home_paid_btn} onClick={this.changePaidToArchived.bind(this, paidList)}>
          {i18n.get('全部确认')}
        </div>
      </div>
    )
  }
}

export default HomeConfirm
