/************************************************
 * Created By nanyuantingfeng On 6/6/16 11:21.
 ************************************************/
import { app, app as api } from '@ekuaibao/whispered'
import { isRepaymentDateTip } from '../../lib/util'

import actions from './home.action'
import {startOpenFlowPerformanceStatistics} from "../../lib/flowPerformanceStatistics";

export default [
  {
    id: '@home',
    reducer: () => actions.getReducer(),

    async onafter() {},

    async 'get:remuneration:setting'() {
      api.dispatch(actions.getRemunerationSetting())
    },

    async 'get:loan:amount'() {
      api.dispatch(actions.getOwnBalanceAmount())
    },

    async 'get:loan:tip'() {
      return api.dispatch(actions.getRepaymentRecordByState('REJECT')).then(rejectData => {
        if (rejectData.items.length) {
          return new Promise(resolve => {
            resolve(true)
          })
        }
        return api.dispatch(actions.listLoanPackage({ state: 'REPAID', start: 0, count: 9999 })).then(loans => {
          return new Promise(resolve => {
            let items = loans.items
            let loanExpired = false
            for (let index = 0; index < items.length; index++) {
              let item = items[index]
              loanExpired = isRepaymentDateTip(item.repaymentDate)
              if (loanExpired) {
                break
              }
            }
            resolve(loanExpired)
          })
        })
      })
    },

    'click:bill'(flow, sourcePage) {
      const fnGoEdit = flow => {
        let { id, formType } = flow
        api.go(`/bill/${formType}/${id}`)
        startOpenFlowPerformanceStatistics()
      }

      const fnGoRejected = flow => {
        let { id, formType } = flow
        api.go(`/rejected/${formType}/${id}`)
        startOpenFlowPerformanceStatistics()
      }

      const fnGoDetail = flow => {
        let router = '/detail/' + flow.id
        if (sourcePage) {
          router = router + '/' + sourcePage
        }
        if (flow.isAlwaysPrint) {
          router = '/paiddetail/' + flow.id + '/' + flow.isAlwaysPrint + '/' + sourcePage
        }
        api.go(router)
        startOpenFlowPerformanceStatistics()
      }
      switch (flow.state) {
        case 'draft':
          fnGoEdit(flow)
          break
        case 'rejected':
          fnGoRejected(flow)
          break
        case 'approving':
        case 'pending':
        case 'paying':
        case 'paid':
        case 'archived':
          fnGoDetail(flow)
          break
        default:
          fnGoDetail(flow)
      }
    },

    'get:print:template': async args => {
      return api.dispatch(actions.getPrintTemplateList(args))
    },

    'get:specification:with:version': async () => {
      return api.dataLoader('@home.specificationWithVersion').load()
    },

    'get:specification:with:version:reload': async () => {
      return api.dataLoader('@home.specificationWithVersion').reload()
    },

    'get:specification:recently': async () => {
      return api.dispatch(actions.getSpecificationRecently())
    },

    'get:requisition:specification:recently': async param => {
      return api.dispatch(actions.getRequiSpecificationRecently(param))
    },

    'save:specification': async spec => {
      if (!spec) return Promise.resolve()
      return api.dispatch(actions.saveCurrentSpecification(spec)).then(async spec => {
        const { isShowRemuneration } = app.require('@elements/puppet/Remuneration/actions')
        spec.remuneration = await isShowRemuneration(spec.id)
        return spec
      })
    },

    'save:specification:after:filtered': async specIds => {
      return api.dispatch(actions.saveSpecAfterFiltered(specIds))
    },

    setSelectBillsType: async type => {
      return api.dispatch(actions.setSelectBillsType(type))
    },

    'get:association:specifications': async type => {
      return api.dispatch(actions.getAssociationSpecifications(type))
    },

    'get:association:group:data': async () => {
      return api.dispatch(actions.getAssociationGroupData())
    },
    'get:specificationGroups:byType'(type) {
      return api.dispatch(actions.getSpecificationGroupsByType(type))
    }
  },

  {
    point: '@@layers',
    prefix: '@home',
    onload: () => require('./layers').default
  },

  {
    point: '@@menus',
    onload: () => ({
      label: () => i18n.get('首页'),
      href: '/home',
      img: [app.require('@images/home-default.png'), app.require('@images/home-on.png')],
      redDot: false,
      weight: 1
    })
  },

  {
    path: '/home/<USER>/:type',
    ref: '/',
    onload: () => import('./home-confirm')
  },

  {
    path: '/home/<USER>/:type',
    ref: '/',
    onload: () => import('./elements/MessageListView')
  },

  {
    path: '/home/<USER>',
    ref: '/',
    onload: () => import('./elements/ExpressReceiveListView')
  },
  {
    path: '/message/pc/:type',
    ref: '/',
    onload: () => import('./message-pc')
  },
  {
    resource: '@home',
    value: {
      ['elements/MessageListView']: require('./elements/MessageListView').default,
      ['elements/util']: require('./elements/util')
    }
  }
]
