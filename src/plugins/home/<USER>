import { app } from '@ekuaibao/whispered'
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/7 下午6:19
 */
import React, { PureComponent } from 'react'
// import EKBIcon from '../../elements/ekbIcon'
const EKBIcon = app.require('@elements/ekbIcon')
import styles from './home-head-new.module.less'
import { app as api } from '@ekuaibao/whispered'
// import { thousandBitSeparator } from '../../components/utils/fnThousandBitSeparator'
const { thousandBitSeparator } = app.require('@components/utils/fnThousandBitSeparator')
import GIF_ON_THE_WAY from './images/onTheWay1.gif'
import { addMapJsApi } from '../../lib/mapjsapi'
import { fnScanClick, fnClickMyCarBusiness } from './elements/util'

const handleGotoMyLoan = () => {
  api.go('loanpackage')
}

const handleToMyApply = () => {
  api.go('/requisition')
}

function MyCarBussiness(props) {
  const { state } = props.myCarBusinessPower

  //有正在进行中的
  return state === 'RUNNING' ? (
    <div className="vertical_box" onClick={fnClickMyCarBusiness}>
      <img className="motion" src={GIF_ON_THE_WAY} />
      <div className="motion_text">{i18n.get('行程中')}</div>
    </div>
  ) : (
    <div className="vertical_box" onClick={fnClickMyCarBusiness}>
      <EKBIcon name="#EDico-taxi" style={{ color: 'var(--brand-base)' }} />
      <div>{i18n.get('用车补贴')}</div>
    </div>
  )
}

const HomeHeadBottom = props => {
  const { myCarBusinessPower = {}, handleShowPop } = props
  const { active = false, state } = myCarBusinessPower
  if (active || state === 'RUNNING') {
    setTimeout(addMapJsApi, 0)
    return (
      <div className="home_head_bottom_vertical home_head_box">
        <div className="vertical_box" onClick={handleShowPop}>
          <EKBIcon name="#EDico-bill" style={{ color: 'rgb(24, 144, 255)' }} />
          <div>{i18n.get('手动校验')}</div>
        </div>
        <div className="vertical_box" onClick={fnScanClick}>
          <EKBIcon name="#EDico-scan" style={{ color: 'rgb(82, 196, 26)' }} />
          <div>{i18n.get('扫码查询')}</div>
        </div>
        <MyCarBussiness myCarBusinessPower={myCarBusinessPower} />
      </div>
    )
  }
  return (
    <div className="home_head_bottom home_head_box">
      <div className={'box_left_bottom'} onClick={handleShowPop}>
        <EKBIcon name="#EDico-bill" />
        <div>{i18n.get('手动校验')}</div>
      </div>
      <div className="box_right">
        <div className="box_right_bottom_wrap" onClick={fnScanClick}>
          <EKBIcon name="#EDico-scan" />
          <div>{i18n.get('扫码查询')}</div>
        </div>
      </div>
    </div>
  )
}

const HomeHead = props => {
  let { loanMoney, requisitionLength, standardCurrency = {}, handleShowPop, myCarBusinessPower } = props

  return (
    <div className={styles.home_head}>
      <div className="home_head_top home_head_box">
        <div className="box_left" onClick={handleGotoMyLoan}>
          <div className="title">
            <span className="symbol" style={{ marginRight: 4 }}>
              {standardCurrency.symbol}
            </span>
            {thousandBitSeparator(new Big(loanMoney).toFixed(standardCurrency.scale))}
          </div>
          <div className="sub_title">{window.IS_SMG ? i18n.get('我的预支') : i18n.get('我的借款')}</div>
        </div>
        <div className="box_right" onClick={handleToMyApply.bind(this, requisitionLength)}>
          <div className="box_right_wrap">
            <div className="title">
              {requisitionLength}
              <span className="symbol" style={{ marginLeft: 4 }}>
                {i18n.get('条')}
              </span>
            </div>
            <div className="sub_title">{i18n.get('申请事项')}</div>
          </div>
        </div>
      </div>
      <HomeHeadBottom myCarBusinessPower={myCarBusinessPower} handleShowPop={handleShowPop} />
    </div>
  )
}

HomeHead.defaultProps = {
  loanMoney: 0,
  requisitionLength: 0
}

export default HomeHead
