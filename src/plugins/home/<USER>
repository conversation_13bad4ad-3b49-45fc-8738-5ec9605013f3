/**************************************************
 * Created by nanyuanting<PERSON> on 8/30/16 14:32.
 **************************************************/
import { Reducer } from '@ekuaibao/store'
import key from './key'

const reducer = new Reducer(key.ID, {
  flow_actives: {},
  billType: '',
  specification_current: '',
  specification_group: [],
  specification_recently: [],
  expenseSpecAfterFiltered: {},
  remunerationSetting: {}
})

reducer.handle(key.BALANCE_OWN_AMOUNT, (state, action) => {
  if (action.error) {
    return state
  }
  let loanMoney = action.payload.value
  return { ...state, loanMoney: loanMoney }
})

reducer.handle(key.LOANPACKAGE_LIST, (state, action) => {
  return state
})

reducer.handle(key.GET_REPAYMENT_RECORD, (state, action) => {
  return state
})

reducer.handle(key.SET_BILL_TYPE, (state, action) => {
  return { ...state, billType: action.payload }
})

reducer.handle(key.GET_PRINT_TEMPLATE_LIST)((state, action) => {
  return { ...state }
})

reducer.handle(key.GET_PRINT_REMIND_COUNT)((state, action) => {
  return { ...state }
})

reducer.handle(key.GET_SPECIFICATION_WITH_VERSION)((state, action) => {
  if (action.error) {
    return state
  }

  let specification_list = []
  let specification_name_map = {}
  const specification_EN_map = {}
  let specification_group_list =
    action && action.payload && action.payload.items
      ? action.payload.items.map(group => {
        specification_name_map[group.id] = group.name
        specification_EN_map[group.id] = group.enName || group.name
        specification_list = specification_list.concat(group.specifications)
        const name = i18n.currentLocale === 'en-US' && group.enName ? group.enName : group.name
        return { label: name, enName: group.enName , type: group.id, checkVisible: true }
      })
      : []
  return {
    ...state,
    specification_group: action?.payload?.items || [],
    specification_group_list,
    specification_name_map,
    specification_list,
    specification_EN_map
  }
})

reducer.handle(key.GET_SPECIFICATION_RECENTLY)((state, action) => {
  let recentlyList = []
  if (action.payload.items) {
    //结算单和报销单不允许添加
    recentlyList = action.payload.items.filter(v => v.type !== 'reconciliation' && v.type !== 'settlement')
  }
  return { ...state, specification_recently: recentlyList }
})

reducer.handle(key.SAVE_CURRENT_SPECIFICATION)((state, action) => {
  return { ...state, specification_current: action.payload }
})

reducer.handle(key.SAVE_SPEC_AFTER_FILTERED)((state, action) => {
  return { ...state, expenseSpecAfterFiltered: action.payload }
})

reducer.handle(key.GET_REMUNERATION_SETTING)((state, action) => {
  return { ...state, remunerationSetting: action.payload }
})

export default reducer
