/**
 * Created by <PERSON><PERSON> on 2018/9/5
 */
@import '../../styles/ekb-colors';

.home_head {
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 32px;
  border-radius: 12px;
  box-shadow: 0 2px 32px 0 var(--brand-1);
  font-size: 28px;
  :global {
    .home_head_box {
      height: 174px;
      display: flex;
      flex-direction: row;
      .box_left {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
      }
      .box_right {
        flex: 1;
        position: relative;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        justify-content: center;
        .box_right_wrap {
          width: 100%;
          padding-left: 25%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;
        }
        .box_right_bottom_wrap {
          width: 100%;
          padding-left: 25%;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          .icon {
            flex-shrink: 0;
            margin-left: -4px;
            padding-left: 0;
            fill: rgb(82, 196, 26);
          }
        }
      }
      .box_left_bottom {
        flex: 1;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        .icon {
          fill: rgb(24, 144, 255);
        }
      }
    }
    .home_head_top {
      margin: 0 48px;
      flex: 1;
      border-bottom: 2px solid rgba(0, 0, 0, 0.04);
      .title {
        color: @black-85;
        line-height: 56px;
        font-size: 40px;
        font-weight: 500;
      }
      .symbol {
        font-size: 28px;
      }
      .sub_title {
        color: @black-45;
        line-height: 44px;
      }
    }
    .home_head_bottom {
      margin: 0 48px;
      height: 112px;
      color: @black-85;
      .icon {
        width: 48px;
        height: 48px;
        padding: 6px;
        margin-right: 4px;
      }
    }
    .home_head_bottom_vertical {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: 170px;
      color: @black-85;
      .vertical_box {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .motion {
          width: 48px;
          margin-bottom: 8px;
        }
        .motion_text {
          background-color: #1890ff;
          color: white;
          text-align: center;
          width: 116px;
          border-radius: 20px;
          animation: changeColor ease-in-out 1s infinite;
        }
        .icon {
          width: 48px;
          height: 48px;
          margin-bottom: 8px;
        }
      }
    }
  }
}
