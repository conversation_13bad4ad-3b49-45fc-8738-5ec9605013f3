/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/8 下午1:38
 */

import React from 'react'
import styles from './home-button-wrapper.module.less'

import PNG_HOME_NEW_EXPENSE from './images/home-new-expense.png'
import PNG_HOME_NEW_LOAN from './images/home-new-loan.png'
import PNG_HOME_NEW_APPLY from './images/home-new-apply.png'
import PNG_HOME_SCAN from './images/home-scan.png'

let ButtonArr = [
  {
    billType: 'expense',
    img: PNG_HOME_NEW_EXPENSE,
    text: i18n.get('新建报销')
  },
  {
    billType: 'loan',
    img: PNG_HOME_NEW_LOAN,
    text: i18n.get('新建借款')
  },
  {
    billType: 'requisition',
    img: PNG_HOME_NEW_APPLY,
    text: i18n.get('新建申请')
  },
  {
    billType: 'scan',
    img: PNG_HOME_SCAN,
    text: i18n.get('验证发票')
  }
]

const handleClick = (onClickBtn, billType) => {
  onClickBtn && onClickBtn(billType)
}

const ButtonItem = params => {
  let { img, text, onClick } = params
  return (
    <li onClick={onClick} className={styles.home_button_item}>
      <div className={styles.home_button_icon}>
        <img src={img} alt="" />
      </div>
      <div className={styles.home_button_text}>{text}</div>
    </li>
  )
}

const HomeButtonWrapper = props => {
  let { onClickBtn } = props
  return (
    <ul className={styles.home_button_wrapper}>
      {ButtonArr.map((el, idx) => {
        let { img, text, billType } = el
        return (
          <ButtonItem
            onClick={handleClick.bind(this, onClickBtn, billType)}
            key={idx}
            img={img}
            text={i18n.get(text)}
          />
        )
      })}
    </ul>
  )
}

export default HomeButtonWrapper
