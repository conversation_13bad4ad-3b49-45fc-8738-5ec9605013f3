/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/11 下午6:33
 */

import { info as enter } from './check-enter'
import { info as checkNumber } from './check-number'

const infoCancel = {
  title: i18n.get('取消'),
  type: 'cancel',
  onClick() {
    Popup.hide()
  }
}

const invoiceCheckTypes = [scan, enter, checkNumber, infoCancel]

import showCheckPopup from './showCheckPopup'

export default function() {
  return showCheckPopup(invoiceCheckTypes)
}
