import React, { Component } from 'react'
import <PERSON>hance<PERSON><PERSON>le<PERSON>ook from '../../lib/EnhanceTitleHook'
import './../message-center/message-detail.less'
import SVG_ERROR from './../message-center/images/message-error.svg'
// @ts-ignore
@EnhanceTitleHook(props => {
  const type = props?.params?.type || ''
  let title = '消息中心'
  if (type === 'amortizePlanList') {
    title = '摊销台账'
  }
  return title
})
class MessagePc extends Component {
  render() {
    return (
      <div className="message-detail">
        <div className="message-error">
          <img src={SVG_ERROR} className="detail-icon" />
          <div className="detail-error-content">请在电脑端进行查看</div>
        </div>
      </div>
    )
  }
}

export default MessagePc
