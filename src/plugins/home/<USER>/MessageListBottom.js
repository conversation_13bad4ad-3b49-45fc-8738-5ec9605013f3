import styles from './MessageListBottom.module.less'
import React from 'react'
import { Checkbox, Button } from '@hose/eui-mobile'

export default function(props) {
  const {
    selectedAll = false,
    selectAllClick = void 0,
    buttonAction = void 0,
    label = '',
    count = 0,
    showPrintBtn,
    buttonActionInvoice
  } = props
  const fnClick = () => {
    selectAllClick(!selectedAll)
  }
  return (
    <div className={styles.bottom_wrapper}>
      <Checkbox checked={selectedAll} onChange={fnClick}>
        {i18n.get('全选')}
      </Checkbox>
      <Button size="middle" onClick={buttonAction}>
        {label + i18n.get('(') + count + i18n.get(')')}
      </Button>
      {showPrintBtn && (
        <Button size="middle" onClick={buttonActionInvoice}>
          {i18n.get('打印单据和发票') + i18n.get('(') + count + i18n.get(')')}
        </Button>
      )}
    </div>
  )
}
