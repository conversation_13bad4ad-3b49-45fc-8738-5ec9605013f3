import styles from './createBtn.module.less'
import React, { PureComponent } from 'react'
import { app, app as api } from '@ekuaibao/whispered'
const OnceTip = app.require('@elements/puppet/onceTip/OnceTip')
import { noSpecificationAlert } from '../../../lib/util'
import { checkBillCache } from '../../../lib/checkBillCache'

export default class CreateBtn extends PureComponent {
  constructor(props) {
    super(props)
    this.userInfo = api.getState()['@common'].me_info.staff
    this.state = { loading: false }
  }

  handleCreateClick = async () => {
    if (this.state.loading) return
    const resultType = await checkBillCache()
    if (resultType !== 'cancel') return
    this.setState({ loading: true })
    api.invokeService('@home:save:specification:after:filtered', {})
    api.invokeService('@home:get:specification:with:version').then(res => {
      this.setState({ loading: false })
      const specifications = res.specification_group?.find(v => v.specifications?.length)
      if (!res.items.length || !specifications) return noSpecificationAlert()
      return api.open('@home:HomeCreateModal', { data: res.specification_group })
    })
  }

  render() {
    const text = i18n.get('点击可新建单据')
    const sessionKey = 'tutor_createBill'
    const arrowStyle = { bottom: 4, right: 24 }
    const style = { top: -60, right: -4 }
    const isFEISHU = window.__PLANTFORM__ === 'DING_TALK'
    const IPhoneX =
      /iphone/gi.test(navigator.userAgent) &&
      ((screen.height == 812 && screen.width == 375) || (screen.height == 896 && screen.width == 414)) //钉钉里判断是不是iphoneX、iphoneXs、iphone XR
    const cls = isFEISHU && IPhoneX ? styles['dingTalk-IPhoneX-createBtn'] : styles['createBtn-wrapper']
    return (
      <div className={cls}>
        <OnceTip userInfo={this.userInfo} text={text} sessionKey={sessionKey} style={style} arrowStyle={arrowStyle} />
        <div className="createBtn" onClick={this.handleCreateClick}>
          <svg className="icon" aria-hidden="true">
            <use xlinkHref="#EDico-plus-default" />
          </svg>
        </div>
      </div>
    )
  }
}
