/**************************************
 * Created By LinK On 2019/3/21 14:21.
 **************************************/
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { Accordion, List } from 'antd-mobile'
import { SearchBar } from '@hose/eui-mobile'
import Highlighter from 'react-highlight-words'
import { debounce } from 'lodash'
const getSpecificationIcon = api.require<any>('@elements/specificationIcon')
const { getSpecificationName } = api.require<any>('@bill/utils/billUtils')
import './SpecGroupList.less'

interface Props {
  data: any
  currentSpecification?: any
  fnSubmit: (param: any) => void
}

interface State {
  searchKeys: string
  searchedSpecificationGroups: any
  activeKeys: string[]
}
export default class SpecGroupList extends React.PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      searchKeys: '',
      searchedSpecificationGroups: props?.data ? [...props?.data] : [],
      activeKeys: this.getSpecificationGroupIds() || []
    }
  }

  getSpecificationGroupIds = () => {
    const groupIds: string[] = []
    const { data = [] } = this.props
    data.forEach((group: any) => {
      groupIds.push(group.id)
    })
    return groupIds
  }
  renderHighlight = (value: string) => (
    <Highlighter
      highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
      searchWords={[this.state.searchKeys]}
      textToHighlight={value || i18n.get('[无标题]')}
      autoEscape={true}
    />
  )

  handleSearch = (val: string) => {
    this.setState({ searchKeys: val })
    if (val) {
      debounce(() => this.onSearch(val), 200)
    } else {
      this.setState({
        searchedSpecificationGroups: this.props?.data ? [...this.props?.data] : [],
        activeKeys: this.getSpecificationGroupIds()
      })
    }
  }

  onSearch = (value: string) => {
    const searchedSpecificationGroups = this.props?.data || []
    // 搜索到的结果
    const searchedRes = [] as any
    // 精确匹配到的模版 id
    const searchedSpecificationIds: string[] = []
    // 过滤出需要展示的模版分组
    const filterGroup: string[] = []
    searchedSpecificationGroups.forEach((group: any) => {
      if (group.name.includes(value)) {
        searchedRes.push(group)
        filterGroup.push(group.id)
      } else {
        group.specifications.forEach((item: any) => {
          // 只是匹配到单据模版名称
          if (item.name.includes(value)) {
            filterGroup.push(group.id)
            searchedSpecificationIds.push(item.id)
          }
        })
      }
    })
    if (searchedSpecificationIds.length) {
      // 精确匹配到单据模版
      const specificationGroupsList = this.props?.data || []
      const notSearchedResList = specificationGroupsList.filter(
        (item: any) => !searchedRes.some((searchedResItem: any) => searchedResItem === item)
      )
      const SearchedResList = notSearchedResList.map((group: any) => {
        return {
          ...group,
          specifications: group.specifications.filter((item: any) => item.name.includes(value))
        }
      })
      this.setState({
        searchedSpecificationGroups: [...SearchedResList, ...searchedRes],
        activeKeys: filterGroup
      })
    } else {
      // 匹配到分组和单据模版或者都没有匹配到
      this.setState({ searchedSpecificationGroups: searchedRes, activeKeys: filterGroup })
    }
  }

  cancelSearch = () => {
    this.setState({
      searchKeys: '',
      searchedSpecificationGroups: this.props?.data ? [...this.props?.data] : [],
      activeKeys: this.getSpecificationGroupIds()
    })
  }

  clearSearch = () => {
    this.cancelSearch()
  }

  switchPanel = (key: string[]) => {
    this.setState({ activeKeys: key })
  }

  render() {
    const { data, fnSubmit, currentSpecification } = this.props
    const { searchedSpecificationGroups, activeKeys } = this.state
    if (!data || !data.length) {
      return null
    }
    return (
      <div className={'home-create-modal-wrapper'}>
        <div className='eui-search-bar-wrapper'>
          <SearchBar
            showCancelButton
            placeholder={i18n.get('搜索')}
            onChange={this.handleSearch}
            onSearch={this.onSearch}
            onCancel={this.cancelSearch}
            onClear={this.clearSearch}
            value={this.state.searchKeys}
          />
        </div>

        {!searchedSpecificationGroups.length ? (
          <div style={{ marginTop: 50 }}>{i18n.get('无可用模板')}</div>
        ) : (
          searchedSpecificationGroups.map((section: any, index: number) => {
            if (!section.specifications.length) {
              return null
            }
            const name = i18n.currentLocale === 'en-US' && section.enName ? section.enName : section.name
            return (
              <div key={index} className="homeCreateModal-content-section-wrapper">
                <Accordion
                  defaultActiveKey={[section.id]}
                  activeKey={activeKeys}
                  onChange={this.switchPanel}
                  className={'section-title'}
                >
                  <Accordion.Panel header={this.renderHighlight(name)} key={section.id}>
                    <List className={'section-list'}>
                      {section.specifications.map((spec: any, idx: number) => {
                        const IconCompo = getSpecificationIcon(spec.icon)
                        const name = getSpecificationName(spec)
                        return (
                          <List.Item
                            key={idx}
                            className={`spec-item ${
                              currentSpecification && currentSpecification.id === spec.id
                                ? 'selected-specification'
                                : ''
                            }`}
                            onClick={() => fnSubmit(spec)}
                          >
                            <IconCompo className="spec-item-icon" style={{ color: spec.color }} />
                            <div className="spec-item-title">{this.renderHighlight(name)}</div>
                          </List.Item>
                        )
                      })}
                    </List>
                  </Accordion.Panel>
                </Accordion>
              </div>
            )
          })
        )}
      </div>
    )
  }
}
