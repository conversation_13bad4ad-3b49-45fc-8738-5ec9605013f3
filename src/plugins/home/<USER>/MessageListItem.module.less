@import "../../../styles/ekb-colors";

.item_wrapper {
  display: flex;
  padding-left: 32px;
  align-items: flex-start;

  //&:first-child {
  //  padding-top: 24px;
  //}
  //> :first-child {
  //  margin-top: 10px;
  //}

  :global {
    .checkBox {
      margin-top: 32px;
      .am-checkbox {
        width: 48px;
        height: 48px;
        display: inline-block;
        .am-checkbox-inner {
          width: 48px;
          height: 48px;
        }
        .am-checkbox-inner:after {
          top: 8px;
          right: 16px;
          background-color: @primary-6;
          border-color: #fff;
        }
      }
      .am-checkbox-checked {
        .am-checkbox-inner {
          background-color: @primary-6
        }
      }
    }

    .avatar {
      margin-top: 32px;
      width: 64px;
      max-height: 64px;
      border-radius: 50%;
      flex-shrink: 0;
      overflow: hidden;

      img {
        display: block;
        width: 100%;
        border-radius: 100%;
      }
    }

    .item:first-child {
      padding-left: 0;
    }

    .item {
      flex: 1;
      overflow: hidden;
      padding-left: 32px;

      //& > div {
      //  border-bottom: none;
      //}

      .item-style-bill {
        margin-top: 0;
        padding-left: 0;

        .bill-content {
          margin-left: 0;
        }
      }
    }
  }
}

.item_wrapper_checked {
  background-color: #F5F5F5;
  div {
    background-color: #F5F5F5;
  }
}
