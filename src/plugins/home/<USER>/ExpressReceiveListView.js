import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { toast } from '../../../lib/util'
import isEmpty from 'lodash/isEmpty'
const BillListWrapper = api.require('@bill/bill-content/BillListWrapper')
import MessageListBottom from './MessageListBottom'
import styles from './MessageListView.module.less'

import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'

@EnhanceTitleHook(i18n.get('待收单据'))
@EnhanceConnect(state => ({
  expressReceiveList: state['@approve'].expressReceiveList
}))
export default class ExpressReceiveListView extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      selectedDataMap: {},
      selectedAll: false
    }
  }

  _updateData = () => {
    this.setState({ selectedDataMap: {}, selectedAll: false }, () => {
      this._updateTitle()
    })
  }

  _updateTitle = () => {
    const { expressReceiveList } = this.props
    let title = i18n.get('待收单据')
    title = title + i18n.get('(共{__k0}条)', { __k0: expressReceiveList.length })
    api.invokeService('@layout:set:header:title', title)
  }

  _itemCheckBoxChange = value => {
    const { checked, data } = value
    const { expressReceiveList } = this.props
    const { selectedDataMap, selectedAll } = this.state
    const dataMap = { ...selectedDataMap }
    let selected = selectedAll
    if (checked) {
      dataMap[data.id] = data
      selected = Object.keys(dataMap).length === expressReceiveList.length
    } else {
      selected = false
      delete dataMap[data.id]
    }
    this.setState({ selectedDataMap: dataMap, selectedAll: selected })
  }

  _handleClickItem = flow => {
    const { backlogId } = flow
    api.go('/approve/approving/expense/' + backlogId + '/approving')
  }

  _handleSelectAllClick = selectAll => {
    let dataMap = {}
    if (selectAll) {
      const { expressReceiveList } = this.props
      expressReceiveList.forEach(element => (dataMap[element.id] = element))
    }
    this.setState({ selectedAll: selectAll, selectedDataMap: dataMap })
  }

  _handleButtonAction = () => {
    const { selectedDataMap } = this.state
    if (isEmpty(selectedDataMap)) {
      toast.info(i18n.get('请选择单据'))
      return
    }
    const ids = Object.keys(selectedDataMap)
    return this._confirmBill(ids)
  }

  _confirmBill = selectedKeys => {
    const { selectedAll } = this.state
    api.invokeService('@approve:batchReceiveExpress', selectedKeys).then(_ => {
      if (!selectedAll) this._updateData()
    })
  }

  render() {
    const { expressReceiveList } = this.props
    const { selectedDataMap, selectedAll } = this.state
    const count = Object.keys(selectedDataMap).length
    const label = i18n.get('确认收单')
    return (
      <div className={styles.message_list_wrap}>
        <BillListWrapper
          style={{ paddingTop: 0 }}
          selectAble={true}
          className={styles.list_wrapper}
          selectedDataMap={selectedDataMap}
          checkBoxChange={this._itemCheckBoxChange}
          itemClick={this._handleClickItem}
          billList={expressReceiveList}
        />
        <MessageListBottom
          selectedAll={selectedAll}
          buttonAction={this._handleButtonAction}
          label={label}
          count={count}
          selectAllClick={this._handleSelectAllClick}
        />
      </div>
    )
  }
}
