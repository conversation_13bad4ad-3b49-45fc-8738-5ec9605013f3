import React from 'react'
import styles from './MessageTip.module.less'
import { app as api } from '@ekuaibao/whispered'

export default function MessageTip(props) {
  const { paidList, printRemindCount } = props

  const handleToConfirmList = () => {
    let type = 'paidList'
    if (printRemindCount > 0) {
      type = 'printList'
    }
    api.go('/home/<USER>/' + type)
  }
  const isShow = (paidList && paidList.length > 0) || printRemindCount > 0
  if (!isShow) return <div />
  const text =
    printRemindCount > 0
      ? i18n.get('{__k0}张单据需要打印', { __k0: printRemindCount || paidList.length })
      : i18n.get('{__k0}张单据审批通过', { __k0: printRemindCount || paidList.length })

  const count = paidList && paidList.length > 0 && printRemindCount > 0 ? 2 : 1
  return (
    <div className={styles['message-tip']}>
      <div className="empty-div" onClick={handleToConfirmList}>
        <span className="white-text">{text}</span>
        <svg className="icon" aria-hidden="true">
          <use xlinkHref="#EDico-right-default" />
        </svg>
      </div>
    </div>
  )
}

MessageTip.defaultProps = {
  paidList: [],
  printList: []
}
