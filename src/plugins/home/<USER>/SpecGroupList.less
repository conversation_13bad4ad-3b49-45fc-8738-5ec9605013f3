@import '../../../styles/ekb-colors';
@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.home-create-modal-wrapper {
  .eui-search-bar-wrapper {
    padding: 24px 24px 0px;
  }
  .homeCreateModal-content-section-wrapper {
    .section-title {
      text-align: left;
      .font-size-3;
      .font-weight-3;
      margin-bottom: @space-4;
      color: @black;
      user-select: none;
      &:before {
        display: none !important;
      }

      .am-accordion-item {
        .am-accordion-header {
          &:after {
            display: none !important;
          }
        }
        .am-accordion-content {
          .am-list-content {
            display: flex;
            align-items: center;
          }
          .am-accordion-content-box {
            &:after {display: none !important;}
          }
        }
      }
    }
    .section-list {
      .spec-item {
        &:active {
          //transform: scale(.95);
          background-color: @color-bg-2;
        }
        min-height: 96px;
        padding-left: @space-6;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .spec-item-icon {
          width: 48px;
          height: 48px;
          font-size: 32px;
          position: relative;
          top: 4px;
          flex-shrink: 0;
          margin-right: @space-5;
          padding: 2px;
        }
        .spec-item-title {
          flex: 1;
          white-space: pre-wrap;
          text-align: left;
          min-height: 96px;
          display: flex;
          align-items: center;
          .font-size-3;
          .font-weight-2;
          color: @black-85;
          //border-bottom: 2px solid rgba(0, 0, 0, 0.04);
          word-break: break-word;
          user-select: none;
        }
        .am-list-line {
          &:after { display: none !important; }
        }
      }
      .selected-specification {
        background-color: @color-bg-2;
      }
      .am-list-body {
        &:after {
          display: none !important;
        }
      }
    }
  }
}

