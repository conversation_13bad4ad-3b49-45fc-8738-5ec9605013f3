@import '../../styles/layout.less';
@import '../../styles/theme.less';

.expense-didierp-wrapper {
  min-height: 100%;
  flex: 1;
  background: @fill-body;
  display: flex;
  flex-direction: column;

  .orders-header {
    display: flex;
    height: 88px;
    margin: 0 @h-spacing-lg;
    align-items: center;

    .title {
      margin: 0;
      flex: 1;
      font-size: @font-size-base;
      line-height: 1.5;
      font-weight: 400;
      color: @color-text-base;
    }

    a {
      display: block;
      font-size: @font-size-base;
      line-height: 1.5;
      color: var(--brand-base);
    }
  }

  .introWrapper {
    border-bottom: 2px solid #e0e0e0;
  }

  .intro {
    font-size: 32px;
    text-align: center;
  }

  .introDesc {
    color: var(--brand-base);
    text-align: center;
    font-size: 28px;
  }

  .captchaEnable {
    color: var(--brand-base);
    border: 20px solid transparent;
  }

  .captchaDisable {
    font-size: 28px;
    color: @color-text-disabled;
  }

  .didi_orders_list_wrapper {
    display: flex;
    min-height: 100%;
    flex-direction: column;
    flex: 1;
    transform: translate3d(0, 0, 0);

    .import_btn {
      position: fixed;
      width: 100%;
      bottom: 0;
      z-index: 999999;

      .am-button {
        border-radius: 0;
      }
    }

    .orders-footer {
      position: fixed;
      width: 100%;
      bottom: 0;
      z-index: 99999;
      display: flex;
      padding: @v-spacing-md @h-spacing-lg;
      box-sizing: border-box;
      background: @fill-base;
      align-items: center;
      border-top: @border-width-sm solid @border-color-base;

      .content {
        flex: 1;
      }

      .select-all {
        font-size: @font-size-base;
        color: @color-text-base;
        vertical-align: middle;

        .am-checkbox {
          margin-right: 20px;
        }
      }

      .info {
        margin: 10px auto 0;
        font-size: @font-size-base;
        color: @color-text-base;

        strong {
          font-weight: 400;
          font-size: @font-size-base;
          color: var(--brand-base);
        }

        .price {
          margin-left: 10px;
        }

        .price::before {
          font-size: @font-size-icontext;
        }
      }

      .import-btn {
        width: 194px;
      }
    }

    .list_footer {
      padding-bottom: 80px;
      text-align: center;

      p {
        margin: 0;
      }
    }
  }

  .didi_orders_list {
    flex: 1;

    .am-list-body {
      padding-left: 0;
    }
  }

  .didi_orders_separator {
    background-color: #f5f5f9;
    height: 1px;
    border-bottom: 2px solid #ececed;
  }

  .didi_order_row_item {
    padding-left: @h-spacing-lg;

    p {
      margin: 0;
    }

    .order_checkbox {
      display: flex;
      align-items: center;

      &>* {
        pointer-events: none;
      }
    }

    .order_content {
      flex: 1;
      flex-direction: column;
      margin-left: @h-spacing-lg;

      .didi_order_row_item_top {
        padding-left: 11px;
        display: flex;
        flex-direction: row;

        .departure_time {
          flex: 1;
          font-size: @font-size-caption-sm;
          color: @color-text-caption;
        }
      }

      .didi_order_row_item_bottom {
        display: flex;
        flex-direction: row;

        .from_to {
          flex: 1;

          div {
            width: 380px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .actual_price {
          text-align: right;
          font-size: @font-size-subhead;
          color: @color-text-base;
        }
      }
    }
  }

  .didi_no_order_notice_wrapper {
    display: flex;
    min-height: 100%;
    flex-direction: column;
    background: @fill-base;
    overflow-y: auto;

    .orders-header {
      border: 0;
    }

    .blink {
      padding: 0 30px;
      background: #ffffff;
    }

    header {
      padding: 64px 0 48px;
      border-bottom: 2px solid @border-color-base;

      h3 {
        text-align: center;
        margin-top: 24px;
        color: @color-text-paragraph;
        font-size: @font-size-subhead;
        font-weight: 500;
        line-height: 1.6;
      }
    }

    h4 {
      margin-top: 80px;
      color: @color-text-paragraph;
      font-weight: 500;
      line-height: 1.6;
    }

    ol,
    li {
      margin: 0;
      padding-left: 0;
      list-style: none;
    }

    p {
      margin: 20px 0 32px;
      font-size: @font-size-base;
      line-height: 1.7;
      color: @color-text-caption;
    }

    img {
      display: block;
      width: 100%;

      &.no-data-pic {
        width: 436px;
        margin: 0 auto;
      }
    }

    .content {
      display: flex;
      flex-direction: column;
      align-items: center;

      .main_content {
        color: @color-text-base;
        text-align: center;
        margin: 0;
      }

      .hint_wrapper {
        margin-top: 30px;
        width: 60%;

        .hint {
          text-align: center;
          margin: 0;
          font-size: 26px;
          color: @color-text-base;
        }
      }
    }
  }

  .bind-btn {
    border-radius: 4px;
  }

  .questionnaires {
    display: block;

    img {
      display: block;
      width: 100%;
    }
  }

  .binding-title {
    margin: 32px 0 16px;
    font-weight: 400;
    font-size: @font-size-caption-sm;
    color: @color-text-secondary;
  }

  .agreement-wrapper {
    width: 100%;
    margin: 48px auto;
    text-align: center;
    font-size: @font-size-caption-sm;
    line-height: 1.5;
    color: @color-text-base;

    .agreement {
      color: var(--brand-base);
    }
  }
}

.didi-popup-inner {
  padding: 50px 0;

  h4 {
    margin: 0;
    text-align: center;
    font-size: 30px;
    color: @color-text-base;
  }

  p {
    font-size: 28px;
    color: @color-text-secondary;
    margin: 32px auto;
    line-height: 1.5;
  }

  .am-button-primary.am-button-active {
    background: var(--brand-5);
  }
}

.didi-bind {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;

  .didi-bind-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 800px;

    .binding-title {
      margin: 32px 0 16px;
      font-weight: 400;
      font-size: @font-size-base;
      color: #8c8c8c;
    }

    .ln {
      background-color: @white;

      #ncpcdom {
        padding: 0 30px;
      }
    }
  }
}