/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/27 下午5:58
 */
@import url('../../styles/ekb-colors');

.unBind_wrap {
  flex       : 1;
  background : @gray-1;
  overflow-y : auto;
}

.unBind_main {
  padding     : 0 32px;
  flex-shrink : 0;
}

.unBind_oval {
  margin-top : 64px;
  text-align : center;
  img {
    width  : 120px;
    height : 120px;
  }
}

.unBind_title {
  margin-top : 32px;
  font-size  : 42px;
  text-align : center;
  color      : @gray-9;
}

.unBind_info {
  margin-top : 20px;
  font-size  : 28px;
  color      : #A2ABAF;
}

.unBind_line {
  margin     : 48px 0;
  border     : none;
  border-top : 2px solid @gray-4;
}

.unBind_text {
  font-size : 28px;
  color     : #A2ABAF;
}

.unBind_ol {
  padding-left : 32px;
  font-size    : 28px;
  color        : #A2ABAF;
  img {
    width : 92%;
  }
  div {
    padding : 36px 0 48px;
  }
  p {
    margin : 0;
  }
}

.unBind_btn {
  flex-shrink : 0;
  font-size   : 30px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  :global {
    .am-button {
      border-radius : 0;
      height        : 100px;
      line-height   : 100px;
    }
  }
}