import './didierp.less'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { InputItem, Button, WhiteSpace, WingBlank } from 'antd-mobile'
import { createForm } from 'rc-form'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import PageContainer from '../page-container'
import key from './key'
import { sendCreateBindingCaptcha } from './didierp.action'
import { toast } from '../../lib/util'
import { getNch5Token } from '../../lib/nch5'
import { Fetch } from '@ekuaibao/fetch'

const resendCaptchaTime = 60 //验证码重发时间，秒为单位
const phoneLength = 11 //手机号长度
const nameLength = 30

@EnhanceConnect(state => ({
  me_info: state['@common'].me_info.staff,
  staff_binding_captchaId: state[key.ID].staff_binding_captchaId
}))
@EnhanceTitleHook(props => {
  return props.params && props.params.type ? i18n.get('绑定手机号') : i18n.get('滴滴授权绑定')
})
@createForm()
export default class Captcha extends PageContainer {
  constructor(props) {
    super(props)
    let { params = {} } = props
    this.isMall = params.type === 'mall'
    this.noCaptha = this.isMall && this.props.me_info.cellphone
    this.state = {
      focused: false,
      captchaFocused: false,
      sendCaptchaEnable: false,
      submitBtnLoading: false,
      sendCaptchaBtn: i18n.get('发送验证码'),
      cellphone: this.isMall ? this.props.me_info.cellphone : '',
      captcha: '',
      ncpcData: null,
      name: this.props.me_info.name
    }
  }

  componentDidMount() {
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage
    !this.noCaptha && getNch5Token(this.setNcpcData, lang)
  }

  componentWillUnmount() {
    clearInterval(this.timerId)
  }

  setNcpcData = (data, token) => {
    delete data.code
    delete data.value
    data.scene = 'message_h5'
    data.token = token
    data.platform = 3
    this.setState({ ncpcData: data })
  }

  sendCaptcha = () => {
    if (!this.state.sendCaptchaEnable) return
    const cellphone = Trim(this.state.cellphone, 'g')
    const ncpcData = this.state.ncpcData
    const platformId = this.isMall ? 'YEEGO' : 'DIDI'
    api.dispatch(sendCreateBindingCaptcha({ platformId, cellphone, ...ncpcData })).then(() => {
      let interval = resendCaptchaTime
      this.setState({
        sendCaptchaBtn: i18n.get('已发送({__k0})', { __k0: interval }),
        sendCaptchaEnable: false
      })
      this.timerId = setInterval(() => {
        this.setState({
          sendCaptchaBtn: i18n.get('已发送({__k0})', { __k0: interval - 1 })
        })
        interval--
        if (interval === 0) {
          this.resetCaptcha()
        }
      }, 1000)
    })
  }

  resetCaptcha = () => {
    this.setState({
      sendCaptchaEnable: true,
      sendCaptchaBtn: i18n.get('发送验证码')
    })
    clearInterval(this.timerId)
  }

  createBinding = () => {
    const { staff_binding_captchaId, onOk, params } = this.props
    const { captcha, cellphone, name } = this.state

    if (staff_binding_captchaId || this.noCaptha) {
      this.setState({ submitBtnLoading: true })
      let platformId = this.isMall ? 'YEEGO' : 'DIDI'
      if (platformId === 'YEEGO' && /[（）\(\)]/.test(this.state.name)) {
        return toast.fail(i18n.get('姓名包含非法字符（）'))
      }
      api
        .invokeService('@third-import:post:bindingStaff', platformId, staff_binding_captchaId, captcha, cellphone, name)
        .then(() => {
          this.setState({ submitBtnLoading: false }, () => {
            if (onOk) return onOk() //跳转路由进入此页面时，没有onOk
            toast.success(i18n.get('绑定成功'))
            if (params && params.type !== 'mall') {
              api.invokeService('@third-import:get:platFormList')
            }
            api.go(-1)
          })
        })
        .catch(() => {
          toast.fail(i18n.get('绑定失败'))
        })
    } else {
      toast.fail(i18n.get('没有找到验证码ID'))
    }
  }

  checkPhoneInput = value => {
    let sendCaptchaEnable = true
    if (
      !value ||
      value.length !== phoneLength ||
      this.state.sendCaptchaBtn !== i18n.get('发送验证码') ||
      (this.isMall && !this.state.name)
    ) {
      sendCaptchaEnable = false
    }
    this.setState({ cellphone: value, sendCaptchaEnable })
  }

  checkNameInput = value => {
    let sendCaptchaEnable = true
    if (
      !this.state.cellphone ||
      this.state.cellphone.length !== phoneLength ||
      this.state.sendCaptchaBtn !== i18n.get('发送验证码') ||
      !value
    ) {
      sendCaptchaEnable = false
    }
    this.setState({ name: value, sendCaptchaEnable })
  }

  checkCaptchaInput = value => {
    this.setState({ captcha: value })
  }

  checkSubmitBtn() {
    if (this.isMall) {
      let captcha = this.noCaptha || !!this.state.captcha //不需要capcha的情况
      return this.state.cellphone && this.state.cellphone.length === phoneLength && this.state.name && captcha
    }
    return this.state.cellphone && this.state.cellphone.length === phoneLength && this.state.captcha
  }

  renderDesc() {
    return (
      <div>
        <p>{i18n.get('请确认以下姓名为证件有效姓名。如不是请手动修正，以保证及时准确出票。')}</p>
        <p className="color-red">{i18n.get('一经确认，不能修改')}</p>
      </div>
    )
  }

  render() {
    const { getFieldProps } = this.props.form
    const { ncpcData } = this.state

    return (
      <div className="didi-bind">
        <div className="didi-bind-main">
          <WingBlank size="lg">
            <h4 className="binding-title">
              {this.isMall ? this.renderDesc() : i18n.get('绑定后就可以直接导入滴滴订单')}
            </h4>
          </WingBlank>
          {this.isMall && (
            <InputItem
              {...getFieldProps('name')}
              onChange={this.checkNameInput}
              value={this.state.name}
              type="text"
              placeholder={i18n.get('请输入真实姓名')}
              maxLength={nameLength}
              autoFocus
              clear
            >
              <div onClick={() => this.setState({ phoneFocused: true })}>{i18n.get('真实姓名', {})}</div>
            </InputItem>
          )}
          <InputItem
            {...getFieldProps('cellphone')}
            focused={this.state.phoneFocused}
            onFocus={() => {
              this.setState({ phoneFocused: false })
            }}
            onChange={this.checkPhoneInput}
            value={this.state.cellphone}
            type="number"
            placeholder={i18n.get('请输入11位手机号')}
            maxLength={phoneLength}
            disabled={this.noCaptha}
            autoFocus
            clear
          >
            <div onClick={() => this.setState({ phoneFocused: true })}>{i18n.get('手机号', {})}</div>
          </InputItem>

          {ncpcData && !this.noCaptha && (
            <InputItem
              {...getFieldProps('captcha')}
              focused={this.state.captchaFocused}
              onFocus={() => {
                this.setState({ captchaFocused: false })
              }}
              onChange={this.checkCaptchaInput}
              value={this.state.captcha}
              extra={
                <span
                  onClick={this.sendCaptcha}
                  className={this.state.sendCaptchaEnable ? 'captchaEnable' : 'captchaDisable'}
                >
                  {this.state.sendCaptchaBtn}
                </span>
              }
              type="number"
              placeholder={i18n.get('请输入验证码')}
              maxLength="6"
            >
              <div onClick={() => this.setState({ captchaFocused: true })}>{i18n.get('验证码', {})}</div>
            </InputItem>
          )}
          {!this.noCaptha && (
            <div className="ln">
              <div id="ncpcdom" />
            </div>
          )}
          <WhiteSpace size="xl" />
          <WingBlank size="lg">
            <Button
              type="primary"
              className="bind-btn"
              onClick={this.createBinding}
              disabled={!this.checkSubmitBtn()}
              loading={this.state.submitBtnLoading}
            >
              {i18n.get('绑定', {})}
            </Button>
          </WingBlank>
          {/*短信防控滑动验证服务必须引入*/}
          {!this.noCaptha && <div id="_umfp" />}
        </div>
      </div>
    )
  }
}

function Trim(str, is_global) {
  let result = str.replace(/(^\s+)|(\s+$)/g, '')
  if (is_global.toLowerCase() === 'g') {
    result = result.replace(/\s/g, '')
  }
  return result
}
