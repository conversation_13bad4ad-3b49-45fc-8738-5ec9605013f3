import { app } from '@ekuaibao/whispered'
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/1 下午3:19
 */

import React from 'react'
import PropTypes from 'prop-types'

import styles from './errorReason.module.less'
import SVG_OVAL from './images/oval.svg'
import { Button } from 'antd-mobile'
// import TelPhoneCall from '../../elements/TelPhoneCall'
const TelPhoneCall = app.require('@elements/TelPhoneCall')

const ErrorReason = props => {
  let { staffStatus = {} } = props
  return (
    <div className={styles.errorReason_wrap}>
      <div className={styles.errorReason_main}>
        <div className={styles.errorReason_oval}>
          <img src={SVG_OVAL} />
        </div>
        <div className={styles.errorReason_title}>{i18n.get('无法加入企业')}</div>
        <div className={styles.errorReason_tip}>
          {i18n.get('功能开通遇到异常，异常原因:{__k0}', { __k0: staffStatus.bindingErrorDetail })}
        </div>
        <div className={styles.errorReason_btn}>
          <TelPhoneCall phoneNumber="4009998293">
            <Button type="primary">{i18n.get('联系易快报客服')}</Button>
          </TelPhoneCall>
        </div>
      </div>
    </div>
  )
}

ErrorReason.defaultProps = {}
ErrorReason.propTypes = {
  staffStatus: {}
}

export default ErrorReason
