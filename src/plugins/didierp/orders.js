import { app, app as api } from '@ekuaibao/whispered'
import './didierp.less'
import { ListView, List, Button, Checkbox, PullToRefresh } from 'antd-mobile'
import { Dialog } from '@hose/eui-mobile'
import { EnhanceConnect } from '@ekuaibao/store'
import key from './key'
import moment from 'moment'
const Money = app.require('@elements/puppet/Money')
const { thousandBitSeparator } = app.require('@components/utils/fnThousandBitSeparator')
import PageContainer from '../page-container'
import Big from 'big.js'
import { Fetch } from '@ekuaibao/fetch'
import { toast, showLoading, hideLoading } from '../../lib/util'
import * as actions from './didierp.action'
import filter from 'lodash/filter'
import remove from 'lodash/remove'

const dataSource = new ListView.DataSource({
  rowHasChanged: (row1, row2) => row1 !== row2
})
const picNoOrder = require('./images/<EMAIL>')
const picHowToUseDidi1 = require('./images/picHowToUseDidi1.png')
const picHowToUseDidi2 = require('./images/picHowToUseDidi2.png')
const { Item } = List

const chineseWeekDayName = {
  Monday: i18n.get('周一'),
  Tuesday: i18n.get('周二'),
  Wednesday: i18n.get('周三'),
  Thursday: i18n.get('周四'),
  Friday: i18n.get('周五'),
  Saturday: i18n.get('周六'),
  Sunday: i18n.get('周日')
}

@EnhanceConnect(state => ({
  didi_orders: state[key.ID].didi_orders,
  standardCurrency: state['@common'].standardCurrency
}))
export default class Orders extends PageContainer {
  constructor(props) {
    super(props)
    this.state = {
      dataSource: dataSource.cloneWithRows([]),
      refreshing: true,
      checkedOrders: [],
      disabled: true
    }
  }

  componentWillMount() {
    const { didiIds } = this.props
    showLoading()
    api.dispatch(
      actions.getOrders(didiIds, () => {
        hideLoading()
        this.setState({ refreshing: false })
      })
    )
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.didi_orders && nextProps.didi_orders.length !== 0) {
      this.setState({
        dataSource: dataSource.cloneWithRows(nextProps.didi_orders)
      })
    }
  }

  onRefresh = () => {
    //刷新动作
    const { didiIds } = this.props
    this.setState({
      refreshing: true,
      checkedOrders: [],
      dataSource: dataSource.cloneWithRows([])
    })
    api.dispatch(
      actions.getOrders(didiIds, () => {
        this.setState({ refreshing: false })
      })
    )
  }

  onRowCheckChange = (data, e) => {
    let { checkedOrders } = this.state
    let orders = [...checkedOrders]
    if (e.target.checked) {
      // 增加数据
      orders.push(data)
      data.ekbChecked = true
    } else {
      // 删除数据
      data.ekbChecked = false
      remove(orders, item => {
        return item.order_id === data.order_id
      })
    }
    this.setState({ checkedOrders: orders })
  }

  importOrders = () => {
    let results = filter(this.props.didi_orders, o => {
      return o.data.ekbChecked
    })
    if (results.length === 0) {
      toast.fail(i18n.get('请选择要导入的订单'))
    } else {
      this.props.getOrders(results)
    }
  }

  // 解绑
  modifyBinding() {
    Dialog.confirm({
      title: i18n.get('解除绑定'),
      content: i18n.get('确定解除当前手机号么？'),
      onConfirm: async () => {
        await Fetch.PUT('/api/didiErp/v1/staffBinding/modify', {})
        api.dispatch(actions.clearStaffBind())
        toast.info(i18n.get('解绑成功～'), 1000)
        api.go('/expense')
      }
    })
  }

  renderFooter = () => {
    if (this.state.refreshing) {
      return null
    }
    if (this.props.didi_orders && this.props.didi_orders.length > 100) {
      return (
        <div className="list_footer">
          <p>{i18n.get('别扯了...到底儿了(Made With Love)')}</p>
        </div>
      )
    } else if (this.props.didi_orders && this.props.didi_orders.length > 10 && this.props.didi_orders.length <= 100) {
      return (
        <div className="list_footer">
          <p>{i18n.get('呀！-.- 没有更多订单了')}</p>
        </div>
      )
    } else {
      return null
    }
  }

  renderList = () => {
    //分隔器
    const separator = (sectionID, rowID) => <div key={`${sectionID}-${rowID}`} className="didi_orders_separator" />

    //行内容定义
    const row = (rowData, sectionID, rowID) => {
      let data = rowData.data
      return (
        <Item key={rowID} style={{ paddingLeft: 0 }}>
          <div className="didi_order_row_item">
            <Checkbox
              className="order_checkbox"
              key={data.order_id}
              onClick={e => {
                this.onRowCheckChange(data, e)
              }}
            >
              <div className="order_content">
                <div className="didi_order_row_item_top">
                  <div className="departure_time">
                    <span>{moment(data.departure_time).format(i18n.get('MM月DD日 HH:mm'))}</span>
                    <span>{chineseWeekDayName[moment(data.departure_time).format('dddd')] || ''}</span>
                  </div>
                </div>
                <div className="didi_order_row_item_bottom">
                  <div className="from_to">
                    <div>
                      <span>{data.start_name}</span>
                    </div>
                    <div>
                      <span>{data.end_name}</span>
                    </div>
                  </div>
                  <Money className="actual_price" value={data.actual_price} />
                </div>
              </div>
            </Checkbox>
          </div>
        </Item>
      )
    }

    return (
      <ListView
        dataSource={this.state.dataSource}
        renderRow={row}
        renderSeparator={separator}
        initialListSize={10}
        pageSize={10}
        scrollRenderAheadDistance={200}
        className="didi_orders_list"
        scrollerOptions={{ scrollbars: true }}
        pullToRefresh={<PullToRefresh refreshing={this.state.refreshing} onRefresh={this.onRefresh} />}
        renderFooter={this.renderFooter}
      />
    )
  }

  renderOrdersFooter() {
    let {
      standardCurrency: { scale, symbol }
    } = this.props
    let { checkedOrders } = this.state
    let totalPrice = new Big(0)
    if (checkedOrders.length) {
      checkedOrders.forEach(order => {
        totalPrice = totalPrice.add(order.actual_price)
      })
    }

    totalPrice = totalPrice.gt(0) ? totalPrice.toFixed(scale) : '0.00'

    return (
      <footer className="orders-footer">
        <div className="content">
          <p className="info">
            <strong>{checkedOrders.length}</strong>
            {i18n.get('个行程')}
            {i18n.get('， 共')}
            <strong className="price">{`${symbol}` + thousandBitSeparator(totalPrice)}</strong>
          </p>
        </div>
        <Button type="primary" className="import-btn" onClick={this.importOrders}>
          {i18n.get('导入订单', {})}
        </Button>
      </footer>
    )
  }

  render() {
    if (!this.props.didi_orders) {
      return null
    }

    // 无订单
    if (!this.props.didi_orders.length) {
      return (
        <div className="didi_no_order_notice_wrapper">
          <header className="orders-header">
            <h2 className="title" />
            <a
              onClick={() => {
                this.modifyBinding()
              }}
            >
              ${i18n.get('解除绑定')}
            </a>
          </header>
          <div className="blink">
            <header>
              <img src={picNoOrder} className="no-data-pic" />
              <h3>{i18n.get('您暂时没有因公订单')}</h3>
            </header>
            <h4>{i18n.get('使用方法')}</h4>
            <ol>
              <li>
                <p>{i18n.get('1.请在叫车页面选择“个人支付（需报销）”的支付方式来标记因公行程')}</p>
                <img src={picHowToUseDidi1} />
                <p>{i18n.get('2.在已完成的订单>发票与报销里面，选择变更订单状态')}</p>
                <img src={picHowToUseDidi2} />
              </li>
            </ol>
          </div>
        </div>
      )
    }

    return (
      <div className="didi_orders_list_wrapper">
        <header className="orders-header">
          <h2 className="title">{i18n.get('我的订单')}</h2>
          <a
            onClick={() => {
              this.modifyBinding()
            }}
          >
            {i18n.get('解除绑定')}
          </a>
        </header>
        {this.renderList()}
        {this.renderOrdersFooter()}
      </div>
    )
  }
}
