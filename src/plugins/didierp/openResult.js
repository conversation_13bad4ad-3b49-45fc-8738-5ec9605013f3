/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/4 下午3:08
 */
import styles from './openResult.module.less'
import React from 'react'
import { Button, WhiteSpace } from 'antd-mobile'
import { app as api } from '@ekuaibao/whispered'
import SVG_ERROR from './images/error.svg'
import SVG_SUCCESS from './images/success.svg'
import SVG_LOADING from './images/loading.svg'

const handleToUse = () => {
  return api.invokeService('@didierp:import:didi')
}

const handleReAuth = onReAuth => {
  onReAuth && onReAuth()
}

const OpenResult = props => {
  let { openStatus, onReAuth, onCancel, onUseClick, errorReason } = props
  if (openStatus === 'loading') {
    return (
      <div className={styles.openResult_wrap}>
        <div className={styles.openResult_main}>
          <div className={styles.openResult_oval}>
            <img src={SVG_LOADING} />
          </div>
          <div className={styles.openResult_title}>{i18n.get('开通中...')}</div>
          <div className={styles.openResult_tip}>{i18n.get('正自动刷新状态，请稍后')}</div>
          <div className={styles.openResult_btn_pri}>
            <Button onClick={handleReAuth.bind(this, onReAuth)} type="primary">
              {i18n.get('重新打开授权窗口')}
            </Button>
            <WhiteSpace size="lg" />
            <Button onClick={onCancel}>{i18n.get('取消')}</Button>
          </div>
        </div>
      </div>
    )
  }
  if (openStatus === 'success') {
    return (
      <div className={styles.openResult_wrap}>
        <div className={styles.openResult_main}>
          <div className={styles.openResult_oval}>
            <img src={SVG_SUCCESS} />
          </div>
          <div className={styles.openResult_title}>{i18n.get('开通成功')}</div>
          <div className={styles.openResult_tip}>{i18n.get('员工报销更便捷，企业管理更透明')}</div>
          <div className={styles.openResult_btn_pri}>
            <Button onClick={onUseClick || handleToUse} type="primary">
              {i18n.get('开始使用')}
            </Button>
          </div>
        </div>
      </div>
    )
  }
  if (openStatus === 'fail') {
    return (
      <div className={styles.openResult_wrap}>
        <div className={styles.openResult_main}>
          <div className={styles.openResult_oval}>
            <img src={SVG_ERROR} />
          </div>
          <div className={styles.openResult_title}>{i18n.get('开通失败')}</div>
          <div className={styles.openResult_tip}>{i18n.get('失败原因：{__k0}', { __k0: errorReason })}</div>
          <div className={styles.openResult_btn}>
            <Button type="primary" onClick={handleReAuth.bind(this, onReAuth)}>
              {i18n.get('重试')}
            </Button>
          </div>
        </div>
      </div>
    )
  }
}

OpenResult.defaultProps = {
  openStatus: ''
}
OpenResult.propTypes = {}

export default OpenResult
