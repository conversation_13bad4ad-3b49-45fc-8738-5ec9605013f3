/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/27 下午5:23
 */

import styles from './unBind.module.less'
import SVG_OVAL from './images/oval.svg'
import PNG_1 from './images/ol_1.png'
import PNG_2 from './images/ol_2.png'
import { Button } from 'antd-mobile'

import React from 'react'

const UnBind = props => {
  return (
    <div className={styles.unBind_wrap}>
      <div className={styles.unBind_main}>
        <div className={styles.unBind_oval}>
          <img src={SVG_OVAL} />
        </div>
        <div className={styles.unBind_title}>{i18n.get('无法加入企业')}</div>
        <div className={styles.unBind_info}>
          {i18n.get('您的号码被绑定在其他滴滴企业中，请按照下方操作退出当前企业重试。')}
        </div>
        <hr className={styles.unBind_line} />
        <div className={styles.unBind_text}>{i18n.get('如何解绑在其他滴滴企业的手机号？')}</div>
        <ol className={styles.unBind_ol}>
          <li>
            <p>{i18n.get('打开「滴滴企业版」App，选择左上角「个人中心」，上拉底部菜单栏，点击「企业版」。')}</p>
            <div>
              <img src={PNG_1} />
            </div>
          </li>
          <li>
            <p>{i18n.get('点击「退出企业」，即可成功解绑，完成所有操作后，回到易快报点击重试加入企业。')}</p>
            <div>
              <img src={PNG_2} />
            </div>
          </li>
        </ol>
      </div>
      <div className={styles.unBind_btn}>
        <Button type="primary" onClick={props.onRetry}>
          {i18n.get('重试')}
        </Button>
      </div>
    </div>
  )
}

UnBind.defaultProps = {}
UnBind.propTypes = {}

export default UnBind
