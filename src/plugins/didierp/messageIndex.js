import './didierp.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import { app as api } from '@ekuaibao/whispered'
import { toast, showLoading, hideLoading } from '../../lib/util'
import UnBind from './unBind'
import Captcha from './captcha'
import OpenDidi from './openDidi'
import OpenResult from './openResult'
import ErrorReason from './errorReason'
import { retryBindDiDi } from './didierp.action'
@EnhanceConnect(
  state => ({
    platFromList: state['@third-import'].platFromList
  }),
  { retryBindDiDi }
)
@EnhanceTitleHook()
export default class DidiErp extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      loadingFinished: false
    }
  }

  componentWillMount = () => {
    showLoading()
    api.invokeService('@third-import:get:platFormList').then(() => {
      this.setState({
        loadingFinished: true
      })
      hideLoading()
    })
  }

  handleNew = () => {
    api.go('/message/bill/expense/true')
  }

  handleRetry = () => {
    showLoading(i18n.get('重试中'))
    this.props.retryBindDiDi({ platformId: 'DIDI' }).then(action => {
      toast.hide()
      if (action.error) return
      let result = action.payload && action.payload[0]
      if (result && !result.isBinding) {
        toast.error(i18n.get('重试失败：{__k0}', { __k0: result.bindingErrorDetail }))
      } else {
        toast.success(i18n.get('绑定成功'))
      }
      api.invokeService('@third-import:get:platFormList')
    })
  }

  handleCancel = () => {
    if (window.isMessage) return api.go(-1)
    this.props.layer.emitCancel()
  }

  handleRegister = () => {
    api.invokeService('@third-import:get:platFormList')
  }

  renderContent() {
    let { platFromList } = this.props
    let platformDidi
    try {
      if (platFromList instanceof Object) {
        platformDidi = platFromList.find(el => el.id === 'DIDI')
      } else {
        platformDidi = JSON.parse(platFromList).find(el => el.id === 'DIDI')
      }
    } catch (e) {
      console.log('e-->>' + e)
    }
    if (!platformDidi) {
      return <div>{i18n.get('暂未开启滴滴功能')}</div>
    }

    let { corporationBinding = [], staffBinding = [] } = platformDidi
    let corporationStatus = corporationBinding.find(v => v.platformId === 'DIDI')
    let staffStatus = staffBinding.find(v => v.platformId === 'DIDI') || {}

    if (!corporationStatus.active) {
      return <div>{i18n.get('暂未开启滴滴功能')}</div>
    }

    if (!corporationStatus.isBinding) {
      return <OpenDidi onCancel={this.handleCancel} />
    }

    if (!staffStatus.isBinding && (!staffStatus.bindingError || staffStatus.bindingError === 'ACCOUNTUNKNOWN')) {
      return <Captcha onOk={this.handleRegister} />
    }

    if (!staffStatus.isBinding && staffStatus.bindingError === 'ACCOUNTISTAKEN') {
      return <UnBind staffStatus={staffStatus} onRetry={this.handleRetry} />
    }

    if (!staffStatus.isBinding && staffStatus.bindingError === 'OTHERS') {
      return <ErrorReason staffStatus={staffStatus} />
    }

    return <OpenResult openStatus="success" onUseClick={this.handleNew} />
  }

  render() {
    if (!this.state.loadingFinished) return <div />
    return <div className="expense-didierp-wrapper">{this.renderContent()}</div>
  }
}
