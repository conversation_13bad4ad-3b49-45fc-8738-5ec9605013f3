import './didierp.less'
import React, { PureComponent } from 'react'
import { app as api, app } from '@ekuaibao/whispered'
import { toast, hideLoading, showLoading } from '../../lib/util'
import { EnhanceConnect } from '@ekuaibao/store'
// import LayerContainer from '../basic-elements/layer-container'
const LayerContainer = app.require("@basic-elements/layer-container")
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import Captcha from './captcha'
// import ThirdImportList from '../thirdImport/thirdImport-list'
const ThirdImportList = app.require("@third-import/thirdImport-list")
import OpenDidi from './openDidi'
import UnBind from './unBind'
import ErrorReason from './errorReason'
import { retryBindDiDi } from './didierp.action'
// import { changeFilter } from '../thirdImport/thirdImport.action'

@EnhanceConnect(
  state => ({
    platFromList: state['@third-import'].platFromList
  }),
  { retryBindDiDi }
)
@EnhanceTitleHook()
export default class DidiErp extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      loadingFinished: false
    }
    props.overrideGetResult(this.getResult)
  }

  componentWillMount = () => {
    api.invokeService('@invoice-form:unify-invoice-list')
    //进场清除滴滴筛选条件
    // api.dispatch(changeFilter(null))
    api.invokeService("@third-import:changeFilter", null)
    const { platFromList } = this.props
    if (!platFromList.length) {
      showLoading()
      api.invokeService('@third-import:get:platFormList').then(() => {
        this.setState({ loadingFinished: true })
        hideLoading()
      })
    } else {
      this.setState({ loadingFinished: true })
    }
  }

  getResult = () => {
    for (let order of this.orders) {
      delete order.ekbChecked
    }
    return this.orders
  }

  getOrders = results => {
    this.orders = results
    this.props.layer.emitOk()
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleRetry = () => {
    toast.loading(i18n.get('重试中'), 0)
    this.props.retryBindDiDi({ platformId: 'DIDI' }).then(action => {
      toast.hide()
      if (action.error) return
      let result = action.payload && action.payload[0]
      if (result && !result.isBinding) {
        toast.error(i18n.get('重试失败：{__k0}', { __k0: result.bindingErrorDetail }))
      } else {
        toast.success(i18n.get('绑定成功'))
      }

      api.invokeService('@third-import:get:platFormList')
    })
  }

  handleRegister = () => {
    api.invokeService('@third-import:get:platFormList')
  }

  renderContent() {
    let { platFromList, orderImported = [], deleteOrders = [] } = this.props
    let platformDidi
    try {
      if (platFromList instanceof Object) {
        platformDidi = platFromList.find(el => el.id === 'DIDI')
      } else {
        platformDidi = JSON.parse(platFromList).find(el => el.id === 'DIDI')
      }
    } catch (e) {
      console.log('e-->>' + e)
    }

    if (!platformDidi) {
      return <div>{i18n.get('暂未开启滴滴功能')}</div>
    }

    let { corporationBinding = [], staffBinding = [] } = platformDidi
    let corporationStatus = corporationBinding.find(v => v.platformId === 'DIDI')
    let staffStatus = staffBinding.find(v => v.platformId === 'DIDI') || {}

    if (!corporationStatus.active) {
      return <div>{i18n.get('暂未开启滴滴功能')}</div>
    }

    if (!corporationStatus.isBinding) {
      return <OpenDidi onCancel={this.handleCancel} />
    }

    if (!staffStatus.isBinding && (!staffStatus.bindingError || staffStatus.bindingError === 'ACCOUNTUNKNOWN')) {
      return <Captcha onOk={this.handleRegister} />
    }

    if (!staffStatus.isBinding && staffStatus.bindingError === 'ACCOUNTISTAKEN') {
      return <UnBind staffStatus={staffStatus} onRetry={this.handleRetry} />
    }

    if (!staffStatus.isBinding && staffStatus.bindingError === 'OTHERS') {
      return <ErrorReason staffStatus={staffStatus} />
    }

    return (
      <ThirdImportList
        getOrders={this.getOrders}
        orderImported={orderImported}
        deleteOrders={deleteOrders}
        orderType="car"
      />
    )
  }

  render() {
    if (!this.state.loadingFinished) return null
    return (
      <LayerContainer>
        <div className="expense-didierp-wrapper">{this.renderContent()}</div>
      </LayerContainer>
    )
  }
}
