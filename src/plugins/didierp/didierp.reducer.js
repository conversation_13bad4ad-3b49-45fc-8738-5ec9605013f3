import { Reducer } from '@ekuaibao/store'
import key from './key'
import { toast } from '../../lib/util'

const reducer = new Reducer(key.ID, {
  didiAuth: false
})

reducer.handle(key.GET_CORPORATION_BINDING, (state, action) => {
  const { items } = action.payload
  if (items && items.length > 0 && items[0].platformId === 'DIDI') {
    const { authScope } = items[0].setting || {}
    const scope = '1|2|3' === authScope
    return { ...state, didiAuth: scope }
  } else {
    return state
  }
})

reducer.handle(key.GET_STAFF_BINDING, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return { ...state }
  }
  return { ...state, staff_binding_exists: action.payload.value }
})

reducer.handle(key.SEND_CREATE_BINGDING_CAPTCHA, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return { ...state }
  }
  return { ...state, staff_binding_captchaId: action.payload.id }
})

reducer.handle(key.CLEAR_CAPTCHA_ID, (state, action) => {
  return { ...state, staff_binding_captchaId: action.payload }
})

reducer.handle(key.CLEAR_STAFF_BINDING, (state, action) => {
  return { ...state, staff_binding_exists: action.payload }
})

reducer.handle(key.CREATE_BINDING, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return { ...state }
  }
  toast.success(i18n.get('绑定成功'))
  return { ...state }
})

reducer.handle(key.GET_ORDERS, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return { ...state }
  }
  // 当没有 保存／提交 的时候过滤掉已经选择的订单
  if (action.ids && action.ids.length) {
    let didi_orders = action.payload.items
    didi_orders = didi_orders.filter(o => {
      return !action.ids.some(id => o.id === id)
    })
    return { ...state, didi_orders }
  }
  return { ...state, didi_orders: action.payload.items }
})

export default reducer
