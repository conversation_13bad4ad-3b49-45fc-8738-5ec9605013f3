/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/27 下午4:13
 */
import styles from './openDidi.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import sourceMap from './souceMap'
import OpenResult from './openResult'
import { app as api } from '@ekuaibao/whispered'
import { toast } from '../../lib/util'
import PNG_LOGO from './images/logo.png'
import PNG_BANNER from './images/banner.png'

@EnhanceConnect(state => ({
  me_info: state['@common'].me_info,
  staffsAdmin: state['@common'].staffsAdmin,
  platFromList: state['@third-import'].platFromList
}))
class OpenDidi extends PureComponent {
  static defaultProps = {}
  static propTypes = {}

  constructor(props) {
    super(props)

    let page = 1
    let status = ''

    this.source = sourceMap.get('DIDI')

    if (this.source) {
      if (this.source.readyState !== 2) {
        page = 2
        status = 'loading'
      } else {
        sourceMap.delete('DIDI')
      }
    }

    this.state = { page, status }
  }

  componentWillMount() {
    let { staffsAdmin } = this.props
    sourceMap.on('DIDI:AUTH:PROGRESS', this.handleChangeStatus)
    if (!staffsAdmin.length) {
      api.invokeService('@common:get:staffs:byPermission', { name: 'SYS_ADMIN' })
    }
  }

  componentWillUnmount() {
    sourceMap.un('DIDI:AUTH:PROGRESS')
  }

  handleChangeStatus = result => {
    this.setState(result)
  }

  handleAuthDiDi = page => {
    const { platFromList } = this.props
    let didi
    try {
      if (platFromList instanceof Object) {
        didi = platFromList.find(el => el.id === 'DIDI') || {}
      } else {
        didi = JSON.parse(platFromList).find(el => el.id === 'DIDI') || {}
      }
    } catch (e) {
      console.log('e-->>' + e)
    }
    const { corporationBinding } = didi
    const bindingStatus = corporationBinding.find(v => v.platformId === 'DIDI')
    api.invokeService('@third-import:get:bindingUrl', bindingStatus.id, false).then(async data => {
      let { url } = data
      api.invokeService('@layout:open:link', url)

      this.source = await sourceMap.add(
        'DIDI',
        `/api/tpp/v2/corporationBindings/getBindingProgress/$${bindingStatus.id}`,
        {
          unBinding: false
        }
      )

      this.setState({ page, status: 'loading' })

      this.source.addEventListener('TPP:BINDING:SUCCESS', e => {
        this.source = sourceMap.delete('DIDI')
        api.invokeService('@third-import:get:platFormList')
        toast.success(i18n.get('滴滴开通成功'))
        sourceMap.emit('DIDI:AUTH:PROGRESS', { status: 'success' })
      })

      this.source.addEventListener('TPP:BINDING:ERROR', e => {
        this.source = sourceMap.delete('DIDI')
        toast.error(i18n.get('滴滴开通失败'))
        sourceMap.emit('DIDI:AUTH:PROGRESS', { status: 'fail', errorReason: i18n.get('滴滴开通失败') })
        this.setState({ status: 'fail', errorReason: i18n.get('滴滴开通失败') })
      })

      this.source.addEventListener('error', e => {
        this.source = sourceMap.delete('DIDI')
        toast.error(i18n.get('滴滴开通失败，原因：网络错误'))
        sourceMap.emit('DIDI:AUTH:PROGRESS', { status: 'fail', errorReason: i18n.get('网络超时') })
      })
    })
  }

  handleReAuth = () => {
    this.handleAuthDiDi.call(this, 2)
  }

  handleRemind = () => {
    let { staffsAdmin } = this.props
    let whiteList = staffsAdmin.map(o => o.id)
    api.invokeService('@layout:select:staff', { whiteList }).then(result => {
      api.invokeService('@third-import:post:remind', 'DIDI', 'BINDING', [result.id])
    })
  }

  renderText = isAdmin => {
    if (isAdmin) {
      return (
        <div className={styles.openDidi_text}>
          <p>{i18n.get('打车数据全面对接')}</p>
          <p>{i18n.get('管理更透明，审批更轻松')}</p>
        </div>
      )
    }
    return <div className={styles.openDidi_text}>{i18n.get('一键导入消费，报销更方便')}</div>
  }

  renderBtn = isAdmin => {
    if (isAdmin) {
      return (
        <div onClick={this.handleAuthDiDi.bind(this, 2)} className={styles.openDidi_btn}>
          {i18n.get('立即开通', {})}
        </div>
      )
    }
    return (
      <div onClick={this.handleRemind} className={styles.openDidi_btn}>
        {i18n.get('提醒管理员开通')}
      </div>
    )
  }

  render() {
    let img =
      window.__PLANTFORM__ === 'HUAWEI'
        ? 'https://pic.ekuaibao.com/Hosedidi.png'
        : 'https://pic.ekuaibao.com/EKB_homepage_notification.png'
    const { me_info, onCancel } = this.props
    const { status } = this.state
    if (!me_info) {
      return null
    }
    const isAdmin = me_info.permissions && me_info.permissions.includes('SYS_ADMIN')
    if (status) {
      return (
        <div className={styles.openDidi_wrap}>
          <OpenResult
            errorReason={this.state.errorReason}
            onReAuth={this.handleReAuth}
            openStatus={status}
            onCancel={onCancel}
          />
        </div>
      )
    }

    return (
      <div className={styles.openDidi_wrap}>
        <div className={styles.openDidi_logo}>
          <img src={img} />
        </div>
        <div className={styles.openDidi_subTitle}>
          <span>{i18n.get('行业首创出行报销解决方案')}</span>
        </div>
        <div className={styles.openDidi_banner}>
          <img src={PNG_BANNER} />
        </div>
        {this.renderText(isAdmin)}
        {this.renderBtn(isAdmin)}
      </div>
    )
  }
}

export default OpenDidi
