import moment from 'moment'
import { Resource } from '@ekuaibao/fetch'
import key from './key'
import { Fetch } from '@ekuaibao/fetch'

const tpp = new Resource('/api/tpp/v2')

//获取员工的滴滴绑定关系
export function getStaffBinding(done) {
  return {
    type: key.GET_STAFF_BINDING,
    payload: Fetch.GET('/api/didiErp/v1/staffBinding/exists'),
    done
  }
}

//获取滴滴平台的授权范围
export function corporationBinding(value = 'DIDI') {
  return {
    type: key.GET_CORPORATION_BINDING,
    payload: tpp.GET(`/corporationBindings/corporationBinding`, { platform: value })
  }
}

//员工获取绑定验证码接口
export function sendCreateBindingCaptcha(params) {
  return {
    type: key.SEND_CREATE_BINGDING_CAPTCHA,
    payload: tpp.POST('/staffBindings/getCaptcha/$platformId', params)
  }
}

export function clearCaptchaId() {
  return {
    type: key.CLEAR_CAPTCHA_ID,
    payload: null
  }
}

export function clearStaffBind() {
  return {
    type: key.CLEAR_STAFF_BINDING,
    payload: false
  }
}

//员工使用手机号绑定
export function createBinding(captchaId, captchaCode, phone, unbinding = false, done) {
  return {
    type: key.CREATE_BINDING,
    payload: Fetch.POST(`/api/tpp/v2/staffBindings/bindingByPhone/$DIDI`, null, {
      body: {
        phone,
        captchaCode,
        unbinding
      }
    }),
    done
  }
}

// 解绑
export function modifyBinding(done) {
  return {
    type: key.MODIFY_BINDING,
    payload: Fetch.PUT('/api/didiErp/v1/modify', {}),
    done
  }
}

//获取员工订单，query需传入获取的订单最晚日期截止于什么时候，格式2017-07-01。此列表尚未支持标准分页实现
export function getOrders(ids, done) {
  return {
    type: key.GET_ORDERS,
    payload: Fetch.GET('/api/didiErp/v1/orders', {
      endDate: moment()
        .add(7, 'd')
        .format('YYYY-MM-DD'),
      count: 2999
    }),
    ids,
    done
  }
}

//重试接口
export function retryBindDiDi(params) {
  const { platformId, ...others } = params

  return {
    type: key.RETRY_BIND_DIDI,
    payload: tpp.POST('/staffBindings/bindingByPhone/$platformId', { platformId }, others)
  }
}
