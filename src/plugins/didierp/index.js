// import { fnOpenDidiErp } from './import-didi/utils'
import { app as api } from '@ekuaibao/whispered'
import { corporationBinding } from './index-action'

export default [
  {
    id: '@didierp',
    reducer: () => require('./didierp.reducer').default,

    'import:didi': async (...args) => {
      const { fnOpenDidiErp } = await import('./import-didi/utils')
      return fnOpenDidiErp(...args).then(result => ({ type: 'didi', data: result }))
    },

    'corporation:binding'() {
      return api.dispatch(corporationBinding())
    }

    // onafter() {
    //   return api.dispatch(corporationBinding())
    // }
  },

  {
    point: '@bill:third:import',
    onload: () => import('./import-didi')
  },

  {
    point: '@@layers',
    prefix: '@didierp',
    onload: () => require('./layers').default
  },

  {
    path: '/didierp',
    ref: '/',
    exact: true,
    onload: () => import('./didierp')
  },
  {
    path: '/didierp/message',
    ref: '/',
    onload: () => import('./messageIndex')
  },
  {
    path: '/didierp/captcha',
    ref: '/',
    exact: true,
    onload: () => import('./captcha')
  },
  {
    path: '/didierp/captcha/:type',
    ref: '/',
    exact: true,
    onload: () => import('./captcha')
  },
  {
    path: '/didierp/unBind',
    ref: '/',
    onload: () => import('./unBind')
  },
  {
    path: '/didierp/errorReason',
    ref: '/',
    onload: () => import('./errorReason')
  }
]
