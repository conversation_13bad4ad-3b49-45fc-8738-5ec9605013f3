/**************************************************
 * Created by nany<PERSON>ingfeng on 28/07/2017 10:07.
 **************************************************/
import { fnOpenDidiErp } from './utils'
import PNG_DIDI_BTN from '../images/didi-btn.png'

const info = {
  iconBtn: PNG_DIDI_BTN,
  title: i18n.get('滴滴企业版'),
  type: 'didi',
  group: 'consumption',
  onClick(...args) {
    return fnOpenDidiErp(...args).then(result => {
      return { type: 'didi', data: result, invoiceForm: 'unify' }
    })
  }
}

export default info
