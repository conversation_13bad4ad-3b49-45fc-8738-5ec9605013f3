import { app as api } from '@ekuaibao/whispered'

function openFeetype(orders, resolve) {
  api.open('@feetype:SelectFeeTypeModal').then(result => {
    api.invokeService('@feetype:get:feetype', result.id).then(feetype => {
      resolve({ orders, feetype })
    })
  })
}

export function fnOpenDidiErp(orderImported = [], deleteOrders = []) {
  const { promise, resolve } = window.PromiseDefer()
  api.open('@didierp:DidiErp', { orderImported, deleteOrders }).then(result => {
    openFeetype(result, resolve)
  })
  return promise
}
