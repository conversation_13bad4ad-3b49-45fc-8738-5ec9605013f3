/***************************************************
 * Created by nanyuantingfeng on 2020/9/18 18:20. *
 ***************************************************/
import { Resource } from '@ekuaibao/fetch'
const tpp = new Resource('/api/tpp/v2')
export function corporationBinding(value = 'DIDI') {
  return {
    type: `@didierp/GET_CORPORATION_BINDING`,
    payload: tpp.GET(`/corporationBindings/corporationBinding`, { platform: value })
  }
}
