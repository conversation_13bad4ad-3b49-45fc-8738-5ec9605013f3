/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/27 下午4:15
 */
@import url('../../styles/ekb-colors');

.openDidi_wrap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: @gray-1;
  overflow-y: auto;
}

.openDidi_logo {
  margin: 100px 136px 24px;
  flex-shrink: 0;
  img {
    width: 100%;
  }
}

.openDidi_subTitle {
  position: relative;
  padding: 0 136px 66px;
  flex-shrink: 0;
  font-size: 26px;
  color: #A2ABAF;
  text-align: center;
  &:before {
    content: '';
    position: absolute;
    top: 18px;
    left: 136px;
    right: 136px;
    height: 2px;
    background: #E7E7E7;
  }
  span {
    position: relative;
    z-index: 2;
    background: @gray-1;
    padding: 0 20px;
  }
}

.openDidi_banner {
  padding: 0 136px 44px;
  flex-shrink: 0;
  img {
    width: 100%;
  }
}

.openDidi_text {
  flex: 1;
  font-size: 30px;
  color: #3A3F3F;
  text-align: center;
  p {
    margin: 0;
    &:nth-child(2) {
      margin-top: 10px;
    }
  }
}

.openDidi_btn {
  flex-shrink: 0;
  margin: 0 136px 64px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border-radius: 8px;
  background-color: #F98919;
  font-size: 30px;
  font-weight: 500;
  color: @gray-1;
}