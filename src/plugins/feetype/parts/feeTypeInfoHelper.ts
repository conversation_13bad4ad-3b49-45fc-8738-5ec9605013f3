import { SpecificationIF, ComponentIF } from '@ekuaibao/ekuaibao_types'
import { standardMoneyForReceivingAmount } from '../../../components/utils/fnInitalValue'
import { allowSelectionReceivingCurrency } from '../../../elements/puppet/Details/utils'
import { get } from 'lodash'

// 获取模版上添加的收款币种
export const getBillReceivingCurrency = (billData: Record<string, any>): string | null => {
  if (!billData) {
    return null
  }
  return billData?.payeeId?.receivingCurrency || billData?.receivingCurrency
}

export const deleteReceivingTemplate = (template:any[])=>{
  const rceivingAmountIndex = template.findIndex(v => v.field === 'receivingAmount' && v.type === 'money')
  if (rceivingAmountIndex > -1) {
    template.splice(rceivingAmountIndex, 1) // 删除 receivingAmount
  }
}

export const addReceivingAmount = (billData: Record<string, any>, specification: SpecificationIF) => {
  const receivingCurrencyNum = getBillReceivingCurrency(billData)
  if (!!receivingCurrencyNum) {
    const hasReceivingAmount = specification.components.find(oo => oo.field === 'receivingAmount')
    if (!hasReceivingAmount) {
      const feeAmountIndex = specification.components.findIndex(oo => oo.field === 'amount')
      const feeAmount = specification.components[feeAmountIndex]
      specification.components.splice(feeAmountIndex + 1, 0, {
        ...feeAmount,
        field: 'receivingAmount',
        name: 'receivingAmount',
        label: i18n.get('收款金额'),
        cnLabel: '收款金额',
        enLabel: i18n.get('收款金额'),
        // editable: true,
        optional: false
      } as ComponentIF)
    }
  }
}

export const addReceivingAmountValue = (billData: Record<string, any>, value: Record<string, any>) => {
  const receivingCurrencyNum = getBillReceivingCurrency(billData)
  if (!receivingCurrencyNum || (value && value.receivingAmount)) {
    return value
  }
  value = value || {}
  value.receivingAmount = standardMoneyForReceivingAmount(value?.amount, receivingCurrencyNum)
  return value
}

export function getAllowSelectionReceivingCurrency(billSpecification:Record<string, any>, billData:Record<string, any>){
  return allowSelectionReceivingCurrency(billSpecification) && getBillReceivingCurrency(billData)
}

export const detailsTemplateAddReceivingAmount = (details:any[]) => {
  return details.map(d => {
    // tslint:disable-next-line:prefer-const
    let { specificationId, feeTypeForm } = d
    if (feeTypeForm.receivingAmount) {
      const template = addReceivingAmount({receivingCurrency:feeTypeForm.receivingAmount?.standardNumCode}, specificationId)
      specificationId = template
    }
    return d
  })
}

export function clearDetailsReceivingAmount(details:any[]) {
  return details?.map(fee => {
    const feeTem = get(fee, 'specificationId.components', [])
    const newFee = { ...fee, feeTypeForm: { ...fee.feeTypeForm } }
    // 删除模板收款字段
    deleteReceivingTemplate(feeTem)
    delete newFee.feeTypeForm.amount // 费用金额
    delete newFee.feeTypeForm.receivingAmount // 收款金额
    return newFee
  })
}

const triggerFeeTypeDependence = (field: any) => {
  const hasDepenceFeeType = field?.dependence?.find((dependence: any) => dependence?.dependenceId === 'flow.FeeType')
  return !!hasDepenceFeeType
}

export const fnHandleComponentLoadFinish = ({ field, bus, feetype }: any) => {
  if (triggerFeeTypeDependence(field)) {
    bus.emit(
      'on:dependence:change',
      { key: 'flow.FeeType', id: feetype?.id, dependenceFeeType: true },
      {
        isInit: true
      }
    )
  }
}
