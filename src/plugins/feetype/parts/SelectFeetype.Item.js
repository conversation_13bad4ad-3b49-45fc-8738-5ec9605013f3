import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by nanyuantingfeng on 8/23/16 11:55.
 **************************************************/
import React from 'react'

import Highlighter from 'react-highlight-words'
import { getFeetypeName } from '../../bill/utils/billUtils'
// import EKBIcon from '../../../elements/ekbIcon'
const EKBIcon = app.require('@elements/ekbIcon')

// const feetype = require('../../../images/feetype')
const feetype = app.require('@images/feetype')

function colorPath(colorPathFeeTypes, currentFeeType, id) {
  let isChecked = false
  let isColorPath = false

  colorPathFeeTypes &&
    colorPathFeeTypes.forEach(o => {
      if (o.id === id) {
        isColorPath = true
      }
    })
  if (currentFeeType && currentFeeType.id === id) {
    isChecked = true
    isColorPath = true
  }
  return { isChecked, isColorPath }
}

/**
 * @return {null}
 */
function ItemArrow(props) {
  let { isChecked, dataSource, onNextClick } = props

  if (isChecked) {
    return (
      <div className="checked">
        <EKBIcon name="#EDico-check-default" className="icon_size" />
      </div>
    )
  }

  if (dataSource.children && dataSource.children.length) {
    return (
      <div className="next-arrow" onClick={onNextClick}>
        <EKBIcon name="#EDico-right-default" className="icon_size" />
      </div>
    )
  }

  return null
}

export function Item(props) {
  let { dataSource, onLineClick, onNextClick, colorPathFeeTypes, currentFeeType, isSearch, searchWords } = props
  let { id, color, icon, name, enName, fullname, enFullName ,code, children, label, description, marginLevel, notSupportSearch } = dataSource
  const showName = getFeetypeName(dataSource)
  fullname = i18n.currentLocale === 'en-US' && enFullName && enName ? enFullName : fullname // 兼容英文场景下的全称
  let handleClick = onLineClick
  if (children && children.length) {
    handleClick = onNextClick
  }
  let { isColorPath, isChecked } = colorPath(colorPathFeeTypes, currentFeeType, id)
  let subSearchWords = searchWords.slice()
  const nameAndEnName = i18n.currentLocale === 'en-US' && enName ? enName : name
  let textToHighlight = label || nameAndEnName
  const wordAfter = '…'
  if (searchWords && searchWords[0].length > 0 && textToHighlight.toLowerCase().indexOf(searchWords[0].toLowerCase()) > -1) {
    //关键字不在可视范围内，……需要高亮处理
    let subSearchWord = subSearchWords[0]
    if (textToHighlight.length > 10) {
      let searchBegin = textToHighlight.toLowerCase().indexOf(subSearchWord.toLowerCase())
      textToHighlight = `${textToHighlight.substring(0, 10)}${wordAfter}`
      searchBegin >= 10 ? subSearchWord = wordAfter :
        subSearchWord = `${subSearchWord.substring(0, 10 - searchBegin)}${textToHighlight.toLowerCase().indexOf(subSearchWord.toLowerCase()) < 0 ? wordAfter : ''}`
    }
    subSearchWords[0] = subSearchWord
  }
  let itemClass = isSearch ? 'item itemSearch' : 'item'
  let fnOnClick = () => handleClick(dataSource)
  const style = {}
  if (!isSearch && marginLevel) style.marginLeft = marginLevel * 32
  // notSupportSearch：不支持搜索的父级元素，没有点击事件
  if (notSupportSearch) fnOnClick = () => {}
  return (
    <div key={id} className={itemClass} onClick={fnOnClick} style={style}>
      <div className="item-img" style={{ backgroundColor: color }}>
        <img src={icon || feetype.SVG_FEE_000} />
      </div>

      <div className="name" style={isColorPath ? { color: '#197CD9' } : null}>
        {isSearch ? (
          <span className="vertical-left-left">
            <div className="withCode">
              <Highlighter
                highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                searchWords={subSearchWords}
                textToHighlight={textToHighlight.length>10 ?`${textToHighlight.substring(0,10)}${wordAfter}` :textToHighlight}
              />
              <span className="feeCode">{code.length > 8?`${code.substring(0,3)}${wordAfter}${code.substring(code.length-4)}`:`${code}`}</span>
            </div>
            {fullname && <span className="fullname">{fullname.length>19 ?
            `${fullname.substring(0,12)}${wordAfter}${fullname.substring(fullname.length-6)}`
            :`${fullname}`}</span>}
            {description && <span className="search-desc">{`${description}`}</span>}
          </span>
        ) : (
          <span className="vertical-left-left">
            <span className='showName'>{showName}</span>
            {description && <span className="description">{`${description}`}</span>}
          </span>
        )}
      </div>

      <ItemArrow isChecked={isChecked} onNextClick={() => onNextClick(dataSource)} dataSource={dataSource} />
    </div>
  )
}

export default Item
