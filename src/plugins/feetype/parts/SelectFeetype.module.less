/**************************************************
 * Created by nanyuantingfeng on 8/23/16 11:55.
 **************************************************/
@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.select_feetype_view_wrapper {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  -webkit-overflow-scrolling: touch;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: translate3d(0, 0, 0);
  :global {
    .batch-import-tips {
      background-color: #e6f7ff;
      font-size: 32px;
      padding: 16px 32px;
      color: #595959;
      img {
        width: 32px;
        height: 32px;
        margin-right: 16px;
        margin-bottom: -4px;
      }
    }
    .search {
      padding: 16px 32px;
    }
    .recommend {
      padding: 24px 32px;
      .recommend-title {
        font: var(--eui-font-head-b1);
        color: var(--eui-text-title);
      }
      .recommend-item {
        margin: 16px 16px 0 0;
        .recommend-item-img {
          width: 32px;
          height: 32px;
          background-color: crimson;
          border-radius: 16px;
          margin-left: 8px;
          img {
            width: 32px;
            height: 32px;
          }
        }
        .recommend-item-title {
          margin: 8px;
          font: var(--eui-font-body-r1);
          color: var(--eui-text-title);
          white-space: break-spaces;
          text-align: left;
        }
      }
    }
    .bg-line {
      height: 16px;
      background-color: var(--eui-bg-base);
      display: flex;
      flex-shrink: 0;
    }
    .crumbs {
      font: var(--eui-font-head-r1);
      padding: 24px 32px;
      white-space: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      display: flex;
      flex-shrink: 0;
      .crumbs-icon {
        margin: 0 8px;
      }
    }
    .list {
      height: calc(100% - 68px);
      overflow: scroll;
      background-color: var(--eui-bg-base);
      .list-item-img {
        width: 64px;
        height: 64px;
        background-color: crimson;
        border-radius: 64px;
        img {
          width: 64px;
          height: 64px;
        }
      }
    }
    .clamp-2 {
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
    }
    .text-placeholder {
      color: var(--eui-text-placeholder);
    }
    .text-primary {
      color: var(--eui-primary-pri-500);
    }
    .text-caption {
      color: var(--eui-text-caption);
    }
    .empty {
      margin-top: calc(50vh - 50% + 104px);
      transform: translateY(-50%);
      .eui-error-block-image {
        height: 100%;
      }
    }
    .eui-list-default .eui-list-body {
      border-top: 0;
      border-bottom: 0;
    }

    // 以下为旧选择器样式
    .tree-wrapper {
      flex: 1;
      width: 100%;
      overflow-y: auto;
    }
    .list-enter-view {
      height: 128px;
      background-color: #ffffff;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      border-bottom: 2px solid #f3f3f3;
      //margin-left: 0.24rem;

      .line-wrap {
        display: flex;
        align-items: center;
        //margin-left: 0.24rem;
        div:first-child {
          width: @space-8;
          height: @space-8;
          border-radius: 30px;
          img {
            width: 64px;
            height: 64px;
          }
        }

        .name {
          flex: 1;
          display: flex;
          margin-left: 40px;
          font-size: 28px;
          line-height: 1;
          color: #54595b;
        }
      }
    }

    .item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 16px 32px;

      .item-img {
        width: 64px;
        height: 64px;
        background-color: crimson;
        border-radius: 32px;
        img {
          width: 64px;
          height: 64px;
        }
      }
      .name {
        flex: 1;
        margin-left: 24px;
        .showName {
          font: var(--eui-font-head-r1);
          color: var(--eui-text-title);
          display: -webkit-box;
          -webkit-line-clamp: 2;
          word-break: break-all;
          overflow: hidden;
        }
        .description {
          font: var(--eui-font-body-r1);
          color: var(--eui-text-placeholder);
          display: -webkit-box;
          -webkit-line-clamp: 2;
          word-break: break-all;
          overflow: hidden;
        }
      }
      .checked {
        color: #197cd9;
      }
      .next-arrow {
        display: flex;
        color: #17202e;
      }
      .icon_size {
        width: 32px;
        height: 32px;
      }
      &:active {
        background: rgba(29, 43, 61, 0.09);
      }
    }
    .itemSearch{
      align-items: flex-start;
      margin: 0 24px;
      .search-desc {
        .font-weight-2;
        font-size: 24px;
        color: @color-black-2;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        line-height: 38px;
      }
      .fullname{
        font-size: 24px;
        color: @color-black-3;
        line-height: 38px;
        overflow: hidden;
        text-overflow: ellipsis;
        justify-content: space-between;
      }
      .withCode{
        display: flex;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        justify-content: space-between;
        width: 100%;
        .feeCode{
          font-size: 24px;
          float:right;
          color:@color-black-3;
        }
      }
    }
  }
}
