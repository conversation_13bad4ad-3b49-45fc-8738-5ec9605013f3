import { app } from '@ekuaibao/whispered'
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/8/18 下午1:57.
 */
import styles from './importCards.module.less'

import React from 'react'
import ThirdPartyCard from 'ekbc-thirdParty-card'
import { checkPayerInfo } from '../../../lib/util'
const ImportCard = app.require('@elements/puppet/ThirdCard/ImportCard')
import SVG_PAYERNO_ERROR from '../images/invoice-payerno-error.svg'
const { fpOpenDetail } = app.require('@elements/puppet/ThirdCard/fnOpenDetails')
const EKBIcon = app.require('@elements/ekbIcon')

const svg = (
  <span style={{ display: 'inline-flex' }}>
    <EKBIcon name="#EDico-didi" style={{ color: '#1E96FA', width: '0.32rem', height: '0.32rem' }} />
  </span>
)

export function ImportCards(props) {
  const orderData = props.orderData || []
  const { isEdit, submitter, submitterId, handleOldDiDiCardClick } = props
  const subId = (submitter && submitter.id) || submitterId
  if (orderData.length === 0) {
    return null
  }
  return (
    <div className={styles['import-cards']}>
      {orderData.length > 0 && <div className="card-title">{i18n.get('订单信息')}</div>}
      {orderData.length > 0 && renderInvoiceError(orderData)}
      {orderData.map((line, index) => {
        if (line.platform === 'DIDI' || line.platform === 'YEEGO' || line.platform === 'HOSE_MALL') {
          line.orderType = line.platform === 'DIDI' ? 'car' : line.orderType
          if (!line.form && line.data) {
            line.form = line.data
          }
          if (line.platform === 'YEEGO' || line.platform === 'HOSE_MALL') {
            line.platformName = i18n.get('易快报自营')
          }

          return (
            <div key={index + line.id} className={styles.thirdCardWrap} onClick={() => handleOldDiDiCardClick(line)}>
              <ThirdPartyCard dataSource={line} iconFont={line.platform === 'DIDI' ? svg : null} />
            </div>
          )
        } else {
          return (
            <ImportCard key={line.id} data={line} onClick={() => fpOpenDetail({ data: line }, subId)} isEdit={isEdit} />
          )
        }
      })}
    </div>
  )
}

const renderInvoiceError = orderData => {
  if (!orderData || orderData[0].platform !== 'fp') {
    return null
  }
  const { showError } = checkPayerInfo(orderData[0].data)
  if (showError) {
    return (
      <div className="invoice-err fs-14">
        <img className="mr-5" src={SVG_PAYERNO_ERROR} />
        <span style={{ color: '#ff7c7c' }}>{i18n.get('发票信息与企业开票信息不一致')}</span>
      </div>
    )
  }
}
