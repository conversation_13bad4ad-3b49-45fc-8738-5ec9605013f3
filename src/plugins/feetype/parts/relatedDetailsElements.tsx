import { app } from '@ekuaibao/whispered'
import './relatedDetailsElements.less'
import React from 'react'
// import Money from '../../../elements/puppet/Money'
const Money = app.require('@elements/puppet/Money')

export function DocumentInfo(props: any) {
  return (
    <div className="info">
      <div className="info-item">{props.code}</div>
      <div className="seg" />
      <div className="info-item des">{props.specificationName}</div>
      <div className="seg" />
      <div className="info-item">{props.ownerName}</div>
    </div>
  )
}

export function DetailInfo(props: any) {
  return (
    <div className="info">
      <div className="info-item">{props.code}</div>
      <div className="seg" />
      <div className="info-item">{props.date}</div>
    </div>
  )
}

export function MoneyText(props: any) {
  return (
    <div className="money-text">
      <Money
        color={props.color || '#1D2B3D'}
        value={props.value}
        valueSize={props.valueSize || 14}
        fontWeight={props.fontWeight || 600}
        currencySymbol={props.currencySymbol}
      />
      <div className="info-item">{props.text}</div>
    </div>
  )
}
