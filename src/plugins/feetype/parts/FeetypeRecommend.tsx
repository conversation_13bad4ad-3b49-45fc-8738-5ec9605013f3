/**
 *  Created by pw on 2019-12-16 20:06.
 */
import React, { useState, MouseEvent } from 'react'
import './FeetypeRecommend.less'
import { FeeTypeIF } from '@ekuaibao/ekuaibao_types'
let CLICK_RECOMEND = false

interface Props {
  recommends: FeeTypeIF[]
  defaultValue: FeeTypeIF
  onChanged: (feeType: FeeTypeIF) => void
}

export default function(props: Props) {
  const { recommends = [], defaultValue } = props

  if (!recommends.length) {
    return null
  }

  const __defaultValue = defaultValue ? defaultValue : recommends.length ? recommends[0] : undefined
  const [value, setSelectValue] = useState(__defaultValue)

  const handlePanelClick = (e: MouseEvent<HTMLDivElement>) => {
    if (!CLICK_RECOMEND) {
      if (e.cancelable) {
        e.preventDefault && e.preventDefault()
      }
      e.stopPropagation()
    }
    if (CLICK_RECOMEND) {
      CLICK_RECOMEND = false
    }
  }

  const handlePanelItemClick = (value: FeeTypeIF) => {
    handleOnChange(value)
    setSelectValue(value)
    CLICK_RECOMEND = true
  }

  const handleOnChange = (value: FeeTypeIF) => {
    const { onChanged } = props
    onChanged && onChanged(value)
  }

  return (
    <div className="feeType_recommend_panel" onClick={handlePanelClick}>
      <div className="title">{i18n.get('推荐')}</div>
      <div className="panel_wrapper">
        {recommends.map((line: FeeTypeIF, index) => {
          const cls = value.id === line.id ? 'panel_item_selected' : 'panel_item_normal'
          return (
            <span key={index} className={`panel_item ${cls}`} onClick={() => handlePanelItemClick(line)}>
              {line.fullname}
            </span>
          )
        })}
      </div>
      <div className="title all">{i18n.get('全部')}</div>
    </div>
  )
}
