@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.feeType_recommend_panel {
  padding: @space-8 @space-6 0;
  .title {
    .font-size-2;
    .font-weight-2;
    margin-bottom: @space-4;
    color: @color-black-2;
  }
  .panel_wrapper {
    display: flex;
    flex-wrap: wrap;
    .panel_item {
      .font-size-3;
      .font-weight-2;
      max-width: 672px;
      margin-right: @space-4;
      margin-bottom: @space-4;
      padding: @space-4 @space-6;
      border-radius: @space-1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      direction: rtl;
      text-align: left;
      :last-child {
        margin-right: 0;
      }
    }
    .panel_item_normal {
      color: rgba(29, 43, 61, 1);
      background: rgba(29, 43, 61, 0.06);
    }
    .panel_item_selected {
      color: rgba(25, 124, 217, 1);
      background: rgba(24, 144, 255, 0.1);
    }
  }
  .all {
    margin-top: @space-8;
    margin-bottom: @space-4;
  }
}
