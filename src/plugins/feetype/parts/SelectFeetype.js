import { app, app as api } from '@ekuaibao/whispered'
import styles from './SelectFeetype.module.less'
import React, { PureComponent } from 'react'
const SelectTreeNode = app.require('@elements/puppet/SelectTreeNode')
import { Fetch } from '@ekuaibao/fetch'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
const SVG_ENTER_LINE = app.require('@images/feetype-enter-line.svg')
import SVG_TIPS from '../images/batch-import-tips.svg'
import feetypeUrlX from '../../../lib/enums.sourcechannelmap.feetypeUrl'
const feetypeUrl = feetypeUrlX[window.__PLANTFORM__]
import Item from './SelectFeetype.Item'
import RecommendFeeType from './SelectFeetype.RecommendFeeType'
export { Item, RecommendFeeType }
const feetype = app.require('@images/feetype')
import { SearchBar, Tag, List, Ellipsis, ErrorBlock } from "@hose/eui-mobile";
import { OutlinedDirectionRight, IllustrationMiddleNoContent, OutlinedTipsDone } from '@hose/eui-icons'
import { highLightSpan } from '../../../lib/util/highLightSpan'
import { getBoolVariation } from '../../../lib/featbit'

function ConfigInfo(props) {
  let { onClick } = props
  return (
    <div className="list-enter-view" onClick={onClick}>
      <div className="line-wrap" style={{ padding: '8px 16px' }}>
        <div>
          <img src={SVG_ENTER_LINE} />
        </div>
        <div className="name" style={{ color: '#54595b' }}>
          {i18n.get('多级费用类型配置说明')}
        </div>
      </div>
    </div>
  )
}

export default class SelectFeetype extends PureComponent {
  // list: 将树结构拍平展示的列表数据
  state = {
    list: null,
    fullPath: [{ name: '费用类型' }],
    currentShowList: [],
    searchValue: '',
    searchResultList: []
  }
  handleLineClick = data => {
    let { onLineClick } = this.props
    return onLineClick && onLineClick(data)
  }

  handleNextClick = data => {
    let { onNext } = this.props
    return onNext && onNext(data)
  }

  handleCancel = () => {
    let { onCancel } = this.props
    return onCancel && onCancel()
  }

  handleListEnterLine = () => {
    let url = `${window.location.origin}/applet/${feetypeUrl}`
    if (process.env.NODE_ENV === 'development') {
      url = `${window.location.origin}/${feetypeUrl}`
    }
    api.invokeService('@layout:open:link', url)
  }

  loop = (dataSource = [], list, marginLevel = 0) => {
    dataSource.forEach(el => {
      list.push({
        ...el,
        children: undefined,
        marginLevel,
        notSupportSearch: !!el.children?.length
      })
      if (el.children?.length) {
        this.loop(el.children, list, marginLevel + 1)
      }
    })
  }

  componentDidMount() {
    // displayList：将树拍平，展示列表
    if (isHongShanTestingEnterprise(Fetch.ekbCorpId) || this.props.displayList) {
      const list = []
      this.loop(this.props.dataSource, list)
      this.setState({ list })
    }
  }

  getName = (feetype, maxSize = 0) => {
    const { name, enName } = feetype
    const showName = i18n.currentLocale === 'en-US' && enName ? enName : name
    return maxSize && showName.length > maxSize ? showName.slice(0, maxSize) + '...' : showName
  }
  getFullName = (feetype) => {
    const { fullname, enFullName } = feetype
    const showFullName = i18n.currentLocale === 'en-US' && enFullName ? enFullName : fullname
    return showFullName ? showFullName.split('／').join('/') : ''
  }

  handleClickFeetype = (item) => {
    if (!item.children?.length) {
      return this.handleLineClick(item)
    }
    const fullPath = [...this.state.fullPath, item]
    this.setState({
      currentShowList: item.children,
      fullPath
    })
  }
  handleClickCrumbs = (index) => {
    const fullPath = this.state.fullPath.slice(0, index + 1)
    this.setState({
      currentShowList: fullPath.length > 1 ? fullPath[fullPath.length - 1].children : [],
      fullPath
    })
  }
  handleSearch = (value) => {
    this.setState({ searchValue: value })
    if (!value) return
    const dataSource = this.state.list || this.props.dataSource
    const searchResult = []
    const searchLoop = (data) => {
      data.forEach(item => {
        const { name, enName, code, children } = item
        if (children?.length) {
          searchLoop(item.children)
        } else if (name.includes(value) || enName.includes(value) || code.includes(value)) {
          searchResult.push(item)
        }
      })
    }
    searchLoop(dataSource)
    this.setState({ searchResultList: searchResult })
  }

  renderList = ({ dataSource, content, description, arrow }) => {
    return (
      <List className='list'>
        {dataSource.map((item) => (
          <List.Item
            key={item.id}
            prefixIcon={
              <div className="list-item-img" style={{ backgroundColor: item.color }}>
                <img src={item.icon || feetype.SVG_FEE_000} />
              </div>
            }
            description={description(item)}
            arrow={arrow && arrow(item) || Boolean(item.children?.length)}
            onClick={() => this.handleClickFeetype(item)}
            data-testid="feetype-list-item"
          >
            {content(item)}
          </List.Item>
        ))}
      </List>
    )
  }

  renderFeetypeList = () => {
    let { dataSource, recommends, currentFeeType, colorPathFeeTypes } = this.props
    const { list, fullPath, currentShowList } = this.state
    dataSource = list || dataSource
    dataSource = fullPath?.length > 1 ? currentShowList : dataSource
    const showRecommends = Boolean(recommends?.length && fullPath.length === 1)
    const currentFeeTypeIds = currentFeeType?.id ? [ currentFeeType.id, ...colorPathFeeTypes?.map(item => item.id) ] : []
    return <>
      {showRecommends &&
        <div className='recommend'>
          <div className='recommend-title'>{i18n.get('常用推荐')}</div>
          <div className='recommend-tags'>
            {recommends.map(item => {
              return (
                <Tag className='recommend-item' onClick={() => this.handleLineClick(item)}>
                  <div className="recommend-item-img" style={{ backgroundColor: item.color }}>
                    <img src={item.icon || feetype.SVG_FEE_000} />
                  </div>
                  <div className={'recommend-item-title'}>
                    {this.getFullName(item) || this.getName(item)}
                  </div>
                </Tag>
              )
            })}
          </div>
        </div>
      }
      {/* 分割线 */}
      {<div className='bg-line'></div>}
      {/* 面包屑 */}
      <div className='crumbs'>
        {this.state.fullPath.map((item, index) => {
          const isLast = index === this.state.fullPath.length - 1
          return (
            <span>
              <span
                className={isLast ? 'text-caption' : 'text-primary'}
                onClick={() => this.handleClickCrumbs(index)}
              >
                {this.getName(item, 11)}
              </span>
              {!isLast && <OutlinedDirectionRight fontSize={16} className='crumbs-icon text-caption' />}
            </span>
          )
        })}
      </div>
      {/* List */}
      {this.renderList({
        dataSource,
        content: (item) => <Ellipsis direction="end" rows={2} content={this.getName(item)} className={currentFeeTypeIds.includes(item.id) && 'text-primary'}/>,
        description: (item) => <Ellipsis direction="end" rows={2} content={item.description} />,
        arrow: (item) => item.id === currentFeeType?.id && <OutlinedTipsDone fontSize={20} className='text-primary' />
      })}
    </>
  }

  renderSearchFeetypeList = () => {
    const { searchValue, searchResultList } = this.state
    if (!searchResultList?.length) {
      return (
        <div className='empty'>
          <ErrorBlock
            image={<IllustrationMiddleNoContent fontSize={200} />}
            title={i18n.get('暂无数据')}
          />
        </div>
      )
    }
    return this.renderList({
      dataSource: searchResultList,
      content: (item) => {
        return (
          <div className='clamp-2'>
            {highLightSpan(this.getName(item), searchValue)}
            <span className='text-placeholder'>
              ({highLightSpan(item.code, searchValue)})
            </span>
          </div>
        )
      },
      description: (item) => {
        return <>
          <Ellipsis direction="middle" rows={1} content={this.getFullName(item)} />
          <Ellipsis direction="end" rows={1} content={item.description} />
        </>
      }
    })
  }

  render() {
    const { dataSource, colorPathFeeTypes, currentFeeType, isBatchImport, isShowCancelBtn, recommends } = this.props
    const { searchValue } = this.state
    return (
      <div className={styles.select_feetype_view_wrapper}>
        {isBatchImport && (
          <div className="batch-import-tips">
            <img src={SVG_TIPS} />
            {i18n.get('将选中的项目明细，按发票生成费用类型相同的消费明细')}
          </div>
        )}

        {getBoolVariation('mfrd-3173-select-feetype') ?
          <>
            {window.__PLANTFORM__ === 'DING_TALK' && <ConfigInfo onClick={this.handleListEnterLine} />}
            <div className='search'>
              <SearchBar
                data-testid="search-input"
                placeholder={i18n.get('搜索名称或编码')}
                onChange={(value) => this.handleSearch(value)}
              />
            </div>
            {!searchValue && this.renderFeetypeList()}
            {searchValue && this.renderSearchFeetypeList()}
          </>
          :
          <div className="tree-wrapper">
            <SelectTreeNode
              hasSearchBar={true}
              eleHeight={67}
              renderHeader={() => {
                if (window.__PLANTFORM__ === 'DING_TALK') {
                  return <ConfigInfo onClick={this.handleListEnterLine} />
                }
                return null
              }}
              renderTopPanel={({ isSearch }) => (
                <RecommendFeeType
                  isSearch={isSearch}
                  defaultValue={currentFeeType}
                  recommends={recommends}
                  onClick={this.handleLineClick}
                />
              )}
              headerTip={({ searchCount }) => i18n.get(`共搜索到{__k0}条`, { __k0: searchCount })}
              headerTipStyle={{ paddingLeft: 16 }}
              renderItem={props => (
                <Item {...props} colorPathFeeTypes={colorPathFeeTypes} currentFeeType={currentFeeType} />
              )}
              dataSource={this.state?.list || dataSource}
              onLineClick={this.handleLineClick}
              onCancel={this.handleCancel}
              onNextClick={this.handleNextClick}
              isShowCancelBtn={isShowCancelBtn}
            />
          </div>
        }
      </div>
    )
  }
}
