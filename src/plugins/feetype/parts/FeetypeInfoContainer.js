import React, { PureComponent } from 'react'
import styles from './FeetypeInfoContainer.module.less'
import { app } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import MessageCenter from '@ekuaibao/messagecenter'
import FeetypeInfoEditable from './FeetypeInfoEditable'
import FeetypeInfoReadonly from './FeetypeInfoReadonly'
import ActionsPart from './ActionsPart'
const { Box, Footer } = app.require('@elements/layout/Box')
const formatRiskNotice = app.require('@bill/utils/formatRiskData.formatRiskNotice')
import { getCalculateField } from '../feetype.action'
import { formatRiskData } from './feetypeUtils'
import cloneDeep from 'lodash/cloneDeep'

@EnhanceConnect(
  state => ({
    RiskPromptOptimization: state['@common'].powers.RiskPromptOptimization,
    globalFieldsMap: state['@common'].baseDataProperties.baseDataPropertiesMap,
    autoExpenseWithBillStriction: state['@common'].powers.autoExpenseWithBillStriction,
    toggleManage: state['@common'].toggleManage
  }),
  { getCalculateField }
)
export default class FeetypeInfoContainer extends PureComponent {
  static defaultProps = {
    isEdit: true
  }

  state = {
    value: this.props.value,
    current: this.props.idx,
    template: this.props.template,
    feetype: this.props.feetype,
    calFields: {},
    loading: false
  }
  apportionIdList = []
  detailId = ''

  bus = new MessageCenter()
  componentDidMount() {
    this.bus.watch('feetype:edit:loading:change', this.loadingChange)
    window.addEventListener('feetype:edit:loading:show', this.showLoading)
    window.addEventListener('feetype:edit:loading:hidden', this.hideLoading)
    this.bus.on('invoice:disable:click', this.handleInvoiceDisable)
    app.dataLoader('@common.payerInfo').load()
    const { toggleManage } = this.props
    if (toggleManage?.tg_fee_detaill_sliding) {
      setTimeout(() => {
        this.forceUpdate()
      }, 200)
    }
  }

  showLoading = () => {
    this.loadingChange(true)
  }

  hideLoading = () => {
    this.loadingChange(false)
  }

  componentWillUnmount() {
    window.removeEventListener('feetype:edit:loading:show', this.showLoading)
    window.removeEventListener('feetype:edit:loading:hidden', this.hideLoading)
  }

  componentWillMount() {
    let { isEdit } = this.props
    if (!isEdit) {
      return this.getAutoCalFields()
    }
    this.bus.un('invoice:disable:click', this.handleInvoiceDisable)
  }
  handleInvoiceDisable = params => {
    const { id, reason } = params
    let { disableInvoiceList = {} } = this.state
    disableInvoiceList[id] = reason
    this.setState({ disableInvoiceList })
    this.forceUpdate()
  }
  getAutoCalFields = () => {
    let { submitterId, billType, getCalculateField } = this.props
    const { feetype } = this.state
    let specification = billType === 'requisition' ? feetype.requisitionSpecificationId : feetype.expenseSpecificationId
    if (!submitterId || !specification) return

    getCalculateField({
      submitterId: submitterId.id,
      specificationId: specification
    }).then(action => {
      if (action.error) return
      let autoRules = action.payload.items || []
      this.setState({ calFields: autoRules[0] })
    })
  }
  loadingChange = loading => {
    this.setState({ loading })
  }
  handlePageChange = type => {
    let { ds, externals } = this.props
    let { current } = this.state
    let idx = type === 'prev' ? current - 1 : current + 1
    let obj = ds[idx]
    let value = {},
      template = [],
      feetype = {},
      preOrNextExternal = ''
    if (obj) {
      value = obj.feeTypeForm
      template = obj.specificationId
      feetype = obj.feeTypeId
      preOrNextExternal = externals ? (externals[value.detailId] ? externals[value.detailId] : {}) : {}
    }

    this.setState({ value, current: idx, template, feetype, preOrNextExternal }, this.getAutoCalFields)
  }

  handleType = () => {
    let { value, enterType } = this.props
    if (enterType) {
      return enterType
    }
    return value ? 'edit' : 'new'
  }

  saveDetailAndApportionId = (detailId, apportionId) => {
    if (Array.isArray(apportionId)) {
      this.apportionIdList = apportionId
    } else {
      apportionId && this.apportionIdList.push(apportionId)
    }
    this.detailId = detailId
  }
  //明细点击保存时 点了禁用的发票传给单据
  handlecloseAndReturnValue = data => {
    // 原来的 handlecloseAndReturnValue 逻辑
    const originHandleCloseAndReturnValue = data => {
      const { closeAndReturnValue, saveTempDisableInvoice } = this.props
      const { disableInvoiceList } = this.state
      saveTempDisableInvoice && disableInvoiceList && saveTempDisableInvoice(disableInvoiceList)
      closeAndReturnValue && closeAndReturnValue(data)
    }

    // 分摊计算的 open 逻辑可能会有问题，提前 catch 然后走原来的逻辑
    try {
      const { closeAndReturnValue, saveTempDisableInvoice } = this.props
      const { disableInvoiceList } = this.state
      const { specificationId: specification } = data instanceof Array ? data?.[data.length - 1] ?? {} : data
      this.bus
        .invoke('get:feetype:components')
        .then(template => {
          const apportionsTemplate = template.find(t => t.field === 'apportions')
          // 由于分摊计算的 open 会在提交单据的时候进行检查，所以要将自动计算的 open 值重新赋值回去
          const specificationResult = cloneDeep(specification)
          const apportionsComponent = specificationResult.components.find(v => v.field === 'apportions')
          if (apportionsComponent) {
            apportionsComponent.open = apportionsTemplate?.open ?? apportionsComponent.open
          }

          data.specificationId = specificationResult
          saveTempDisableInvoice && disableInvoiceList && saveTempDisableInvoice(disableInvoiceList)
          closeAndReturnValue && closeAndReturnValue(data)
        })
        .catch(() => {
          originHandleCloseAndReturnValue(data)
        })
    } catch (e) {
      originHandleCloseAndReturnValue(data)
    }
  }
  renderEditable() {
    /**
     * feetype : 当前选择的消费类型
     * feetypes : 消费类型的全集
     * globalFields : 全局字段列表
     * index : details 列表里面的索引,很重要,
     * 判断是新加还是修改, 这个对后面的处理很是总要
     */
    let {
      billType,
      feetype,
      value,
      globalFields,
      closeAndReturnValue,
      billTemplate,
      bus,
      risks = [],
      submitterId,
      ds,
      idx,
      flowId,
      flowAllowModifyFields,
      external,
      template,
      detailId,
      globalFieldsMap,
      setValidateError,
      selectCurrencyDisable,
      foreignNumCode,
      billState = 'new',
      sourcePage,
      ...others
    } = this.props
    const components = template && template.components
    const currentRisks = risks.filter(element => element.loc === idx)
    let { notices, type } = formatRiskData(currentRisks)
    const { loading } = this.state
    return (
      <Box bus={this.bus} className={styles.feeTypeInfo_Wrap}>
        <Box>
          <FeetypeInfoEditable
            index={idx}
            {...others}
            sourcePage={sourcePage}
            billBus={bus}
            foreignNumCode={foreignNumCode}
            selectCurrencyDisable={selectCurrencyDisable}
            ds={ds}
            flowId={flowId}
            submitterId={submitterId}
            billType={billType}
            billState={billState}
            feetype={feetype}
            value={value}
            globalFields={globalFields}
            billTemplate={billTemplate}
            external={external}
            notices={notices}
            type={type}
            components={components}
            globalFieldsMap={globalFieldsMap}
            flowAllowModifyFields={flowAllowModifyFields}
            saveDetailAndApportionId={this.saveDetailAndApportionId}
          />
        </Box>
        <Footer style={{ height: 'auto' }}>
          <ActionsPart
            {...others}
            sourcePage={sourcePage}
            external={external}
            loading={loading}
            setValidateError={setValidateError}
            originValue={value}
            type={this.handleType()}
            detailId={this.detailId || detailId}
            apportionIdList={this.apportionIdList}
            closeAndReturnValue={this.handlecloseAndReturnValue}
            formBus={bus}
            billState={billState}
          />
        </Footer>
      </Box>
    )
  }

  renderReadonly() {
    let {
      globalFields,
      billTemplate,
      risks = [],
      ds,
      idx,
      submitterId,
      billType,
      flowId,
      billBus,
      closeAndReturnValue,
      external,
      globalFieldsMap,
      hideActionsPart,
      RiskPromptOptimization,
      canAddTag,
      detailFlow,
      fnRefreshList,
      ...others
    } = this.props

    let { value, current, template, feetype, calFields, preOrNextExternal } = this.state
    const currentRisks = risks.filter(element => element.loc === current)
    let { notices, type } = formatRiskData(currentRisks)
    //费用超标提示
    let externalData = preOrNextExternal ? preOrNextExternal : external
    let noticeList = formatRiskNotice({
      riskData: externalData,
      components: template.components,
      RiskPromptOptimization,
      globalFieldsMap,
      isFeeDetail: true
    })
    if (noticeList.length) {
      notices = notices.concat(noticeList)
    }

    return (
      <Box bus={this.bus} className={styles.feeTypeInfo_Wrap}>
        <Box>
          <FeetypeInfoReadonly
            {...others}
            feetype={feetype}
            value={value}
            globalFields={globalFields}
            template={template}
            billTemplate={billTemplate}
            risk={currentRisks}
            external={externalData}
            submitterId={submitterId}
            billType={billType}
            calFields={calFields}
            flowId={flowId}
            billBus={billBus}
            notices={notices}
            closeAndReturnValue={closeAndReturnValue}
          />
        </Box>
        <Footer style={{ height: 'auto' }}>
          {!hideActionsPart && (
            <ActionsPart
              canAddTag={canAddTag}
              type="read"
              details={ds}
              idx={idx}
              detailFlow={detailFlow}
              fnRefreshList={fnRefreshList}
              currentIdx={current}
              onPageChange={this.handlePageChange}
              showPagination={others.showPagination}
            />
          )}
        </Footer>
      </Box>
    )
  }

  render() {
    let { isEdit } = this.props
    if (isEdit) {
      return this.renderEditable()
    } else {
      return this.renderReadonly()
    }
  }
}
