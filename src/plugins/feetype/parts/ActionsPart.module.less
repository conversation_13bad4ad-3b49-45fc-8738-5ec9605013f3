@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.actions_part_wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 32px 0 var(--brand-1);
}

.actions_part_btnGroup {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 20px 32px;
  :global {
    .eui-button {
      flex: 1 1;
      height: 80px;
      margin-right: @space-4;
      &:last-child{
        margin-right: 0px;
      }
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.left_action_wrapper {
  display: flex;
  align-items: center;
}

.right_action_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 100%;
  font-size: 28px;
  color: #ffffff;
  background: var(--brand-base);
  cursor: pointer;
}

.dis_right_action_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 100%;
  font-size: 28px;
  color: #ffffff;
  background: var(--brand-base);
  cursor: pointer;
  opacity: 0.4;
}

.prev_action {
  font-size: 28px;
  color: var(--brand-base);
  line-height: 100px;
  margin-left: 15px;
}

.next_action {
  font-size: 28px;
  color: var(--brand-base);
  line-height: 100px;
  margin-right: 15px;
}

.page_content {
  font-size: 26px;
  color: #9ea3a5;
  line-height: 100px;
}

.disabled {
  color: #9ea3a5;
}

.import_action {
  height: 100%;
  background-color: #ffffff;
  display: flex;
  font-size: 36px;
  align-items: center;
  :global {
    .left {
      color: var(--brand-base);
      flex: 1;
      text-align: center;
    }
    .right {
      flex: 1;
      background-color: var(--brand-base);
      color: #ffffff;
      border-radius: 8px;
      height: 76px;
      margin-right: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
