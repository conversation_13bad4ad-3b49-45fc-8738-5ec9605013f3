import { cloneDeep, get, groupBy } from 'lodash'
import { MoneyMath } from '@ekuaibao/money-math'
/**
 *  Created by gym on 2018/9/25 下午6:24.
 */

export function formatRiskData(currentRisks) {
  if (!currentRisks) return
  let notices = []
  let type = {}
  currentRisks
    .filter(e => e.type !== 'calculate')
    .map(risk => {
      notices.push({ content: risk.errorMsg })
      type = risk.allowSubmit ? 'warning' : 'error'
    })
  return { notices, type }
}
