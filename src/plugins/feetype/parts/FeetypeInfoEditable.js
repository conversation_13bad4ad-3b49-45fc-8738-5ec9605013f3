import { app, app as api } from '@ekuaibao/whispered'
const isNeedUpdateResult = app.require('@lib/parser.isNeedUpdateResult')
const isNeedUpdateCustomizeResult = app.require('@lib/parser.isNeedUpdateCustomizeResult')
import { filterFeeTypeTemplate } from '../../../lib/util/parser.parseAsMeta'

import { Dynamic } from '@ekuaibao/template'
const editable = app.require('@components/dynamic/index.editable')
import createDOMForm from 'rc-form/lib/createDOMForm'
const SelectSpecificationForFeeType = app.require('@elements/puppet/SelectSpecificationForFeeType')
const { getAutoCalResultOnField } = app.require('@bill/utils/autoCalResult')
const { getCustomizeCalResultOnField, callCustomizeCalByQueue, checkValueOrIdEqual } = app.require(
  '@bill/utils/customizeCalculate'
)
import { parseShowValue2SaveValue } from '../../bill/utils/formatUtil'
import { getEffectiveCurrencyInfo } from '../../bill/utils/billFetchUtil'
const { modifyBillOfLimitField } = app.require('@bill/utils/parseSpecification')
const {
  getForeignNumCode,
  isSelectCurrencyDisable,
  standardValueMoney,
  handleGetDimensionById,
  constantValue
} = app.require('@components/utils/fnInitalValue')
import { nanoid } from 'nanoid'
import { Fetch } from '@ekuaibao/fetch'
import { ImportCards } from './ImportCards'
import { EnhanceConnect } from '@ekuaibao/store'
import MessageCenter from '@ekuaibao/messagecenter'
import { isFunction, uuid } from '@ekuaibao/helpers'
import {
  getAutoCalResult,
  setValidateError,
  getOfficialCardSetting,
  getPayments,
  getFeeTypeTemplateById,
  getPresetTemplate
} from '../feetype.action'
import { MoneyMath } from '@ekuaibao/money-math'
import styles from './FeetypeInfoEditable.module.less'
import { getV, isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'

const {
  formatFeetypeValuesForInvoice,
  formatNewTemplateValue,
  getInvoiceIdsByFeeTypeForm,
  checkCSCMoney,
  filterCSCFields
} = app.require('@bill/utils/billUtils')

import { get, cloneDeep, uniq, isEqual, each, isEmpty } from 'lodash'
const RiskNotice = app.require('@elements/puppet/RiskNotice')
const formatRiskNotice = app.require('@bill/utils/formatRiskData.formatRiskNotice')
import { getCurrencyBaseAttribute } from '../../../lib/money-conversion'
import FeeTypeBase from './FeeTypeBase'
import {
  toast,
  debounce,
  getBillKey,
  hideLoading,
  showLoading,
  localStorageSet,
  getUrlParamString
} from '../../../lib/util'
const getDefaultValue = app.require('@components/utils/getInitialValue')
import { Button, List } from 'antd-mobile'
import InvoiceMappingValue from '../../../lib/InvoiceMappingValue'
const { related } = app.require('@components/utils/Related')
import { handlerCurrencyMoneySelectChange, setDefaultCurrencyMoney } from '../../bill/utils/defaultCurrency.js'
import { updateInvoiceDeduction } from '../../../lib/invoice/InvoicePriceTaxSeparated'
import { getInvoiceDisableInfo, getInvoiceMarkInfo } from '../../invoice-form/utils/invoiceDisableUtils'
import {
  filterDataLinkFields,
  getDependenceFieldOfDataLink,
  getDataLinkPermissionFieldComponent,
  handleAutoAssignOfOneResultOfDataLink
} from '../../bill/utils/dataLinkUtils.openDataLinkEntityList'
import { getCardToOrder } from '../../officialCard/officialCard.action'
import Big from 'big.js'
import classNames from 'classnames'
import { fnIsRiskError } from '../../bill/utils/formatRiskData.formatRiskNotice'
import { getValidateErrorByShow, getSpecificationHiddenFields } from '../../bill/utils/billUtils'
import { Form } from '@hose/eui-mobile'
const FormItem = Form.Item
import CustomWrapperWidget from '../../home5/elements/components/CustomWrapperWidget'
import { callFnByQueue, callFnByQueueNew } from '../../bill/utils/callFbByQueue'
import {
  addReceivingAmount,
  addReceivingAmountValue,
  getBillReceivingCurrency,
  getAllowSelectionReceivingCurrency,
  fnHandleComponentLoadFinish
} from './feeTypeInfoHelper'
import { changeFormValueByFieldName, foreignTimesRate } from '../../../components/utils/fnInitalValue'
import { getBoolVariation } from "../../../lib/featbit";
import { resetForeignInvoiceForEntity } from '../../../components/utils/fnCurrencyObj'
import { useNewAutomaticAssignment } from '../../../components/utils/fnAutoDependence'


function create(T) {
  //屏蔽一些由setState，setFieldsValue,onChange 引发的不必要的dynamicChange，eg：setState({value})后 changedValues 是整个表单会引发不必要的 自动/计算/联动赋值
  let TEMPLATE_REF
  const FORMCOMPONENT = createDOMForm({
    onValuesChange(props, changedValues) {
      const filteredChangedValues = cloneDeep(changedValues)
      const { form } = TEMPLATE_REF?.props ?? {}
      const preValues = form?.getFieldsValue && form?.getFieldsValue()
      Object.keys(filteredChangedValues)?.forEach(field => {
        if (checkValueOrIdEqual(filteredChangedValues[field], preValues?.[field])) {
          delete filteredChangedValues[field]
        }
      })
      setTimeout(() => {
        //先走的  onValuesChange
        //再运行  setFieldValue
        props.bus.emit('dynamic:value:changed', changedValues, filteredChangedValues)
      }, 0)
    }
  })(T)

  return React.forwardRef((props, ref) => {
    return <FORMCOMPONENT {...props} ref={ref} wrappedComponentRef={inst => (TEMPLATE_REF = inst)} />
  })
}

export const specificationKeyMap = {
  expense: 'expenseSpecification',
  requisition: 'requisitionSpecification',
  permit: 'requisitionSpecification',
  receipt: 'receiptSpecification',
  corpPayment: 'expenseSpecification',
  reimbursement: 'expenseSpecification'
}

@EnhanceConnect(
  state => ({
    mappingRelation: state['@third-import'].mappingRelation,
    lastChoice: state['@common'].lastChoice.choiceValue,
    payeeComponentData: state['@bill'].payeeComponentData,
    baseDataPropertiesMap: state['@common'].baseDataProperties.baseDataPropertiesMap,
    validateError: state['@bill'].validateError,
    dimensionCurrencyInfo: state['@bill'].dimensionCurrencyInfo,
    filterFeeTypes: state['@common'].newFilterFeeTypes.items,
    canUseDefaultCurrency: state['@common'].powers.powerCodeMap?.indexOf('162005') >= 0, //默认币种设置
    customizeQueryPower: state['@common'].powers.customizeQuery,
    RiskPromptOptimization: state['@common'].powers.RiskPromptOptimization,
    civilServiceCard: state['@common'].powers.CivilServiceCard,
    expenseSpecAfterFiltered: state['@home'].expenseSpecAfterFiltered,
    disableInvoicePower: state['@common'].powers.KA_DISABLE_INVOICE,
    allStandardCurrency: state['@common'].allStandardCurrency,
    me_info: state['@common'].me_info.staff,
    standardCurrency: state['@common'].standardCurrency,
    orgConfigVisibilityActive:
      !!state['@common'].organizationConfig.visibilityConfigs?.length &&
      !state['@common'].visibilityStaffOptions.fullVisible
  }),
  {
    getAutoCalResult,
    setValidateError
  }
)
export default class FeetypeInfoEditable extends FeeTypeBase {
  _$UUID

  isMount = false
  constructor(props) {
    super(props)
    let { feetype, value, foreignNumCode, selectCurrencyDisable, disableInfo, markInfo, formData, billSpecification } = props
    let allowReceivingCurrency = getAllowSelectionReceivingCurrency(billSpecification, formData)
    value = this.setOverInvoiceBytDimension(value)
    value = addReceivingAmountValue(formData, value)
    this.state = {
      feetype,
      value: value,
      copyValue: value,
      specification: null,
      template: null,
      autoCalFields: [],
      max: NaN,
      min: NaN,
      calFields: {},
      customizeQueryRule: [],
      noticeList: [],
      selectCurrencyDisable,
      foreignNumCode,
      settlementId: '', // 公务卡设置的结算方式ID
      presetTemplateId: '', // 快速报销根据企业id获取预置单据模版id，快速报销菜单中报销明细自动计算使用
      legalEntityMultiCurrency: '', // 快速报销根据企业id获取预置法人实体多币种id，快速报销菜单中报销明细自动计算使用
      getPresetTemplateCount: 0, // 查询presetTemplateId的次数，小概率有第一次查不到的情况，重新查一次
      markInfo,
      disableInfo,
      dependenceFieldOfDataLink: []
    }
    this.allowSelectionReceivingCurrency = allowReceivingCurrency
    this.timer = new Date().getTime()
    this.oneResultDataLinkLock = true
  }

  async updateAutoCalFields(specification, onlyQueryFields) {
    let { submitterId, billSpecification = {}, dimensionCurrencyInfo, customizeQueryPower, me_info } = this.props
    const { presetTemplateId } = this.state
    const specificationId = get(specification, 'id')
    if (billSpecification?.type === 'permit') {
      submitterId = me_info
    }
    const id = typeof submitterId === 'object' ? submitterId.id : submitterId
    const legalEntityId = dimensionCurrencyInfo?.dimension?.id
    if (customizeQueryPower) {
      this.isFirstCustomizeCalFinished = false
      //保存后再打开等表单赋值后再去查询联查监听字段，不然手动修改后可能会被重新查询覆盖
      const delayGetQueryField = onlyQueryFields ? 1000 : 0
      setTimeout(() => {
        this.updateCustomizeCalResult(undefined, undefined, true, onlyQueryFields)
      }, delayGetQueryField)
    }
    const action = await app.invokeService(
      '@bill:getCalculateField',
      specificationId,
      id,
      billSpecification.id,
      legalEntityId,
      {
        presetTemplateId
      }
    )
    if (action.error) return
    let autoRules = action.payload.items || []
    if (!autoRules.length) return
    let autoCalFields = autoRules[0].fields //参与计算的字段
    const autoCalOnFields = autoRules[0].onFields //被计算的字段
    if (getBoolVariation('cyxq-75092')) {
      callFnByQueueNew({}, () => this.updateAutoCalResult(true))
    } else {
      this.updateAutoCalResult(true)
    }
    this.setState({ autoCalFields, autoCalOnFields, calFields: autoRules[0] })
    return { autoCalFields, autoCalOnFields, calFields: autoRules[0] }
  }

  addRelatedComponet(template) {
    const { allowAdd } = related.specificationConfig
    if (allowAdd) {
      const { flowId } = this.props
      related.setFlowId(flowId)
      let obj = template && template.find(line => line.name === 'linkDetailEntities')
      if (!obj) {
        const { value = {} } = this.state
        const detailId = this.fnDetailId(value)
        obj = { name: 'linkDetailEntities', type: 'linkDetailEntities', showLabel: false, _tempConsumId: detailId }
      }
      template.push(obj)
    }
    return template
  }

  fnDetailId = value => {
    const detailId1 = get(value, 'detailId')
    const arrLinkDetails = get(value, 'linkDetailEntities', []) || []
    const dataList = arrLinkDetails.length > 0 ? arrLinkDetails[0].dataList : []
    const detailId2 = dataList && dataList.length > 0 && dataList[0]._tempConsumId
    return detailId1 || detailId2 || uuid()
  }

  getOtherFeeTypeObj = (list = [], feeTypeId) => {
    let otherFeeType = undefined
    let hasFeeType = false
    let fn = (list, feeTypeId) => {
      each(list, item => {
        if (otherFeeType && hasFeeType) return
        if (!otherFeeType && item.id !== feeTypeId && !item.children?.length) {
          otherFeeType = item
        }
        if (!hasFeeType && item.id === feeTypeId && !item.children?.length) {
          hasFeeType = true
        }
        fn(item.children, feeTypeId)
      })
    }
    if (list && list.length > 0) fn(list, feeTypeId)
    return { otherFeeType, hasFeeType }
  }

  reselectFeetype = () => {
    const { filterFeeTypes, feetype } = this.props
    if (!filterFeeTypes?.length) return feetype
    const { otherFeeType, hasFeeType } = this.getOtherFeeTypeObj(filterFeeTypes, feetype.id)
    if (hasFeeType) return feetype
    return otherFeeType
  }

  componentWillMount() {
    let {
      bus,
      billType,
      value,
      external,
      globalFieldsMap,
      payeeComponentData,
      sourcePage,
      globalFields,
      isPermitForm,
      ds = [],
      billSpecification,
      payeeInfoComponent,
      isModifyBill,
      limitFieldRequireds,
      RiskPromptOptimization,
      dataFromOrder,
      hideFields,
      canEditFieldWhiteList = [],
      autoExpenseWithBillStriction,
      formData
    } = this.props
    // 根据费用明细可见范围，获取费用明细
    const selectedFeetype = this.reselectFeetype()
    api.invokeService('@feetype:get:feetype', selectedFeetype.id).then(async feetype => {
      if (!feetype) {
        return
      }
      if (!feetype.expenseSpecification || !feetype.requisitionSpecification) {
        const id = billType === 'expense' ? feetype.expenseSpecificationId : feetype.requisitionSpecificationId
        const response = await getFeeTypeTemplateById(id)
        if (billType === 'expense') {
          feetype.expenseSpecification = response?.value
        } else {
          feetype.requisitionSpecification = response?.value
        }
      }
      let specification = feetype[specificationKeyMap[billType]]
      // 收款金额处理
      addReceivingAmount(formData, specification)

      if (get(payeeComponentData, 'visible') && !specification.components.find(oo => oo.field === 'feeDetailPayeeId')) {
        specification.components.push(payeeComponentData.payeeComponent)
      }
      if (sourcePage === 'recordExpends' && payeeInfoComponent) {
        if (!specification.components.find(oo => oo.field === 'feeDetailPayeeId')) {
          specification.components.push(payeeInfoComponent)
        }
      }
      let editableFieldList = canEditFieldWhiteList
      if (autoExpenseWithBillStriction && billType === 'expense' && sourcePage !== 'recordExpends') {
        editableFieldList = ['invoiceForm']
      }
      if (editableFieldList?.length && sourcePage !== 'eCardExpense') {
        specification.components = specification.components.map(component => {
          if (editableFieldList.includes(component.field)) {
            return component
          }
          return { ...component, editable: false, optional: true }
        })
      }
      if (sourcePage == 'eCardExpense') {
        const isActive = getBoolVariation('moreinformation')
        specification.components = specification.components.filter(component => {
          const item = globalFields.find(i => i?.name === component?.field)
          if (isActive) { // 谷老板让放出来更多的字段，包括自动计算，联动赋值
            // 处理逻辑，优先处理特殊条件，易商卡消费不展示，发票，分摊不展示
            if (item?.name === 'u_易商卡消费' || item?.name === 'invoiceForm' || item?.name === 'apportions') {
              return false
            }
            return true
          } else {
            // 处理逻辑，优先处理特殊条件，易商卡消费不展示
            if (item?.name === 'u_易商卡消费') {
              return false
            }
            // 只有手动输入的展示
            return component?.defaultValue?.type === 'none' && component?.editable && !component?.optional
          }

        })
      }
      const { detailsShowFields } = dataFromOrder || {}
      let template = filterFeeTypeTemplate({ specification, globalFields, isPermitForm, detailsShowFields, hideFields })
      if (isModifyBill && limitFieldRequireds?.length) {
        template = modifyBillOfLimitField(limitFieldRequireds, template)
      }
      let onlyDs = cloneDeep(ds)
      if (!Array.isArray(onlyDs)) {
        onlyDs = [onlyDs]
      }
      const target = onlyDs.find(detail => detail.feeTypeId && detail.feeTypeId.id === feetype.id) || {}
      const oldSpecification = getV(target, 'specificationId', {})
      const { feeTypeForm } = target
      const { configs = [], type } = billSpecification
      const source = getUrlParamString(location.search, 'source') || ''
      const isLockFeeTypeVersion = getV(
        configs.find(config => config.ability === type),
        'lockFeeTypeVersion',
        false
      )
      const isUseOldVersion =
        feeTypeForm &&
        isLockFeeTypeVersion &&
        oldSpecification.id !== specification.id &&
        !isEqual(oldSpecification, specification)
      if (isUseOldVersion) {
        template = filterFeeTypeTemplate({
          specification: oldSpecification,
          globalFields,
          isPermitForm,
          detailsShowFields,
          hideFields
        })
        specification = oldSpecification
      }
      //明细已保存过再次打开时onlyQuery=true，联查不用重新赋值，仅查询监听字段;
      const onlyQuery = !!value && typeof value?.detailId !== 'undefined'
      !isUseOldVersion && this.updateAutoCalFields(specification, onlyQuery)
      template = this.addRelatedComponet(template)
      this.getHiddenFields(specification)
      this.setState({ feetype, specification, template }, () => {
        if (external) {
          bus.setFieldsExternalsData?.({ ...external })
          let noticeList = formatRiskNotice({
            riskData: external,
            components: template,
            globalFieldsMap,
            RiskPromptOptimization,
            isFeeDetail: true,
            isForbid: !isModifyBill
          })
          let externalData = cloneDeep(external)
          this.setState({ noticeList, externalData })
        }
        source == 'ModifyNow' && this.initValidateByModify()
      })
    })
    this.fnFixMinAndMax(ds, value)
    super.componentWillMount()
    bus.watch('element:ref:select:property', this.handleSelectProperty)
    bus.watch('element:select:city:click', this.handleSelectCity)
    bus.watch('dynamic:value:changed', this.handleDynamicValueChange)
    bus.watch('element:feeType:compontents', this.handleFeeTypeCompontents)
    bus.watch('element:dataLink:select:property', this.handleDataLinkSelect)
    // bus.on('element:field:changed:external', this.handleDetailExternalChange)
    bus.watch('element:money:value:changed:external', this.handleDetailExternalChange)
    bus.watch('element:apportions:value:changed:external', this.handleApportionsExternalChange)
    api.watch('element:apportions:update:localStorage', this.updateLocalStorage)
    api.watch('get:feetype:form:value', this.handleGetFormValue)
    bus.watch('get:feetype:components', this.handleGetComponents)
    bus.watch('check:cscfields:complete', this.fnCheckCSCFields)
    bus.on('element:add:one', this.handleReOne)
    bus.on('update:calculate:template', this.updateCalTemplate)
    bus.watch('element:ref:select:staffs', this.handleSelectStaffs)
    bus.watch('element:related:application', this.handleRelatedApplication)
    bus.watch('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
    api.watch('bill:invoice:disable:byApprover', this.handleDisableInvoiceByApprover)
    api.watch('bill:check:newinvoice:disable:status', this.updateInvoiceState)
    api.watch('apportion:line:changed', this.handleEditApportion)
    api.watch('amortization:line:changed', this.handleEditAmortization)
    api.watch('get:current:feeType:value', this.handleCurrentFeeTypeValue)
    bus.watch('timeField:change', this.handleChangeTimeField)

    this._$UUID = String(Math.random())
    api.backControl.create(this._$UUID, () => {
      this.backHookCallback()
    })
  }
  fnHandleFeetypeComponent = () => { }

  componentDidMount = () => {
    const { bus, civilServiceCard } = this.props
    const { feetype } = this.state
    this.handleGetPresetTemplate()
    this.isMount = true
    if (feetype) {
      setTimeout(() => {
        bus.emit('on:dependence:change', { key: 'flow.FeeType', id: feetype.id, dependenceFeeType: true })
        // 获取业务对象字段依赖列表
        this.initDataLinkPermissionFields()
        //业务对象筛选数据结构只有一条数据，自动赋值逻辑
        this.autoAssignOfOneResultOfDataLink()
      }, 2000)
    }
    if (civilServiceCard) {
      getOfficialCardSetting().then(res => {
        const settlementId = get(res, 'value.settlementId')
        if (settlementId) {
          getPayments().then(v => {
            const payments = get(v, 'items', [])
            if (payments.find(el => el.id === settlementId)) {
              this.setState({ settlementId: payments.find(el => el.id === settlementId) })
            } else {
              toast.info(i18n.get('未匹配到结算方式，请联系管理员'))
            }
          })
        }
      })
    }
  }

  getHiddenFields = async specification => {
    const hiddenFields = await getSpecificationHiddenFields(specification)
    this.setState({ hiddenFields })
  }

  handleCurrentFeeTypeValue = () => {
    const { billSpecification } = this.props
    const { feetype } = this.state
    return {
      formSpecId: billSpecification?.id ?? '',
      feeTypeSpecId: feetype?.id ?? ''
    }
  }

  handleGetPresetTemplate = () => {
    const { isQuickExpends, allStandardCurrency } = this.props
    const { presetTemplateId, getPresetTemplateCount } = this.state
    if (isQuickExpends && !presetTemplateId && !getPresetTemplateCount) {
      // 根据企业id获取预置单据模版id, 第一次无id则需要重新查一次
      getPresetTemplate().then(async res => {
        const specificationId = get(res, 'value.specificationId')
        const legalEntityMultiCurrency = get(res, 'value.legalEntityMultiCurrency')
        if (specificationId && legalEntityMultiCurrency) {
          this.setState({ presetTemplateId: specificationId, getPresetTemplateCount: 1, legalEntityMultiCurrency })
          const result = await handleGetDimensionById(legalEntityMultiCurrency)
          const baseCurrencyId = result?.form?.baseCurrencyId
          if (baseCurrencyId) {
            const { items: rates = [] } = await api.invokeService('@common:get:currency:rates:by:id', baseCurrencyId)
            const currency = allStandardCurrency.find(item => item.id === baseCurrencyId)
            api.invokeService('@bill:update:dimension:currency', { currency, rates })
          }
        } else {
          this.setState({ getPresetTemplateCount: 1 }, () => this.handleGetPresetTemplate())
        }
      })
    }
  }

  // 审批中修改自动定位错误字段
  initValidateByModify = debounce(() => {
    const { bus, isModifyBill } = this.props
    const { specification } = this.state
    if (isModifyBill && bus) {
      bus.getValueWithValidate(1).catch(e => {
        const components = specification.components
        const errors = getValidateErrorByShow(components, Object.keys(e))
        if (!errors.length) {
          setValidateError({ bill: Object.keys(e) })
        }
        return Promise.reject(e)
      })
    }
  }, 1000)

  // 获取业务对象依赖关系
  initDataLinkPermissionFields = async () => {
    let { specification } = this.state
    const items = await getDataLinkPermissionFieldComponent(specification?.id)
    this.setState({
      dependenceFieldOfDataLink: items
    })
  }

  //业务对象筛选数据结果只有一条数据时自动赋值处理
  autoAssignOfOneResultOfDataLink = debounce((fields = []) => {
    const { bus, submitterId, billState, isModifyBill } = this.props
    const { template, feetype } = this.state
    const editBill = (billState && ['draft', 'rejected'].includes(billState)) || isModifyBill

    // 唯一值处理逻辑只在新建单据时做改为在编辑态做
    if (billState && !['new', 'draft', 'rejected', 'modify'].includes(billState)) return

    let curTime = new Date().getTime()
    this.timer = curTime
    const dataLinkFields = filterDataLinkFields(template || [], fields)
    if (!dataLinkFields.length) return
    if (!this.isMount) return

    bus
      .getValue()
      .then(async res => {
        const feeTypeId = feetype?.id || ''
        const limit = 10
        const currentValue = {
          submitterId: submitterId?.id || '',
          form: { ...res, feeTypeId }
        }
        showLoading()
        const result = await handleAutoAssignOfOneResultOfDataLink({ dataLinkFields, currentValue, limit })
        if (this.timer === curTime && this.isMount) {
          const rangeOnlyOneAutomaticAssignment = useNewAutomaticAssignment() ? 'rangeOnlyOneAutomaticAssignment' : 'autoFillByFilterDataOnlyOne'
          const checkValue = []
          dataLinkFields.forEach((line, idx) => {
            const temp = result[idx]
            const { id } = res[line.field] || {}
            const isPreviousIdPresent = (temp || []).map(v => v?.dataLink?.id).includes(id) // 上一次存在的id是否在这次结果中
            if (temp && temp.length === 1 && line[rangeOnlyOneAutomaticAssignment]) {
              bus.emit('on:autoAssignDataLink', { id: temp[0]?.dataLink?.id, field: line })
            }
            if (id && !isPreviousIdPresent && temp.length >= limit) {
              // 因返回数据存在翻页，这里记录id，后面统一找
              checkValue.push({ field: line.field, id: id })
              line.filterBy = `(id.in("${id}")) && (active==true)`
            } else if (temp && temp.length !== 1 && !isPreviousIdPresent || !line[rangeOnlyOneAutomaticAssignment] && !isPreviousIdPresent) {
              if (!editBill || (editBill && !this.oneResultDataLinkLock)) {
                bus.emit('clear:autoAssignDataLink', { field: line })
              }
            }
          })
          // 确定数据删除
          if (checkValue.length) {
            const res2 = await handleAutoAssignOfOneResultOfDataLink({
              dataLinkFields,
              currentValue,
              filterBySearch: true
            })

            dataLinkFields.forEach((line, idx) => {
              const temp = res2[idx]
              const { id } = res[line.field] || {}
              const isPreviousIdPresent = (temp || []).map(v => v?.dataLink?.id).includes(id) // 上一次存在的id是否在这次结果中

              if (!isPreviousIdPresent) {
                if (!isEditBill || (isEditBill && !this.oneResultDataLinkLock)) {
                  bus.emit('clear:autoAssignDataLink', { field: line })
                }
              }
            })
          }
        }
        this.oneResultDataLinkLock = false
      })
      .finally(() => {
        hideLoading()
      })
  }, 300)

  handleGetComponents = () => {
    return this.state.template
  }

  componentWillUnmount() {
    let { bus, setValidateError, isQuickExpends } = this.props
    super.componentWillUnmount()

    this.isMount = false
    setValidateError({ detail: [] })
    bus.un('element:ref:select:property', this.handleSelectProperty)
    bus.un('element:select:city:click', this.handleSelectCity)
    bus.un('dynamic:value:changed', this.handleDynamicValueChange)
    bus.un('element:feeType:compontents', this.handleFeeTypeCompontents)
    bus.un('element:add:one', this.handleReOne)
    bus.un('element:dataLink:select:property', this.handleDataLinkSelect)
    // bus.un('element:field:changed:external', this.handleDetailExternalChange)
    bus.un('element:money:value:changed:external', this.handleDetailExternalChange)
    bus.un('element:apportions:value:changed:external', this.handleApportionsExternalChange)
    api.un('element:apportions:update:localStorage', this.updateLocalStorage)
    api.un('get:feetype:form:value', this.handleGetFormValue)
    api.un('get:feetype:components', this.handleGetComponents)
    bus.un('check:cscfields:complete', this.fnCheckCSCFields)
    bus.un('element:ref:select:staffs', this.handleSelectStaffs)
    bus.un('element:related:application', this.handleRelatedApplication)
    bus.un('update:calculate:template', this.updateCalTemplate)
    bus.un('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
    api.un('bill:check:newinvoice:disable:status', this.updateInvoiceState) //新增发票查询禁用状态
    api.un('bill:invoice:disable:byApprover', this.handleDisableInvoiceByApprover) //审批人禁用发票
    api.un('apportion:line:changed', this.handleEditApportion) //编辑分摊时进行业务对象联查
    api.un('amortization:line:changed', this.handleEditAmortization)
    api.un('get:current:feeType:value', this.handleCurrentFeeTypeValue)
    bus.un('timeField:change', this.handleChangeTimeField)

    api.backControl.remove(this._$UUID)
    if (isQuickExpends) {
      api.invokeService('@bill:update:dimension:currency', null) //清空法人实体多币种信息
    }
  }

  handleGetFormValue = () => {
    const { bus } = this.props
    return bus.getValue()
  }

  // overload ”handleSelectStaff“ of FeeTypeBase
  handleSelectStaff = (whiteList, field) => {
    return api.invokeService('@layout:select:staff', {
      whiteList,
      showBottom: field.optional,
      isVisibilityStaffs: this.props.orgConfigVisibilityActive
    })
  }

  handleSelectStaffs = data => {
    const { orgConfigVisibilityActive } = this.props
    return this.fnGetBillDate(data.staffRangeRule).then(isByRule => {
      return api
        .invokeService('@layout:select:multiple:staff', {
          ...data,
          isVisibilityStaffs: orgConfigVisibilityActive,
          isByRule
        })
        .then(data => {
          return data
        })
    })
  }
  async fnGetBillDate(ruleId) {
    const result = ruleId && ruleId !== 'false'
    if (!result) return result

    const { bus = new MessageCenter(), formData, billSpecification } = this.props
    if ((bus.getValue && !isFunction(bus.getValue)) || !billSpecification) {
      return
    }
    let formValue = {}
    try {
      formValue = (await bus.getValue()) || {}
    } catch (e) {
      console.log(e)
    }
    const value = (bus.$extraParseDetail && bus.$extraParseDetail(formValue)) || {}
    if (value) {
      value.feeTypeId = get(value, 'feeTypeId.id')
      value.specificationId = get(value, 'specificationId.id')
    }
    formData.details = [value]
    const billData = parseShowValue2SaveValue(formData)
    return api.dataLoader('@common.staffRangeByRule').reload({ ...billData, ruleId })
  }
  handleRelatedApplication = data => {
    const { applicationListDetails, dataFromOrder } = this.props
    return api.open('@feetype:RelatedApplicationModal', {
      data,
      dataSource: applicationListDetails,
      dataFromOrder
    })
  }

  handleApportionsExternalChange = (apportionId, detailId) => {
    const { saveDetailAndApportionId } = this.props
    const { externalData } = this.state
    saveDetailAndApportionId(detailId, apportionId)
    if (apportionId) {
      Array.isArray(apportionId)
        ? apportionId.forEach(id => delete externalData.apportions[id])
        : delete externalData.apportions[apportionId]
      !Object.keys(externalData.apportions).length && delete externalData.apportions
    } else {
      delete externalData.apportions
    }
    this.setFieldsExternalAndGetNotice(externalData)
  }

  handleDetailExternalChange = args => {
    const { saveDetailAndApportionId } = this.props
    const { detailId, fieldName } = args
    const { externalData } = this.state
    if (externalData) {
      saveDetailAndApportionId(detailId)
      delete externalData[fieldName]
      this.setFieldsExternalAndGetNotice(externalData)
    }
  }

  setFieldsExternalAndGetNotice = external => {
    const { bus, globalFieldsMap, RiskPromptOptimization, isModifyBill } = this.props
    const { template } = this.state
    external && bus.setFieldsExternalsData?.({ ...external })
    let noticeList = formatRiskNotice({
      riskData: external,
      components: template,
      globalFieldsMap,
      RiskPromptOptimization,
      isFeeDetail: true,
      isForbid: !isModifyBill
    })
    this.setState({ noticeList })
  }

  //检查发票禁用标记状态
  //导入发票合并生成明细时：feeData 是 object；生成多条时：feeData 是 Arr
  updateInvoiceState = async feeData => {
    if (!this.props.disableInvoicePower) return
    const { disableInfo, markInfo } = this.state
    const details = Array.isArray(feeData) ? feeData : [{ ...feeData }]
    try {
      let disableData = await getInvoiceDisableInfo({ details })
      let markData = await getInvoiceMarkInfo({ details })

      let disableList = {}
      let markList = {}
      if (disableData) {
        disableList = { ...disableData, ...disableInfo }
        this.setState({
          disableInfo: disableList
        })
      }
      if (markData) {
        markList = { ...markData, ...markInfo }
        this.setState({
          markInfo: markList
        })
      }
      if (api.has('bill:invoice:disable:change')) {
        api.invoke('bill:invoice:disable:change', disableList, markList)
      }
      this.forceUpdate()
    } catch (e) { }
  }
  //审批中禁用发票
  handleDisableInvoiceByApprover = id => {
    let { disableInfo } = this.state
    disableInfo[id].disable = true
    this.setState({ disableInfo })
    this.forceUpdate()
  }
  parseAsValue = (value, globalFieldsMap) => {
    let values = cloneDeep(value)
    Object.keys(values).forEach(item => {
      let baseItem = globalFieldsMap[item]
      if (baseItem && baseItem.dataType && baseItem.dataType.type === 'ref') {
        values[item] = values[item] && values[item].id
      }
    })
    return values
  }

  handleDataLinkSelect = data => {
    let { template, specification } = this.state
    const { flowId, index, globalFieldsMap, bus, sourcePage } = this.props
    return bus.getValue().then(async value => {
      let formValue = this.parseAsValue(value, globalFieldsMap)
      let values = bus.$extraParseDetail(cloneDeep(value))
      let saveValue = {
        form: { ...formValue, feeTypeId: get(values, 'feeTypeId.id') }
      }

      const { openDataLinkEntityList } = await app.require('@bill/utils/dataLinkUtils.openDataLinkEntityList').call()

      return openDataLinkEntityList({
        data,
        template,
        props: { ...this.props, feeTypeSpecificationId: specification?.id },
        flowId,
        index,
        isDetail: true,
        saveValue,
        sourcePage
      })
    })
  }

  handleFeeTypeCompontents = () => this.state.specification

  handleReOne = preValue => {
    const { bus, field, formData } = this.props
    const { specification } = this.state
    this.updateTemplateAndRelatedMap()
    bus.emit('continue:add:empty:apportion')
    this.getDefaultValue4Template(specification).then(defaultValue => {
      const selectCurrencyDisable = isSelectCurrencyDisable(field, [preValue])
      const foreignNumCode = getForeignNumCode(Array.isArray(preValue) ? preValue : [preValue])
      defaultValue = addReceivingAmountValue(formData, defaultValue)
      this.setState({ value: defaultValue, selectCurrencyDisable, foreignNumCode })
      this.updateAutoCalFields(specification)
    })
  }

  updateCalTemplate = async value => {
    const { bus, external, sourcePage, isQuickExpends } = this.props
    const { template, value: feeTypeValue = {} } = this.state
    const clearValidFields = []
    const fields = Object.keys(value)
    if (isHongShanTestingEnterprise(Fetch.ekbCorpId) && (isQuickExpends || sourcePage === 'recordExpends')) {
      // 自动计算逻辑优化
      const templateMap = template?.reduce((result, field) => {
        result[field.field] = field
        return result
      }, {})
      fields.forEach(field => {
        let changeField = templateMap[field]
        if (changeField) {
          changeField = { ...changeField, ...value[field] }
          if (value[field]['optional']) {
            clearValidFields.push(field)
          }
          templateMap[field] = changeField
        }
      })
      template.forEach(el => {
        const field = templateMap[el.field]
        if (field && field.attributeHide !== undefined) {
          el.attributeHide = field.attributeHide
        }

        if (field && field.optional !== undefined) {
          el.optional = field.optional
        }
      })
    } else {
      fields.forEach(field => {
        let index = template.findIndex(el => el.field === field)
        if (index !== -1) {
          template[index] = { ...template[index], ...value[field] }
          if (value[field]['optional']) {
            clearValidFields.push(field)
          }
        }
      })
    }

    if (fields?.length) {
      const stateVaue = { template: [...template] }
      if (sourcePage === 'recordExpends') {
        let formValue = (await bus.getValue()) || feeTypeValue
        if (formValue && Object.keys(formValue).length) {
          Object.keys(formValue).forEach(key => {
            const vv = formValue[key]
            if (!vv) {
              delete formValue[key]
            }
          })
        }
        stateVaue.value = formValue
      }
      this.setState({ ...stateVaue }, () => {
        if (clearValidFields && clearValidFields.length) {
          bus.getValueWithValidate(clearValidFields)
          external && bus.setFieldsExternalsData?.({ ...external })
        }
      })
    }
  }

  fnFixMinAndMax(details, dataSource = {}) {
    if (!details || details.length === 0) {
      return
    }
    const { ordersData = [], thirdPartyOrders = [] } = dataSource
    if (ordersData.length > 1) {
      let itemOrder = ordersData || thirdPartyOrders || []
      const itemAmount = dataSource.amount
      const transactRecordIds = []
      const platforms = []
      itemOrder.forEach(item => {
        if (item.transactRecordId) {
          transactRecordIds.push(item.transactRecordId)
        }
        if (item.platform === 'transact') {
          platforms.push(item.platform)
        }
      })
      if (transactRecordIds.length !== itemOrder.length || platforms.length !== itemOrder.length) {
        return
      }
      Fetch.GET(`/api/mall/v1/officialCard/[${transactRecordIds.join(',')}]`).then(res => {
        const { items = [] } = res
        let maxVal = new MoneyMath(0)
        items.forEach(item => {
          maxVal = maxVal.add(new MoneyMath(item.useBalance))
          // 修正真实可用余额
          itemOrder.forEach(item => {
            const useBalance = items.find(o => o.id === item.transactRecordId)?.useBalance
            item.data.balance = useBalance
          })
        })
        // 后端记录剩余的都是能用的
        details
          .filter(item => {
            const orders = get(dataSource, 'orders')
            const itemOrders = get(item, 'feeTypeForm.orders')
            let ods = item.feeTypeForm.ordersData,
              odsfirst = ods ? ods[0] : null
            return !!ods && odsfirst && ods.every(el => el.platform === 'transact') && itemOrders === orders
          })
          .forEach(v => {
            let val = new MoneyMath(0)
            const { amount } = v.feeTypeForm
            v.feeTypeForm.ordersData.forEach(vl => {
              const { transactRecordId: trid, id } = vl
              if (transactRecordIds.includes(trid)) {
                const useOrder = items.find(el => el.id === vl.transactRecordId)?.useOrder
                const use = useOrder && useOrder.find(o => o.orderId === id)
                if (use) {
                  val = val.plus(use.money)
                }
              }
            })
            maxVal = maxVal.add(val).minus(amount)
          })
        maxVal = maxVal.add(itemAmount).standMoney
        if (Big(maxVal).gte(0)) {
          this.setState({ max: maxVal, min: '0.01', value: dataSource })
        } else {
          this.setState({ max: '-0.01', min: maxVal, value: dataSource })
        }
      })
    } else {
      let itemOrder = ordersData[0] || thirdPartyOrders[0] || {}
      const itemAmount = dataSource.amount
      const { transactRecordId, platform } = itemOrder

      if (platform !== 'transact' || !transactRecordId) {
        return
      }
      Fetch.GET(`/api/mall/v1/officialCard/detail/$${transactRecordId}`).then(res => {
        const { useBalance, useOrder } = res
        // 后端记录剩余的都是能用的
        let maxVal = new MoneyMath(useBalance)
        details
          .filter(item => {
            let ods = item.feeTypeForm.ordersData,
              odsfirst = ods ? ods[0] : null
            return !!ods && odsfirst && odsfirst.platform === 'transact'
          })
          .forEach(v => {
            const { transactRecordId: trid, id } = v.feeTypeForm.ordersData[0]
            if (transactRecordId === trid) {
              const use = (useOrder || []).find(o => o.orderId === id)
              const { amount } = v.feeTypeForm
              if (use) {
                maxVal = maxVal.add(use.money).minus(amount)
              } else {
                maxVal = maxVal.minus(amount)
              }
            }
          })

        maxVal = maxVal.add(itemAmount).standMoney

        // 修正真实可用余额
        itemOrder.data.balance = useBalance

        if (Big(maxVal).gte(0)) {
          this.setState({ max: maxVal, min: '0.01', value: dataSource })
        } else {
          this.setState({ max: '-0.01', min: maxVal, value: dataSource })
        }
      })
    }
  }

  resetAmount = data => {
    if (data == void 0) {
      return data || {}
    }
    if (!data.amount) {
      data.amount = { standard: '0.00' }
    }
    if (data.amount && data.amount.standard && !data.amount.standardStrCode) {
      let sObj = getCurrencyBaseAttribute(
        this.props.standardCurrency || api.getState()['@common'].standardCurrency,
        'standard'
      )
      data.amount = Object.assign(data.amount, sObj)
    }
    return data
  }

  // 根据时间获取对应时期的本位币所对应的汇率
  handleChangeTimeField = async ({ date }) => {
    const { bus, timeField, dimensionCurrencyInfo, formData } = this.props
    if (!timeField) return
    const time = date || date?.[timeField] || formData?.[timeField]
    const standardCurrency = dimensionCurrencyInfo?.currency || api.getState()['@common']?.standardCurrency
    const numCode = standardCurrency?.numCode
    if (time) {
      const historyCurrencyInfo = await getEffectiveCurrencyInfo(numCode, time)
      const historyCurrency = historyCurrencyInfo?.items
      bus.emit('set:feetype:history:currency:rate', historyCurrency)
      this.setReceivingAmounRate(historyCurrency)
    } else {
      const allCurrencyRates = await api.dataLoader('@common.allCurrencyRates').load()
      const allCurrency = allCurrencyRates.filter(i => i.originalId === numCode)
      bus.emit('set:feetype:history:currency:rate', allCurrency || [])
      this.setReceivingAmounRate(allCurrency || [])
    }
  }

  // 设置收款金额的汇率
  setReceivingAmounRate = async rates => {
    const { bus } = this.props
    const value = await bus.getFieldsValue()
    const receivingAmount = value?.receivingAmount
    // 无值的情况下
    if (receivingAmount?.standardNumCode && receivingAmount?.foreignNumCode && receivingAmount?.rate && rates?.length) {
      const moneyValue = foreignTimesRate(receivingAmount, rates)
      moneyValue.rate = moneyValue.rate ?? receivingAmount.rate // 时间口径时，汇率重算覆盖了之前的汇率
      bus.setFieldsValue({
        receivingAmount: { ...moneyValue },
        isSetFielAmountTrigger: true
      })
    }
  }

  setOverInvoiceBytDimension(value) {
    const { formData } = this.props;
    const legalEntityMultiCurrency = get(formData, 'legalEntityMultiCurrency')
    // 法人实体
    if (legalEntityMultiCurrency) {
      resetForeignInvoiceForEntity(value)
    }
    return value
  }

  handleDynamicValueChange = (changeValues, filteredChangedValues) => {
    if (!changeValues) return
    const { bus, billSpecification } = this.props
    const { autoCalFields, customizeQueryRule, specification, dependenceFieldOfDataLink, autoCalOnFields } = this.state
    const changeKeys = Object.keys(changeValues)
    let needUpdate = false
    bus?.has('bill:value:changed:forAIAttachment') && bus?.emit('bill:value:changed:forAIAttachment', changeValues)
    changeKeys.forEach(key => {
      // 由于发票自动计算，发票变更强制触发自动计算
      const isInvoice = key === 'invoiceForm'
      if (isNeedUpdateResult(key, autoCalFields, autoCalOnFields) || isInvoice) {
        // this.updateAutoCalResult()
        needUpdate = true
      }
    })
    if (needUpdate) {
      if (getBoolVariation('cyxq-75092')) {
        callFnByQueueNew({}, this.updateAutoCalResult)
      } else {
        callFnByQueue(this.updateAutoCalResult)
      }
    }
    if (!filteredChangedValues) {
      filteredChangedValues = changeValues
    }
    this.handleAmountChangeEvent(changeValues)
    const filteredChangeKeys = Object.keys(filteredChangedValues)
    filteredChangeKeys.forEach(key => {
      if (isNeedUpdateCustomizeResult(key, customizeQueryRule)) {
        const fn = () => {
          this.updateCustomizeCalResult(filteredChangedValues)
        }
        if (getBoolVariation('cyxq-75092')) {
          callFnByQueueNew({}, fn)
        } else {
          callCustomizeCalByQueue(fn)
        }
      }
    })

    // 判断变更字段是否属于业务对象依赖字段,如果是执行业务对象唯一值自动赋值
    const currentDependenceField = getDependenceFieldOfDataLink(dependenceFieldOfDataLink, changeKeys)
    if (currentDependenceField) {
      this.autoAssignOfOneResultOfDataLink(currentDependenceField?.fields)
    }

    this.updateLocalStorage()
    //监听单据查询组件,系统计算的时候走
    if (changeKeys.includes('flowLinks')) return
    if (!billSpecification?.id?.includes('system:对账单') && specification?.components?.length > 0) {
      let isFlowLinks = specification?.components?.find(v => v?.field === 'flowLinks')
      if (isFlowLinks && !isFlowLinks?.editable) {
        bus.emit('flowLinkDynamicChange')
      }
    }
  }

  handleAmountChangeEvent = async changeValues => {
    if (!this.allowSelectionReceivingCurrency) return
    const { bus, standardCurrency, dimensionCurrencyInfo, formData: billData } = this.props
    const formValue = await bus.getFieldsValue()
    const receivingString = 'receivingAmount'
    const amount = 'amount'
    // 检查 changeValues 是否只包含 'amount' 或 'receivingAmount' 中的一个，但不能同时包含
    const hasAmount = changeValues.hasOwnProperty(amount);
    const hasReceivingAmount = changeValues.hasOwnProperty(receivingString);
    const setMoneyField = formValue[hasAmount ? receivingString : amount] // 需要设置的值
    const emptyA = isEmpty(setMoneyField?.standard)
    const emptyB = isEmpty(changeValues[hasAmount ? amount : receivingString])
    const receivingCurrency = getBillReceivingCurrency(billData)
    const existingProperty = hasAmount ? amount : receivingString
    let existingValue = changeValues[existingProperty]
    const emptyValueEqual = () => {
      // 兼容所有空值二次更新
      if (emptyA || emptyB) {
        return emptyA == emptyB
      } else {
        return false
      }
    }

    const equal = !isEqual(existingValue, setMoneyField) && !emptyValueEqual()
    if (
      (hasAmount || hasReceivingAmount) &&
      !(hasAmount && hasReceivingAmount) &&
      equal &&
      !changeValues?.isSetFielAmountTrigger // handleAmountAndReceivingAmountChange 这个事件触发的不再更新
    ) {
      // 处理仅存在一个属性且它们的值不相等的情况
      const currency = dimensionCurrencyInfo?.currency ?? standardCurrency // 当前单据的本位币

      if (existingProperty === amount && !existingValue) {
        existingValue = standardValueMoney('', currency) // 给默认值
      }

      const { formData, isChangeValue } = changeFormValueByFieldName(existingProperty, existingValue, formValue, {
        receivingCurrency
      })
      let updateFormValue = isChangeValue
      // if (updateFormValue && existingProperty === 'amount') {
      //   const newReceivingAmount = get(formData, 'receivingAmount')
      //   const oldReceivingAmount = get(formValue, 'receivingAmount')
      //   updateFormValue = !fnCompareEqual(newReceivingAmount, oldReceivingAmount)
      // }
      if (updateFormValue) {
        bus.setFieldsValue({ ...formData, isSetFielAmountTrigger: true })
      }
      // this.handleAmountAndReceivingAmountChange({ [existingProperty]: existingValue })
    }
  }


  updateLocalStorage = () => {
    let { index, flowId = getBillKey(), bus, value } = this.props
    if (api.has('get:bills:value')) {
      api.invoke('get:bills:value').then(bill => {
        let newBill = cloneDeep(bill)
        let { values } = newBill
        bus.getValue().then(detail => {
          detail = this.resetAmount(cloneDeep(detail))
          let newDetail = bus.$extraParseDetail(detail)
          if (value?.detailId) {
            newDetail.feeTypeForm.detailId = value.detailId
          } else {
            newDetail.feeTypeForm.detailId = nanoid()
          }
          if (index === undefined) {
            if (values.details) {
              values.details.push(newDetail)
            } else {
              values.details = [newDetail]
            }
          } else {
            values.details = values.details || []
            values.details[index] = newDetail
          }
          localStorageSet(flowId, JSON.stringify(values))
        })
      })
    }
  }
  updateAutoCalResult = async checkDefaultValue => {
    const {
      bus = new MessageCenter(),
      formData,
      billSpecification,
      baseDataPropertiesMap,
      isQuickExpends,
      me_info
    } = this.props
    const { legalEntityMultiCurrency } = this.state
    if ((bus.getValue && !isFunction(bus.getValue)) || !billSpecification) {
      return
    }
    let formValue = {}
    try {
      formValue = (await bus.getFieldsValue()) || {}
    } catch (e) {
      console.log(e)
    }
    const { template, presetTemplateId } = this.state
    if (isQuickExpends && presetTemplateId && legalEntityMultiCurrency) {
      // 快速报销明细时设置id，会在自动计算的入参billData中设置specificationId
      billSpecification.id = presetTemplateId
      formData.legalEntityMultiCurrency = {
        id: legalEntityMultiCurrency
      }
    }

    //老补助的自动计算后台要求必须传amount
    const amountField = (template && template.find(t => t.name === 'amount')) || {}
    const type = get(amountField, 'defaultValue.type')
    if (!(amountField.editable === true && type === 'formula') && (!formValue || !formValue.amount)) {
      if (!formValue) {
        formValue = {}
      }
      formValue.amount = standardValueMoney(0) //补助的自动计算后台要求必须传amount
    }
    const value = (bus.$extraParseDetail && bus.$extraParseDetail(formValue)) || {}
    if (value) {
      value.feeTypeId = get(value, 'feeTypeId.id')
      value.specificationId = get(value, 'specificationId.id')
    }
    if (billSpecification?.type === 'permit') {
      formData.submitterId = me_info
    }
    bus.emit('feetype:edit:loading:change', true)
    const needUpdateDefaultValue = checkDefaultValue && !this.props.value
    await getAutoCalResultOnField(
      baseDataPropertiesMap,
      bus,
      billSpecification,
      formData,
      'detail_',
      value,
      this.updateLocalStorage,
      template,
      needUpdateDefaultValue
    ).finally(() => {
      bus.emit('feetype:edit:loading:change', false)
    })
    if (checkDefaultValue) {
      this.props.canUseDefaultCurrency &&
        this.props.isNewCreate &&
        !this.props.flowId &&
        setDefaultCurrencyMoney.call(this)
    }
  }

  isFirstCustomizeCalFinished = false
  updateCustomizeCalResult = debounce(async (changeValues = {}, changedFields, checkDefaultValue, onlyQueryFields) => {
    const { bus = new MessageCenter(), formData, billSpecification, baseDataPropertiesMap, billState } = this.props
    if ((bus.getValue && !isFunction(bus.getValue)) || !billSpecification) {
      return
    }
    let formValue = {}
    try {
      formValue = (await bus.getValue()) || {}
    } catch (e) {
      console.log(e)
    }
    //老补助的自动计算后台要求必须传amount
    const amountField = (template && template.find(t => t.name === 'amount')) || {}
    const type = get(amountField, 'defaultValue.type')
    if (!(amountField.editable === true && type === 'formula') && (!formValue || !formValue.amount)) {
      if (!formValue) {
        formValue = {}
      }
      formValue.amount = standardValueMoney(0) //补助的自动计算后台要求必须传amount
    }
    // 开启分摊开关 但是还未添加时需要传分摊模板id，否则后端无法查询联查规则
    // const curItem = { specificationId: this.apportionSpecification }
    // let apportions = get(formValue, 'apportions')
    // if (apportions && !apportions.length) {
    //   apportions.push(curItem)
    //   formValue.apportions = apportions
    // }
    // 开启摊销开关 但是还未添加时需要传分摊模板id，否则后端无法查询联查规则
    // const curItem2 = { specificationId: this.amortizationSpecification }
    // let amortization = get(formValue, 'amortization')
    // if (amortization && !amortization.length) {
    //   amortization.push(curItem)
    //   formValue.amortization = amortization
    // }
    const { template } = this.state
    const value = (bus.$extraParseDetail && bus.$extraParseDetail(formValue)) || {}
    if (value) {
      value.feeTypeId = get(value, 'feeTypeId.id')
      value.specificationId = get(value, 'specificationId.id')
    }
    bus.emit('feetype:edit:loading:change', true)
    const needUpdateDefaultValue = checkDefaultValue && !this.props.value
    const customizeQueryRule = await getCustomizeCalResultOnField(
      baseDataPropertiesMap,
      bus,
      billSpecification,
      formData,
      'detail_',
      value,
      this.updateLocalStorage,
      template,
      needUpdateDefaultValue,
      value.specificationId,
      billState,
      changeValues,
      changedFields,
      onlyQueryFields
    )
    customizeQueryRule && this.setState({ customizeQueryRule: customizeQueryRule?.queryFields })
    if (checkDefaultValue) {
      this.isFirstCustomizeCalFinished = true
    }
    bus.emit('feetype:edit:loading:change', false)
  }, 200)
  // 打开分摊 /切换分摊模板时 仅为了查询监听字段 参数无apportion
  // 新增/编辑 单条分摊记录时 根据index apportion 组装到from表单
  handleEditApportion = async param => {
    const { index = 0, apportionSpecification, apportion, isChangeTemplate } = param
    let currentEditField = { type: 'apportion_', values: [], operate: 'editApportion' }
    if (!isChangeTemplate) {
      currentEditField.apportionIdx = index
    }
    const changedFields = [currentEditField]
    const { bus, formData, billSpecification, baseDataPropertiesMap, billState } = this.props
    if ((bus.getValue && !isFunction(bus.getValue)) || !billSpecification) {
      return
    }
    let formValue = {}
    try {
      formValue = cloneDeep(await bus.getValue()) || {}
    } catch (e) {
      console.log(e)
    }
    let curApportions = get(formValue, 'apportions', [])
    const curItem = { apportionForm: { ...apportion }, specificationId: apportionSpecification }
    if (!isChangeTemplate) {
      curApportions[index] = curItem
    } else {
      curApportions[index] = { ...curApportions[index], specificationId: apportionSpecification }
    }
    // if (!curApportions.length || !curApportions[index]) {
    //   curApportions.push(curItem)
    // } else {
    //   if (!isChangeTemplate) {
    //     curApportions[index] = curItem
    //   } else {
    //     curApportions[index] = { ...curApportions[index], specificationId: apportionSpecification }
    //   }
    // }
    formValue.apportions = curApportions
    this.apportionSpecification = apportionSpecification
    const { template } = this.state
    const value = (bus.$extraParseDetail && bus.$extraParseDetail(formValue)) || {}
    if (value) {
      value.feeTypeId = get(value, 'feeTypeId.id')
      value.specificationId = get(value, 'specificationId.id')
    }
    const customizeQueryRule = await getCustomizeCalResultOnField(
      baseDataPropertiesMap,
      bus,
      billSpecification,
      formData,
      'detail_',
      value,
      this.updateLocalStorage,
      template,
      false,
      value.specificationId,
      billState,
      {},
      changedFields,
      false
    )
    //分摊模板变更时 需要查询单据/明细上的依赖字段有哪些
    customizeQueryRule && isChangeTemplate && this.setState({ customizeQueryRule: customizeQueryRule?.queryFields })
    return customizeQueryRule
  }
  // 打开摊销 /切换摊销模板时 仅为了查询监听字段 参数无 amortization
  // 新增/编辑 单条摊销记录时 根据index amortization 组装到from表单
  handleEditAmortization = async param => {
    const { index = 0, amortizationSpecification, apportion, isChangeTemplate } = param
    let currentEditField = { type: 'apportion_', values: [], operate: 'editApportion' }
    if (!isChangeTemplate) {
      currentEditField.apportionIdx = index
    }
    const changedFields = [currentEditField]
    const { bus, formData, billSpecification, baseDataPropertiesMap, billState } = this.props
    if ((bus.getValue && !isFunction(bus.getValue)) || !billSpecification) {
      return
    }
    let formValue = {}
    try {
      formValue = cloneDeep(await bus.getValue()) || {}
    } catch (e) {
      console.log(e)
    }
    let curApportions = get(formValue, 'apportions', [])
    const curItem = { apportionForm: { ...apportion }, specificationId: amortizationSpecification }
    if (!curApportions.length || !curApportions[index]) {
      curApportions.push(curItem)
    } else {
      if (!isChangeTemplate) {
        curApportions[index] = curItem
      } else {
        curApportions[index] = { ...curApportions[index], specificationId: amortizationSpecification }
      }
    }
    formValue.apportions = curApportions
    this.apportionSpecification = amortizationSpecification
    const { template } = this.state
    const value = (bus.$extraParseDetail && bus.$extraParseDetail(formValue)) || {}
    if (value) {
      value.feeTypeId = get(value, 'feeTypeId.id')
      value.specificationId = get(value, 'specificationId.id')
    }
    const customizeQueryRule = await getCustomizeCalResultOnField(
      baseDataPropertiesMap,
      bus,
      billSpecification,
      formData,
      'detail_',
      value,
      this.updateLocalStorage,
      template,
      false,
      value.specificationId,
      billState,
      {},
      changedFields,
      false
    )
    //分摊模板变更时 需要查询单据/明细上的依赖字段有哪些
    customizeQueryRule && isChangeTemplate && this.setState({ customizeQueryRule: customizeQueryRule?.queryFields })
    return customizeQueryRule
  }
  getDefaultValue4Template = specification => {
    const { lastChoice, submitterId, globalFieldsMap } = this.props
    const defaultValueMap = {}
    const defaultValueList = specification.components.map(c => {
      const field = globalFieldsMap[c.field]
      return getDefaultValue(c, field, lastChoice, submitterId).then(v => {
        if (v !== undefined) {
          defaultValueMap[c.field] = v
        }
        return
      })
    })
    return Promise.all(defaultValueList).then(_ => {
      return defaultValueMap
    })
  }

  handleChangeFeeTypeClick = async () => {
    let {
      bus,
      billType,
      external,
      globalFieldsMap,
      payeeComponentData,
      billSpecification,
      recordFeeTypes,
      sourcePage,
      globalFields,
      isPermitForm,
      ds = [],
      payeeInfoComponent,
      RiskPromptOptimization,
      civilServiceCard,
      isQuickExpends,
      isModifyBill,
      dataFromOrder,
      canSelectFeeType = true,
      autoExpenseWithBillStriction,
      hideFields,
      updateCurrentFeeType,
      formData,
      submitterId
    } = this.props
    const { detailsShowFields } = dataFromOrder || {}
    let { feetype, value } = this.state
    const formValue = await bus.getValue()
    if (
      billType === 'reconciliation' ||
      billType === 'settlement' ||
      !canSelectFeeType ||
      (autoExpenseWithBillStriction &&
        billType === 'expense' &&
        sourcePage !== 'recordExpends' &&
        sourcePage !== 'eCardExpense')
    ) {
      return
    }
    const invoices = getInvoiceIdsByFeeTypeForm(formValue)
    const userId = api.getState('@common.me_info.staff.id')
    const staffId = ds?.ownerId?.id || userId
    if (isQuickExpends) {
      let { items = [] } = await Fetch.GET(`/api/v1/form/feeTypes/quickExpenseList/$${staffId}`)
      recordFeeTypes = items
    }
    api
      .open('@feetype:SelectFeeTypeModal', {
        feetype: recordFeeTypes,
        currentFeeType: feetype,
        invoices,
        needRecommend: !!billSpecification,
        specification: billSpecification,
        ds,
        isQuickExpends,
        updateCurrentFeeType, //易商卡页面方法
        submitterId
      })
      .then(feetype => {
        try {
          bus.emit('feetype:edit:loading:change', true)
          api.invokeService('@feetype:get:feetype', feetype.id).then(async feetype => {
            if (!feetype.expenseSpecification || !feetype.requisitionSpecification) {
              const id = billType === 'expense' ? feetype.expenseSpecificationId : feetype.requisitionSpecificationId
              const response = await getFeeTypeTemplateById(id)
              if (billType === 'expense') {
                feetype.expenseSpecification = response?.value
              } else {
                feetype.requisitionSpecification = response?.value
              }
            }
            let specification = getV(feetype, specificationKeyMap[billType], {})
            // 收款金额处理
            addReceivingAmount(formData, specification)
            if (
              get(payeeComponentData, 'visible') &&
              !specification.components.find(oo => oo.field === 'feeDetailPayeeId')
            ) {
              specification.components.push(payeeComponentData.payeeComponent)
            }
            if (sourcePage === 'recordExpends' && payeeInfoComponent) {
              if (!specification.components.find(oo => oo.field === 'feeDetailPayeeId')) {
                specification.components.push(payeeInfoComponent)
              }
            }
            let template = filterFeeTypeTemplate({
              specification,
              globalFields,
              isPermitForm,
              detailsShowFields,
              hideFields
            })
            let onlyDs = cloneDeep(ds)
            if (!Array.isArray(onlyDs)) {
              onlyDs = [onlyDs]
            }
            const target = onlyDs.find(detail => detail.feeTypeId && detail.feeTypeId.id === feetype.id) || {}
            const oldSpecification = getV(target, 'specificationId', {})
            const { feeTypeForm } = target
            const { configs = [], type } = billSpecification
            const isLockFeeTypeVersion = getV(
              configs.find(config => config.ability === type),
              'lockFeeTypeVersion',
              false
            )
            const isUseOldVersion =
              feeTypeForm &&
              isLockFeeTypeVersion &&
              oldSpecification.id !== specification.id &&
              !isEqual(oldSpecification, specification)
            if (isUseOldVersion) {
              template = filterFeeTypeTemplate({
                specification: oldSpecification,
                globalFields,
                isPermitForm,
                detailsShowFields,
                hideFields
              })
              specification = oldSpecification
            }
            template = this.addRelatedComponet(template)
            await this.getHiddenFields(specification)
            const oldtemplate = this.state.template
            bus.getValue().then(async data => {
              data = { ...value, ...data }
              let newValue = formatNewTemplateValue(data, template)
              newValue = formatFeetypeValuesForInvoice(newValue, template)
              const ordersData = get(this.state, 'value.ordersData', [])
              const orders = get(this.state, 'value.orders', [])
              // 查询单据模板上所有固定值并重新赋值
              template.forEach(tmp => {
                if (constantValue(tmp)) {
                  newValue[tmp.field] = constantValue(tmp)
                }
              })
              if (civilServiceCard && ordersData?.some(el => el.platform === 'transact')) {
                newValue = await this.handleCSCFieldsValueAssignment(
                  { ...data, ordersData, orders },
                  newValue,
                  oldtemplate,
                  template
                )
              }
              if (newValue.amortizes) {
                delete newValue.amortizes
              }
              this.setState({ template: null }, () => {
                newValue = addReceivingAmountValue(formData, newValue)
                this.setState({ feetype, specification, template, value: newValue }, async () => {
                  setTimeout(() => {
                    bus.emit('on:dependence:change', { key: 'flow.FeeType', id: feetype.id, dependenceFeeType: true })
                  }, 1000)
                  if (!isUseOldVersion) {
                    await this.updateAutoCalFields(specification)
                  }
                  //切换费用类型的时候重新计算发票相关字段
                  const { invoiceForm } = this.state.value
                  if (invoiceForm && invoiceForm.invoices && invoiceForm.invoices.length > 0) {
                    await updateInvoiceDeduction([feetype], invoiceForm.invoices, true)
                    const invoiceMappingValue = new InvoiceMappingValue()
                    const feeTypeForm = await invoiceMappingValue.invoice2FeeTypeForm(invoiceForm.invoices, template)
                    bus.setFieldsValue(feeTypeForm)
                  }
                  if (external) {
                    bus.setFieldsExternalsData?.({ ...external })
                    let noticeList = formatRiskNotice({
                      riskData: external,
                      components: template,
                      globalFieldsMap,
                      RiskPromptOptimization,
                      isFeeDetail: true,
                      isForbid: !isModifyBill
                    })
                    let externalData = cloneDeep(external)
                    this.setState({ noticeList, externalData })
                  }
                  this.autoAssignOfOneResultOfDataLink()

                  api?.logger?.info?.('app切换费用类型', {
                    feeTypeName: feetype?.name,
                    feeTypeId: feetype?.id,
                    oldFeeType: oldtemplate,
                    feeType: template,
                    formOld: data,
                    formNew: newValue
                  })
                  bus.emit('feetype:edit:loading:change', false)
                })
              })
            })
          })
        } catch (e) {
          console.log(e)
          bus.emit('feetype:edit:loading:change', false)
        }
      })
  }

  handleCSCFieldsValueAssignment = (value, newInitvalue, oldtemplate, template) => {
    const officialCardMoney = template?.filter(el => el?.defaultValue?.type === 'officialCardMoney')
    const officialCardSettlement = template?.find(el => el?.defaultValue?.type === 'officialCardSettlement')?.field

    if (officialCardMoney?.length) {
      const oldOfficialCardMoney = oldtemplate?.find(el => el?.defaultValue?.type === 'officialCardMoney')?.field
      if (oldOfficialCardMoney && value[oldOfficialCardMoney]) {
        officialCardMoney.forEach(v => {
          newInitvalue[v.field] = value[oldOfficialCardMoney]
        })
        newInitvalue.orders = value.orders
        newInitvalue.ordersData = value.ordersData
      }
    } else {
      delete newInitvalue.orders
      delete newInitvalue.ordersData
      this.setState({
        max: NaN,
        min: NaN
      })
    }
    if (officialCardSettlement) {
      const oldOfficialCardSettlement = oldtemplate?.find(el => el?.defaultValue?.type === 'officialCardSettlement')
        ?.field
      if (oldOfficialCardSettlement && value[oldOfficialCardSettlement]) {
        newInitvalue[officialCardSettlement] = value[oldOfficialCardSettlement]
      }
    }
    return newInitvalue
  }

  handleSelectProperty = field => {
    const { flowId } = this.props
    return this.selectProperty(field, flowId)
  }
  handleSelectCity = dataSource => {
    const { formData, bus } = this.props
    return bus.getValue().then(value => {
      const travelers = value.travelers || []
      let travelerId = undefined
      if (travelers.length === 1) {
        travelerId = travelers[0]
      } else if (travelers.length === 0) {
        travelerId = formData.submitterId.id
      }
      return api.open('@basic:CitySelector', {
        ...dataSource,
        bus,
        travelerId,
        isMultipleTraveler: travelers.length > 1 ? true : false
      })
    })
  }

  fnMapEditable = template => {
    let { mappingRelation } = this.props
    template &&
      template.forEach(v => {
        let mapping = mappingRelation.find(o => o.to === v.field)
        if (mapping) {
          v.isFromThirdParty = true
          // v.editable = mapping.editable
        }
      })
  }
  backHookCallback = async () => {
    const { copyValue } = this.state
    const { bus } = this.props
    if (copyValue) {
      const linkDetailEntities = copyValue.linkDetailEntities
      if (linkDetailEntities) {
        const detailId1 = get(copyValue, 'detailId')
        const detailId2 = this.fnDetailId(copyValue)
        const removeId = detailId2 || detailId1
        removeId && related.removeByConsumeId(removeId)
        let list = []
        linkDetailEntities.forEach(line => {
          const dataList = line.dataList.map(item => {
            const { modifyValue, id, unwrittenOffAmount } = item
            return { consumeAmount: modifyValue || unwrittenOffAmount, relateId: id }
          })
          list = list.concat(dataList)
        })
        related.setRelatedMap({ id: removeId, value: list })
      }
    } else if (bus) {
      try {
        const value = await bus.getValue()
        if (value) {
          const detailId = this.fnDetailId(value)
          detailId && related.removeByConsumeId(detailId)
        }
      } catch (e) { }
    }
    api.backControl.remove(this._$UUID)
    api.backControl.invoke()
  }

  updateTemplateAndRelatedMap = () => {
    const { template } = this.state
    template.map(line => {
      if (line.name === 'linkDetailEntities') {
        line._tempConsumId = uuid()
      }
      return line
    })
  }

  fnNoVerifyTemplate = (template, noNeedVerify, isNeedFilterTemplate) => {
    const { billType, isQuickExpends, sourcePage } = this.props
    return template.map(item => {
      const { type, field } = item
      // if (noNeedVerify && !isQuickExpends) {
      if (noNeedVerify) {
        if (type === 'money' && field === 'amount') {
          item.optional = false
        } else if (type === 'invoice') {
          item.optional = true
          item.invoiceType.isRequired = false
        } else {
          if (!isQuickExpends && sourcePage !== 'eCardExpense') {
            item.optional = true
          }
        }
      }

      if (isNeedFilterTemplate) {
        //对账单时费用模版的结算单数据互联字段需要隐藏
        if (billType === 'reconciliation' && item.field === 'supplierSettlement') {
          item.hide = true
        }
        //结算单时费用模版的对账单数据互联字段需要隐藏
        if (billType === 'settlement' && item.field === 'supplierReconciliation') {
          item.hide = true
        }
      }
    })
  }

  fnFilterApportionsAndDataLinkEdits = template => {
    return template.filter(item => {
      const { type = '' } = item
      return type !== 'apportions' && type !== 'dataLinkEdits'
    })
  }

  // 修改币种类型时，设置所有币种
  _handlerCurrencyMoneySelectChange = parms => {
    const { canUseDefaultCurrency, isNewCreate, flowId, data } = this.props
    canUseDefaultCurrency && isNewCreate && !flowId && handlerCurrencyMoneySelectChange.call(this, parms, 'feeDetail')

    // if (this.allowSelectionReceivingCurrency) {
    //   const { name } = parms?.field || {}
    //   if (name === 'amount') {
    //     this.handleAmountChangeEvent({ amount: parms?.data })
    //   } else if (name === 'receivingAmount') {
    //     this.handleAmountChangeEvent({ receivingAmount: parms?.data })
    //   }
    // }
  }

  handleOldDiDiCardClick = thirdPartyOrder => {
    if (thirdPartyOrder.platform === 'DIDI') {
      api
        .invokeService('@bill:get:datalink:template:byId', { entityId: thirdPartyOrder?.id, type: 'CARD' })
        .then(resp => {
          api.open('@bill:DataLinkEntityTripOrderDetailModal', {
            field: get(resp, 'value.data.dataLink.entity.fields', []),
            value: resp?.value,
            title: i18n.get('订单详情')
          })
        })
    }
  }

  handleClickSelectCivilServiceCard = async () => {
    const { value, template, settlementId } = this.state
    const { bus, submitter, valuation, billBus, ds } = this.props
    let principalId = get(submitter, 'id')
    const selectedOrders = get(value, 'orders')
    const { ids, selectList, merge } = await api.open('@officialCard:ImportOfficialCard', {
      bus,
      principalId,
      valuation,
      inDetail: true,
      selectedOrders,
      detailsValue: value,
      billBus,
      ds
    })
    const callBack = (state, action) => {
      let { items } = action.payload
      let sumMoney = 0
      // 修正已使用金额
      items = items.map(item => {
        const listItem = selectList.find(l => l.id === item.transactRecordId)
        if (listItem) {
          item.data.useBalance = listItem.useBalance
          const useBalance =
            typeof listItem.useBalance === 'object' ? listItem.useBalance.standard : listItem.useBalance
          sumMoney = new Big(sumMoney).plus(useBalance).valueOf()
        }
        return item
      })
      if (merge) {
        items.forEach(item => {
          item.merge = merge
        })
      }
      const orders = items.map(el => el.id)
      bus.getValue().then(res => {
        let max, min
        if (Big(sumMoney).gte(0)) {
          max = sumMoney
          min = '0.01'
        } else {
          max = '-0.01'
          min = sumMoney
        }
        // 重置金额字段的最大值和最小值范围
        const setDetailValue = () => {
          let value = { ...res, ordersData: items, orders }
          const officialCardMoney = standardValueMoney(sumMoney)
          const invoices = get(value, 'invoiceForm.invoices')
          template.forEach(el => {
            if (el?.defaultValue?.type === 'officialCardMoney' || (el.field === 'amount' && !invoices?.length)) {
              value[el.field] = cloneDeep(officialCardMoney)
            }
            if (el?.defaultValue?.type === 'officialCardSettlement' && settlementId) {
              value[el.field] = settlementId
            }
          })
          this.setState({ value })
        }
        const moneyField = template.find(t => t.field === 'amount')
        if (max && moneyField) moneyField.max = max
        if (min && moneyField) moneyField.min = min
        this.setState({ max, min, template: template }, setDetailValue)
      })
    }
    api.dispatch(getCardToOrder(ids, principalId, callBack))
  }

  renderCivilServiceCardSelect = () => {
    const { supportCivilServiceCardFeeTypeIds, isModifyBill, billSpecification } = this.props
    if (!supportCivilServiceCardFeeTypeIds || isModifyBill || billSpecification?.type !== 'expense') return null
    const { feetype } = this.state
    const supportAllFeeTypeIds =
      typeof supportCivilServiceCardFeeTypeIds === 'string' &&
      supportCivilServiceCardFeeTypeIds === 'supportAllFeeTypeIds'
    if (!supportAllFeeTypeIds && !supportCivilServiceCardFeeTypeIds.includes(feetype.id)) return null
    return (
      <Button className="civilServiceCardSelect" onClick={this.handleClickSelectCivilServiceCard}>
        {'选择公务卡'}
      </Button>
    )
  }

  fnCheckCSCFields = async values => {
    const { template, settlementId, value } = this.state
    const ordersData = get(value, 'ordersData', [])
    const CSCorders = ordersData?.filter(o => o && o.platform === 'transact')
    const transactRecordIds = ordersData.map(v => v.transactRecordId)
    if (!transactRecordIds?.length) return 'continue'
    const checkFields = filterCSCFields(template)
    const res = await Fetch.GET(`/api/mall/v1/officialCard/[${transactRecordIds.join(',')}]`)
    const CSCData = res?.items || []
    let sumUseBalance = 0
    CSCData.forEach(v => {
      sumUseBalance = new Big(Number(sumUseBalance)).plus(new Big(Number(v.useBalance)))
    })
    const errorMsg = []
    if (checkFields?.length && CSCorders?.length) {
      const officialCardSettlementField = checkFields?.find(
        el => get(el, 'defaultValue.type') === 'officialCardSettlement'
      )
      if (officialCardSettlementField && !values[officialCardSettlementField.field]) {
        if (settlementId) {
          values[officialCardSettlementField.field] = settlementId
        } else {
          toast.info(i18n.get('未匹配到结算方式，请联系管理员'))
          return 'break'
        }
      }
      checkFields.forEach(el => {
        const type = get(el, 'defaultValue.type')
        if (type === 'officialCardMoney' || el.field === 'amount') {
          const result = checkCSCMoney(values, el, sumUseBalance)
          if (result) {
            errorMsg.push(`${el.cnLabel}字段金额缺失或错误`)
          }
        }
      })
    }
    if (errorMsg?.length) {
      errorMsg.forEach(v => {
        toast.warning(v)
      })
      return 'break'
    }
    return 'continue'
  }

  render() {
    let {
      bus,
      submitterId,
      lastChoice,
      flowAllowModifyFields,
      flowId,
      isFrom,
      notices,
      billSpecification,
      shouldUpdate,
      billTemplate,
      validateError = {},
      showAllFeeType,
      isModifyBill,
      cannotEditAmountField,
      apportionVisibleList,
      noNeedVerify = false,
      sourcePage = '',
      isTicketReview,
      components,
      globalFields,
      originalValue,
      billType,
      invoiceRiskData,
      civilServiceCard,
      details,
      currentFlowNode,
      plan,
      notShowModalIfAllInvoiceSuccess = false,
      renderFeetypeItem,
      timeField,
      ...others
    } = this.props
    let {
      hiddenFields,
      feetype,
      value,
      specification,
      template,
      max,
      min,
      calFields,
      customizeQueryRule,
      noticeList,
      selectCurrencyDisable,
      foreignNumCode,
      settlementId,
      markInfo,
      disableInfo
    } = this.state
    if (!specification || !template) return null
    let orderData = []

    if (value && value.ordersData && value.ordersData.length > 0) {
      orderData = orderData.concat(value.ordersData)
      if (orderData.find(v => v.platform === 'DIDI')) {
        //只有滴滴需要mapping控制编辑
        this.fnMapEditable(template)
      }
    }

    bus.$extraParseDetail = feeTypeForm => {
      if (orderData.length > 0 && feeTypeForm) {
        let { orders, ordersData } = value
        feeTypeForm.orders = orders
        feeTypeForm.ordersData = cloneDeep(ordersData)
      }

      return {
        specificationId: { ...specification, components: template },
        feeTypeId: feetype,
        feeTypeForm: { ...feeTypeForm, detailId: feeTypeForm?.detailId }
      }
    }

    if ((max || min) && template && template.length) {
      let moneyTemplate = template.find(t => t.field === 'amount')
      if (moneyTemplate) {
        moneyTemplate.max = max && Big(max).lt(moneyTemplate.max) ? max : moneyTemplate.max
        moneyTemplate.min = min && Big(min).gt(moneyTemplate.min) ? min : moneyTemplate.min
      }
    }
    if (noticeList.length) {
      notices = notices.concat(noticeList)
    }
    const isNeedFilterTemplate = ['reconciliation', 'settlement'].includes(billType)
    if (noNeedVerify || isNeedFilterTemplate) {
      this.fnNoVerifyTemplate(template, noNeedVerify, isNeedFilterTemplate)
    }

    if (sourcePage === 'recordExpends') {
      template = this.fnFilterApportionsAndDataLinkEdits(template)
    }

    if (template && template.length) {
      const pay = billSpecification.configs.find(v => v.ability === 'pay')
      template.forEach(item => {
        if (item.name === 'feeDetailPayeeId' && pay && pay?.optionalPayeeByZero) {
          item.optionalPayeeByZero = true
          item.optional = true
          item.allowClear = true
        }
      })
    }
    const { ds = {}, index, external } = this.props
    const shouldSaveFeetype = ds[index]?.shouldSaveFeetype

    const { taskId, nodes = [] } = plan || {}
    const currentNode = nodes.find(node => node.id === taskId)
    const riskType = !isModifyBill && fnIsRiskError(external) ? 'error' : 'warning'
    const feetypeSpecificationId = feetype[specificationKeyMap[billType]]?.id
    return (
      <div
        id="feetype-info-editable"
        style={sourcePage === 'eCardExpense' ? { marginBottom: 0, backgroundColor: 'white' } : {}}
        className={classNames(styles['feetype-info-editable'], 'inertial-rolling', {
          'highlight-modifiable-field': currentNode?.config?.highlightModifiableField || false
        })}
      >
        <RiskNotice notices={notices} type={riskType} isFeeDetail={true} />
        {sourcePage !== 'eCardExpense' ? (
          <SelectSpecificationForFeeType
            flowAllowModifyFields={flowAllowModifyFields}
            dataSource={feetype}
            onClick={this.handleChangeFeeTypeClick}
            disabled={value?.systemGenerationDetail}
          />
        ) : (
          <>
            <FormItem
              name={'feeValue'}
              label={i18n.get('选择费用类型')}
              rules={[{ required: true, message: '请选择费用类型' }]}
            >
              <CustomWrapperWidget
                isFeetype={true}
                onClick={this.handleChangeFeeTypeClick}
                placeholder="选择费用类型"
              />
            </FormItem>
            {renderFeetypeItem?.('eCardExpense-feetyp-select-form-item-wrapper')}
          </>
        )}

        {this.renderCivilServiceCardSelect()}
        <Dynamic
          {...others}
          className="feetype-info-editable-form"
          bus={bus}
          hiddenFields={hiddenFields}
          billTemplate={billTemplate}
          billSpecification={billSpecification}
          flowId={flowId}
          create={create}
          globalFields={globalFields}
          shouldUpdate={shouldUpdate}
          noNeedVerify={noNeedVerify}
          template={template}
          elements={editable}
          feetypeId={feetype.id}
          value={value}
          validateError={validateError.detail}
          submitter={submitterId}
          calFields={calFields}
          customizeQueryRule={customizeQueryRule}
          hasOrders={orderData.length > 0}
          lastChoice={lastChoice}
          isDetail={true}
          flowAllowModifyFields={flowAllowModifyFields}
          isFrom={isFrom}
          detailId={value && value.detailId}
          components={components}
          selectCurrencyDisable={selectCurrencyDisable}
          foreignNumCode={foreignNumCode}
          showAllFeeType={showAllFeeType}
          isModifyBill={isModifyBill}
          cannotEditAmountField={cannotEditAmountField}
          apportionVisibleList={apportionVisibleList}
          isTicketReview={isTicketReview}
          originalValue={originalValue}
          invoiceRiskData={invoiceRiskData}
          disableInfo={disableInfo}
          markInfo={markInfo}
          sourcePage={sourcePage}
          details={details}
          currentFlowNode={currentFlowNode}
          plan={plan}
          shouldSaveFeetype={shouldSaveFeetype}
          notShowModalIfAllInvoiceSuccess={notShowModalIfAllInvoiceSuccess}
          receivingCurrencyNum={getBillReceivingCurrency(others.formData)}
          feetypeTemplate={template}
          feetypeSpecificationId={feetypeSpecificationId}
          businessType={"DETAILS"}
          timeField={timeField}
          onComponentLoadFinished={field => fnHandleComponentLoadFinish({ field, bus, feetype })}
        />
        <ImportCards
          orderData={orderData}
          submitter={submitterId}
          isEdit={true}
          handleOldDiDiCardClick={this.handleOldDiDiCardClick}
        />
      </div>
    )
  }
}
