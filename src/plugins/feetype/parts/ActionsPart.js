import { app, app as api } from '@ekuaibao/whispered'
import styles from './ActionsPart.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { Button, Dialog } from '@hose/eui-mobile'
import { OutlinedEditDeleteTrash } from '@hose/eui-icons'
import { toast } from '../../../lib/util'
import { getCurrencyBaseAttribute } from '../../../lib/money-conversion'
const { standardValueMoney } = app.require('@components/utils/fnInitalValue')
const { getValidateErrorByShow, fnCheckApportionOtherConfig } = app.require('@bill/utils/billUtils')
const { related } = app.require('@components/utils/Related')
import { get, cloneDeep, isArray, groupBy } from 'lodash'
import { isFunction } from '@ekuaibao/helpers'
import { MoneyMath } from '@ekuaibao/money-math'
import { checkChangedFields } from '../../bill/utils/customizeCalculate'
import { saveFeeDetailLog } from '../../../lib/dataflux/billLogEvent'
import { batchAddTag } from '../feetype.action'
import { isElectronicAirAndTrain } from '../../importOCR/utils'
import { validateFeeTypeOnlyCurrency, validateInvoicesMoneyLessThanAmount } from '../../bill/utils/billUtils'
import { getBoolVariation } from '../../../lib/featbit'

@EnhanceConnect(state => ({
  standardCurrency: state['@common'].standardCurrency,
  didiAuth: state['@didierp'].didiAuth,
  customizeQueryPower: state['@common'].powers.customizeQuery,
  civilServiceCard: state['@common'].powers.CivilServiceCard,
  travelBackInfoV3: state['@bill'].travelBackInfoV3,
  dimensionCurrency: state['@bill'].dimensionCurrencyInfo
}))
export default class ActionsPart extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    const { isModifyBill, showAllFeeType, cannotEditAmountField, setFormBus, bus } = props
    // 审批中修改时，如果单据的明细权限为不全可见，且当前费用明细中含有不可见的分摊时，不允许修改费用明细中的金额字段和分摊字段
    this.apportionDisabled = isModifyBill && !showAllFeeType && cannotEditAmountField
    this.dataValueCache = []
    if (setFormBus) {
      setFormBus(bus)
    }
  }

  resetAmount = data => {
    if (data == undefined) {
      return data
    }
    if (data.amount && data.amount.standard && !data.amount.standardStrCode) {
      const obj = getCurrencyBaseAttribute(this.props.standardCurrency, 'standard')
      return Object.assign(data.amount, obj)
    }
    return data
  }

  handleSaveWithNew = reAddOne => {
    let { bus, closeAndReturnValue, setValidateError, isTicketReview, civilServiceCard } = this.props
    // 0轻松模式，1严格模式
    const level = reAddOne === 0 ? 0 : 1

    bus &&
      isFunction(bus.getValueWithValidate) &&
      bus
        .getValueWithValidate(level)
        .catch(e => {
          return bus.invoke('get:feetype:components').then(components => {
            let arr = getValidateErrorByShow(components, Object.keys(e))
            if (!arr.length) {
              setValidateError({ detail: Object.keys(e) })
            }
            throw e
          })
        })
        .then(async data => {
          saveFeeDetailLog()
          if (level === 0) {
            data.detailStatus = 'draft'
          }
          if (!validateFeeTypeOnlyCurrency(data)) {
            return toast.error(i18n.get('一个费用明细中只能存在一种消费币种（原币或本位币），请检查发票信息'))
          }
          if (level !== 0 && !validateInvoicesMoneyLessThanAmount(data)) {
            return toast.error(i18n.get('费用金额大于发票总金额'))
          }
          if (civilServiceCard) {
            const status = await bus.invoke('check:cscfields:complete', data)
            if (status === 'break') return
          }
          const { flag, notWrittenOffAmount } = await this.isShowRelatedTips(data)
          if (flag) {
            this.handleRelatedTips(notWrittenOffAmount)
            return
          }
          const approveAmountErr = isTicketReview && this.ticketReviewCheckApproveAmount(isTicketReview, data)
          if (approveAmountErr) {
            toast.error(approveAmountErr)
            return
          }
          if (data?.invoiceForm?.invoices?.length > 0) {
            const hasExceedComErr = this.fnTextExceed(bus, data)
            if (hasExceedComErr.length > 0) {
              hasExceedComErr.forEach(tip => {
                Dialog.alert({ content: i18n.get(tip), confirmText: i18n.get('确定') })
              })
              return
            }
          }
          if (this.checkPayInfo(data)) {
            Dialog.alert({ content: i18n.get('费用明细金额不为0时收款信息必填'), confirmText: i18n.get('确定') })
            return
          }
          this.updateAmount(data)
          data = this.resetAmount(data)
          setValidateError({ detail: [] })
          const { apportions, amortizes } = data
          if (apportions && !this.apportionDisabled) {
            const { isPresetApportion, notEditablePresetApportion } = this.getPresetApportion(apportions)
            if (isPresetApportion) {
              const existEmptyApportionItem = this.checkExistEmptyApportion(apportions)
              if (existEmptyApportionItem) {
                if (notEditablePresetApportion) {
                  toast.fail(i18n.get('分摊预置信息存在数据缺失，请联系管理员'))
                } else {
                  toast.fail(i18n.get('分摊明细存在空值，已标红处理，请修改'))
                  bus.emit('exist:apportion:empty:item', true)
                }
                return
              }
            }
            // 检查分摊模板其它配置
            const hasOtherConfigErr = fnCheckApportionOtherConfig(apportions)
            if (hasOtherConfigErr) {
              toast.fail(hasOtherConfigErr)
              return
            }
            bus.invoke('feeType:update:amount:save:apportions', data).then(res => {
              let value = bus.$extraParseDetail(res)
              this.updateSwitcher(value)
              const didiCheck = this.checkDidi(data)
              if (!didiCheck) return
              if (reAddOne) {
                value.reAddOne = true
              }
              this.dataValueCache.push(value)
              this.saveFeeTypeLog(value, level === 0 ? '费用草稿' : '费用保存', false)
              closeAndReturnValue(this.dataValueCache)
            })
          } else if (amortizes?.length && !this.apportionDisabled) {
            const existEmptyItem = this.checkExistEmptyAmortizeId(amortizes)
            if (existEmptyItem) {
              toast.fail(i18n.get('摊销明细存在空值，已标红处理，请修改'))
              bus.emit('exist:amortization:empty:item')
              return
            }
            bus.invoke('feeType:update:amount:save:amortization', data).then(res => {
              let value = bus.$extraParseDetail(res)
              this.updateSwitcher(value)
              const didiCheck = this.checkDidi(data)
              if (!didiCheck) return
              if (reAddOne) {
                value.reAddOne = true
              }
              this.dataValueCache.push(value)
              this.saveFeeTypeLog(value, level === 0 ? '费用草稿' : '费用保存', false)
              closeAndReturnValue(this.dataValueCache)
            })
          } else {
            let value = bus.$extraParseDetail(data)
            this.updateSwitcher(value)
            const didiCheck = this.checkDidi(data)
            if (!didiCheck) return
            if (reAddOne) {
              value.reAddOne = true
            }
            this.dataValueCache.push(value)
            this.saveFeeTypeLog(value, level === 0 ? '费用草稿' : '费用保存', false)
            closeAndReturnValue(this.dataValueCache)
          }
        })
        .catch(e => console.log(e))
  }
  ticketReviewCheckApproveAmount = (isTicketReview, data) => {
    if (data.invoiceForm?.invoices?.length > 0) {
      const {
        amount,
        invoiceForm: { invoices }
      } = data
      const hasApproveAmountErr = invoices.filter(item => {
        const { approveAmount } = item
        return approveAmount && Number(approveAmount.standard) > Number(this.getTicketAmount4approve(item))
      })
      if (hasApproveAmountErr.length > 0) {
        return i18n.get('发票的核发金额大于发票金额')
      }
      if (isTicketReview.isApproveEqualExpense || isTicketReview.isApproveGreatExpense) {
        //后台这两个变量 isApproveEqualExpense 核发必须小，isApproveGreatExpense 核发必须大
        const invoiceTotal = invoices.reduce((cur, dataSource) => {
          const { approveAmount } = dataSource
          if (approveAmount) {
            return new Big(Number(cur))
              .plus(new Big(Number(approveAmount.standard)))
              .toFixed(Number(amount.standardScale))
          } else {
            const ticketAmount = this.getTicketAmount4approve(dataSource)
            return new Big(Number(cur)).plus(new Big(Number(ticketAmount))).toFixed(Number(amount.standardScale))
          }
        }, 0)
        if (isTicketReview.isApproveEqualExpense && isTicketReview.isApproveGreatExpense) {
          if (Number(invoiceTotal) !== Number(amount.standard)) {
            return i18n.get('费用金额需等于核发金额合计')
          }
        } else if (isTicketReview.isApproveGreatExpense) {
          if (Number(invoiceTotal) < Number(amount.standard)) {
            return i18n.get('费用金额大于核发金额合计')
          }
        } else if (isTicketReview.isApproveEqualExpense) {
          if (Number(invoiceTotal) > Number(amount.standard)) {
            return i18n.get('费用金额小于核发金额合计')
          }
        }
      }
    }
  }
  getTicketAmount4approve = item => {
    const {
      invoiceId: { form, entityId }
    } = item
    const invoiceMoney =
      entityId === 'system_发票主体'
        ? form[i18n.get(`E_{__k0}_价税合计`, { __k0: entityId })]
        : form[i18n.get(`E_{__k0}_金额`, { __k0: entityId })] || form[i18n.get(`E_{__k0}_金额合计`, { __k0: entityId })]
    const isCheckerInvoice = get(item, 'invoiceId.form.E_system_发票主体_验真', false)
    let ticketAmount = isCheckerInvoice ? this.getAllMoney(item.itemIds) : invoiceMoney?.standard
    if (entityId === 'system_非税收入类票据') {
      ticketAmount = invoiceMoney?.standard
    }
    if (isElectronicAirAndTrain(form)) {
      ticketAmount = invoiceMoney?.standard
    }
    return ticketAmount
  }
  getAllMoney = details => {
    return details.reduce((cur, next) => {
      const { form } = next
      const money =
        typeof form['E_system_发票明细_金额'] === 'object' && form['E_system_发票明细_金额'] !== null
          ? form['E_system_发票明细_金额'].standard
          : form['E_system_发票明细_金额'] || 0 // @i18n-ignore
      const tax =
        typeof form['E_system_发票明细_税额'] === 'object' && form['E_system_发票明细_税额'] !== null
          ? form['E_system_发票明细_税额'].standard
          : form['E_system_发票明细_税额'] || 0 // @i18n-ignore
      const standardScale =
        typeof form['E_system_发票明细_金额'] === 'object' ? form['E_system_发票明细_金额'].standardScale : 2 // @i18n-ignore
      const num = new Big(Number(money)).plus(new Big(Number(tax)))
      return new Big(Number(cur)).plus(new Big(Number(num))).toFixed(Number(standardScale))
    }, 0)
  }
  updateSwitcher = detail => {
    const { feeTypeForm, specificationId } = detail
    specificationId.components.forEach(cmp => {
      const { type, field } = cmp
      if (type === 'switcher' && feeTypeForm[field] === undefined) {
        feeTypeForm[field] = false
      }
    })
  }
  updateAmount = res => {
    //金额字段选填的时候如果没有填值就设置为0，否则后台会报错
    const { dimensionCurrency } = this.props
    if (!res.amount || !res.amount.standard) {
      res.amount = standardValueMoney('0.00', dimensionCurrency?.currency)
    }
  }
  checkPayInfo = res => {
    let { billSpecification } = this.props
    if (billSpecification && res) {
      const pay = billSpecification.configs.find(v => v.ability === 'pay')
      const payAmount = get(res, 'amount.standard', 0)
      const hasFeeDetailPayeeId = Object.keys(res)
      const feeDetailPayeeId = get(res, 'feeDetailPayeeId.id')
      if (
        Number(payAmount) &&
        hasFeeDetailPayeeId.includes('feeDetailPayeeId') &&
        !feeDetailPayeeId &&
        pay &&
        pay?.optionalPayeeByZero
      ) {
        return true
      }
    }
    return false
  }

  getPresetApportion = apportions => {
    const configs = get(apportions[0], 'specificationId.configs', [])
    const apportionConfig = (configs?.length > 0 && !configs[0].isApi && configs[1]?.apportionConfig) || {}
    return {
      isPresetApportion: apportionConfig.type === 'preinstall',
      notEditablePresetApportion: apportionConfig.editable === false
    }
  }

  checkExistEmptyApportion = apportions => {
    return apportions.find(line => {
      const { apportionId, ...others } = line?.apportionForm || {}
      return !!Object.values(others || {}).filter(v => !v).length
    })
  }

  checkExistEmptyAmortizeId = amortizes => {
    let components = amortizes?.[0]?.specificationId?.components || []
    const isDay = !!amortizes?.[0]?.amortizeForm?.amortizeDateRange
    if (isDay) {
      components = components.filter(item => item.field !== 'amortizeMonth')
    } else {
      components = components.filter(item => item.field !== 'amortizeDateRange' && item.field !== 'amortizeDays')
    }
    let emptyFlag = false
    for (let i = 0; i < amortizes.length; i++) {
      const form = amortizes[i]?.amortizeForm || {}
      const empty = components.find(v => v.optional === false && !form[v.field])
      if (!!empty) {
        emptyFlag = true
        break
      }
    }
    return emptyFlag
  }
  handleSaveWithEdit = level => {
    this.getValueWithValidateSaveWithEdit(level)
  }
  // 保存费类的一些后续操作
  saveFeeTypeDataOperate = (formValue, level, done) => {
    let { bus, external, closeAndReturnValue, originValue } = this.props
    let { detailId } = originValue || {}

    let value = bus.$extraParseDetail(formValue)
    const didiCheck = this.checkDidi(formValue)
    if (!didiCheck) return
    let { feeTypeForm, ...others } = value
    feeTypeForm = { ...feeTypeForm, detailId }
    value = { feeTypeForm, ...others }
    this.updateSwitcher(value)
    closeAndReturnValue(value)
    this.saveFeeTypeLog(value, level === 0 ? '费用草稿' : '费用保存', true)
    external && done?.(value)
  }
  getValueWithValidateSaveWithEdit = async level => {
    let {
      bus,
      civilServiceCard,
      closeAndReturnValue,
      originValue,
      external,
      setValidateError,
      isTicketReview,
      validateLevel,
      travelBackInfoV3
    } = this.props
    let { detailId, systemGenerationDetail } = originValue || {}
    // 0轻松模式，1严格模式
    const currentLevel = validateLevel !== undefined ? validateLevel : level === 0 ? 0 : 1

    bus &&
      isFunction(bus.getValueWithValidate) &&
      bus
        .getValueWithValidate(currentLevel)
        .then(async data => {
          if (bus.has('@bill:checkDateValiable')) {
            const error = await bus.invoke('@bill:checkDateValiable')
            if (error) {
              return
            }
          }
          if (!validateFeeTypeOnlyCurrency(data)) {
            return toast.error(i18n.get('一个费用明细中只能存在一种消费币种（原币或本位币），请检查发票信息'))
          }
          if (currentLevel !== 0 && !validateInvoicesMoneyLessThanAmount(data)) {
            return toast.error(i18n.get('费用金额大于发票总金额'))
          }
          const prevTravelPlanning = originValue?.travelPlanning?.[0] || {}
          const currentTravelPlanning = data?.travelPlanning?.[0] || {}
          if (
            prevTravelPlanning.travelId &&
            travelBackInfoV3?.includes(prevTravelPlanning.travelId) &&
            (prevTravelPlanning.startDate != currentTravelPlanning.startDate ||
              prevTravelPlanning.endDate != currentTravelPlanning.endDate)
          ) {
            const params = {
              startTime: currentTravelPlanning.startDate,
              endTime: currentTravelPlanning.endDate,
              travelId: prevTravelPlanning.travelId
            }
            try {
              await api.invokeService('@bill:check:purchase:travel', params)
            } catch (e) {
              Dialog.alert({
                title: e?.errorMessage
              })
              return
            }
          }
          if (systemGenerationDetail) {
            data.systemGenerationDetail = systemGenerationDetail
          }
          saveFeeDetailLog()
          if (level === 0) {
            data.detailStatus = 'draft'
          }
          if (originValue?.detailNo) {
            data.detailNo = originValue.detailNo
          }
          if (civilServiceCard) {
            const status = await bus.invoke('check:cscfields:complete', data)
            if (status === 'break') return
          }
          const { flag, notWrittenOffAmount } = await this.isShowRelatedTips(data)
          if (flag) {
            this.handleRelatedTips(notWrittenOffAmount)
            return
          }
          const approveAmountErr =
            !getBoolVariation('close-approveAmount-validate') &&
            isTicketReview &&
            this.ticketReviewCheckApproveAmount(isTicketReview, data)
          if (approveAmountErr) {
            toast.error(approveAmountErr)
            return
          }
          if (data?.invoiceForm?.invoices?.length > 0) {
            const hasExceedComErr = this.fnTextExceed(bus, data)
            if (hasExceedComErr.length > 0) {
              hasExceedComErr.forEach(tip => {
                Dialog.alert({ content: i18n.get(tip), confirmText: i18n.get('确定') })
              })
              return
            }
          }
          if (this.checkPayInfo(data)) {
            Dialog.alert({ content: i18n.get('费用明细金额不为0时收款信息必填'), confirmText: i18n.get('确定') })
            return
          }
          this.updateAmount(data)
          data = this.resetAmount(data)
          const { apportions, amortizes } = data
          if (apportions && !this.apportionDisabled) {
            const { isPresetApportion, notEditablePresetApportion } = this.getPresetApportion(apportions)
            if (isPresetApportion) {
              const existEmptyApportionItem = this.checkExistEmptyApportion(apportions)
              if (existEmptyApportionItem) {
                if (notEditablePresetApportion) {
                  toast.fail(i18n.get('分摊预置信息存在数据缺失，请联系管理员'))
                } else {
                  toast.fail(i18n.get('分摊明细存在空值，已标红处理，请修改'))
                  bus.emit('exist:apportion:empty:item', true)
                }
                return
              }
            }
            // 检查分摊模板其它配置
            const hasOtherConfigErr = fnCheckApportionOtherConfig(apportions)
            if (hasOtherConfigErr) {
              toast.fail(hasOtherConfigErr)
              return
            }
            if (bus.has('feeType:update:amount:save:apportions')) {
              bus.invoke('feeType:update:amount:save:apportions', data).then(res => {
                this.saveFeeTypeDataOperate(res, level, this.getApportionDiffAndSetExternal)
              })
            } else {
              this.saveFeeTypeDataOperate(data, level, this.getApportionDiffAndSetExternal)
            }
          } else if (amortizes?.length && !this.apportionDisabled) {
            if (bus.has('feeType:update:amount:save:amortization')) {
              bus.invoke('feeType:update:amount:save:amortization', data).then(res => {
                const temp = amortizes[0].specificationId
                const existEmptyItem = this.checkExistEmptyAmortizeId(amortizes, temp)
                if (existEmptyItem) {
                  toast.fail(i18n.get('摊销明细存在空值，已标红处理，请修改'))
                  bus.emit('exist:amortization:empty:item')
                  return
                }
                this.saveFeeTypeDataOperate(data, level, this.getApportionDiffAndSetExternal)
              })
            } else {
              this.saveFeeTypeDataOperate(data, level, this.getApportionDiffAndSetExternal)
            }
          } else {
            this.saveFeeTypeDataOperate(data, level, this.getDetailDiffAndSetExternal)
          }
          if (detailId) {
            api.emit('update:editedDetailList', detailId)
          }
        })
        .catch(e => {
          return bus.invoke('get:feetype:components').then(components => {
            let arr = getValidateErrorByShow(components, Object.keys(e))
            if (!arr.length) {
              setValidateError?.({ detail: Object.keys(e) })
            }
            throw e
          })
        })
  }
  saveFeeTypeLog = (value, type, isEdit = false) => {
    const { originalValue = {}, billSpecification = {}, formData = {} } = this.props
    if (isArray(value)) {
      value = value?.[0] || {}
    } else {
      value = value || {}
    }
    // 保存 修改 草稿 费用明细
    api?.logger?.info?.('费用明细变更埋点', {
      type: type === '明细详情中删除明细' ? type : `${isEdit ? '修改' : '新建'}-${type}`,
      specificationId: billSpecification?.id,
      specificationName: billSpecification?.name,
      flowId: formData?.flowId,
      code: originalValue?.code,
      feeTypeId: value?.specificationId?.id || value?.specificationId,
      feeTypeName: value?.feeTypeId?.name || value?.feeTypeId,
      form: value?.feeTypeForm
    })
  }

  //发票超额指定值，配置了超额的文本字段就必填
  fnTextExceed = (bus, data) => {
    let value = bus.$extraParseDetail(data)
    const {
      specificationId: { components }
    } = value
    //明细模板配置了超额项的文字字段集合
    let textTemplate = []
    components?.forEach(t => {
      const type = get(t, 'dataType.elemType.type') || get(t, 'dataType.type')
      const amountChecked = get(t, 'exceedConfigs.amountChecked', false)
      const invoiceAmount = get(t, 'exceedConfigs.invoiceAmount', 0)
      if (type === 'text' && amountChecked) {
        let obj = {
          field: t.field,
          label: t.label || t.cnLabel,
          invoiceAmount: invoiceAmount
        }
        textTemplate.push(obj)
      }
    })
    let hasExceedComErr = []
    if (textTemplate.length > 0) {
      const {
        invoiceForm: { invoices }
      } = data
      //明细发票金额面值集合
      let amountArr = []
      invoices.forEach(val => {
        const { originalData } = val
        const {
          master: { form }
        } = originalData
        let money = form?.E_system_发票主体_价税合计?.standard
        amountArr.push(money)
      })
      textTemplate.forEach(item => {
        let invoiceAmount = item.invoiceAmount
        let label = item.label
        let hasExceed = false
        amountArr.forEach(num => {
          if (Number(num) > Number(invoiceAmount)) {
            hasExceed = true
          }
        })
        //某个发票面额值大于相应文本超额值
        if (hasExceed && (data[item.field] === '' || data[item.field] === null || data[item.field] === undefined)) {
          hasExceedComErr.push(`有发票金额大于${invoiceAmount}，${label}不能为空`)
        }
      })
    }
    return hasExceedComErr
  }

  getApportionDiffAndSetExternal = value => {
    let { external, originValue, handleExternalChangeDelete, detailId, apportionIdList } = this.props
    const { apportions } = external
    const externalField = cloneDeep(external)
    delete externalField.apportions
    this.getFieldsValueDiff({ externalField, value, originValue, handleExternalChangeDelete, detailId })

    const newFields = groupBy(value.feeTypeForm.apportions, 'apportionForm.apportionId', [])
    const originFields = groupBy(originValue.apportions, 'apportionForm.apportionId', [])
    apportionIdList.forEach(item => {
      if (apportions[item]) {
        if (newFields[item]) {
          Object.keys(apportions[item]).forEach(key => {
            if (newFields[item][0].apportionForm[key].standard !== originFields[item][0].apportionForm[key].standard) {
              handleExternalChangeDelete(item, detailId)
            }
          })
        } else {
          handleExternalChangeDelete(item, detailId)
        }
      }
    })
  }

  getDetailDiffAndSetExternal = value => {
    let { external, originValue, handleExternalChangeDelete, detailId } = this.props
    delete external.apportions
    if (Object.keys(external).length) {
      this.getFieldsValueDiff({ externalField: external, originValue, value, handleExternalChangeDelete, detailId })
    } else {
      handleExternalChangeDelete(false, detailId)
    }
  }

  getFieldsValueDiff = args => {
    const { externalField, value, originValue, handleExternalChangeDelete, detailId } = args
    Object.keys(externalField).forEach(key => {
      let newValues = value.feeTypeForm[key]
      if (!newValues) {
        //切换模板时
        handleExternalChangeDelete(false, detailId, key)
      } else {
        let oldValues = originValue[key]
        let fields = newValues.standard ? newValues.standard : newValues.name ? newValues.name : newValues
        let originFields = oldValues.standard ? oldValues.standard : oldValues.name ? oldValues.name : oldValues
        if (fields !== originFields) {
          handleExternalChangeDelete(false, detailId, key)
        }
      }
    })
  }

  handleDelete = () => {
    Dialog.confirm({
      title: i18n.get('删除'),
      content: i18n.get('确定删除？'),
      onConfirm: async () => {
        let { closeAndReturnValue, detailId, external, handleExternalChangeDelete, bus, formData } = this.props
        this.saveFeeTypeLog(formData?.details, '明细详情中删除明细', false)
        if (external) {
          handleExternalChangeDelete(false, detailId)
        }
        const currentValue = bus && isFunction(bus.getValue) ? await bus.getValue() : {}
        const detailId1 = get(currentValue, 'detailId')
        const linkDetailEntities = get(currentValue, 'linkDetailEntities', [])
        const dataList = linkDetailEntities.length > 0 ? linkDetailEntities[0].dataList : []
        const detailId2 = dataList && dataList.length > 0 && dataList[0]._tempConsumId
        const removeId = detailId2 || detailId1
        related.removeByConsumeId(removeId)
        closeAndReturnValue(-1)
      }
    })
  }
  checkDidi = data => {
    const { didiAuth } = this.props
    if (!didiAuth) return true
    let { ordersData, invoiceForm } = data
    let didi = ordersData && ordersData.find(item => item.platform === 'DIDI' && item.form.useCarType !== 'TAXI')
    if (didi) {
      if (
        !invoiceForm ||
        invoiceForm.type !== 'unify' ||
        !invoiceForm.invoiceCorporationId ||
        invoiceForm.invoiceCorporationId.channel !== 'DIDI'
      ) {
        toast.error(i18n.get('为确保订单顺利开票，请选择正确的发票形式或开票方'))
        return false
      }
    } else {
      if (
        invoiceForm &&
        invoiceForm.type === 'unify' &&
        invoiceForm.invoiceCorporationId &&
        invoiceForm.invoiceCorporationId.channel === 'DIDI'
      ) {
        toast.error(i18n.get('仅支持滴滴快车、专车、豪华车、代驾订单的统一开票'))
        return false
      }
    }
    return true
  }

  // 判断当前单据是否有默认费用类型
  fnIsFeeTypeEmpty = () => {
    let { billSpecification } = this.props
    if (billSpecification == void 0) {
      return true
    }
    const detail = billSpecification.components.filter(a => {
      return a.type === 'details' || a.type === 'requisitionDetails'
    })
    const isFeeTypeEmpty = !!detail.length && (detail[0].isFeeTypeEmpty ? detail[0].isFeeTypeEmpty : false)
    return !isFeeTypeEmpty
  }

  handleSaveWithReAddOne = () => {
    const { bus, setValidateError } = this.props
    // 这里与money 组件失焦事件冲突，延迟两秒
    setTimeout(() => {
      bus &&
        isFunction(bus.getValueWithValidate) &&
        bus
          .getValueWithValidate(1)
          .catch(e => {
            return bus.invoke('get:feetype:components').then(components => {
              let arr = getValidateErrorByShow(components, Object.keys(e))
              if (!arr.length) {
                setValidateError({ detail: Object.keys(e) })
              }
              throw e
            })
          })
          .then(async data => {
            const { flag, notWrittenOffAmount } = await this.isShowRelatedTips(data)
            if (flag) {
              this.handleRelatedTips(notWrittenOffAmount)
              return
            }
            if (!validateInvoicesMoneyLessThanAmount(data)) {
              return toast.error(i18n.get('费用金额大于发票总金额'))
            }
            if (this.checkPayInfo(data)) {
              Dialog.alert({ content: i18n.get('费用明细金额不为0时收款信息必填'), confirmText: i18n.get('确定') })
              return
            }
            this.updateAmount(data)
            data = this.resetAmount(data)
            setValidateError({ detail: [] })
            const { apportions, amortizes } = data
            if (apportions && !this.apportionDisabled) {
              bus.invoke('feeType:update:amount:save:apportions', data).then(res => {
                this.commonLogic(res, data)
              })
            } else if (amortizes?.length && !this.apportionDisabled) {
              bus.invoke('feeType:update:amount:save:amortization', data).then(res => {
                this.commonLogic(res, data)
              })
            } else {
              this.commonLogic(undefined, data)
            }
          })
          .catch(e => console.log(e))
    }, 200)
  }

  commonLogic = (res, data) => {
    const { bus, formBus, sourcePage = '', civilServiceCard } = this.props
    const value = res ? bus.$extraParseDetail(res) : bus.$extraParseDetail(data)
    const didiCheck = this.checkDidi(data)
    if (!didiCheck) return
    if (civilServiceCard && value?.feeTypeForm?.ordersData?.some(el => el.platform === 'transact')) {
      toast.fail(i18n.get('公务卡数据不支持再记一笔'))
      return
    }
    this.saveFeeTypeLog(value, '再记一笔', false)
    const isFeeTypeEmpty = this.fnIsFeeTypeEmpty()
    if (!isFeeTypeEmpty) {
      bus.emit('element:add:one', value)
      //重新选择消费类型
      this.handleSaveWithNew(true)
    } else {
      if (sourcePage !== 'recordExpends') {
        this.fnReOne(value, formBus, sourcePage)
      }
      bus.resetFields().then(() => {
        bus.emit('element:add:one', [value])
        // 这个是李阳随手记页面传递过来的bus
        formBus.emit('element:add:one', [value])
      })
    }
  }

  fnReOne = (value, formBus, sourcePage) => {
    const { customizeQueryPower } = this.props
    formBus.getFieldsValue().then(values => {
      const lines = values.details || []
      const details = lines.concat(value)
      if (customizeQueryPower) {
        const changeDetails = cloneDeep(details)
        let changedFields = checkChangedFields(value, null)
        changeDetails.currentEditField = [{ type: 'detail_', values: changedFields, operate: 'saveFeeType' }]
        formBus.invoke('detail:value:change', changeDetails)
      } else {
        formBus.invoke('detail:value:change', details)
      }
      formBus.setFieldsValue({ details })
    })
  }

  handlePageChange = type => {
    this.props.onPageChange && this.props.onPageChange(type)
  }

  isShowRelatedTips = async data => {
    const { dataFromOrder } = this.props
    const { showImportInPermit } = dataFromOrder || {}
    const template = await this.props.bus.invoke('get:feetype:components')
    const amountField = template.find(el => el.name === 'amount')
    const defType = get(amountField, 'defaultValue.type')
    const isFormulaAmount = defType === 'formula' || defType === 'costStandard'
    if ((isFormulaAmount && !amountField.editable) || showImportInPermit) {
      return { flag: false }
    }
    const { allowAdd, notOverIsChecked } = related.specificationConfig
    const notWrittenOffAmount = this.saveTipsFlag(data)
    const zero = new MoneyMath(notWrittenOffAmount).equal(0)
    const flag0 = notOverIsChecked ? !zero : Number(notWrittenOffAmount.standard) > 0
    const isAllow = allowAdd && flag0
    return { flag: isAllow, notWrittenOffAmount }
  }

  saveTipsFlag = data => {
    const { amount, linkDetailEntities } = data
    if (!linkDetailEntities || !linkDetailEntities?.length) {
      return false
    }
    const dataList = linkDetailEntities.length > 0 ? linkDetailEntities[0].dataList : []
    const _tempConsumId = dataList && dataList.length > 0 && dataList[0]._tempConsumId
    const totalMoney = related.getUseMoneyByConsumeId(_tempConsumId)
    return new MoneyMath(totalMoney).minus(amount).value
  }

  handleRelatedTips(notWrittenOffAmount) {
    const amount = Number(get(notWrittenOffAmount, 'standard', 0))
    const content =
      amount < 0
        ? i18n.get('「费用金额」不可大于关联明细的「本次报销金额」')
        : i18n.get('「费用金额」不可小于关联明细的「本次报销金额」')
    const title = i18n.get('提示')
    Dialog.alert({ title, content, confirmText: i18n.get('确定') })
  }
  render4New() {
    const { loading, isQuickExpends, isPermitForm, sourcePage, showDraftBtn = true, setFormBus, bus } = this.props
    if (sourcePage === 'eCardExpense') {
      return null
    }

    return (
      <div className={styles.actions_part_wrapper}>
        <div className={styles.actions_part_btnGroup}>
          {!isQuickExpends && !isPermitForm && (
            <Button
              category="secondary"
              size="middle"
              disabled={loading}
              onClick={loading ? () => {} : this.handleSaveWithReAddOne}
            >
              {i18n.get('再记一笔')}
            </Button>
          )}
          {showDraftBtn && (
            <Button
              category="secondary"
              size="middle"
              disabled={loading}
              onClick={loading ? () => {} : () => this.handleSaveWithNew(0)}
            >
              {i18n.get('存为草稿')}
            </Button>
          )}
          <Button
            category="primary"
            size="middle"
            disabled={loading}
            onClick={loading ? () => { } : () => this.handleSaveWithNew()}
            data-testid="actions_part_btnGroup_save"
          >
            {i18n.get('保存')}
          </Button>
        </div>
      </div>
    )
  }

  render4Edit() {
    const {
      loading,
      cannotDelete,
      billState,
      billSpecification,
      sourcePage,
      showDraftBtn = true,
      autoExpenseWithBillStriction
    } = this.props
    const isEBussCard = billSpecification?.configs?.find(item => item.isEBussCard)?.isEBussCard
    const EBussCardCanDelete = (isEBussCard && billState === 'new') || !isEBussCard

    if (sourcePage === 'eCardExpense') {
      return null
    }

    const showDeleteBtn =
      !cannotDelete && EBussCardCanDelete && !(autoExpenseWithBillStriction && billSpecification?.type === 'expense')

    return (
      <div className={styles.actions_part_wrapper}>
        <div className={styles.actions_part_btnGroup}>
          {showDeleteBtn && (
            <Button
              category="secondary"
              theme="danger"
              size="middle"
              disabled={loading}
              onClick={loading ? () => {} : this.handleDelete}
            >
              {i18n.get('删除')}
            </Button>
          )}
          {showDraftBtn && (
            <Button
              category="secondary"
              size="middle"
              disabled={loading}
              onClick={loading ? () => {} : () => this.handleSaveWithEdit(0)}
            >
              {i18n.get('存为草稿')}
            </Button>
          )}
          <Button
            category="primary"
            size="middle"
            disabled={loading}
            onClick={loading ? () => { } : this.handleSaveWithEdit}
            data-testid="actions_part_btnGroup_save"
          >
            {i18n.get('保存')}
          </Button>
        </div>
      </div>
    )
  }

  handleAddTagLog = (result, payload) => {
    api?.logger?.info('待审批明细维度添加标签', { result, payload })
  }

  handleAddTag = () => {
    const { detailFlow, fnRefreshList } = this.props
    api.open('@feetype:AddTagModal', { data: [detailFlow] }).then(res => {
      batchAddTag(res?.newTag)
        .then(res => {
          toast.success(i18n.get('添加成功')) //暂时先不要提示了吧 后端没有错误的情况
          api.emit('update:detail:tags')
          fnRefreshList && fnRefreshList()
          this.handleAddTagLog(res, res?.newTag)
        })
        .catch(err => {
          this.handleAddTagLog(err, res?.newTag)
        })
    })
  }

  render4Read() {
    const { currentIdx, details, canAddTag, showPagination = true } = this.props
    //如果从明细维度进来的话需要展示添加标签的按钮，canAddTag表示可以添加标签
    return (
      <div className={styles.actions_part_wrapper}>
        {canAddTag ? (
          <div className={styles.actions_part_btnGroup}>
            <Button size="middle" block onClick={this.handleAddTag}>
              {i18n.get('添加审批意见')}
            </Button>
          </div>
        ) : showPagination ? (
          <>
            <div
              className={currentIdx > 0 ? styles.prev_action : `${styles.disabled} ${styles.prev_action}`}
              onClick={currentIdx > 0 ? this.handlePageChange.bind(this, 'prev') : undefined}
            >
              <span>&lt;</span>
              <span>{i18n.get('上一页')}</span>
            </div>
            <div className={styles.page_content}>
              {currentIdx + 1}/{details.length}
            </div>
            <div
              className={
                currentIdx < details.length - 1 ? styles.next_action : `${styles.disabled} ${styles.next_action}`
              }
              disabled={currentIdx > details.length - 1}
              onClick={currentIdx < details.length - 1 ? this.handlePageChange.bind(this, 'next') : undefined}
            >
              <span>{i18n.get('下一页')}</span>
              <span>&gt;</span>
            </div>
          </>
        ) : null}
      </div>
    )
  }

  renderInvoiceImport() {
    return (
      <div className={styles.actions_part_wrapper}>
        <div className={styles.actions_part_btnGroup}>
          <Button block size="middle" category="secondary" onClick={this.handleDelete}>
            {i18n.get('取消')}
          </Button>
          <Button block size="middle" onClick={this.handleSaveWithEdit}>
            {i18n.get('生成消费明细')}
          </Button>
        </div>
      </div>
    )
  }

  render() {
    const { type } = this.props

    if (type === 'new') {
      return this.render4New()
    }

    if (type === 'edit') {
      return this.render4Edit()
    }

    if (type === 'read') {
      return this.render4Read()
    }

    if (type === 'invoiceImport' || type === 'dataLinkImport') {
      return this.renderInvoiceImport()
    }
  }
}
