/**************************************************
 * Created by nany<PERSON>ing<PERSON> on 8/23/16 11:55.
 **************************************************/
import React from 'react'

import FeeTypeRecommend from './FeetypeRecommend'

export function RecommendFeeType(props) {
  const { isSearch } = props
  if (isSearch) {
    return null
  }
  const { defaultValue, recommends = [], onClick } = props
  return <FeeTypeRecommend defaultValue={defaultValue} recommends={recommends} onChanged={onClick} />
}

export default RecommendFeeType
