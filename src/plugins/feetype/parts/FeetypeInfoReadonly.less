/*!
 * Copyright 2019 yang<PERSON><PERSON> <yang<PERSON><PERSON>@shimo.im>. All rights reserved.
 * @since 2019-06-10 15:06:46
 */
@import '~@ekuaibao/eui-styles/less/token.less';

.fee-type-info-bill-bar {
  background-color: @color-white-1;
  padding: 12 * 2px 16 * 2px;
  margin-bottom: 8 * 2px;

  > .label {
    color: @color-black-3;
    font-size: 12 * 2px;
    font-weight: 400;
    line-height: 20 * 2px;
  }

  > .title {
    display: flex;
    flex-direction: row;
    overflow: hidden;
    align-items: center;
    font-size: 14 * 2px;
    line-height: 22 * 2px;
    color: @color-black-1;

    > .name {
      flex: 1;
      font-weight: 500;
    }

    > .money {
      flex-shrink: 0;
      font-weight: 400;
    }
  }

  > .intro {
    margin-top: 2 * 2px;
    display: flex;
    flex-direction: row;
    align-items: flex-start;

    > .detail {
      font-size: 12 * 2px;
      line-height: 20 * 2px;
      color: @color-brand;
    }

    > .tags {
      padding: 4 * 2px 0;
      line-height: 1em;
      flex: 1;

      > .item {
        display: inline-block;
        color: @color-black-2;
        font-size: 12 * 2px;
        padding: 0 8 * 2px;
        border-left: 1 * 2px solid @color-line-1;
        line-height: 12 * 2px;

        &:first-child {
          padding-left: 0;
          border-left: 0;
        }
      }
    }
  }
}
