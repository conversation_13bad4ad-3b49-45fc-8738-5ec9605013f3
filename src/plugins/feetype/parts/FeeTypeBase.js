import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by nanyuantingfeng on 20/07/2017 14:08.
 **************************************************/
import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { getBoolVariation, useDepartmentVisible } from '../../../lib/featbit'

export default class FeeTypeBase extends PureComponent {
  componentWillMount() {
    let { bus } = this.props
    bus.watch('element:ref:select:department', this.handleSelectDepartment)
    bus.watch('element:ref:select:payee', this.handleSelectPayee)
    bus.watch('element:ref:select:staff', this.handleSelectStaff)
    bus.watch('element:attachments:line:click', this.handleAttachment)
    api.dataLoader('@common.departmentsVisibility').load()
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('element:ref:select:department', this.handleSelectDepartment)
    bus.un('element:ref:select:payee', this.handleSelectPayee)
    bus.un('element:ref:select:staff', this.handleSelectStaff)
    bus.un('element:attachments:line:click', this.handleAttachment)
  }

  handleAttachment = (value, index) => {
    const { fnPreviewAttachments } = app.require('@components/utils/fnAttachment')
    fnPreviewAttachments({ value, index })
  }
  selectProperty = (field, flowId) => {
    let { bus, submitterId } = this.props
    let name = field && (get(field, 'dataType.entity') || get(field, 'dataType.elemType.entity'))
    let label = i18n.currentLocale === 'en-US' && field.enLable ? field.enLable : field.label || field.cnLabel
    let type = field && field.type
    let showBottom = field && field.optional
    let canSelectParent = 'all' === field.selectRange
    let { dependenceList, isDependence, multiple, selected, hideCode = false } = field
    if (type === 'select_search' || type?.endsWith(':select_search')) {
      return api.open('@basic:SelectSearch', {
        label,
        type,
        showBottom,
        canSelectParent,
        field,
        bus,
        selected,
        multiple,
        hideCode
      })
    } else {
      if (field?.allMatchList && !dependenceList?.length) {
        return api.open('@basic:SelectProperty', {
          data: field.allMatchList,
          label,
          type,
          showBottom,
          canSelectParent,
          multiple,
          selected,
          hideCode,
          submitterId: submitterId?.id
        })
      }
      const param = flowId ? { name, flowId } : { name }
      if(field?.dataType?.entity?.startsWith('basedata.Enum')){
        return api.open('@basic:SelectEnumValue', {
          data: dependenceList,
          popupTitle: i18n.currentLocale === 'en-US'&& field.enLable ? field.enLable : field.label || field.cnLabel,
          params: param
        })
      }
      const promise = isDependence
        ? Promise.resolve({ items: dependenceList })
        : api.invokeService('@common:get:property:by:name', param)
      return promise.then(data => {
        return api.open('@basic:SelectProperty', {
          data,
          label,
          type,
          showBottom,
          canSelectParent,
          multiple,
          selected,
          hideCode,
          submitterId: submitterId?.id
        })
      })
    }
  }

  handleSelectDepartment = field => {
    if (useDepartmentVisible()) {
      return api.open('@basic:SelectDepartmentV3', field)
    }
    const { submitterId } = this.props
    const canSelectParent = 'leaf' !== field.selectRange
    return api.open('@basic:SelectDepartmentV2', {
      showBottom: field.optional,
      canSelectParent,
      dataSource: field.dataSource,
      isAsyn: field.isAsyn,
      emptyText: field.emptyText,
      isVisibilityDep: true,
      onlyBelongDepartment: field.onlyBelongDepartment,
      submitter: submitterId,
      submitterId: submitterId && submitterId.id
    })
  }

  handleSelectPayee = (selectedPayeeId, dependenceList, dependence, isFeeDetailPayeeId, options) => {
    let { isModifyBill, flowId, billSpecification, payeeComponentData } = this.props
    const { specification, template } = this.state
    const templateid = get(payeeComponentData, 'visible') ? billSpecification?.id : specification?.id
    const allowClear =
      template?.find(item => item.name === 'feeDetailPayeeId' && item.allowClear === true)?.allowClear || false
    return api.open(
      getBoolVariation('new_version_of_payment_account') ? '@basic:SelectPayee' : '@basic:SelectPayeeOld',
      {
        allowClear,
        isModifyBill,
        flowId,
        selectedPayeeId,
        dependenceList,
        dependence,
        isFeeDetailPayeeId,
        templateid,
        specification: get(payeeComponentData, 'visible') ? billSpecification : specification,
        filterRules: options?.filterRules
      }
    )
  }

  handleSelectStaff = (whiteList, field) => {
    if (field.valueRangeFilter && field.valueRangeFilter !== 'false' && this.fnGetBillDate) {
      return this.fnGetBillDate(field.valueRangeFilter).then(isByRule => {
        return api.invokeService('@layout:select:staff', {
          whiteList,
          showBottom: field.optional,
          isVisibilityStaffs: false,
          isByRule,
          isAllowExternalStaff: field.allowExternalStaff,
          allowInteriorStaff: field.allowInteriorStaff ?? true
        })
      })
    } else {
      return api.invokeService('@layout:select:staff', {
        whiteList,
        showBottom: field.optional,
        isVisibilityStaffs: false,
        isAllowExternalStaff: field.allowExternalStaff,
        allowInteriorStaff: field.allowInteriorStaff ?? true
      })
    }
  }
}
