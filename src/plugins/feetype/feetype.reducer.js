/**************************************************
 * Created by nanyuanting<PERSON> on 20/07/2017 14:20.
 **************************************************/
import { Reducer } from '@ekuaibao/store'
import key, { ID } from './key'

const reducer = new Reducer(ID, {
  feetypes: []
})

reducer.handle(key.GET_FEETYPE_LIST, (state, action) => {
  const { items } = action.payload
  return { ...state, feetypes: items }
})

reducer.handle(key.GET_AUTO_CAL_RESULT)((state, action) => {
  return state
})

reducer.handle(key.GET_AUTO_CAL_RESULT)((state, action) => {
  return state
})

export default reducer
