import React, { PureComponent } from 'react'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import FeetypeInfoContainer from './parts/FeetypeInfoContainer'
import { fnLinkDetailEntitiesValue } from '../bill/utils/relatedExpenseLinkUtils'

@EnhanceConnect((state: any) => ({
  globalFields: state['@common'].baseDataProperties.data,
  feetypes: state['@common'].feeTypeList
}))
// @ts-ignore
@EnhanceTitleHook(i18n.get('消费详情'))
export default class FeeTypeInfoPage extends PureComponent<any, any> {
  state: any = {}
  bus = new MessageCenter()

  componentDidMount() {
    const { params: { flowId, detailId } } = this.props
    api.invokeService('@common:get:flow:detail:info', { id: flowId }).then(({ value: flow }: any) => {
      const detail = flow.form.details.find((d: any) => d.feeTypeForm.detailId === detailId)
      const cDetail = fnLinkDetailEntitiesValue(detail)
      this.setState({ flow, detail: cDetail, ownerId: flow.ownerId.id })
    })
    this.bus.watch('invoice:updata:bills', this.handleUpdateBills)
  }

  private handleUpdateBills = () => {
    history.go(-1)
  }

  render() {
    const { flow, detail, ownerId } = this.state
    const { globalFields, feetypes, params } = this.props
    const { isMine } = params
    if (!flow) return null
    return (
      <FeetypeInfoContainer
        globalFields={globalFields}
        feetypes={feetypes}
        feetype={detail.feeTypeId}
        value={detail.feeTypeForm}
        isEdit={false}
        risks={[]}
        ds={[detail.feeTypeForm]}
        template={detail.specificationId}
        idx={0}
        submitterId={flow.form.submitterId}
        billType={flow.formType}
        showBillBar={true}
        billForm={flow.form}
        flowId={flow.id}
        isMine={isMine}
        billBus={this.bus}
        hideActionsPart={true}
        ownerId={ownerId}
      />
    )
  }
}
