import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import SelectFeeType from '../parts/SelectFeetype'
import <PERSON>hanceTitleHook from '../../../lib/EnhanceTitleHook'
import { app as api } from '@ekuaibao/whispered'
import { isObject } from '@ekuaibao/helpers'

@EnhanceConnect(state => ({
  filterFeeTypes: state['@common'].newFilterFeeTypes.items,
  filterFeeTypeMap: state['@common'].newFilterFeeTypes.map,
  feeTypeMap: state['@common'].feetypes.map,
  feeTypeList: state['@common'].feetypes.data
}))
@EnhanceTitleHook(props => props.title)
export default class SelectFeeTypeModal extends PureComponent {
  constructor(props) {
    super(props)
    const { currentFeeType } = props
    this.currentFeeType = currentFeeType
    this.state = { feeTypeRecommends: [] }
  }

  async componentDidMount() {
    const { specification, needRecommend = false, invoices, filterFeeTypeMap, submitterId } = this.props
    if (needRecommend) {
      const specificationId = specification.id
      const staffId = isObject(submitterId) ? submitterId.id : submitterId
      // 常用推荐
      let { items = [] } = await api.invokeService('@feetype:get:feeType:recommend', {
        specificationId,
        invoices,
        staffId
      })
      if (filterFeeTypeMap) {
        items = items.filter(item => !!filterFeeTypeMap[item.id])
      }
      this.setState({ feeTypeRecommends: items })
    }
  }

  componentWillUnmount() {
    api.invokeService('@layout:set:header:title', '')
  }

  handleLineClick = data => {
    let { fn, updateCurrentFeeType } = this.props
    if (updateCurrentFeeType) {
      updateCurrentFeeType(data).then(() => {
        this.props.layer.emitOk(data)
      })
      return
    }
    if (!fn) this.props.layer.emitOk(data)
    fn &&
      fn(data.id).then(result => {
        result && this.props.layer.emitOk(result)
      })
  }
  handleCancelClick = () => {
    this.props.layer.emitCancel()
  }

  fnGetColorPaths(feeType) {
    let pArr = []
    let fn = feeType => {
      if (feeType.parentId) {
        let pFeeType = this.props.feeTypeMap[feeType.parentId]
        pFeeType && pArr.push(pFeeType)
        pFeeType && fn(pFeeType)
      }
    }
    feeType && fn(feeType)
    return pArr
  }

  render() {
    const {
      filterFeeTypes,
      isBatchImport,
      isShowCancelBtn,
      feetype,
      recommends,
      isShowTips = false,
      feeTypeList,
      displayList
    } = this.props
    let availableFeetypes = feetype ? feetype : filterFeeTypes
    if (window.FROM_SUISHOUJI) {
      availableFeetypes = feeTypeList
    }
    const { feeTypeRecommends } = this.state
    return (
      <div className="h-100p w-100p">
        {isShowTips && (
          <div style={{ color: '#1d2b3', marginRight: '16px', marginLeft: '16px' }}>
            {i18n.get('您选择的部分申请事项，未包含消费明细，请选择这些申请事项要导入的费用类型')}
          </div>
        )}
        <SelectFeeType
          dataSource={availableFeetypes}
          recommends={recommends || feeTypeRecommends}
          onLineClick={this.handleLineClick}
          onCancel={this.handleCancelClick}
          colorPathFeeTypes={this.fnGetColorPaths(this.currentFeeType)}
          isBatchImport={isBatchImport}
          currentFeeType={this.currentFeeType}
          isShowCancelBtn={isShowCancelBtn}
          displayList={displayList}
        />
      </div>
    )
  }
}
