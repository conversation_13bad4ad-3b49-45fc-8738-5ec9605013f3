@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.parentBox {
  height: 100%;
  overflow-y: scroll;

  :global {
    .border-line {
      padding: 32px;
      border-bottom: 2px solid rgba(20, 34, 52, 0.15);
    }

    .addRepayBtn {
      margin: 35px 20px;
    }

    .placholder-txt {
      color: rgba(20, 34, 52, 0.3);
    }

    .title {
      color: rgba(20, 34, 52, 0.3) !important;
    }

    .zhTxt {
      margin-left: 138px;
      color: rgba(29, 43, 61, 0.5);
    }
  }

  .header {
    display: flex;
    margin: 15px;
    width: calc(100% - 30px);

    .header-item {
      background-color: #f1f2f3;
      text-align: center;
      width: 50%;
      padding: 10px 0;

      .txt {
        color: #bdc1c6;
      }

      .number {
        color: #2a2a2c;
        font-weight: 600;
      }

      span {
        display: block;
      }
    }
  }

  .custom-input-item {
    padding-left: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background-color: white;

    &:nth-child(2) {
      padding-bottom: 0;
    }

    :global {
      .label {
        width: 200px;
        color: #54595b;
        font-size: 28px;
        margin-right: 0.1rem;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-input-item-right {
        padding-top: 15px;

        .am-list-item.am-input-item {
          padding: 0;

          input {
            font-weight: 600;
          }
        }

        .item-warn-warning {
          width: 90px;
          height: 90px;
        }

        .item-warn-error {
          width: 90px;
          height: 90px;
        }
      }
    }
  }

  .currency-money-symbol {
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    align-items: center;

    :global {
      .currency-symbol {
        font-size: 32px;
        text-align: left;
        color: rgba(29, 43, 61, 0.5);
        width: 128px;
      }

      .currency-code {
        margin: 0 10px;
        color: #1d2b3d;
      }

      .ml_30 {
        margin-left: 30px;
      }

      .ml_10 {
        margin-left: 10px;
      }

      .currency-icon {
        margin-right: 8px;
      }

      .arrow {
        height: 44px;
        width: 44px;
        font-size: 28px;
        text-align: center;
        color: #b1b9bd;
      }
    }
  }

  .vertical-label {
    display: flex;
    align-items: center;
    white-space: pre-wrap;
    text-align: left;
    word-break: break-all;
    margin-bottom: @space-4;
    font-size: 28px;
    .font-weight-2;

    .vertical-title {
      .font-weight-2;
      display: block;
      color: rgba(29, 43, 61, 0.75);

      div {
        display: inline;
      }
    }

    .vertical-optional {
      .font-weight-3;
      font-size: 28px;
      color: #f4526b;
      margin: 0 8px;
    }
  }

  .vertical-content {
    padding-left: 20px;

    .vertical-content-line {
      display: flex;
      align-items: center;
    }
  }

  .line0 {
    display: flex;
    align-items: center;
    width: 100%;
    overflow: hidden;

    :global {
      .right {
        flex: 1;
        margin-left: 20px;
        font-size: 32px;
        line-height: 1;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .bank-img {
        display: flex;
        flex-direction: row;
        background-color: #f9f9f9;
        border: 2px solid #efefef;
        border-radius: 10px;
        justify-content: center;
        align-items: center;
        height: 50px;
        padding: 5px 7px;

        .payee-icon {
          height: 40px;
          width: 40px;
        }

        span {
          font-size: 32px;
          margin-left: 5px;
        }
      }
    }
  }
}
