@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.AddTagModal {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--eui-bg-base);
  :global {
    .add_tag_modal_content {
      flex: 1;
      display: flex;
      flex-direction: column;
      .content_item_wrapper {
        background-color: white;
        padding: 32px;
        display: flex;
        flex-direction: column;
        &:not(:last-child) {
          margin-bottom: 16px;
        }
        .tag_auto{
           color: var(--eui-decorative-sub-600);
           background-color: var(--eui-decorative-sub-100);
        }
        .tag_manual{
           color: var(--eui-primary-pri-600);
           background-color:var(--eui-primary-pri-100);
        }
        .content_item_label{
          color: var(--eui-text-title);
          margin-bottom: 16px;
          font: var(--eui-font-body-r1)
        }
        .content_item_label_disable{
          color: var(--eui-text-disabled);
          margin-bottom: 16px;
          font: var(--eui-font-body-r1)
        }
        .content_item_placeholder{
          font: var(--eui-font-head-r1);
          color: var(--eui-text-placeholder);

        }
        .content_item_value{
         font: var(--eui-font-head-r1);
         color: var(--eui-text-title);
        }

      }
      .command_tag_wrapper {
        padding: 0 32px;
      }
    }
    .add_tag_modal_footer {
      padding: 16px 32px;
      flex-shrink: 0;
      background-color: white;
    }
  }
}
