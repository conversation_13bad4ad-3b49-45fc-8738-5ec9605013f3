/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/8/22 下午7:18.
 */

import React, { PureComponent } from 'react'
import { ImportCards } from '../parts/ImportCards'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'

@EnhanceTitleHook(props => props.name)
export default class ImportCardsModal extends PureComponent {
  render() {
    let { ordersData, submitterId } = this.props
    return <ImportCards orderData={ordersData} submitterId={submitterId} />
  }
}
