/**************************************************
 * Created by nanyuantingfeng on 19/07/2017 15:33.
 **************************************************/
const className = window.isPC && window.__PLANTFORM__ === 'KD_CLOUD' ? 'debugger_top_fix mt-45' : 'debugger_top_fix'

export default [
  {
    key: 'ManualrepayModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./ManualrepayModal'),
    title: i18n.get('手动还款')
  },
  {
    key: 'SelectFeeTypeModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./SelectFeeTypeModal'),
    title: i18n.get('选择费用类型')
  },
  {
    key: 'FeeTypeInfoModal',
    enhancer: 'layer',
    enhancerOptions: { className: className },
    getComponent: () => import('./FeeTypeInfoModal'),
    title: i18n.get('消费记录')
  },
  {
    key: 'ImportCardsModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./ImportCardsModal'),
    title: i18n.get('订单信息')
  },
  {
    key: 'RelatedApplicationModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./RelatedDetails/RelatedApplicationModal'),
    title: i18n.get('添加关联明细')
  },
  {
    key: 'modifyMoneyModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: true,
      animated: true,
      visible: true,
      className: 'fix-ekb-enhance-modal-clean-wrapper'
    },
    getComponent: () => import('./RelatedDetails/ModifyMoneyModal')
  },
  {
    key: 'AddTagModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./AddTagModal'),
    title: i18n.get('添加审批意见')
  }
]
