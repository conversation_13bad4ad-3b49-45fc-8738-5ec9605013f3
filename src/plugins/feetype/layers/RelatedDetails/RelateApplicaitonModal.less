@import '~@ekuaibao/eui-styles/less/token.less';

.relate-wrapper {
  background: @color-white-1;
  padding-bottom: @space-12;
  .relate-item-wrapper {
    margin-top: @space-8;
    .title-seg {
      border-top: 2px solid var(--eui-line-divider-default);
    }
    .details-title {
      font-size: 28px;
      color: @color-black-1;
      margin: @space-6 0 0 60px;
    }

    .title-wrapper {
      display: flex;
      align-items: flex-start;
      .title-content {
        width: 100%;
        .ra-top {
          .des {
            width: 0;
          }
        }
      }
    }

    .item-content {
      padding: @space-7 108px;
      .item-title {
        display: flex;
        align-items: center;
        .custom-info {
          flex: 1;
        }
        .relate-icon {
          width: 64px;
          height: 64px;
          background: rgba(24, 144, 255, 0.1);
          border-radius: 64px;
          margin-right: @space-7;
          margin-top: @space-5;
          .icon {
            width: 100%;
            height: 100%;
            color: @color-brand;
            padding: @space-5;
          }
        }
      }
    }

    .info-money {
      margin-top: @space-7;
      display: flex;
      align-items: center;
      .money-item {
        width: 50%;
      }
    }

    .selected-style {
      background: rgba(29, 43, 61, 0.03);
    }
    .p-12-24 {
      padding: @space-7 48px;
    }

    .title {
      font-size: 32px;
      color: @color-black-1;
      line-height: 48px;
      margin-bottom: @space-6;
    }
    .f-bold {
      .font-weight-3;
    }
  }

  .checkbox-type {
    margin: @space-4 @space-7 0 0;
    .am-checkbox.am-checkbox-checked .am-checkbox-inner {
      border-radius: 0 !important;
    }
    .am-checkbox-inner {
      border-radius: 0 !important;
    }
  }

  .apply-item-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 0;
    padding: @space-6 48px;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 32px 0 var(--brand-1);
    .checkbox-type {
      margin-top: 0;
      .check-box {
        margin-right: @space-6;
      }
    }
    .btn {
      // margin-right: @space-7;
      padding: @space-4 @space-9;
      border-radius: @radius-3;
      font-weight: 600;
    }
    .primary {
      background-color: @color-brand;
      color: #fff;
    }
    .cancel {
      color: #1d2b3d;
      background-color: rgba(29, 43, 61, 0.06);
    }
  }
}
