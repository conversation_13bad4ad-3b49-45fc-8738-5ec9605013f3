import { app } from '@ekuaibao/whispered'
import './ModifyMoneyModal.less'
import React, { PureComponent } from 'react'
import { Dynamic } from '@ekuaibao/template'
// @ts-ignore
import { createForm } from 'rc-form'
// import { editable } from '../../../../components'
const editable = app.require('@components/dynamic/index.editable')
import MessageCenter from '@ekuaibao/messagecenter/esm/index'
import { MoneyMath } from '@ekuaibao/money-math'
import { toast } from '../../../../lib/util'
// import { related } from '../../../../components/utils/Related'
const { related } = app.require('@components/utils/Related')
interface Props {
  bus: any
  layer: any
  data: any
  _tempConsumId: string
}

export default class ModifyMoneyModal extends PureComponent<Props> {
  bus: any = new MessageCenter()
  template = [
    {
      name: 'modifyMoney',
      placeholder: i18n.get('请输入金额'),
      type: 'money',
      optional: false,
      editable: true
    }
  ]
  constructor(props: Props) {
    super(props)
  }

  handleOk = () => {
    this.bus.getValueWithValidate(0).then((value: any) => {
      const { modifyMoney } = value
      if (Number(modifyMoney.standard) < 0) {
        toast.info(i18n.get('占用金额不能为负数'))
        return
      }
      const {
        data: { id, unwrittenOffAmount },
        _tempConsumId
      } = this.props
      const consumeUseMoneys = related.getDetialsMoneyByConsumeId(_tempConsumId)
      const obj = consumeUseMoneys && consumeUseMoneys.find((line: any) => line.relateId === id)
      const currentUseMoney = obj ? obj.consumeAmount : 0
      const useMoney = related.relatedMap[id]
      const otherUseMoney = new MoneyMath(useMoney).minus(currentUseMoney).value
      const over = new MoneyMath(unwrittenOffAmount).minus(otherUseMoney).value
      const notWrittenOffAmount = new MoneyMath(over).minus(value.modifyMoney).value
      const flag = new MoneyMath(notWrittenOffAmount).gte(0)
      flag ? this.props.layer.emitOk(value) : toast.info(i18n.get('占用金额不能大于可用金额'))
    })
  }

  render() {
    return (
      <div className="modifymoney-view-wrapper">
        <div className="title">
          <div>{i18n.get('修改本次报销金额')}</div>
        </div>
        <div className="content">
          <Dynamic
            isVertical={false}
            bus={this.bus}
            selectCurrencyDisable={true}
            elements={editable as any}
            template={this.template}
            create={T => createForm()(T)}
          />
        </div>
        <div className="footer-wrapper">
          <div
            className="footer"
            onClick={() => {
              this.props.layer.emitCancel()
            }}
          >
            {i18n.get('取消')}
          </div>
          <div className="line" />
          <div className="footer ok" onClick={this.handleOk}>
            {i18n.get('确定')}
          </div>
        </div>
      </div>
    )
  }
}
