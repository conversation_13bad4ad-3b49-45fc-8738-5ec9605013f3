import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import Enhance<PERSON><PERSON>leHook from '../../../../lib/EnhanceTitleHook'
import './RelateApplicaitonModal.less'
import { Checkbox, Dialog, Radio } from '@hose/eui-mobile'
import { MoneyMath } from '@ekuaibao/money-math'
import { get, set, cloneDeep, uniqBy } from 'lodash'
import { uuid } from '@ekuaibao/helpers'
import moment from 'moment'
import { toast, showLoading } from '../../../../lib/util'
import { parseAsMeta } from '../../../../lib/util/parser'
import { getRequisitionInfoDetails } from '../../feetype.action'
import { DocumentInfo, DetailInfo, MoneyText } from '../../parts/relatedDetailsElements'
import { MoneyIF } from '@ekuaibao/ekuaibao_types'
import DetailItemExpandFields from '../../../../elements/puppet/Details/DetailItemExpandFields'
import { getObjById } from '../../../../plugins/bill/utils/importFormatData' 

const EkbIcon = app.require<any>('@elements/ekbIcon')
const { related } = app.require<any>('@components/utils/Related')
const { fnFilterFeetypes } = app.require<any>('@bill/utils/billUtils')
const { computerNotWrittenOffAmount } = app.require<any>('@components/utils/fnRelatedUtils')
const moneyStr2StandardMoneyValue = app.require<any>('@lib/parser.moneyStr2StandardMoneyValue')

interface Props {
  layer: any
  data: any
  isImport: boolean
  newFilterFeeTypes?: any[]
  dataSource?: any[]
  applicationListDetails: any[]
  dataFromOrder: any
}
interface State {
  dataSource: any
  selectedIdForSingleSelect: string
}

// @ts-ignore
@EnhanceTitleHook(i18n.get('添加关联明细'))
export default class RelatedApplicationModal extends PureComponent<Props, State> {
  isAllowSelect: boolean
  constructor(props: Props) {
    super(props)
    this.isAllowSelect = this.fnIsAllowSelect()
    this.state = {
      dataSource: [],
      selectedIdForSingleSelect: ''
    }
  }

  initData = async (dataSource: any, relatedData: any[]) => {
    if (!dataSource || dataSource.length === 0) {
      return
    }
    const cDataSource = cloneDeep(dataSource)
    const { dataFromOrder } = this.props
    const { showImportInPermit } = dataFromOrder || {}
    let selectedIdForSingleSelect
    for (const line of cDataSource){
      const { flowId } = line
      const selectDataList = relatedData.find((selectLine: any) => selectLine.flowId === flowId)
      const dataList = selectDataList ? get(selectDataList, 'dataList', []) : []

      line.dataList.forEach((item: any) => {
        item.modifyValue = this.fnGetSaveData(item)
        item.feeTypeForm = {...item}
        const flag = dataList.find((l: any) => l.id === item.id)
        if (flag && showImportInPermit) {
          selectedIdForSingleSelect = item.id
        }else if(flag && !showImportInPermit){
          item.checked = true
        }
      })
      const details =  await getObjById(line.dataList) 
      line.dataList = details
    }
    if (selectedIdForSingleSelect) {
      this.setState({ selectedIdForSingleSelect })
    }
    return cDataSource
  }
  

  fnGetSaveData = (item: any) => {
    const useMoney = related.relatedMap[item.id] || moneyStr2StandardMoneyValue(0)
    const unwrittenOffAmount = new MoneyMath(item.unwrittenOffAmount).minus(useMoney).value
    return this.fnFDealWidthMoney(unwrittenOffAmount)
  }

  componentDidMount(): void {
    const { dataSource } = this.props
    if (dataSource && dataSource.length) {
      this.fnSetSeleteItem(dataSource)
    } else {
      showLoading(i18n.get('加载中...'))
      const ids = related.expenseLinkIds
      getRequisitionInfoDetails({ ids })
        .then((result: any) => {
          const items = result.items.length && result.items
          this.fnSetSeleteItem(items)
          toast.hide()
        })
        .catch(() => {
          toast.hide()
        })
    }
  }

  fnSetSeleteItem = async (arr: any[]) => {
    const { data } = this.props
    const relatedData = get(data, 'relatedData', [])
    const c = await this.initData(arr, relatedData)
    this.setState({ dataSource: c || [] })
  }

  fnIsAllowSelect = () => {
    const { allowAdd, notOverIsChecked } = related.specificationConfig
    return allowAdd && notOverIsChecked
  }

  handleOnSelect = (pObj: any, obj: any) => {
    const { checked } = obj
    obj.checked = !checked
    this.forceUpdate()
  }

  handleOnSelectRaido = (id: string) => {
    this.setState({ selectedIdForSingleSelect: id })
  }

  handleOnSelectP = (obj: any) => {
    const { isCheckedP } = obj
    obj.isCheckedP = !isCheckedP
    obj.dataList = get(obj, 'dataList', []).map((line: any) => {
      const { flag } = this.fnComputerNotWrittenOffAmount(line)
      const isDisable = this.isAllowSelect && flag
      if (!isDisable) {
        line.checked = obj.isCheckedP
      }
      return line
    })
    this.forceUpdate()
  }

  getResult = () => {}

  handleSave = async () => {
    const { isImport } = this.props
    let list = this.fnGetCheckedList()
    if (!list.length) {
      return toast.error(i18n.get('请选择要导入的申请单明细！'))
    }
    if (list.length > 50) {
      return Dialog.alert({
        title: i18n.get('提示'),
        content: i18n.get('单次最多可导入50条明细，您已选择{total}条，请修改！', { total: list.length })
      })
    }
    let noFeeTypeList = list.filter((line: any) => this.fnIsNoFeeType(line))
    if (isImport && noFeeTypeList && noFeeTypeList.length) {
      noFeeTypeList = await this.fnNoDetailsSelectFeetype(noFeeTypeList)
      const hasFeeTypeList = list.filter((line: any) => !this.fnIsNoFeeType(line))
      list = hasFeeTypeList.concat(noFeeTypeList)
    }
    const result = this.fnFormatDataSave(list)
    await this.props.layer.emitOk(result)
  }

  fnNoDetailsSelectFeetype = async (noFeeTypeList: any[]) => {
    const result: any = await app.open('@feetype:SelectFeeTypeModal', { isShowCancelBtn: true, isShowTips: true })
    let feeTypeId = (await app.invokeService('@feetype:get:feetype', result.id)) || {}
    const { expenseSpecification, ...other } = feeTypeId
    feeTypeId = { ...other, expenseSpecificationId: expenseSpecification }
    return noFeeTypeList.map((line: any) => {
      const dataList = line.dataList.map((detail: any) => {
        return { ...detail, feeTypeId }
      })
      return { ...line, dataList }
    })
  }

  fnFormatDataSave = (list: any[]) => {
    const { isImport, data } = this.props
    !isImport && related.removeByConsumeId(data._tempConsumId)
    const notSupportFeeTypeList = this.checkFeeType(list)
    if (notSupportFeeTypeList.length) {
      return this.renderNoSupportFeeType(uniqBy(notSupportFeeTypeList, 'feeTypeId.id'))
    }
    let dataList0: any[] = []
    list.forEach((line: any) => {
      const { dataList, ...others } = line
      const itemList = dataList
        ? dataList.map((v: any) => {
            // @ts-ignore
            const useMoney = related.relatedMap[v.id] || moneyStr2StandardMoneyValue(0)
            const unwrittenOffAmount = new MoneyMath(v.unwrittenOffAmount).minus(useMoney).value
            const noWrittenOffAmount = this.fnFDealWidthMoney(unwrittenOffAmount)
            v.modifyValue = noWrittenOffAmount
            const val = { consumeAmount: noWrittenOffAmount, relateId: v.id }
            return !isImport ? val : this.fnImport({ val, noWrittenOffAmount, v, line: { ...others, dataList: [v] } })
          })
        : []
      dataList0 = dataList0.concat(itemList)
    })
    this.fnRelatedDetails(dataList0)
    return !isImport ? list : dataList0
  }

  fnGetCheckedList = () => {
    const { data, dataFromOrder } = this.props
    const { showImportInPermit } = dataFromOrder || {}
    const { selectedIdForSingleSelect } = this.state
    let list: any[] = []
    list = cloneDeep(this.state.dataSource)
    list = list
      .map((line: any) => {
        const dataList = line.dataList
          .map((item: any) => {
            if (item.checked || (showImportInPermit && selectedIdForSingleSelect === item.id)) {
              if (data && data._tempConsumId) {
                item._tempConsumId = data._tempConsumId
              }
              return item
            }
          })
          .filter((detail: any) => detail)
        if (dataList && dataList.length) {
          return { ...line, dataList }
        }
      })
      .filter((l: any) => l)
    return list
  }

  fnRelatedDetails = (dataList: any[]) => {
    const { isImport, data } = this.props
    if (!isImport) {
      related.setRelatedMap({ id: data._tempConsumId, value: dataList })
    }
  }

  fnImport = ({
    val,
    noWrittenOffAmount,
    v,
    line
  }: {
    val: { consumeAmount: any; relateId: string }
    [key: string]: any
  }) => {
    const relatedItem = {
      id: uuid(),
      value: val
    }
    related.setRelatedMap({ id: relatedItem.id, value: [{ ...val }] })

    const dataList = get(line, 'dataList', []).map((detail: any) => {
      return { ...detail, _tempConsumId: relatedItem.id }
    })
    const vMap: any = {}
    Object.keys(v).forEach((key: string) => {
      if (v[key]) {
        vMap[key] = v[key]
      }
    })

    return {
      noWrittenOffAmount,
      ...vMap,
      linkDetailEntities: [{ ...line, dataList }]
    }
  }

  fnFDealWidthMoney = (money: any) => {
    if (!new MoneyMath(money).gte(0)) {
      set(money, 'standard', '0')
    }
    return money
  }

  checkFeeType = (list: any[]) => {
    let cList: any[] = []
    list.forEach((line: any) => {
      const { dataList } = line
      const items = dataList.map((item: any) => item)
      cList = cList.concat(items)
    })
    const visibleFeeTypeIds = fnFilterFeetypes(this.props.newFilterFeeTypes)
    const notSupportFeeTypeList: any[] = []
    visibleFeeTypeIds.length &&
      cList.forEach(v => !visibleFeeTypeIds.includes(v?.feeTypeId?.id) && notSupportFeeTypeList.push(v))
    return notSupportFeeTypeList
  }

  renderNoSupportFeeType = (list: any) => {
    Dialog.alert({
      title: i18n.get('创建失败'),
      content: (
        <>
          <div>{i18n.get('当前单据模板不支持:')}</div>
          <div>{list.map((v: any) => v?.feeTypeId?.name).join(i18n.get('、'))}</div>
          <div>{i18n.get('请手动创建明细并关联')}</div>
        </>
      )
    })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  fnCheckedActive = (line: any) => {
    const detailId = get(line, 'id')
    const useMoney = related.relatedMap[detailId]
    const notWrittenOffAmount = new MoneyMath(line.unwrittenOffAmount).minus(useMoney).value
    const flag = new MoneyMath(notWrittenOffAmount).equal(0) || !new MoneyMath(notWrittenOffAmount).gte(0)
    const isDisabled = this.isAllowSelect && flag
    return !isDisabled
  }

  handleSelectAll = (checked: boolean) => {
    this.state.dataSource.forEach((line: any) => {
      const { dataList } = line
      dataList.map((item: any) => {
        if (this.fnCheckedActive(item)) {
          item.checked = checked
        }
      })
    })
    this.forceUpdate()
  }
  fnGetDataList = () => {
    let list: any[] = []
    this.state.dataSource.forEach((line: any) => {
      const { dataList } = line
      const items = dataList.map((item: any) => item)
      list = list.concat(items)
    })
    return list
  }

  checkAllSelected = () => {
    if (!this.state.dataSource.length) {
      return false
    }
    const list = this.fnGetDataList()
    return !list.find((v: any) => this.fnCheckedActive(v) && !v.checked)
  }

  fnComputerNotWrittenOffAmount = (data: any) => {
    const cData = this.props.data
    const _tempConsumId = get(cData, '_tempConsumId')
    return computerNotWrittenOffAmount(data, _tempConsumId)
  }

  fnIsNoFeeType = (item: any) => {
    const applyContentRule = this.fnGetApplyContentRule(item)
    return applyContentRule === 'manual' || applyContentRule === 'amountAndTrip' || applyContentRule === 'travelManage'
  }

  fnGetApplyContentRule = (item: any) => {
    const requisitionConfig = get(item, 'specificationId.configs', []).find(
      (line: any) => line.ability === 'requisition'
    )
    return get(requisitionConfig, 'applyContentRule', '')
  }

  render() {
    const { dataSource, selectedIdForSingleSelect } = this.state
    const { isImport, dataFromOrder } = this.props
    const { showImportInPermit, companyRealPayFromOrder } = dataFromOrder || {}
    const allSelected = this.checkAllSelected()
    // const dataSource = getObjById(dataSource.dataList)
    return (
      <div className="relate-wrapper">
        {dataSource.map((item: any) => {
          const { dataList } = item
          const isShowDetails = this.fnIsNoFeeType(item)
          let notWrittenOff = {}
          const obj = dataList.find((item: any) => {
            const { flag } = this.fnComputerNotWrittenOffAmount(item)
            return !flag
          })
          dataList.forEach((item: any) => {
            const { notWrittenOffAmount } = this.fnComputerNotWrittenOffAmount(item)
            notWrittenOff = new MoneyMath(notWrittenOff).add(notWrittenOffAmount).value
          })

          return (
            <div key={item.id} className="relate-item-wrapper">
              <RelateTitleView
                item={item}
                flag={!obj}
                isAllowSelect={this.isAllowSelect}
                onChange={this.handleOnSelectP}
                singleSelect={showImportInPermit}
                notWrittenOffAmount={notWrittenOff}
                isShowDetails={!isShowDetails}
              />
              {!isShowDetails && (
                <div className="p-12-24">
                  <div className="title-seg" />
                  <div className="details-title f-bold">{i18n.get('申请明细')}</div>
                </div>
              )}
              {dataList?.map((line: any) => {
                const { flag, notWrittenOffAmount } = this.fnComputerNotWrittenOffAmount(line)
                return (
                  <RelateItemView
                    isShowDetails={!isShowDetails}
                    key={line.id}
                    isAllowSelect={this.isAllowSelect}
                    singleSelect={showImportInPermit}
                    companyRealPayFromOrder={companyRealPayFromOrder}
                    selectedIdForSingleSelect={selectedIdForSingleSelect}
                    onRadioChange={this.handleOnSelectRaido}
                    data={line}
                    onChange={this.handleOnSelect.bind(this, item, line)}
                    isImport={isImport}
                    flag={flag}
                    notWrittenOffAmount={notWrittenOffAmount}
                  />
                )
              })}
            </div>
          )
        })}
        <Footer
          isImport={isImport}
          value={allSelected}
          singleSelect={showImportInPermit}
          onSelectAll={this.handleSelectAll}
          onSave={this.handleSave}
          onCancel={this.handleCancel}
        />
      </div>
    )
  }
}

function Footer(props: any) {
  const { value, singleSelect } = props
  return (
    <div className="apply-item-footer">
      <div className="checkbox-type">
        {!singleSelect && <Checkbox className="check-box" checked={value} onChange={props.onSelectAll}>
          {i18n.get('全选')}
        </Checkbox>}
      </div>
      <div className="dis-f">
        <div className="btn primary mr-10" onClick={props.onSave}>
          {i18n.get('确定')}
        </div>
        <div className="btn cancel" onClick={props.onCancel}>
          {i18n.get('取消')}
        </div>
      </div>
    </div>
  )
}

export function RelateTitleView(props: any) {
  const { onChange, item, flag, isAllowSelect, isShowDetails, notWrittenOffAmount, singleSelect } = props
  const { code, title, money } = item
  const specificationName = get(item, 'specificationId.name', '')
  const ownerName = get(item, 'ownerId.name', '')
  const dataList = get(item, 'dataList', [])
  const cCheckeds = dataList.filter((line: any) => line.checked)
  const checked = cCheckeds.length === dataList.length
  const isDisable = isAllowSelect && flag
  const standardSymbol = get(item, 'moneyNode.standardSymbol')
  return (
    <div
      className={`title-wrapper p-12-24 ${checked && isShowDetails ? 'selected-style' : ''}`}
      onClick={() => !isDisable && onChange && onChange(item)}
    >
      <div className="checkbox-type">
        {!singleSelect && <Checkbox checked={checked} disabled={isDisable}/>}
      </div>
      <div className="title-content">
        <div className="ra-top">
          <div className="title f-bold">{i18n.get(title)}</div>
          <DocumentInfo code={code} specificationName={specificationName} ownerName={ownerName} />
        </div>
        <div className="info-money">
          <div className="money-item">
            <MoneyText value={notWrittenOffAmount} text={i18n.get('未报销金额')} currencySymbol={standardSymbol} />
          </div>
          <div className="money-item">
            <MoneyText value={money} text={i18n.get('明细金额')} currencySymbol={standardSymbol} />
          </div>
        </div>
      </div>
    </div>
  )
}
interface ItemProps {
  data: any
  isImport: boolean
  onChange: () => void
  isAllowSelect: boolean
  isShowDetails?: boolean
  flag: boolean
  notWrittenOffAmount: any
  singleSelect: boolean
  onRadioChange: any
  selectedIdForSingleSelect?: string
  companyRealPayFromOrder?: MoneyIF
}
export function RelateItemView(props: ItemProps) {
  const {
    onChange,
    data,
    isAllowSelect,
    isShowDetails = true,
    flag,
    notWrittenOffAmount,
    singleSelect,
    onRadioChange,
    selectedIdForSingleSelect,
    companyRealPayFromOrder,
  } = props
  const { feeDate, amount, id } = data
  const amountStandard = typeof amount === 'object' ? amount?.standard : amount
  const companyRealPayStandard = typeof companyRealPayFromOrder === 'object'
    ? companyRealPayFromOrder?.standard : companyRealPayFromOrder
  const isDisable = isAllowSelect && flag
    || (companyRealPayFromOrder && Number(amountStandard) < Number(companyRealPayStandard))
  const feeTypeId: any = get(data, 'feeTypeId')
  const { name, code, icon } = feeTypeId ? feeTypeId : { name: '', code: '', icon: '' }
  const isChecked = singleSelect ? id === selectedIdForSingleSelect: data.checked
  const standardSymbol = get(data, 'requisitionMoney.standardSymbol')
  const  globalFields = app.getState()['@common'].baseDataProperties.data || []
  const requisitionSpecificationId = get(data, 'feeTypeId.requisitionSpecificationId', '')
  const components = (requisitionSpecificationId && parseAsMeta(requisitionSpecificationId, globalFields, {})) || []
  
  const fnOnClick = singleSelect
    ? () => !isDisable && onRadioChange(id)
    : () => !isDisable && onChange()
  return (
    <div
      className={`item-content ${isChecked ? 'selected-style' : ''}`}
      onClick={fnOnClick}
      style={!isShowDetails ? { display: 'none' } : {}}
    >
      <div className="item-title">
        <div className="checkbox-type">
          {singleSelect
            ? <Radio checked={isChecked} disabled={isDisable}/>
            : <Checkbox checked={isChecked} disabled={isDisable}/>}
        </div>
        <div className="relate-icon">
          {icon ? <img src={icon} className="icon" /> : <EkbIcon name="#EDico-bill1" className="icon" />}
        </div>
        <div className="custom-info">
          <div>{name}</div>
          <DetailInfo code={code} date={feeDate ? moment(feeDate).format('YYYY.MM.DD') : i18n.get('无消费日期')} />
          <DetailItemExpandFields 
            feeTypeForm={data.feeTypeForm}
            style={{ marginLeft:0 }}
            components={components}
           />
        </div>
      </div>
      <div className="info-money">
        <div className="money-item">
          <MoneyText
            color={flag ? '#ff0000' : '#1D2B3D'}
            value={flag ? 0 : notWrittenOffAmount}
            text={i18n.get('未报销金额')}
            currencySymbol={standardSymbol}
          />
        </div>
        <div className="money-item">
          <MoneyText color={'#1D2B3D'} value={amount} text={i18n.get('明细金额')} currencySymbol={standardSymbol} />
        </div>
      </div>
    </div>
  )
}
