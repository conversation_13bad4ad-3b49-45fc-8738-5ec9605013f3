import React, { Fragment, useState, useEffect, useMemo } from 'react'
import styles from './ManualrepayModal.module.less'
import { InputItem } from 'antd-mobile'
import { Button, Dialog } from '@hose/eui-mobile'
import { app } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import { Fetch } from '@ekuaibao/fetch'
import { get } from 'lodash'
import MoneyNzh from '../../../elements/puppet/MoneyNzh'
import { Upload2QiNiu } from '../../../elements/puppet/Upload'
import { getBoolVariation } from '../../../lib/featbit'

const { fnClickAttachments, parseAsShowValue, fnPreviewAttachments } = app.require('@components/utils/fnAttachment')

const ManualrepayModal = props => {
  const [preCheckedItem, setPreCheckedItem] = useState([])

  const [collectionAccount, setCollectionAccount] = useState({})

  const [newRepayMent, setNewRepayMent] = useState({})

  const bus = new MessageCenter()

  // 回显数据
  useEffect(() => {
    const { loanManualRepayment } = props
    if (loanManualRepayment) {
      const {
        preCheckedItem = [],
        collectionAccount,
        manualRepaymentMoney,
        waitWrittenOffAmount,
        attachments
      } = loanManualRepayment
      setNewRepayMent(loanManualRepayment)
      setPreCheckedItem(preCheckedItem)
      setAttachments(attachments)
      setManualRepaymentMoney(manualRepaymentMoney)
      setWaitWrittenOffAmount(waitWrittenOffAmount)
      setCollectionAccount(collectionAccount)
    }
  }, [])

  useEffect(() => {
    bus.watch('manual:repay:line:click', handleAttachment)

    return () => {
      bus.un('manual:repay:line:click', handleAttachment)
    }
  })

  // 预览
  const handleAttachment = (value, index) => {
    fnPreviewAttachments({ value, index })
  }

  // 选择收款账户点击事件
  const chooseCollectionAccount = () => {
    if (isEidtable) handleSelectPayee()
  }

  // 选择收款账户
  const handleSelectPayee = () => {
    const isNew = getBoolVariation('select_pay_account_new')
    app
      .open(isNew ? '@basic:SelectPayViewNew' : '@basic:SelectPayView', {
        showAll: true,
        data: { selectItem: collectionAccount },
        isManual: true
      })
      .then(result => {
        setCollectionAccount(result)
      })
  }

  const [attachments, setAttachments] = useState([])

  // 校验添加的还款
  const validateForm = () => {
    if (
      !Object.keys(manualRepaymentMoney).length ||
      !Object.keys(waitWrittenOffAmount).length ||
      !Object.keys(collectionAccount).length ||
      !preCheckedItem.length
    ) {
      Dialog.alert({ content: i18n.get('信息不完整，请重新检查') })
      return false
    }
    return true
  }

  // 是否外币借款
  const isForeignCurrency = useMemo(() => {
    return !!(preCheckedItem && preCheckedItem.length && preCheckedItem[0].foreignAmount)
  }, [preCheckedItem])

  // 借款包总额
  const totalRemainAmount = useMemo(() => {
    if (preCheckedItem.length) {
      const { loanInfoId, remain, foreignCurrencyLoan } = preCheckedItem[0]
      return isForeignCurrency
        ? foreignCurrencyLoan
          ? foreignCurrencyLoan.foreign
          : loanInfoId.foreignCurrencyLoan.foreign
        : loanInfoId.remain
        ? loanInfoId.remain
        : remain
    } else return 0
  }, [isForeignCurrency, preCheckedItem])

  // 本次核销金额
  const thisTimeWirttenAmount = useMemo(() => {
    let amount = 0
    if (preCheckedItem.length) {
      if (isForeignCurrency) {
        amount =
          typeof preCheckedItem[0].foreignAmount == 'string'
            ? preCheckedItem[0].foreignAmount
            : preCheckedItem[0].foreignAmount.foreign
      } else {
        amount = +preCheckedItem[0].amount
      }
    }
    return amount
  }, [preCheckedItem])

  // 是否允许编辑汇率
  const allowEditRate = useMemo(() => {
    return app.getState('@common').currencyConfig?.allowEditRate
  }, [])

  // 提交事件
  const handleConfirm = () => {
    if (!validateForm()) return
    const { foreign, standard } = manualRepaymentMoney

    const validAmount = isForeignCurrency ? foreign : standard

    const loanManualRepayment = {
      title: preCheckedItem[0].title,
      accountId: collectionAccount.id, //付款账户
      loanInfoId: preCheckedItem[0].loanInfoId.id,
      repaymentDate: preCheckedItem[0].repaymentDate,
      manualRepaymentMoney,
      waitWrittenOffAmount,
      attachments,
      collectionAccount,
      preCheckedItem
    }

    let compareAmount = Number(validAmount) + Number(thisTimeWirttenAmount) - Number(totalRemainAmount)

    if (Number(standard) == 0) {
      Dialog.alert({ content: i18n.get('还款金额必须大于零') })
      return
    }

    if (compareAmount > 0) {
      Dialog.alert({ content: i18n.get('还款金额加核销金额不能大于借款余额') })
    } else if (compareAmount < 0) {
      if (checkDegree == 'strong') {
        Dialog.alert({ content: i18n.get('您有借款未结清，请将借款结清后再提交单据') })
      } else {
        Dialog.confirm({
          title: i18n.get('提示'),
          content: i18n.get('您有借款未结清，是否继续提交单据？'),
          confirmText: i18n.get('继续'),
          onConfirm: props.layer.emitOk({ ...loanManualRepayment, type: props.type })
        })
      }
    } else {
      props.layer.emitOk({ ...loanManualRepayment, type: props.type })
    }
  }

  // 返回点击事件
  const handleComeBack = () => {
    props.layer.emitOk({ ...newRepayMent, type: props.type })
  }

  // 校验强度
  const checkDegree = useMemo(() => {
    return props.isForbiddenSubmit ? 'week' : 'strong'
  }, [props.isForbiddenSubmit])

  // 添加借款包点击事件
  const chooseRepay = () => {
    if (isEidtable)
      app.open('@basic:ManualRepayPopup', { ...props, preCheckedItem }).then(item => {
        setPreCheckedItem(item)
        const { foreignCurrencyLoan, totalMoneyNode } = item[0]
        if (foreignCurrencyLoan) {
          const {
            foreignStrCode,
            foreignNumCode,
            foreignSymbol,
            foreignUnit,
            foreignScale,
            rate,
            standardStrCode,
            standardSymbol,
            standardNumCode,
            standardScale,
            standardUnit
          } = foreignCurrencyLoan

          setManualRepaymentMoney(pre => ({
            ...pre,
            foreign: '0.00',
            standard: '0.00',
            foreignStrCode,
            foreignNumCode,
            foreignUnit,
            foreignScale,
            foreignSymbol,
            standardStrCode,
            standardSymbol,
            standardNumCode,
            standardScale,
            standardUnit,
            rate: rate
          }))
          setWaitWrittenOffAmount(pre => ({
            ...pre,
            foreign: '0.00',
            standard: '0.00',
            foreignStrCode,
            foreignNumCode,
            foreignUnit,
            foreignScale,
            foreignSymbol,
            standardStrCode,
            standardSymbol,
            standardNumCode,
            standardScale,
            standardUnit,
            rate: rate
          }))
        } else {
          if (totalMoneyNode) {
            const { standardStrCode, standardSymbol, standardNumCode, standardScale, standardUnit } = totalMoneyNode
            setManualRepaymentMoney(pre => ({
              ...pre,
              foreign: '0.00',
              standard: '0.00',
              standardStrCode,
              standardSymbol,
              standardNumCode,
              standardScale,
              standardUnit,
              foreignStrCode: '',
              foreignNumCode: '',
              foreignUnit: '',
              foreignScale: 0,
              foreignSymbol: ''
            }))
            setWaitWrittenOffAmount(pre => ({
              ...pre,
              foreign: '0.00',
              standard: '0.00',
              standardStrCode,
              standardSymbol,
              standardNumCode,
              standardScale,
              standardUnit,
              foreignStrCode: '',
              foreignNumCode: '',
              foreignUnit: '',
              foreignScale: 0,
              foreignSymbol: ''
            }))
          } else {
            const {
              standardStrCode,
              standardSymbol,
              standardNumCode,
              standardScale,
              standardUnit
            } = props.value.writtenOffAmount
            setManualRepaymentMoney(pre => ({
              ...pre,
              foreign: '0.00',
              standard: '0.00',
              standardStrCode,
              standardSymbol,
              standardNumCode,
              standardScale,
              standardUnit,
              foreignStrCode: '',
              foreignNumCode: '',
              foreignUnit: '',
              foreignScale: 0,
              foreignSymbol: ''
            }))
            setWaitWrittenOffAmount(pre => ({
              ...pre,
              foreign: '0.00',
              standard: '0.00',
              standardStrCode,
              standardSymbol,
              standardNumCode,
              standardScale,
              standardUnit,
              foreignStrCode: '',
              foreignNumCode: '',
              foreignUnit: '',
              foreignScale: 0,
              foreignSymbol: ''
            }))
          }
        }
      })
  }

  // 还款对象
  const [manualRepaymentMoney, setManualRepaymentMoney] = useState({
    standard: '0.00', //本位币金额
    standardUnit: '元', //本位币单位
    standardScale: 2, //本位币精度
    standardSymbol: '¥', //本位币符号
    standardNumCode: '156', //本位币数字代码
    standardStrCode: 'CNY', //本位币字母代码
    rate: '', //汇率
    foreign: '0.00', //外币金额
    foreignStrCode: '', //外币字母代码
    foreignNumCode: '', //外币数字代码
    foreignSymbol: '', //外币符号
    foreignUnit: '', //外币单位
    foreignScale: 2 //外币精度
  })

  // 待核销对象
  const [waitWrittenOffAmount, setWaitWrittenOffAmount] = useState({
    standard: '0.00', //本位币金额
    standardUnit: '元', //本位币单位
    standardScale: 2, //本位币精度
    standardSymbol: '¥', //本位币符号
    standardNumCode: '156', //本位币数字代码
    standardStrCode: 'CNY', //本位币字母代码
    rate: '', //汇率
    foreign: '0.00', //外币金额
    foreignStrCode: '', //外币字母代码
    foreignNumCode: '', //外币数字代码
    foreignSymbol: '$', //外币符号
    foreignUnit: '', //外币单位
    foreignScale: 2 //外币精度
  })

  // 异步处理待核销事件
  const handleWrittenOffAmount = newData => {
    const { foreign, standard, rate } = newData
    const curAmount = isForeignCurrency ? foreign : standard
    let computedAmount = totalRemainAmount - thisTimeWirttenAmount - Number(curAmount)
    if (isForeignCurrency) {
      setWaitWrittenOffAmount(prevState => ({
        ...prevState,
        foreign: Number(computedAmount).toFixed(2),
        standard: Number(computedAmount * rate).toFixed(2)
      }))
    } else {
      setWaitWrittenOffAmount(prevState => ({
        ...prevState,
        standard: Number(computedAmount).toFixed(2)
      }))
    }
  }

  // 监听还款金额，处理待核销金额
  useEffect(() => {
    props.eidtable && handleWrittenOffAmount(manualRepaymentMoney)
  }, [manualRepaymentMoney])

  // 统一处理待核销金额
  const handleAmountChange = ({ key }, value) => {
    if (Number(value) < 0) {
      value = '0.00'
    }
    switch (key) {
      case 'foreign':
        setManualRepaymentMoney(prevState => ({
          ...prevState,
          standard: Number(value * prevState.rate).toFixed(2),
          [key]: value
        }))

        return

      case 'standard':
        setManualRepaymentMoney(prevState => ({
          ...prevState,
          foreign: isForeignCurrency ? Number(value / prevState.rate).toFixed(2) : '0.00',
          [key]: value
        }))

        return

      case 'rate':
        if (value > 0) {
          setManualRepaymentMoney(prevState => ({
            ...prevState,
            foreign: Number(prevState.standard / value).toFixed(2),
            [key]: value
          }))
        } else {
          setManualRepaymentMoney(prevState => ({
            ...prevState,
            [key]: value
          }))
        }

        return
    }
  }

  // 输入框失焦事件
  const handleOnBlur = ({ type, key }, value) => {
    switch (type) {
      case 'manual':
        return setManualRepaymentMoney(prevState => ({ ...prevState, [key]: Number(value).toFixed(2) }))
    }
  }

  // 本位币借款DOM
  const StandardCurrencyPrefix = (transformCurrency, type) => {
    const { standardStrCode, standard } = transformCurrency
    return (
      <>
        <div className={styles['currency-money-symbol']}>
          <div className="currency-symbol">本位币</div>
          <div className="currency-code">{standardStrCode}</div>
          <InputItem
            placeholder="请输入金额"
            clear
            type="digit"
            disabled={preCheckedItem.length ? false : true}
            editable={isEidtable && type == 'manual' ? true : false}
            value={standard}
            onChange={handleAmountChange.bind(this, { type, key: 'standard' })}
            onBlur={handleOnBlur.bind(this, { type, key: 'standard' })}
          />
        </div>
        <div className="zhTxt">{MoneyNzh.toMoney(standard, { outSymbol: false })}</div>
      </>
    )
  }

  // 外币借款DOM
  const ForeignCurrencyPrefix = (transformCurrency, type) => {
    const { standardStrCode, foreignStrCode, rate, foreign, standard } = transformCurrency
    return (
      <>
        <div className={styles['currency-money-symbol']}>
          <div className="currency-symbol">原币</div>
          <div className="currency-code">{foreignStrCode}</div>
          <InputItem
            placeholder="请输入金额"
            clear
            disabled={preCheckedItem.length ? false : true}
            editable={isEidtable && type == 'manual' ? true : false}
            value={foreign}
            type="digit"
            onChange={handleAmountChange.bind(this, { type, key: 'foreign' })}
            onBlur={handleOnBlur.bind(this, { type, key: 'foreign' })}
          />
        </div>
        <div className={styles['currency-money-symbol']}>
          <div className="currency-symbol">本位币</div>
          <div className="currency-code">{standardStrCode}</div>
          <InputItem
            placeholder="请输入金额"
            clear
            disabled={preCheckedItem.length ? false : true}
            editable={isEidtable && type == 'manual' ? true : false}
            value={standard}
            type="digit"
            onChange={handleAmountChange.bind(this, { type, key: 'standard' })}
            onBlur={handleOnBlur.bind(this, { type, key: 'standard' })}
          />
        </div>
        <div className="zhTxt">{MoneyNzh.toMoney(foreign, { outSymbol: false })}</div>
        {type == 'manual' && (
          <div className={styles['currency-money-symbol']}>
            <div className="currency-symbol">{i18n.get('核算汇率')}</div>
            <InputItem
              className="ml_30"
              placeholder={i18n.get('请输入核算汇率')}
              clear
              type="digit"
              disabled={preCheckedItem.length ? false : true}
              editable={isEidtable && type == 'manual' && allowEditRate ? true : false}
              value={rate}
              onChange={handleAmountChange.bind(this, { type, key: 'rate' })}
              onBlur={handleOnBlur.bind(this, { type, key: 'rate' })}
            />
          </div>
        )}
      </>
    )
  }

  const preSetValue = () => {
    return attachments.map(parseAsShowValue)
  }

  // 渲染还款/待核销formItem
  const renderAmount = type => {
    switch (type) {
      // 还款金额
      case 'manual':
        return isForeignCurrency
          ? ForeignCurrencyPrefix(manualRepaymentMoney, type)
          : StandardCurrencyPrefix(manualRepaymentMoney, type)
      // 待核销金额
      case 'writtenOff':
        return isForeignCurrency
          ? ForeignCurrencyPrefix(waitWrittenOffAmount, type)
          : StandardCurrencyPrefix(waitWrittenOffAmount, type)
    }
  }

  // 渲染添加的银行
  const generateBankText = () => {
    let { cardNo, name, accountNo, accountName } = collectionAccount
    let nameValue = accountName || name
    let codeValue = accountNo || cardNo || ''
    codeValue = codeValue.replace(/\s/gi, '')
    const n = nameValue.length > 3 ? nameValue.slice(0, 2) + '...' : nameValue
    const b = codeValue.slice(codeValue.length - 4)
    return i18n.get(`{__k0} （{__k1}）`, { __k0: b, __k1: n })
  }

  // 银行图标
  const bankIcon = useMemo(() => {
    if (collectionAccount) {
      const { unionBank, icon } = collectionAccount
      return !!unionBank ? unionIcon : icon
    }
    return ''
  }, [collectionAccount])

  // 银行卡号
  const bankNumber = useMemo(() => {
    if (collectionAccount) {
      const { unionBank, bank } = collectionAccount
      return !!unionBank ? unionBank : bank
    }
    return ''
  }, [collectionAccount])

  // 文件change事件
  const uploadChange = file => {
    setAttachments(file)
  }

  // 文件删除事件回调
  const handleUploadDelete = (line, index) => {
    let TempFileList = attachments && attachments.slice(0)
    TempFileList && TempFileList.splice(index, 1)
    uploadChange(TempFileList)
  }

  // 文件点击事件回调
  const handleUploadClick = (line, index) => {
    fnClickAttachments({ bus, value: attachments, line, index })
  }

  // 文件变更事件回调
  const handleUploadChange = list => {
    if (attachments && attachments.length >= 1) {
      Dialog.alert({ content: i18n.get('只能上传一个附件') })
      return
    }

    let allSuffix = list[0].name.split('.')
    let lastSuffix = allSuffix[allSuffix.length - 1]
    let allowSuffix = ['jpg', 'jpeg', 'bmp', 'pdf']
    if (!allowSuffix.includes(lastSuffix)) {
      Dialog.alert({ content: i18n.get('上传的附件格式应为({__k0})', { __k0: allowSuffix.join('/') }) })
      return
    }

    const TempFileList = attachments && attachments.slice(0)
    handleUploadDone({ list, value: TempFileList, onChange: uploadChange }, true)
  }

  // 获取附件列表
  const getFileId = keys => {
    return Fetch.POST(
      '/api/v1/attachment/attachments',
      {},
      {
        body: {
          keys: keys
        }
      }
    )
  }

  const handleFileId = key => {
    return {
      id: key.id,
      url: key.url,
      thumbUrl: get(key, 'thumbUrl'),
      key: key.key
    }
  }

  // 处理附件上传
  const handleUploadDone = (props, parse) => {
    let { list, value, onChange } = props
    let params = []
    list.map(line => {
      let key = line.response['key']
      let filename = line.response['x:originalname']
      params.push({ key, filename })
    })
    getFileId(params).then(({ items }) => {
      let map = {}
      items &&
        items.map(item => {
          map[item.key] = item
        })
      list.map(line => {
        let key = line.response.key
        if (map[key]) {
          const fileId = handleFileId(map[key])
          line.response.fileId = fileId
          line.fileId = fileId.id
          line.fileName = line.name
          line.key = key
        }
      })
      if (parse) {
        list = list.map(line => {
          let { name, fileName, key, fileId } = line
          return { fileName: fileName || name, key, fileId }
        })
      }
      value = (value || []).concat(list)
      onChange && onChange(value)
    })
  }

  // 渲染上传附件
  const renderAttachment = () => {
    return (
      <Upload2QiNiu
        label="请上传附件"
        isEdit={isEidtable}
        isRequire={false}
        onDelete={handleUploadDelete}
        onClick={handleUploadClick}
        onChange={handleUploadChange}
        fileList={preSetValue()}
        maxSize={20}
      />
    )
  }

  // 是否可编辑
  const isEidtable = useMemo(() => {
    return props.eidtable
  }, [props.eidtable])

  return (
    <Fragment>
      <div className={styles['parentBox']}>
        <div className={styles['header']}>
          <div className={styles['header-item']}>
            <span className={styles['txt']}>总还款金额</span>
            {manualRepaymentMoney.foreignSymbol ? (
              <span className={styles['number']}>
                {manualRepaymentMoney.foreignSymbol + manualRepaymentMoney.foreign}
              </span>
            ) : (
              ''
            )}
            <span className={styles['number']}>
              {manualRepaymentMoney.standardSymbol + manualRepaymentMoney.standard}
            </span>
          </div>
          <div className={styles['header-item']}>
            <span className={styles['txt']}>待核销总金额</span>
            {manualRepaymentMoney.foreignSymbol ? (
              <span className={styles['number']}>
                {waitWrittenOffAmount.foreignSymbol + waitWrittenOffAmount.foreign}
              </span>
            ) : (
              ''
            )}
            <span className={styles['number']}>
              {waitWrittenOffAmount.standardSymbol + waitWrittenOffAmount.standard}
            </span>
          </div>
        </div>
        <div className="border-line">
          <div className={styles['vertical-label']}>
            <div className={styles['vertical-title']}>借款</div>
            <div className={styles['vertical-optional']}>*</div>
          </div>
          <div className={styles['vertical-content']} onClick={chooseRepay}>
            {preCheckedItem.length ? preCheckedItem[0].title : <span className="placholder-txt">请选择借款包</span>}
          </div>
        </div>
        <div className="border-line">
          <div className={styles['vertical-label']}>
            <div className={styles['vertical-title']}>还款金额</div>
            <div className={styles['vertical-optional']}>*</div>
          </div>
          <div className={styles['vertical-content']}>
            <div className={styles['custom-input-item']}>
              <div className="custom-input-item-right">{renderAmount('manual')}</div>
            </div>
          </div>
        </div>
        <div className="border-line">
          <div className={styles['vertical-label']}>
            <div className={styles['vertical-title']}>待核销金额</div>
            <div className={styles['vertical-optional']}>*</div>
          </div>
          <div className={styles['vertical-content']}>
            <div className={styles['custom-input-item']}>
              <div className="custom-input-item-right">{renderAmount('writtenOff')}</div>
            </div>
          </div>
        </div>
        <div className="border-line">
          <div className={styles['vertical-label']}>
            <div className={styles['vertical-title']}>收款账户</div>
            <div className={styles['vertical-optional']}>*</div>
          </div>
          <div className={styles['vertical-content']} onClick={chooseCollectionAccount}>
            {Object.keys(collectionAccount).length ? (
              <div className={styles['line0']}>
                <div className="bank-img">
                  <img className="payee-icon" src={bankIcon} />
                  <span>{bankNumber}</span>
                </div>
                <div className="right">{generateBankText()}</div>
              </div>
            ) : (
              <span className="placholder-txt">请选择收款账户</span>
            )}
          </div>
        </div>
        <div className="border-line">
          <div className={styles['vertical-label']}>
            <div className={styles['vertical-title']}>附件</div>
          </div>
          <div className={styles['vertical-content']}>{renderAttachment()}</div>
        </div>
        {isEidtable ? (
          <Button block category="primary" onClick={handleConfirm} className="addRepayBtn">
            确定
          </Button>
        ) : (
          <Button block category="primary" onClick={handleComeBack} className="addRepayBtn">
            返回
          </Button>
        )}
      </div>
    </Fragment>
  )
}

export default ManualrepayModal
