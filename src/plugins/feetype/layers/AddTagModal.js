import React, { PureComponent } from 'react'
import <PERSON>han<PERSON><PERSON><PERSON><PERSON>Hook from '../../../lib/EnhanceTitleHook'
import { Button, Cascader, Space, Tag, Toast } from '@hose/eui-mobile/2x'
import styles from './AddTagModal.module.less'
import {
  getTagsList,
  getTagsByDetailId,
  getBatchTagsByDetailId,
  getBatchList,
  getCommonTag,
  getTagGlobalConfig
} from '../feetype.action'
export const getTagColor = (type, status) => {
  if (status === 0) {
    return 'neu'
  }
  switch (type) {
    case 'AUTO':
      return 'cyan'
    case 'MANUAL':
      return 'pri'
    default:
      return 'pri'
  }
}
@EnhanceTitleHook(props => props.name)
export default class AddTagModal extends PureComponent {
  // api.open('@feetype:AddTagModal')   //调用对话框

  state = {
    visible: false,
    newTag: undefined,
    tags: [],
    options: [],
    tagMap: undefined
  }
  componentDidMount() {
    this.initData()
  }
  initData = async () => {
    const { detailIds, feeTypeIds, specificationIds } = getFetchParams(this.props.data)
    if (!!detailIds.length) {
      const [tagsValue, tagListValue, commonTagValue, tagConfigValue] = await Promise.all([
        getBatchTagsByDetailId({ detailIds }),
        getBatchList({ specificationIds, feeTypeIds }),
        getCommonTag({ specificationIds, feeTypeIds }),
        getTagGlobalConfig()
      ])
      const value = tagsValue?.items || []
      const list = tagListValue?.items || []
      const tagConfig = tagConfigValue?.value || {}
      const displayTagValue = tagConfig.displayTagValue
      const commonTags = (commonTagValue?.items ?? []).map(item => {
        const showLabel = displayTagValue ? item.tagValueId.label : `${item.tagId.name}/${item.tagValueId.label}`
        return {
          ...item,
          showLabel
        }
      })
      const tags = value.map(item => {
        const label = displayTagValue ? item.label : `${item.tagName}/${item.label}`
        return {
          label,
          tagType: item.tagType,
          status: item.status
        }
      })
      const tagMap = {}
      const newList = list.map(item => {
        const { tagValues = [] } = item
        const children = tagValues.map(o => {
          tagMap[o.id] = { ...o, parent: item }
          return { value: o.id, label: o.label }
        })
        return {
          value: item.id,
          label: item.name,
          children: children
        }
      })
      this.setState({ tags, options: newList, tagMap, commonTags })
    }
  }
  handleOnConfirm = value => {
    this.setState({ newTag: value })
  }
  handleOnClick = () => {
    this.setState({ visible: true })
  }
  hanldeOk = () => {
    const { newTag } = this.state
    if (!newTag?.length) {
      Toast.show({
        icon: 'fail',
        content: i18n.get('新标签不能为空')
      })
      return
    }
    if (newTag.length < 2) {
      Toast.show({
        icon: 'fail',
        content: i18n.get('标签选择不完整')
      })
      return
    }
    const { tagMap } = this.state
    const tagValue = tagMap[newTag.pop()]
    this.props.layer.emitOk({
      newTag: {
        tagId: tagValue.parent.id,
        tagName: tagValue.parent.name,
        tagValueId: tagValue.id,
        label: tagValue.label,
        tagType: tagValue.parent.type, //MANUAL：手动， AUTO：自动
        tagRelationType: 'FLOW_DETAIL', //FLOW_DETAIL：费用明细，FLOW:单据
        tagBatch: getBatchParams(this.props.data),
      }
    })
  }
  commandTagClick = item => {
    const newTag = [item.tagId.id, item.tagValueId.id]
    this.setState({ newTag })
  }
  render() {
    const { newTag, options = [], tags = [], commonTags = [] } = this.state
    return (
      <div className={styles['AddTagModal']}>
        <div className="add_tag_modal_content">
          {!!tags?.length && (
            <div className="content_item_wrapper">
              <span className="content_item_label_disable">{i18n.get('已有标签')}</span>
              <Space wrap>
                {tags.map(item => {
                  const color = getTagColor(item?.tagType, item?.status)
                  return <Tag color={color}>{item.label}</Tag>
                })}
              </Space>
            </div>
          )}
          <div className="content_item_wrapper">
            <span className="content_item_label">{i18n.get('添加标签')}</span>
            <Cascader
              options={options}
              title={i18n.get('选择标签')}
              value={newTag}
              visible={this.state.visible}
              onConfirm={this.handleOnConfirm}
              onClose={() => {
                this.setState({ visible: false })
              }}
            >
              {items => {
                if (items.every(item => item === null)) {
                  return (
                    <span className="content_item_placeholder" onClick={this.handleOnClick}>
                      {i18n.get('请选择标签')}
                    </span>
                  )
                }
                return (
                  <span className="content_item_value" onClick={this.handleOnClick}>
                    {items.map(item => item?.label ?? item.value).join('/')}
                  </span>
                )
              }}
            </Cascader>
          </div>
          <div className="command_tag_wrapper">
            {commonTags.map(item => {
              return (
                <Tag
                  style={{ cursor: 'pointer', marginRight: 8 }}
                  key={item?.id}
                  color="neu"
                  onClick={() => this.commandTagClick(item)}
                >
                  {item.showLabel}
                </Tag>
              )
            })}
          </div>
        </div>
        <div className="add_tag_modal_footer">
          <Button size="middle" block onClick={this.hanldeOk}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
const getFetchParams = details => {
  const detailIds = []
  const feeTypeIds = []
  const specificationIds = []
  const flowSpecificationIds = []
  if (!details?.length) return { specificationIds, feeTypeIds, flowSpecificationIds, detailIds }
  details?.forEach(item => {
    const detailId = getId(item?.form?.detailId)
    const feeTypeId = getId(item?.form?.feeTypeId)
    const specificationId = getId(item?.form?.specificationId)
    const flowSpecificationId = getId(item?.form?.flowSpecificationId)
    detailIds.push(detailId)
    feeTypeIds.push(feeTypeId)
    specificationIds.push(specificationId)
    flowSpecificationIds.push(flowSpecificationId)
  })
  return {
    detailIds: detailIds.filter(Boolean),
    feeTypeIds: feeTypeIds.filter(Boolean),
    specificationIds: specificationIds.filter(Boolean),
    flowSpecificationIds: flowSpecificationIds.filter(Boolean)
  }
}

const getBatchParams = details => {
  if(!details?.length) return []
  const data = {}
  details.forEach(item => {
    const detailId = item?.form?.detailId
    const flowId = item?.form?.flow?.id
    const backLogId = item?.nodeState?.backlogId
    const flowCode = item?.form?.flow?.form?.code
    const saveData = data[flowId]
    if (!saveData) {
      data[flowId] = {
          backLogId,
          flowId,
          flowCode,
          flowDetailIds: [detailId]
      }
    } else {
      saveData.flowDetailIds.push(detailId)
    }
  })
  return Object.values(data)
}

const getId = data => {
  return typeof data === 'string' ? data : data?.id
}
