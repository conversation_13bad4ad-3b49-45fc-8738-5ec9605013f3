import React, { PureComponent } from 'react'
import FeetypeInfoContainer from '../parts/FeetypeInfoContainer'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import { app as api } from '@ekuaibao/whispered'
import { setValidateError } from '../feetype.action'
import { getTripEditable } from '../../../lib/util'
import { MoneyMath } from '@ekuaibao/money-math'
import { get, groupBy } from 'lodash'

@EnhanceConnect(
  state => ({
    globalFields: state['@common'].baseDataProperties.data,
    feetypes: state['@common'].feeTypeList,
    travelBackInfo: state['@bill'].travelBackInfo
  }),
  { setValidateError }
)
@EnhanceTitleHook()
export default class FeeTypeInfoModal extends PureComponent {
  componentDidMount() {
    if (this.props.sourcePage !== 'eCardExpense') {
      const { billType } = this.props
      const billTypeMap = {
        requisition: i18n.get('申请明细'),
        expense: i18n.get('费用明细'),
        reconciliation: i18n.get('对账明细'),
        settlement: i18n.get('结算明细')
      }
      const title = billTypeMap[billType] || i18n.get('费用明细')
      api.invokeService('@layout:set:header:title', title)
    }
  }

  fnLinkDetailEntitiesValue = value => {
    if (!value) {
      return value
    }
    const { detailId, linkDetailEntities } = value
    const map = {}
    if (!linkDetailEntities) return value
    const dataList = linkDetailEntities.find(item => item?.dataList)
    if (dataList) return value
    const entities = groupBy(linkDetailEntities, 'linkDetailEntityId.linkId')
    const list = Object.keys(entities).map(flowId => {
      const dataList = entities[flowId].map(item => {
        const { linkDetailEntityId, amount } = item
        const { id, unwrittenOffAmount } = linkDetailEntityId
        return {
          ...linkDetailEntityId,
          unwrittenOffAmount: new MoneyMath(unwrittenOffAmount).add(map[id] ?? 0).value,
          modifyValue: amount,
          _tempConsumId: detailId
        }
      })
      return { flowId, dataList }
    })
    value.linkDetailEntities = list
    return value
  }
  closeAndReturnValue = data => {
    this.props.layer.emitOk(data)
  }

  render() {
    // feetype 的值是由调用者传递过来的

    let {
      billType,
      feetypes,
      feetype,
      globalFields,
      value,
      isEdit,
      billTemplate,
      template,
      bus,
      risks = [],
      submitterId,
      ds,
      idx,
      flowAllowModifyFields,
      flowId,
      enterType,
      billBus,
      setValidateError,
      invoiceRiskData,
      ...others
    } = this.props
    value = this.fnLinkDetailEntitiesValue(value)
    let hasOrdered = false
    // 待提交单据重新判断行程是否可编辑
    // const trip = get(value, `['u_行程规划'][0]`)
    // if (isEdit && trip) {
    //   hasOrdered = !getTripEditable(trip, this.props.travelBackInfo)
    // }

    return (
      <FeetypeInfoContainer
        {...others}
        ds={ds}
        flowId={flowId}
        submitterId={submitterId}
        setValidateError={setValidateError}
        globalFields={globalFields}
        feetypes={feetypes}
        billType={billType}
        feetype={feetype}
        template={template}
        value={value}
        isEdit={isEdit}
        closeAndReturnValue={this.closeAndReturnValue}
        billTemplate={billTemplate}
        risks={risks}
        bus={bus}
        idx={idx}
        flowAllowModifyFields={flowAllowModifyFields}
        invoiceRiskData={invoiceRiskData}
        enterType={enterType}
        billBus={billBus}
      />
    )
  }
}
