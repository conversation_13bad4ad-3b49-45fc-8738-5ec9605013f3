import { Resource } from '@ekuaibao/fetch'
import key from './key'
const feeTypes = new Resource('/api/v1/form/feeTypes')
const calculateField = new Resource('/api/form/v2')
const rule = new Resource('/api/rpc/v1/rule')
const specifications = new Resource('/api/form/v1/specificationVersions')
const recommendAction = new Resource('/api/v2/recommend')
const requisitionInfo = new Resource('/api/form/v3/requisition/info')
const setting = new Resource('/api/mall/v1/officialCard')
const payment = new Resource('/api/flow/v2/referables')
const quickExpenseConfig = new Resource('/api/v1/quickExpenseConfig')
const tags = new Resource('/api/v1/flow/tag')

export function getFeetypeList() {
  return {
    type: key.GET_FEETYPE_LIST,
    payload: feeTypes.GET('/tree/active')
  }
}

const joinReferenceData = () =>
  'components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform?join=icon.fileId,fileId,/v1/attachment/attachments'

const joinAssignmentRule = () => 'components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping'

export function getFeeTypeById(id) {
  const join = {
    join: 'specificationId,specification,/form/v1/specificationVersions',
    join$1: `expenseSpecificationId,expenseSpecification,/form/v1/specificationVersions?join=${joinReferenceData()}&join=${joinAssignmentRule()}`,
    join$2: `requisitionSpecificationId,requisitionSpecification,/form/v1/specificationVersions?join=${joinReferenceData()}&join=${joinAssignmentRule()}`,
    join$3: `receiptSpecificationId,receiptSpecification,/form/v1/specificationVersions?join=${joinReferenceData()}&join=${joinAssignmentRule()}`
  }
  return feeTypes.GET('/[id]', { id, ...join })
}

export function getSpecificationById(id) {
  return specifications.GET('/$id', { id })
}

export function getAutoCalResult(params) {
  return {
    type: key.GET_AUTO_CAL_RESULT,
    payload: rule.POST('/calculate', params)
  }
}

export function getCalculateField(params) {
  return {
    type: key.GET_CALCULATE_FIELD,
    payload: calculateField.GET('/calculateFields/staffId/$submitterId/specId/$specificationId', { ...params })
  }
}

export function getRecommendFeeType(param) {
  return recommendAction.POST('/feeType', param)
}

export function getRequisitionInfoDetails(param) {
  const { ids } = param
  const join = {
    join:
      'dataList.feeTypeId,feeTypeId,/v1/form/feeTypes?join=expenseSpecificationId,expenseSpecificationId,/form/v1/specificationVersions&join=requisitionSpecificationId,requisitionSpecificationId,/form/v1/specificationVersions',
    join$1: `specificationId,specificationId,/form/v1/specificationVersions`,
    join$2: `ownerId,ownerId,/v1/organization/staffs`,
    join$5: 'dataList.attachments.fileId,fileId,/v1/attachment/attachments'
  }
  return requisitionInfo.POST('/details', { ids }, { ...join })
}

/**
 * Copy At @bill.action
 */
export function setValidateError(param) {
  return {
    type: `@bill/VALIDATE_ERROR`,
    payload: param
  }
}
export function getFeeTypeTemplateById(id) {
  let data = {
    id: id,
    join: `components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`,
    join$1: `components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`
  }
  return specifications.GET('/$id', data)
}

// 获取公务卡设置信息
export function getOfficialCardSetting(params) {
  return setting.GET('/config')
}

// 获取启动的结算方式
export function getPayments() {
  return payment.GET('/$name', { name: 'basedata.Settlement', withVisibility: true })
}

// 根据企业id获取预置单据模版id
export function getPresetTemplate(data) {
  return quickExpenseConfig.GET('/getPresetTemplate')
}
//获取费用明细上面已添加的标签
export function getTagsByDetailId(detailsIds) {
  return tags.GET(`/byDetailId/[detailsIds]`, { detailsIds })
}
/**
 * 获取批量明细已有标签值（取交集）
 * @param params
 * @returns {Promise<any>}
 */
export async function getBatchTagsByDetailId(params) {
  return tags.POST('/batch/byDetailIds', params)
}
/**
 * 获取批量打标签的候选值
 * @param params: {specificationIds: string[], feeTypeIds: string[]}
 * @returns {Promise<any>}
 */
export async function getBatchList(params) {
  return tags.POST(`/batch`, params)
}
/**
 * 获取常用标签值
 * @param params: {specificationIds: string[], feeTypeIds: string[]}
 * @returns {Promise<void>}
 */
export async function getCommonTag(params) {
  const qs = {
    join: 'tagId,tagId,/v1/flow/tag/batch',
    join$1: 'tagValueId,tagValueId,/v1/flow/tagValue'
  }
  return tags.POST(`/batch/userCommon`, params, qs)
}
// 获取标签全局配置
export const getTagGlobalConfig = async () => {
  return tags.POST('/config')
}
//获取当前费用明细上面可添加的标签列表
export function getTagsList(specificationId, feeTypeId) {
  return tags.GET(`/$specificationId/$feeTypeId`, {
    specificationId: encodeURIComponent(specificationId),
    feeTypeId: encodeURIComponent(feeTypeId)
  })
}
//手动打标签
export function addTag(data) {
  return tags.POST(`/relatedDetail`, data)
}

/**
 * 批量打标签
 * @param data
 * @returns {Promise<any>}
 */
export function batchAddTag(data) {
  return tags.POST('/batch/add', data)
}
