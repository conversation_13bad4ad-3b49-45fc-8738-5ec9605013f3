import loadable from '@loadable/component'
import * as actions from './feetype.action'

export default [
  {
    id: '@feetype',
    reducer: () => require('./feetype.reducer').default,

    getSpecificationById: async id => {
      return actions.getSpecificationById(id)
    },
    'get:feetype': async (id, isMulti = false) => {
      if (id && typeof id === 'object') {
        return Promise.resolve(id)
      }
      return actions.getFeeTypeById(id).then(data => {
        if (data.items && data.items.length) {
          return isMulti ? data.items : data.items[0]
        }
      })
    },
    'get:feetype:by:ids': async ids => {
      return actions.getFeeTypeById(ids)
    },
    'get:feeType:recommend': async param => {
      return actions.getRecommendFeeType(param)
    },
    'get:requisitioninfo:details': async param => {
      return actions.getRequisitionInfoDetails(param)
    }
  },
  {
    point: '@@layers',
    prefix: '@feetype',
    onload: () => require('./layers').default
  },
  {
    path: '/flow_detail/:flowId/:detailId/:isMine',
    ref: '/',
    onload: () => import('./FeeTypeInfoPage')
  },
  {
    path: '/with-note-detail/:id/:isEdit/:hasError',
    ref: '/',
    onload: () => import('./FeeTypeInfoDetail')
  },
  {
    resource: '@feetype',
    value: {
      ['SelectFeetype.Item']: loadable(() => import('./parts/SelectFeetype.Item')),
      ['SelectFeetype.RecommendFeeType']: loadable(() => import('./parts/SelectFeetype.RecommendFeeType')),
      ['parts/FeeTypeBase']: require('./parts/FeeTypeBase').default
    }
  }
]
