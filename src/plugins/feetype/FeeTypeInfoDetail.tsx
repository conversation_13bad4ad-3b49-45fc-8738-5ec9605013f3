import React from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import { app } from '@ekuaibao/whispered'
import { MeIF } from '@ekuaibao/ekuaibao_types'
import { FeeTypeIF, GlobalFieldIF, ComponentIF } from '@ekuaibao/ekuaibao_types'
import FeetypeInfoContainer from './parts/FeetypeInfoContainer'
import MessageCenter from '@ekuaibao/messagecenter'
import { hideLoading, showLoading, toast } from '../../lib/util'
const SkeletonListEUI = app.require<any>('@home5/SkeletonList')
const EmptyWidget = app.require<any>('@home5/EmptyWidget')
import actions from '../common/common.action'
interface URLParams {
  id: string
  isEdit: boolean
  hasError: string
}

interface Props {
  params: URLParams
  userInfo: MeIF
  feeTypes: FeeTypeIF
  globalFields: GlobalFieldIF
  billType?: string
}

interface State {
  feeTypeInfo?: undefined | FeeTypeIF
}

@EnhanceConnect((state: any) => ({
  globalFields: state['@common'].baseDataProperties.data,
  feeTypes: state['@common'].feeTypeList,
  userInfo: state['@common'].me_info
}))
@((EnhanceTitleHook as any)(i18n.get('消费详情')))
export default class FeeTypeInfoDetail extends React.Component<Props, State> {
  // @ts-ignore
  state = { feeTypeInfo: undefined }
  private bus = new MessageCenter()
  async componentDidMount() {
    const { params } = this.props
    const standardCurrency = app.getState()['@common'].standardCurrency
    if (!Object.keys(standardCurrency || {}).length) {
      app.dispatch(actions.getStandardCurrency())
    }
    if (params.hasError === 'false' || !params.hasError) {
      const { value } = await app.invokeService('@record-expends:get:withNode:detail:by:id', {
        id: params.id
      })
      if (!value) {
        return
      }
      if (value?.form?.invoiceForm) {
        const { feeTypeForm } = await app.invokeService('@record-expends:get:withNode:detail:invoice:by:id', {
          feeTypeForm: value.form
        })
        value.form.invoiceForm = feeTypeForm?.invoiceForm
      }
      this.setState({ feeTypeInfo: value })
    }
  }

  handleSave = async (data: any) => {
    showLoading()
    const { feeTypeInfo } = this.state
    const formatData = app.invokeService('@record-expends:format:withNode:data', [data], {
      stage: feeTypeInfo.form.stage
    })
    await app
      .invokeService('@record-expends:save:withNode', formatData, {
        id: feeTypeInfo.id
      })
      .then(() => {
        hideLoading()
        app.go(-1)
      })
      .catch((error: any) => {
        hideLoading()
        toast.error(error?.message)
      })
  }

  render() {
    const { globalFields, feeTypes, userInfo, params, billType = 'expense' } = this.props
    const { feeTypeInfo } = this.state
    if (params.hasError === 'true') {
      return <EmptyWidget type={'noCentent'} tips={window.__WITHNOTE_ERROR} />
    }
    if (!feeTypeInfo) {
      return <SkeletonListEUI />
    }
    return (
      <FeetypeInfoContainer
        globalFields={globalFields}
        feetypes={feeTypes}
        feetype={feeTypeInfo.form.feeTypeId}
        value={{ ...feeTypeInfo.form, systemGenerationDetail: true }}
        isEdit={params.isEdit}
        risks={[]}
        ds={[feeTypeInfo.form]}
        template={feeTypeInfo.form.specificationId}
        idx={0}
        billType={billType}
        billForm={feeTypeInfo.form}
        hideActionsPart={true}
        bus={this.bus}
        needAutoCal={false}
        sourcePage="recordExpends"
        billSpecification={{ components: [], type: feeTypeInfo.form.stage, configs: [] }}
        formData={{ submitterId: userInfo.staff }}
        submitterId={userInfo.staff}
        cannotDelete={true}
        showDraftBtn={false}
        closeAndReturnValue={this.handleSave}
        canEditFieldWhiteList={['invoiceForm']}
        validateLevel={0}
      />
    )
  }
}
