import React, { useState, useEffect } from 'react'
import styles from './SelectDropDown.module.less'
import SVG_DOWN_BOLDER from '../images/bill-down-bolder.svg'
import classNames from 'classnames'
import { Checkbox } from 'antd-mobile'

const SelectDropDown = (props: any) => {
  const [popupShow, setPopupShow] = useState(false)
  const [checkedItem, setCheckedItem] = useState({})
  const { filterSource = [], onChange } = props

  useEffect(() => {
    setCheckedItem(filterSource && filterSource[0])
  }, [filterSource])

  const onCheckboxClick = (selectKey: any) => {
    setCheckedItem(selectKey)
    onChange && onChange(selectKey)
    setPopupShow(false)
  }

  const getPopUpView = () => {
    return (
      <div className="mask" onClick={() => setPopupShow(false)}>
        <div className="wrapper">
          {filterSource.map((key: any) => {
            const checked = checkedItem?.id === key?.id
            return (
              <Checkbox
                key={key?.id}
                className={classNames({ selected: checked })}
                onChange={() => { onCheckboxClick(key) }}
                checked={checked}
              >
                {key?.name}
              </Checkbox>
            )
          })}
        </div>
      </div>
    )
  }

  return (
    <div className={styles['filter-wrapper']}>
      <div className="datalink-type" onClick={() => setPopupShow(true)}>
        {checkedItem?.name}
        <img src={SVG_DOWN_BOLDER} />
      </div>
      {popupShow && getPopUpView()}
    </div>
  )
}

export default SelectDropDown