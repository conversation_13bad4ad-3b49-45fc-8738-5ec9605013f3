@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.filter-wrapper {
  :global {
    .datalink-type {
      font-size: 40px;
      height: 100px;
      align-items: center;
      line-height: 100px;
      padding-left: 32px;
      margin-top: 16px;
      img {
        padding-left: 4px;
      }
    }
    .mask {
      position: absolute;
      background-color: rgba(29, 43, 61, 0.25);
      width: 100%;
      height: 100%;
      z-index: 1000;
      .wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        max-height: 912px;
        background-color: @color-white-1;
        padding: 0 32px 48px;
        justify-content: space-between;
        .am-checkbox {
          width: 100%;
          height: 100%;
          position: absolute;
          left: 0;
          display: flex;
          align-items: inherit;
        }
        .am-checkbox-wrapper {
          position: relative;
          flex-direction: row;
          width: calc(50% - 64px);
          height: 80px;
          .font-size-3;
          margin: 16px;
          display: flex;
          justify-content: space-between;
          border: rgba(29, 43, 61, 0.15) 2px solid;
          align-items: center;
          padding: 0 32px;
          border-radius: 40px;
          &.selected {
            border-color: @color-black-1;
            background-color: rgba(29, 43, 61, 0.03);
          }
          .am-checkbox-inner {
            right: 20px;
            border: none;
            border-radius: unset;
            background-color: unset;
            &:after {
              border-color: @color-brand;
            }
          }
        }
      }
    }
  }
}
