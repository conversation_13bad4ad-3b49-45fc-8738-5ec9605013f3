import { app } from '@ekuaibao/whispered'
import React, { FC, useState, useEffect, useRef } from 'react'
import { getFlowStage } from '../../bill.action'
import styles from './CalculateBillState.module.less'
type FlowState =
  | 'draft'
  | 'approving'
  | 'sending'
  | 'receiving'
  | 'receivingExcep'
  | 'pending'
  | 'paying'
  | 'withdraw'
  | 'paid'
  | 'archived'
  | 'rejected'
  | 'nullify'

type CheckState = 'PENDING' | 'PROCESSED' | null

type IProps = {
  flowId: string
  type: string
  billState: FlowState
  skipCheck: boolean
}

type IState = {
  flowState?: FlowState
  budgetState?: CheckState
  costControlState?: CheckState
  dataLinkLedgerState?: CheckState
}

const CalculateBillState: FC<IProps> = props => {
  const { flowId, type, billState, skipCheck } = props
  const [billStatusResult, setBillStatusResult] = useState<IState>({})
  const [fetchCount, setFetchCount] = useState(0)
  const fetchData = async () => {
    const { value } = await getFlowStage(flowId)
    setBillStatusResult(value)
  }

  const showCondition = () =>
    (billStatusResult?.flowState === 'pending' || billStatusResult?.flowState === undefined) &&
    (billStatusResult?.budgetState === 'PENDING' ||
      billStatusResult?.costControlState === 'PENDING' ||
      billStatusResult?.dataLinkLedgerState === 'PENDING' ||
      billStatusResult?.budgetState === undefined ||
      billStatusResult?.costControlState === undefined ||
      billStatusResult?.dataLinkLedgerState === undefined) &&
    fetchCount < 5 &&
    billState === 'pending'

  const calculateDelay = () => {
    if (showCondition()) {
      return 2000
    }
    return null
  }

  useInterval(() => {
    if (skipCheck) {
      fetchData()
      setFetchCount(fetchCount + 1)
    }
  }, calculateDelay())

  useEffect(() => {
    if (skipCheck) {
      if (
        billState === 'draft' ||
        billState === 'rejected' ||
        (billStatusResult?.flowState !== undefined && billStatusResult?.flowState !== 'pending')
      ) {
        setTimeout(() => {
          app.go(-1)
          app.invokeService(
            '@home:click:bill',
            { id: flowId, state: billStatusResult?.flowState || billState, formType: type },
            'homePage'
          )
        }, 2000)
      }
    }
  }, [])
  if (!showCondition() || !skipCheck) return null
  return (
    <div style={{ padding: '0 16px' }}>
      <div className={styles.calculateBillStateContainer}>
        <div className={styles.iconWrapper}>
          <svg className="icon" aria-hidden="true">
            <use xlinkHref="#ico-7-icon_tips" />
          </svg>
        </div>
        <div className={styles.tipsWrapper}>
          <div className={styles.t1}>{i18n.get('正在智能校验预算、费标、台账中...')}</div>
          <div className={styles.t2}>{i18n.get('智能校验通过后，单据将进行审批')}</div>
        </div>
      </div>
    </div>
  )
}

export default CalculateBillState

const useInterval = (callback: () => void, delay: number | null) => {
  const fnCallback = useRef<() => void>()
  useEffect(() => {
    fnCallback.current = callback
  })
  useEffect(() => {
    const tick = () => {
      fnCallback.current()
    }
    if (delay !== null) {
      // 执行callback回调
      const callbackId = setInterval(tick, delay)
      return () => clearInterval(callbackId)
    } else {
      return () => {}
    }
  }, [delay])
}
