import React, { Component } from 'react'
import styles from './BillPayPlan.module.less'
import { Fetch } from '@ekuaibao/fetch'
import { payPlanStatusMap } from '../../../../lib/enums'
import { app, app as api } from '@ekuaibao/whispered'
import { getReceiptFileModalList } from './fetchUtil'
const Money = app.require<any>('@elements/puppet/Money')

interface Props {
  bus: any
  value: any
  billState: string
  payPlans: any[]
  multiplePayeesMode: boolean
  inPartialPayState: boolean
  KA_REEXCHANGE_PROCESSING: boolean
}

interface State {
  payPlanList: any[]
  isHaveRecipt: boolean
}

export default class BillPayPlanReadonly extends Component<Props, State> {
  state: State = {
    payPlanList: [],
    isHaveRecipt: false
  }

  componentDidMount() {
    this.fnSetPayPlanList(this.props)
  }

  componentWillReceiveProps(nextProps: Props) {
    if (this.props.payPlans !== nextProps.payPlans) {
      this.fnSetPayPlanList(nextProps)
    }
  }

  fnSetPayPlanList = (props: Props) => {
    const { payPlans, billState, value, multiplePayeesMode } = props
    if (!payPlans?.length) return
    const payPlanList = payPlans.map(payPlan => {
      const { dataLinkForm, dataLinkId, receiptId } = payPlan
      return {
        amount: dataLinkForm?.E_system_支付计划_支付金额 ?? null,
        status: dataLinkForm?.E_system_支付计划_支付状态 ?? null,
        payeeId: multiplePayeesMode ? dataLinkForm?.E_system_支付计划_收款信息 : value.payeeId,
        id: dataLinkId,
        receiptId: receiptId,
        dataLinkId: dataLinkId,
        E_system_支付计划_legalEntity: dataLinkForm?.legalEntity,
        isShowAPInfo: payPlan?.isShowAPInfo,
        payableCurrencyStandard: dataLinkForm?.E_system_支付计划_应付币种金额,
        paidAmount: dataLinkForm?.E_system_支付计划_实付币种金额,
        paidOffLineFinishedTime: dataLinkForm?.E_system_支付计划_实际支付时间,
        paidRate: dataLinkForm?.E_system_支付计划_实付币种汇率
      }
    })
    // 待支付部分完成显示‘回单文件’列
    let isHaveRecipt = false
    if (billState === 'paying') {
      isHaveRecipt = !!payPlans.find(el => el?.dataLinkForm.E_system_支付计划_支付状态 === '支付成功')
    }
    this.setState({ payPlanList, isHaveRecipt })
  }

  handleItemClick = async (payPlan: any, index: number) => {
    const { payPlanList, isHaveRecipt } = this.state
    const { inPartialPayState, billState, value, KA_REEXCHANGE_PROCESSING } = this.props
    const { receiptId, dataLinkId } = payPlan
    const { paymentChannel } = value
    const params: any = {
      inReadonly: true,
      inPartialPayState,
      payPlanList,
      payPlan,
      index,
      isHaveRecipt,
      billState
    }
    // KA_REEXCHANGE_PROCESSING:  KA-退汇处理
    if (KA_REEXCHANGE_PROCESSING && receiptId?.length > 1) {
      const param = {
        limit: { count: 100, start: 0 }
      }
      const res = await getReceiptFileModalList(param, receiptId)
      const receiptList = res.items?.data?.map((item: any, index: number) => {
        return { key: index + 1, ...item }
      })
      params.receiptList = receiptList
    } else if (receiptId?.length > 0) {
      let receiptURL
      const paymentChannelIsNSTC = paymentChannel === 'NSTC'
      if (paymentChannelIsNSTC) {
        receiptURL = await Fetch.GET(`/api/pay/v1/receipt/findById/${receiptId[0]}`)
      } else {
        receiptURL = await Fetch.POST(`/api/pay/v3/receipt/getFileUrl/[${receiptId}]/${dataLinkId}`)
      }
      params.paymentChannelIsNSTC = paymentChannelIsNSTC
      params.receiptURL = receiptURL
    }
    api.open('@bill:AddPayPlanModal', params)
  }

  render() {
    const { multiplePayeesMode, inPartialPayState, billState, value } = this.props
    const { payPlanList, isHaveRecipt } = this.state
    // 所有支付计划中的某条有带法人实体字段时，展示法人实体字段
    const haveLegalEntity = value?.paymentPlanByApportion && payPlanList.some(el => el.E_system_支付计划_legalEntity)
    // 单收款人只在支付完成后 才会显示支付计划表格
    let singlePaidMode = false
    if (!multiplePayeesMode) {
      const status = ['paid', 'archived']
      if (!(status.indexOf(billState) > -1)) {
        singlePaidMode = true
      }
    }
    if (singlePaidMode) return null
    if (!payPlanList.length) return null

    const title =
      (isHaveRecipt || billState === 'paid' || billState === 'archived'
        ? i18n.get('支付计划/记录')
        : i18n.get('支付计划')) + `（${payPlanList.length}）`

    return (
      <div className={styles['billPayPlan-wrap']}>
        <div className="billPayPlan-title">{title}</div>
        <div className={styles['billPayPlan-readonly']}>
          {payPlanList.map((plan, index) => (
            <div key={index} className="billPayPlan-readonly-item" onClick={() => this.handleItemClick(plan, index)}>
              <div className="name">{plan.payeeId?.name || '无'}</div>
              {haveLegalEntity && (
                <div className="legalEntityName">{plan.E_system_支付计划_legalEntity?.name || '-'}</div>
              )}
              <Money className="billPayPlanContentItem-content-money" value={plan.amount} />
              {inPartialPayState && (
                <div className="billPayPlan-readonly-item-status">
                  {plan.status && (
                    <>
                      <div
                        className={'billPayPlan-readonly-item-status-block ' + (payPlanStatusMap as any)[plan.status]}
                      />
                      <span>{i18n.get(plan.status)}</span>
                    </>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    )
  }
}
