/**************************************
 * Created By LinK On 2022/1/6 17:33.
 **************************************/
import { Resource } from '@ekuaibao/fetch';
import { QuerySelect } from 'ekbc-query-builder'
const receipt = new Resource('/api/pay/v1/receipt')

// 获取回单文件弹窗列表内容
export function getReceiptFileModalList(params: any, ids: string[]) {
  const query = new QuerySelect().limit(params.limit.start, params.limit.count)
  const str = ids.map((id: string) => {
    return `\"${id}"`
  })
  if (ids.length) {
    query.filterBy(`id.in(${str})`)
  }
  return receipt.POST('/search', query.value())
}