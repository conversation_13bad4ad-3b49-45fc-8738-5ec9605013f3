import styles from './BillPayPlan.module.less'
import React from 'react'
import BillPayPlanContentItem, { Props } from './BillPayPlanContentItem'
import { app as api } from '@ekuaibao/whispered'
const EmptyWidget = api.require<any>('@home5/EmptyWidget')

export default function BillPayPlanContent(props: Props) {
  const { payPlanList = [], ...others } = props

  if (!payPlanList.length) {
    return <BillPayPlanContentNull />
  }

  return (
    <div className="billPayPlan-content">
      {payPlanList.map((payPlan, index) => (
        <BillPayPlanContentItem {...others} key={payPlan.id} index={index} payPlan={payPlan} />
      ))}
    </div>
  )
}

function BillPayPlanContentNull() {
  return (
    <div className={styles['billPayPlan-content-null']}>
      <EmptyWidget size={100} />
    </div>
  )
}
