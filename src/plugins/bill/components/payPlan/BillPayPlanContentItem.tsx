import styles from './BillPayPlanContentItem.module.less'
import React from 'react'
import { Checkbox, SwipeAction } from 'antd-mobile'
import { OnChangeParams } from 'antd-mobile/lib/checkbox/PropsType'
import { app } from '@ekuaibao/whispered'
import { getV } from "@ekuaibao/lib/lib/help";
const Money = app.require<any>('@elements/puppet/Money')

export interface Props {
  index?: number
  payPlan?: any
  payPlanList?: any[]
  selectPayPlan: any[]
  canEditPayPlan: boolean
  currency?: any
  handleCheckBox: (checked: boolean, id: string) => void
  handleItemClick: (payPlan: any, index: number) => void
  handleItemDelete: (index: number) => void
}

export default function BillPayPlanContentItem(props: Props) {
  const {
    index,
    payPlan,
    selectPayPlan,
    canEditPayPlan,
    currency,
    handleCheckBox,
    handleItemClick,
    handleItemDelete,
  } = props
  const { payeeId, amount, id } = payPlan
  if (!payeeId) return null

  const { accountName, name } = payeeId
  const style = index === 0 ? { borderTop: 'none' } : {}
  const checked = !!~selectPayPlan.indexOf(id)
  const symbol = currency?.symbol
  const legalEntityName = getV(payPlan, 'E_system_支付计划_legalEntity.name')

  const rightActions = [
    {
      text: i18n.get('删除'),
      onPress: () => handleItemDelete(index),
      style: { backgroundColor: '#F4333C', color: 'white', fontSize: 16, width: 96 }
    }
  ]

  const handleChange = (e: OnChangeParams) => {
    handleCheckBox(e.target.checked ?? false, id)
  }

  const handleClick = () => {
    handleItemClick(payPlan, index)
  }

  return (
    <SwipeAction
      className={styles['billPayPlanContentItem-wrap']}
      disabled={!canEditPayPlan}
      right={rightActions}
      autoClose
    >
      <div className="billPayPlanContentItem" style={style}>
        <Checkbox onChange={handleChange} checked={checked} disabled={!canEditPayPlan} />
        <div className="billPayPlanContentItem-content" onClick={handleClick}>
          <div className="billPayPlanContentItem-content-label">{accountName || name}</div>
          {legalEntityName && (<div className="billPayPlanContentItem-content-label">{legalEntityName}</div>)}
          <div className="billPayPlanContentItem-content-money">
            <span>{i18n.get('支付金额：')}</span>
            <Money style={{ display: 'inline-block' }} value={amount} currencySymbol={symbol} />
          </div>
        </div>
      </div>
    </SwipeAction>
  )
}
