import Big from 'big.js'
import get from 'lodash/get'

//计算当前支付计划列表中的金额总和，包含没有带收款信息的支付计划数据
export function getCurrentMoney(payPlanList: any[] = []) {
  let currentMoney = 0
  payPlanList.forEach(payPlan => {
    const standardScale = get(payPlan, 'amount.standardScale', 2)
    const a = new Big(currentMoney)
    currentMoney = Number(new Big(payPlan.amount.standard).plus(a).toFixed(Number(standardScale)))
  })
  return currentMoney
}

//计算当前支付计划列表中的金额总和
export function getCurrentMoneyWithoutNoPayee(payPlanList: any[] = []) {
  let currentMoney = 0
  payPlanList.forEach(payPlan => {
    if (!payPlan.payeeId) return
    const standardScale = get(payPlan, 'amount.standardScale', 2)
    const a = new Big(currentMoney)
    currentMoney = Number(new Big(payPlan.amount.standard).plus(a).toFixed(Number(standardScale)))
  })
  return currentMoney
}

//处理数字精度问题
export function formatNumber(num: number, digit: number) {
  const m: number = Math.pow(10, digit)
  return parseInt(String(num * m), 10) / m
}
