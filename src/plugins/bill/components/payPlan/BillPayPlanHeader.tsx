import styles from './BillPayPlan.module.less'
import React from 'react'
import { app } from '@ekuaibao/whispered'
const Money = app.require<any>('@elements/puppet/Money')

interface Props {
  summaryMoney: any
  currentMoney: number
  hasValidation?: boolean
  hasPayPlan?: boolean
  currency?: any
  inPaymentPlanByApportionMode?: boolean
}

export default function BillPayPlanHeader(props: Props) {
  const { summaryMoney, currentMoney, hasValidation, hasPayPlan, currency, inPaymentPlanByApportionMode } = props
  const style = currentMoney === 0 || !hasValidation ? {} : { backgroundColor: 'rgba(244, 82, 107, 0.1)' }

  let tip
  if (hasValidation) {
    if (!hasPayPlan) {
      tip = i18n.get('请添加支付计划')
    } else if (currentMoney > 0) {
      tip = i18n.get('已计划支付金额 < 总支付金额，请修改')
    } else if (currentMoney < 0) {
      if (inPaymentPlanByApportionMode){
        tip = i18n.get('已计划支付金额>总支付金额，请将收款方式调整至「按金额」后修改')
      } else {
        tip = i18n.get('已计划支付金额 > 总支付金额，请修改')
      }
    }
  }

  return (
    <>
      <div className={styles['billPayPlan-header'] + ' billPayPlan-header-forfix'} style={style}>
        <BillPayPlanHeaderItem label={i18n.get('支付金额总计')} summaryMoney={summaryMoney} currency={currency} />
        <BillPayPlanHeaderItem label={i18n.get('未计划金额')} summaryMoney={currentMoney} currency={currency} />
      </div>
      {tip && (
        <div className={styles['billPayPlan-tip']}>
          <svg className="icon warn-icon" aria-hidden="true">
            <use xlinkHref="#EDico-close-circle" />
          </svg>
          {tip}
        </div>
      )}
    </>
  )
}

interface ItemProps {
  summaryMoney: any
  label: string
  currency: any
}

function BillPayPlanHeaderItem(props: ItemProps) {
  const { summaryMoney, label, currency } = props
  const symbol = currency?.symbol
  return (
    <div className="billPayPlan-header-item">
      <div className="billPayPlan-header-item-label">{label}</div>
      <Money className="billPayPlan-header-item-value" value={summaryMoney} currencySymbol={symbol} />
    </div>
  )
}
