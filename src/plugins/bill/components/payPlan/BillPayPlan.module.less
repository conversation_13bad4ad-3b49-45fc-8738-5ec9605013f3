@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.billPayPlan-wrap {
  margin-top: 16px;
  background-color: var(--eui-bg-body);
  :global {
    .billPayPlan-title {
      padding: 0 32px;
      font: var(--eui-font-body-r1);
      color: var(--eui-text-caption);
      height: 92px;
      line-height: 92px;
    }
    .billPayPlan-content {
      border-top: 2px solid var(--eui-line-divider-default);
      border-bottom: 2px solid var(--eui-line-divider-default);
    }
    .billPayPlan-switch {
      height: 96px;
      margin: 0 @space-6;
      color: @color-black-2;
      border-bottom: 2px solid var(--eui-line-divider-default);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .billPayPlan-batch-action {
      height: 100px;
      display: flex;
      align-items: center;
      padding: 0 @space-6;
      border-bottom: 2px solid var(--eui-line-divider-default);
      &.billPayPlan-batch-action-active {
        .billPayPlan-batch-action-btn {
          color: @color-brand-2;
        }
      }
      .billPayPlan-batch-action-btn {
        flex: 1;
        height: 100px;
        line-height: 100px;
        text-align: center;
        color: @color-black-4;
        user-select: none;
        transition: color ease 0.4s;
      }
    }
    .billPayPlan-button-wrap {
      position: relative;
      padding: @space-6;
      border-bottom: 2px solid var(--eui-line-divider-default);
    }
  }
}

.billPayPlan-content-null {
  border-top: 2px solid var(--eui-line-divider-default);
  border-bottom: 2px solid var(--eui-line-divider-default);
}

.billPayPlan-header {
  .font-size-2;
  margin: 0 @space-6 @space-6;
  color: @color-black-2;
  height: 144px;
  background-color: @color-bg-2;
  border-radius: 8px;
  padding: @space-4 0;
  display: flex;
  justify-content: space-around;
  transition: background-color ease 0.2s;
  align-items: center;
  :global {
    .billPayPlan-header-item-value {
      .font-weight-3;
    }
  }
}

.billPayPlan-readonly {
  :global {
    .billPayPlan-readonly-item {
      // height: 92px;
      padding: @space-5 @space-6;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 2px solid var(--eui-line-divider-default);
      .billPayPlan-readonly-item-status,
      .billPayPlanContentItem-content-money,
      .name {
        flex: 1;
      }
      .billPayPlanContentItem-content-money {
        text-align: right;
      }
      .billPayPlan-readonly-item-status {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        .billPayPlan-readonly-item-status-block {
          display: inline-block;
          width: 8px;
          height: 24px;
          border-radius: 8px;
          margin-right: @space-4;
          margin-left: @space-6;
          &.orange {
            background-color: @color-warning;
          }
          &.red {
            background-color: @color-error;
          }
          &.blue {
            background-color: @color-inform;
          }
          &.green {
            background-color: @color-success;
          }
        }
      }
      .name {
        position: relative;
        padding-left: @space-5;
        text-align: left;
        color: @color-black-1;
        &::before {
          content: '';
          width: 12px;
          height: 12px;
          position: absolute;
          background-color: @color-brand-2;
          top: 14px;
          left: 0;
        }
      }
      .legalEntityName {
        width: 140px;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: @color-black-1;
      }
    }
  }
}

.billPayPlan-tip {
  .font-size-2;
  line-height: 44px;
  color: @color-error-2;
  margin: 0 @space-6 @space-4;
  :global {
    .icon {
      margin-right: @space-4;
    }
  }
}
