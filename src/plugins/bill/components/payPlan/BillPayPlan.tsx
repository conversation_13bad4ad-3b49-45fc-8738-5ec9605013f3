import styles from './BillPayPlan.module.less'
import React, { PureComponent } from 'react'
import classnames from 'classnames'
import BillPayPlanHeader from './BillPayPlanHeader'
import BillPayPlanContent from './BillPayPlanContent'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { getCurrentMoneyWithoutNoPayee } from './payPlanUtil'
import { standardValueMoney } from '../../../../lib/InvoiceMappingValue'
import { MoneyMath } from '@ekuaibao/money-math'
import { getV } from '@ekuaibao/lib/lib/help'
import { Switch } from 'antd-mobile'
import { But<PERSON>, Dialog } from '@hose/eui-mobile'
import { nanoid } from 'nanoid'
interface Props {
  bus: any
  flowId: string
  isModifyBill: boolean
  dimensionCurrencyInfo?: any
  value?: any
  allStandardCurrency?: any[]
  billSpecification?: any
}

interface State {
  payeeInfo: Partial<PayeeInfo>
  payPlanList: any[]
  selectPayPlan: any[]
  currentMoney: number
  summaryMoney: any
  hasValidation: boolean
  supportPayPlanByApportion: boolean // 单据模板是否支持【按分摊生成支付计划】
  writtenOffMoney: any
}

interface PayeeInfo {
  onChange: (data: any) => void
  showPayPlan: boolean
  canEditPayPlan: boolean
  payPlans: any[]
  multiplePayeesMode: boolean
  payPlanMode: boolean
  payeePayPlan: boolean
  paymentPlanByApportion: boolean // 判断单据上的【包含分摊明细】开关是否打开
}

@EnhanceConnect((state: any) => ({
  allStandardCurrency: state['@common'].allStandardCurrency,
  dimensionCurrencyInfo: state['@bill'].dimensionCurrencyInfo
}))
export default class BillPayPlan extends PureComponent<Props, State> {
  state: State = {
    payeeInfo: {},
    payPlanList: [],
    selectPayPlan: [],
    currentMoney: 0,
    summaryMoney: 0,
    hasValidation: false,
    supportPayPlanByApportion: false,
    writtenOffMoney: 0
  }

  componentWillMount() {
    const { bus } = this.props
    bus.on('detail:value:change', this.handleDetailsChange)
    bus.on('payPlan:visible:change', this.handleChangeVisible)
    bus.on('payPlan:payMoney:change', this.handleChangeSummary)
    bus.on('payPlan:validation:change', this.handleChangeValidation)
    bus.on('payPlan:currency:change', this.handleCurrencyChange)
    bus.on('clear:payPlan:value', this.fnClearPayPlan)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('detail:value:change')
    bus.un('payPlan:visible:change')
    bus.un('payPlan:payMoney:change')
    bus.un('payPlan:validation:change')
    bus.un('payPlan:currency:change')
    bus.un('clear:payPlan:value')
  }

  componentDidMount() {
    const { bus } = this.props
    this.initPayPlanByApportion()
    if (bus.has('get:pay:money')) {
      bus.invoke('get:pay:money').then((money: any) => {
        this.setState({ summaryMoney: money })
      })
    }
  }

  fnClearPayPlan = () => {
    this.setState({ payPlanList: [] }, this.setFormValue)
  }

  // 检查当前模板是否支持按分摊明细生成支付计划
  checkSurportPlanByApportion = () => {
    const { billSpecification } = this.props
    const configs = getV(billSpecification, 'configs', [])
    const pay = configs.find(el => el.ability === 'pay')
    return getV(pay, 'paymentPlanByApportion', false)
  }

  initPayPlanByApportion = () => {
    const supportPayPlanByApportion = this.checkSurportPlanByApportion()
    if (!supportPayPlanByApportion) return
    this.setState({ supportPayPlanByApportion })
  }

  handleDetailsChange = async (details?: any[]) => {
    const { payeeInfo } = this.state
    this.handleChangeVisible(payeeInfo, details)
    this.handleChangeValidation(false)
  }

  handleCurrencyChange = (currency: any) => {
    const { payPlanList } = this.state
    payPlanList.forEach(item => {
      const value = item.amount?.standard
      item.amount = standardValueMoney(value, currency)
    })
    this.setState({ payPlanList: [...payPlanList] }, this.setFormValue)
  }

  handleChangeSummary = async (payMoney: any = 0, writtenOffMoney: any = 0) => {
    this.setState({ summaryMoney: payMoney, writtenOffMoney: writtenOffMoney }, this.handleDetailsChange)
  }

  handleChangeValidation = (hasValidation: boolean) => {
    this.setState({ hasValidation })
  }

  handleChangeVisible = async (payeeInfo: State['payeeInfo'], details?: any[]) => {
    const { showPayPlan, payPlans, payPlanMode } = payeeInfo
    if (payPlanMode) {
      // 按金额多收款
      if (!this.state.payeeInfo?.payPlanMode && this.state.payeeInfo?.paymentPlanByApportion && details) {
        // 从按分摊生成支付计划切换到按金额时
        const payPlanList = await this.getPayPlanByDetail({ ...payeeInfo, paymentPlanByApportion: false }, details)
        const newPayeeInfo = { ...payeeInfo, payPlanList, paymentPlanByApportion: false }
        return this.setState({ payeeInfo: newPayeeInfo, payPlanList }, this.setFormValue)
      } else if (payPlans) {
        const payPlanList = await this.getPayPlanByLocal(payPlans)
        return this.setState({ payeeInfo, payPlanList }, this.setFormValue)
      } else {
        return this.setState({ payeeInfo }, this.setFormValue)
      }
    }
    if (!payPlanMode && showPayPlan) {
      // 按明细多收款
      const payPlanList = await this.getPayPlanByDetail(payeeInfo, details)
      return this.setState(
        {
          payeeInfo: { ...payeeInfo, canEditPayPlan: false },
          payPlanList
        },
        this.setFormValue
      )
    }
    return this.setState({ payeeInfo })
  }

  // 从草稿或者保存的数据中获取支付支付
  getPayPlanByLocal = async (payPlans: any[]) => {
    const payeeAccounts: any[] = api.getState()['@common'].userPayeeAccount
    let payPlanList: State['payPlanList'] = []
    if (payPlans?.[0]?.id) {
      // 有草稿时用草稿
      if (payPlans?.[0]?.E_system_支付计划_legalEntity) {
        payPlanList = payPlans.map(el => {
          delete el.E_system_支付计划_legalEntity
          return el
        })
      } else {
        payPlanList = payPlans
      }
    } else {
      // 没有草稿，有保存的数据时，用保存的数据
      payPlanList = payPlans?.map(payPlan => {
        const payee = payPlan.dataLinkForm?.E_system_支付计划_收款信息
        const payeeId = payee?.id ? payee : payeeAccounts.find(el => el.id === payee)
        const amount = payPlan.dataLinkForm?.E_system_支付计划_支付金额 ?? null
        return {
          id: payPlan.dataLinkId,
          payeeId,
          amount
        }
      })
    }
    return payPlanList
  }

  // 从费用明细中获取支付计划
  getPayPlanByDetail = async (payeeInfo: State['payeeInfo'], details?: any[]) => {
    const { bus } = this.props
    const { payeePayPlan, paymentPlanByApportion } = payeeInfo
    let payPlanList: State['payPlanList'] = []
    let writtenOffMoney = this.state.writtenOffMoney
    details = details ?? (await bus.getFieldsValue())?.details ?? []
    details.forEach(detail => {
      const { feeTypeForm } = detail
      const { amount, feeDetailPayeeId, companyRealPay, settlement, detailId = nanoid(), apportions } =
        feeTypeForm || {}
      const settlementType = settlement?.opportunity
      const needCalculate = !settlementType || settlementType === 'SINGLEPAYMENT'
      let detailAmount: any = needCalculate ? Number(amount?.standard ?? 0) : 0
      let detailScale = Number(amount?.scale ?? 2)
      if (detailAmount > 0 && writtenOffMoney > 0) {
        if (detailAmount > writtenOffMoney) {
          detailAmount = new Big(detailAmount).minus(writtenOffMoney).toFixed(detailScale)
          writtenOffMoney = 0
        } else {
          writtenOffMoney = new Big(writtenOffMoney).minus(detailAmount).valueOf()
          detailAmount = 0
        }
      }
      if (companyRealPay) {
        const companyRealPayMoney = Number(companyRealPay?.standard ?? companyRealPay)
        detailAmount = new Big(detailAmount).minus(companyRealPayMoney).toFixed(detailScale)
      }
      let _foreign = amount?.foreign
      if (amount.hasOwnProperty('foreign') && amount.standardNumCode && (writtenOffMoney > 0 || companyRealPay)) {
        _foreign = new Big(detailAmount).div(amount.rate).toFixed(Number(amount.foreignScale))
      }
      if (feeDetailPayeeId) {
        const { supportPayPlanByApportion } = this.state
        // 按分摊明细生成支付计划
        const createPayPlanByApportion =
          supportPayPlanByApportion && // 单据模板是否支持【按分摊生成支付计划】
          paymentPlanByApportion && // 单据上的【包含分摊明细】开关是否打开
          apportions?.length && // 当前明细中是否包含分摊数据
          !payeePayPlan // 按收款信息汇总金额
        if (createPayPlanByApportion) {
          apportions.forEach((item: any) => {
            payPlanList.push({
              id: item.apportionForm?.apportionId,
              amount: item.apportionForm?.apportionMoney,
              E_system_支付计划_legalEntity: getV(item, 'apportionForm.法人实体'),
              payeeId: feeDetailPayeeId
            })
          })
        } else {
          payPlanList.push({
            id: detailId,
            payeeId: feeDetailPayeeId,
            amount: { ...amount, standard: detailAmount, foreign: _foreign }
          })
        }
      }
    })
    // 按收款信息汇总金额：
    if (payeePayPlan) {
      payPlanList = payPlanList.reduce((list, item) => {
        const { payeeId, amount } = item
        const payPlan = list.find((p: any) => {
          const isSameId = p.payeeId?.id === payeeId?.id
          const isSameStrCode = p.amount?.foreignStrCode === amount?.foreignStrCode
          return isSameId && isSameStrCode
        })
        if (payPlan) {
          payPlan.amount = new MoneyMath(payPlan.amount).add(amount).value
        } else {
          list.push(item)
        }
        return list
      }, [])
    }
    return payPlanList
  }

  setFormValue = () => {
    const { payeeInfo, payPlanList, summaryMoney = 0 } = this.state
    const { showPayPlan, onChange, ...rest } = payeeInfo
    if (!showPayPlan) return

    const summaryStandard = Number(summaryMoney?.standard ?? summaryMoney)
    const standardScale = Number(summaryMoney?.standardScale ?? 2)
    const currentMoney = getCurrentMoneyWithoutNoPayee(payPlanList)
    const noPlanMoney = Number(new Big(summaryStandard).minus(currentMoney).toFixed(Number(standardScale)))
    onChange({ ...rest, payPlanList, currentMoney: noPlanMoney })
  }

  handleAdd = () => {
    const { billSpecification } = this.props
    const { payPlanList, summaryMoney, payeeInfo } = this.state
    if (!payeeInfo.canEditPayPlan) return
    const currentMoney = getCurrentMoneyWithoutNoPayee(payPlanList)
    api.open('@bill:AddPayPlanModal', {
      billSpecification,
      payPlanList,
      summaryMoney,
      currentMoney,
      fnSetState: this.setState.bind(this),
      fnSetFormValue: this.setFormValue.bind(this)
    })
  }

  handleItemClick = (payPlan: any, index: number) => {
    const { payPlanList, summaryMoney, payeeInfo } = this.state
    const { flowId, isModifyBill, billSpecification } = this.props
    const currentMoney = getCurrentMoneyWithoutNoPayee(payPlanList)
    api.open('@bill:AddPayPlanModal', {
      inReadonly: !payeeInfo.canEditPayPlan,
      payPlanList,
      summaryMoney,
      currentMoney,
      billSpecification,
      isModifyBill,
      flowId,
      payPlan,
      index,
      fnSetState: this.setState.bind(this),
      fnSetFormValue: this.setFormValue.bind(this)
    })
  }

  handleCheckBox = (checked: boolean, id: string) => {
    const { selectPayPlan } = this.state
    if (checked) {
      this.setState({ selectPayPlan: [...selectPayPlan, id] })
    } else {
      this.setState({ selectPayPlan: selectPayPlan.filter(item => item != id) })
    }
  }

  handleItemDelete = (index: number) => {
    const { payPlanList } = this.state
    payPlanList.splice(index, 1)
    this.setState({ payPlanList, selectPayPlan: [] }, this.setFormValue)
  }

  handleBatchDelete = () => {
    const { selectPayPlan, payPlanList } = this.state
    if (!selectPayPlan.length) return
    Dialog.confirm({
      title: i18n.get('批量删除'),
      content: i18n.get('是否确定删除所有选中项？'),
      onConfirm: () => {
        selectPayPlan.forEach(id => {
          const index = payPlanList.findIndex(item => item.id === id)
          !!~index && payPlanList.splice(index, 1)
        })
        this.setState({ payPlanList, selectPayPlan: [] }, this.setFormValue)
      }
    })
  }

  handleBatchFix = () => {
    const { selectPayPlan, payPlanList } = this.state
    if (!selectPayPlan.length) return

    api.open('@apportion:PercentModifierModal', { isAmount: true, forPayPlan: true }).then(({ value }) => {
      selectPayPlan.forEach(id => {
        const payPlan = payPlanList.find(item => item.id === id)
        payPlan && (payPlan.amount.standard = value)
      })
      this.setState({ payPlanList: [...payPlanList] }, this.setFormValue)
    })
  }

  handleChangeApportionSwitch = async (checked: boolean) => {
    const { payeeInfo } = this.state
    const paymentPlanByApportion = checked
    const payPlanList = await this.getPayPlanByDetail({ ...payeeInfo, paymentPlanByApportion })
    const newPayeeInfo = { ...payeeInfo, payPlanList, paymentPlanByApportion }
    this.setState({ payPlanList, payeeInfo: newPayeeInfo }, this.setFormValue)
  }

  renderApportionSwitch = () => {
    const { supportPayPlanByApportion, payeeInfo } = this.state
    const { multiplePayeesMode, payPlanMode, payeePayPlan, paymentPlanByApportion } = payeeInfo
    const showApportionSwitch =
      supportPayPlanByApportion && // 单据模板是否支持【按分摊生成支付计划】
      multiplePayeesMode && // 是否是多收款人模式
      !payPlanMode && // 多收款 - 按金额
      !payeePayPlan // 多收款 - 按收款信息汇总金额
    // 单据模板是否支持【按分摊生成支付计划】,并且在按明细多收款模式时，展示开关
    if (!showApportionSwitch) return null
    return (
      <div className="billPayPlan-switch">
        <span>{i18n.get('包含分摊明细')}</span>
        <Switch checked={paymentPlanByApportion} onChange={this.handleChangeApportionSwitch} />
      </div>
    )
  }

  render() {
    const {
      payeeInfo,
      payPlanList,
      selectPayPlan,
      summaryMoney = 0,
      hasValidation,
      supportPayPlanByApportion
    } = this.state
    const { showPayPlan, canEditPayPlan, paymentPlanByApportion } = payeeInfo
    if (!showPayPlan) return null

    const titleText =
      payPlanList.length > 0 ? i18n.get(`支付计划（{__k0}）`, { __k0: payPlanList.length }) : i18n.get('支付计划')
    const summaryStandard = Number(summaryMoney?.standard ?? summaryMoney)
    const standardScale = Number(summaryMoney?.standardScale ?? 2)
    const currentMoney = getCurrentMoneyWithoutNoPayee(payPlanList)
    const noPlanMoney = Number(new Big(summaryStandard).minus(currentMoney).toFixed(Number(standardScale)))

    const { dimensionCurrencyInfo, value, allStandardCurrency } = this.props
    const currencyName = value?.legalEntityMultiCurrency?.name
    const currencyValue = allStandardCurrency?.find(v => v.name === currencyName)
    const currency = dimensionCurrencyInfo?.currency || currencyValue

    return (
      <div className={styles['billPayPlan-wrap']}>
        <div className="billPayPlan-title">{titleText}</div>
        <BillPayPlanHeader
          inPaymentPlanByApportionMode={supportPayPlanByApportion && paymentPlanByApportion}
          hasValidation={hasValidation}
          hasPayPlan={!!payPlanList.length}
          summaryMoney={summaryStandard}
          currentMoney={noPlanMoney}
          currency={currency}
        />
        <BillPayPlanContent
          currency={currency}
          payPlanList={payPlanList}
          selectPayPlan={selectPayPlan}
          canEditPayPlan={canEditPayPlan}
          handleCheckBox={this.handleCheckBox}
          handleItemClick={this.handleItemClick}
          handleItemDelete={this.handleItemDelete}
        />
        {this.renderApportionSwitch()}
        <div
          className={classnames('billPayPlan-batch-action', {
            'billPayPlan-batch-action-active': selectPayPlan.length
          })}
        >
          <div className="billPayPlan-batch-action-btn" onClick={this.handleBatchFix}>
            {i18n.get('批量修改')}
          </div>
          <div className="billPayPlan-batch-action-btn" onClick={this.handleBatchDelete}>
            {i18n.get('批量删除')}
          </div>
        </div>
        <div className="billPayPlan-button-wrap">
          <Button
            category="dashed"
            theme="highlight"
            size="large"
            block
            disabled={!canEditPayPlan}
            onClick={this.handleAdd}
          >
            {i18n.get('+ 添加支付计划')}
          </Button>
        </div>
      </div>
    )
  }
}
