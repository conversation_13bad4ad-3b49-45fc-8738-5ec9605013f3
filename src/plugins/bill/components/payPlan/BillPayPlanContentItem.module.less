@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.billPayPlanContentItem-wrap {
  :global {
    .billPayPlanContentItem {
      padding: @space-5 @space-6;
      height: 100%;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      border-top: 2px solid @color-line-2;
      .billPayPlanContentItem-content {
        flex: 1;
        margin-left: @space-6;
        .am-checkbox-wrapper {
          flex-shrink: 0;
        }
        .billPayPlanContentItem-content-label {
          color: @color-black-1;
        }
        .billPayPlanContentItem-content-money {
          .font-size-1;
          color: @color-black-3;
        }
      }
    }
    .am-swipe-content {
      position: relative;
      // height: 130px;
    }
  }
}
