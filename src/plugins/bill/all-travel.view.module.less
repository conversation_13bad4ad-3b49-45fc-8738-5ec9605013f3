@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.allTravelViewWrapper{
    overflow-y: auto;
    background-color: var(--eui-bg-base);
    
    .date {
        font: var(--eui-font-body-r1);
        color: var(--eui-text-caption);
        margin: @space-5 @space-6 @space-4;
    }
    .listContainer {
        border: 2px solid var(--eui-line-border-card);
        border-radius: @radius-3;
        padding: @space-4 @space-6 @space-6;
        margin: 0 @space-6 @space-4;
        background-color: var(--eui-bg-body);
        :global {
            .type {
                font: var(--eui-font-body-b1);
                color: var(--eui-text-title);
                margin-bottom: @space-4;
                margin-left: -@space-2;
    
                .eui-icon {
                    font-size: 48px;
                    vertical-align: bottom;
                }
    
                .name {
                    margin: @space-1 @space-2;
                    display: inline-block;
                }
            }
        }
    }
}