/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/11 下午6:03
 */
import React, { PureComponent } from 'react'
import { app, app as api } from '@ekuaibao/whispered'
import EkbPopup from '@ekuaibao/popup-mobile'
import { OutlinedEditFilter, OutlinedEditTableGroup, FilledDirectionExpandDown } from '@hose/eui-icons'
import { SearchBar } from '@hose/eui-mobile/2x'
import { Calendar } from '@ekuaibao/mcalendar'
import dayjs from 'dayjs'
import styles from './bill-content.module.less'
import { session } from '@ekuaibao/session-info'
import BillList from './bill-list'
import BillListWrapper from './BillListWrapper'
const OnceTip = app.require('@elements/puppet/onceTip/OnceTip')
import { EnhanceConnect } from '@ekuaibao/store'
import EKBDropDown from '../elements/EKBDropDown'
import BillPopup from './bill-popup'
import { Fetch } from '@ekuaibao/fetch'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import EkbIcon from '../../../elements/ekbIcon'
import BillFilter from './bill-filter'
import classNames from 'classnames'
import { getScenesLabel } from '../utils/billUtils'
import BillSort from './bill-sort'
import { enableHidingFinishedBills } from '../../../lib/featbit'

const SkeletonListEUI = app.require('@home5/SkeletonList')
const STATE = {
  SEARCH: 'SEARCH',
  SORT: 'SORT',
  CHECKED: 'CHECKED'
}

let timeout
const typeMap = () => {
  return {
    waitInvoice: i18n.get('查看待开发票'),
    expense: i18n.get('报销单'),
    loan: window.IS_SMG ? i18n.get('预支单') : i18n.get('借款单'),
    requisition: i18n.get('申请单')
  }
}
@EnhanceConnect(
  state => ({
    descByUpdateTime: state['@bill'].BillContent ? state['@bill'].BillContent.descByUpdateTime : false,
    userInfo: state['@common'].me_info.staff
  }),
  undefined,
  '@bill/BillContent'
)
export default class BillContent extends PureComponent {
  dataList = []
  static defaultProps = {
    editable: false,
    showDropDown: false,
    closeDropDown: true,
    useNewItem: false,
    sortValue: [],
  }
  searchRef = React.createRef()
  constructor(props) {
    super(props)
    this.isHongShanTestingEnterprise = isHongShanTestingEnterprise(Fetch.ekbCorpId)

    this.segmentations = [
      { key: 'bill', label: i18n.get('单据') },
      { key: 'detail', label: i18n.get('明细') }
    ]

    this.state = {
      showGroupDropDown: false,
      closeGroupDropDown: true,
      activeGroupText: props?.activeGroup?.label,
      activeGroup: props?.activeGroup,
      activeGroupPupUp: props?.activeGroup?.name,
      stateChecked: '',
      selectedFilter: false,
      ...this.initState(props.billType),
      calendarVisible: false,
      startDate: dayjs()
        .subtract(6, 'month')
        .format('YYYY-MM-DD'),
      endDate: dayjs().format('YYYY-MM-DD'),
      selectedDate: [
        dayjs()
          .subtract(6, 'month')
          .toDate(),
        dayjs().toDate()
      ],
    }
  }

  // static getDerivedStateFromProps(props, state) {
  //   if ( !!props.menuList && JSON.stringify(props.menuList) !== JSON.stringify(state.menuList)) {
  //     const item = props.menuList[0]
  //     const type = item.type
  //     const activeText = typeMap[type] || type
  //     props.onChangeBillType(type, '', true)
  //     return { menuList: props.menuList, activePupUp: type, activeText  }
  //   }
  //   return null
  // }

  componentDidMount() {
    const { bus } = this.props
    api.dataLoader('@common.me_info').load()
    bus && bus.watch('get:selected:data', this.getSelectedData)
    bus && bus.on('list:update', this.listUpdate)
    api.on('update:cache:view:data', this.handleCacheViewUpdateData)
  }

  componentWillUnmount() {
    EkbPopup.hide()
    const { bus } = this.props
    bus && bus.un('get:selected:data', this.getSelectedData)
    bus && bus.un('list:update', this.listUpdate)
    api.un('update:cache:view:data', this.handleCacheViewUpdateData)
  }

  initState = (type = 'all', actionType, searchValue = '') => {
    if (actionType === 'cancel') {
      return {
        searchValue,
        filterBy: '', // 搜索功能生成的filterBy字符串
        selectedDataMap: {},
        inEdite: false,
        selectAll: false,
        showDropDown: false,
        closeDropDown: true
      }
    }
    let { billType, activeText, menuList } = this.props
    const activePupUp = type ? type : billType ? billType : 'all'
    return {
      searchValue,
      filterBy: '',
      activePupUp,
      activeText: typeMap()[type] || getScenesLabel(type, menuList) || activeText,
      selectedDataMap: {},
      inEdite: false,
      selectAll: false,
      showDropDown: false,
      closeDropDown: true
    }
  }

  listUpdate = type => {
    this.setState(this.initState(type))
  }

  editeHandleChange = (data, actionType) => {
    const { bus, editable } = this.props
    if (!editable || (enableHidingFinishedBills() && data?.state === 'nullify')) return // 已作废的单据不支持选中
    const { inEdite, searchValue } = this.state
    bus && bus.emit('edite:state:change', !inEdite)
    if (inEdite) {
      return this.setState(this.initState(null, actionType, searchValue))
    }
    if (data && data.id && editable) {
      //点击头像
      this.handleCheckboxChange({ checked: true, data })
    }
    this.setState({ inEdite: !inEdite, stateChecked: '' })
  }

  getSelectedData = () => {
    const { selectedDataMap } = this.state
    return selectedDataMap
  }

  handleCheckboxChange = value => {
    const { checked, data } = value
    const { bus } = this.props
    const { selectedDataMap } = this.state
    const dataMap = { ...selectedDataMap }
    if (checked) {
      dataMap[data.id] = data
    } else {
      delete dataMap[data.id]
    }
    const selectAll = this.dataList.length === Object.keys(dataMap).length
    bus && bus.emit('select:data:change', dataMap)
    this.setState({ selectedDataMap: dataMap, selectAll })
  }

  selectAllChange = select => {
    const { selectAll } = this.state
    const { bus } = this.props
    if (selectAll === select) return
    let dataMap = {}
    if (select) {
      const { dataList } = this
      dataList.filter(item => enableHidingFinishedBills() ? item.state !== 'nullify' : true).forEach(element => (dataMap[element.id] = element)) // 过滤掉已作废的单据
    }
    bus && bus.emit('select:data:change', dataMap)
    this.setState({ selectAll: select, selectedDataMap: dataMap })
  }

  handleChangeBillType = type => {
    const { onChangeBillType, searchFrom, menuList } = this.props
    EkbPopup.hide()
    if (type === 'cancel') {
      return null
    }

    this.setState(
      {
        activePupUp: type,
        activeText: typeMap()[type] || getScenesLabel(type, menuList),
        showDropDown: false,
        searchValue: '',
        filterBy: ''
      },
      () => setTimeout(() => this.setState({ closeDropDown: true }), 200)
    )

    return onChangeBillType(type, '', true)
  }

  fnOpenPopup = () => {
    const { menuList, searchFrom, billType } = this.props
    const { activePupUp, activeText } = this.state
    const activeType = activeText === i18n.get('全部') ? activePupUp : billType || 'all'

    EkbPopup.show(
      <BillPopup
        onChangeBillType={this.handleChangeBillType.bind(this)}
        activeType={activeType}
        menuList={menuList}
        searchFrom={searchFrom}
      />,
      { animationType: 'slide-up', maskClosable: true }
    )
  }

  handleOpenPopup(showOld) {
    if (this.state?.showGroupDropDown) return
    if (showOld && !window.isNewHome) return this.fnOpenPopup()
    const { showDropDown } = this.state
    if (showDropDown) {
      this.handleCloseDropDown()
    } else {
      this.handleOpenDropDown()
    }
  }
  handleChangeGroupType = menu => {
    const { onChangeGroupType } = this.props
    const { activeGroupPupUp } = this.state
    if (activeGroupPupUp === menu?.name) {
      return
    }
    let name = menu?.name
    let label = menu?.label
    if (menu?.name === 'clear') {
      label = i18n.get('选择分组展示字段')
      name = ''
    }
    this.setState(
      {
        activeGroupPupUp: name,
        activeGroupText: label,
        showGroupDropDown: false
      },
      () => setTimeout(() => this.setState({ closeGroupDropDown: true }), 200)
    )
    return onChangeGroupType({ menu: menu?.name === 'clear' ? undefined : menu })
  }
  handleOpenGroupPopup() {
    const { showGroupDropDown, inEdite } = this.state
    if (inEdite) return
    if (showGroupDropDown) {
      this.handleGroupCloseDropDown()
    } else {
      this.handleGroupOpenDropDown()
    }
  }
  handleGroupOpenDropDown = () => {
    const { closeGroupDropDown, showGroupDropDown } = this.state
    if (!closeGroupDropDown && !showGroupDropDown) return null
    this.setState({ closeGroupDropDown: false }, () => setTimeout(() => this.setState({ showGroupDropDown: true }), 0))
  }

  handleGroupCloseDropDown = () => {
    this.setState({ showGroupDropDown: false }, () =>
      setTimeout(() => this.setState({ closeGroupDropDown: true }), 200)
    )
  }

  handleOpenDropDown = () => {
    const { closeDropDown, showDropDown } = this.state
    if (!closeDropDown && !showDropDown) return null
    this.setState({ closeDropDown: false }, () => setTimeout(() => this.setState({ showDropDown: true }), 0))
  }

  handleCloseDropDown = () => {
    this.setState({ showDropDown: false }, () => setTimeout(() => this.setState({ closeDropDown: true }), 200))
  }

  handleSearchChecked = () => {
    this.setState(
      {
        stateSearch: !this.state.stateSearch
      },
      () => {
        this.searchRef?.current?.focus()
      }
    )
  }

  handleFilterClick = () => {
    this.setState({
      selectedFilter: !this.state.selectedFilter
    })
  }

  handleSorter = () => {
    const { onChangeBillType, descByUpdateTime, searchFrom, activeGroup, onChangeGroupType } = this.props
    const { activePupUp, filterBy } = this.state
    this.setState({
      stateChecked: STATE.SORT
    })
    this.props.setState({
      descByUpdateTime: !descByUpdateTime
    })
    if (activeGroup) {
      onChangeGroupType({ descByUpdateTime: !descByUpdateTime })
    } else {
      onChangeBillType(activePupUp, filterBy, searchFrom)
    }
  }

  renderHeader = showOld => {
    const { inEdite } = this.state
    const { searchFrom, showFilter, applyNewFilter = false } = this.props
    if (showFilter && !applyNewFilter) return null
    let activeText = searchFrom === 'home' ? this.props.activeText : this.state.activeText
    return inEdite ? this.renderInEditeHeader(activeText) : this.renderUnEditeHeader(activeText, searchFrom, showOld)
  }

  renderInEditeHeader = activeText => {
    const { selectAll, selectedDataMap } = this.state
    const count = Object.keys(selectedDataMap).length
    return (
      <div className={styles.bill_content_header}>
        <div className="header_left">
          <div className="type_name">{activeText}</div>
          <div className="select_count">{i18n.get(`已选 {__k0} 项`, { __k0: count })}</div>
        </div>
        <div className="header_right">
          <div onClick={this.selectAllChange.bind(this, !selectAll)}>
            {selectAll ? i18n.get('取消全选') : i18n.get('全选')}
          </div>
          <div className="edit" onClick={_ => this.editeHandleChange(null, 'cancel')}>
            {i18n.get('取消')}
          </div>
        </div>
      </div>
    )
  }

  renderUnEditeHeader = (activeText, searchFrom, showOld) => {
    const { editable, descByUpdateTime, showFilter, showSort, fnSwitchApproveMode, inFeeTypeMode, sortValue } = this.props
    const { showDropDown, showNext, stateChecked, stateSearch, showGroupDropDown, selectedFilter } = this.state
    let text = i18n.get('点击可批量审批')
    if (searchFrom === 'recycle') {
      text = i18n.get('点击可批量还原')
    }
    const sessionKey = 'tutor_handleBatch'
    const batchModeSwitchOffsetRight = fnSwitchApproveMode ? 124 : 0
    const batchModeSwitchStyle = { bottom: -44, right: batchModeSwitchOffsetRight }
    const batchModeSwitchArrowStyle = { top: 4, right: 18 }

    const sorterButtonOffsetRight = fnSwitchApproveMode ? 164 : 80
    const sorterStyle = fnSwitchApproveMode
      ? { bottom: -44, right: sorterButtonOffsetRight }
      : { bottom: 0, right: sorterButtonOffsetRight }
    const sorterArrowStyle = fnSwitchApproveMode ? { top: 4, right: 18 } : { right: 4 }
    const fnClickEditIcon = !showDropDown ? this.editeHandleChange : () => {}
    const arrowClass = !showDropDown ? 'rotate' : ''
    const showDrop = showDropDown || showGroupDropDown
    return (
      <div className={styles.bill_content_header}>
        <div className="type_name" onClick={this.handleOpenPopup.bind(this, showOld)}>
          <span className={showDropDown ? 'active' : ''}>{activeText}</span>
          <EkbIcon
            className={arrowClass}
            name="#EDico-icon_uni_drop-title"
            style={{ color: [showDropDown ? 'var(--brand-base)' : '#272E3B'], fontSize: 16 }}
          />
        </div>
        <div className="header_right">
          {editable && showNext && (
            <OnceTip
              userInfo={this.props.userInfo}
              text={
                searchFrom === 'recycle'
                  ? i18n.get('按删除时间降序排列')
                  : descByUpdateTime
                  ? i18n.get('按提交时间降序排列')
                  : i18n.get('按提交时间升序排列')
              }
              sessionKey={'tutor_sorter'}
              fnCallBack={() => {
                this.setState({ showThirdTip: true })
              }}
              style={sorterStyle}
              arrowStyle={sorterArrowStyle}
            />
          )}

          {editable && !showDrop && (
            <OnceTip
              userInfo={this.props.userInfo}
              text={text}
              sessionKey={sessionKey}
              fnCallBack={() => {
                this.setState({ showNext: true })
              }}
              style={batchModeSwitchStyle}
              arrowStyle={batchModeSwitchArrowStyle}
            />
          )}
          <div className={styles.bill_content_search} onClick={this.handleSearchChecked}>
            <EkbIcon
              name="#EDico-icon_uni_search"
              style={{ color: [stateSearch ? 'var(--brand-base)' : 'var(--eui-icon-n1)'], fontSize: 20 }}
            />
          </div>
          {showFilter && (
            <div className={styles.bill_content_filter} onClick={this.handleFilterClick}>
              <OutlinedEditFilter style={{ color: [selectedFilter ? 'var(--brand-base)' : 'var(--eui-icon-n1)'], fontSize: 20 }} />
            </div>
          )}
          {showSort && (
            <BillSort value={sortValue} className={styles.bill_content_sort} onChange={this.props.onSortChange} />
          )}
          {editable && (
            <div
              className={descByUpdateTime ? `${styles.sorter} ${styles.desc}` : styles.sorter}
              onClick={this.handleSorter}
            >
              <EkbIcon
                name="#EDico-icon_uni_sort"
                style={{ color: [stateChecked === STATE.SORT ? 'var(--brand-base)' : '#333333'], fontSize: 20 }}
              />
            </div>
          )}
          {editable && (
            <div className={styles.bill_content_header_batch} onClick={fnClickEditIcon}>
              <EkbIcon name="#ico-7-icon_list" style={{ color: '#333333', fontSize: 20 }} />
            </div>
          )}
          {fnSwitchApproveMode && (
            <div className="segmentation-wrapper">
              {this.segmentations.map(seg => {
                const mode = inFeeTypeMode ? 'detail' : 'bill'
                const cls = `segmentation-item ${seg.key === mode ? 'segmentation-item-select' : ''} `
                const fn = seg.key === mode ? () => {} : fnSwitchApproveMode
                return (
                  <div key={seg.key} className={cls} onClick={fn}>
                    {seg.label}
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>
    )
  }

  renderContent = showOld => {
    const {
      dataSource,
      menuList,
      showFilter,
      filterList,
      emptyText,
      onHandleClick,
      searchFrom = '',
      style,
      footer,
      onEndReached,
      onLoadMore,
      onEndReachedThreshold,
      approveShowState,
      isAlwaysPrint,
      useNewItem,
      customLoadMore,
      dataSourceTotal,
      applyNewFilter,
      hideAvatar,
      flowType,
      showSkeleton,
      useNewStyle
    } = this.props
    let { selectedDataMap, inEdite, searchValue } = this.state
    if (showSkeleton) {
      return <SkeletonListEUI showImage={false} />
    }
    this.dataList = dataSource
    const emptyTextValue = searchValue ? i18n.get('没有找到您所要的结果') : i18n.get(emptyText)
    return showOld ? (
      <BillList
        searchFrom={searchFrom}
        searchValue={searchValue}
        onChangeBillFilter={this.handleChangeBillFilter}
        menuList={menuList}
        showFilter={showFilter}
        filterList={filterList}
        onHandleClick={onHandleClick}
        dataSource={dataSource}
        emptyText={emptyTextValue}
        style={style}
        customLoadMore={customLoadMore}
        dataSourceTotal={dataSourceTotal}
        applyNewFilter={applyNewFilter}
        useNewStyle={useNewStyle}
      />
    ) : (
      <BillListWrapper
        {...this.props}
        hasSearchBar={true}
        isAlwaysPrint={isAlwaysPrint}
        searchValue={searchValue}
        footer={footer}
        selectAble={inEdite}
        avatarClick={searchFrom === 'payment-review' ? undefined : this.editeHandleChange}
        selectedDataMap={selectedDataMap}
        checkBoxChange={this.handleCheckboxChange}
        itemClick={onHandleClick}
        billList={dataSource}
        emptyText={emptyTextValue}
        onEndReached={onEndReached || onLoadMore}
        approveShowState={approveShowState}
        onEndReachedThreshold={onEndReachedThreshold}
        useNewItem={useNewItem}
        hideAvatar={hideAvatar}
        flowType={flowType}
        useNewStyle={useNewStyle}
      />
    )
  }

  renderTutor = showOld => {
    const { dataSource, editable, userInfo, searchFrom, groupMenu, activeGroup, hideAvatar } = this.props
    if (!dataSource.length || !editable || hideAvatar) return null
    const { inEdite, showThirdTip, showDropDown, showGroupDropDown, activeGroupPupUp, stateSearch } = this.state
    if (showOld || inEdite) return null
    if (!userInfo) {
      return null
    }
    const { corporationId, id } = userInfo
    const key = 'tutor_handleBatch' + '-' + corporationId.id + '-' + id
    const show = session?.user?.get(key)
    if (!show || !showThirdTip) return null
    if (showGroupDropDown || showDropDown) return null

    let text = i18n.get('点击头像可快速审批')
    if (searchFrom === 'recycle') {
      text = i18n.get('点击头像可快速还原')
    }
    const sessionKey = 'tutor_handleBatchInShort'
    const style = { top: 80, left: 57 }
    if (stateSearch) {
      style.top = 135
    }
    if (this.isHongShanTestingEnterprise && groupMenu?.length > 0) {
      if (activeGroupPupUp || activeGroup?.name) {
        if (stateSearch) {
          style.top = 215
        } else {
          style.top = 155
        }
      } else {
        if (stateSearch) {
          style.top = 170
        } else {
          style.top = 115
        }
      }
    }
    const arrowStyle = { top: 20, left: 4 }
    return <OnceTip userInfo={userInfo} text={text} sessionKey={sessionKey} style={style} arrowStyle={arrowStyle} />
  }

  renderCalendar = () => {
    const { onChangeBillFilterDate } = this.props
    const { calendarVisible, startDate, endDate, selectedDate } = this.state
    const now = new Date()
    return (
      <>
        <div className={styles.show_calendar} onClick={() => this.setState({ calendarVisible: true })}>
          {startDate}
          {i18n.get('至')}
          {endDate}
          <FilledDirectionExpandDown />
        </div>
        <Calendar
          visible={calendarVisible}
          closeOnMaskClick={true}
          onCancel={() => this.setState({ calendarVisible: false })}
          onConfirm={(start, end) => {
            this.setState({
              startDate: dayjs(start).format('YYYY-MM-DD'),
              endDate: dayjs(end).format('YYYY-MM-DD'),
              selectedDate: [start, end],
              calendarVisible: false
            })
            const startDate = dayjs(start)
              .startOf('day')
              .valueOf()
            const endDate = dayjs(end)
              .endOf('day')
              .valueOf()
            onChangeBillFilterDate && onChangeBillFilterDate(startDate, endDate)
          }}
          defaultValue={selectedDate}
          infiniteOpt
          defaultDate={new Date(now.setMonth(now.getMonth() - 6))}
          prevDate={36}
        />
      </>
    )
  }

  searchMap = val => {
    const { searchFrom: from, onChangeBillType, showFilter, onChangeBillFilter, enableGlobalSearch } = this.props
    const { activePupUp } = this.state
    let filterBy = ''
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage === 'en-US'
    switch (from) {
      case 'home':
        //来自首页的搜索
        if (lang) {
          filterBy = `lower(form.title).containsIgnoreCase(lower("${val}")) || lower(form.code).containsIgnoreCase(lower("${val}"))`
        } else {
          filterBy = `form.title.containsIgnoreCase("${val}") || form.code.containsIgnoreCase("${val}")`
        }
        break
      case 'approving':
      case 'paying':
      case 'recycle':
        //来自待办或者待支付页面的搜索、回收站搜索
        if (lang) {
          filterBy = `lower(flowId.form.title).containsIgnoreCase(lower("${val}")) || lower(flowId.form.code).containsIgnoreCase(lower("${val}"))||lower(flowId.form.submitterId.name).containsIgnoreCase(lower("${val}"))`
        } else {
          filterBy = `flowId.form.title.containsIgnoreCase("${val}") || flowId.form.code.containsIgnoreCase("${val}")||flowId.form.submitterId.name.containsIgnoreCase("${val}")`
        }
        break
      case 'archived':
        //来自已完成页面的搜索
        if (lang) {
          filterBy = `(state == "paid" || state == "archived") && (lower(form.title).containsIgnoreCase(lower("${val}")) || lower(form.code).containsIgnoreCase(lower("${val}")) || lower(form.submitterId.name).containsIgnoreCase(lower("${val}")))`
        } else {
          filterBy = `(state == "paid" || state == "archived") && (form.title.containsIgnoreCase("${val}") || form.code.containsIgnoreCase("${val}") || form.submitterId.name.containsIgnoreCase("${val}"))`
        }
        break
      case 'approved':
        //来自已审批页面的搜索
        filterBy = `form.title.containsIgnoreCase("${val}") || form.code.containsIgnoreCase("${val}")||form.submitterId.name.containsIgnoreCase("${val}")`
        break
      case 'receiving':
      case 'sending':
        filterBy = `flowId.form.title.containsIgnoreCase("${val}") || flowId.form.code.containsIgnoreCase("${val}")||flowId.form.submitterId.name.containsIgnoreCase("${val}")`
        break
      case 'received':
      case 'sent':
        filterBy = `form.title.containsIgnoreCase("${val}") || form.code.containsIgnoreCase("${val}")||form.submitterId.name.containsIgnoreCase("${val}")`
        break
      case 'payment-review':
        filterBy = val
        break
      case 'approving-detail':
        if (lang) {
          filterBy = `lower(form.feeTypeId.name).containsIgnoreCase(lower("${val}")) || lower(form.submitterId.name).containsIgnoreCase(lower("${val}"))`
        } else {
          filterBy = `form.feeTypeId.name.containsIgnoreCase("${val}") || form.submitterId.name.containsIgnoreCase("${val}")`
        }
        break
      default:
        if (from && from.startsWith('carbonCopy')) {
          filterBy = `flowId.form.title.containsIgnoreCase("${val}") || flowId.form.code.containsIgnoreCase("${val}")||flowId.form.submitterId.name.containsIgnoreCase("${val}")`
        }
    }

    /**
     * 高级搜索要求传入 flowId.form.details.contains 或者 form.details.contains
     * 具体不同场景传入的不同，如果有 flowId.form 字段肯定是 flowId.form.details.contains
     *
     * receivingExcep 的时候 filterBy 为空，所以需要特殊处理
     */
    if (enableGlobalSearch && (!!filterBy || from === 'receivingExcep')) {
      if (!!filterBy) {
        filterBy += '||'
      }
      const prefix = filterBy.includes('flowId.') || from === 'receivingExcep' ? 'flowId.form' : 'form'
      const isMoneyReg = /^-?\d+(\.\d{0,2})?$/
      const useGlobalSearchV2 = window?.PLATFORMINFO?.useGlobalSearchV2
      if (isMoneyReg.test(val) && useGlobalSearchV2) {
        filterBy += `${prefix}.payMoney.standard.containsIgnoreCase("${val}") || ${prefix}.expenseMoney.standard.containsIgnoreCase("${val}")`
      } else {
        filterBy += `${prefix}.containsIgnoreCase("${val}")`
      }
    }
    if (!!filterBy) {
      filterBy = `&&(${filterBy})`
    }
    const needRefreshTitle = val !== undefined && !val?.length
    showFilter
      ? onChangeBillFilter({ ...this.newFilterParam, filterSearch: filterBy })
      : onChangeBillType(activePupUp, filterBy, needRefreshTitle)
    this.setState({ filterBy })
  }

  handleCacheViewUpdateData = () => {
    const { filterBy, activePupUp } = this.state
    const { onChangeBillType } = this.props
    let _filterBy = filterBy
    if (filterBy.startsWith('&&')) {
      _filterBy = filterBy.slice(2)
    }
    onChangeBillType && onChangeBillType(activePupUp, _filterBy , true, true)
  }

  handleChangeBillFilter = param => {
    const { filterBy } = this.state
    const { onChangeBillFilter } = this.props
    this.newFilterParam = param
    onChangeBillFilter && onChangeBillFilter({ ...param, filterSearch: filterBy })
  }

  handleSearchInPage = (val = this.state.searchValue) => {
    const searchText = val && val.trim()
    this.setState({ searchValue: val })
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
    timeout = setTimeout(() => {
      this.searchMap.call(this, searchText)
    }, 400)
  }

  handleClear = () => {
    this.handleSearchInPage('')
  }

  renderSearchBar(showOld, searchFrom) {
    const { enableGlobalSearch } = this.props
    const { searchValue, inEdite, showGroupDropDown } = this.state
    if (showOld && !window.isNewHome) return null
    let headerStyle = {}
    let initText = enableGlobalSearch ? i18n.get('搜索文本、数字类信息或提交人') : i18n.get('搜索标题、单号或提交人')
    let placeholder
    switch (searchFrom) {
      case 'home':
      case 'recycle':
        placeholder = i18n.get('搜索标题或单号')
        break
      case 'payment-review':
        placeholder = i18n.get('搜索批次号')
        break
      case 'approving-detail':
        placeholder = i18n.get('搜索费用明细或提交人')
        break
      default:
        placeholder = initText
        break
    }
    if (inEdite) {
      headerStyle.display = 'none'
    }
    if (showGroupDropDown) {
      headerStyle.zIndex = 3
    }

    return (
      <React.Fragment>
        <SearchBar
          ref={this.searchRef}
          showCancelButton
          className={styles['bill-list-search-bar']}
          style={headerStyle}
          value={searchValue}
          placeholder={placeholder}
          onChange={this.handleSearchInPage.bind(this)}
          onClear={this.handleClear}
          onCancel={this.handleClear}
        />
      </React.Fragment>
    )
  }

  renderGroupSelect() {
    const { groupMenu, activeGroup } = this.props
    const { showGroupDropDown, activeGroupText, inEdite } = this.state
    if (this.isHongShanTestingEnterprise && groupMenu?.length > 0) {
      const label = activeGroup?.label || activeGroupText || i18n.get('选择分组展示字段')
      const arrowClass = !showGroupDropDown ? 'rotate' : ''
      const labelClass = showGroupDropDown ? 'active' : ''
      return (
        <div className={styles.bill_content_group} style={{ zIndex: showGroupDropDown ? 3 : 1 }}>
          <div className="type_name" onClick={this.handleOpenGroupPopup.bind(this)}>
            <span className={label === i18n.get('选择分组展示字段') ? 'placeholder' : labelClass}>{label}</span>
            {!inEdite && (
              <EkbIcon
                className={arrowClass}
                name="#EDico-icon_uni_drop-title"
                style={{ color: [showGroupDropDown ? 'var(--brand-base) ' : '#272E3B'], fontSize: 16 }}
              />
            )}
          </div>
        </div>
      )
    }
  }

  renderSearch(showOld) {
    // applyNewFilter 使用新的过滤头部
    const { searchFrom = '', applyNewFilter = false } = this.props
    const { stateSearch } = this.state
    if (showOld && !applyNewFilter) {
      return this.renderSearchBar(showOld, searchFrom)
    }
    if (stateSearch) {
      return this.renderSearchBar(showOld, searchFrom)
    }
    return null
  }

  render() {
    const {
      className,
      searchFrom = '',
      menuList,
      groupMenu,
      activeGroup,
      showFilter,
      filterList,
      showDateFilter
    } = this.props
    const {
      showDropDown,
      closeDropDown,
      activePupUp,
      stateSearch,
      closeGroupDropDown,
      activeGroupPupUp,
      showGroupDropDown,
      selectedFilter
    } = this.state
    const showOld = !!~['home'].indexOf(searchFrom)
    return (
      <div className={classNames(styles.bill_content, className)}>
        {!closeDropDown && (
          <EKBDropDown
            show={showDropDown}
            activeType={activePupUp}
            menuList={menuList}
            fnChangeType={this.handleChangeBillType}
            fnCancel={this.handleCloseDropDown}
          />
        )}
        {!closeGroupDropDown && (
          <EKBDropDown
            isGroup
            isSearch={stateSearch}
            show={showGroupDropDown}
            activeType={activeGroupPupUp || activeGroup?.name}
            menuList={[...groupMenu, { label: i18n.get('清空选择'), name: 'clear' }]}
            fnChangeType={this.handleChangeGroupType}
            fnCancel={this.handleGroupCloseDropDown}
          />
        )}
        {this.renderHeader(showOld)}
        {this.renderSearch(showOld)}
        {this.renderGroupSelect()}
        {this.renderTutor(showOld)}
        {showDateFilter && this.renderCalendar()}
        {this.renderContent(showOld)}
        {showFilter && (
          <BillFilter
            groupList={menuList}
            filterList={filterList}
            onChangeBillFilter={this.handleChangeBillFilter}
            showGroupIcon={false}
            showFilterIcon={false}
            selectedFilter={selectedFilter}
            top={56}
            onFilterCancel={this.handleFilterClick}
          />
        )}
      </div>
    )
  }
}

BillContent.defaultProps = {
  activeType: 'all', //默认单据类型
  activeText: i18n.get('全部'), //默认显示单据类型标题
  dataSource: [], //数据源
  emptyText: i18n.get('暂无单据'), //空数据时显示的文字提示
  searchFrom: 'home', //从哪个几面跳转到搜索
  onChangeBillType: () => {},
  onHandleClick: () => {}
}
