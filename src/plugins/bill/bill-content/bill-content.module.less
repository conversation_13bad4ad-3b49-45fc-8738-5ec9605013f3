/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/11 下午6:07
 */
@import '../../../styles/ekb-colors';
@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.global_search_checkbox {
  &_newline {
    padding: 0 @space-6;
    margin-bottom: @space-4;
  }

  &_label {
    line-height: 0.7rem;
    margin-left: 4px;
  }
}

.bill_content {
  min-height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  :global {
    .am-list-view-scrollview-content {
      min-width: auto !important;
      left: 0;
      right: 0;
    }
  }
}

.bill-list-search-bar {
  z-index: 1;
  flex-shrink: 0;
  position: relative;
  width: 100%;
  height: 80px;
  margin: @space-4 0;
  padding: 0 @space-6;
  transition: all ease 0.3s;
  transform: translateZ(0);
}

.bill_content_header {
  position: relative;
  width: 100%;
  height: 120px;
  background: @color-white-2;
  z-index: 3;
  padding: 0 @space-6;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  :global {
    .header_left {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .type_name {
      cursor: pointer;
      color: var(--eui-text-title);
      font: var(--eui-font-body-b1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .active {
        color: @eui-ref-color-brand;
      }
      svg {
        margin-left: 8px;
        transition: 0.2s ease all;
        &.rotate {
          transform: rotate(180deg);
        }
      }
    }
    .type_name_small {
      color: #3a3f3f;
      font-size: 28px;
    }
    .select_count {
      color: var(--eui-text-caption);
      font: var(--eui-font-note-r2);
      margin-left: 16px;
    }
    .segmentation-wrapper {
      padding: @space-1;
      display: flex;
      background: rgba(29, 43, 61, 0.06);
      border-radius: @space-3;
      margin-left: @space-6;
      cursor: pointer;
      z-index: 6;
      .segmentation-item {
        .font-size-2;
        .font-weight-2;
        padding: @space-2 @space-5;
        text-align: center;
        color: @eui-sys-neutral-grey-2;
      }
      .segmentation-item-select {
        background: @color-white-1;
        border-radius: @space-2;
        color: var(--brand-base);
      }
    }
    .header_right {
      display: flex;
      cursor: pointer;

      div {
        color: @eui-ref-color-brand;
      }
      .edit {
        margin-left: 48px;
      }
    }
    div {
      display: flex;
      align-items: center;
      font-size: 28px;
      color: @gray-9;
      img {
        width: 40px;
        height: 40px;
      }
    }
  }
}

.bill_content_group {
  position: relative;
  width: 100%;
  height: 64px;
  background: @color-white-2;
  z-index: 1;
  padding: 0 @space-6;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  :global {
    .type_name {
      cursor: pointer;
      color: @eui-ref-color-grey;
      font-size: 28px;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .active {
        color: @eui-ref-color-brand;
      }

      .placeholder {
        color: rgba(0, 0, 0, 0.5);
      }

      svg {
        margin-left: 8px;
        transition: 0.2s ease all;

        &.rotate {
          transform: rotate(180deg);
        }
      }
    }
  }
}

.bill_content_header_search {
  width: 60px;
  height: 60px;
}

.bill_content_search {
  height: 60px;
}

.bill_content_filter, .bill_content_sort {
  width: 72px;
  height: 60px;
  display: flex;
  justify-content: flex-end;
}

.sort_panel_extra {
  color: var(--eui-text-title);
  font: var(--eui-font-head-b2);
}

.bill_content_header_batch {
  margin-left: 40px;
  height: 60px;
  :global {
    img {
      width: 40px;
      height: 40px;
      color: @color-black-1;
      transition: color ease 0.4s;
      &.disable {
        color: @color-black-4;
      }
    }
  }
}

.bill_content_body {
  display: flex;
  flex: 1;
}

.desc {
  transform: rotate(180deg);
}

.sorter {
  margin-left: 40px;
  :global {
    img {
      width: 40px;
      height: 40px;
    }
  }
}

.show_calendar {
  :global {
    padding: 24px 32px;
    border-top: 16px solid var(--eui-bg-base);
    font: var(--eui-font-body-b1);
    color: var(--eui-text-title);
    .eui-icon {
      margin-left: 8px;
      margin-bottom: 2px;
      font-size: 24px;
    }
  }
}
