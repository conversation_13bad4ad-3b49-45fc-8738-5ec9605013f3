<?xml version="1.0" encoding="UTF-8"?>
<svg width="14px" height="11px" viewBox="0 0 14 11" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 48.2 (47327) - http://www.bohemiancoding.com/sketch -->
    <title>对勾</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M9.21751442,10.7175144 L9.21751442,0.717514421 L11.2175144,0.717514421 L11.2175144,10.7175144 L11.2175144,12.7175144 L4.21751442,12.7175144 L4.21751442,10.7175144 L9.21751442,10.7175144 Z" id="path-1"></path>
    </defs>
    <g id="phone组件/单据列表单元/管理态-已选中时" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" transform="translate(-25.000000, -32.000000)">
        <g id="icon/对勾/16px" transform="translate(24.000000, 29.000000)">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <use id="Combined-Shape" fill="#FFFFFF" fill-rule="evenodd" transform="translate(7.717514, 6.717514) rotate(-315.000000) translate(-7.717514, -6.717514) " xlink:href="#path-1"></use>
            <g id="Group" stroke-width="1" fill-rule="evenodd" mask="url(#mask-2)" fill="#FFFFFF">
                <g id="_/icon颜色/白色">
                    <rect id="Rectangle-3" x="0" y="0" width="16" height="16"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>