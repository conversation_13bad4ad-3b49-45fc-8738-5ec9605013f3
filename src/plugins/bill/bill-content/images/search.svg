<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 48.2 (47327) - http://www.bohemiancoding.com/sketch -->
    <title>icon/搜索/20px</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M9,14 C11.7614237,14 14,11.7614237 14,9 C14,6.23857625 11.7614237,4 9,4 C6.23857625,4 4,6.23857625 4,9 C4,11.7614237 6.23857625,14 9,14 Z M14.6063847,13.1921711 L18.363961,16.9497475 L16.9497475,18.363961 L13.1921711,14.6063847 C12.0235906,15.4815965 10.5723351,16 9,16 C5.13400675,16 2,12.8659932 2,9 C2,5.13400675 5.13400675,2 9,2 C12.8659932,2 16,5.13400675 16,9 C16,10.5723351 15.4815965,12.0235906 14.6063847,13.1921711 Z" id="path-1"></path>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon/搜索/20px">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <use id="Combined-Shape" fill="#979797" fill-rule="nonzero" xlink:href="#path-1"></use>
            <g id="Group" mask="url(#mask-2)" fill="#595959">
                <g id="_/icon颜色/次级色">
                    <rect id="Rectangle-3" x="0" y="0" width="20" height="20"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>