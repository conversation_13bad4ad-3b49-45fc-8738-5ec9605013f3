<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 48.2 (47327) - http://www.bohemiancoding.com/sketch -->
    <title>icon/列表批量/20px</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M4,13 L4,16 L7,16 L7,13 L4,13 Z M4.82842712,5.94974747 L8.36396103,2.41421356 L9.77817459,3.82842712 L6.24264069,7.36396103 L4.82842712,8.77817459 L2,5.94974747 L3.41421356,4.53553391 L4.82842712,5.94974747 Z M2,11 L9,11 L9,18 L2,18 L2,11 Z M11,11 L18,11 L18,18 L11,18 L11,11 Z M11,2 L18,2 L18,9 L11,9 L11,2 Z M13,13 L13,16 L16,16 L16,13 L13,13 Z M13,4 L13,7 L16,7 L16,4 L13,4 Z" id="path-1"></path>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon/列表批量/20px">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <use id="Combined-Shape" fill="#979797" fill-rule="nonzero" xlink:href="#path-1"></use>
            <g id="Group" mask="url(#mask-2)" fill="#595959">
                <g id="_/icon颜色/次级色">
                    <rect id="Rectangle-3" x="0" y="0" width="20" height="20"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>