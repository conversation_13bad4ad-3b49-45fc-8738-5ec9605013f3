import { app } from '@ekuaibao/whispered'
import React from 'react'
const CardList_BillItem = app.require('@home5/elements/cards/mybill/cardList_myBill.CardList_BillItem')

export const BillItem5 = (props: any) => {
  const convertedProps: any = convertProps(props)

  return <CardList_BillItem data={convertedProps} />
}

// TODO: 我的单据截取数据 2019年04月19日
const response = {
  createTime: 1555556969747,
  form: {
    code: '*********',
    expenseMoney: {
      standard: '0.00',
      standardUnit: i18n.get('元'),
      standardScale: '2',
      standardSymbol: '¥',
      standardNumCode: '156'
    },
    specificationId: {
      id: 'ZBs8-v-lYknI00:bbc8049f02c2b7e888ab3085add75f441160b8c5',
      name: i18n.get('啊啊啊')
    },
    submitterId: {
      id: 'VMU6hlsYeg0000:0207433316953284',
      name: i18n.get('王路'),
      avatar: 'https://static.dingtalk.com/media/lADPDgQ9qchIn4fNAoDNAoA_640_640.jpg'
    },
    title: ''
  },
  formType: 'expense',
  id: '3uY8-ChTeYek00',
  // logs: [],
  sIndex: 3,
  state: 'draft'
}

const convertProps = (props: any) => {
  const newProps: {
    data: {
      flow?: { id: string; state: string }
      form?: { title: string; type: string; amount: string; specification: { name: string; id: string } }
      plan?: { type: 'NOMAL' | 'COUNTERSIGN'; urgent: boolean }
      operator?: { count: number; type: 'PERSON' | 'SYSTEM' | 'EBOT'; staff: { name: string } }
    }
  } = {
    data: {
      // TODO: 按照上面的『我的单据』的数据字段做过来，其他字段未知
      form: {
        title: props.form.title,
        type: props.formType,
        amount: props.form.expenseMoney.standard, // TODO: 似乎可以改组件内部渲染，因为这个数据应该已经做好了格式
        specification: {
          name: props.form.specificationId.name,
          id: props.form.specificationId.id
        }
      }
    }
  }
  return newProps
}
