/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/11 下午6:03
 */
import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import EkbPopup from '@ekuaibao/popup-mobile'
import { SearchBar } from '@hose/eui-mobile'
import styles from './bill-content.module.less'
import BillListWrapper from './BillListWrapper'
import { EnhanceConnect } from '@ekuaibao/store'
const EKBIcon = app.require('@elements/ekbIcon')
import { Fetch } from '@ekuaibao/fetch'
let timeout

@EnhanceConnect(
  state => ({
    descByUpdateTime: state['@bill'].BillContent ? state['@bill'].BillContent.descByUpdateTime : false,
    userInfo: state['@common'].me_info.staff
  }),
  undefined,
  '@bill/BillContent'
)
export default class BillContentPermission extends PureComponent {
  dataList = []
  static defaultProps = {
    showDropDown: false,
    closeDropDown: true,
    useNewItem: false
  }

  constructor(props) {
    super(props)
    this.state = this.initState(props.billType)
  }

  componentDidMount() {
    const { bus } = this.props
    api.dataLoader('@common.me_info').load()
    bus && bus.watch('get:selected:data', this.getSelectedData)
    bus && bus.on('list:update', this.listUpdate)
  }

  componentWillUnmount() {
    EkbPopup.hide()
    const { bus } = this.props
    bus && bus.un('get:selected:data', this.getSelectedData)
    bus && bus.un('list:update', this.listUpdate)
  }

  initState = (type = 'all', actionType, searchValue = '') => {
    if (actionType === 'cancel') {
      return {
        searchValue,
        filterBy: '', // 搜索功能生成的filterBy字符串
        selectedDataMap: {},
        selectAll: false,
        showDropDown: false,
        closeDropDown: true
      }
    }
    let { billType } = this.props
    const activePupUp = type ? type : billType ? billType : 'all'
    return {
      searchValue,
      filterBy: '',
      activePupUp,
      selectedDataMap: {},
      selectAll: false,
      showDropDown: false,
      closeDropDown: true
    }
  }

  listUpdate = type => {
    this.setState(this.initState(type))
  }

  getSelectedData = () => {
    const { selectedDataMap } = this.state
    return selectedDataMap
  }

  handleCheckboxChange = value => {
    const { checked, data } = value
    const { bus } = this.props
    const { selectedDataMap } = this.state
    const dataMap = { ...selectedDataMap }
    if (checked) {
      dataMap[data.id] = data
    } else {
      delete dataMap[data.id]
    }
    const selectAll = this.dataList.length === Object.keys(dataMap).length
    bus && bus.emit('select:data:change', dataMap)
    this.setState({ selectedDataMap: dataMap, selectAll })
  }

  handleChangeBillType = type => {
    const { onChangeBillType } = this.props
    EkbPopup.hide()
    if (type === 'cancel') {
      return null
    }

    this.setState(
      {
        activePupUp: type,
        showDropDown: false,
        searchValue: '',
        filterBy: ''
      },
      () => setTimeout(() => this.setState({ closeDropDown: true }), 200)
    )

    return onChangeBillType(type, '', true)
  }

  handleSorter = () => {
    const { onChangeBillType, descByUpdateTime, searchFrom } = this.props
    const { activePupUp, filterBy } = this.state

    this.props.setState({
      descByUpdateTime: !descByUpdateTime
    })
    onChangeBillType(activePupUp, filterBy, searchFrom)
  }

  renderHeader = () => {
    const { descByUpdateTime } = this.props
    const { showDropDown } = this.state
    const editIconClass = !showDropDown ? '' : 'disable'
    return (
      <div className={styles.bill_content_header} style={{ justifyContent: 'flex-end' }}>
        <div className="header_right">
          <div
            className={descByUpdateTime ? `${styles.sorter} ${styles.desc}` : styles.sorter}
            onClick={this.handleSorter}
          >
            <EKBIcon name="#EDico-sort1" className={editIconClass} />
          </div>
          <div onClick={this.props.onRefresh}>刷新</div>
        </div>
      </div>
    )
  }

  renderContent = () => {
    const {
      dataSource,
      emptyText,
      onHandleClick,
      footer,
      onEndReached,
      onEndReachedThreshold,
      approveShowState,
      isAlwaysPrint,
      useNewItem
    } = this.props
    let { selectedDataMap, searchValue } = this.state
    this.dataList = dataSource
    const emptyTextValue = searchValue ? i18n.get('没有找到您所要的结果') : i18n.get(emptyText)

    return (
      <BillListWrapper
        hasSearchBar={true}
        isAlwaysPrint={isAlwaysPrint}
        searchValue={searchValue}
        footer={footer}
        selectAble={false}
        avatarClick={() => {}}
        selectedDataMap={selectedDataMap}
        checkBoxChange={this.handleCheckboxChange}
        itemClick={onHandleClick}
        billList={dataSource}
        emptyText={emptyTextValue}
        onEndReached={onEndReached}
        approveShowState={approveShowState}
        onEndReachedThreshold={onEndReachedThreshold}
        useNewItem={useNewItem}
      />
    )
  }

  searchMap = val => {
    const { onChangeBillType } = this.props
    const { activePupUp } = this.state
    let filterBy = ''
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage === 'en-US'
    if (lang) {
      filterBy = `&&(lower(flowId.form.title).containsIgnoreCase(lower("${val}")) || lower(flowId.form.code).containsIgnoreCase(lower("${val}"))||lower(flowId.form.submitterId.name).containsIgnoreCase(lower("${val}")))`
    } else {
      filterBy = `&&(flowId.form.title.containsIgnoreCase("${val}") || flowId.form.code.containsIgnoreCase("${val}")||flowId.form.submitterId.name.containsIgnoreCase("${val}"))`
    }
    onChangeBillType(activePupUp, filterBy)
    this.setState({ filterBy })
  }

  handleSearchInPage = val => {
    this.setState({ searchValue: val })
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
    timeout = setTimeout(() => {
      this.searchMap.call(this, val)
    }, 400)
  }

  handleClear = () => {
    this.handleSearchInPage('')
  }

  renderSearchBar() {
    const { searchValue } = this.state
    let headerStyle = {}
    const placeholder = i18n.get('搜索标题、单号或提交人')
    return (
      <SearchBar
        className={styles['bill-list-search-bar']}
        style={headerStyle}
        value={searchValue}
        placeholder={placeholder}
        onChange={this.handleSearchInPage.bind(this)}
        onClear={this.handleClear}
        onCancel={this.handleClear}
      />
    )
  }

  render() {
    let { className } = this.props
    let cls = className ? `${styles.bill_content} ${className}` : `${styles.bill_content}`
    return (
      <div className={cls}>
        {this.renderHeader()}
        {this.renderSearchBar()}
        {this.renderContent()}
      </div>
    )
  }
}

BillContentPermission.defaultProps = {
  activeType: 'all', //默认单据类型
  dataSource: [], //数据源
  emptyText: i18n.get('暂无单据'), //空数据时显示的文字提示
  searchFrom: 'home', //从哪个几面跳转到搜索
  onChangeBillType: () => {},
  onHandleClick: () => {}
}
