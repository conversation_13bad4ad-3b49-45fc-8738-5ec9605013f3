/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/14 下午1:52
 */
import React, { Component } from 'react'
import { app } from '@ekuaibao/whispered'
import styles from './bill-list.module.less'
import BillItem from './bill-item-new'
import BillFilter from './bill-filter'
import { connect } from '@ekuaibao/mobx-store'
const CardList_Bill = app.require('@home5/elements/cards/mybill/cardList_myBill.CardList_Bill')
const BillQuickEntrance = app.require('@home5/elements/cards/mybill/cardList_myBill.BillQuickEntrance')
const BillListWrapper = app.require('@home5/elements/cards/cardItems/BillList')
const EmptyWidget = app.require('@home5/EmptyWidget')
const { handleBillClickItem } = app.require('@home5/util/billItemHelper')

import get from 'lodash/get'
import classNames from 'classnames'

const COUNT = 10

// @ts-ignore
@connect(store => ({
  paidList: get(store, '<EMAIL>')
}))
export default class BillList extends Component {
  state = { page: 1 }

  handleLoadMore = () => {
    const { customLoadMore } = this.props
    if (customLoadMore) {
      customLoadMore()
      return
    }

    let { page } = this.state
    this.setState({ page: 0 }, () => {
      this.setState({ page: page + 1 })
    })
  }

  getLoadMoreVisible = () => {
    let { page } = this.state
    const { dataSource, customLoadMore, dataSourceTotal } = this.props
    if (customLoadMore) return dataSource.length < dataSourceTotal
    return dataSource.length > page * COUNT ? true : page === 1 && dataSource.length > COUNT
  }

  renderList(dataSource = [], onHandleClick) {
    const { style, searchFrom, searchValue, customLoadMore, useNewStyle } = this.props
    let { page } = this.state
    let data = customLoadMore ? dataSource : dataSource.slice(0, page * COUNT)
    let showMore = this.getLoadMoreVisible()
    const newListStyle = window.isNewHome ? {} : { marginTop: 0 }
    let listComponent
    if (data[0] && data[0].plan) {
      // myV2
      if (useNewStyle) {
        listComponent = (
          <BillListWrapper
            list={data}
            style={{ marginTop: 0 }}
            searchValue={searchValue}
            applyType={'list'}
            onClickItem={handleBillClickItem}
            showEntrustMark={true}
          />
        )
      } else {
        listComponent = <CardList_Bill list={data} style={newListStyle} searchValue={searchValue} />
      }
    } else {
      listComponent = data.map((el, i) => (
        <BillItem
          searchFrom={searchFrom}
          onHandleClick={onHandleClick}
          key={i}
          isShowState={true}
          data={el}
          searchValue={searchValue}
        />
      ))
    }
    return (
      <div style={style} className="bill-list-wrapper">
        {listComponent}
        {showMore && (
          <div className="load-more" onClick={this.handleLoadMore}>
            {i18n.get('加载更多')}
          </div>
        )}
      </div>
    )
  }

  render() {
    let {
      dataSource,
      onHandleClick,
      paidList,
      searchValue,
      menuList,
      showFilter,
      filterList,
      onChangeBillFilter,
      applyNewFilter
    } = this.props
    const useFilter = showFilter ? !applyNewFilter : false
    const style = searchValue === '' ? {} : { height: 0, padding: 0, display: 'none' }
    return (
      <div className={classNames(styles.bill_list, { [styles.bill_list_new_style]: applyNewFilter })}>
        <BillQuickEntrance paidList={paidList} style={style} newStyle />
        {useFilter && (
          <BillFilter groupList={menuList} filterList={filterList} onChangeBillFilter={onChangeBillFilter} />
        )}
        {dataSource.length ? (
          this.renderList(dataSource, onHandleClick)
        ) : (
          <EmptyWidget size={200} type="billList" tips="暂无单据" />
        )}
      </div>
    )
  }
}

BillList.defaultProps = {
  dataSource: [],
  onHandleClick: () => {},
  emptyText: i18n.get('暂无单据')
}
