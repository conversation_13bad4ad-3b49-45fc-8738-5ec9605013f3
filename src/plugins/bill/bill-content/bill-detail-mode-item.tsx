/**************************************
 * Created By LinK On 2023/8/3 21:25.
 **************************************/
import React, { useEffect, useState } from "react";
import './bill-detail-mode-item.less'
import { transformData } from "../../home5/elements/cards/cardItems/transform";
import Highlighter from "react-highlight-words";
import { FlowTypeEnum } from "../../home5/elements/cards/cardItems/enums";
import classNames from "classnames";
import { Checkbox } from "@hose/eui-mobile/2x";
import { get } from "lodash";
import { app } from "@ekuaibao/whispered";
import { getSpecificationName } from "../utils/billUtils";
import { SourceSignEnum } from "../../../lib/enums";

const EKBIcon = app.require<any>('@elements/ekbIcon')
const Money = app.require<any>('@elements/puppet/Money')

interface ICardListBillItem {
  id: string
  data: any
  searchValue?: string
  needOperatorFormPlan?: boolean
  isAlwaysPrint?: boolean
  flowType?: FlowTypeEnum
  applyType?: 'list' | 'card'
  onClickItem?: (data: any) => void
  selectAble?: boolean
  checked?: boolean
  onCheckedChange?: (params: { checked: boolean; data: any }) => void
  bus?: any
}


const BillDetailModeItem: React.FC<ICardListBillItem> = props => {
  const {
    data,
    searchValue,
    needOperatorFormPlan,
    isAlwaysPrint = false,
    flowType,
    applyType = 'card',
    selectAble,
    checked,
    onClickItem,
    onCheckedChange,
    bus
  } = props

  const [isNotAllowBatchApprove, setIsNotAllowBatchApprove] = useState(false)
  const refreshNotAllowBatchApprove = () => {
    if (bus && bus.has('get:bill:isNotAllowBatchApprove')) {
      if (data?.flowId?.backlogId) {
        bus.invoke('get:bill:isNotAllowBatchApprove', data?.flowId?.backlogId).then((isNotAllow: boolean) => {
          if (isNotAllow) {
            handleCheckedChange(false)
          }
          setIsNotAllowBatchApprove(isNotAllow)
        })
      }
    }
  }

  useEffect(() => {
    bus && bus.on('bill:item:refresh:notAllowBatchApprove', refreshNotAllowBatchApprove)
    return () => {
      bus && bus.un('bill:item:refresh:notAllowBatchApprove', refreshNotAllowBatchApprove)
    }
  }, [])

  const {
    payingFailure,
    urgent,
    title,
    amount,
    auto,
    alterFlag,
    subsidyGeneration,
    dateStr,
    sourceSign
  } = transformData(data, flowType)

  const feeTypeName = get(data, 'form.feeTypeId.name', '')
  const state = get(data, 'form.state','')
  const type = get(data, 'form.stage','')
  const code = get(data, 'form.flow.form.code', '')
  const billSpecificationName = getSpecificationName(data?.form?.flowSpecificationId)
  const submitterName = get(data, 'form.submitterId.name', '')

  const handleCheckedChange = (checked: boolean) => {
    onCheckedChange && onCheckedChange({ checked, data })
  }

  let t = title || i18n.get('[无标题]')
  if (searchValue) {
    t = (
      <Highlighter
        highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
        searchWords={[searchValue]}
        textToHighlight={t}
      />
    )
  }

  const handleClickItem = () => {
    if (onClickItem) {
      onClickItem({ id: data.id, formType: type, state, isAlwaysPrint, data })
      app?.logger?.info('待审批从明细维度查看费用明细详情', { id: data.id, formType: type, state, isAlwaysPrint, data })
    }
  }

  let showMark = false
  const samePerson = data?.ownerId === data?.form?.submitterId?.id
  if (!samePerson) showMark = true

  return (
    <div
      className={classNames('card-bill-item-wrapper', {
        'card-bill-item-wrapper-card': applyType === 'card',
        'card-bill-item-wrapper-list': applyType === 'list',
        'card-bill-item-wrapper-selected': checked
      })}
    >
      <div className="card-bill-item-left">
        {selectAble && (
          <Checkbox className="check-box" checked={checked} key={data.id} onChange={handleCheckedChange} />
        )}
      </div>
      <div className="card-bill-item-right" onClick={handleClickItem}>
        <div className={'bill-info-title-wrapper'}>
          <div className={'left'}>
            {urgent && <div className="bill-info-urgent">{i18n.get('[急]')}</div>}
            {payingFailure && <div className="bill-info-pay-failure">{`[${i18n.get('支付失败')}]`}</div>}
            {alterFlag && <div className="bill-info-tag-placeholder">[变更]</div>}
            {(auto || subsidyGeneration) && <div className="bill-info-tag-placeholder">[自动创建]</div>}
            {sourceSign === SourceSignEnum.SYSTEM && (
              <div className="bill-info-tag-placeholder">{`[${i18n.get('系统')}]`}</div>
            )}
            <div className="ekb-highlight-box">
              {showMark && <span className="entrust-mark">{i18n.get('[委托]')}</span>}
              <div className="bill-info-title">
                <span>{feeTypeName}</span>
              </div>
            </div>
          </div>
        </div>
        {!!submitterName?.length ? (
          <div className="bill-info-staff-wrapper">
            <div className="left">
              <div className="bill-info-staff">{`${submitterName}`}</div>
              {!!billSpecificationName && (
                <div className="approve-specification">
                  <span className="mood-words">{i18n.get('的')}</span>
                  {billSpecificationName}
                  <span className="ml-8">{t}</span>
                </div>
              )}
            </div>
          </div>
        ) : null}
        <div className="bill-info">
          <div className="bill-info-code">
            {dateStr}
            {` ${code}`}
            {window.isNewHome && get(data, 'flow.state') === 'paid' && (
              <span className="cardList_BillItem-status">{i18n.get('未确认')}</span>
            )}
          </div>
          <div className="bill-info-money-wrapper">
            {amount && Number(amount?.standard || amount) !== 0 ? (
              <Money value={amount} isShowSymbol={false} isShowStrCode className="bill-info-money" />
            ) : (
              <span className="bill-info-money-no-money">{i18n.get('暂无金额')}</span>
            )}
          </div>
        </div>
        {selectAble && isNotAllowBatchApprove && (
          <div className="not-allow-batch-approve-tip">{i18n.get('该单据不可批量审批')}</div>
        )}
      </div>
      {applyType === 'list' && <div className="bill-info-divider" />}
    </div>
  )
}

export default BillDetailModeItem