import { app } from '@ekuaibao/whispered'
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/14 下午1:59
 */

import styles from './bill-item.module.less'
import React from 'react'
import classnames from 'classnames'
const Money = app.require('@elements/puppet/Money')
import SVG_BILL_EXPENSE from './images/bill-type-expense.svg'
import SVG_BILL_LOAN from './images/bill-type-loan.svg'
import SVG_BILL_PERMIT from './images/permit.png'
import SVG_BILL_REQUISITION from './images/bill-type-requisition.svg'
const SVG_FAIL = app.require('@images/exclamation-circle.svg')
const { EXPENSE_STATE } = app.require('@basic-elements/basic-state-const')

let iconMap = {
  expense: { label: '报销', color: 'cyan' },
  loan: { label: '借款', color: 'warning' },
  requisition: { label: '申请', color: 'info' },
  permit: { label: '授权', color: 'sub' },
}

let moneyMap = {
  expense: 'expenseMoney',
  loan: 'loanMoney',
  requisition: 'requisitionMoney',
  permit: 'permitMoney'
}

import isUrgent from './bill-item.isUrgent'
import moment from 'moment'

export { isUrgent }

const BillItem = props => {
  let { data, onHandleClick, isShowIcon, style, standardStrCode } = props
  let { logs = [] } = data
  //加急
  const urgent = isUrgent(logs)
  const isPaid = data.state === 'paid' || data.state === 'archived'
  let lastLast = (logs.length && logs[logs.length - 1]) || {}
  let { action } = lastLast
  return (
    <div onClick={onHandleClick.bind(this, data)} className={styles.bill_item} style={style}>
      <div className={styles.bill_item_top}>
        <div className={styles.bill_item_title}>
          {renderIcon(data.formType, isShowIcon)}
          <div className='title-wrap'>{i18n.get(data.form.title) || i18n.get('[无标题]')}</div>
        </div>
        <div className={styles.bill_item_money}>{renderMoney(data, standardStrCode)}</div>
      </div>

      <div className={styles.bill_item_bottom}>
        {renderBillInfo(data)}
        {action === 'freeflow.failure' ? renderFailure() : renderState(data.state)}
      </div>
      <div className={styles.bill_item_info} style={!urgent ? { marginBottom: 12 + 'px' } : {}}>
        {renderOtherInfo(data)}
      </div>
      {urgent && renderUrgent(isPaid)}
    </div>
  )
}

const renderOtherInfo = data => {
  return <>
    <span>{data.code}</span>
    <span className='separator' />
    <span>{moment(data.submitDate).format('YYYY-MM-DD')}</span>
  </>
}

const renderBillInfo = data => {
  let { form } = data
  return (
    <div className={styles.bill_item_spe}>
      {form.submitterId && typeof form.submitterId === 'object' && <span>{`${form.submitterId.name} | `}</span>}
      <span>{i18n.currentLocale === 'en-US' ? form.specificationId?.enName || form.specificationId?.name : form.specificationId.name}</span>
    </div>
  )
}

const renderMoney = (data, standardStrCode) => {
  const money = data.form[moneyMap[data.formType]]
  const noMoney = data.form[`${data.formType}Money`]
  return (
    <>
      {noMoney && Number(noMoney) !== 0 ? (
        <div className={styles.money_content}>
          <span className="money-name">{i18n.get('本位币')}</span>
          <Money
            value={money}
            isShowSymbol={false}
            isShowStrCode={true}
            currencyStrCode={standardStrCode}
          />
        </div>
      ) : (
        <span className={styles.money_gray}>{i18n.get('暂无金额')}</span>
      )}
    </>
  )
}

export const renderState = state => {
  if (state === 'PROCESS' || state === 'MANUAL_CLOSED' || state === 'FORCE_CLOSED') {
    state = 'paid'
  }
  let label
  if (EXPENSE_STATE[state]) {
    label = EXPENSE_STATE[state].label
  }

  if (!label) {
    return null
  }

  return (
    <div className={styles.bill_item_state}>
      <span className={EXPENSE_STATE[state].colorKey} />
      <span className="text">{i18n.get(label)}</span>
    </div>
  )
}

const renderUrgent = isPaid => {
  return <div className={isPaid ? styles.bill_unurgent : styles.bill_urgent}>{i18n.get('加急')}</div>
}

const renderIcon = (formType, isShowIcon) => {
  if (!isShowIcon) return null
  const iconInfo = iconMap[formType]
  const { label, color } = iconInfo
  return <span className={classnames(styles.bill_item_icon, color)}>
    {i18n.get(label)}
  </span>
}

export const renderFailure = () => {
  return (
    <div className="horizontal">
      <img src={SVG_FAIL} alt="" />
      <div className="fs-12 color-red-6">{i18n.get('支付失败')}</div>
    </div>
  )
}

BillItem.defaultProps = {
  isShowIcon: true
}

export default BillItem
