import { app } from '@ekuaibao/whispered'
import React from 'react'
import { Checkbox } from 'antd-mobile'
import styles from './MessageListItem.module.less'
import classNames from 'classnames'
const ListItem = app.require('@home5/elements/cards/auditpending/CardListItem')
const BillItem = app.require('@home5/elements/cards/cardItems/BillItem')
const CardList_BillItem = app.require('@home5/elements/cards/mybill/cardList_myBill.CardList_BillItem')
const PaymentReviewListItem = app.require('@home5/elements/cards/paymentReview/CardListItem')
import { app as api } from '@ekuaibao/whispered'
const SVG_AVATAR_NULL = app.require('@images/avatar-null.svg')
const NEW_SVG_AVATAR_NULL = app.require('@images/new/avatar-null.svg')
const EYP_SVG_AVATAR = app.require('@images/epy-people.svg')
import EkbIcon from '../../../elements/ekbIcon'
import BillDetailModeItem from './bill-detail-mode-item'

export default class extends React.PureComponent {
  containerEl = React.createRef()

  componentDidMount() {
    this.centerActiveItem()
  }

  centerActiveItem() {
    const { entries, index } = api.history
    if (entries.length - 1 > index) {
      const nextHistory = entries[index + 1]
      const match = nextHistory.pathname.match(/\/approving\/expense\/([\w_]+)\/approving/)
      if (match && this.props.data && this.props.data.backlogId === match[1]) {
        this.containerEl.current.scrollIntoView && this.containerEl.current.scrollIntoView()
      }
    }
  }

  handleCheckedChange = params => {
    const { checkBoxChange } = this.props
    checkBoxChange(params)
  }

  render() {
    const {
      data,
      selectAble = false,
      checked,
      itemClick,
      checkBoxChange,
      avatarClick,
      searchValue,
      approveShowState,
      useNewItem,
      isAlwaysPrint,
      needOperatorFormPlan,
      searchFrom,
      closeGroupSet = new Set(),
      EUICheckbox,
      hideAvatar = false,
      flowType,
      useNewStyle,
      inFeeTypeMode,
    } = this.props

    if (!data) {
      return null
    }
    if (data?.isGroupItem) {
      return (
        <div
          key={data?.id}
          className={styles.item_group}
          onClick={() => {
            itemClick?.(data)
          }}
        >
          <div>{data?.label}</div>
          <EkbIcon
            style={{ marginRight: '16px' }}
            name={closeGroupSet.has(data?.id) ? '#EDico-up-default' : '#EDico-down-default'}
            className="icon-real"
          />
        </div>
      )
    }
    if (closeGroupSet.has(data?.groupId)) {
      return null
    }
    let submitterId = data?.form?.submitterId
    const onChange = () => {
      const params = { checked: !checked, data }
      checkBoxChange(params)
    }
    const handleAcatarClick = () => {
      avatarClick && avatarClick(data)
    }

    let avatarComponent = null
    let wrapperChecked = styles.item_wrapper_checked
    if (submitterId && !hideAvatar) {
      const { avatar } = submitterId
      if (searchFrom === 'recycle') {
        wrapperChecked = styles.item_wrapper_recycle_checked
        avatarComponent = (
          <div className="avatar-recycle" onClick={handleAcatarClick}>
            <img src={avatar || NEW_SVG_AVATAR_NULL} alt="" />
          </div>
        )
      } else if (!hideAvatar) {
        avatarComponent = (
          <div className="avatar" onClick={handleAcatarClick}>
            <img src={avatar || SVG_AVATAR_NULL} alt="" />
          </div>
        )
      }
    }

    if (data?.payerId && searchFrom === 'payment-review') {
      const avatar = data.payerId?.avatar

      avatarComponent = (
        <div className="avatar" onClick={handleAcatarClick}>
          <img src={avatar || EYP_SVG_AVATAR} alt="" />
        </div>
      )
    }

    if (!data?.id && data?.flow && data?.flow?.id) {
      data.id = data.flow.id
    }
    if (!data?.formType && data?.form && data?.form?.type) {
      data.formType = data.form.type
    }
    const BillItemComponent = inFeeTypeMode ? BillDetailModeItem : BillItem
    let listItem = useNewStyle ? (
      <BillItemComponent
        {...this.props}
        hideAvatar
        approveShowState={!!approveShowState}
        searchValue={searchValue}
        onClickItem={itemClick.bind(this, data)}
        data={flowType === 'backlog' ? { flowId: data } : data}
        extraClassName={'item-style-bill'}
        applyType={'list'}
        flowType={flowType}
        selectAble={selectAble}
        onCheckedChange={this.handleCheckedChange}
      />
    ) : (
      <ListItem
        {...this.props}
        hideAvatar
        approveShowState={!!approveShowState}
        searchValue={searchValue}
        onClick={itemClick.bind(this, data)}
        el={{ flowId: data }}
        extraClassName={'item-style-bill'}
      />
    )
    if (useNewItem) {
      listItem = (
        <CardList_BillItem
          isAlwaysPrint={isAlwaysPrint}
          needOperatorFormPlan={needOperatorFormPlan}
          searchValue={searchValue}
          data={data}
        />
      )
    }
    if (searchFrom === 'payment-review') {
      listItem = (
        <PaymentReviewListItem
          {...this.props}
          hideAvatar
          approveShowState={!!approveShowState}
          searchValue={searchValue}
          onClick={itemClick.bind(this, data)}
          el={data}
          extraClassName={'item-style-bill'}
        />
      )
    }

    const renderCheckbox = () => {
      if (useNewStyle) {
        return null
      }
      if (!selectAble) {
        return avatarComponent
      }
      if (searchFrom === 'recycle') {
        const disabledWhenStateInNullify = data.state === 'nullify'
        return <EUICheckbox className="checkBox" disabled={disabledWhenStateInNullify} checked={checked} key={data.id} onChange={onChange} />
      }
      return <Checkbox className="checkBox" checked={checked} key={data.id} onChange={onChange} />
    }

    return (
      <div
        ref={this.containerEl}
        className={
          classNames(styles.item_wrapper, {
            [wrapperChecked]: useNewStyle ? false : checked,
            [styles['payment-review-item']]: searchFrom === 'payment-review'
          }) + ' item_wrapper_forFix'
        }
      >
        <div className={`item_inner ${useNewStyle ? '' : 'item_inner-normal'}`}>
          {renderCheckbox()}
          <div className="item">{listItem}</div>
        </div>
      </div>
    )
  }
}
