@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.card-list-bill-container {
  margin-top: @space-5;
  background: var(--eui-bg-body);
}

.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-bill-item-wrapper {
  display: flex;
  flex-direction: row;
  position: relative;

  &:active {
    background-color: @color-bg-1;
  }
  &:last-child {
    margin-bottom: 0;
    .bill-info-money-wrapper {
      bottom: 0;
    }
  }

  .card-bill-item-left {
    display: flex;
    align-items: flex-start;
    .check-box {
      margin-right: @space-5;
      margin-top: 10px;
    }
  }

  .card-bill-item-right {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    .bill-info-title-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        display: flex;
        overflow: hidden;
        .ekb-highlight-box {
          display: flex;
          align-items: center;

          .entrust-mark {
            color: var(--eui-function-info-600);
            font: var(--eui-font-body-r1);
            margin-right: 2px;
          }
        }
        .bill-info-title {
          overflow: hidden;
          flex: 1;
          display: flex;
          align-items: center;
          span {
            font: var(--eui-font-body-b1);
            color: var(--eui-text-title);
            user-select: none;
            .ellipsis();
          }
        }
        .bill-info-urgent {
          margin-right: @space-2;
          flex-shrink: 0;
          font: var(--eui-font-body-r1);
          color: var(--eui-function-danger-500);
        }
        .bill-info-pay-failure {
          margin-right: @space-2;
          flex-shrink: 0;
          font: var(--eui-font-body-r1);
          color: var(--eui-function-danger-500);
        }
        .bill-info-tag-placeholder {
          flex-shrink: 0;
          margin-right: @space-2;
          font: var(--eui-font-body-r1);
          color: var(--eui-text-placeholder);
        }
        .desc {
          flex-shrink: 0;
          margin-left: @space-1;
          font: var(--eui-font-body-r1);
          color: var(--eui-function-warning-500);
        }
      }
      .right {
        display: flex;
        flex-shrink: 0;
        .operator-span {
          display: inline-block;
          max-width: 240px;
        }
        .bill-state {
          font: var(--eui-font-note-r2);
          color: var(--eui-text-white);
          padding: 2px 8px;
          border-radius: 8px;
          flex-shrink: 0;
        }
        .bill-state-red {
          //驳回
          color: var(--eui-function-danger-600);
          background: var(--eui-function-danger-100);
        }
        .bill-state-blue {
          //审批中
          color: var(--eui-function-info-600);
          background: var(--eui-function-info-100);
        }
        .bill-state-orange {
          color: var(--eui-decorative-neu-600);
          background: var(--eui-decorative-neu-100);
        }
        .bill-state-green {
          //已完成
          color: var(--eui-function-success-600);
          background: var(--eui-function-success-100);
        }
      }
      .right-card {
        position: absolute;
        right: 0;
        top: 0;
        border-radius: 0 4px;
      }
      .right-list {
        border-radius: 4px;
      }
    }
    .bill-info-staff-wrapper {
      margin-top: @space-3;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        display: flex;
        overflow: hidden;
        .bill-info-staff {
          font: var(--eui-font-note-b2);
          color: var(--eui-primary-pri-500);
          flex-shrink: 0;
        }
        .bill-info-addition-staff {
          margin-left: @space-2;
          font: var(--eui-font-note-r2);
          color: var(--eui-primary-pri-500);
          flex-shrink: 0;
        }
        .approve-desc {
          margin-left: @space-2;
          font: var(--eui-font-note-r2);
          color: var(--eui-text-title);
        }
        .approve-specification {
          margin-left: @space-2;
          font: var(--eui-font-note-r2);
          color: var(--eui-text-title);
          .ellipsis();
          .mood-words {
            margin-right: @space-2;
          }
        }
      }
      .right {
        display: flex;
        .bill-info-expedited {
          color: @color-error-1;
          flex-shrink: 0;
        }
      }
    }

    .bill-info {
      margin-top: @space-3;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .bill-info-code {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-placeholder);
        .cardList_BillItem-status {
          height: 44px;
          font: var(--eui-font-note-r2);
          margin-left: @space-2;
          background-color: var(--eui-function-warning-50);
          color: var(--eui-function-warning-300);
          padding: 0 @space-2;
          border-radius: @radius-1;
        }
      }
      .bill-info-money-wrapper {
        .bill-info-money {
          font: var(--eui-num-body-b1);
          color: var(--eui-text-title);
          .currency {
            font: var(--eui-font-note-b2);
            color: var(--eui-text-title);
          }
        }
        .bill-info-money-no-money {
          font: var(--eui-font-note-r2);
          color: var(--eui-text-placeholder);
          user-select: none;
        }
      }
    }
    .not-allow-batch-approve-tip {
      color: var(--eui-function-danger-500);
      font: var(--eui-font-note-b2);
      margin: 6px 0 0;
    }
  }
}

.card-bill-item-wrapper-card {
  margin-top: @space-4;
  padding: @space-4 @space-5;
  border-radius: 6px;
  background-color: var(--eui-bg-body-overlay);
}

.card-bill-item-wrapper-list {
  padding: @space-6;
  .bill-info-divider {
    position: absolute;
    left: @space-6;
    right: @space-6;
    bottom: 0;
    border-bottom: 2px solid var(--eui-line-divider-default);
  }
}

.card-bill-item-wrapper-selected {
  background: var(--eui-fill-selected);
}
