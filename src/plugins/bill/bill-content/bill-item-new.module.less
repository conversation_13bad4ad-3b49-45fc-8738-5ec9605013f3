@import "../../../styles/ekb-colors";

.bill_item {
  background: @gray-1;
  border-bottom: 2px solid @gray-3;
  padding-right: 32px;
  padding-bottom: 24px;
  font-size: 0;

  .bill_urgent {
    flex-shrink: 0;
    width: 30px;
    height: 30px;
    fill: #f5222d;
    margin-right: 8px;
  }

  .bill_unurgent {
    flex-shrink: 0;
    width: 30px;
    height: 30px;
    fill: @gray-7;
    margin-right: 8px;
  }
}

.bill_item_top {
  display: flex;
  align-items: center;
  margin-top: 24px;
  height: 44px;
  position: relative;
}

.bill_item_title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 28px;
  color: @gray-9;
  height: 100%;
  :global {
    .text {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.bill_item_title_content {
  max-width: 440px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bill_item_money {
  flex-shrink: 0;
  //padding-bottom: 24px;
  :global {
    & .bill-item-money {
      color: @black-85;
      .currency {
        font-size: 28px !important;
        margin-right: 4px;
      }
      .value {
        font-size: 40px !important;
        font-weight: 500;
      }
    }
  }

}

.bill_item_bottom {
  display: flex;
  margin-top: 0.04rem;
  justify-content: space-between;
}

.bill_item_spe {
  font-size: 24px;
  color: @black-45;
  :global {
    span {
      &:first-child {
        margin-right: 8px;
      }
    }
  }
}

.money_gray {
  display: block;
  font-size: 28px;
  text-align: left;
  color: rgba(0, 0, 0, 0.45);
}

.bill_item_state {
  display: flex;
  align-items: center;
  font-size: 24px;
  color: @gray-6;
  :first-child {
    margin-right: 12px;
    width: 16px;
    height: 16px;
    border-radius: 16px;
  }
  :global {
    .text {
      font-size: 28px;
      color: @black-85;
    }
  }
}

.bill_item_spec {
  display: inline-block;
  max-width: 100%;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  margin-top: 12px;
  :global {
    .text_box {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: @black-65;
      font-size: 24px;
      padding: 2px 16px;
    }
  }
  //}
}

.bill_item_count {
  display: flex;
  align-items: center;
  font-size: 28px;
  color: #fa8c16;
}