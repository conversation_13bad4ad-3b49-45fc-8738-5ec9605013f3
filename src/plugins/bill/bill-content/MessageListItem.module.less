@import "../../../styles/ekb-colors";
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.item_wrapper {
  display: flex;
  align-items: flex-start;

  :global {
    .item_inner {
      display: flex;
      align-items: flex-start;
      width: 100%;
      .eui-checkbox.eui-checkbox-checked .eui-checkbox-icon {
        background-color: var(--eui-primary-pri-500);
      }
    }
    .item_inner-normal{
      margin: 0 32px;
      border-bottom: 2px solid rgba(39, 46, 59, 0.1);
    }
    .checkBox {
      margin-top: 36px;
    }

    .avatar {
      margin-top: 32px;
      width: 64px;
      height: 64px; // 兼容图片失效（有地址但是未返回图片）
      border: 2px solid #00000040;
      border-radius: 50%;
      flex-shrink: 0;
      overflow: hidden;

      img {
        display: block;
        width: 100%;
        border-radius: 100%;
      }
    }

    .avatar-recycle {
      margin-top: 32px;
      width: 64px;
      height: 64px;
      border-radius: 8px;
      flex-shrink: 0;
      overflow: hidden;
      img {
        display: block;
        width: 100%;
      }
    }

    .item:first-child {
      padding-left: 0;
    }

    .item {
      flex: 1;
      overflow: hidden;
      padding-left: 32px;

      .item-style-bill {
        margin-top: 0;
        padding-left: 0;

        .bill-content {
          margin-left: 0;
        }
      }
    }
  }
}

.payment-review-item {
  position: relative;
  padding-left: 0;
  min-height: 206px;

  &.item_wrapper_checked {
    background-color: #f8f8f9;
    div {
      background-color: #f8f8f9;
    }
  }

  :global {
    .checkBox {
      margin-top: 38px;
      padding-left: 32px;
      .am-checkbox {
        width: 32px;
        height: 155px;
        vertical-align: unset;

        .am-checkbox-inner {
          width: 32px;
          height: 32px;
          border-radius: 4px;
          &::after {
            top: 3px;
            right: 10px;
            width: 8px;
            height: 12px;
          }
        }
      }
    }

    .avatar {
      margin-left: 32px;
      border-radius: 8px;
      border: none;

      img {
        border-radius: 8px;

      }
    }
  }
}

.item_wrapper_recycle_checked {
  background-color: @eui-sys-thin-brand;
}

.item_wrapper_checked {
  background-color: #F5F5F5;
  div {
    background-color: #F5F5F5;
  }
}

.item_group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px 0 32px;
  height: 88px;
  color: #000000;
  font-size: 28px;
  font-weight: 500;
  border-bottom: 2px solid rgba(39, 46, 59, 0.1);
}
