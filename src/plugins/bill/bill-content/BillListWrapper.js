import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const ListViewWrapper = app.require('@elements/listview/ListViewWrapper')
import BillItemWrapper from './BillItemWrapper'
import { Checkbox } from '@hose/eui-mobile'
const EmptyWidget = app.require('@home5/EmptyWidget')
const SkeletonListEUI = app.require('@home5/SkeletonList')

const noop = () => {}
export default class BillListWrapper extends PureComponent {
  static defaultProps = {
    hasSearchBar: false,
    selectAble: false,
    selectedDataMap: {},
    checkBoxChange: noop,
    itemClick: noop,
    avatarClick: noop,
    useNewItem: false,
    needOperatorFormPlan: false
  }

  row = rowData => {
    return (
      <BillItemWrapper
        EUICheckbox={Checkbox}
        {...this.props}
        hasSearchBar={this.props.hasSearchBar}
        selectAble={this.props.selectAble}
        searchValue={this.props.searchValue}
        key={rowData.id}
        id={rowData.id}
        data={rowData}
        approveShowState={this.props.approveShowState}
        checked={!!this.props.selectedDataMap[rowData.id] || false}
        checkBoxChange={this.props.checkBoxChange}
        avatarClick={this.props.avatarClick}
        itemClick={this.props.itemClick}
        useNewItem={this.props.useNewItem}
        isAlwaysPrint={this.props.isAlwaysPrint}
        needOperatorFormPlan={this.props.needOperatorFormPlan}
        hideAvatar={this.props.hideAvatar}
        flowType={this.props.flowType}
        useNewStyle={this.props.useNewStyle}
      />
    )
  }

  footer = () => {
    return <div />
  }

  render() {
    const {
      searchFrom = '',
      billList = [],
      emptyText,
      onEndReached,
      onEndReachedThreshold,
      hasSearchBar,
      selectAble,
      showSkeleton,
      closeGroupSet = new Set(),
      ...others
    } = this.props
    if (showSkeleton) {
      return <SkeletonListEUI showImage={false} />
    }
    if (!billList.length) {
      return <EmptyWidget size={200} type={searchFrom === 'recycle' ? 'recycle' : 'billList'} tips={emptyText} />
    }
    // 列表分组逻辑--关闭分组
    const listData = listDataFilter(billList, closeGroupSet)
    return (
      <ListViewWrapper
        selectAble={selectAble}
        hasSearchBar={hasSearchBar}
        listData={listData}
        itemRender={this.row}
        searchFrom={searchFrom}
        onEndReached={onEndReached}
        onEndReachedThreshold={onEndReachedThreshold}
        {...others}
      />
    )
  }
}

const listDataFilter = (listData, closeGroupSet = new Set()) => {
  if (!closeGroupSet.size) return listData
  return listData.filter(item => !closeGroupSet.has(item.groupId) || item.isGroupItem)
}
