/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/14 下午2:17
 */

import React from 'react'
import styles from './bill-empty.module.less'
import SVG_EMPTY from './../../basic-elements/images/empty.svg'

const BillEmpty = props => {
  let { emptyText = i18n.get('暂无单据'), emptyImg, imgStyle, textStyle, autoHeight, style = {} } = props
  return (
    <div style={style} className={autoHeight ? styles.bill_empty_autoHeight : styles.bill_empty}>
      <img src={emptyImg ? emptyImg : SVG_EMPTY} style={imgStyle ? imgStyle : {}} />
      <div style={textStyle ? textStyle : {}}>{emptyText}</div>
    </div>
  )
}

export default BillEmpty
