/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/14 下午2:00
 */
@import "../../../styles/ekb-colors";

.bill_item {
  background: @gray-1;
  border-bottom: 2px solid var(--eui-line-divider-default);

  .bill_urgent {
    width: 80px;
    height: 40px;
    border-radius: 20px;
    background-color: #fff1f0;
    font-size: 24px;
    line-height: 1.67;
    color: #f5222d;
    text-align: center;
    margin: 16px 0 24px 0;
    display: inline-block;
  }

  .bill_unurgent {
    width: 80px;
    height: 40px;
    border-radius: 20px;
    background-color: #f5f5f5;
    font-size: 24px;
    line-height: 1.67;
    color: #8c8c8c;
    text-align: center;
    margin: 16px 0 24px 0;
    display: inline-block;
  }
}
.bill_item_icon {
  padding: 0 8px;
  min-width: 56px;
  height: 32px;
}
.bill_item_top {
  height: 40px;
  display: flex;
  align-items: center;
}

.bill_item_title {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  font: var(--eui-font-body-r1);
  color: var(--eui-text-title);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  :global {
    .title-wrap {
      flex: 1;
      display: inline-block;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    span {
      flex-shrink: 0;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 36px;
      margin-right: 12px;
      border-radius: 8px;
      font: var(--eui-font-note-r1);
      font-weight: 500;
      color: var(--eui-static-white);
    }
    .cyan {
      background-color: var(--eui-decorative-cyan-500);
    }
    .warning {
      background-color: var(--eui-function-warning-500);
    }
    .info {
      background-color: var(--eui-function-info-500);
    }
    .sub {
      background-color: var(--eui-decorative-sub-500);
    }
  }
}

.bill_item_money {
  flex-shrink: 0;
  margin-left: 12px;
}

.bill_item_bottom {
  display: flex;
  margin-top: 8px;
  justify-content: space-between;
}

.bill_item_info {
  margin-top: 16px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font: var(--eui-font-note-r2);
  color: var(--eui-text-caption);
  :global {
    .separator {
      background-color: var(--eui-line-border-card);
      margin: 0 12px;
      display: inline-block;
      width: 2px;
      height: 24px;
    }
  }
}

.bill_item_spe {
  font: var(--eui-font-note-r2);
  color: var(--eui-text-caption);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.money_gray {
  display: block;
  font-size: 28px;
  flex-shrink: 0;
  text-align: right;
  color: var(--eui-text-placeholder);
}

.bill_item_state {
  display: flex;
  align-items: center;
  font: var(--eui-font-note-r2);
  color: var(--eui-text-caption);
  flex-shrink: 0;
  :first-child {
    margin-right: 12px;
    width: 12px;
    height: 12px;
    border-radius: 12px;
  }
  :global {
    .warn {
      background-color: var(--eui-function-warning-500);
    }
    .info {
      background-color: var(--eui-function-info-500);
    }
    .danger {
      background-color: var(--eui-function-danger-500);
    }
    .success {
      background-color: var(--eui-function-success-500);
    }
  }
}

.money_content {
  display: flex;
  align-items: center;
  font: var(--eui-font-body-r1);
  color: var(--eui-text-title);
  :global {
    .money-name {
      margin-right: 12px;
      font-weight: 400;
      color: var(--eui-text-caption);
      line-height: 20px;
    }
    .currency {
      font: var(--eui-font-body-r1) !important;
    }
  }
}