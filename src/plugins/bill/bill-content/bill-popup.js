/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/13 下午4:39
 */
import styles from './bill-popup.module.less'
import classnames from 'classnames'
import { getScenesLabel } from '../utils/billUtils'

let arr = [
  {
    label: i18n.get('全部'),
    type: 'all',
    checkVisible: true
  },
  {
    label: i18n.get('报销单'),
    type: 'expense',
    checkVisible: true
  },
  {
    label: window.IS_SMG ? i18n.get('预支单') : i18n.get('借款单'),
    type: 'loan',
    checkVisible: true
  },
  {
    label: i18n.get('申请单'),
    type: 'requisition',
    checkVisible: true
  },
  {
    label: i18n.get('取消'),
    type: 'cancel',
    checkVisible: true
  }
]

export default props => {
  const { onChangeBillType, searchFrom } = props
  const handleChange = type => {
    onChangeBillType && onChangeBillType(type)
  }

  if (searchFrom === 'home') {
    arr[0].label = i18n.get('我的单据')
  }

  const { activeType, menuList = arr } = props

  const menuListElm =
    menuList && menuList.length
      ? menuList.map(el => {
          const { type, checkVisible } = el
          const cls = classnames({ active: activeType === type })
          return (
            checkVisible && (
              <li className={cls} key={type} onClick={handleChange.bind(this, type)}>
                {getScenesLabel(type, menuList)}
              </li>
            )
          )
        })
      : null

  return (
    <ul className={styles.bill_popup_wrap}>
      <li className={styles.bill_popup_title}>{i18n.get('选择显示的单据类型')}</li>
      {menuListElm}
    </ul>
  )
}
