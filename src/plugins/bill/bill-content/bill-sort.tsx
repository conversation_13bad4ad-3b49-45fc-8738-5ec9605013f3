import { OutlinedDirectionSwitchVertical, OutlinedTipsClose } from '@hose/eui-icons'
import { CheckList, Popup } from '@hose/eui-mobile'
import React, { useState } from 'react'
import styles from './bill-sort.module.less'
import classNames from 'classnames'

interface BillSortProps {
  value: {value: string, order: string}[]
  onChange: (value: {value: string, order: string}[]) => void
  className?: string
}

export default function BillSort({ value, onChange, className }: BillSortProps) {
  const [inSort, setInSort] = useState(false)

  const sortList = [
    {
      value: [{ value: 'form.submitDate', order: 'desc' }],
      key: 'form.submitDate-desc',
      label: i18n.get('按提交最新时间排序')
    },
    {
      value: [{ value: 'form.submitDate', order: 'asc' }],
      key: 'form.submitDate-asc',
      label: i18n.get('按提交最早时间排序')
    },
    {
      // 先按状态排序，然后按提交时间排序
      value: [{ value: 'state', order: 'asc' }, { value: 'updateTime', order: 'desc' }],
      key: 'state-asc_updateTime-desc',
      label: i18n.get('按单据状态排序')
    }
  ]

  const handleChange = (key: string[]) => {
    const currentValue = sortList.find(item => item.key === key[0])?.value
    if (currentValue) {
      onChange(currentValue)
    }
    setInSort(false)
  }

  const stringValue = value?.map(item => `${item.value}-${item.order}`).join('_') || ''

  return (
    <div>
      <div className={classNames(className, styles['bill-sort'])} onClick={() => setInSort(!inSort)}>
        <OutlinedDirectionSwitchVertical
          style={{ color: inSort ? 'var(--brand-base)' : 'var(--eui-icon-n1)', fontSize: 20 }}
        />
      </div>
      <Popup onMaskClick={() => setInSort(false)} className={styles['bill-sort-popup']} visible={inSort} onClose={() => setInSort(false)} position='bottom' radius>
        <div className={styles['bill-sort-popup-title']}>
          <OutlinedTipsClose onClick={() => setInSort(false)} className={styles['bill-sort-popup-title-close']} />
          {i18n.get('选择排序方式')}
        </div>
        <CheckList value={[stringValue]} onChange={handleChange}>
          {sortList.map((item) => (
            <CheckList.Item key={item.key} value={item.key}>
              {item.label}
            </CheckList.Item>
          ))}
        </CheckList>
      </Popup>
    </div>
  )
}
