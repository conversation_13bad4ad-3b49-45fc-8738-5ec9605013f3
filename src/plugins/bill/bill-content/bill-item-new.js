import React from 'react'
import styles from './bill-item-new.module.less'
import { app } from '@ekuaibao/whispered'
import moment from 'moment'
import Highlighter from 'react-highlight-words'
import { isUrgent, renderFailure, renderState } from './bill-item'
const Money = app.require('@elements/puppet/Money')

let moneyMap = {
  expense: 'expenseMoney',
  loan: 'loanMoney',
  requisition: 'requisitionMoney'
}

const BillItem = props => {
  let { data, onHandleClick, style, searchFrom, searchValue, approveShowState } = props
  searchValue += ''
  let { logs = [], form } = data
  const { specificationId, riskWarningCount } = form
  //加急
  const urgent = isUrgent(logs)
  const isPaid = data.state === 'paid' || data.state === 'archived'
  let lastLast = (logs.length && logs[logs.length - 1]) || {}
  let { action } = lastLast

  return (
    <div onClick={onHandleClick.bind(this, data)} className={styles.bill_item} style={style}>
      <div className={styles.bill_item_top}>
        <div className={styles.bill_item_title}>
          <div className={styles.bill_item_title_content}>
            {urgent && renderUrgent(isPaid)}
            {searchValue && (
              <Highlighter
                highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                searchWords={[searchValue]}
                textToHighlight={data.form.title || i18n.get('[无标题]')}
              />
            )}
          </div>
        </div>
        {!approveShowState && (action === 'freeflow.failure' ? renderFailure() : renderState(data.state))}
        {approveShowState && Number(riskWarningCount) > 0 && renderRiskCount(riskWarningCount)}
      </div>
      <div className={styles.bill_item_bottom} style={{ paddingBottom: 4 + 'px' }}>
        {renderBillInfo(data)}
      </div>
      <div className={styles.bill_item_money}>{renderMoney(data)}</div>
      {renderSpec(searchFrom, specificationId)}
    </div>
  )
}

const renderSpec = (searchFrom, specificationId) => {
  if (searchFrom === 'home') return null
  return (
    <div className={styles['bill_item_spec']}>
      <div className="text_box">{specificationId.name}</div>
    </div>
  )
}

const renderBillInfo = data => {
  const { form, createTime } = data
  const log = data.logs[0]
  let time = log ? moment(log.time).format('YYYY/MM/DD') : moment(createTime).format('YYYY/MM/DD')
  return (
    <div className={styles.bill_item_spe}>
      {time && <span>{time}</span>}
      {form.submitterId && typeof form.submitterId === 'object' && <span>{form.submitterId.name}</span>}
    </div>
  )
}

const renderMoney = data => {
  const money = data.form[moneyMap[data.formType]]
  const noMoney = data.form[`${data.formType}Money`]
  return (
    <div>
      {noMoney && Number(noMoney) !== 0 ? (
        <Money value={money} valueSize={16} className="bill-item-money" />
      ) : (
        <span className={styles.money_gray}>{i18n.get('暂无金额')}</span>
      )}
    </div>
  )
}

export const renderRiskCount = count => {
  return <div className={styles.bill_item_count}>{i18n.get(`{__k0}条风险`, { __k0: count })}</div>
}

const renderUrgent = isPaid => {
  return (
    <svg className={(isPaid ? styles.bill_unurgent : styles.bill_urgent) + ' ' + 'icon'} aria-hidden="true">
      <use xlinkHref="#EDico-expedited" />
    </svg>
  )
}

BillItem.defaultProps = {
  isShowIcon: true
}

export default BillItem
