import React, { useState, useEffect, useMemo } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { cloneDeep } from 'lodash'
import classNames from 'classnames'
import styles from './bill-filter.module.less'
import EKBDropDown from '../elements/EKBDropDown'
import EKBGroupDropDown from '../elements/EKBGroupDropDown'
import { localStorageSet } from '../../../lib/util'

interface Props {
  groupList: any[]
  filterList: any[]
  onChangeBillFilter: Function
  showGroupIcon?: boolean
  showFilterIcon?: boolean
  selectedFilter?: boolean
  onFilterCancel?: () => void
  top?: number
}

const BillFilter: React.FC<Props> = (props: Props) => {
  const {
    groupList,
    filterList,
    onChangeBillFilter,
    showGroupIcon = true,
    showFilterIcon = true,
    selectedFilter,
    top,
    onFilterCancel
  } = props

  const defaultGroupItem = useMemo(() => {
    const defaultgroup = groupList[0]
    defaultgroup.label = i18n.get('全部单据分组')
    return defaultgroup
  }, [groupList])

  const userInfoId = api.getState()['@common'].me_info?.staff?.id + '-filter' || 'bill-select-filter'
  const preSelectFilter = JSON.parse(localStorage.getItem(userInfoId))

  const defaultActiveTypes = useMemo(() => {
    return cloneDeep(filterList)
      .map((menu: any) => {
        if (!menu?.children?.length) {
          return null
        }
        menu.children = menu.type === 'entrust' ? [menu.children[1]] : [menu.children[0]]
        return menu
      })
      .filter(el => !!el)
  }, [filterList])

  const [openShowGroup, setOpenShowGroup] = useState(false)
  const [openShowFilter, setOpenShowFilter] = useState(false)
  const [isShowGroup, setIsShowGroup] = useState(false)
  const [selectGroupKey, setSelectGroupKey] = useState(defaultGroupItem.type)
  const [selectFilterKeys, setSelectFilterKeys] = useState(preSelectFilter || defaultActiveTypes)

  useEffect(() => {
    setTimeout(() => setIsShowGroup(openShowGroup), 300)
  }, [openShowGroup])

  useEffect(() => {
    setTimeout(() => setOpenShowGroup(isShowGroup), 300)
  }, [isShowGroup])

  useEffect(() => {
    if (!showFilterIcon) {
      setIsShowGroup(false)
      setOpenShowFilter(selectedFilter)
    }
  }, [selectedFilter])

  const handleChangeBillType = (billGroupId: any) => {
    setSelectGroupKey(billGroupId)
    setIsShowGroup(false)
    const filterTypes = selectFilterKeys.map(el => ({
      key: el.type,
      value: el?.children?.map(item => item.type)
    }))
    onChangeBillFilter({ billGroupId, filterTypes })
  }

  const handleChangeFilter = (filters: any) => {
    setOpenShowFilter(false)
    setSelectFilterKeys(filters)
    localStorageSet(userInfoId, JSON.stringify(filters))
    const filterTypes = filters.map(el => ({
      key: el.type,
      value: el?.children?.map(item => item.type)
    }))
    onChangeBillFilter({ billGroupId: selectGroupKey, filterTypes })
    onFilterCancel && onFilterCancel()
  }

  const handleGroupDropDown = () => {
    setOpenShowFilter(false)
    isShowGroup ? setIsShowGroup(!isShowGroup) : setOpenShowGroup(!isShowGroup)
  }

  const handleFilterDropDown = () => {
    setIsShowGroup(false)
    setOpenShowFilter(!openShowFilter)
  }

  const handleCloseDropDown = () => {
    setIsShowGroup(false)
    setOpenShowFilter(false)
    onFilterCancel && onFilterCancel()
  }

  const selectGroup = groupList.find(el => el.type === selectGroupKey)
  const activeText = selectGroup?.label
  return (
    <div className={styles['bills-filter-wrapper']}>
      {openShowGroup && (
        <EKBDropDown
          show={isShowGroup}
          activeType={selectGroupKey}
          menuList={groupList}
          fnChangeType={handleChangeBillType}
          fnCancel={handleCloseDropDown}
          downContentClassName="filter-dropdown-content"
          style={{ top: 140 }}
        />
      )}
      {
        <EKBGroupDropDown
          isOpen={openShowFilter}
          activeTypes={selectFilterKeys}
          defaultActiveTypes={defaultActiveTypes}
          menuList={filterList}
          fnChangeType={handleChangeFilter}
          fnCancel={handleCloseDropDown}
          style={{ top: top || 140 }}
        />
      }
      {showGroupIcon ? (
        <div className={classNames('type-name', { 'type-name-open': isShowGroup })} onClick={handleGroupDropDown}>
          <span className="text-ellipsis type-group-name">{i18n.get(activeText)}</span>
          <svg className="icon small" aria-hidden="true">
            <use xlinkHref={`#EDico-caret-${isShowGroup ? 'up' : 'down'}`} />
          </svg>
        </div>
      ) : null}
      {showFilterIcon ? (
        <div
          className={classNames('type-name ml-10', { 'type-name-open': openShowGroup })}
          onClick={handleFilterDropDown}
        >
          <span>{i18n.get('筛选')}</span>
          <svg className="icon" aria-hidden="true">
            <use xlinkHref="#EDico-filter" />
          </svg>
        </div>
      ) : null}
    </div>
  )
}

export default BillFilter
