/**
 * Created by LinK on 2018/2/26.
 */
import styles from './followWeChat-modal.module.less'
import React, { PureComponent } from 'react'
import { Checkbox } from 'antd-mobile'
import { app as api } from '@ekuaibao/whispered'

export default class FollowWeChatModal extends PureComponent {
  state = { doNotRemind: false }

  handleOK = () => {
    if (this.state.doNotRemind) {
      const userInfo = api.getState()['@common'].me_info.staff
      localStorage.setItem('isBindWeChat:' + userInfo.id, JSON.stringify(true))
    }
    this.props.layer.emitCancel()
  }

  handleCancel() {
    this.props.layer.emitCancel()
  }

  handleChange = e => {
    this.setState({ doNotRemind: e.target.checked })
  }

  render() {
    return (
      <div className={styles['followWeChat-modal-wrapper']}>
        <div className="header-wrapper">
          <div className="content-wrapper">
            <p>{i18n.get('关注合思微信服务号')}</p>
            <p>{i18n.get('申请审批结果、出行提醒实时推送')}</p>
          </div>
          <img src={this.props.qrCode} className="qrCode" />
          <p>{i18n.get('截图保存后在微信识别')}</p>
        </div>
        <div className="checkBox-wrapper">
          <Checkbox className="checkBox" checked={this.state.doNotRemind} onChange={this.handleChange}>
            {i18n.get('不再提醒', {})}
          </Checkbox>
        </div>
        <div className="footer-wrapper">
          <div className="btn" onClick={this.handleOK}>
            {i18n.get('我知道了')}
          </div>
        </div>
      </div>
    )
  }
}
