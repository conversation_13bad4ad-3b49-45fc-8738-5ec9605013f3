/*
 * @Author: 樊超
 * @Date: 2021-07-27 15:04:19
 * @LastEditTime: 2021-08-06 14:21:00
 * @LastEditors: Please set LastEditors
 * @Description: 关联行程消费信息
 * @FilePath: /applet/src/plugins/bill/elements/RelevantApply.js
 */
import React, { useState, useEffect } from 'react'
import { app as api } from '@ekuaibao/whispered'
import styles from './RelevantApply.module.less'
import RelevantApplyItem from './RelevantApplyItem'
import { Resource } from '@ekuaibao/fetch'
import moment from 'moment'
import { uuid, isArray } from '@ekuaibao/helpers'
import { get } from 'lodash'
import classnames from 'classnames'
import {
  OutlinedDirectionRight,
  TwoToneGeneralTaxi,
  TwoToneGeneralTrain,
  TwoToneGeneralHotel,
  TwoToneGeneralAirplane,
  OutlinedDirectionDown,
  OutlinedDirectionUp
} from '@hose/eui-icons'
import { getDateFormater } from '@ekuaibao/lib/lib/help'
const datalinkAction = new Resource('/api/v2/datalink')
const orderTemplate = new Resource('/api/tpp/v2/order')
const entityField = new Resource('/api/v1/datalink/entityField')

const typeMap = {
  飞机: 'FLIGHT',
  火车: 'TRAIN',
  酒店: 'HOTEL',
  用车: 'TAXI'
}
const RelevantApply = ({ submitterId, requisitionId, submitterName, fromExpenseLink, setHasOrder, isEdit }) => {
  const [groups, setGroups] = useState([])
  const [currentGroup, setCurrentGroup] = useState({})
  const [loading, setLoading] = useState(false)
  const [template, setTemplate] = useState([])
  const [dataKey, setDataKey] = useState('')
  const getData = async () => {
    const allOrders = await datalinkAction.POST('/searchByRequistion', undefined, { requisitionId, submitterId })
    const groups = formatData(allOrders)
    const dataArr = Object.keys(allOrders)
    const dataKey = allOrders[dataArr[dataArr.length - 1]]
    setDataKey(dataKey)
    if (groups?.length) {
      setHasOrder?.(requisitionId)
      setCurrentGroup(groups[0])
    }
    setGroups(groups)
  }
  const getTemplate = async () => {
    const res = await orderTemplate.GET('/template')
    const template = res?.items || []
    // 用第一个元素的entityId取业务对象的所有字段，修正日期字段的withTime值
    if (template?.[0]?.entityId) {
      const res = await entityField.GET('/$id', { id: template?.[0]?.entityId })
      const entityFields = res?.items || []
      template.forEach(item => {
        if (item?.fields?.length) {
          item?.fields?.forEach(el => {
            if (el.type === 'date') {
              const targetField = entityFields.find(field => field.name === el.name)
              if (targetField) {
                el.withTime = targetField.withTime
              }
            }
            if (el.type === 'number') {
              const targetField = entityFields.find(field => field.name === el.name)
              if (targetField) {
                el.unit = targetField.unit
              }
            }
          })
        }
      })
    }
    setTemplate(template)
  }

  useEffect(() => {
    getTemplate()
    getData()
  }, [submitterId, requisitionId])

  const handleGoToAllTrip = () => {
    if (isEdit) {
      api.open('@bill:AllTravelLayer', { submitterId, requisitionId, submitterName })
    } else {
      api.go(`/all-travel/${submitterId}/${requisitionId}/${submitterName}`)
    }
  }

  if (!groups?.length) {
    return null
  }

  const dataFormater = getDateFormater(false)
  return (
    <div
      className={classnames(
        styles.relevantApplyContainer,
        fromExpenseLink ? styles.relevantApplyContainerFromExpenseLink : styles.normalContainer
      )}
    >
      <div className={styles.headerContainer}>
        <span className={styles.label}>{i18n.get('行程消费信息')}</span>
        {groups.length > 1 && (
          <span className={styles.go} onClick={() => handleGoToAllTrip()}>
            {i18n.get('查看全部行程')} <OutlinedDirectionRight />
          </span>
        )}
      </div>
      {groups.length > 1 && (
        <div className={styles.filterContainer}>
          {groups.map(group => (
            <span
              key={group.key}
              className={classnames(styles.btn, currentGroup?.key === group.key ? styles.activeBtn : '')}
              onClick={() => {
                setLoading(true)
                setTimeout(() => {
                  setCurrentGroup(group)
                  setLoading(false)
                }, 300)
              }}
            >
              {moment(group.label).format(dataFormater)}
            </span>
          ))}
        </div>
      )}
      <div>
        {currentGroup?.groups?.map((group, index) => {
          const fields = template?.find(v => v.type === typeMap[group.orderType])?.fields || []
          return (
            <OrderTypeWrapper
              date={currentGroup.label}
              key={group.id}
              orderType={group.orderType}
              groupItem={group}
              submitterName={submitterName}
              fields={fields}
              dataKey={dataKey}
            />
          )
        })}
      </div>
    </div>
  )
}

const OrderTypeWrapper = props => {
  const { orderType, submitterName, date, fields, dataKey } = props
  const groupItem = {
    ...props.groupItem,
    list: props.groupItem?.list?.slice().reverse()
  };
  const [expandList, setExpandList] = useState([])
  const [expand, setExpand] = useState(false)
  useEffect(() => {
    const expandList = groupItem.list.slice(0, 1)
    setExpandList(expandList)
  }, [groupItem.id])

  const handleExpand = () => {
    const _expand = !expand
    if (_expand) {
      setExpandList(groupItem.list.slice())
    } else {
      setExpandList(groupItem.list.slice(0, 1))
    }
    setExpand(_expand)
  }

  return (
    <div className={styles.listContainer}>
      {orderType === '酒店' && (
        <div className="type">
          <TwoToneGeneralHotel className="icon" />
          <span className="name">{i18n.get('酒店')}</span>
          {groupItem?.list?.length > 1 && (
            <span className="expandAction" onClick={handleExpand}>
              {i18n.get('{__k0}{__k1}条{__k2}行程', {
                __k0: expand ? '收起' : '展开',
                __k1: groupItem?.list?.length,
                __k2: '酒店'
              })}
              {expand ? <OutlinedDirectionUp /> : <OutlinedDirectionDown />}
            </span>
          )}
        </div>
      )}
      {orderType === '火车' && (
        <div className="type">
          <TwoToneGeneralTrain className="icon" />
          <span className="name">{i18n.get('火车')}</span>
          {groupItem?.list?.length > 1 && (
            <span className="expandAction" onClick={handleExpand}>
              {i18n.get('{__k0}{__k1}条{__k2}行程', {
                __k0: expand ? '收起' : '展开',
                __k1: groupItem?.list?.length,
                __k2: '火车'
              })}
              {expand ? <OutlinedDirectionUp /> : <OutlinedDirectionDown />}
            </span>
          )}
        </div>
      )}
      {orderType === '飞机' && (
        <div className="type">
          <TwoToneGeneralAirplane className="icon" />
          <span className="name">{i18n.get('机票')}</span>
          {groupItem?.list?.length > 1 && (
            <span className="expandAction" onClick={handleExpand}>
              {i18n.get('{__k0}{__k1}条{__k2}行程', {
                __k0: expand ? '收起' : '展开',
                __k1: groupItem?.list?.length,
                __k2: '机票'
              })}
              {expand ? <OutlinedDirectionUp /> : <OutlinedDirectionDown />}
            </span>
          )}
        </div>
      )}
      {orderType === '用车' && (
        <div className="type">
          <TwoToneGeneralTaxi className="icon" />
          <span className="name">{i18n.get('用车')}</span>
          {groupItem?.list?.length > 1 && (
            <span className="expandAction" onClick={handleExpand}>
              {i18n.get('{__k0}{__k1}条{__k2}行程', {
                __k0: expand ? '收起' : '展开',
                __k1: groupItem?.list?.length,
                __k2: '用车'
              })}
              {expand ? <OutlinedDirectionUp /> : <OutlinedDirectionDown />}
            </span>
          )}
        </div>
      )}
      {expandList.map((order, index) => {
        return (
          <RelevantApplyItem
            key={order.id}
            date={date}
            item={order.form}
            dataKey={dataKey}
            submitterName={submitterName}
            fields={fields}
            index={index}
          />
        )
      })}
    </div>
  )
}

const formatData = data => {
  const { entityPrefixForOrder, ...others } = data
  return Object.keys(others).reduce((result, key) => {
    const list = data[key]
    if (!list || !isArray(list)) {
      return result
    }
    const { groups } = list.reduce(
      (map, item) => {
        const orderType = get(item, 'form.订单类型')
        const sortId = setSortId(orderType)
        let orderTypeObj = map.typeMap[orderType]
        if (!orderTypeObj) {
          orderTypeObj = { id: uuid(8), orderType, entityPrefixForOrder, list: [], sortId }
          map.groups.push(orderTypeObj)
          map.typeMap[orderType] = orderTypeObj
        }
        orderTypeObj.list.push(item)
        return map
      },
      { groups: [], typeMap: {} }
    )
    groups.sort((pre, next) => {
      return pre.sortId - next.sortId
    })
    const group = { key, label: key, groups }
    result.push(group)

    return result
  }, [])
}

const setSortId = type => {
  switch (type) {
    case '酒店':
      return 1
    case '飞机':
      return 2
    case '火车':
      return 3
    case '用车':
      return 4
    default:
      return 0
  }
}

export default RelevantApply
