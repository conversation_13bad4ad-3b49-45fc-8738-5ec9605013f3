@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.relevantApplyContainerFromExpenseLink {
  margin: 24px 16px 16px 16px;
}

.normalContainer {
  padding: 32px 0 0;
}

.relevantApplyContainer {


  .headerContainer {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    .label {
      font: var(--eui-font-body-r1);
      color: var(--eui-text-caption);
    }

    .go {
      color: var(--eui-primary-pri-500);
      font: var(--eui-font-body-r1);

      :global {
        .eui-icon {
          margin: 2px 0;
          font-size: 24px;
          color: var(--eui-primary-pri-500);
        }
      }
    }
  }

  .listContainer {
    border: 2px solid var(--eui-line-border-card);
    border-radius: 16px;
    padding: 16px;
    margin-bottom: @space-4;
    :global {
      .type {
        font: var(--eui-font-body-b1);
        color: var(--eui-text-title) !important;
        margin-bottom: @space-4;
        margin-left: -@space-2;

        .eui-icon {
          font-size: 48px;
          vertical-align: bottom;
        }

        .name {
          margin: 4px @space-2;
          display: inline-block;
        }

        .expandAction {
          float: right;
          font: var(--eui-font-body-r1);
          color: var(--eui-primary-pri-500);
          cursor: pointer;
          margin-top: @space-1;

          .eui-icon {
            font-size: 24px;
            margin: @space-2 0 @space-2 @space-2;
            color: var(--eui-primary-pri-500) !important;
          }
        }
      }
    }
  }

  .filterContainer {
    display: flex;
    flex-wrap: wrap;

    .btn {
      cursor: pointer;
      padding: @space-3 @space-4;
      margin-bottom: @space-4;
      margin-right: @space-4;
      border-radius: @radius-2;
      font: var(--eui-font-note-r2);
      color: var(--eui-text-caption);
      background: var(--eui-bg-filler);
    }

    .activeBtn {
      color: var(--eui-primary-pri-500);
      background: var(--eui-primary-pri-50);
      border: 2px solid var(--eui-primary-pri-500);
    }
  }
}