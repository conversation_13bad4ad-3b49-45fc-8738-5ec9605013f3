import React, { PureComponent } from 'react'
import { withCommentMention } from './withCommentMention'
import { toast } from '../../../lib/util'

import style from './comment.module.less';

class Comment extends PureComponent {
  constructor(props) {
    super(props)
    let { bus } = props
    bus.watch('get:mention:content', this.handleMentionResult)
    bus.on('mention:remaind', this.handleMentionRemind)
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('bill:get:mention:content', this.handleMentionResult)
    bus.un('mention:remaind', this.handleMentionRemind)
  }

  fnCheckComment = (comment = '') => {
    let value = comment.trim()
    if (value.length === 0) {
      toast.error(i18n.get('请输入评论内容'))
      return false
    }

    if (value.length > 1000) {
      toast.error(i18n.get('评论内容超过1000个字'))
      return false
    }
    return true
  }

  handleMentionResult = () => {
    return new Promise((resolve, reject) => {
      return this.props?.commentMention?.handleMentionResult().then((result) => {
        const { validatorContent, participants, ...rest } = result;
        if(validatorContent.length === 0 && !this.fnCheckComment(validatorContent)) {
          reject()
        }
        resolve({
          ...rest,
          participants
        })
      }).catch(e => {
        reject(e)
      })
    })
  }

  handleMentionRemind = () => {
    this.props.commentMention.handleMentionRemind();
  }

  render() {
    const { commentMention } = this.props;
    const { value, textareaId } = commentMention;

    return (
      <div className={style['comment-textarea-wrapper']}>
        <div 
          id={textareaId}
          contentEditable={true}
          data-placeholder={i18n.get('请输入评论内容')}
          onInput={commentMention.updateValue}
        />
        <div className='comment-textarea-count'>
          {value.length}/1400
        </div>
      </div>
    )
  }
}

export default withCommentMention(Comment, {
  textareaId: 'commentTextarea'
});
