/**
 *  Created by gym on 2018/9/21 下午6:55.
 */

import styles from './FeetypeRiskModal.module.less'
import React, { PureComponent } from 'react'
import RiskWarningItem from '../layers/NoteRiskWarning'
import RiskWarningBudgetItem from '../layers/NoteRiskWarningBudget'
import { riskType } from '../../../elements/puppet/RiskNotice/type'
import { Tag } from '@hose/eui-mobile'
import { isErrorItem } from '../../../elements/puppet/RiskNotice/ai-audit-result/utils'

export default class FeetypeRiskModal extends PureComponent {
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  renderItem = (messages, priceMessages, risk) => {
    if (risk.type === 'budget' && priceMessages?.length) {
      return <RiskWarningBudgetItem risk={risk} />
    } else if (risk.type === 'costControl') {
      return <RiskWarningItem risk={risk} />
    } else {
      return (
        <div className="risk-item-content">
          {messages.map((v, idx) => (
            <div className="risk-content-detail-item" key={idx}>
              {messages.length === 1 ? v : idx + 1 + v}
            </div>
          ))}
        </div>
      )
    }
  }

  render() {
    let { riskList, riskTitle } = this.props
    if (!riskTitle) {
      riskTitle = i18n.get('超标详情')
    }

    return (
      <div className={styles['risk-modify-wrapper']}>
        <div className="risk-header">{riskTitle}</div>
        <div className="risk-content">
          {riskList.map((item, index) => {
            let classname = item.controlVersion ? 'risk-item-text' : ''
            const isCostControl = item.type === 'costControl'
            const riskTypeString = i18n.get(riskType[item?.type] || item?.type)

            return (
              <div className="risk-items" key={index}>
                {!isCostControl ? (
                  <div className="risk-item-title">
                    <div className={classname}>
                        <Tag color={isErrorItem(item) ? "danger" : "warning"} fill="invert" className="mr-8" size="small">
                            {riskTypeString}
                        </Tag>
                        <span>{item.controlName}</span>

                      {!!item.controlVersion && <span>:</span>}
                    </div>
                    {!!item.controlVersion && (
                      <div className="version">{i18n.get(` 依据版本{__k0}`, { __k0: item.controlVersion })}</div>
                    )}
                  </div>
                ) : null}
                {this.renderItem(item.messages, item.prices, item)}
              </div>
            )
          })}
        </div>
        <div className="risk-footer" onClick={this.handleCancel}>
          {i18n.get('知道了')}
        </div>
      </div>
    )
  }
}
