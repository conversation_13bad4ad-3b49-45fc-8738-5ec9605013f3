import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import styles from '../datalink/DataLinkList.module.less'
import { Checkbox } from 'antd-mobile'
import { Dialog } from '@hose/eui-mobile'
import { get } from 'lodash'
import { toast } from '../../../../lib/util'
import EnhanceTitleHook from '../../../../lib/EnhanceTitleHook'
import { TravelCard } from '../datalink/TravelCard'
import { getNewOrderListData, getWeek } from '../datalink/dataLinkUtil'
import moment from 'moment'
import SelectDropDown from '../../components/SelectDropDown/SelectDropDown'
const EKBSearchBar = app.require<any>('@ekb-components/base/EKBSearchBar')
const EKBIcon = app.require<any>('@elements/ekbIcon')
const { getStrLastWord } = app.require<any>('@components/utils/fnDataLinkUtil')
const AgreeItem = Checkbox.AgreeItem
const packageSize = 99
const tripOrderPackageSize = 9999 //行程订单一次性取值
const orderList = []

interface Props {
  flowId?: 'string'
}

interface State {
  listData: any[]
  selectedList: any
  loading: boolean
  searchValue: string
  hasMore: boolean
  start: number
  tripOptions: any[]
  isChecked: boolean
  typeFilter: string
}

//@ts-ignore
@EnhanceTitleHook()
export default class NewOrderQuoteSelectList extends PureComponent<Props, State> {
  filterByKey: string | undefined
  searchTimer: string
  state: State = {
    listData: [],
    selectedList: {},
    searchValue: '',
    hasMore: true,
    start: 0,
    loading: false,
    tripOptions: [],
    isChecked: false,
    typeFilter: ''
  }
  componentWillMount() {
    this.getTripOptions()
    this.getDataList()
  }

  getDataList(filterBy?: string, start = 0) {
    if (start === 0) {
      this.setState({ loading: true })
    }
    const params = {
      start,
      count: tripOrderPackageSize
    }
    this.setState({ loading: false })
    this.setState({
      listData: orderList,
      start: start + packageSize
    })
  }

  getSearchKey = form => {
    let nameKey = '',
      codeKey = '',
      formKey = '',
      toKey = ''
    for (let key in form) {
      if (getStrLastWord(key, '_') === 'name') {
        nameKey = key
      } else if (getStrLastWord(key, '_') === 'code') {
        codeKey = key
      } else if (getStrLastWord(key, '_') === i18n.get('出发地')) {
        // @i18n-ignore
        formKey = key
      } else if (getStrLastWord(key, '_') === i18n.get('目的地')) {
        // @i18n-ignore
        toKey = key
      }
    }
    return { nameKey, codeKey, formKey, toKey }
  }

  handleOnSearchChange = (value: string) => {}

  handleCancel() {
    this.setState({ searchValue: '', listData: [] }, () => {
      this.getDataList()
    })
  }

  handleSearchCancel = () => {
    this.handleCancel()
  }

  handleOnSearchClear = () => {
    this.setState({ searchValue: '' })
  }

  // 订单详情
  handleOrderItem = (rowData, updateAllCheck = false) => {
    api.open('@bill:NewOrderDetail', {
      orderInfo: rowData,
      title: '订单'
    })
  }

  handleOrderItemSelect = (rowData, updateAllCheck = false) => {
    let id = get(rowData, 'id')
    let { selectedList, listData } = this.state
    if (!selectedList[id]) {
      selectedList[id] = rowData
      this.setState({ selectedList })
    } else {
      delete selectedList[id]
    }
    this.forceUpdate()
  }

  expenseNotSlected = () => toast.error(i18n.get(i18n.get('请选择订单')))

  handleCreateConsume = async params => {}
  handleCheckedAll = e => {
    const { checked } = e.target
    const { listData } = this.state
    const selectedList = []
    if (checked) {
      orderList.forEach(item => {
        if (!selectedList[item.id]) {
          selectedList[item.id] = item
        }
      })
    }
    this.setState({ selectedList })
  }

  get allSelectedStatus() {
    const { selectedList } = this.state
    return Object.keys(selectedList).length && orderList.length === Object.keys(selectedList).length
  }

  getDateValue = item => {
    const value = item.dataLink
    const isHotel = get(item, 'dataLink.entity.type') === 'HOTEL'
    const isShop = get(item, 'dataLink.entity.type') === 'SHOP'
    const startDate = isHotel
      ? value[Object.keys(value).find(o => !!o.endsWith('入住日期'))] // @i18n-ignore
      : isShop
      ? value[Object.keys(value).find(o => !!o.endsWith('订单日期'))]
      : value[Object.keys(value).find(o => !!o.endsWith('出发时间'))] // @i18n-ignore
    return startDate
  }

  private getTripOptions = () => {
    api.invokeService('@bill:get:micro:order:list').then((res: any) => {
      const result = res.items
      const tripOptions = result.map((el: any) => ({ name: el.type, id: el.name }))
      this.setState({
        tripOptions
      })
    })
  }

  filterData = (typeFilter: string) => {
    this.setState({ typeFilter, searchValue: '', listData: [] }, () => {
      this.getDataList()
    })
  }
  handleShow = () => {
    const { tripOptions } = this.state
    Dialog.confirm({
      title: i18n.get('提示'),
      content: i18n.get('因差旅平台同步订单有一定的时效性，可通过手动同步差旅订单'),
      confirmText: i18n.get('刷新'),
      onConfirm: () => {
        api.open('@third-import:SelectDateRange', { months: 1 }).then(async params => {
          const { startTime, endTime } = params as any
          const result = await api.invokeService('@bill:pull:TravelOrder', {
            startDate: startTime,
            endDate: endTime
          })
          if (result?.value?.success) {
            toast.success(i18n.get('同步成功'))
            this.setState(
              {
                selectedList: [],
                listData: [],
                searchValue: '',
                typeFilter: tripOptions[0]
              },
              () => {
                this.getDataList()
              }
            )
          } else {
            toast.success(i18n.get('同步失败'))
          }
        })
      }
    })
  }

  getGroup = (trips: any) => {
    const tripsMap: any = {}
    trips.forEach((item: any) => {
      const date = `${moment(item.startDate).format('YYYY年MM月DD日')} ${getWeek(item.startDate)}`
      if (tripsMap[date]) {
        tripsMap[date].push(item)
      } else {
        tripsMap[date] = [item]
      }
    })
    return tripsMap
  }

  renderOrderList = () => {
    const { listData, selectedList } = this.state
    const tripGroup = this.getGroup(listData)
    return (
      <div className="travel-order-wrapper">
        {Object.keys(tripGroup).map((date, index) => {
          return (
            <div key={`group${index}`}>
              <div className="date-group">{date}</div>
              {tripGroup[date] &&
                tripGroup[date].map((trip: any, i: number) => {
                  const id = get(trip, 'id')
                  const checked = !!selectedList[id]
                  const data = getNewOrderListData(trip)
                  return (
                    <div className="trip-order-item" key={trip.id}>
                      <Checkbox checked={checked} onChange={() => this.handleOrderItemSelect(trip)} />
                      <TravelCard
                        style={{ flex: 1, marginLeft: 5 }}
                        key={i}
                        data={data}
                        handleCardClick={() => this.handleOrderItem(trip)}
                      ></TravelCard>
                    </div>
                  )
                })}
            </div>
          )
        })}
      </div>
    )
  }

  renderOrderFooter = () => {
    return (
      <div className={styles.thirdList_footer}>
        <AgreeItem checked={this.allSelectedStatus} onChange={this.handleCheckedAll}>
          {i18n.get('全选')}
        </AgreeItem>
        <div
          onClick={() => {
            this.handleCreateConsume({ name: 'select.feetype' })
          }}
          className={styles.thirdList_footer_btn}
        >
          <span>{i18n.get('导入消费')}</span>
        </div>
      </div>
    )
  }

  renderEmpty() {
    const { loading } = this.state
    return <div className="datalink-empty">{loading ? i18n.get('正在加载...') : i18n.get('无可选项')}</div>
  }

  render() {
    let { listData, searchValue, tripOptions } = this.state
    const hasData = listData && listData.length > 0
    return (
      <div className={styles['dataLink-wrapper']}>
        <SelectDropDown filterSource={tripOptions} onChange={this.filterData} />
        <div className="search-bar-padding">
          <EKBSearchBar
            value={searchValue}
            placeholder={i18n.get('搜索')}
            onChange={this.handleOnSearchChange}
            onCancel={this.handleSearchCancel}
            onClear={this.handleOnSearchClear}
          />
        </div>
        <div className="sync-wrapper" onClick={this.handleShow}>
          <EKBIcon name="#EDico-info-circle" />
          <div className="info">{i18n.get('找不到订单？')}</div>
          <EKBIcon name="#EDico-right-default" />
        </div>
        <div className="multi-list">{hasData ? this.renderOrderList() : this.renderEmpty()}</div>
        {hasData && this.renderOrderFooter()}
      </div>
    )
  }
}
