// @i18n-ignore-all
import React, { Component } from 'react'
import style from './TripOrderCard.module.less'
import { app as api } from '@ekuaibao/whispered'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import { toast } from '../../../../../lib/util'
import { TravelCard } from '../../datalink/TravelCard'
import { getNewOrderListData, secretStr } from '../../datalink/dataLinkUtil'
import { get } from 'lodash'

const orderTypeMap = {
  "改签": 2,
  "退票": 3,
}

export default class TripOrderCard extends Component {

  handleCopy = () => {
    toast.success(i18n.get('复制成功！'))
  }

  renderHotelPeople = () => {
    const { orderInfo } = this.props
    const staffs = orderInfo.orderStaffs

    return (
      <div className={style["hotel-trip-people"]}>
        <div className="em">
          {i18n.get('房间:')} {orderInfo['房型']}
        </div>
        <div>
          {staffs.map((e, i) => {
            let text = e?.name
            text += index !== staffs.length - 1 ? i18n.get('、') : ''
            return <span key={e?.id}>{text}</span>
          })}
        </div>
      </div>
    )
  }

  renderPeople = () => {
    const { orderInfo } = this.props
    const staffs = orderInfo.orderStaffs

    return (
      <>
        {staffs.map((e, i) => (
          <div className={style["trip-people"]} key={i}>
            <div className="dis-f jc-sb">
              <span className="em">{e?.name}</span>
              <span className="green">{orderInfo['订单状态']}</span>
            </div>
            <ol>
              <li>
                {i18n.get('证件号')} {secretStr(orderInfo['证件信息'], 6, 4)}
              </li>
              <li>
                {i18n.get('手机号')} {secretStr(e?.cellphone, 3, 4)}
              </li>
              {tripType === 'FLIGHT' && (
                <li>
                  {i18n.get('客票号')} {orderInfo['票号']}
                  <CopyToClipboard text={orderInfo['票号']}>
                    <a className="btn-copy color-blue" onClick={this.handleCopy}>
                      {i18n.get('复制')}
                    </a>
                  </CopyToClipboard>
                </li>
              )}
            </ol>
          </div>
        ))}
      </>
    )
  }

  getTypeUrl = (type, token, source, orderId, orderType) => {
    const link = /\?/.test(i.source) ? '&' : '?'
    const formatOrderType = orderTypeMap[orderType] || 1

    return (
      source +
      `${link}token=${token}&ekbAccessToken=${Fetch.accessToken || ''}` +
      (type === 'TRAIN'
        ? `&orderNo=${orderId}&orderType=${formatOrderType}`
        : type === 'HOTEL'
          ? `&detailId=${orderId}`
          : `&orderId=${orderId}`)
    )
  }
  handleCardClick = data => {
    const { type, sourceData } = data
    const parentId = get(sourceData, 'entity.parentId')
    const platform = get(sourceData, `E_${parentId}_订票平台`, '')
    const white = ['FLIGHT', 'HOTEL', 'TRAIN']
    const status = sourceData[Object.keys(sourceData).find(o => !!o.endsWith('订单状态')) || '']
    const statusList = ['退票', '退订', '退订/离店'] // @i18n-ignore
    const isRefund = statusList.includes(status) && sourceData?.useCount < sourceData?.totalCount
    if (platform === '合思商城' && white.includes(type)) {
      if (type !== 'TRAIN' && isRefund) {
        return
      }
      const orderId = get(sourceData, `E_${parentId}_订单号`, '')
      const orderType = get(sourceData, `E_${parentId}_订单状态`, '')
      const TK = `BUTTON_${type}_ORDER_DETAIL`
      Promise.all([
        api.invokeService('@mall:get:travel:intent', { type: TK }),
        api.invokeService('@mall:get:travel:intent:jwt', { type: TK })
      ]).then(async result => {
        const items = result[0] && result[0].items ? result[0].items : []
        const token = result[1] ? result[1].id : ''
        items.forEach(i => {
          i.source = this.getTypeUrl(type, token, i?.source, orderId, orderType)
        })
        api.thirdResources.deleteByType(TK)
        api.thirdResources.add(items)
        if (items.length > 0) {
          const services = {
            token: () => {
              return new Promise((resolve, reject) => {
                setTimeout(() => resolve({ RD: { c: 3 } }), 1000)
              })
            }
          }
          const result2 = await api.request({
            type: TK || 'BUTTON_TRAVEL_ORDER',
            services: services
          })
        } else {
          // 判断是否提供订购渠道
          return toast.info('您暂无权限，请联系贵司管理员开通合思商旅')
        }
      })
    }
  }

  renderOrderList = (orderInfo, index) => {
    const data = getNewOrderListData(orderInfo)
    console.log('data===', data)
    return (
      <TravelCard
        key={index}
        data={data}
        style={{ margin: 0, marginBottom: 24, borderRadius: 6 }}
        handleCardClick={() => this.handleCardClick(orderInfo)}
      />
    )
  }
  render() {
    const { orderInfo, tripOrderAll } = this.props
    if (!orderInfo) return null
    const { type } = orderInfo
    // const travelCardData = getTravelOrderData(dataLink)
    return (
      <>
        <div className={style['order-detail-card']}>
          {type === 'TRAIN' && (
            <div className="ticket-num">
              {i18n.get('取票号')} {orderInfo['票号']}
            </div>
          )}
          {tripOrderAll.map((e, i) => this.renderOrderList(orderInfo, i))}
        </div>
        <h5>{i18n.get('出行人')}</h5>
        {/* {type === 'HOTEL' ? this.renderHotelPeople() : this.renderPeople()} */}
      </>
    )
  }
}
