// @i18n-ignore-all
import React, { Component } from 'react'
import style from './TripOrder.module.less'
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { Button } from 'antd-mobile'
import TripCard from './TripCard'
import TripOrderCard from './TripOrderCard'
import { Fetch } from '@ekuaibao/fetch'
const EKBIcon = api.require('@elements/ekbIcon')
const SVG_WARNING = api.require('@images/invoice-payerno-warning.svg')

interface IProps {
    orderInfo: any
    title?: string
}
interface IState {
    tripInfo: any
    customTrip: any[]
    tripOrderAll: any[]
}
export default class TripOrder extends Component<IProps, IState> {
    constructor(props: IProps) {
        super(props)
        this.state = {
            tripInfo: undefined,
            customTrip: [],
            tripOrderAll: []
        }
    }

    async componentWillMount() {
        const { orderInfo: { id } } = this.props
        this.getTripInfo()

        // 查询退改签订单cardList，订单列表页获取业务对象比较卡，就没有放在那边 props传递了...
        // const { items } = await api.invokeService('@bill:get:original:order:no', { dataLinkId: id })
        // const tripOrderAll = this.constructionTripOrderAll(items)
        // this.setState({ tripOrderAll })
    }

    constructionTripOrderAll = (data: any[]) => {
        let result = data.map(e => {
            e = Object.assign(e, e.form)
            delete e.form
            return e
        })
        return result
    }

    getTripInfo = async () => {
        // const { orderInfo } = this.state
        const { orderInfo } = this.props
        const trip = orderInfo['关联行程']
        if (!trip) return
        const customTripType = await api.invokeService('@bill:get:travel:management')
        const customTripTypeList = customTripType?.items || []

        const customTrip = customTripTypeList.find(item => item.entityId === orderInfo.id)

        this.setState({
            customTrip
        })
    }

    // 关联行程详情
    handleTripDetail = () => {

        const data = this.props.orderInfo['关联行程']
        api.open('@bill:DataLinkEntityDetailModal', { value: { id: data.id }, title: i18n.get('行程详情') })
    }

    // 前往商城
    handleOpenMall = async () => {
        const { orderInfo } = this.props
        const { type, travelInfoId = '', bookingPlatform } = orderInfo
        // const parentId = get(dataLink, 'entity.parentId')
        // const currentPlatform = get(dataLink, `E_${parentId}_订票平台`, '')
        // const { tripType } = this.state
        const applyId = ''
        // const tripId = dataLink.id || ''
        const white = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI', 'FOOD']
        if (!white.includes(type)) { return }
        const TK = type ? `BUTTON_${type}_ORDER` : 'BUTTON_TRAVEL_ORDER'
        const result = await Promise.all([
            api.invokeService('@mall:get:travel:intent', { type: TK }),
            api.invokeService('@mall:get:travel:intent:jwt', { type: TK })
        ])
        const items = result[0] && result[0].items ? result[0].items : []
        const token = result[1] ? result[1].id : ''
        const config = applyId ? await api.invokeService('@bill:get:getFlowDetailInfo', applyId) : {}
        const tripPlatform = get(config, 'value.form.specificationId.configs', []).find(
            (i) => i.ability == 'tripPlatform'
        )
        const checked = get(tripPlatform, 'checked')
        const platform = get(tripPlatform, 'platform') || []
        const pp = platform.join(',')
        items.forEach((i) => {
            const type = /\?/.test(i.source) ? '&' : '?'
            if (!i.source.includes('token')) {
                i.source = i.source + `${type}token=${token}&applyId=${applyId}&tripId=${travelInfoId}&ekbAccessToken=${Fetch.accessToken || ''}`
            }
            const power = i.powers && i.powers.length ? i.powers[0] : undefined
            i.disabled = checked && pp.length > 1 ? !pp.includes(power) : false
        })
        const selectItem = items.find(v => v.title === bookingPlatform)
        if (selectItem) {
            api.thirdResources.deleteByType(TK)
            api.thirdResources.add([selectItem])
            const services = {
                token: () => {
                    return new Promise((resolve, reject) => {
                        setTimeout(() => resolve({ RD: { c: 3 } }), 1000)
                    })
                }
            }
            await api.request({
                type: TK || 'BUTTON_TRAVEL_ORDER',
                services: services
            })
        }
    }

    // 订单头部信息
    renderOrderHeader = () => {
        const { orderInfo } = this.props
        const { bookingPlatform, orderStatus, orderAmount, isExcess } = orderInfo
        return (
            <div className="order-info">
                <div>
                    <div className="dis-f jc-sb mb-4 em">
                        <span className="green">{orderStatus}</span>
                        {orderAmount && <span> {`${orderAmount} ￥`} </span>}
                    </div>
                    <div className="dis-f jc-sb">
                        <span style={{ display: 'inline-block', width: '80%' }}> {`${i18n.get('订单号')} ${orderInfo.originalOrderNo}`} </span>
                        <span>{i18n.get('总金额')}</span>
                    </div>
                    {/* {orderInfo['订单类型'] !== '主订单' && <div className="price">{orderInfo['订单类型']}</div>} */}
                    {<div className="price">{'订单类型'}</div>}

                    {bookingPlatform !== '合思商城' &&
                        <Button className="btn" onClick={this.handleOpenMall}>
                            {i18n.get(`打开${bookingPlatform}`)}
                        </Button>}
                    {isExcess && (
                        <div className="price">
                            <img className="img" src={SVG_WARNING} />
                            <span>超标原因: {orderInfo.excessReason || '暂无更多原因说明'}</span>
                        </div>
                    )}
                </div>
            </div>
        )
    }

    render() {
        const { tripInfo, customTrip, tripOrderAll } = this.state
        const { orderInfo } = this.props
        if (!orderInfo) { return null }


        return (
            <div className={style['trip-order-wrapper']}>
                {this.renderOrderHeader()}
                <TripOrderCard tripOrderAll={[1,2]} orderInfo={orderInfo} tripType={orderInfo.type} />
                {tripInfo && (
                    <div className="order-trip">
                        <h5>{i18n.get('关联行程')}</h5>
                        <div className="cur-p" onClick={this.handleTripDetail}>
                            <TripCard trip={tripInfo} customTrip={customTrip}>
                                <EKBIcon name="#EDico-right-default" />
                            </TripCard>
                        </div>
                    </div>
                )}
            </div>
        )
    }
}
