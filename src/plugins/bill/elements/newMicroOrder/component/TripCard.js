import React, { PureComponent } from 'react'
import moment from 'moment'
import styles from './TripCard.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
const { getIcon } = api.require('@components/utils/fnDataLinkUtil')
const { getDays } = api.require('@bill/elements/datalink/dataLinkUtil')
@EnhanceConnect(state => ({
  tripTypes: state['@common'].tripTypes.list
}))
export default class TripCard extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      tripsTemplate: []
    }
  }
  componentDidMount() {
    api.invokeService('@common:list:tripTypes')
    this.getTripsTemplate()
  }
  formatData = data => {
    const {
      trip: { tripType = '' }
    } = this.props
    const tripFeeType = getIcon(tripType, this.state.tripsTemplate)
    let { startTime, endTime, startCity, endCity, name } = data
    startTime = (startTime && moment(startTime).format(i18n.get('MM月DD日'))) || ''
    endTime = (endTime && moment(endTime).format(i18n.get('MM月DD日'))) || ''
    startCity = (startCity && JSON.parse(startCity)[0].label) || ''
    endCity = (endCity && JSON.parse(endCity)[0].label) || ''
    return { startTime, endTime, startCity, endCity, tripFeeType, name }
  }

  getTripsTemplate = () => {
    const { tripTypes } = this.props
    const tripsTemplate = tripTypes
    this.setState({
      tripsTemplate
    })
  }
  renderDate() {
    const { trip,customTrip } = this.props
    const { startTime, endTime } = trip
    let dateString = ''
    if (customTrip?.dateTypeField?.dateType === 'multiple'|| trip.tripType === 'FOOD' || trip.tripType === 'TAXI' || trip.tripType === 'COMMON' || trip.tripType === 'HOTEL') {
      const days = getDays(startTime, endTime)
      dateString = i18n.get(`{__k0}~{__k1} 共{__k2}天`, {
        __k0: moment(startTime).format(i18n.get('MM月DD日')),
        __k1: moment(endTime).format(i18n.get('MM月DD日')),
        __k2: days + 1
      })
      if(trip.tripType === 'HOTEL'){
        dateString = dateString + `${days}${i18n.get('晚')}`
      }
    } else {
      dateString = `${moment(startTime).format(i18n.get('MM月DD日'))}`
    }
    return <span>{dateString}</span>
  }
  render() {
    const { trip } = this.props
    if (!trip) return null
    const { startCity, endCity, tripFeeType = {}, name } = this.formatData(trip)
    return (
      <div className={styles['trip-card']}>
        <div className="icon-wrapper" style={{ backgroundColor: tripFeeType.color }}>
          <img src={tripFeeType.icon} />
        </div>
        <div className="content-wrapper">
          <div className="city">
            {name}
          </div>
          <div className="date">{this.renderDate()}</div>
        </div>
        {this.props.children}
      </div>
    )
  }
}
