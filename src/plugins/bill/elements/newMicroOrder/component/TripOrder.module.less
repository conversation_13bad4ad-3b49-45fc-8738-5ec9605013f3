@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.trip-order-wrapper {
  width: 100%;
  // height: 100%;
  color: @color-black-2;
  padding: 32px;
  .font-size-2;
  .font-weight-2;
  background: #f7f7f7;
  :global {
    .green {
      color: #199a82 !important;
    }
    .red {
      color: #f4526b !important;
    }
    .orange {
      color: #ce8b40 !important;
    }
    .gray {
      color: rgba(29, 43, 61, 0.5) !important;
    }
    .order-info,
    .order-detail,
    .order-trip {
      padding: @space-7;
      margin-bottom: @space-4;
      background: #fff;
      border-radius: 12px;
      h5 {
        margin-bottom: @space-6;
        color: @color-black-3;
        .font-size-2;
      }
    }
    .em {
      margin-bottom: @space-2;
      color: @color-black-1;
      .font-size-3;
      .font-weight-3;
    }
    ol {
      padding: 0;
      margin: 0;
    }
    .order-info {
      color: rgba(29, 43, 61, 0.75);
      .price {
        display: flex;
        margin-top: @space-4;
        padding: @space-4 @space-5;
        background: rgba(250, 150, 42, 0.1);
        border-radius: @radius-3;
        color: #ce8b40;
        .img {
          margin-right: 4px;
          width: 36px;
        }
      }
      .ant-btn {
        margin: @space-6 @space-4 0 0;
        width: 65px;
        border-radius: @radius-2;
        .font-size-2;
        .font-weight-3;
      }
      .btn-gray {
        color: #1d2b3d;
        border: none;
        background: rgba(29, 43, 61, 0.06);
        &:active,
        &:hover {
          color: rgba(29, 43, 61, 0.75);
          background: rgba(29, 43, 61, 0.09);
        }
      }
      .btn-red {
        color: #f4526b;
        border: none;
        background: rgba(244, 82, 107, 0.1);
        &:active,
        &:hover {
          color: rgba(244, 82, 107, 0.7);
          background: rgba(244, 82, 107, 0.2);
        }
      }
    }
    .order-trip {
      margin-bottom: 0;
      .icon {
        margin-left: auto;
      }
    }
    .btn{
      font-size: 28px;
      color: #fff;
      height: 68px;
      background: var(--brand-base);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 24px;
    }
  }
}
