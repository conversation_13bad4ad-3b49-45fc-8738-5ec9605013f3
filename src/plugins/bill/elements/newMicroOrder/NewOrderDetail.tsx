import React, { PureComponent } from 'react'
import <PERSON>hance<PERSON><PERSON>le<PERSON><PERSON> from '../../../../lib/EnhanceTitleHook'
import TripOrder from './component/TripOrder'

interface IProps {
  orderInfo: any
  title?: string
}
// @ts-ignore
@EnhanceTitleHook(props => props.title)
export default class NewOrderDetailModal extends PureComponent<IProps, {}> {
  render() {
    return <TripOrder {...this.props} />
  }
}
