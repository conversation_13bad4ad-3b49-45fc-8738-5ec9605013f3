/*
 * @Author: 樊超
 * @Date: 2021-07-27 16:45:50
 * @LastEditTime: 2021-08-06 14:13:04
 * @LastEditors: Please set LastEditors
 * @Description: 关联行程消费信息Item
 * @FilePath: /applet/src/plugins/bill/elements/RelevantApplyItem.js
 */
import React, { useState } from 'react'
import styles from './RelevantApplyItem.module.less'
import classnames from 'classnames'
import moment from 'moment'
import { Fetch } from '@ekuaibao/fetch'
import {
  OutlinedGeneralLocal,
  OutlinedGeneralBuilding,
  OutlinedGeneralNotice,
  OutlinedGeneralMember,
  OutlinedGeneralAirplane,
  OutlinedGeneralTrain,
  OutlinedGeneralCar,
  OutlinedDirectionDown,
  OutlinedDirectionUp
} from '@hose/eui-icons'
import { getDateFormater } from '@ekuaibao/lib/lib/help'

const getPlace = placeJson => {
  let place = {}
  try {
    const places = JSON.parse(placeJson)
    if (places?.length) {
      place = places[0]
    }
  } catch (err) {
    place = {}
  }
  return place
}

function CustomField({ fields, item }) {
  const [expand, setExpand] = useState(true)
  const handleClick = () => {
    setExpand(!expand)
  }

  if (!fields?.length) return null

  return (
    <div className="customField">
      {fields?.map((v, index) => {
        let value = ''
        const filedValue = item[v.name]
        if (filedValue && v.type === 'text') {
          value = filedValue
        }
        if (filedValue && v.type === 'money') {
          value = filedValue?.standardStrCode + ' ' + filedValue?.standard
        }
        if (filedValue && v.type === 'date') {
          const dataFormater = getDateFormater(v.withTime)
          value = moment(filedValue).format(dataFormater)
        }
        if (filedValue && v.type === 'number') {
          value = `${filedValue}${v.unit}`
        }
        if (expand && index > 1) return null
        return (
          <div>
            <span className="label">{v.label}：</span>
            <span>{value}</span>
          </div>
        )
      })}
      {fields?.length > 2 && (
        <span className="expandAction" onClick={handleClick}>
          {i18n.get(expand ? '展开' : '收起')}
          {expand ? <OutlinedDirectionDown /> : <OutlinedDirectionUp />}
        </span>
      )}
    </div>
  )
}

const RelevantApplyItem = ({ date, item, dataKey, submitterName, fields, index }) => {
  const type = item['订单类型']
  const orderAmount = item[`${dataKey}订单金额`]
  const amount = orderAmount?.standardStrCode + ' ' + orderAmount?.standard
  const dataFormaterWithoutTime = getDateFormater(false)
  date = moment(date).format(dataFormaterWithoutTime)
  const startTime = moment(item[`${dataKey}出发时间`]).format('HH:mm')
  const endTime = moment(item[`${dataKey}到达时间`]).format('HH:mm')
  const placeJson = type === '酒店' ? item[`${dataKey}住宿地`] : item[`${dataKey}到达地`]
  const place = getPlace(placeJson)?.label
  const pedestrian = item?.出行人姓名
  const num = item[`${dataKey}间夜数`]
  const price =
    orderAmount && Number(num) && !isNaN(Number(num))
      ? orderAmount?.standardStrCode +
      ' ' +
      new Big(orderAmount.standard).div(num).toFixed(Number(orderAmount.standardScale))
      : ''
  const dataFormater = getDateFormater(true)
  const firstFieldValue = moment(item[`${dataKey}到达时间`]).format(dataFormater)
  if (type === '飞机') {
    const departurePlace = item[`${dataKey}出发机场`]
    const arrivePlace = item[`${dataKey}到达机场`]
    return (
      <div className={classnames(styles.relevantApplyItemCommon, { 'mt-8': !!index })}>
        <div className="presetField">
          <div className="date">
            <span>{firstFieldValue}</span>
            <span>{amount}</span>
          </div>
          <div>
            <OutlinedGeneralLocal />
            <span>{place}</span>
          </div>
          <div>
            <OutlinedGeneralAirplane />
            <span>{`${startTime} ${departurePlace} - ${endTime} ${arrivePlace}`}</span>
          </div>
          <div>
            <OutlinedGeneralNotice />
            <span>
              {item[`${dataKey}航空公司`]}
              {item[`${dataKey}航班号`]}
            </span>
          </div>
          <div>
            <OutlinedGeneralMember />
            <span>{item?.出行人姓名 || submitterName}</span>
          </div>
        </div>
        <CustomField fields={fields} item={item} />
      </div>
    )
  } else if (type === '酒店') {
    return (
      <div className={classnames(styles.relevantApplyItemCommon, { 'mt-8': !!index })}>
        <div className="presetField">
          <div className="date">
            <span>{date}</span>
            <span>{price}</span>
          </div>
          <div>
            <OutlinedGeneralLocal />
            <span>{place}</span>
          </div>
          <div>
            <OutlinedGeneralBuilding />
            <span>{item[`${dataKey}酒店名称`]}</span>
          </div>
          <div>
            <OutlinedGeneralNotice />
            <span>{item[`${dataKey}房型`]}</span>
          </div>
          <div>
            <OutlinedGeneralMember />
            <span>{pedestrian}</span>
          </div>
        </div>
        <CustomField fields={fields} item={item} />
      </div>
    )
  } else if (type === '火车') {
    const departurePlace = item[`${dataKey}出发车站`]
    const arrivePlace = item[`${dataKey}到达车站`]
    return (
      <div className={classnames(styles.relevantApplyItemCommon, { 'mt-8': !!index })}>
        <div className="presetField">
          <div className="date">
            <span>{firstFieldValue}</span>
            <span>{amount}</span>
          </div>
          <div>
            <OutlinedGeneralLocal />
            <span>{place}</span>
          </div>
          <div>
            <OutlinedGeneralTrain />
            <span>{`${startTime} ${departurePlace} - ${endTime} ${arrivePlace}`}</span>
          </div>
          <div>
            <OutlinedGeneralNotice />
            <span>
              {item[`${dataKey}火车车次`]} <span className="line"></span> {item[`${dataKey}火车席位`]}
            </span>
          </div>
          <div>
            <OutlinedGeneralMember />
            <span>{item?.出行人姓名 || submitterName}</span>
          </div>
        </div>
        <CustomField fields={fields} item={item} />
      </div>
    )
  } else if (type === '用车') {
    return (
      <div className={classnames(styles.relevantApplyItemCommon, { 'mt-8': !!index })}>
        <div className="presetField">
          <div className="date">
            <span>{firstFieldValue}</span>
            <span>{amount}</span>
          </div>
          <div>
            <OutlinedGeneralLocal />
            <span>{place}</span>
          </div>
          <div className="taxi">
            <span className="point">
              <span className="start"></span>
            </span>
            <span className="place">{item[`${dataKey}实际出发地点`]}</span>
          </div>
          <div className="taxi">
            <span className="point">
              <span className="end"></span>
            </span>
            <span className="place">{item[`${dataKey}实际到达地点`]}</span>
          </div>
          <div>
            <OutlinedGeneralCar />
            <span>
              {item[`${dataKey}车牌号`]} <span className="line"></span> {item[`${dataKey}车型`]}{' '}
            </span>
          </div>
          <div>
            <OutlinedGeneralMember />
            <span>{item?.出行人姓名 || submitterName}</span>
          </div>
        </div>
        <CustomField fields={fields} item={item} />
      </div>
    )
  } else {
    return <></>
  }
}

export default RelevantApplyItem
