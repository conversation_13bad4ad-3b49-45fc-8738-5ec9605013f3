.risk-modify-wrapper {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  padding: 0 32px;
  :global {
    .risk-header {
      height: 100px;
      align-self: flex-start;
      background-color: rgba(255, 255, 255, 0.95);
      font-size: 36px;
      line-height: 100px;
      color: #000000;
    }
    .risk-content {
      flex: 1;
      padding: 32px 0;
      max-height: 680px;
      overflow-y: auto;
      .risk-items {
        margin-bottom: 48px;
        .risk-item-title {
          display: flex;
          flex-direction: row;
          align-items: center;
          font-size: 28px;
          color: #000000;
          margin-bottom: 16px;
          justify-content: space-between;
          .risk-item-text {
            text-align: left;
            width: 400px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis
          }
          .version {
            font-size: 24px;
            color: rgba(0, 0, 0, 0.45);
          }
        }
        .risk-item-content {
          display: flex;
          flex-direction: column;
          align-items: self-start;
          font-size: 28px;
          color: rgba(0, 0, 0, 0.65);
          background-color: #fff8eb;
          border-radius: 10px;
          max-height: 680px;
          overflow: auto;
          >div {
            text-align: left;
            line-height: 60px;
            padding: 16px 24px;
            width: 100%;
          }
          .warning{
            color: #FAAD14;
          }
        }
      }
    }
    .risk-footer {
      height: 100px;
      font-size: 36px;
      line-height:100px;
      text-align: center;
      color: var(--brand-base);
      border-top: 2px solid rgba(255, 255, 255, 0.95);
      box-shadow: inset 0 0.5px 0 0 rgba(0, 0, 0, 0.09);
    }
  }
}