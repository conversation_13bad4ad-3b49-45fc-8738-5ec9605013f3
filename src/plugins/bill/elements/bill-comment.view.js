import React from 'react'
import { But<PERSON>, Checkbox } from '@hose/eui-mobile'
import { EnhanceConnect } from '@ekuaibao/store'
import { app } from '@ekuaibao/whispered'
import SVG_COMMENT from '../images/comment.svg'
import SVG_COMMENT_ATTACHMENT from '../images/comment-attach.svg'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import style from './bill-comment.module.less'
import { UploadWithConfig } from '../../../elements/puppet/Upload'

import { getBoolVariation } from '../../../lib/featbit';

const {
  buildData,
  fnClickAttachments,
  fnPreviewAttachments,
  getToken,
  parseAsSaveValue,
  getUploadUrl,
  uploadDone
} = app.require('@components/utils/fnAttachment')

import loadable from '@loadable/component'
const FilesUploader = loadable(() => import('@ekuaibao/uploader/esm/FilesUploader'))
const HuaWeiUploader = loadable(() => import('@ekuaibao/uploader/esm/HuaWeiUploader'))
const UploadItem = app.require('@elements/puppet/Upload/UploadItem')
import { debounce, showLoading, hideLoading } from '../../../lib/util'
import { MessageCenter } from '@ekuaibao/messagecenter'
import CommentComponent from './comment.view'
import CommentComponentV2 from './commentV2.view';
import { defaultInvalidSuffixes, onInvalidFile } from '../../../lib/invalidSuffixFile.tsx'
import ClipboardUploader from '../../basic-elements/approve/ClipboardUploader'

const maxSize = 64

@EnhanceConnect(state => ({
  staffs: state['@common'].staffs,
  uploadServiceUrl: state['@common'].uploadServiceUrl,
  orgConfig: state['@common'].organizationConfig,
  visibilityStaffOptions: state['@common'].visibilityStaffOptions
}))
@EnhanceTitleHook(i18n.get('评论'))
export default class BillComment extends UploadWithConfig {
  constructor(props) {
    super(props)
    this.state = {
      comment: '',
      fileList: [],
      keepItSecret: false,
      suffixesFiledName: 'COMMENT',
      attachmentSetting: {}
    }
  }
  bus = new MessageCenter()

  componentWillMount() {
    this.bus.watch('element:attachments:line:click', this.handleAttachment)
    app.dataLoader('@common.staffs').load()
    app.dataLoader('@common.uploadServiceUrl').load()
    app.dataLoader('@common.organizationConfig').load()
    this.handleInitConfig()
    getToken().then(token => {
      this.setState({ token })
    })
  }

  componentWillUnmount() {
    this.bus.un('element:attachments:line:click', this.handleAttachment)
    this.bus.un('over', this.handleOver)
  }

  handlePublish = () => {
    this.bus.invoke('get:mention:content').then(mentionContent => {
      let { fileList, keepItSecret } = this.state
      let attachments = []
      if (fileList && fileList.length) {
        attachments = fileList.map(parseAsSaveValue)
      }
      this.props.layer.emitOk({ ...mentionContent, attachments, keepItSecret })
    })
  }

  handleRemind = () => {
    this.debounceRemind()
  }

  debounceRemind = debounce(() => {
    this.bus.emit('mention:remaind')
  }, 300)

  handleAttachment = (value, index) => {
    fnPreviewAttachments({ value, index })
  }

  handleChange = uploaderFileList => {
    this.setState({ uploaderFileList })
  }

  handleOnStart = () => {
    showLoading(i18n.get('上传中...'))
  }

  handleDone = uploaderFileList => {
    hideLoading()
    this.setState({ uploaderFileList: [] }, () => {
      let { fileList } = this.state
      uploadDone.call(this, {
        list: uploaderFileList,
        value: fileList,
        onChange: list => {
          this.setState({ fileList: list })
        }
      })
    })
  }

  handleUploadFinish = attachments => {
    hideLoading()
    const { fileList = [] } = this.state
    this.setState({ fileList: fileList.concat(attachments), uploaderFileList: [] })
  }

  /**
   *
   * @param {File[]} invalidFiles // 错误文件列表
   * @param {'invilidaFileType' | 'otherInvalid'} type // 文件错误类型
   */
  handleInvalidFiles = (invalidFiles, type) => {
    hideLoading()
    let { invalidSuffixes = defaultInvalidSuffixes } = this.props
    onInvalidFile(invalidFiles, invalidSuffixes, type)
  }

  handleLineClick = (line, index) => {
    let { fileList } = this.state
    fnClickAttachments({ bus: this.bus, value: fileList, line, index })
  }

  handleDelete = (line, index) => {
    let { fileList } = this.state
    fileList = fileList.slice(0) // Copy 一份, 不然我列表不会刷新
    fileList.splice(index, 1)
    this.setState({ fileList })
  }

  onCheckboxChange = (checked, type) => {
    this.setState(JSON.parse(`{"${type}":${checked}}`))
  }

  render() {
    const { token, fileList, keepItSecret } = this.state
    const {
      staffs,
      uploadServiceUrl,
      invalidSuffixes = defaultInvalidSuffixes,
      orgConfig,
      visibilityStaffOptions
    } = this.props
    const uploadUrl = uploadServiceUrl && uploadServiceUrl.uploadUrl
    const validAccept = this.getAccept()
    const isVisibilityStaffs = !!orgConfig.visibilityConfigs?.length && !visibilityStaffOptions.fullVisible

    const CommentComp = getBoolVariation('mfrd-3040-comment-optimization') ? CommentComponentV2 : CommentComponent;

    return (
      <div className={style['comment-wrapper']}>
        <div className="comment-content">
          <CommentComp bus={this.bus} staffs={staffs} isVisibilityStaffs={isVisibilityStaffs} />
          <div className="comment">
            <img src={SVG_COMMENT} onClick={this.handleRemind} />
            {window.__PLANTFORM__ === 'HUAWEI' && window.isAndroid ? (
              <HuaWeiUploader
                action={IS_STANDALONE ? getMinioUploadUrl() : uploadUrl}
                onChange={this.handleChange}
                onDone={this.handleDone}
                onStart={this.handleOnStart}
                data={file => buildData(file, token, uploadServiceUrl)}
              >
                <div className="content-attachment">
                  <img src={SVG_COMMENT_ATTACHMENT} />
                </div>
              </HuaWeiUploader>
            ) : (
              // 评论上传附件
              <FilesUploader
                accept={validAccept}
                action={IS_STANDALONE ? getUploadUrl : uploadUrl}
                type={IS_STANDALONE}
                maxSize={maxSize}
                onChange={this.handleChange}
                onDone={this.handleDone}
                onStart={this.handleOnStart}
                onInvalidFile={this.handleInvalidFiles}
                invalidSuffixes={invalidSuffixes}
                data={file => buildData(file, token, uploadServiceUrl)}
                invalidSuffixesConfig={this.state?.attachmentSetting?.invalidSuffixesConfig}
              >
                <div className="content-attachment">
                  <img src={SVG_COMMENT_ATTACHMENT} />
                </div>
                <ClipboardUploader
                  useClipboard
                  onStartUpload={this.handleOnStart}
                  onUploadChange={this.handleChange}
                  onUploadFinish={this.handleUploadFinish}
                />
              </FilesUploader>
            )}
            <Checkbox
              className="comment-checkbox"
              onChange={e => {
                this.onCheckboxChange(e, 'keepItSecret')
              }}
              checked={keepItSecret}
            >
              {i18n.get('仅被@的人可见')}
            </Checkbox>
          </div>
          <div className="attachment-wrapper">
            {fileList.map((line, index) => (
              <UploadItem
                key={index}
                file={line}
                isEdit={true}
                onClickItem={() => this.handleLineClick(line, index)}
                onRemoveItem={() => this.handleDelete(line, index)}
              />
            ))}
          </div>
        </div>
        <div className="comment-publish" onClick={this.handlePublish}>
          <Button block category="primary" size="large">
            {i18n.get('发布')}
          </Button>
        </div>
      </div>
    )
  }
}
