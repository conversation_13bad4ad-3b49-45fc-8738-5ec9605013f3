@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.relevantApplyItemCommon {
  background: var(--eui-bg-body-overlay);
  border-radius: 8px;
  padding: 16px;

  :global {
    .presetField {
      font: var(--eui-font-body-r1);
      color: var(--eui-text-title);

      .eui-icon {
        font-size: 28px;
        color: var(--eui-icon-n3);
        margin-right: @space-4;
      }

      div {
        margin-bottom: @space-2;
        display: flex;

        span {
          display: inline-block;
        }

        .eui-icon {
          margin-top: 6px;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .date {
        font: var(--eui-font-head-b1);
        color: var(--eui-text-title);
        display: flex;
        justify-content: space-between;
        margin-bottom: @space-5;
      }

      .taxi {
        display: flex;

        .point {
          width: 28px;
          height: 28px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: @space-4;
          margin-top: 6px;

          span {
            width: 10px;
            height: 10px;
            border-radius: @radius-3;
            display: inline-block;
          }
        }

        .start {
          background: var(--eui-function-success-500);
        }

        .end {
          background: var(--eui-function-warning-500);
        }

        .place {
          flex: 1;
          font: var(--eui-font-body-r1);
          color: var(--eui-text-title);
        }
      }
    }

    .customField {
      border-top: 2px solid var(--eui-line-divider-default);
      font: var(--eui-font-body-r1);
      color: var(--eui-text-title);
      margin-top: @space-5;
      padding-top: @space-5;

      div {
        margin-bottom: @space-2;
        display: flex;

        span {
          display: inline-block;
        }

        .label {
          color: var(--eui-text-caption);
          white-space: nowrap;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .expandAction {
        padding: 0;
        margin-top: @space-2;
        display: inline-block;
        cursor: pointer;
        font: var(--eui-font-body-r1);
        color: var(--eui-primary-pri-500);

        .eui-icon {
          font-size: 24px;
          color: var(--eui-primary-pri-500);
          margin-left: @space-2;
          margin-top: 6px;
        }
      }
    }
  }
}