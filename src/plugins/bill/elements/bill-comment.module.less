.comment-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  :global {
    .comment-content {
      flex: 1;
      background-color: var(--eui-bg-base);
      overflow-y: auto;
      .eui-text-area {
        padding: 32px;
      }
      .comment {
        height: 100px;
        padding: 0 32px 0 16px;
        border-top: 2px solid var(--eui-line-divider-default);
        background-color: var(--eui-bg-body);
        display: flex;
        .comment-checkbox {
          flex: 1;
          justify-content: right;
          .eui-checkbox-content{
            font-size: 28px;
          }
        }
        .content-attachment {
          display: flex;
          width: 100%;
        }
      }
      .attachment-wrapper {
        background-color: var(--eui-bg-body);
        .fileItem_forFix {
          margin: 0 0;
          padding: 0 32px;
        }
      }
    }
    .comment-publish {
      flex-shrink: 0;
      width: 100%;
      height: auto;
      padding: 20px 32px;
      background-color: var(--eui-bg-body);
      box-shadow: var(--eui-shadow-up-1);
    }
  }
}
