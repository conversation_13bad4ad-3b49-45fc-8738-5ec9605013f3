.modify_wrapper {
  border-radius: 4px;
  :global {
    .header-wrapper {
      height: 100px;
      font-size: 32px;
      color: #54595b;
      flex-shrink: 0;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .modify-content {
      max-height: 800px;
      overflow: auto;
      background-color: rgba(0, 0, 0, 0.04);
      .content-textArea {
        margin: 16px 0 64px 0;
      }
    }
    .footer-wrapper {
      flex-shrink: 0;
      height: 88px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .btn {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32px;
        text-align: right;
        color: var(--brand-base);
        &:last-child {
          border-left: 2px solid #E0E0E0;
        }
      }
    }

  }
}