/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/12.
 */

import styles from './orderButtonWrapper.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { Button } from 'antd-mobile'
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'

@EnhanceConnect(state => ({
  mall_info: state['@mall'].value,
  powers: state['@common'].powers
}))
export default class OrderButtonWrapper extends PureComponent {
  constructor(props) {
    super(props)
    api.invokeService('@mall:get:mall:list')
    this.state = {
      isTripRequisition: false
    }

    this.state.hmallToken = '' //合思商城token
    this.state.openedModulesStr = '' //开通的模块字符串集合
  }

  componentWillMount() {
    let { bus } = this.props
    bus && bus.on('order:button:changed', this.changeVisible)
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus && bus.un('order:button:changed', this.changeVisible)
  }

  changeVisible = (tripType = []) => {
    //TODO X
    let plainV = tripType.filter(v => v.tripTypeId.name === '飞机').length > 0 // @i18n-ignore
    let trainV = tripType.filter(v => v.tripTypeId.name === '火车').length > 0 // @i18n-ignore
    let hotelV = tripType.filter(v => v.tripTypeId.name === '酒店').length > 0 // @i18n-ignore
    this.setState({ isTripRequisition: plainV || trainV || hotelV, plainV, trainV, hotelV })
  }

  handleOrderPlain() {
    api.invokeService('@mall:open:mall')
  }

  handleClickHoseButton = funCode => {
    const { hmallToken } = this.state

    let path = ''
    switch (funCode) {
      case 'MenuFL02':
        path = 'flight'
        break
      case 'MenuFL03':
        path = 'interFlight'
        break
      case 'MenuFL04':
        path = 'hotel'
        break
      case 'MenuFL05':
        path = 'train'
        break
    }

    api.invokeService('@mall:getHoseMallHref', path, hmallToken).then(d => {
      api.invokeService('@layout:open:link', d)
    })
    // window.location.href = href;
  }

  handleClickButton = (funCode, flowCode, mall_info) => {
    if (!mall_info) return null
    if (!mall_info.isStaffBinding) {
      api.go('/didierp/captcha/mall')
    } else {
      api.invokeService('@mall:get:mall:redirect:url', funCode, flowCode).then(url => {
        api.invokeService('@layout:open:link', url.value)
      })
    }
  }


  fnGetMallEntries = mall_info => {
    if (!mall_info) return {}
    let obj = {}
    const { entries = [] } = mall_info
    entries.forEach(el => {
      obj[el.code] = el.active
    })
    return obj
  }

  render() {
    const { billState, isBillOwner, mall_info, powers, formType, flowCode } = this.props
    if (formType !== 'requisition') return null
    const corporationHasBinding = !!mall_info && mall_info.isCorporationBinding
    const { isTripRequisition, plainV, trainV, hotelV, hmallToken, openedModulesStr } = this.state
    const hasYEEGO = !!powers.YEEGO
    const hasBusiness = !!powers.Business
    const corporationCanUseYEEGO = hasYEEGO && corporationHasBinding
    const permissions = this.fnGetMallEntries(mall_info)
    const hasOpenBtn = !!~Object.values(permissions).indexOf(true)
    const planeBtnVisible =
      (corporationCanUseYEEGO && (permissions['MenuFL02'] || permissions['MenuFL03']) && plainV) || hasBusiness
    const hasInnerFlight = (corporationCanUseYEEGO && permissions['MenuFL02'] && plainV) || hasBusiness
    const hasInterFlight = (corporationCanUseYEEGO && permissions['MenuFL03'] && plainV) || hasBusiness
    const hasTrain = permissions['MenuFL05'] && trainV
    const hasHotal = permissions['MenuFL04'] && hotelV
    const hasHoseHotel = hmallToken && openedModulesStr.indexOf('MenuFL04') > -1 && hotelV
    const hasHoseInnerFlight = hmallToken && openedModulesStr.indexOf('MenuFL02') > -1 && plainV
    const hasHoseTrain = hmallToken && openedModulesStr.indexOf('MenuFL05') > -1 && trainV

    const visible =
      billState !== 'draft' &&
      billState !== 'rejected' &&
      isTripRequisition &&
      ((corporationCanUseYEEGO && hasOpenBtn) || hasBusiness) &&
      isBillOwner
    if ((!visible && !hmallToken) || !isBillOwner || (billState !== 'archived' && billState !== 'paid')) return null

    const btnDisabled = billState === 'approving' || billState === 'paying' || !hasYEEGO

    return (
      <div className={styles['order-button-wrapper']}>
        {hasHoseInnerFlight && (
          <Button
            disabled={billState === 'approving' || billState === 'paying'}
            className="order-button spec"
            type="primary"
            onClick={
              corporationCanUseYEEGO
                ? _ => this.handleClickHoseButton('MenuFL02', flowCode, mall_info)
                : this.handleOrderPlain
            }
          >
            {i18n.get('订合思机票')}
          </Button>
        )}
        {hasInnerFlight && !hasHoseInnerFlight && (
          <Button
            disabled={billState === 'approving' || billState === 'paying'}
            className="order-button spec"
            type="primary"
            onClick={
              corporationCanUseYEEGO
                ? _ => this.handleClickButton('MenuFL02', flowCode, mall_info)
                : this.handleOrderPlain
            }
          >
            {i18n.get('国内机票')}
          </Button>
        )}
        {hasInterFlight && (
          <Button
            disabled={billState === 'approving' || billState === 'paying'}
            className="order-button spec"
            type="primary"
            onClick={
              corporationCanUseYEEGO
                ? _ => this.handleClickButton('MenuFL03', flowCode, mall_info)
                : this.handleOrderPlain
            }
          >
            {i18n.get('国际机票')}
          </Button>
        )}
        {hasTrain && (
          <Button
            className="order-button"
            disabled={btnDisabled}
            type="primary"
            onClick={_ => this.handleClickButton('MenuFL05', flowCode, mall_info)}
          >
            <div>{i18n.get('订火车票')}</div>
            <div style={hasYEEGO ? { display: 'none' } : {}}> {i18n.get('(敬请期待)')}</div>
          </Button>
        )}
        {hasHoseHotel && (
          <Button
            className="order-button"
            disabled={billState === 'approving' || billState === 'paying'}
            type="primary"
            onClick={_ => this.handleClickHoseButton('MenuFL04', flowCode, mall_info)}
          >
            <div>{i18n.get('订合思酒店')}</div>
          </Button>
        )}
        {hasHotal && !hasHoseHotel && (
          <Button
            className="order-button"
            disabled={btnDisabled}
            type="primary"
            onClick={_ => this.handleClickButton('MenuFL04', flowCode, mall_info)}
          >
            <div>{i18n.get('订酒店')}</div>
            <div style={hasYEEGO ? { display: 'none' } : {}}> {i18n.get('(敬请期待)')}</div>
          </Button>
        )}
      </div>
    )
  }
}
