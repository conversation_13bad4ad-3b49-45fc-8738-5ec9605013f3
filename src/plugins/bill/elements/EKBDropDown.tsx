import React from 'react'
import { app } from '@ekuaibao/whispered'
import styles from './EKBDropDown.module.less'
import { getScenesLabel } from '../utils/billUtils'
const EkbIcon = app.require<any>('@elements/ekbIcon')

interface menuProps {
  [x: string]: any
  type: string
  label: string
  checkVisible: boolean
  isHidden: (props: any) => boolean
}

interface Props {
  menuList: menuProps[]
  show: boolean
  isGroup?: boolean
  isSearch?: boolean
  fnCancel?: Function
  activeType: string
  fnChangeType: Function
  downContentClassName?: string
  style?: any
  receiptPermission?: boolean | undefined
}

export default function EKBDropDown(props: Props) {
  const { show, fnCancel, menuList, activeType, fnChangeType, downContentClassName = '', style, isGroup, isSearch } = props
  const maskClassName = show ? 'ekb-drop-down-mask show' : 'ekb-drop-down-mask'
  const groupClass = isSearch ? 'ekb-drop-down-group-search' : 'ekb-drop-down-group'
  const showStyle = isGroup ? `ekb-drop-down ${groupClass} show` : 'ekb-drop-down show'
  const closeStyle = isGroup ? `ekb-drop-down  ${groupClass}` : 'ekb-drop-down'
  const contentClassName = show ? showStyle : closeStyle
  return (
    <div className={styles['ekb-drop-down-wrap']} style={style}>
      <div className={maskClassName} onClick={() => fnCancel && fnCancel()} />
      <div className={contentClassName}>
        <div className={`ekb-drop-down-content inertial-rolling ${downContentClassName}`}>
          {menuList.map((menu, idx: number) => {
            const type = isGroup ? menu?.name : menu?.type
            const itemClassName = activeType === type ? 'ekb-drop-down-item selected' : 'ekb-drop-down-item'
            const isHidden = menu.isHidden ? menu.isHidden(props) : false
            return (
              isHidden
              ? null
                : <div key={idx} className={itemClassName} onClick={() => fnChangeType(isGroup ? menu : type)}>
                <span className='label'>{getScenesLabel(type, menuList)}</span>
                  {activeType === type && (
                  <EkbIcon name="#EDico-icon_uni_check" style={{ color: 'var(--brand-base)', fontSize: 20 }} />
                )}
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

