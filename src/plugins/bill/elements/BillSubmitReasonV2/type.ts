export type T_RiskWarningContentItem = {
  /** 发票号 */
  invoiceNum: string
  /** 风险信息 */
  invoiceMsg: string
  /** 风险原因保存的唯一id */
  invoiceRiskExplainId: string
  /** 原因 */
  invoiceRiskExplainContent: string
  /** 相关单据 */
  relatedFlows: {
    /** 单据号 */
    flowCode: string
    /** 发票号 */
    invoiceNum: string
  }[]
}

export type T_RiskWarningItem = {
  /** 规范id */
  invoiceNormId: string 
  /** 规范描述 */
  invoiceNormDesc: string
  /** 违规内容 */
  riskWarningContent: T_RiskWarningContentItem[]
}

