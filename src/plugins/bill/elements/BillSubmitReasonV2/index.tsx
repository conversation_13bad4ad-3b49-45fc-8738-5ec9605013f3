import React, { useState, useEffect, Fragment } from 'react'
import { Button, TextArea } from '@hose/eui-mobile'
import { T_RiskWarningItem } from './type'
import cloneDeep from 'lodash/cloneDeep'
import { app as api } from '@ekuaibao/whispered'
import classNames from 'classnames'
import styles from './index.module.less'

const EKBIcon = api.require<any>('@elements/ekbIcon')

export type I_InvoiceRiskArr = {
  invoiceNormId: string
  invoiceNum: string
  invoiceRiskExplainId: string
  invoiceRiskExplainContent: string
}[]

interface IProps {
  /** 风险信息 */
  riskWarningArr: T_RiskWarningItem[]
  /** 继续提交按钮点击 */
  handleSubmit?: (invoiceRiskArr: I_InvoiceRiskArr) => void
  /** 取消点击 */
  handleCancel?: () => void
}

interface IErrorInfoMap {
  /** 错误信息 key: `invoiceNormId-invoiceNum` */
  [key: string]: string
}

const MAX_LENGTH = 150

export default function BillSubmitReasonV2(props: IProps) {
  const { riskWarningArr = [], handleSubmit, handleCancel } = props

  const [riskWarningArrLocal, setRiskWarningArrLocal] = useState<T_RiskWarningItem[]>([])
  const [errorInfoMap, setErrorInfoMap] = useState<IErrorInfoMap>({})

  useEffect(() => {
    setRiskWarningArrLocal(riskWarningArr)
  }, [])

  useEffect(() => {
    if (Object.keys(errorInfoMap).length) {
      invoiceRiskReasonValidator()
    }
  }, [riskWarningArrLocal])

  const handleReasonChange = (riskWarningIndex: number, contentIndex: number, value: string) => {
    const riskWarningArrClone = cloneDeep(riskWarningArrLocal)
    riskWarningArrClone[riskWarningIndex].riskWarningContent[contentIndex].invoiceRiskExplainContent = value
    setRiskWarningArrLocal(riskWarningArrClone)
  }

  const invoiceRiskReasonValidator = () => {
    let hasError = false
    const errorInfoMapTemp: IErrorInfoMap = {}
    riskWarningArrLocal.forEach(item => {
      const { invoiceNormId, riskWarningContent = [] } = item
      riskWarningContent.forEach(contentItem => {
        const { invoiceNum, invoiceRiskExplainContent } = contentItem
        if (!invoiceRiskExplainContent || invoiceRiskExplainContent.length > MAX_LENGTH) {
          const errorInfo = invoiceRiskExplainContent ? i18n.get('最多输入{__k0}个字符', { __k0: MAX_LENGTH }) : i18n.get('请填写原因')
          errorInfoMapTemp[`${invoiceNormId}-${invoiceNum}`] = errorInfo
          hasError = true
        }
      })
    })
    setErrorInfoMap(errorInfoMapTemp)
    return hasError
  }

  const handleSubmitClick = () => {
    if (!invoiceRiskReasonValidator()) {
      const invoiceRiskArr: I_InvoiceRiskArr = []
      riskWarningArrLocal.forEach(item => {
        const { invoiceNormId, riskWarningContent = [] } = item
        riskWarningContent.forEach(contentItem => {
          const { invoiceNum, invoiceRiskExplainId, invoiceRiskExplainContent } = contentItem
          invoiceRiskArr.push({
            invoiceRiskExplainId,
            invoiceNormId,
            invoiceNum,
            invoiceRiskExplainContent: invoiceRiskExplainContent
          })
        })
      })
      handleSubmit && handleSubmit(invoiceRiskArr)
    }
  }

  const handleCancelClick = () => {
    handleCancel && handleCancel()
  }

  return (
    <div className={styles['bill-submit-reason-v2']}>
      <div className="title">{i18n.get('违规风险提示')}</div>
      <div className="risk-warning">
        {riskWarningArrLocal.map((item, riskWarningIndex) => {
          const { invoiceNormId, invoiceNormDesc, riskWarningContent = [] } = item
          return (
            <Fragment key={invoiceNormId}>
              <div className="risk-desc">
                <div className="label">
                  <EKBIcon className="warning-icon" name={'#EDico-plaint-circle'} />
                  {i18n.get(`风险{__k0}：`, { __k0: riskWarningIndex + 1 })}
                </div>
                <div>{invoiceNormDesc}</div>
              </div>
              {riskWarningContent.map((contentItem, contentIndex) => {
                const { invoiceNum, invoiceMsg, invoiceRiskExplainContent, relatedFlows = [] } = contentItem
                return (
                  <div key={invoiceNum} className="risk-detail">
                    <div className="risk-item">
                      <div className="label">{i18n.get('风险信息')}</div>
                      <div>{invoiceMsg}</div>
                    </div>
                    {relatedFlows && relatedFlows?.length > 0 && (
                      <div className="risk-item">
                        <div className="label">{i18n.get('相关单据')}</div>
                        <div>
                          {relatedFlows
                            ?.map(relatedFlowsItem => `${relatedFlowsItem.flowCode}(${relatedFlowsItem.invoiceNum})`)
                            .join('、')}
                        </div>
                      </div>
                    )}
                    <div className="risk-item">
                      <div className="label required">{i18n.get('原因')}</div>
                      <TextArea
                        className={classNames('textarea', {
                          'textarea-error': errorInfoMap[`${invoiceNormId}-${invoiceNum}`]
                        })}
                        value={invoiceRiskExplainContent}
                        onChange={e => handleReasonChange(riskWarningIndex, contentIndex, e)}
                        placeholder={i18n.get('请填写原因，限150字')}
                        border
                      />
                      <div className="ext-info">
                        <div className="error-info">{errorInfoMap[`${invoiceNormId}-${invoiceNum}`]}</div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </Fragment>
          )
        })}
      </div>
      <div className="bill-submit-reason-action">
        <Button block category="secondary" size="middle" onClick={handleCancelClick}>
          {i18n.get('取消')}
        </Button>
        <Button block category="primary" size="middle" onClick={handleSubmitClick}>
          {i18n.get('继续提交')}
        </Button>
      </div>
    </div>
  )
}
