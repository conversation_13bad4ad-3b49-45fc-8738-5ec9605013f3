.followWeChat-modal-wrapper {
  :global {
    .header-wrapper {
      background-image: url('../images/qr_code_bg.png');
      background-size: 100% 100%;
      height: 560px;
      color: #fff;
      .qrCode {
        background: white;
        border-radius: 12px;
        width: 256px;
        height: 256px;
        margin: 20px 0;
      }
      font-size: 24px;
    }
    .content-wrapper {
      padding-top: 80px;
      p {
        text-align: center;
        margin: 0;
        &:nth-child(1) {
          font-size: 36px;
          font-weight: 500;
        }
        &:nth-child(2) {
          font-size: 28px;
        }
      }
    }
    .checkBox-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 72px;
      .checkBox {
        font-size: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #92949f;
        .am-checkbox {
          width: 32px;
          height: 32px;
          .am-checkbox-inner {
            width: 32px;
            height: 32px;
            margin-right: 10px;
            border-radius: 0;
            border-width: 2px;
            &:after {
              position: initial;
              margin-left: 30%;
              margin-top: -10%;
            }
          }
        }
      }
    }
    .footer-wrapper {
      flex-shrink: 0;
      height: 100px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      border-top: 2px solid #e0e0e0;
      .btn {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32px;
        text-align: right;
        color: var(--brand-base);
      }
    }
  }
}
