import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import styles from './BillSubmittingWithDialogModal.module.less'
import get from 'lodash/get'
const i18n = window.i18n
const { related } = app.require<any>('@components/utils/Related')
import { getEntityFormById, recordInvoiceRiskWarning, saveTripInfo, setDetailFLowRelation } from '../bill.action' // 触发发票风险记录的
import { Fetch } from '@ekuaibao/fetch'
import { Dialog, Questionnaire, SpinLoading } from '@hose/eui-mobile'
import questionnaireConfig from '../../../lib/questionnaireConfig'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { EnhanceConnect } from '@ekuaibao/store'
import { getNeedSubmitRiskReasonList } from '../utils/billUtils'
import { T_RiskWarningItem } from './BillSubmitReasonV2/type'
const UncontrolledLottie: any = app.require('@elements/puppet/Animation/UncontrolledLottie')
import { getBoolVariation } from '../../../lib/featbit'
interface Props {
  layer: any
  editFlow: Function
  submitFlow: Function
  flowId: string
  billsparams: any
  isEbotShift: boolean
  handlePageGo: Function
  isModifyBill: boolean
  isBillCopiedValue: boolean
  checkBudgetOccupy?: (params: any) => Promise<boolean>
  specification: any
  alterFlag: any
  subsidyGeneration: string
  skipCheck: boolean
  expenseSpecAfterFiltered?: any
  isEnableDraftConfig: boolean
  handleSynchronous: any
  changeSubmitLock: (isBillSubmitLock: boolean) => void
}

interface State {
  id: string
  state: string
  flowState: string
  billErrText: string
  needSubmitRiskReason?: boolean
  skipCheck: boolean
  billType?: string
  needSubmitRiskReasonList?: T_RiskWarningItem[]
}

interface Map {
  [key: string]: any
}
@EnhanceConnect((state: any) => ({
  expenseSpecAfterFiltered: state['@home'].expenseSpecAfterFiltered,
  skipCheck: state['@bill'].skipCheck
}))
class BillSubmittingWithDialogModal extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    props.overrideGetResult(this.getResult.bind(this))
    const { flowId } = this.props
    this.state = {
      id: flowId,
      state: 'submitting',
      flowState: undefined,
      billErrText: undefined,
      needSubmitRiskReason: false,
      skipCheck: false,
      needSubmitRiskReasonList: []
    }
  }

  async componentDidMount() {
    const { billsparams, checkBudgetOccupy, isModifyBill } = this.props
    if (checkBudgetOccupy && isModifyBill) {
      const result = await checkBudgetOccupy(billsparams)
      if (result?.value) {
        this.setState({
          billErrText: i18n.get('单据占用的预算已被冻结，禁止修改'),
          state: 'submitted',
          flowState: 'failed'
        })
        return
      }
    }
    this.props.skipCheck === null && this.fetchCalculateCorpIdWhiteList()
    if (billsparams && billsparams.name === 'freeflow.editApproving') {
      this.billSubmit()
    } else {
      this.billSaveAndSubmit()
    }
  }

  fetchCalculateCorpIdWhiteList = async () => {
    const { value: skipCheck } = await api.invokeService('@bill:get:calculate:corpId:whitelist')
    this.setState({ skipCheck })
  }

  billSaveAndSubmit = async () => {
    let { editFlow, flowId, billsparams, alterFlag, subsidyGeneration } = this.props
    const value: Map = { form: billsparams.form, params: billsparams.params }
    if (alterFlag) {
      value.form.alterFlag = alterFlag
    }
    subsidyGeneration && (value.form.subsidyGeneration = subsidyGeneration)

    const action = await editFlow(flowId, value, 'saveAndSubmit')
    const errorCode = get(action, 'payload.errorCode', '')
    if (action.error || !!errorCode) {
      const errorMessage = get(action, 'payload.errorMessage', '')
      const msg = get(action, 'payload.msg', '')
      this.setState({
        state: 'submitted',
        billErrText: msg || errorMessage || i18n.get('程序错误请联系管理员')
      })
    } else {
      this.setState({ id: get(action, 'payload.flow.id') }, () => {
        this.billSubmit(get(action, 'payload.flow'))
      })
    }
  }

  fnSetDetailFLowRelation = (antion: any) => {
    const { expenseSpecAfterFiltered } = this.props
    const id = get(antion, 'payload.flow.id')
    if (expenseSpecAfterFiltered?.extendType === 'QuickExpense' && id) {
      // 查询单据明细是否是快速报销明细
      setDetailFLowRelation({
        flowId: id,
        detailSource: 'QUICK_EXPENSE'
      })
    }
  }

  billSubmit = async (formValue?: any) => {
    const {
      submitFlow,
      billsparams,
      isEbotShift,
      isBillCopiedValue,
      isModifyBill,
      alterFlag,
      subsidyGeneration,
      isEnableDraftConfig
    } = this.props
    const { id, skipCheck } = this.state
    const value = formValue ? { ...billsparams, form: formValue.form } : { ...billsparams }
    // 申请单变更依据
    if (alterFlag) {
      value.form.alterFlag = alterFlag
    }
    // 补助标识
    subsidyGeneration && (value.form.subsidyGeneration = subsidyGeneration)

    const action = await submitFlow({ id, data: value, isBillCopiedValue, isModifyBill })
    this.fnSetDetailFLowRelation(action)
    try {
      // @bills:get:flow:risk:warning 的数据必须等这个接口完成才能有
      await recordInvoiceRiskWarning(id || get(action, 'payload.flow.id'))
    } catch {}
    if (action.error) {
      // 票据连号需要填写原因，输出判断条件
      let needSubmitRiskReason = false
      let needSubmitRiskReasonList: T_RiskWarningItem[] = []
      if (action.payload?.msg && action.payload.msg?.indexOf('发票违规，允许提交且需说明原因') !== -1) {
        const flowId = id || get(action, 'payload.flow.id')
        needSubmitRiskReasonList = await getNeedSubmitRiskReasonList(flowId)
        needSubmitRiskReason = needSubmitRiskReasonList && needSubmitRiskReasonList.length > 0
        if (needSubmitRiskReason) {
          api
            .open('@bill:BillRiskReasonModal', {
              flowId,
              invoiceRiskArr: needSubmitRiskReasonList
            })
            .then(() => {
              this.handleReasonOK()
            })
        }
      }

      this.setState({
        state: 'submitted',
        flowState: 'draft',
        billErrText: action.payload.msg || i18n.get('网络错误，请稍后重试'),
        needSubmitRiskReason,
        needSubmitRiskReasonList
      })
    } else {
      related.clearRelateMap()
      if (billsparams.name === 'freeflow.editApproving' && !isEbotShift) {
        this.handleEditApprovingResult()
      } else {
        if (skipCheck) {
          this.setState({ state: 'submitNoCheck', flowState: action.payload.flow.state, billType: action.billType })
        } else {
          if (Fetch.ekbCorpId === 'bnB3nHI6Fb3qzw') {
            this.getFlowStateTainYang(get(action, 'payload.flow.id'), action)
          } else {
            this.getFlowState(get(action, 'payload.flow.id'), action)
          }
        }
      }
      if (isEnableDraftConfig) {
        const flowId = id || get(action, 'payload.flow.id')
        const hasTrip = get(billsparams, 'form.u_行程规划')?.length ? true : false
        if (hasTrip) {
          this.props?.handleSynchronous && this.props?.handleSynchronous(flowId, 'freeflow.submit')
        }
      }
    }
  }

  // 天阳间隔两秒 轮循5次共十秒
  getFlowStateTainYang = async (id: string, action: any) => {
    try {
      await this.ebotCheck(id, action)
      await this.sleep(2000)
      await this.ebotCheck(id, action)
      await this.sleep(2000)
      await this.ebotCheck(id, action)
      await this.sleep(2000)
      await this.ebotCheck(id, action)
      await this.sleep(2000)
      await this.ebotCheck(id, action)
    } catch (e) {
      if (e && e.state === 'noProcessing') {
      }
    }
  }

  getFlowState = async (id: string, action: any) => {
    try {
      if (getBoolVariation('cyxq-74035')) {
        // 延迟1秒再去做检查，后台这个时候还没有数据还没有存储完成
        await this.sleep(1000)
      }
      await this.ebotCheck(id, action)
      await this.sleep(3000)
      await this.ebotCheck(id, action)
      await this.sleep(7000)
      await this.ebotCheck(id, action)
    } catch (e) {
      if (e && e.state === 'noProcessing') {
      }
    }
  }

  sleep = (time: number) => {
    return new Promise(resolve => {
      setTimeout(resolve, time)
    })
  }
  fnGetIsTravel = async () => {
    const { specification, billsparams } = this.props
    const components = specification?.components
    const name = specification?.name
    let res = false
    if (/差旅|出差|商旅/.test(name)) {
      res = true
    }
    if (components.find(oo => oo.field === 'u_行程规划' || oo.field === 'trips' || /差旅|出差|商旅/.test(oo.field))) {
      res = true
    }
    if ('trips' in billsparams?.form) {
      res = true
    }
    try {
      const data = await app.invokeService('@requisition:get:operation')
      if (!data?.items?.length) {
        res = false
      }
      if (res) {
        if (billsparams?.form?.u_行程规划) {
          let field = components.find(oo => oo.field === 'u_行程规划')
          const list = billsparams?.form?.u_行程规划 ?? []
          const trips_info = await this.fnFormatTrips(field, list)
          app.dispatch(saveTripInfo(trips_info))
        } else {
          const trips_info = billsparams?.form?.trips || []
          app.dispatch(saveTripInfo(trips_info))
        }
      }
      return res
    } catch (error) {
      console.log(error)
    }
  }

  fnFormatTrips = async (field: any, list: any[]) => {
    const id = get(field, 'referenceData.id')
    const behaviour = get(field, 'behaviour')
    let res = await api.dispatch(getEntityFormById(id, behaviour))
    console.log(list, res)
    res = res?.items ?? []
    list = list
      .map(item => {
        const dataLinkTemplateId = item?.dataLinkTemplateId
        const trip = res.find(oo => oo.templateId === dataLinkTemplateId)
        const entity = trip?.entity
        let tripTypeId = entity?.type ?? ''
        if (tripTypeId === '') return null
        tripTypeId = ':' + tripTypeId.toLowerCase()
        let tripForm = null
        if (tripTypeId === ':flight' || tripTypeId === ':train') {
          tripForm = this.fnFormatTrainOrFlight(item?.dataLinkForm)
        } else if (tripTypeId === ':hotel') {
          tripForm = this.fnFormatHotel(item?.dataLinkForm)
        }
        return { tripTypeId, tripForm }
      })
      .filter(Boolean)
    return list
  }
  fnFormatTrainOrFlight = (data: any) => {
    const res = {}
    for (let key in data) {
      if (key.endsWith('出发地')) {
        res.tripFromCity = data[key]
      }
      if (key.endsWith('目的地')) {
        res.tripToCity = data[key]
      }
      if (key.endsWith('行程日期')) {
        res.tripDate = data[key]
      }
      if (key.endsWith('name')) {
        res.label = data[key]
      }
    }
    return res
  }

  fnFormatHotel = (data: any) => {
    const res = {}
    const time = {}
    for (let key in data) {
      if (key.endsWith('住宿地')) {
        res.tripCity = data[key]
      }
      if (key.endsWith('入住日期')) {
        time.start = data[key]
      }
      if (key.endsWith('离店日期')) {
        time.end = data[key]
      }
      if (key.endsWith('name')) {
        res.label = data[key]
      }
    }
    res.tripDatePeriod = time
    return res
  }
  ebotCheck = async (flowId: string, action) => {
    const { billsparams } = this.props
    try {
      const result = await api.invokeService('@bill:get:flow:submit:check:state', flowId)
      let value = result.value
      if (value.state === 'pending' || value.state === 'checking') {
        this.setState({
          state: 'smartCheck'
        })
        return Promise.resolve()
      } else {
        if (billsparams.name === 'freeflow.editApproving') {
          this.handleEditApprovingResult()
        } else {
          let errText = value.state === 'failed' ? value.rejectReason : ''
          if(getBoolVariation('cyxq-74035') && value.state === 'failed' && value.rejectReason) {
            // 和WEB端保持一致
            errText = i18n.get('单据存在风险，已标注在单据上，请修改后重新送审')
          }
          let checkedState = value.state === 'failed' ? value.flowState : ''
          let riskData
          // 存在连号风险，阻断提交单据，填写完原因保存单据草稿态
          let needSubmitRiskReason = false
          let needSubmitRiskReasonList: T_RiskWarningItem[] = []
          if (value.state === 'failed' && errText && errText?.indexOf('发票违规，允许提交且需说明原因') !== -1) {
            needSubmitRiskReasonList = await getNeedSubmitRiskReasonList(flowId)
            needSubmitRiskReason = needSubmitRiskReasonList && needSubmitRiskReasonList.length > 0
            if (needSubmitRiskReason) {
              api
                .open('@bill:BillRiskReasonModal', {
                  flowId,
                  invoiceRiskArr: needSubmitRiskReasonList
                })
                .then(() => {
                  this.handleReasonOK()
                })
            }
          }

          this.setState({
            state:
              action.billType === 'requisition' && (await this.fnGetIsTravel()) ? 'submittedRequisition' : 'submitted',
            billErrText: errText,
            flowState: checkedState,
            needSubmitRiskReason,
            needSubmitRiskReasonList
          })
        }
        return Promise.reject({ state: 'noProcessing' })
      }
    } catch (e) {
      return Promise.reject(e)
    }
  }

  handleEditApprovingResult = () => {
    const { id } = this.state
    const { handlePageGo } = this.props
    this.props.layer.emitOk('toast')
    handlePageGo(id)
  }

  initSurvey = () => {
    const { specification, handlePageGo } = this.props
    const { id } = this.state
    // 删除白名单判断
    Questionnaire.initSurvey({
      sid: questionnaireConfig?.submit?.sid,
      channelId: questionnaireConfig?.submit?.channelId,
      externalUserId: api.getState()['@common'].me_info?.staff?.userId,
      externalCompanyId: api.getState()['@common'].me_info?.staff?.corporationId?.id,
      parameters: {
        name: specification?.name,
        // @ts-ignore
        type: billTypeMap()[specification?.type || 'expense']
      }
    })
  }

  handleCancel = () => {
    const { billErrText, id } = this.state
    const { handlePageGo, changeSubmitLock } = this.props

    changeSubmitLock && changeSubmitLock(false)

    this.props.layer.emitCancel()
    if (!billErrText) {
      api.emit('header:left:change', false)
      // 清楚  current_flow 数据
      api.invokeService('@common:clear:flow:detail:info')
      handlePageGo(id)
      setTimeout(() => {
        this.initSurvey()
      }, 500)
    }
  }

  handleOk = () => {
    this.props.layer.emitOk()
  }

  getResult = () => {
    const { flowState, id } = this.state
    const { billsparams } = this.props
    let result: { [key: string]: any } = { ...billsparams }
    result.id = id
    result.state = flowState
    result.modifyErr = true
    return result
  }

  renderFailReason = (failTextAndReason: string[]) => {
    const processText = (text: string) => {
      return text?.replace(/\$TITLE\$/g, '')
    }
    return (
      <>
        {failTextAndReason.map((t, idx) => {
          return !!t ? (
            <div className="dis-f" key={idx}>
              <span className="circle" />
              <span className="flex-1">
                {processText(t)}
                {failTextAndReason.length !== 1 ? '；' : ''}
              </span>
            </div>
          ) : (
            ''
          )
        })}
      </>
    )
  }

  fnBillSubmittedRequisition = () => {
    this.props.layer.emitCancel()
    api.go(`/bill/requisition/success/${this.state?.id}`)
  }

  handleOkToBillsDetail = () => {
    const { id, billType, flowState } = this.state
    const params = { id, formType: billType, state: flowState, isAlwaysPrint: false }
    api.emit('header:left:change', false)
    this.props.layer.emitCancel()
    api.go(-1)
    api.invokeService('@home:click:bill', params, 'homePage')
  }

  reset = () => {
    return new Promise(r => {
      this.setState(
        {
          state: 'submitting',
          flowState: undefined,
          billErrText: undefined,
          needSubmitRiskReason: false,
          needSubmitRiskReasonList: []
        },
        r
      )
    })
  }

  handleReasonOK = async () => {
    await this.reset()
    await this.billSubmit()
  }

  billSubmitting = () => {
    const {
      billsparams: { name }
    } = this.props
    let text = name === 'freeflow.editApproving' ? i18n.get('单据保存中，请稍后') : i18n.get('单据提交中，请稍后')
    return {
      title: '',
      header: '',
      iconType: '',
      actions: [],
      bodyClassName: 'submitting',
      content: (
        <div className={styles['bill-submitting-modal']}>
          <UncontrolledLottie />
          <div className={'text'}>{text}</div>
        </div>
      )
    } as any
  }

  billSmartCheck = () => {
    return {
      title: i18n.get('智能校验中'),
      header: <SpinLoading />,
      iconType: '',
      bodyClassName: 'smartCheck',
      actions: [
        {
          key: 'confirm',
          text: i18n.get('关闭'),
          style: {
            color: 'var(--eui-text-link-normal)'
          },
          onClick: () => {
            this.handleCancel()
          }
        }
      ],
      content: i18n.get(
        '单据校验可能还需要一段时间，您可以继续等待或关闭此窗口，检验完成后您可在「我的单据」查看结果。'
      )
    } as any
  }

  billSubmitted = () => {
    const { billErrText, flowState } = this.state
    let text = billErrText ? billErrText : i18n.get('提交成功，请耐心等待审批')
    let iconType = billErrText ? 'warn' : 'success'
    let btn = flowState ? i18n.get('立即修改') : i18n.get('我知道了')
    const failTextAndReason = text?.split('\n') || ['']
    const textMore = failTextAndReason.length > 2
    return {
      // title: billErrText ? '' : i18n.get('提交成功'),
      header: '',
      iconType,
      bodyClassName: 'submitNoCheck',
      actions: [
        {
          key: 'confirm',
          text: btn,
          style: {
            color: 'var(--eui-text-link-normal)'
          },
          onClick: () => {
            flowState ? this.handleOk() : this.handleCancel()
          }
        }
      ],
      content: <div className={textMore ? 'text-more' : ''}> {this.renderFailReason(failTextAndReason)}</div>
    } as any
  }

  billSubmitNoCheck = () => {
    return {
      title: i18n.get('提交成功'),
      header: '',
      iconType: 'success',
      bodyClassName: 'submitNoCheck',
      actions: [
        {
          key: 'confirm',
          text: i18n.get('我知道了'),
          style: {
            color: 'var(--eui-text-link-normal)'
          },
          onClick: () => {
            this.handleOkToBillsDetail()
          }
        }
      ],
      content: i18n.get('请耐心等待审批')
    } as any
  }

  billStateMap: Map = {
    submitting: this.billSubmitting,
    smartCheck: this.billSmartCheck,
    submitted: this.billSubmitted,
    submittedRequisition: this.fnBillSubmittedRequisition,
    submitNoCheck: this.billSubmitNoCheck
  }

  render() {
    const { state, needSubmitRiskReason } = this.state
    if (needSubmitRiskReason) {
      return null
    }
    const { bodyClassName, ...resetConfig } = this.billStateMap[state]() || {}
    return (
      <Dialog
        {...resetConfig}
        bodyClassName={styles[`${bodyClassName}`]}
        closeOnAction={true}
        maskStyle={{ background: 'unset' }}
        visible={true}
      />
    )
  }
}

export default BillSubmittingWithDialogModal
