@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.ekb-drop-down-wrap {
  z-index: 2;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  :global {
    .ekb-drop-down-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: -1;
      opacity: 0;
      transition: opacity ease .2s;
      background: rgba(20, 34, 52, 0.24);
      box-shadow: 0px 4px 4px rgb(0 0 0 / 25%);
      // .mask-black;
      &.show {
        opacity: 1;
      }
    }
    .ekb-drop-down {
      position: absolute;
      top: -100%;
      left: 0;
      width: 100%;
      min-height: 240px;
      max-height: 1032px;
      background: white;
      transition: all ease .2s;
      padding: 120px 0 0;
      .ekb-drop-down-content {
        overflow-y: scroll;
        max-height: 912px;
        width: 100%;
        .ekb-drop-down-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 88px;
          padding: 0 @space-5 0 @space-6;
          font: var(--eui-font-body-r1);
          color: var(--eui-icon-n1);
          .label {
            flex: 1;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            user-select: none;
          }
          img {
            flex-shrink: 0;
            width: 32px;
          }
          &.selected {
            color: @eui-ref-color-brand;
            background: @eui-sys-focus-brand;
          }
          .img-icon {
            width: 40px;
            height: 40px;
          }
        }
      }
      &.show {
        top: 0;
      }
    }
    .ekb-drop-down-group {
      padding: 184px 0 0;
    }
    .ekb-drop-down-group-search {
      padding: 300px 0 0;
    }
    }
}
