/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/1/9 下午4:09.
 */
import styles from './modify-modal.module.less'
import React, { PureComponent } from 'react'
import { TextareaItem } from 'antd-mobile'
import { toast } from '../../../lib/util'

export default class ModifyModal extends PureComponent {
  constructor(props) {
    super(props)
    props.overrideGetResult && props.overrideGetResult(this.getResult)
    this.state = { value: '' }
    this.isCancel = false
  }

  getResult = () => {
    let isCancel = this.isCancel
    let { value } = this.state
    this.isCancel = false
    return {
      isCancel,
      editReason: value
    }
  }

  handleOK = () => {
    let { value } = this.state
    if (value && value.length > 1400) {
      toast.error(i18n.get('超过1400字'))
      return null
    }
    this.props.layer.emitOk()
  }

  handleCancel = () => {
    this.isCancel = true
    this.props.layer.emitOk()
  }

  handleModifyChange = value => {
    this.setState({ value })
  }

  render() {
    return (
      <div className={styles['modify_wrapper']}>
        <div className="header-wrapper">{i18n.get('确定修改单据？', {})}</div>
        <div className="modify-content">
          <div className="content-textArea">
            <TextareaItem
              onChange={this.handleModifyChange}
              placeholder={i18n.get('请填写修改原因（选填,1400个字）')}
              rows={3}
              className="text-area"
              value={this.state.value}
              count={1400}
            />
          </div>
        </div>
        <div className="footer-wrapper">
          <div className="btn" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </div>
          <div className="btn" onClick={this.handleOK}>
            {i18n.get('确定')}
          </div>
        </div>
      </div>
    )
  }
}
