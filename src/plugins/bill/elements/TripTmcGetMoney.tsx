import React from 'react'
import createServer from './createServer'

export default class TripTmcGetMoney extends React.Component {
  server: any
  componentWillMount() {
    this.server = createServer(this)
  }
  componentDidMount() {
    this.server.listen(window)
  }
  componentWillUnmount() {
    this.server.unlisten(window)
  }
  close=()=>{
    this.props.layer?.emitOk({money:32.23})
  }
  render() {
    const { url } = this.props
    return (
      <iframe
        sandbox="allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-same-origin allow-scripts allow-popups"
        src={url}
        frameBorder="0"
        width="100%"
        height="100%"
      />
    )
  }
}
