.comment-textarea-wrapper {
  padding: 32px;
  background: var(--eui-bg-body);
  width: 100%;

  :global {
    #commentTextarea {
      display: block;
      box-sizing: border-box;
      height: 480px;
      line-height: 48px;
      font: var(--eui-font-head-r1);
      text-align: var(--text-align);
      color: var(--color);
      background: transparent;
      overflow-y: scroll;

      .at-button {
        outline: none;
        appearance: none;
        background-color: transparent;
        color: var(--eui-primary-pri-500, #2555ff);
      }
    }

    #commentTextarea[contenteditable='true']:empty:before {
      content: attr(data-placeholder);
      color: var(--eui-text-placeholder); /* placeholder 的颜色 */
      pointer-events: none;
    }

    /* 确保空白字符不会影响 placeholder 的显示 */
    #commentTextarea[contenteditable='true']:empty:not(:focus):before {
      content: attr(data-placeholder);
    }

    .comment-textarea-count {
      margin-top: 4px;
      color: var(--eui-text-placeholder);
      font: var(--eui-font-note-r2);
      text-align: var(--count-text-align, right);
    }
  }
}
