import { useState, useEffect, useCallback, useRef } from 'react';
import { app as api } from '@ekuaibao/whispered';
import { get } from 'lodash';

import { getUserDisplayName } from '../../../components/utils/fnDataLinkUtil'


interface UseCommentMentionProps {
  textareaId: string;
  onContentChange?: (value: string) => void;
  isVisibilityStaffs?: boolean;
}

export const useCommentMention = ({
  textareaId,
  onContentChange,
  isVisibilityStaffs = false,
}: UseCommentMentionProps) => {
  const [value, setValue] = useState('');
  const originalRangeRef = useRef<Range | null>(null);
  const focusNodeRef = useRef<Node | null>(null);

  const autoFocus = () => {
    const el = document.getElementById(textareaId);
    if(document.activeElement !== el) {
      el?.focus();
    }
  }

  // 在光标位置插入文本
  const insertTextAtCursor = useCallback((text: string) => {
    const el = document.getElementById(textareaId);
    if (!el) return;

    // 直接调用此API插入文本时，需要先聚焦
    autoFocus();

    const selection = window.getSelection();
    if (!selection) return;
    
    const textNode = document.createTextNode(text);
    const range = originalRangeRef.current || 
      (selection.rangeCount > 0 ? selection.getRangeAt(0) : document.createRange());

    range.deleteContents();
    range.insertNode(textNode);
    range.setStartAfter(textNode);
    range.collapse(true);

    selection.removeAllRanges();
    selection.addRange(range);

    originalRangeRef.current = range;
    focusNodeRef.current = textNode;
  }, [textareaId]);


  // 创建@按钮元素
  const createAtButton = useCallback((user: { userInfo: any; label: string }) => {
    const btn = document.createElement("span");
    btn.style.display = "inline-block";
    btn.dataset.user = JSON.stringify(user.userInfo);
    btn.className = "at-button";
    btn.contentEditable = "false";
    btn.textContent = `@${user.label}`;

    const wrapper = document.createElement("span");
    wrapper.style.display = "inline-block";
    wrapper.contentEditable = "false";

    const spaceElem = document.createElement("span");
    spaceElem.style.whiteSpace = "pre";
    spaceElem.textContent = "\u200b";
    spaceElem.contentEditable = "false";

    const clonedSpaceElem = spaceElem.cloneNode(true);
    wrapper.appendChild(spaceElem);
    wrapper.appendChild(btn);
    wrapper.appendChild(clonedSpaceElem);
    return wrapper;
  }, []);

  // 插入@按钮
  const insertAtButton = useCallback((userData: { userInfo: any; label: string }) => {
    const node = focusNodeRef.current;
    if (!node?.parentNode) return;

    const nextNode = node.nextSibling;
    const parentNode = node.parentNode;
    
    const prevNode = node.previousSibling;
    const hasSpace = prevNode?.textContent === '\u00A0';
    
    if (!hasSpace && prevNode) {
      const spaceNode = document.createTextNode('\u00A0');
      parentNode.insertBefore(spaceNode, node);
    }

    parentNode.removeChild(node);

    const atButton = createAtButton(userData);
    const spaceNode = document.createTextNode('\u00A0');

    if (nextNode) {
      parentNode.insertBefore(atButton, nextNode);
      parentNode.insertBefore(spaceNode, nextNode);
    } else {
      parentNode.appendChild(atButton);
      parentNode.appendChild(spaceNode);
    }
    resetCursor(spaceNode);
  }, [createAtButton]);

  // 重置光标位置
  const resetCursor = useCallback((insertedNode: Node) => {
    const selection = window.getSelection();
    if (!selection) return;

    const range = document.createRange();
    range.setStartAfter(insertedNode);
    range.collapse(true);

    selection.removeAllRanges();
    selection.addRange(range);
    originalRangeRef.current = range.cloneRange();
  }, []);

  // 保存光标位置
  const saveRange = useCallback(() => {
    const selection = window.getSelection();
    if (selection?.rangeCount > 0) {
      originalRangeRef.current = selection.getRangeAt(0).cloneRange();
    }
  }, []);

  // 处理@人员选择
  const fnSelectStaff = () => {
    const el = document.getElementById(textareaId);
    // 选人界面需要失焦，不然光标会展示出来
    el?.blur();

    return api.invokeService('@layout:select:staff', { isVisibilityStaffs })
      .then((staff: any) => {
      insertAtButton({
          label: getUserDisplayName(staff),
          userInfo: { id: staff.id }
      });
      updateValue();
      });
  }

  // 移动端点击 @ 符号插入的场景，需要插入零宽字符，否则删除时光标位置会乱跳
  const insertFakeNode = (el: HTMLElement) => {
    const hackNode = document.createTextNode('\u200B');
    el.appendChild(hackNode);
  }

  // 处理@提醒
  const handleMentionRemind = useCallback(() => {
    const el = document.getElementById(textareaId);
    if (!el) return;

    autoFocus();

    if (!el.innerText.trim()) {
      while (el.firstChild) {
        el.removeChild(el.firstChild);
      }
      originalRangeRef.current = null;
    }

    const selection = window.getSelection();
    if (!selection) return;

    if (originalRangeRef.current) {
      try {
        originalRangeRef.current.startContainer.nodeType;
      } catch (e) {
        originalRangeRef.current = null;
      }
    }

    if (originalRangeRef.current) {
      selection.removeAllRanges();
      selection.addRange(originalRangeRef.current);
    } else {
      const range = document.createRange();
      const lastChild = el.lastChild;
      if (lastChild) {
        range.setStartAfter(lastChild);
      } else {
        range.selectNodeContents(el);
      }
      range.collapse(false);
      selection.removeAllRanges();
      selection.addRange(range);
      originalRangeRef.current = range;
    }

    insertTextAtCursor('@');
    updateValue();

    insertFakeNode(el);
    fnSelectStaff();
  }, [textareaId, insertTextAtCursor, fnSelectStaff]);


  // 获取@内容
  const handleMentionResult = useCallback(() => {
    return new Promise((resolve, reject) => {
      const el = document.getElementById(textareaId);
      if (!el) return reject();

      let mentionContent = el.innerText;
      let validatorContent = el.innerText;
      const participants: string[] = [];
      // 1. 收集所有 @ 按钮及其位置信息
      const mentions: Array<{
        text: string;
        id: string;
        index: number;
      }> = [];

      // 用于跟踪已处理的文本长度
      let processedLength = 0;

      // 遍历所有 @ 按钮，按照 DOM 顺序记录它们在文本中的位置
      el.querySelectorAll('.at-button').forEach(button => {
        const userData = JSON.parse((button as HTMLElement).dataset.user || '{}');
        const id = get(userData, 'id', '');
        const displayName = button.textContent || '';
        
        // 从上一次处理位置开始查找
        const index = mentionContent.indexOf(displayName, processedLength);
        
        if (id && index !== -1) {
          mentions.push({
            text: displayName,
            id,
            index
          });
          participants.push(id);
          // 更新已处理的位置
          processedLength = index + displayName.length;
        }
      });

      // 2. 按位置从后向前替换，同时生成 validatorContent
      mentions
        .sort((a, b) => b.index - a.index)
        .forEach(mention => {
          const start = mention.index;
          const end = start + mention.text.length;
          
          // 处理 mentionContent：替换为 ID
          mentionContent = 
            mentionContent.slice(0, start) + 
            `{${mention.id}}` + 
            mentionContent.slice(end);
          
          // 处理 validatorContent：直接删除 @ 内容
          validatorContent = 
            validatorContent.slice(0, start) + 
            validatorContent.slice(end);
        });

      resolve({ 
        comment: mentionContent, 
        participants, 
        validatorContent
      });
    });
  }, [textareaId]);

  // 检查是否需要触发@
  const checkIfRemind = (event: KeyboardEvent) => {
    const isAtKey = event.key === '@';
    const range = originalRangeRef.current;
    if (!range) return;

    const position = range.startOffset;
    const startNode = range.startContainer;

    if (position > 0 && startNode.textContent) {
      const checkCharacter = startNode.textContent.charAt(position - 1);

      if (checkCharacter === '@' && isAtKey) {
        const atRange = document.createRange();
        atRange.setStart(startNode, position - 1);
        atRange.setEnd(startNode, position);
        atRange.deleteContents();

        const hackNode = document.createTextNode('@');
        atRange.insertNode(hackNode);
        focusNodeRef.current = hackNode;
        resetCursor(hackNode);
        fnSelectStaff();
      }
    }
  }

  // 处理键盘事件
  const handleKeyUp = useCallback((event: KeyboardEvent) => {
    saveRange();
    checkIfRemind(event);
  }, [saveRange, checkIfRemind]);

  // 过滤零宽字符
  const getVisibleText = (text: string) => {
    return text
      // 移除零宽字符
      .replace(/[\u200B-\u200D\uFEFF]/g, '')
      // 保留换行符
      .replace(/\r\n/g, '\n')
      // 移除其他不可见字符
      // .replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
  };

  // 更新内容
  const updateValue = useCallback(() => {
    const el = document.getElementById(textareaId);
    if (!el) return;

    const newValue = el.textContent || '';
    if (!newValue.trim() && el.childNodes.length === 1 && el.firstChild.nodeName === 'BR') {
      el.innerHTML = '';
    }

    const visibleText = getVisibleText(newValue);

    setValue(visibleText);
    onContentChange?.(visibleText);
  }, [textareaId, onContentChange]);

  const handlePaste = useCallback((e: ClipboardEvent) => {
    e.preventDefault();
    
    // 从剪贴板获取纯文本
    const pastedText = e.clipboardData?.getData('text/plain') || '';
    
    // 如果粘贴的内容为空，直接返回
    if (!pastedText) return;
    
    const el = document.getElementById(textareaId);
    if (!el) return;
    
    // 获取当前选区
    const selection = window.getSelection();
    if (!selection?.rangeCount) return;
    const range = selection.getRangeAt(0);
    
    // 删除选中内容
    range.deleteContents();
    
    // 处理换行符和空格
    const lines = pastedText.split(/\r\n|\r|\n/);
    const fragment = document.createDocumentFragment();
    
    lines.forEach((line, index) => {
      // 添加文本，保留空格
      const textNode = document.createTextNode(line);
      fragment.appendChild(textNode);
      
      // 在每行后面添加换行符（最后一行除外）
      if (index < lines.length - 1) {
        fragment.appendChild(document.createElement('br'));
      }
    });
    
    // 插入处理后的内容
    range.insertNode(fragment);

    if (fragment.lastChild) {
      range.setStartAfter(fragment.lastChild);
    } else {
      range.collapse(false); // 如果没有子节点，设置光标在 range 的末尾
    }
    
    // 清空选中状态
    selection.removeAllRanges();
    selection.addRange(range); // 重新设置光标位置
    
    // 保存新的光标位置并更新内容
    originalRangeRef.current = range.cloneRange();
    updateValue();
  }, [textareaId, updateValue]);

  // 设置事件监听
  useEffect(() => {
    const el = document.getElementById(textareaId);

    if (el) {
      el.addEventListener('mouseup', saveRange);
      el.addEventListener('keyup', handleKeyUp);
      // 考虑中文输入法相关事件
      el.addEventListener('compositionstart', saveRange);
      el.addEventListener('compositionend', saveRange);
      el.addEventListener('paste', handlePaste);
    }

    return () => {
      if (el) {
        el.removeEventListener('mouseup', saveRange);
        el.removeEventListener('keyup', handleKeyUp);
        el.removeEventListener('compositionstart', saveRange);
        el.removeEventListener('compositionend', saveRange);
        el.removeEventListener('paste', handlePaste);
      }
    };
  }, [textareaId, saveRange, handleKeyUp, handlePaste]);

  return {
    value,
    handleMentionRemind,
    handleMentionResult,
    updateValue,
    insertTextAtCursor,
    resetCursor,
    textareaId,
  };
};
