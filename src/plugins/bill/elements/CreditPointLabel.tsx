/**************************************
 * Created By LinK On 2020/4/21 19:48.
 **************************************/

import React, { PureComponent } from 'react'
import styles from './CreditPointLabel.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'

interface Submitter extends StringAnyProps {
  id?: string
  name?: string
}

interface CreditPointObj extends StringAnyProps {
  level: string
  point: number
  detailReadable: boolean
  display: boolean
  updateTime: number
}

interface Props {
  ownerId: Submitter
  submitter: Submitter
  myCreditPoint: CreditPointObj
  flowId: string
  billNotes: any[]
  activeCreditRule: any[]
  canEditNote?: boolean
  detailReadable: boolean
  authorRemovable: boolean
  addNoteMode: boolean
  billNotesInHistory: any[]
  showBillNotesInHistory: boolean
}

@EnhanceConnect((state: any) => ({
  myCreditPoint: state['@bill'].myCreditPoint,
  Credit: state['@common'].powers.Credit,
  billNotes: state['@bill'].billNotes,
  activeCreditRule: state['@bill'].activeCreditRule,
  addNoteMode: state['@bill'].addNoteMode,
  detailReadable: state['@bill'].detailReadable,
  authorRemovable: state['@bill'].authorRemovable,
  billNotesInHistory: state['@bill'].billNotesInHistory,
  showBillNotesInHistory: state['@bill'].showBillNotesInHistory
}))
export default class CreditPointLabel extends PureComponent<Props> {
  state = { creditDetail: '' }

  componentWillReceiveProps(np: Props) {
    const { myCreditPoint } = this.props
    const { myCreditPoint: npMyCreditPoint } = np
    if (myCreditPoint.updateTime !== npMyCreditPoint.updateTime) {
      this.fnRefreshCreditPont(np)
    }
  }

  componentDidMount() {
    const submitter: Submitter = this.props.submitter || {}
    const owner: Submitter = this.props.ownerId || {}
    const { id: ownerId } = owner
    const { id: submitterId } = submitter
    if (!submitterId) return
    //@ts-ignore
    const { Credit, myCreditPoint } = this.props
    if (!Credit) return
    if (myCreditPoint.display) {
      this.fnRefreshCreditPont(this.props)
    }
    const id = ownerId ? (ownerId === submitterId ? submitterId : ownerId) : submitterId
    api.invokeService('@bill:get:my:credit:point', id)
  }

  fnRefreshCreditPont = (props: Props) => {
    const owner: Submitter = props.ownerId || {}
    const { name = '' } = owner
    if (!name) return
    const { myCreditPoint } = props
    const { level, point, detailReadable, display } = myCreditPoint
    if (!display) return
    let creditDetail = i18n.get(`{__k0} 信用等级：{__k1}级`, { __k0: name, __k1: level })
    if (detailReadable) creditDetail = i18n.get(`{__k0}（{__k1}）`, { __k0: creditDetail, __k1: point })
    this.setState({ creditDetail })
  }
  showCreditNote = () => {
    const {
      flowId,
      canEditNote,
      detailReadable,
      authorRemovable,
      showBillNotesInHistory,
      billNotes,
      billNotesInHistory
    } = this.props
    let noteArr = showBillNotesInHistory ? billNotesInHistory : billNotes
    noteArr = noteArr?.filter((it: { type: string }) => it.type === 'CREDIT')
    api.open('@bill:NoteListModal', {
      headerTitle: i18n.get('信用批注'),
      riskList: [],
      riskTitle: '',
      noteArr,
      flowId,
      detailId: '',
      isDetail: '',
      field: 'Credit-Note-Pop',
      canEditNote,
      detailReadable,
      authorRemovable,
      showBillNotesInHistory
    })
  }

  render() {
    const { creditDetail } = this.state
    if (!creditDetail) return null
    return (
      <div className={styles['creditPointLabel-wrap']} onClick={this.showCreditNote}>
        {creditDetail}
      </div>
    )
  }
}
