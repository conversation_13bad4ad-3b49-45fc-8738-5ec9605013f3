import React from 'react';
import { useCommentMention } from './useCommentMention';

// Hook 返回值类型定义
interface CommentMentionHook {
  value: string;
  handleMentionRemind: () => void;
  handleMentionResult: () => Promise<{ comment: string; participants: string[] }>;
  updateValue: () => void;
  insertTextAtCursor: (text: string) => void;
  resetCursor: (insertedNode: Node) => void;
  textareaId: string;
}

// Hook 配置参数类型定义
interface CommentMentionConfig {
  textareaId?: string;
  onContentChange?: (value: string) => void;
  isVisibilityStaffs?: boolean;
}

// 注入到组件的 Props 类型定义
export interface WithCommentMentionProps {
  commentMention: CommentMentionHook;
  isVisibilityStaffs?: boolean;
  commentMentionConfig?: CommentMentionConfig;
}

// 原始组件的 Props 类型定义（不包含注入的 props）
type OriginalProps<P> = Omit<P, keyof WithCommentMentionProps>;

export const withCommentMention = <P extends WithCommentMentionProps>(
  WrappedComponent: React.ComponentType<P>,
  defaultConfig: CommentMentionConfig = {}
) => {
  return function WithCommentMentionComponent(
    props: OriginalProps<P> & { 
      isVisibilityStaffs?: boolean;
      commentMentionConfig?: CommentMentionConfig;
    }
  ) {
    const { 
      isVisibilityStaffs,
      commentMentionConfig,
      ...restProps 
    } = props;

    const commentMention = useCommentMention({
      textareaId: 'commentTextarea',
      isVisibilityStaffs,
      ...defaultConfig,
      ...commentMentionConfig,
    });

    return (
      <WrappedComponent
        {...(restProps as P)}
        commentMention={commentMention}
        isVisibilityStaffs={isVisibilityStaffs}
      />
    );
  };
};