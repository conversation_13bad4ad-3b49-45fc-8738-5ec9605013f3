import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import * as util from '../../../lib/util'
import BillSubmitReasonV2, { I_InvoiceRiskArr } from './BillSubmitReasonV2'
import { T_RiskWarningItem } from './BillSubmitReasonV2/type'

interface IProps {
  flowId: string
  invoiceRiskArr: T_RiskWarningItem[]
  layer: any
  overrideGetResult: any
}

interface IState {

}

export default class BillRiskReasonModal extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props)
    props.overrideGetResult(this.getResult.bind(this))
  }

  handleRiskReasonSubmit = async (invoiceRiskArr: I_InvoiceRiskArr) => {
    const { flowId } = this.props
    // 提交原因数据
    const res = await api.invokeService(
      '@bill:save:invoice:reason:batch',
      invoiceRiskArr.map((item: { invoiceRiskExplainId: any; invoiceNum: any; invoiceNormId: any; invoiceRiskExplainContent: any }) => {
        return {
          id: item.invoiceRiskExplainId,
          flowId,
          invoiceId: item.invoiceNum,
          invoiceNormId: item.invoiceNormId,
          content: item.invoiceRiskExplainContent
        }
      }))
    if (res?.value) {
      util.toast.success(i18n.get('保存成功'))
      this.props.layer.emitOk()
    }
  }

  getResult = () => {
    return
  }

  render() {
    const { invoiceRiskArr = [] } = this.props
    return (
      <BillSubmitReasonV2 riskWarningArr={invoiceRiskArr} handleSubmit={this.handleRiskReasonSubmit} handleCancel={() => {
        this.props.layer.emitCancel()
      }} />
    )
  }
}