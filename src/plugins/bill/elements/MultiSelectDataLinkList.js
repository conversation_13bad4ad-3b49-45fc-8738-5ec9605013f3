import { app, app as api } from '@ekuaibao/whispered'
import styles from './datalink/DataLinkList.module.less'
import React, { PureComponent } from 'react'
const EKBSearchBar = app.require('@ekb-components/base/EKBSearchBar')
import { Checkbox, Icon } from 'antd-mobile'
import { Dialog } from '@hose/eui-mobile'
import {
  formatEntityList,
  getNameCodeSearchKey,
  filterDataLinkUseCount,
  handleDataLinkUseCount,
  formatData
} from '../utils/dataLinkUtils'
import classnames from 'classnames'
import { EnhanceConnect } from '@ekuaibao/store'
import { get, sortBy } from 'lodash'
const ActionsPanel = app.require('@elements/ActionsPanel')
const EKBIcon = app.require('@elements/ekbIcon')
// const { cancel, selectFeetype } = app.require('@basic-elements/basic-state-const')
import { toast } from '../../../lib/util'
const ThirdImportListItem = app.require('@third-import/thirdImport-list-item')
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import ListTitle from './datalink/ListTitle'
const { getStrLastWord } = app.require('@components/utils/fnDataLinkUtil')
import { TravelCard } from './datalink/TravelCard'
import { getTravelOrderData, getWeek } from './datalink/dataLinkUtil'
import moment from 'moment'
import SelectDropDown from '../components/SelectDropDown/SelectDropDown'
import RouteListFilter from '../../mycarbusiness/element/routerListFilter'
const AgreeItem = Checkbox.AgreeItem
const CheckboxItem = Checkbox.CheckboxItem
let timeout
const packageSize = 200
const packageCount = 999
const tripOrderPackageSize = 9999 //行程订单一次性取值
import FILTERICON from '../images/filter.svg'
import { QuerySelect } from 'ekbc-query-builder'
import Popup from '@ekuaibao/popup-mobile/esm/index'
import FilterForm from './datalink/datalink-detail-modal/elements/FilterForm'
import { isString } from '@ekuaibao/helpers'

@EnhanceTitleHook()
@EnhanceConnect(state => ({
  entityListData: state['@bill'].entityListData,
  dataLink: state['@bill'].dataLink,
  searchDataLink: state['@mine'].searchDataLink,
  powerCodeMap: state['@common'].powers.powerCodeMap
}))
export default class MultiSelectDataLinkList extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      listData: [],
      selectedList: {},
      entityId: props.id,
      searchValue: '',
      isLoading: false,
      hasMore: true,
      isAllChecked: false,
      template: {},
      start: '',
      loading: false,
      tripOptions: [],
      isChecked: false,
      typeFilter: null,
      filterByOther: '',
      filter: {}
    }
    this.filterByKey = undefined
  }

  componentWillMount() {
    let { value } = this.props
    if (value && value.disableStrategy === 'LIMIT_COUNT') {
      api.invoke('get:bills:value').then(result => {
        this.map = handleDataLinkUseCount(result)
        this.getEntityList()
      })
    } else {
      this.getEntityList()
    }
    this.isTripOrder() && this.getTripOptions()
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.entityListData !== nextProps.entityListData) {
      this.handleFormatDataLink(nextProps.entityListData)
    } else if (this.props.searchDataLink !== nextProps.searchDataLink) {
      this.handleFormatDataLinknew(nextProps.searchDataLink)
    }
  }
  getEntityList(filterBy, start = 0) {
    const { value, flowId, filterId, expenseIds = [], formData = {}, otherFilters } = this.props
    if (start === 0) {
      this.setState({ loading: true })
    }
    const isPrivateCar = get(value, 'platformId.type') === 'PRIVATE_CAR'
    const entityId = get(value, 'id')
    const { submitterId } = formData
    const { typeFilter, filterByOther } = this.state
    let currentEntityId = '' // 行程订单可按机酒火等类型筛选
    if (this.isTripOrder() && typeFilter) {
      currentEntityId = typeFilter.id
    }
    let params = { entityId: currentEntityId || entityId, isPrivateCar: isPrivateCar }
    if (submitterId) {
      params = { ...params, submitterId }
    }
    if (flowId) {
      params = { ...params, flowId }
    }
    if (isPrivateCar) {
      params = filterBy ? { ...params, filterBy } : { ...params, start, count: packageCount }
      api.invokeService('@bill:get:entity:list', params).then(item => {
        this.setState({ loading: false })
      })
    } else {
      const isOrder = this.isTripOrder()
      let orderBy = undefined
      let filter = 'active==true'
      const query = new QuerySelect().filterBy(filter)
      if (isOrder) {
        const filterCode =
          expenseIds && expenseIds.length
            ? expenseIds.map(item => `form.E_${entityId}_申请单编号.containsIgnoreCase("${item}")`).join(' || ') //@i18n-ignore
            : ''
        if (filterCode) {
          query.filterBy(filterCode)
        }
        orderBy = [
          { value: `form.E_${entityId}_出发时间`, order: 'DESC' }, //@i18n-ignore
          { value: `form.E_${entityId}_入住日期`, order: 'DESC' } //@i18n-ignore
        ]
        params = { ...params, params: { type: 'order' } } //传参数给后台表明是行程管理里面的订单，需要后台根据配置来过滤数据 }
      }
      if (filterBy) {
        query.filterBy(filterBy)
      }
      if (filterByOther) {
        query.filterBy(filterByOther)
      }
      if (otherFilters?.length) {
        if (Array.isArray(otherFilters)) {
          otherFilters.forEach(filter => {
            query.filterBy(filter)
          })
        } else if (isString(otherFilters)) {
          query.filterBy(otherFilters)
        }
      }
      params = {
        ...params,
        type: 'LIST',
        start,
        count: isOrder ? tripOrderPackageSize : packageSize
      }
      params = { ...params, ...query.value() }
      if (filterId) {
        params = { ...params, filterId, form: formData }
      }
      if (isOrder) {
        params = { ...params, orderBy }
      }
      return api.invokeService('@mine:post:serach:dataLink', params).then(res => {
        this.setState({ loading: false })
        const temp = res.items.template.content.expansion
        this.setState({
          template: temp,
          start: start + packageSize
        })
      })
    }
  }

  handleFormatDataLink(result) {
    let { listData } = this.state
    let { value } = this.props
    let entityList = get(result, 'items') || []
    entityList = entityList.slice()
    let hasMore = listData.length + entityList.length === result.count
    entityList =
      get(value, 'disableStrategy') === 'LIMIT_COUNT' ? filterDataLinkUseCount(this.map, entityList) : entityList
    let newEntityList = formatEntityList(value.fields, entityList)
    let cListData = listData.concat(newEntityList)
    if (cListData.every(v => v?.dataLink?.id)) {
      const map = new Map()
      cListData.forEach(item => {
        if (!map.has(item?.dataLink?.id)) {
          map.set(item?.dataLink?.id, item)
        }
      })
      cListData = [...map.values()]
    }
    this.setState({ listData: cListData, isLoading: false, hasMore: !hasMore })
    this.filterByKey = this.filterByKey ? this.filterByKey : getNameCodeSearchKey(entityList)
  }
  handleFormatDataLinknew(result) {
    let { listData } = this.state
    let { value } = this.props
    let entityList = get(result, 'items.data') || []
    entityList = entityList.slice()
    let hasMore = listData.length + entityList.length === result.items.total
    entityList =
      get(value, 'disableStrategy') === 'LIMIT_COUNT' ? filterDataLinkUseCount(this.map, entityList) : entityList
    let newEntityList = formatEntityList(value.fields, entityList)
    let cListData = listData.concat(newEntityList)
    if (cListData.every(v => v?.dataLink?.id)) {
      const map = new Map()
      cListData.forEach(item => {
        if (!map.has(item?.dataLink?.id)) {
          map.set(item?.dataLink?.id, item)
        }
      })
      cListData = [...map.values()]
    }
    let { template } = result.items
    this.setState({ listData: cListData, isLoading: false, hasMore: !hasMore, template })
    if (entityList.length > 0) {
      this.filterByKey = this.filterByKey ? this.filterByKey : this.getSearchKey(entityList[0].dataLink)
    }
  }
  getSearchKey = form => {
    let nameKey = '',
      codeKey = '',
      formKey = '',
      toKey = ''
    for (let key in form) {
      if (getStrLastWord(key, '_') === 'name') {
        nameKey = key
      } else if (getStrLastWord(key, '_') === 'code') {
        codeKey = key
      } else if (getStrLastWord(key, '_') === i18n.get('出发地')) {
        // @i18n-ignore
        formKey = key
      } else if (getStrLastWord(key, '_') === i18n.get('目的地')) {
        // @i18n-ignore
        toKey = key
      }
    }
    return { nameKey, codeKey, formKey, toKey }
  }

  handleOnSearchChange = value => {
    value = value.trim()
    if (!this.filterByKey) return this.setState({ searchValue: value })
    let { nameKey, codeKey } = this.filterByKey || {}
    let filterBy = value ? `(form.${nameKey}.containsIgnoreCase("${value}") || form.${codeKey}.containsIgnoreCase("${value}"))` : ''
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
    this.setState({ searchValue: value, listData: [] }, () => {
      timeout = setTimeout(() => {
        this.getEntityList.call(this, filterBy)
      }, 400)
    })
  }

  handleCancel() {
    this.setState({ searchValue: '', listData: [] }, () => {
      this.getEntityList()
    })
  }

  handleSearchCancel = () => {
    this.handleCancel()
  }

  handleOnSearchClear = () => {
    this.setState({ searchValue: '' })
  }
  //实体查看详情
  handleOnnewItemClick = (rowData, updateAllCheck = false) => {
    let field = this.props.value
    let rowValue = {}
    rowValue.data = rowData.dataLink
    rowValue.id = rowData.dataLink.id
    rowValue.template = this.state.template
    const title = get(field, 'referenceData.name')
    const isOrder = this.isTripOrder()
    if (isOrder) {
      api.open('@bill:DataLinkEntityTripOrderDetailModal', {
        field,
        value: { ...rowValue, data: { dataLink: rowData.dataLink } },
        title
      })
    } else {
      api.open('@bill:DataLinkEntityDetailModal', { field, value: rowValue })
    }
  }
  //私车公用选中
  handleOnItemClick = (rowData, updateAllCheck = false) => {
    let { id } = rowData
    let { selectedList, listData } = this.state
    if (!selectedList[id]) {
      selectedList[id] = rowData
      if (updateAllCheck) {
        this.setState({ selectedList, isAllChecked: Object.keys(selectedList).length === listData.length })
      } else {
        this.setState({ selectedList })
      }
    } else {
      if (updateAllCheck) {
        this.setState({ isAllChecked: false })
      }
      delete selectedList[id]
    }
    this.forceUpdate()
  }

  renderEmpty(isPrivateCar) {
    const { loading } = this.state
    return (
      <div className="datalink-empty">
        {loading ? i18n.get('正在加载...') : isPrivateCar ? i18n.get('暂无行车记录') : i18n.get('无可选项')}
      </div>
    )
  }

  renderListFooter = () => {
    let { isLoading, hasMore } = this.state
    let endStr = hasMore ? i18n.get('加载完毕') : i18n.get('没有更多数据了')
    return <div style={{ padding: 30, textAlign: 'center' }}>{isLoading ? i18n.get('加载更多') : endStr}</div>
  }

  isTripOrder = () => {
    const type = get(this.props.value, 'platformId.type', '')
    const referenceType = get(this.props.value, 'type', '')
    const isOrder = type === 'TRAVEL_MANAGEMENT' && referenceType === 'ORDER'
    return isOrder
  }
  renderItem = rowData => {
    let { template, selectedList, isChecked } = this.state
    let id = get(rowData, 'dataLink.id')
    let checked = !!selectedList[id]
    let cls = classnames('item-wrapper', { 'bg-checked': checked })
    const style = { width: '100%', display: 'block' }
    return (
      <div className={cls} style={style}>
        <CheckboxItem checked={checked} onChange={this.handleSelectItemClick.bind(this, rowData)}>
          <div onClick={this.handleOnnewItemClick.bind(this, rowData)}>
            <ListTitle data={rowData} template={template} isShowAll={isChecked} key={id} />
          </div>
        </CheckboxItem>
      </div>
    )
  }
  handleSelectItemClick = (rowData, updateAllCheck = false) => {
    let id = get(rowData, 'dataLink.id')
    let { selectedList, listData } = this.state
    if (!selectedList[id]) {
      selectedList[id] = rowData
      if (updateAllCheck) {
        this.setState({ selectedList, isAllChecked: Object.keys(selectedList).length === listData.length })
      } else {
        this.setState({ selectedList })
      }
    } else {
      if (updateAllCheck) {
        this.setState({ isAllChecked: false })
      }
      delete selectedList[id]
    }
    this.forceUpdate()
  }

  renderPrivateCarItem = rowData => {
    const { id } = rowData
    let { selectedList } = this.state
    let checked = !!selectedList[rowData.id]
    const data = formatData(rowData, this.props.value)

    return (
      <ThirdImportListItem
        key={id}
        onCheckedChange={() => {
          this.handleOnItemClick(rowData, true)
        }}
        data={data}
        isChecked={checked}
        disabled={false}
      />
    )
  }
  isPrivateCar = () => {
    const type = get(this.props.value, 'platformId.type', '')
    return 'PRIVATE_CAR' === type
  }
  buttonClick = params => {
    const { name = '' } = params
    const { selectedList } = this.state
    const { bus, getRuleDataLinkFeetype, specificationId, value, formType, formData } = this.props
    switch (name) {
      case 'cancel':
        this.props.layer.emitCancel()
        break
      case 'select.feetype':
        if (Object.keys(selectedList).length <= 0) {
          toast.error(this.isPrivateCar() ? i18n.get('请选择待报销的行车记录') : i18n.get('请选择业务对象明细'))
          return
        }
        getRuleDataLinkFeetype({
          specificationId,
          dataLinkEntityId: value.id,
          type: formType,
          billData: formData
        }).then(async resp => {
          const dataList = Object.values(selectedList).map(item => {
            return item.dataLink || item
          })
          if (!resp.error) {
            let items = get(resp, 'payload.items') || []
            const orgItems = items
            let feetypeConfigMap = undefined
            if (get(value, 'type') === 'ORDER' && get(value, 'platformId.type') === 'TRAVEL_MANAGEMENT') {
              //行程订单，在行程管理里面有订单类型对应的消费类型的配置
              const feetypeConfig = await api.invokeService('@bill:get:travelManagementConfig', {
                type: 'associatedFeeType'
              })
              const contextDetail = feetypeConfig?.value?.contextDetail
              if (contextDetail && contextDetail.length) {
                //如果配置了导入订单的费用类型，过滤掉不能用的费用类型
                for (let item of contextDetail) {
                  const feetype = orgItems.find(o => o.id === item.feeTypeId)
                  if (feetype) {
                    const paymentMethod = item.paymentMethod
                    const travelTypes = item.travelTypes
                    if (!feetypeConfigMap) {
                      feetypeConfigMap = {}
                    }
                    travelTypes.forEach(o => {
                      feetypeConfigMap[`${paymentMethod}${o}`] = feetype
                    })
                    items = items.filter(i => i.id !== feetype.id)
                  }
                }
              }
            }
            //@i18n-ignore
            const payMap = {
              个人支付: 'personalPayment',
              企业支付: 'enterprisePayment',
              混合支付: 'mixedPayment',
              个人: 'personalPayment',
              企业: 'enterprisePayment'
            }
            const orderListFeetype = []
            const orderListOther = []
            if (feetypeConfigMap) {
              dataList.forEach(item => {
                const parentId = get(item, 'entity.parentId') || get(item, 'entity.id')
                const type = get(item, 'entity.type')
                //@i18n-ignore
                const payType = get(item, `E_${parentId}_支付方式`)
                const feetype = feetypeConfigMap[`${payMap[payType]}${type}`] || feetypeConfigMap[`all${type}`]
                if (feetype) {
                  item.feeType = feetype
                  orderListFeetype.push(item)
                } else {
                  orderListOther.push(item)
                }
              })
            }
            if (orderListFeetype.length === dataList.length) {
              this.updateCousume(undefined, orderListFeetype, value)
            } else {
              let copySelectedDatas = dataList
              if (orderListFeetype.length > 0) {
                copySelectedDatas = [...orderListFeetype, ...orderListOther]
              }
              if (items.length === 0) {
                if (orderListFeetype.length > 0) {
                  api
                    .open('@bill:ImportTripOrderConfirmModal', {
                      headerTitle: i18n.get('提示'),
                      content: i18n.get(`有${orderListOther.length}条行程订单没有匹配到对应的费用模板,是否继续？`)
                    })
                    .then(result => {
                      const { type } = result
                      if (type === 'ok') {
                        this.updateCousume(undefined, orderListFeetype, value)
                      }
                    })
                } else {
                  toast.error(i18n.get('无可用消费类型，请联系管理员'))
                }
              } else if (items.length === 1) {
                const feeType = items[0]
                this.updateCousume(feeType, copySelectedDatas, value)
              } else {
                api
                  .open('@feetype:SelectFeeTypeModal', {
                    feetype: items,
                    isShowCancelBtn: true,
                    parentLayer: this.props.layer,
                    formType
                  })
                  .then(feetype => {
                    if (feetype === 'cancel') {
                      return this.props.layer.emitCancel()
                    }
                    this.updateCousume(feetype, copySelectedDatas, value)
                  })
              }
            }
          }
        })
        break
    }
  }
  updateCousume = (feetype, copySelectedDatas, value) => {
    const { bus } = this.props
    const val = { type: 'dataLink', feetype, dataLinkList: copySelectedDatas, dataLinkEntity: value }
    bus.invoke('element:details:import:data:format', val).then(consume => {
      this.props.layer.emitOk({ consume })
    })
  }
  handleCheckedAll = e => {
    // 兼容@hose/eui-mobile
    let checked = e
    if (e.target) {
      checked = e.target.checked
    }
    let { listData } = this.state
    let selectedList = { ...this.state.selectedList }
    if (checked) {
      listData.forEach(item => {
        selectedList[item.id || get(item, 'dataLink.id')] = item
      })
    } else {
      const ids = listData.map(item => item.dataLink.id)
      const selected = {}
      Object.keys(selectedList)
        .filter(id => !ids.includes(id))
        .forEach(id => {
          selected[id] = selectedList[id]
        })
      selectedList = selected
    }
    this.setState({
      selectedList,
      isAllChecked: checked
    })
  }
  renderOrder = () => {
    const { listData } = this.state
    const tripGroup = this.getGroup(listData)
    return (
      <div className="travel-order-wrapper">
        {Object.keys(tripGroup).map((date, index) => {
          return this.renderTripGroup(date, tripGroup[date], index)
        })}
      </div>
    )
  }
  renderTripGroup = (date, list, index) => {
    const { selectedList } = this.state
    return (
      <div key={`group${index}`}>
        <div className="date-group">{date}</div>
        {list &&
          list.map((trip, i) => {
            const id = get(trip, 'dataLink.id')
            const checked = !!selectedList[id]
            const data = getTravelOrderData(trip.dataLink)
            return (
              <div className="trip-order-item" key={`trip${id}`}>
                <Checkbox checked={checked} onChange={this.handleSelectItemClick.bind(this, trip)} />
                <TravelCard
                  style={{ flex: 1, marginLeft: 5 }}
                  data={data}
                  key={i}
                  handleCardClick={this.handleOnnewItemClick.bind(this, trip)}
                ></TravelCard>
              </div>
            )
          })}
      </div>
    )
  }
  getDateValue = item => {
    const value = item.dataLink
    const isHotel = get(item, 'dataLink.entity.type') === 'HOTEL'
    const isShop = get(item, 'dataLink.entity.type') === 'SHOP'
    const startDate = isHotel
      ? value[Object.keys(value).find(o => !!o.endsWith('入住日期'))] // @i18n-ignore
      : isShop
        ? value[Object.keys(value).find(o => !!o.endsWith('订单日期'))]
        : value[Object.keys(value).find(o => !!o.endsWith('出发时间'))] // @i18n-ignore
    return startDate
  }
  getGroup = trips => {
    const newDataList = sortBy(trips, item => {
      return -this.getDateValue(item)
    })
    const tripsMap = {}
    newDataList.forEach(item => {
      const startDate = this.getDateValue(item)
      const date = `${moment(startDate).format('YYYY年MM月DD日')} ${getWeek(startDate)}`
      if (tripsMap[date]) {
        tripsMap[date].push(item)
      } else {
        tripsMap[date] = [item]
      }
    })
    return tripsMap
  }
  renderTripOrderBottom = () => {
    const { selectedList, listData } = this.state
    const selectedKeys = Object.keys(selectedList)
    const selectedLength = selectedKeys.length ?? 0
    const dataListLength = listData.length
    const checkedAll = listData.filter(item => selectedKeys.includes(item.dataLink.id)).length === dataListLength
    return (
      <div className={styles.thirdList_footer}>
        <AgreeItem checked={checkedAll} onChange={this.handleCheckedAll}>
          {i18n.get('全选')}
          <span style={{ marginLeft: 8 }}>{i18n.get('已选({__key0})', { __key0: selectedLength })}</span>
        </AgreeItem>
        <div
          onClick={() => {
            this.buttonClick({ name: 'select.feetype' })
          }}
          className={styles.thirdList_footer_btn}
        >
          <span>{i18n.get('导入消费')}</span>
        </div>
      </div>
    )
  }
  getTripOptions = () => {
    const { value } = this.props
    const id = get(this.props.value, 'platformId.id')
    api.invokeService('@bill:get:entity:details:list', { id }).then(res => {
      const entity = res.items.find(i => i.id === value.id)
      entity.name = '全部' // @i18n-ignore
      const tripOptions = [entity, ...entity.children]
      this.setState({
        tripOptions
      })
    })
  }
  filterData = typeFilter => {
    this.setState({ typeFilter, searchValue: '', listData: [] }, () => {
      this.getEntityList()
    })
  }
  handleShow = () => {
    const { tripOptions } = this.state
    Dialog.confirm({
      title: i18n.get('提示'),
      content: i18n.get('因差旅平台同步订单有一定的时效性，可通过手动同步差旅订单'),
      confirmText: i18n.get('刷新'),
      onConfirm: () => {
        api.open('@third-import:SelectDateRange', { months: 1 }).then(async params => {
          const { startTime, endTime } = params
          const result = await api.invokeService('@bill:pull:TravelOrder', {
            startDate: startTime,
            endDate: endTime
          })
          if (result?.value?.success) {
            toast.success(i18n.get('同步成功'))
            this.setState(
              {
                selectedList: [],
                listData: [],
                searchValue: '',
                tripOptions: [...tripOptions],
                typeFilter: tripOptions[0]
              },
              () => {
                this.getEntityList()
              }
            )
          } else {
            toast.success(i18n.get('同步失败'))
          }
        })
      }
    })
  }
  handlePopupCancel = () => {
    Popup.hide()
  }
  handleFilterData = (filter, filterByOther) => {
    Popup.hide()
    const { filterBy } = this.state
    this.setState({ filter, isChecked: filter.show_full_name, filterByOther: filterByOther?.filterBy }, () => {
      this.getEntityList(filterBy)
    })
  }
  hanldeFilter = () => {
    const fields = this.props?.value?.referenceData?.fields || []
    Popup.show(
      <div className={styles['popup-modal']} onCancel={this.handlePopupCancel}>
        <div className="modal-title">
          <div className="title ">{i18n.get('筛选条件')}</div>
          <Icon type="cross" onClick={this.handlePopupCancel} />
        </div>
        <span className="modal-title-tips">{i18n.get('您可对当前业务对象下的字段进行筛选，')}</span>
        <span className="modal-title-tips mb-8">{i18n.get('展示结果等于您输入所有条件的组合')}</span>
        <FilterForm fields={fields} filter={this.state.filter} handleFilter={this.handleFilterData}></FilterForm>
      </div>,
      {
        animationType: 'slide-up',
        maskClosable: true,
        wrapClassName: 'popup-wrapper-style'
      }
    )
  }

  /** 过滤私车公用数据 */
  filerPrivateCar = param => {
    const { value } = this.props
    const entityId = get(value, 'id')
    let filterArr = []
    if (param.cause) {
      filterArr.push(`form.E_${entityId}_事由.containsIgnoreCase("${param.cause}")`)
    }
    if (param.startTime) {
      filterArr.push(`form.E_${entityId}_行驶日期>=${param.startTime}`)
    }
    if (param.endTime) {
      filterArr.push(`form.E_${entityId}_行驶日期<=${param.endTime}`)
    }
    this.setState({
      listData: []
    })
    this.getEntityList(filterArr.join(' && '))
  }

  render() {
    let { listData, searchValue, selectedList, isAllChecked, tripOptions } = this.state
    const isPrivateCar = this.isPrivateCar()
    const isOrder = this.isTripOrder()
    const isHoseMall = this.props.powerCodeMap.includes('120209')
    return (
      <div className={styles['dataLink-wrapper']}>
        {isOrder && <SelectDropDown filterSource={tripOptions} onChange={this.filterData} />}
        {!isPrivateCar && (
          <div className="search-wrapper">
            <div className="eui-search-bar-wrapper">
              <EKBSearchBar
                value={searchValue}
                placeholder={i18n.get('搜索')}
                onChange={this.handleOnSearchChange}
                onCancel={this.handleSearchCancel}
                onClear={this.handleOnSearchClear}
              />
            </div>
            <div onClick={this.hanldeFilter} className="search-wrapper-filter">
              <img className="mr-5" src={FILTERICON}></img>
              {i18n.get('筛选')}
            </div>
          </div>
        )}
        {isOrder && isHoseMall && (
          <div className="sync-wrapper" onClick={this.handleShow}>
            <EKBIcon name="#EDico-info-circle" />
            <div className="info">{i18n.get('找不到订单？')}</div>
            <EKBIcon name="#EDico-right-default" />
          </div>
        )}
        {isPrivateCar && (
          <>
            <RouteListFilter
              checked={isAllChecked}
              onChange={this.handleCheckedAll}
              isImport={true}
              onFilter={this.filerPrivateCar}
            />
            <div className="fg-line" />
          </>
        )}
        <div className="multi-list">
          {listData.length > 0
            ? isOrder
              ? this.renderOrder()
              : listData.map(item => {
                if (isPrivateCar) {
                  return this.renderPrivateCarItem(item)
                } else {
                  return this.renderItem(item)
                }
              })
            : this.renderEmpty(isPrivateCar)}
        </div>
        {listData.length > 0 &&
          (!isPrivateCar ? (
            // isOrder ? (
            this.renderTripOrderBottom()
          ) : (
            // ) : (
            //   <ActionsPanel
            //     leftButtons={[]}
            //     rightButtons={[cancel(), selectFeetype()]}
            //     buttonAction={this.buttonClick}
            //   />
            // )
            <div
              className="bottom_bar privatecar-bottom-bar"
              onClick={() => {
                this.buttonClick({ name: 'select.feetype' })
              }}
            >
              <div>{i18n.get(`导入 {__k0} 条消费`, { __k0: Object.keys(selectedList).length })}</div>
            </div>
          ))}
      </div>
    )
  }
}
