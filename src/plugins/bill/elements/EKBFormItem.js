/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019-01-18 11:16.
 */

import React, { PureComponent } from 'react'
import './EKBFormItem.less'

export default class EKBFormItem extends PureComponent {
  constructor(props) {
    super(props)
  }
  render() {
    const { children, value, style, required = false } = this.props
    const { label, content, error, errorText } = value

    return (
      <div style={{ borderBottom: style?.borderBottom !== 'none' ? '1px solid rgba(0, 0, 0, 0.09)' : 'none' }}>
        <div className="ekb-form-item" style={style}>
          <span className="item-label">
            {`${label}`}
            {required && <span style={{ color: 'red', marginLeft: '2px' }}>*</span>}
          </span>
          {children ? (
            children
          ) : (
            <span className="item-content" style={value.label === '备注' ? { 'font-weight': '400' } : {}}>
              {content ? content : i18n.get(`暂无{__k0}`, { __k0: label })}
            </span>
          )}
        </div>
        {error &&
          <div style={{ color: 'red', margin: '-2px 0 16px' }}>
            {i18n.get(errorText)}
          </div>
        }
      </div>
    )
  }
}
