/**
 *  Created by <PERSON><PERSON><PERSON> on 2020/12/27
 */

import styles from './FeetypeRiskModal.module.less'
import React, { PureComponent } from 'react'

export default class SingleFeetypeRiskModal extends PureComponent {
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  render() {
    let { riskList, riskTitle } = this.props
    if (!riskTitle) {
      riskTitle = i18n.get('风险提示')
    }
    return (
      <div className={styles['risk-modify-wrapper']}>
        <div className="risk-header">{riskTitle}</div>
        <div className="risk-content">
          <div className="risk-items">
            <div className="risk-item-content">
              {riskList.map((vv, kIndex) => (
                <div key={kIndex}>
                  {riskList.length > 1 ? i18n.get(`{__k0}、{__k1}`, { __k0: kIndex + 1, __k1: vv }) : vv}
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="risk-footer" onClick={this.handleCancel}>
          {i18n.get('知道了')}
        </div>
      </div>
    )
  }
}
