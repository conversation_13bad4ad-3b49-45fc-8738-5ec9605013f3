import React, { useState, useEffect } from 'react'
import { app } from '@ekuaibao/whispered'
import { Button } from 'antd-mobile'
import { cloneDeep } from 'lodash'

import styles from './EKBGroupDropDown.module.less'
//@ts-ignore
const EkbIcon = app.require('@elements/ekbIcon')

interface menuProps {
  type: string
  label: string
  children?: menuProps[]
}

interface Props {
  menuList: menuProps[]
  isOpen: boolean
  fnCancel: () => {}
  activeTypes: any[]
  defaultActiveTypes: menuProps[]
  fnChangeType: (param: menuProps[]) => {}
  downContentClassName?: string
  style: any
}

const EKBGroupDropDown:React.FC<Props> = (props: Props) => {
  const { isOpen, fnCancel, menuList, defaultActiveTypes, activeTypes, fnChangeType, downContentClassName = '', style } = props
  const [selectTypes, setSelectTypes] = useState(activeTypes || defaultActiveTypes)
  const [isShowMask, setIsShowMask] = useState(false)
  const [isShowContent, setIsShowContent] = useState(false)
  const userInfoId = app.getState()['@common'].me_info?.staff?.id + '-filter' || 'bill-select-filter'

  useEffect(() => {
    if (isOpen) {
      setIsShowMask(isOpen)
      setTimeout(() => setIsShowContent(isOpen), 300)
    } else {
      setIsShowContent(isOpen)
      setTimeout(() => setIsShowMask(isOpen), 300)
    }

  }, [isOpen])

  const handleReset = () => {
    localStorage.removeItem(userInfoId)
    setSelectTypes(defaultActiveTypes)
  }

  const handleOK = () => {
    fnChangeType(selectTypes)
  }
  const handleClickMenu = (menuType: string, subMenuType: string) => {
    const newSelectTypes = cloneDeep(selectTypes)
    const selectMenu = newSelectTypes.find(el => el.type === menuType)
    const subMenu = menuList.find(el => el.type === menuType)
    const selectSubMenu = subMenu.children.find(el => el.type === subMenuType)
    selectMenu.children = [selectSubMenu]
    setSelectTypes(newSelectTypes)
  }

  const maskClassName = isShowContent ? 'ekb-drop-down-mask show' : 'ekb-drop-down-mask'
  const contentClassName = isShowContent ? 'ekb-drop-down show' : 'ekb-drop-down'
  if (!isShowMask && !isShowContent) {
    return null
  }
  return (
    <div className={styles['ekb-drop-down-wrap']} style={style}>
      <div className={maskClassName} onClick={() => fnCancel && fnCancel()} />
      <div className={contentClassName}>
        <div className={`ekb-drop-down-content inertial-rolling ${downContentClassName}`}>
          {menuList.map((menu) => {
            if (!menu?.children?.length) {
              return null
            }
            const selectItems = selectTypes.find(el => el.type === menu.type)?.children
            const subMenu = menu.children.map((el) => {
              const isSelected = !!selectItems?.filter(item => item.type === el.type).length
              const itemClassName = isSelected ? 'ekb-drop-down-item selected' : 'ekb-drop-down-item'
              return (
                <div key={el.type} className={itemClassName} onClick={() => handleClickMenu(menu.type, el.type)}>
                  <span>{el.label}</span>
                  {isSelected && (
                    <div className="item-selected-tip">
                      <EkbIcon name="#EDico-check-default"/>
                    </div>
                  )}
                </div>
              )
            })
            return (
              <div key={menu.type} className='ekb-drop-down-group'>
                <div className='ekb-drop-down-groupName'>
                  <span>{menu.label}</span>
                </div>
                {subMenu}
              </div>
            )
          })}
        </div>
        <div className='ekb-drop-down-footer'>
          <Button className="btn" onClick={handleReset}>{i18n.get('重置')}</Button>
          <Button className="primary-btn" onClick={handleOK}>{i18n.get('确定')}</Button>
        </div>
      </div>
    </div>
  )
}

export default EKBGroupDropDown
