/**
 * @description 申请单送审完成时 散客转化计划
 * <AUTHOR>
 */
import { app, app as api } from '@ekuaibao/whispered'
import React, { useEffect, useState } from 'react'
import styles from './bill-requisition-success.module.less'
import IMG from './../images/sus.svg'
import { T } from '@ekuaibao/i18n'
import moment from 'moment'
import { EnhanceConnect } from '@ekuaibao/store'
import RequisitionBanner from '../../requisition/elements/requisition-banner'
type IProps = {
  trips_info: any[]
  params: any
}
export function BillRequisitionSus(props: IProps) {
  const { trips_info, params } = props
  const flightTrip = trips_info.find(
    el =>
      el?.tripTypeId?.endsWith(':flight') &&
      moment(el?.tripForm?.tripDate)
        .add(1, 'd')
        .isAfter(Date.now(), 'day')
  )
  const handlePageGo = () => {
    window?.endpoint?.invoke('Mall:close', { flowId: params?.id }) // send command "Mall:close" to mall page
    app.go('/home5')
  }
  const trainTrip = trips_info.find(
    el =>
      el?.tripTypeId?.endsWith(':train') &&
      moment(el?.tripForm?.tripDate)
        .add(1, 'd')
        .isAfter(Date.now(), 'day')
  )
  const hotelTrip = trips_info.find(
    el =>
      el?.tripTypeId?.endsWith(':hotel') &&
      moment(el?.tripForm?.tripDatePeriod?.end)
        .add(1, 'd')
        .isAfter(Date.now(), 'day')
  )
  const mallRole = api.getState()['@common'].mallRole
  const isShowBanner = mallRole?.mallRole === '0' && (flightTrip || trainTrip || hotelTrip)
  return (
    <div className={styles['bill-requisition-success']}>
      <div className="bill-sus-info">
        <div className="imgWrap">
          <img src={IMG} alt="" />
        </div>
        <div className="sus">
          <T name="提交成功" />
        </div>
        <div className="txt">
          <T name="单据已提交成功" />
        </div>
        <div className="txt">
          <T name="个人中心可实时关注审核进度" />
        </div>
        <div className="btn_return" onClick={() => handlePageGo()}>
          <span>
            <T name="返回首页" />
          </span>
        </div>
      </div>
      {isShowBanner && (
        <div className="banner">
          <p>
            <T name="与你行程相关的机酒推荐：" />
          </p>
          <RequisitionBanner trips={{ flightTrip, trainTrip, hotelTrip }} isTicketParam={true}/>
        </div>
      )}
    </div>
  )
}

export default EnhanceConnect(state => ({
  trips_info: state['@bill'].trips_info
}))(BillRequisitionSus)
