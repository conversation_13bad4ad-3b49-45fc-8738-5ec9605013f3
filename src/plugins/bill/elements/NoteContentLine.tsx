/**************************************
 * Created By LinK On 2020/4/30 14:21.
 **************************************/
import React from 'react'
import styles from './NoteContentLine.module.less'

interface Props {
  name: string
  text: string
  score?: number
}

export default function NoteContentLine(props: Props) {
  const { name, text, score } = props
  const scoreValue = score >= 0 ? `+${score}` : score
  const fontColor = score >= 0 ? { color: '#00B42A' } : {}
  return (
    <div className={styles['note-content-text-wrap']}>
      <span className="note-content-text-authorName">{name}</span>
      <span className="note-content-text-content">
        {text}
      </span>
      {scoreValue && <span className="note-content-text-score" style={fontColor}>{scoreValue}</span>}
    </div>
  )
}
