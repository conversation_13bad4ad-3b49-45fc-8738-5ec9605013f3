import React, { Component } from 'react'
import { Button } from 'antd-mobile'
import { EnhanceConnect } from '@ekuaibao/store'
import { get, differenceBy } from 'lodash'
import { app } from '@ekuaibao/whispered'
import { getEntityFormById } from '../../../components/dynamic/inner/action'
import styles from './TripOrderAction.module.less'
import { Fetch } from '@ekuaibao/fetch'
import { processMallResourceAuth } from '../../../lib/mallSourceAuth'

interface Props {
  data: any
  travelPower: any[]
  dataLinkEntity: any[]
}
interface State {
  tripTypes: any[]
}

@EnhanceConnect((state: any) => ({
  travelPower: state['@common'].powers?.CARGO_TRAVEL_MANAGEMENT,
  dataLinkEntity: state['@bill'].dataLinkEntity || []
}))
export default class TripOrderAction extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      tripTypes: []
    }
  }
  componentWillMount() {
    const { dataLinkEntity } = this.props
    if (dataLinkEntity.length) {
      return
    }
    const { data } = this.props
    const tripDataLink = get(data, `form.details[0].feeTypeForm['u_行程规划']`) // @i18n-ignore
    if (tripDataLink) {
      const components = get(data, `form.details[0].specificationId.components`) || []
      const field = components.find((i: any) => i.field === 'u_行程规划') // @i18n-ignore
      if (field) {
        app.dispatch(getEntityFormById(field.referenceData.id, field.behaviour))
      }
    }
  }

  componentWillReceiveProps(nextPros: Props) {
    const {
      dataLinkEntity,
      travelPower,
      data: { formType, state, id }
    } = nextPros
    const { tripTypes } = this.state
    if (
      !tripTypes.length &&
      travelPower &&
      formType === 'requisition' &&
      (state === 'paid' || state === 'archived') &&
      dataLinkEntity?.length
    ) {
      const tripTypes = this.getTripTypes(dataLinkEntity)
      this.registerTripType(tripTypes, id)
      this.setState({ tripTypes }, () => {
        this.bookingBtnViewTrack()
      })
    }
  }

  getTripTypes = (dataLinkEntity: any[]) => {
    let trips: any = []
    const {
      data: { form }
    } = this.props
    trips = trips.concat(form['u_行程规划'] || []) // @i18n-ignore
    form?.details?.forEach((i: any) => {
      trips = trips.concat(i.feeTypeForm['u_行程规划'] || []) // @i18n-ignore
    })
    //单据模板上配置了限制订购条件，如果符合限制订购条件，dataLinkId没有值，不允许订购
    trips = trips.filter((v: any) => !!v?.dataLinkId)
    const templateIds = differenceBy(trips, 'dataLinkTemplateId').map((i: any) => i.dataLinkTemplateId)
    const TAXIType = ['DING_TALK', 'WEIXIN'].includes(window.__PLANTFORM__) ? [] : ['TAXI']
    const whiteTripTypes = ['FLIGHT', 'HOTEL', 'TRAIN', ...TAXIType] // ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI']
    const tripTypes = dataLinkEntity.filter(
      i => templateIds.includes(i.templateId) && whiteTripTypes.includes(i.entity.type)
    )
    return tripTypes
  }

  registerTripType = (tripTypes: any[], id: StringAnyProps) => {
    if (!tripTypes?.length) {
      return
    }
    const sources = tripTypes.map(i => `BUTTON_${i.entity.type}_ORDER`)
    Promise.all([
      app.invokeService('@mall:get:all:travel:intent', { sources }),
      app.invokeService('@mall:get:mall:auth:query', { type: sources[0] })
    ]).then(async result => {
      const items = result[0]?.items || []
      const authQuery = result[1] || ''
      const config = await app.invokeService('@bill:get:getFlowInfo:tripPlatform', id)
      const checked = get(config, 'value.ability.checked')
      const platform = (get(config, 'value.ability.platform') || []).join(',')
      items.forEach((i: any) => {
        const type = /\?/.test(i.source) ? '&' : '?'
        if (!i.source.includes('token')) {
          i.source = i.source + `${type}${authQuery}`
        }
        if (window.isPC && i.realm === 'MALL') {
          i.sourceType = 'IFRAME'
        }
        if (i.realm === 'MALL') {
          i.sourceType = 'OPEN_LINK'
        }
        if (i.realm === 'TC_TRIP' && !!id) {
          //同程商旅定制化
          i.source = i.source + `${type}flowId=${id}`
        }
        const power = i.powers && i.powers.length ? i.powers[0] : undefined
        i.disabled = checked && platform.length > 1 ? !platform.includes(power) : false
      })
      sources.forEach(type => {
        app.thirdResources.deleteByType(type)
      })
      app.thirdResources.add(items)
    })
  }

  handleOrder = (type: string) => {
    this.trackStatementHelper(type, 'aplForm_booking_click', '申请单详情差旅订购按钮点击')
    app.request({
      type: `BUTTON_${type}_ORDER` || 'BUTTON_TRAVEL_ORDER',
      prepareResource: processMallResourceAuth
    })
  }

  // 按钮展示
  bookingBtnViewTrack = () => {
    const { tripTypes } = this.state
    tripTypes?.forEach(e => {
      this.trackStatementHelper(e?.entity?.type, 'aplForm_booking_view', '申请单详情差旅订购按钮显示')
    })
  }

  trackStatementHelper = (type: string, key: string, actionName: string): void => {
    app.invokeService('@common:set:track', {
      key,
      actionName,
      type,
      source: window.__PLANTFORM__ || null
    })
  }

  render() {
    const { tripTypes } = this.state
    if (!tripTypes.length) {
      return null
    }
    return (
      <div className={styles['trip-order-action']}>
        <div className="title">
          {i18n.get('差旅订购')}
          {i18n.get('：')}
        </div>
        <div className="actions">
          {tripTypes.map((i: any) => (
            <Button type="primary" key={i.entity.id} onClick={() => this.handleOrder(i.entity.type)}>
              {i18n.get(i.entity.name)}
            </Button>
          ))}
        </div>
      </div>
    )
  }
}
