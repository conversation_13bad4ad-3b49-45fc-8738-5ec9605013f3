import React, { PureComponent } from 'react'
import { TextArea } from '@hose/eui-mobile'
import { getStaffDisplayName, getMentions, insertFlg, getCursorPosition } from '../utils/commentUtil'
import { app as api } from '@ekuaibao/whispered'
import { toast, formatRegStr } from '../../../lib/util'
import { cloneDeep } from 'lodash'

export default class Comment extends PureComponent {
  constructor(props) {
    super(props)
    this.commentTextarea = 'commentTextarea'
    this.state = { value: '', staffs: [] }
    let { bus } = props
    bus.watch('get:mention:content', this.handleMentionResult)
    bus.on('mention:remaind', this.handleMentionRemind)
  }

  async componentDidMount() {
    let { staffs } = this.props
    if (!staffs?.length) {
      api
        .dataLoader('@common.staffs')
        .load()
        .then(data => {
          this.fnGenStaffMap(data)
        })
    } else {
      this.fnGenStaffMap(staffs)
    }
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('bill:get:mention:content', this.handleMentionResult)
    bus.un('mention:remaind', this.handleMentionRemind)
  }

  fnGenStaffMap(staffs) {
    this.staffMap = {}
    this.displayStaffMap = []
    let data = cloneDeep(staffs) || []
    data.forEach(line => {
      this.staffMap[line.id] = line
      this.displayStaffMap[getStaffDisplayName(line)] = line
    })
  }

  fnSelectStaff = position => {
    let input = document.getElementById(this.commentTextarea)
    const { isVisibilityStaffs = false } = this.props
    input.blur()
    return api.invokeService('@layout:select:staff', { isVisibilityStaffs }).then(staff => {
      let { value } = this.state
      let displayName = getStaffDisplayName(this.staffMap[staff.id])
      value = insertFlg(value, displayName + ' ', position)
      this.setState({ value })
    })
  }

  fnCheckComment = (comment = '') => {
    let value = comment.trim()
    if (value.length === 0) {
      toast.error(i18n.get('请输入评论内容'))
      return false
    }

    if (value.length > 1400) {
      toast.error(i18n.get('评论内容超过1400个字'))
      return false
    }
    return true
  }

  handleMentionResult = () => {
    return new Promise((resolve, reject) => {
      let { value } = this.state
      let validatorContent = cloneDeep(value)
      let mentionContent = cloneDeep(value)
      let mentions = getMentions(value)
      let participants = []
      mentions.forEach(str => {
        let findName = str.replace('@', '')
        let staff = this.displayStaffMap[findName]
        if (staff) {
          let replaceStr = `{${staff.id}}`
          let replaceName = formatRegStr(str)
          let reg = new RegExp(`${replaceName}`, 'g')
          mentionContent = mentionContent.replace(reg, replaceStr)
          validatorContent = validatorContent.replace(reg, '')
          participants.push(staff.id)
        }
      })

      if (participants.length === 0 && !this.fnCheckComment(validatorContent)) {
        return reject()
      }

      resolve({ comment: mentionContent, participants })
    })
  }

  handleMentionRemind = () => {
    let { value } = this.state
    let position = getCursorPosition(this.commentTextarea)
    let flg = value.length ? ' @' : '@'
    value = insertFlg(value, flg, position)
    this.setState({ value }, () => {
      this.fnSelectStaff(position + flg.length)
    })
  }

  handleTextChange = val => {
    this.setState({ value: val })
  }

  onKeyUp = event => {
    let { value } = this.state
    const isDeleteKey = event.keyCode === 8 || event.keyCode === 46 //8:backspace 46:delete
    if (value && value.length) {
      let position = getCursorPosition(this.commentTextarea)
      let checkCharacter1 = value.substring(position - 1, position)
      if (checkCharacter1 === '@' && !isDeleteKey) {
        //最后插入@
        this.fnSelectStaff(position)
      }
    }
  }

  render() {
    const { value } = this.state
    return (
      <TextArea
        id={this.commentTextarea}
        value={value}
        rows={10}
        maxLength={1400}
        showCount
        placeholder={i18n.get('请输入评论内容')}
        onChange={this.handleTextChange}
        onKeyUp={this.onKeyUp}
      />
    )
  }
}
