@import '~@eku<PERSON>bao/eui-styles/less/token-mobile.less';

.ekb-drop-down-wrap {
  z-index: 2;
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  :global {
    .ekb-drop-down-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: -1;
      opacity: 0;
      transition: opacity ease .2s;
      background: rgba(20, 34, 52, 0.24);
      box-shadow: 0px 4px 4px rgb(0 0 0 / 25%);
      // .mask-black;
      &.show {
        opacity: 1;
      }
    }
    .ekb-drop-down {
      position: absolute;
      top: -100%;
      left: 0;
      width: 100%;
      min-height: 240px;
      // max-height: 1032px;
      background: white;
      transition: all ease .2s;
      .ekb-drop-down-content {
        overflow: scroll;
        max-height: 750px;
        padding-left: @space-6;
        padding-right: @space-6;
        .ekb-drop-down-groupName {
          .font-size-3;
          .font-weight-3;
          margin-bottom: 16px;
        }
        .ekb-drop-down-item {
          position: relative;
          display: inline-flex;
          align-items: center;
          justify-content: space-between;
          width: 48.5%;
          height: 80px;
          border-radius: 8px;
          margin-bottom: @space-6;
          color: @color-black-1;
          padding: 0 @space-5 0 @space-6;
          background: rgba(78, 89, 105, 0.06);
          .font-weight-2;
          .font-size-3;
          span {
            flex: 1;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            user-select: none;
          }
          img {
            flex-shrink: 0;
            width: 32px;
          }
          &.selected {
            background: #ffffff;
            border: var(--eui-primary-pri-500) 2px solid;
          }
          .item-selected-tip {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 32px;
            height: 32px;
            border-bottom-right-radius: @radius-2;
            border-width: 16px;
            border-style: solid;
            border-color: #ffffff var(--eui-primary-pri-500) var(--eui-primary-pri-500) #ffffff;
            font-size: @icon-size-3;
            .icon {
              position: absolute;
              color: #ffffff;
            }
          }
          &:nth-child(2n) {
            margin-right: 3%;
          }
        }
      }
      &.show {
        top: 0;
      }
    }
    .ekb-drop-down-footer {
      display: flex;
      justify-content: space-between;
      margin: 32px;
      .btn {
        width: 160px;
        background-color: rgba(78, 89, 105, 0.06);
        margin-right: 16px;
      }
      .primary-btn {
        flex: 1;
        color: #ffffff;
        background-color: var(--eui-primary-pri-500);
      }
    }
  }
}