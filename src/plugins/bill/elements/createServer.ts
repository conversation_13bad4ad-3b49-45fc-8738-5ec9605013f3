import { Server } from '@ekuaibao/rpc'

class Services {
  app: any
  constructor(app: any) {
    this.app = app
  }
  'Mall:getData'() {
    let d = this.app.props.trip
    return new Promise(resolve => {
      resolve(d)
    })
  }
  'Mall:setData' = (data: any) => {
    console.log(data)
    const { money = 0 } = data
    if (money == 0) {
      // 关闭当前弹窗
      this.app?.props?.layer?.emitCancel()
    } else {
      this.app?.props?.layer?.emitOk(data)
    }
  }
}

export default function createServer(props: any) {
  return new Server(new Services(props))
}
