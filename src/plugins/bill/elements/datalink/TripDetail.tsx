import React, { Component } from 'react'
import styles from './TripDetail.module.less'
import { app } from '@ekuaibao/whispered'
import { get, forIn, uniq, size, isBoolean } from 'lodash'
import { stateMap } from '@ekuaibao/lib/lib/enums'
import EnhanceTitleHook from '../../../../lib/EnhanceTitleHook'
import OrderListPopover from './OrderListPopover'
import { TemplateKind } from './datalink-detail-modal/types'
import moment from 'moment'
import {
  TwoToneGeneralAirplane,
  TwoToneGeneralTrain,
  TwoToneGeneralTaxi,
  TwoToneGeneralHotel,
  TwoToneGeneralFood
} from '@hose/eui-icons'
import { Button } from '@hose/eui-mobile'
import { Fetch, Resource } from '@ekuaibao/fetch'
import { getBoolVariation } from '../../../../lib/featbit';
const flightIcon = require('../../images/flight_new.svg')
const hotelIcon = require('../../images/hotel_new.svg')
const trainIcon = require('../../images/train_new.svg')
const carIcon = require('../../images/car_new.svg')
const commonIcon = require('../../images/common_new.svg')
const foodIcon = require('../../images/food_new.svg')

const Money = app.require<any>('@elements/puppet/Money')
const { getCityAndDate } = app.require<any>('@bill/elements/datalink/dataLinkUtil')
const SVG_WARING = app.require<any>('@images/invoice-payerno-warning.svg')

const TripMapLists: { [key: string]: any } = {
  FLIGHT: '机票',
  HOTEL: '酒店',
  TRAIN: '火车',
  TAXI: '用车',
  FOOD: '餐饮',
  CUSTOM: '自定义',
  COMMON: '通用'
}

const ICON_MAP: any = {
  FLIGHT: flightIcon,
  HOTEL: hotelIcon,
  TRAIN: trainIcon,
  TAXI: carIcon,
  CUSTOM: commonIcon,
  FOOD: foodIcon
}
const { getDays } = app.require('@bill/elements/datalink/dataLinkUtil')
const cityFetch = new Resource('/api/v2/basedata/city/')

const formTypeMap: StringStringProps = {
  expense: i18n.get('报销'),
  loan: i18n.get('借款'),
  requisition: i18n.get('申请'),
  custom: i18n.get('基础'),
  permit: i18n.get('授权'),
  payment: i18n.get('付款')
}

interface States {
  tripInfo: any
  tripDataLink: any
  tripOrders: any[]
  tripType: string
  specificationId: any
  deptList: any[]
  showButton: boolean
  customTripTypeList: any[]
  customTrip?: any
}
interface Props {
  params: any
}

// @ts-ignore
@EnhanceTitleHook(i18n.get('行程详情'))
export default class TripDetail extends Component<Props, States> {
  private TK = ''
  private type = ''
  constructor(props: Props) {
    super(props)
    this.state = {
      tripDataLink: {},
      tripOrders: [],
      tripInfo: {},
      tripType: '',
      showButton: false,
      specificationId: {},
      deptList: [],
      customTripTypeList: []
    }
  }

  async componentWillMount() {
    const {
      params: { id }
    } = this.props
    const { value } = await app.invokeService('@bill:get:datalink:template:byId', {
      entityId: id,
      type: TemplateKind.DETAIL
    })
    const tripType = get(value, 'data.dataLink.entity.type')
    this.TK = `BUTTON_${tripType}_ORDER`
    this.type = tripType
    const customTripType = await app.invokeService('@bill:get:travel:management')
    const customTripTypeList = customTripType?.items || []
    let tripDataLink = {}
    if(getBoolVariation('fkrd-5582-travel-planning-show-foreign-country')){
      tripDataLink = await this.formatDataLinkNew(get(value, 'data.dataLink'))
    }else{
      tripDataLink = this.formatDataLink(get(value, 'data.dataLink'))
    }
    const dataLinkId = get(value, 'data.dataLink.entity.id', '')
    const customTrip = customTripTypeList.find((item: any) => item.entityId === dataLinkId)
    const { fromCity, toCity, startDate, endDate, name } = getCityAndDate({ dataLinkForm: tripDataLink }, customTrip)
    const tripInfo = { startTime: startDate, endTime: endDate, startCity: fromCity, endCity: toCity, tripType, name }
    const specificationId = await this.getSpecification(tripDataLink)
    const traveler = tripDataLink['出行人']
    let tripOrders = tripDataLink['订单'] || []
    if(getBoolVariation('fkrd-5582-travel-planning-show-foreign-country')){
      tripOrders = await this.getOrdersNew(tripOrders)
    }else{
      tripOrders = this.getOrders(tripOrders)
    }
    const deptIds = traveler && traveler.map((item: any) => item.defaultDepartment)
    const res = await app.invokeService('@common:get:departments:fullName:by:ids', uniq(deptIds))
    this.registerTripType(tripDataLink, tripType)

    this.setState({
      customTrip,
      tripType,
      tripInfo,
      tripOrders,
      tripDataLink,
      specificationId,
      deptList: res.items
    })
  }

  getOrders = (data: any) => {
    const orders: any = {}
    data.forEach((i: any) => {
      let dataLink = get(i, 'data.dataLink')
      dataLink = this.formatDataLink(dataLink)
      const traveler = dataLink['出行人']
      traveler?.forEach((traveler: any) => {
        if (!orders[traveler.id]) {
        }
        orders[traveler.id].push(i)
      })
    })
    return orders
  }

  getOrdersNew = async (data: any) => {
    const orders: any = {}
    for (const i of data) {
      let dataLink = get(i, 'data.dataLink')
      dataLink = await this.formatDataLinkNew(dataLink)
      const traveler = dataLink['出行人']
      traveler?.forEach((traveler: any) => {
        if (!orders[traveler.id]) {
          orders[traveler.id] = []
        }
        orders[traveler.id].push(i)
      })
    }
    return orders
  }

  registerTripType = (tripDataLink: any, tripType: string) => {
    // 机票下单：BUTTON_FLIGHT_ORDER 火车下单：BUTTON_TRAIN_ORDER  酒店下单：BUTTON_HOTEL_ORDER
    const applyId = tripDataLink['原始单据']?.id || ''
    const tripId = tripDataLink.id
    const white = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI', 'FOOD']
    if (!white.includes(tripType)) {
      return
    }
    Promise.all([
      app.invokeService('@mall:get:travel:intent', { type: this.TK }),
      app.invokeService('@mall:get:travel:intent:jwt', { type: this.TK })
    ]).then(async result => {
      const items = result[0] && result[0].items ? result[0].items : []
      const token = result[1] ? result[1].id : ''
      const config = await app.invokeService('@bill:get:getFlowInfo:tripPlatform', applyId)
      const checked = get(config, 'value.ability.checked')
      const platform = (get(config, 'value.ability.platform') || []).join(',')
      items.forEach((i: any) => {
        const type = /\?/.test(i.source) ? '&' : '?'
        if (!i.source.includes('token')) {
          i.source = i.source + `${type}token=${token}&applyId=${applyId}&tripId=${tripId}&ekbAccessToken=${Fetch.accessToken || ''}`
        }
        const power = i.powers && i.powers.length ? i.powers[0] : undefined
        i.disabled = checked && platform.length > 1 ? !platform.includes(power) : false
      })

      this.setState({ showButton: items.length > 0 })
      app.thirdResources.deleteByType(this.TK)
      app.thirdResources.add(items)
    })
  }
  getSpecification = async (tripDataLink: any) => {
    const specificationId = get(tripDataLink['原始单据'], 'form.specificationId')
    if (specificationId) {
      const res = await app.invokeService('@feetype:getSpecificationById', specificationId)
      return res.value
    }
    return null
  }

  processCityData = async (cityDataString: string) => {
    if (!cityDataString) return cityDataString
    
    try {
      const cityArray = JSON.parse(cityDataString)
      if (!Array.isArray(cityArray) || cityArray.length === 0) return cityDataString
      
      const cityKeys = cityArray.map((city: any) => city.key).filter(Boolean)
      
      if (cityKeys.length > 0) {
        try {
          const res = await cityFetch.GET('[ids]', { ids: cityKeys.join(',') })
          
          if (res && res.items && Array.isArray(res.items)) {
            const enhancedCityArray = cityArray.map((city: any) => {
              const cityDetail = res.items.find((item: any) => item.id === city.key)
              if (cityDetail && cityDetail.fullName && cityDetail.countryCode !== 'CN') {
                const countryName = cityDetail.fullName.split(',')[0]
                if (!city.label.includes(`(${countryName})`)) {
                  return { ...city, label: `${city.label}(${countryName})` }
                }
              }
              return city
            })
            
            return JSON.stringify(enhancedCityArray)
          }
        } catch (error) {
          console.error('请求城市详情失败:', error)
        }
      }
      
      return cityDataString
    } catch (error) {
      console.error('解析城市数据失败:', error)
      return cityDataString
    }
  }

  enhanceNameWithCountryInfo = (name: string, accommodationData: string, departureData: string, destinationData: string) => {
    if (!name) return name
    
    try {
      const accommodationCities = accommodationData ? JSON.parse(accommodationData) : []
      const departureCities = departureData ? JSON.parse(departureData) : []
      const destinationCities = destinationData ? JSON.parse(destinationData) : []
      
      const allCities = [...accommodationCities, ...departureCities, ...destinationCities]
      const cityMap = new Map()
      
      allCities.forEach(city => {
        if (city.key && city.label) {
          cityMap.set(city.label.split('(')[0], city.label)
        }
      })
      
      let enhancedName = name
      
      if (name.includes(' - ')) {
        const cityParts = name.split(' - ')
        const enhancedParts = cityParts.map(part => {
          const trimmedPart = part.trim()
          const enhancedCity = cityMap.get(trimmedPart)
          return enhancedCity || trimmedPart
        })
        enhancedName = enhancedParts.join(' - ')
      } else {
        const trimmedName = name.trim()
        const enhancedCity = cityMap.get(trimmedName)
        if (enhancedCity) {
          enhancedName = enhancedCity
        }
      }
      
      return enhancedName
    } catch (error) {
      console.error('增强name属性失败:', error)
      return name
    }
  }

  formatDataLink = (dataLink: any) => {
    const data: any = {}
    forIn(dataLink, (value, key) => {
      const lastIndex = key.lastIndexOf('_')
      data[lastIndex > -1 ? key.substring(lastIndex + 1) : key] = value
    })
    return data
  }

  formatDataLinkNew = async (dataLink: any) => {
    const data: any = {}
    forIn(dataLink, (value, key) => {
      const lastIndex = key.lastIndexOf('_')
      data[lastIndex > -1 ? key.substring(lastIndex + 1) : key] = value
    })

    if (data['住宿地']) {
      data['住宿地'] = await this.processCityData(data['住宿地'])
    }
    if (data['出发地']) {
      data['出发地'] = await this.processCityData(data['出发地'])
    }
    if (data['目的地']) {
      data['目的地'] = await this.processCityData(data['目的地'])
    }

    if (data['name']) {
      data['name'] = this.enhanceNameWithCountryInfo(
        data['name'],
        data['住宿地'],
        data['出发地'],
        data['目的地']
      )
    }
    return data
  }

  getButtonText = (typeName: string) => {
    const { showButton } = this.state
    if (!showButton) {
      return ''
    }
    switch (typeName) {
      case 'FLIGHT':
        return i18n.get('订机票')
      case 'HOTEL':
        return i18n.get('订酒店')
      case 'TRAIN':
        return i18n.get('订火车票')
      case 'TAXI':
        return ['DING_TALK', 'WEIXIN'].includes(window.__PLANTFORM__) ? '' : i18n.get('用车')
      default:
        return ''
    }
  }

  getDeptName = (traveler: any) => {
    const { deptList } = this.state
    const { defaultDepartment } = traveler

    const dept = deptList.find((item: any) => item.id === defaultDepartment)
    return dept ? dept.name : i18n.get('无')
  }

  handleOnClick = async () => {
    app.invokeService('@common:set:track', {
      key: 'mytrips_mall_click',
      actionName: '订购按钮点击量',
      from: 'other',
      type: this.type
    })
    const services = {
      token: () => {
        return new Promise((resolve, reject) => {
          setTimeout(() => resolve({ RD: { c: 3 } }), 1000)
        })
      }
    }
    const result = await app.request({
      type: this.TK || 'BUTTON_TRAVEL_ORDER',
      services: services
    })
  }
  handleOrderDetail = (value: any) => {
    app.open('@bill:DataLinkEntityTripOrderDetailModal', {
      field: value.entity.fields,
      value: { data: { dataLink: value } },
      title: i18n.get('订单详情')
    })
    app.invokeService('@common:set:track', {
      key: 'mytrips_order_view',
      actionName: '订单详情页pv',
      from: 'detail'
    })
  }
  handleBillDetail = (bill: any) => {
    app.go('/requisitionToDetail/formRequisition/' + bill.id)
  }
  handleRiskDetail = (riskContent: any) => {
    app.open('@bill:TripRiskModal', { riskContent })
  }

  getActionText = (typeName: string) => {
    switch (typeName) {
      case 'HOTEL':
        return i18n.get('订店')
      case 'FOOD':
        return i18n.get('订餐')
      case 'TAXI':
        return i18n.get('用车')
      default:
        return i18n.get('订票')
    }
  }

  renderTravelers = () => {
    const { tripDataLink, tripOrders, tripType } = this.state
    const traveler = tripDataLink['出行人'] || []
    const buttonText = this.getButtonText(tripType)
    const actionText = this.getActionText(tripType)
    return (
      <>
        <section className="trip-order">
          <div className="trip-order-title"> {i18n.get('出行人')}</div>
          {traveler.map((item: any, i: number) => {
            const ownOrders = tripOrders[item.id]
            let orderStatusCls = ''
            let currentOrder = null
            if (ownOrders) {
              currentOrder = this.formatDataLink(ownOrders[0].data.dataLink)
              orderStatusCls =
                currentOrder['订单状态'] === '已出票' || currentOrder['订单状态'] === '出票'
                  ? 'trip-order-item-icon lightgreen'
                  : 'trip-order-item-icon lightgray'
            }

            return (
              <div className="trip-order-item" key={i}>
                <div className="trip-order-item-name">{item.name}</div>
                <div className="trip-order-item-dept">{this.getDeptName(item)}</div>
                {currentOrder && <span className={orderStatusCls}>{currentOrder['订单状态']}</span>}
                {!currentOrder && buttonText && (
                  <span className="trip-order-item-icon blue">{i18n.get(`待{__k0}`, { __k0: actionText })}</span>
                )}
                {currentOrder && (
                  <div className="order-information">
                    <div className="order-information-num">
                      {i18n.get(`订单号 {__k0}`, {
                        __k0: currentOrder['订单号']
                      })}
                    </div>
                    {ownOrders.length === 1 ? (
                      <span
                        className="order-information-detail"
                        onClick={() => this.handleOrderDetail(ownOrders[0].data.dataLink)}
                      >
                        {i18n.get('订单详情')}
                      </span>
                    ) : (
                      <OrderListPopover orders={ownOrders} tripType={tripType} />
                    )}
                  </div>
                )}
              </div>
            )
          })}
        </section>
      </>
    )
  }

  renderDate() {
    const { tripInfo, customTrip } = this.state
    const { startTime, endTime } = tripInfo
    let dateString = ''
    const typeList = ['FOOD', 'TAXI', 'COMMON', 'HOTEL']
    if (customTrip?.dateTypeField?.dateType === 'multiple' || typeList.includes(tripInfo?.tripType)) {
      const days = getDays(startTime, endTime)
      dateString = i18n.get(`{__k0}~{__k1} 共{__k2}天`, {
        __k0: moment(startTime).format(i18n.get('MM月DD日')),
        __k1: moment(endTime).format(i18n.get('MM月DD日')),
        __k2: days + 1
      })
      if (tripInfo?.tripType === 'HOTEL') {
        dateString = dateString + `${days}${i18n.get('晚')}`
      }
    } else {
      dateString = `${moment(startTime).format(i18n.get('MM月DD日'))}`
    }
    return <span>{dateString}</span>
  }

  formatData = () => {
    const { tripInfo } = this.state
    let { startCity, endCity, name } = tripInfo
    startCity = (startCity && JSON.parse(startCity)[0].label) || ''
    endCity = (endCity && JSON.parse(endCity)[0].label) || ''
    return { startCity, endCity, name }
  }

  render() {
    const { tripDataLink, tripOrders, tripInfo, tripType, specificationId, customTrip } = this.state

    const budgetAmount = tripDataLink['预算金额']
    const applicationFormCode = tripDataLink['申请单编码']
    const buttonText = this.getButtonText(tripType)
    const traveler = tripDataLink['出行人'] || []
    const bill = tripDataLink['原始单据']
    const remark = tripDataLink['备注'] || ''
    const disabled = isBoolean(tripDataLink.active) ? !tripDataLink.active : false
    const { startCity, endCity, name } = this.formatData()
    const stateTxtMap: any = stateMap()

    const tripStatus: string = stateTxtMap[get(bill, 'state', '')]?.text

    let showTripTypeTxt: string = TripMapLists[tripType]
    if (tripType === 'CUSTOM') showTripTypeTxt = customTrip?.name || ''

    // 单据状态分类
    const finishStatus = ['PROCESS', 'archived', 'paid']
    const unusualStatus = ['failure', 'nullify', 'receivingExcep', 'rejected']
    const todoStatus = ['draft', 'paying', 'receiving', 'sending']
    const processStatus = ['approving', 'pending']
    const cur_state = get(bill, 'state', '')
    // 根据不同单据状态添加不同类名
    const tripStatusClassName = `status-icon ${finishStatus.includes(cur_state) ? 'lightgreen' : ''} ${
      unusualStatus.includes(cur_state) ? 'red' : ''
    } ${todoStatus.includes(cur_state) ? 'lightgray' : ''} ${processStatus.includes(cur_state) ? 'blue' : ''}`

    return (
      <div className={styles['trip-detail-wrapper']}>
        {remark.indexOf('[未同步]') > -1 && (
          <section className="trip-warning">
            <div className="tips">
              <img src={SVG_WARING} />
              {i18n.get('行程单未同步')}
              <a onClick={() => this.handleRiskDetail(remark)}>{i18n.get('查看')}</a>
              {i18n.get('，')}
              {i18n.get('请注意')}
            </div>
          </section>
        )}
        {disabled && <div className="status-disabled">{i18n.get('已停用')}</div>}
        <section className="trip-detail">
          <div className="trip-detail-head">
            {tripType === 'FLIGHT' && <TwoToneGeneralAirplane className="trip-icon" />}
            {tripType === 'HOTEL' && <TwoToneGeneralHotel className="trip-icon" />}
            {tripType === 'TRAIN' && <TwoToneGeneralTrain className="trip-icon" />}
            {tripType === 'TAXI' && <TwoToneGeneralTaxi className="trip-icon" />}
            {tripType === 'FOOD' && <TwoToneGeneralFood className="trip-icon" />}
            {(tripType === 'CUSTOM' || tripType === 'COMMON') && <img src={commonIcon} className="custom-icon" />}
            <span className="trip-type-txt">{showTripTypeTxt}</span>
          </div>
          <div className="trip-detail-content">
            <div className="trip-detail-content-city">{name}</div>
            <div className="trip-detail-content-date">{this.renderDate()}</div>
          </div>
        </section>
        <div className="other-info">
          {this.renderTravelers()}
          {tripType === 'FOOD' && budgetAmount && (
            <section className="trip-bill">
              <h5>{i18n.get('餐饮金额')}</h5>
              <div className="option-line">
                <div className="grow">
                  <div className="dis-f jc-e mb-4">
                    <div className="em">
                      <Money value={budgetAmount} />
                    </div>
                  </div>
                  <div className="dis-f jc-e">
                    <span>{i18n.get('总金额')}</span>
                  </div>
                </div>
              </div>
            </section>
          )}
          {bill && (
            <section className="trip-bill">
              <div className="trip-bill-title">{i18n.get('关联单据')}</div>
              <div className="trip-bill-item" onClick={() => this.handleBillDetail(bill)}>
                <span className={tripStatusClassName}>{tripStatus}</span>
                <div className="trip-bill-item-name">{get(bill, 'form.title', '')}</div>
                <div className="trip-bill-item-content">
                  <div className="trip-bill-item-child">
                    <span className="trip-bill-item-dept">{specificationId && specificationId.name}</span>
                    <span className="trip-bill-item-code">{applicationFormCode}</span>
                  </div>
                  <Money className="trip-bill-money" value={get(bill, `form.${bill.formType}Money`, '')} />
                </div>
              </div>
            </section>
          )}
        </div>
        {buttonText && (
          <div className="footer">
            <Button category="primary" className="btn-confirm" disabled={disabled} onClick={this.handleOnClick}>
              {i18n.get(`一键{__k0}`, { __k0: buttonText })}
            </Button>
            <div className="color-gray text">
              {i18n.get(`当前共 {__k0}/{__k1} 人已成功{__k2}`, {
                __k0: size(tripOrders),
                __k1: traveler.length,
                __k2: buttonText
              })}
            </div>
          </div>
        )}
      </div>
    )
  }
}
