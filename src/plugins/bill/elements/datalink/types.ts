/**
 *  Created by gym on 2019-05-17 11:43.
 */
export interface datalinkCardInterface {
  data: any
  id: string
  template: any
  total: number
}

export interface titleListInterface {
  label: string
  name: string
  optional: boolean
  source: string
  type: string
  scale?: number
  unit?: string
  withTime?: boolean
  entity?: string
}

export interface overviewItemInterface {
  name: string
  value?: string
  label: string
  classname?: string
  controlType?: string
  type?: string
  executeName?: string
}

export interface Traveler {
  id: string
  type: number
  name: string
}
export interface TravelDetail {
  id: string
  type: string
  name: string
  startTime: number
  endTime: number
  budgetAmount: any
  cities: any[]
  staffs: any[]
  orders: any[]
  traveler: Traveler[]
  [key: string]: any
}
