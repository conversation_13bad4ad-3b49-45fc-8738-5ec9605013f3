@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.list_title {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  img {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    margin-right: @space-4;
    flex-shrink: 0;
  }
  .content-wrapper {
    display: flex;
    flex: 1;
    overflow: hidden;
    justify-content: space-between;
  }
  .listname {
    text-align: left;
    flex: 1;
    overflow: hidden;
    .list_name_top{
      width: 100%;
      color: @eui-ref-color-grey;
      font-size: 28px;
      line-height: 0.48rem;
      height: 0.48rem;
      font-weight: 500;
      margin-bottom: 0.1rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .collect-icon{
        margin-left: 5px;
        color: #FADC19;
      }
    }
    .list_name_top_show_all{
      width: 100%;
      color: #1d2b3d;
      font-size: 0.32rem;
      line-height: 0.48rem;
      font-weight: 500;
      white-space: pre-wrap;
      margin-bottom: 0.08rem;
    }
    .list_is_top {
      background: #f5f5f5;
      padding: 0.02rem 0.04rem;
      font-size: 0.12rem;
      margin-left: 0.14rem;
      border-radius: 0.08rem;
      opacity: 0.5;
    }
    .list_name_bottom{
      width: 100%;
      font-size: 0.24rem;
      color: @eui-sys-neutral-grey-2;
      font-weight: 400;
      height: 0.4rem;
      line-height: 0.4rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .describe {
    flex-shrink: 0;
    display: flex;
    margin-left: @space-2;
    > div:nth-child(1) {
      flex: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      > div:nth-child(1) {
        color: #1d2b3d;
        font-size: 28px;
        line-height: 0.48rem;
        height: 0.48rem;
        font-weight: 400;
        margin-bottom: 0.08rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > div:nth-child(2) {
        font-size: 0.24rem;
        color: rgba(29, 43, 61, 0.5);
        font-weight: 400;
        height: 0.4rem;
        line-height: 0.4rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .imgbox {
      margin-left: 0.24rem;
      padding-top: 0.04rem;
      >svg{
        width: 0.36rem;
        height: 0.36rem;
        color: var(--brand-base);
      }
    }
    .dirLeftIcon{
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(29, 33, 41, 0.6);
    }
  }
  .iconImg {
    width: 0.4rem;
    height: 0.4rem;
    margin-left: 0.24rem;
    display: inline-block;
  }
}
