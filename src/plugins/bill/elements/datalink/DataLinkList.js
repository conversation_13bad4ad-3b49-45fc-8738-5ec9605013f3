import { app, app as api } from '@ekuaibao/whispered'
import styles from './DataLinkList.module.less'
import React, { PureComponent } from 'react'
const EKBSearchBar = app.require('@ekb-components/base/EKBSearchBar')
const ListViewWrapper = app.require('@elements/listview/ListViewWrapper')
const { getStrLastWord } = app.require('@components/utils/fnDataLinkUtil')
import { Button, Checkbox, Icon } from 'antd-mobile'
import { SearchBar, Dialog } from '@hose/eui-mobile'
import { getTravelOrderData, getWeek } from './dataLinkUtil'
import { sortBy, get } from 'lodash'
import Popup from '@ekuaibao/popup-mobile/esm/index'
import FilterForm from './datalink-detail-modal/elements/FilterForm'
import {
  formatEntityList,
  getNameCodeSearchKey,
  filterDataLinkUseCount,
  handleDataLinkUseCount,
  handleHeaderFields,
  formatData
} from '../../utils/dataLinkUtils'
import { toast } from '../../../../lib/util'
import moment from 'moment'
import classnames from 'classnames'
import { EnhanceConnect } from '@ekuaibao/store'
const ThirdImportListItem = app.require('@third-import/thirdImport-list-item')
import ListTitle from './ListTitle'
import { getV } from '@ekuaibao/lib/lib/help'
const CheckboxItem = Checkbox.CheckboxItem
const AgreeItem = Checkbox.AgreeItem
import { TravelCard } from './TravelCard'
const EKBIcon = app.require('@elements/ekbIcon')
let timeout
const packageSize = 10
const tripOrderPackageSize = 9999 //行程订单一次性取值
import FILTERICON from '../../images/filter.svg'
import { QuerySelect } from 'ekbc-query-builder'
import { OutlinedEditFilter } from '@hose/eui-icons'
import EKBDropDown from '../EKBDropDown'
const EmptyWidget = app.require('@home5/EmptyWidget')

const menuTypeMap = {
  ALL: i18n.get('全部'),
  COLLECT: i18n.get('我的收藏')
}

const menuList = [
  { label: menuTypeMap.ALL, type: 'ALL', checkVisible: true },
  { label: menuTypeMap.COLLECT, type: 'COLLECT', checkVisible: true }
]

@EnhanceConnect(state => ({
  dataLink: state['@bill'].dataLink,
  powerCodeMap: state['@common'].powers.powerCodeMap
}))
export default class DataLinkModal extends PureComponent {
  searchRef = React.createRef()
  constructor(props) {
    super(props)
    this.state = {
      listData: [],
      entityId: props.id || [],
      searchValue: '',
      isLoading: false,
      hasMore: true,
      loading: false,
      isAllChecked: false,
      filterBy: '',
      isChecked: false,
      filterByOther: '',
      template: {},
      filter: {},
      isSkipCondition: false,
      showDropDown: false,
      activeType: 'ALL',
      activeText: menuTypeMap.ALL,
      isOpenSearch: false,
      collectData: [],
      selectData: []
    }
    this.filterByKey = undefined
  }

  componentWillMount() {
    let {
      value: { referenceData },
      isDetail,
      dataMap,
      bus,
      fromDataLinkUpdate,
      template
    } = this.props
    if (referenceData && referenceData.disableStrategy === 'LIMIT_COUNT' && !fromDataLinkUpdate) {
      if (isDetail) {
        //如果是消费记录的话需要实时获取当前消费记录表单的值来计算引用次数，其余的消费记录以及表头的的引用次数从外面传过来，然后将两个值合并
        bus &&
          bus.getValue().then(result => {
            let map = {}
            handleHeaderFields(template, result, map)
            for (let key in map) {
              if (dataMap[key]) {
                dataMap[key] += 1
              } else {
                dataMap[key] = 1
              }
            }
            this.map = dataMap
            this.getEntityList()
          })
      } else {
        //不是消费记录的话直接获取整个单据的值来计算引用次数就可以了
        api.invoke('get:bills:value').then(result => {
          this.map = handleDataLinkUseCount(result)
          this.getEntityList()
        })
      }
    } else {
      this.getEntityList()
    }
    api.on('update:dataLinkListOfCollect', this.updateDataLinkList)
  }

  componentWillUnmount() {
    api.un('update:dataLinkListOfCollect', this.updateDataLinkList)
  }

  updateDataLinkList = v => {
    if (v) {
      this.getEntityList()
    }
  }

  getIsCollectType = () => {
    return this.state.activeType === 'COLLECT'
  }

  getEntityList(filterBy, start = 0) {
    let {
      value: { referenceData, filterId, allMatchList, field },
      flowId,
      isDetail,
      formValue,
      dependenceParams,
      feeTypeSpecificationId
    } = this.props
    if (start === 0) {
      this.setState({ loading: true })
    }
    const { filterByOther } = this.state
    const platformType = get(referenceData, 'platformId.type')
    const isPrivateCar = platformType === 'PRIVATE_CAR'
    const activeFilter = `active==true`
    const submitterId = getV(this.props, 'formValue.form.submitterId') || getV(this.props, 'submitterId.id')
    const isSkipCondition = this.state.isSkipCondition
    if (isPrivateCar) {
      let params = {
        entityId: typeof referenceData === 'object' ? get(referenceData, 'id') : referenceData,
        submitterId: submitterId?.id || submitterId,
        child: true,
        type: 'TABLE'
      }
      const query = new QuerySelect()
        .orderBy('createTime', 'DESC')
        .select(`ownerId(...),...`)
        .limit(start, packageSize)

      if (filterBy) {
        query.filterBy(filterBy)
      }
      if (flowId) {
        query.filterBy(`(${activeFilter} || flowCounts.containsIgnoreCase("${flowId}"))`)
      } else {
        query.filterBy(activeFilter)
      }
      if (filterByOther) {
        query.filterBy(filterByOther)
      }
      
      params = {
        ...params,
        flowId,
        filterId,
        query: query.value(),
        form: formValue && formValue.form,
        params: formValue && formValue.params
      }
      const shouldFixParam = api.getState()['@common'].toggleManage?.['tg_fix_searchDatalink_request']
      if (shouldFixParam && params?.form?.linkDetailEntities?.length > 0) {
        params?.form?.linkDetailEntities.forEach(el => {
          if (
            el?.specificationId
            && typeof el.specificationId === 'object'
            && el.specificationId?.id
          ) {
            el.specificationId = el.specificationId.id
          }
        })
      }
      // 私车公用 使用 v2 和 web 端一样的接口
      api.invokeService('@bill:get:entity:list:v2', params).then(res => {
        const data = this.formatRegStr(res)
        this.setState({ loading: false })
        this.handleFormatDataLink(data)
      })
    } else {
      const isTravelOrder = platformType === 'TRAVEL_MANAGEMENT' && referenceData.type === 'ORDER'
      let orderBy = undefined
      if (isTravelOrder) {
        const startDate = referenceData.fields.find(item => item.name.endsWith('出发时间')).name //@i18n-ignore
        const hotelStartDate = referenceData.fields.find(item => item.name.endsWith('入住日期')).name //@i18n-ignore
        orderBy = [
          { value: `form.${startDate}`, order: 'DESC' },
          { value: `form.${hotelStartDate}`, order: 'DESC' }
        ]
      }
      const query = new QuerySelect().filterBy(activeFilter)
      if (filterBy) {
        query.filterBy(filterBy)
      }
      if (filterByOther) {
        query.filterBy(filterByOther)
      }

      let newForm = formValue && formValue.form
      if (feeTypeSpecificationId) {
        newForm = { ...formValue.form, specificationId: feeTypeSpecificationId }
      }

      let params = {
        entityId: typeof referenceData === 'object' ? get(referenceData, 'id') : referenceData,
        type: 'LIST',
        start,
        count: isTravelOrder ? tripOrderPackageSize : packageSize,
        filterId,
        isDetail,
        flowId,
        form: newForm,
        field,
        params: isTravelOrder ? { type: 'order' } : formValue && formValue.params,
        submitterId: submitterId?.id || submitterId
      }
      params = { ...params, ...query.value() }
      if (isTravelOrder) {
        params = { ...params, orderBy }
      }
      if (typeof allMatchList !== 'undefined') {
        //业务对象联查
        params.allMatchList = allMatchList
      }
      //配置为空可查看全量数据
      if (isSkipCondition) {
        params.isSkipCondition = isSkipCondition
      }
      //我的收藏
      const isCollectType = this.getIsCollectType()
      if (isCollectType) {
        params.favoriteStatus = true
      }
      let promise = undefined
      if (dependenceParams) {
        const { start, count, filterBy } = params
        const data = { query: { limit: { start, count }, filterBy } }
        promise = api
          .invokeService('@bill:get:datalink:dependence:list', { ...dependenceParams, ...data })
          .then(res => {
            return { items: res }
          })
      } else {
        promise = api.invokeService('@mine:post:serach:dataLink', params)
      }

      return promise.then(res => {
        this.setState({ loading: false })
        const temp = res?.items?.template?.content?.expansion
        this.handleFormatDataLink1(res, start)
        this.setState({
          template: temp,
          start: start + packageSize,
          isSkipCondition: res?.items?.rule?.isCanViewAllDataWithResultBlank
        })
      })
    }
  }

  formatRegStr(res) {
    // 组装成原本的数据结构
    const it = get(res, 'items', {})
    const count = get(it, 'total', 0)
    const da = get(it, 'data', [])
    const items = da.reduce((res, ii) => {
      const dataLink = get(ii, 'dataLink')
      res.push({ ...dataLink, form: dataLink })
      return res
    }, [])
    return { count, items }
  }
  handleFormatDataLink(result) {
    let { listData, entityId } = this.state
    let {
      value: { referenceData },
      multi
    } = this.props
    let entityList = get(result, 'items') || []
    entityList = entityList.slice()
    let hasMore = listData.length + entityList.length === result.count
    entityList =
      get(referenceData, 'disableStrategy') === 'LIMIT_COUNT'
        ? filterDataLinkUseCount(this.map, entityList)
        : entityList
    let newEntityList = formatEntityList(referenceData.fields, entityList)
    let cListData = listData.concat(newEntityList)
    this.setState({
      listData: cListData,
      isLoading: false,
      hasMore: !hasMore,
      isAllChecked: multi ? cListData.length === entityId.length : false
    })
    this.filterByKey = this.filterByKey ? this.filterByKey : getNameCodeSearchKey(entityList)
  }

  handleFormatDataLink1(result, start) {
    let { listData, entityId } = this.state
    if (start <= 0) {
      listData = []
    }
    let {
      value: { referenceData },
      multi
    } = this.props
    let entityList = get(result, 'items.data') || []
    entityList = entityList.slice()
    let hasMore = listData.length + entityList.length === result.items.total
    entityList =
      get(referenceData, 'disableStrategy') === 'LIMIT_COUNT'
        ? filterDataLinkUseCount(this.map, entityList)
        : entityList
    let newEntityList = formatEntityList(referenceData.fields, entityList)
    let cListData = listData.concat(newEntityList)
    let { template } = result.items
    this.setState({
      isLoading: false,
      hasMore: !hasMore,
      template,
      isAllChecked: multi ? cListData.length === entityId.length : false
    })
    const isCollectType = this.getIsCollectType()
    if (isCollectType) {
      this.setState({ collectData: cListData })
    } else {
      this.setState({ listData: cListData })
    }
    if (entityList.length > 0) {
      this.filterByKey = this.filterByKey ? this.filterByKey : this.getSearchKey(entityList[0].dataLink)
    }
  }
  getSearchKey = form => {
    let nameKey = '',
      codeKey = '',
      formKey = '',
      toKey = ''
    for (let key in form) {
      if (getStrLastWord(key, '_') === 'name') {
        nameKey = key
      } else if (getStrLastWord(key, '_') === 'code') {
        codeKey = key
        //@i18n-ignore
      } else if (getStrLastWord(key, '_') === '出发地') {
        formKey = key
        //@i18n-ignore
      } else if (getStrLastWord(key, '_') === '目的地') {
        toKey = key
      }
    }
    return { nameKey, codeKey, formKey, toKey }
  }

  handleOnSearchChange = value => {
    value = value.trimStart() // 英文中间有空格
    const type = get(this.props.value, 'referenceData.platformId.type', '')
    const isPrivateCar = 'PRIVATE_CAR' === type
    if (!this.filterByKey) return this.setState({ searchValue: value })
    let { nameKey, codeKey, toKey, formKey } = this.filterByKey || {}
    let filterBy = value
      ? isPrivateCar
        ? `(form.${formKey}.address.containsIgnoreCase("${value}") || form.${toKey}.address.containsIgnoreCase("${value}"))`
        : `(form.${nameKey}.containsIgnoreCase("${value}") || form.${codeKey}.containsIgnoreCase("${value}"))`
      : ''

    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
    this.setState({ searchValue: value, filterBy, listData: [], collectData: [] }, () => {
      timeout = setTimeout(() => {
        this.getEntityList.call(this, filterBy)
      }, 400)
    })
  }

  handleCancel() {
    this.setState({ searchValue: '', filterBy: '', listData: [], isAllChecked: false, collectData: [] }, () => {
      this.getEntityList()
    })
  }

  handleSearchCancel = () => {
    this.handleCancel()
  }

  handleOnSearchClear = () => {
    this.setState({ searchValue: '', filterBy: '' })
  }

  handleSelectPrivateCarItemClick = rowData => {
    const { multi, fromDataLinkEdit } = this.props
    let { entityId, listData, isAllChecked } = this.state
    let { id } = rowData
    let {
      form: { codeLabel, codeStr, nameStr }
    } = rowData
    if (!multi) {
      if (fromDataLinkEdit) {
        this.props.layer.emitOk({ dataLink: rowData })
      } else {
        this.props.layer.emitOk({ ...rowData, showCodeLabel: codeLabel, showCode: codeStr, name: nameStr })
      }
    } else {
      if (entityId.indexOf(id) > -1) {
        entityId = entityId.filter(item => item !== id)
        isAllChecked = false
      } else {
        entityId = [...entityId, id]
        isAllChecked = listData.length === entityId.length + 1
      }
      const selectData = listData.filter(it => entityId.includes(it?.id))
      this.setState({ entityId, isAllChecked, selectData: selectData.map(it => ({ dataLink: it })) })
    }

  }

  handleSelectItemClick = (rowData, disabled) => {
    if (disabled) return
    const { multi } = this.props
    let { entityId, listData, isAllChecked } = this.state
    let { dataLink } = rowData
    let { id } = dataLink
    if (multi) {
      if (entityId.indexOf(id) > -1) {
        entityId = entityId.filter(item => item !== id)
        isAllChecked = false
      } else {
        entityId = [...entityId, id]
        isAllChecked = listData.length === entityId.length + 1
      }
      const selectData = listData.filter(it => entityId.includes(it?.dataLink?.id))
      this.setState({ entityId, isAllChecked, selectData })
    } else {
      this.props.layer.emitOk({ rowData, dataLink, id })
    }
  }
  handleImport = () => {
    const { fromDataLinkUpdate } = this.props
    const { entityId, selectData } = this.state
    if (fromDataLinkUpdate) {
      this.props.layer.emitOk({ selectData })
    } else {
      this.props.layer.emitOk({ id: entityId })
    }
  }

  renderTripGroup = (date, list, index) => {
    const { multi, selectedId = [], fromDataLinkUpdate } = this.props
    const { entityId } = this.state
    return (
      <div key={`group${index}`}>
        <div className="date-group">{date}</div>
        {list &&
          list.map((trip, i) => {
            const id = get(trip, 'dataLink.id')
            const isSelected = selectedId.indexOf(id) > -1
            const checked = multi
              ? entityId.indexOf(trip.dataLink.id) > -1
              : fromDataLinkUpdate
                ? isSelected
                : entityId === trip.dataLink.id
            const data = getTravelOrderData(trip.dataLink)
            return (
              <div className="trip-order-item" key={`trip${id}`}>
                <Checkbox
                  checked={checked}
                  disabled={isSelected}
                  onChange={this.handleSelectItemClick.bind(this, trip, isSelected)}
                />
                <TravelCard
                  style={{ flex: 1, marginLeft: 5 }}
                  data={data}
                  key={i}
                  handleCardClick={this.handleOnItemClick.bind(this, trip)}
                ></TravelCard>
              </div>
            )
          })}
      </div>
    )
  }
  handleOnClick = (type, value) => {
    api.open('@home5:TripDetailModal', { value, type })
  }
  getDateValue = item => {
    const value = item.dataLink
    const isHotel = get(item, 'dataLink.entity.type') === 'HOTEL'
    const isShop = get(item, 'dataLink.entity.type') === 'SHOP'
    const startDate = isHotel
      ? value[Object.keys(value).find(o => !!o.endsWith('入住日期'))] // @i18n-ignore
      : isShop
        ? value[Object.keys(value).find(o => !!o.endsWith('订单日期'))]
        : value[Object.keys(value).find(o => !!o.endsWith('出发时间'))] // @i18n-ignore
    return startDate
  }
  getGroup = trips => {
    const newDataList = sortBy(trips, item => {
      return -this.getDateValue(item)
    })
    const tripsMap = {}
    newDataList.forEach(item => {
      const startDate = this.getDateValue(item)
      const date = `${moment(startDate).format('YYYY年MM月DD日')} ${getWeek(startDate)}`
      if (tripsMap[date]) {
        tripsMap[date].push(item)
      } else {
        tripsMap[date] = [item]
      }
    })
    return tripsMap
  }

  handleOnItemClick = rowData => {
    let field = this.props.value
    let rowValue = {}
    rowValue.data = rowData.dataLink
    rowValue.id = rowData.dataLink.id
    rowValue.template = this.state.template
    let title = get(field, 'referenceData.name')
    const type = get(this.props.value, 'referenceData.platformId.type', '')
    const referenceType = get(this.props.value, 'referenceData.type', '')
    const isOrder = type === 'TRAVEL_MANAGEMENT' && referenceType === 'ORDER'
    if (isOrder) {
      api.open('@bill:DataLinkEntityTripOrderDetailModal', {
        field,
        value: { ...rowValue, data: { dataLink: rowData.dataLink } },
        title
      })
    } else {
      api.open('@bill:DataLinkEntityDetailModal', { field, value: rowValue, title, hasCollect: true })
    }
  }

  handleViewAllData = () => {
    this.getEntityList()
  }

  row = rowData => {
    const { template, entityId, isChecked } = this.state
    const { multi, selectedId = [], fromDataLinkUpdate } = this.props
    const isSelected = selectedId.indexOf(rowData.dataLink.id) > -1
    const checked = multi
      ? entityId.indexOf(rowData.dataLink.id) > -1
      : fromDataLinkUpdate
        ? isSelected
        : entityId === rowData.dataLink.id
    const cls = classnames('item-wrapper', { 'bg-checked': checked })
    return (
      <div className={cls}>
        <CheckboxItem
          checked={checked}
          disabled={isSelected}
          onChange={this.handleSelectItemClick.bind(this, rowData, isSelected)}
        >
          <div onClick={this.handleOnItemClick.bind(this, rowData)}>
            <ListTitle data={rowData} isShowAll={isChecked} template={template} key={entityId} isShowDirRight={true} />
          </div>
        </CheckboxItem>
      </div>
    )
  }
  renderPrivateCarItem = rowData => {
    const { entityId } = this.state
    const { multi } = this.props
    const { id } = rowData
    const data = formatData(rowData, this.props.value.referenceData)
    const checked = entityId?.indexOf(id) > -1

    return multi ? <ThirdImportListItem isChecked={checked} key={id} showCheckBox={multi} data={data} disabled={false} onCheckedChange={this.handleSelectPrivateCarItemClick.bind(this, rowData)} /> : (
      <div style={{ width: '100%' }} onClick={this.handleSelectPrivateCarItemClick.bind(this, rowData)}>
        <ThirdImportListItem key={id} showCheckBox={false} data={data} disabled={false} />
      </div>
    )
  }

  renderContentNullBody() {
    return <EmptyWidget size={200} type="noCentent" tips={i18n.get('无可选项')} />
  }

  renderEmpty() {
    const { loading, isSkipCondition } = this.state

    if (isSkipCondition) {
      return (
        <div className="datalink-empty">
          {loading ? (
            i18n.get('正在加载...')
          ) : (
            <span>
              暂无数据，点击
              <i className="datalink-viewAll" onClick={this.handleViewAllData}>
                查看全量数据
              </i>
            </span>
          )}
        </div>
      )
    }
    return <div className="datalink-empty">{loading ? i18n.get('正在加载...') : this.renderContentNullBody()}</div>
  }

  renderListFooter = () => {
    let { isLoading, hasMore } = this.state
    let endStr = hasMore ? i18n.get('加载完毕') : i18n.get('没有更多数据了')
    return <div style={{ padding: 30, textAlign: 'center' }}>{isLoading ? i18n.get('加载更多') : endStr}</div>
  }

  onEndReached = () => {
    let { isLoading, hasMore, listData, filterBy, collectData } = this.state
    if (isLoading || !hasMore) {
      return
    }
    this.setState({ isLoading: true })
    const isCollectType = this.getIsCollectType()
    const len = isCollectType ? collectData.length : listData.length
    this.getEntityList(filterBy, len)
  }

  renderOrder = () => {
    const { listData } = this.state
    const tripGroup = this.getGroup(listData)
    return (
      <div className="travel-order-wrapper">
        {Object.keys(tripGroup).map((date, index) => {
          return this.renderTripGroup(date, tripGroup[date], index)
        })}
      </div>
    )
  }
  renderTripOrderBottom = () => {
    const { isAllChecked } = this.state
    return (
      <div className={styles.thirdList_footer}>
        <AgreeItem checked={isAllChecked} onChange={this.handleCheckedAll}>
          {i18n.get('全选')} {i18n.get('已选')}({this.state.entityId?.length ?? 0})
        </AgreeItem>
        <Button type="primary" className={styles.thirdList_footer_btn} onClick={this.handleImport}>
          {i18n.get('确定')}
        </Button>
      </div>
    )
  }
  handleCheckedAll = e => {
    const { checked } = e.target
    const { listData } = this.state
    const type = get(this.props.value, 'referenceData.platformId.type', '')
    const isPrivateCar = 'PRIVATE_CAR' === type
    const entityId = listData.map(item => isPrivateCar ? item?.id : get(item, 'dataLink.id'))
    const selectData = listData.filter(it => isPrivateCar ? entityId.includes(it?.id) : entityId.includes(it?.dataLink?.id)).map(item => (isPrivateCar ? { dataLink: item } : item))
    if (checked) {
      this.setState({
        selectData,
        entityId,
        isAllChecked: true
      })
    } else {
      this.setState({
        selectData: [],
        entityId: [],
        isAllChecked: false
      })
    }
  }
  handleShow = () => {
    Dialog.confirm({
      title: i18n.get('提示'),
      content: i18n.get('因差旅平台同步订单有一定的时效性，可通过手动同步差旅订单'),
      confirmText: i18n.get('刷新'),
      onConfirm: () => {
        api.open('@third-import:SelectDateRange', { months: 1 }).then(async params => {
          const { startTime, endTime } = params
          const result = await api.invokeService('@bill:pull:TravelOrder', {
            startDate: startTime,
            endDate: endTime
          })
          if (result?.value?.success) {
            toast.success(i18n.get('同步成功'))
            this.setState(
              {
                listData: [],
                searchValue: ''
              },
              () => {
                this.getEntityList()
              }
            )
          } else {
            toast.success(i18n.get('同步失败'))
          }
        })
      }
    })
  }
  handlePopupCancel = () => {
    Popup.hide()
  }
  handleFilterData = (filter, filterByOther) => {
    Popup.hide()
    const { filterBy } = this.state
    this.setState({ filter, isChecked: filter.show_full_name, filterByOther: filterByOther?.filterBy }, () => {
      this.getEntityList(filterBy)
    })
  }
  handleFilter = () => {
    const fields = this.props?.value?.referenceData?.fields || []
    Popup.show(
      <div className={styles['popup-modal']} onCancel={this.handlePopupCancel}>
        <div className="modal-title">
          <div className="title ">{i18n.get('筛选条件')}</div>
          <Icon type="cross" onClick={this.handlePopupCancel} />
        </div>
        <span className="modal-title-tips">{i18n.get('您可对当前业务对象下的字段进行筛选，')}</span>
        <span className="modal-title-tips mb-8">{i18n.get('展示结果等于您输入所有条件的组合')}</span>
        <FilterForm fields={fields} filter={this.state.filter} handleFilter={this.handleFilterData}></FilterForm>
      </div>,
      {
        animationType: 'slide-up',
        maskClosable: true,
        wrapClassName: 'popup-wrapper-style'
      }
    )
  }

  handleOpenPopup = () => {
    const { showDropDown } = this.state
    this.setState({ showDropDown: !showDropDown })
  }

  handleChangeMenuType = menuType => {
    this.setState(
      {
        activeType: menuType,
        activeText: menuTypeMap[menuType],
        searchValue: '',
        filter: {},
        filterBy: '',
        filterByOther: ''
      },
      _ => {
        this.getEntityList()
      }
    )
  }

  handleCloseMenuType = () => {
    this.setState({ showDropDown: false })
  }

  handleOpenSearchBar = () => {
    const { isOpenSearch } = this.state
    this.setState({
      isOpenSearch: !isOpenSearch
    })
  }

  renderHeader = () => {
    const { showDropDown, isOpenSearch, activeText } = this.state
    const arrowClass = !showDropDown ? 'rotate' : ''

    return (
      <div className="search-wrapper">
        <div className="type_name" onClick={this.handleOpenPopup}>
          <span>{activeText}</span>
          <EKBIcon
            className={arrowClass}
            name="#EDico-icon_uni_drop-title"
            style={{ color: [showDropDown ? 'var(--brand-base)' : '#272E3B'], fontSize: 16 }}
          />
        </div>
        <div className="header-right">
          <EKBIcon
            name="#EDico-icon_uni_search"
            style={{ color: [isOpenSearch ? 'var(--brand-base)' : '#000'], fontSize: 20 }}
            onClick={this.handleOpenSearchBar}
          />
          <OutlinedEditFilter style={{ marginLeft: '27px' }} fontSize={20} onClick={this.handleFilter} />
        </div>
      </div>
    )
  }

  renderSearchBar = () => {
    const { searchValue, isOpenSearch } = this.state
    let headerStyle = {}

    if (!isOpenSearch) return null

    return (
      <React.Fragment>
        <SearchBar
          ref={this.searchRef}
          showCancelButton
          className={styles['dataLink-list-search-bar']}
          style={headerStyle}
          value={searchValue}
          placeholder={i18n.get('请输入名称或编码')}
          onChange={this.handleOnSearchChange}
          onClear={this.handleOnSearchClear}
          onCancel={this.handleSearchCancel}
        />
      </React.Fragment>
    )
  }

  renderNewSearch() {
    const { showDropDown, activeType } = this.state

    return (
      <React.Fragment>
        {this.renderHeader()}
        {this.renderSearchBar()}
        {showDropDown && (
          <EKBDropDown
            show={showDropDown}
            activeType={activeType}
            menuList={menuList}
            fnChangeType={this.handleChangeMenuType}
            fnCancel={this.handleCloseMenuType}
          />
        )}
      </React.Fragment>
    )
  }

  renderOrderSearch() {
    const { searchValue } = this.state
    return (
      <div className="search-wrapper">
        <div className="eui-search-bar-wrapper">
          <EKBSearchBar
            value={searchValue}
            placeholder={i18n.get('搜索')}
            onChange={this.handleOnSearchChange}
            onCancel={this.handleSearchCancel}
            onClear={this.handleOnSearchClear}
          />
        </div>
        <div onClick={this.handleFilter} className="search-wrapper-filter">
          <img className="mr-5" src={FILTERICON}></img>
          {i18n.get('筛选')}
        </div>
      </div>
    )
  }

  renderDefaultContent = () => {
    const { listData } = this.state
    const type = get(this.props.value, 'referenceData.platformId.type', '')
    const referenceType = get(this.props.value, 'referenceData.type', '')
    const isOrder = type === 'TRAVEL_MANAGEMENT' && referenceType === 'ORDER'
    const isPrivateCar = 'PRIVATE_CAR' === type
    const row = isPrivateCar ? this.renderPrivateCarItem : this.row

    return (
      <>
        {listData.length > 0 ? (
          isOrder ? (
            this.renderOrder()
          ) : (
            <div className="list-parent">
              <ListViewWrapper
                style={{ paddingTop: 0 }}
                isPrivateCar={isPrivateCar}
                footer={this.renderListFooter}
                onEndReached={this.onEndReached}
                onEndReachedThreshold={10}
                className="list-wrapper"
                listData={listData}
                itemRender={row}
              />
            </div>
          )
        ) : (
          this.renderEmpty()
        )}
      </>
    )
  }

  renderMyCollect = () => {
    const { collectData } = this.state

    return (
      <>
        {collectData.length ? (
          <div className="list-parent">
            <ListViewWrapper
              style={{ paddingTop: 0 }}
              isPrivateCar={false}
              footer={this.renderListFooter}
              onEndReached={this.onEndReached}
              onEndReachedThreshold={10}
              className="list-wrapper"
              listData={collectData}
              itemRender={this.row}
            />
          </div>
        ) : (
          this.renderEmpty()
        )}
      </>
    )
  }

  renderContent = () => {
    const isCollectType = this.getIsCollectType()
    if (isCollectType) {
      return this.renderMyCollect()
    } else {
      return this.renderDefaultContent()
    }
  }

  render() {
    const { listData, showDropDown, activeType } = this.state
    const { multi } = this.props
    const type = get(this.props.value, 'referenceData.platformId.type', '')
    const referenceType = get(this.props.value, 'referenceData.type', '')
    const isOrder = type === 'TRAVEL_MANAGEMENT' && referenceType === 'ORDER'
    const isPrivateCar = 'PRIVATE_CAR' === type
    const row = isPrivateCar ? this.renderPrivateCarItem : this.row
    const isHoseMall = this.props.powerCodeMap.includes('120209')

    return (
      <div className={styles['dataLink-wrapper']}>
        {isOrder ? this.renderOrderSearch() : this.renderNewSearch()}
        {isOrder && isHoseMall && (
          <div className="sync-wrapper" onClick={this.handleShow}>
            <EKBIcon name="#EDico-info-circle" />
            <div className="info">{i18n.get('找不到订单？')}</div>
            <EKBIcon name="#EDico-right-default" />
          </div>
        )}
        <div className="datalink-content">{isOrder ? this.renderDefaultContent() : this.renderContent()}</div>
        {multi && this.renderTripOrderBottom()}
      </div>
    )
  }
}
