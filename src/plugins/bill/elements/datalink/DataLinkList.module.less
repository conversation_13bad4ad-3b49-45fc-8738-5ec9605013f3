@import "../../../../styles/ekb-colors";
@import "~@ekuaibao/eui-styles/less/token-mobile.less";
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.dataLink-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  :global {
    .eui-checkbox {
      --icon-size: 32px; 
      .eui-checkbox-content {
        font-size: 32px;
      }
    }
    .fg-line {
      width: 100%;
      height: 16px;
      background: #F5F5F5;
    }
    .search-bar-padding{
      padding: 12px 24px;
    }
    .search-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-shrink: 0;
      width: 100%;
      height: 120px;
      padding: 0 @space-6;
      z-index: 3;

      .eui-search-bar-wrapper{
        padding-left: 24px;
        flex: 1;
      }

      .search-wrapper-filter {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-left: 16px;
        font-weight: 400;
        margin-right: 24px;
        font-size: 28px;
        color: rgba(39, 46, 59, 0.72);
      }

      .header-right {
        display: flex;
        cursor: pointer;
        .filterItem {
          margin-left: 28px;
        }
      }

      .type_name {
        font-size: var(--eui-text-title);
        color: var(--eui-text-title);
        cursor: pointer;
        .active {
          color: @eui-ref-color-brand;
        }
        svg {
          margin-left: 8px;
          transition: 0.2s ease all;
          &.rotate {
            transform: rotate(180deg);
          }
        }
      }
    }
    .sync-wrapper {
      display: flex;
      align-items: center;
      height: 100px;
      background-color: #f0fcff;
      .info {
        flex: 1 1;
        font-size: 0.26rem;
        color: #52b6ea;
      }
      .icon {
        color: #52b6ea;
        width: 32px;
        height: 32px;
        margin: 0 32px;
      }
    }
    .datalink-content {
      overflow: hidden;
      flex: 1;
      display: flex;
      .list-parent {
        display: flex;
        flex: 1;
        flex-direction: column;
        .list_top {
          margin: 0 24px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-end;
          font-size: 32px;
          line-height: 1.5;
          text-align: right;
          .am-switch {
            margin-left: 32px;
            input[type="checkbox"]:checked + .checkbox {
              background: var(--brand-base);
            }
          }
        }
        .list-wrapper {
          flex: 1;
        }
        .bg-checked {
          background-color: #fafafa;
        }
        .item-wrapper {
          display: flex;
          padding: 0.12rem 0.32rem 0.12rem 0;
          border-bottom: 2px solid #e8e8e8;
          .am-list-item {
            background: none;
            flex: 1;
            .am-list-thumb {
              .am-checkbox-wrapper {
                .am-checkbox {
                  width: 100px;
                  top: 22px !important;
                }
              }
            }
            .am-list-line {
              padding-right: 0;
              .am-list-content {
                width: 80vw;
              }
            }
          }
          .am-list-item.am-list-thumb.am-checkbox-wrapper.am-checkbox {
            width: auto;
          }
          .datalink-card-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .item-title {
              font-size: 28px;
              font-weight: 600;
              color: rgba(29, 43, 61, 1);
              line-height: 44px;
              max-width: 560px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .item-code {
            font-size: 24px;
            color: rgba(29, 43, 61, 0.5);
            line-height: 40px;
          }
          .left {
            flex: 1;
            div:first-child {
              font-size: 32px;
              color: #262626;
            }
            div:last-child {
              font-size: 28px;
              color: #8c8c8c;
            }
          }
          .right {
            flex-shrink: 0;
            width: 32px;
            height: 32px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
      .travel-order-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        overflow: auto;
        background: white;
        padding: @space-4 @space-6;
        .title {
          text-align: left;
          .font-weight-3;
          .font-size-5;
          color: @color-black-1;
        }
        .date-group {
          text-align: left;
          .font-weight-3;
          .font-size-2;
          color: @color-black-1;
          margin-top: @space-4;
          margin-bottom: @space-4;
        }
        .trip-order-item {
          display: flex;
          flex-direction: row;
          align-items: center;
        }
      }
      .datalink-empty {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #bfbfbf;
        font-size: 32px;
        .datalink-viewAll{
          font-style: normal;
          color: @color-brand-2;
        }
      }
    }

    .multi-list {
      height: 100%;
      overflow: scroll;
      padding-bottom: 88px;
      .list_top {
        margin: 0 24px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        font-size: 32px;
        line-height: 1.5;
        text-align: right;
        .am-switch {
          margin-left: 32px;
          input[type="checkbox"]:checked + .checkbox {
            background: var(--brand-base);
          }
        }
      }
      .datalink-empty {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: @gray-6;
        font-size: 28px;
      }
      .travel-order-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        overflow: auto;
        background: white;
        padding: @space-4 @space-6;
        .title {
          text-align: left;
          .font-weight-3;
          .font-size-5;
          color: @color-black-1;
        }
        .date-group {
          text-align: left;
          .font-weight-3;
          .font-size-2;
          color: @color-black-1;
          margin-top: @space-4;
          margin-bottom: @space-4;
        }
        .trip-order-item {
          display: flex;
          flex-direction: row;
          align-items: center;
        }
      }
      .item-wrapper {
        display: flex;
        // padding: 24px 0 24px 34px;
        padding: 0.12rem 0.32rem 0.12rem 0;
        align-items: center;
        .name {
          font-size: 32px;
          color: #262626;
        }

        .code {
          margin-top: 8px;
          font-size: 28px;
          color: #8c8c8c;
        }

        .left {
          width: 60px;
          height: 60px;
          border: 2px solid rgba(0, 0, 0, 0.45);
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .right {
          padding: 24px 0;
          border-bottom: 2px solid #e8e8e8;
          flex: 1;
          margin-left: 24px;
        }
      }
      .bottom_bar {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 100px;
        width: 100%;
        display: flex;
        color: var(--brand-base);
        z-index: 999;
        justify-content: center;
        font-size: 32px;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.9);
        box-shadow: 0 2px 32px 0 var(--brand-1);
        transform: translate3d(0, 0, 0);
        
      }
    }
    .bottom_bar {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 100px;
      width: 100%;
      display: flex;
      color: var(--brand-base);
      z-index: 999;
      justify-content: center;
      font-size: 32px;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2px 32px 0 var(--brand-1);
      transform: translate3d(0, 0, 0);
      &.privatecar-bottom-bar {
        padding: 8px 32px;
        > div {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid #3860E2;
          border-radius: 8px;
          color: #3860E2;
        }
      }
    }
  }
}
.dataLink-list-search-bar{
  width: 100%;
  height: 80px;
  margin: @space-4 0;
  padding: 0 @space-6;
  transition: all ease 0.3s;
  transform: translateZ(0);
}

.popup-modal {
  overflow: hidden;
  height: 85vh;
  display: flex;
  flex-direction: column;
  :global {
    .modal-title {
      display: flex;
      padding: @space-5 @space-6 @space-4;
      align-items: center;
      .cancel {
        .font-weight-2;
        .font-size-3;
        color: @color-black-3;
      }
      .title {
        flex: 1;
        .font-weight-3;
        .font-size-4;
        text-align: left;
        color: @color-black-1;
      }
    }
    .modal-title-tips {
      .font-weight-2;
      font-size: 24px;
      text-align: left;
      padding-left: @space-6;
      line-height: 32px;
      color: rgba(39, 46, 59, 0.48);
    }
  }
}

.thirdList_footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  flex-shrink: 0;
  z-index: 999;
  background-color: #ffffff;
  box-shadow: 0 4px 64px 0 var(--brand-1);
}

.thirdList_footer_select {
  height: 100%;
  :global {
    .am-checkbox-wrapper {
      margin-top: 5px;
    }

    .am-checkbox-agree .am-checkbox-agree-label {
      font-size: 36px;
    }

    .am-checkbox-agree .am-checkbox {
      top: 6px;
    }
    
  }
}

.thirdList_footer_btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 238px;
  height: 76px;
  border-radius: 8px;
  margin-right: 12px;
  font-size: 36px;
  color: #ffffff;
  background: var(--brand-base);
  img {
    margin-right: 16px;
    width: 32px;
    height: 32px;
  }
}
