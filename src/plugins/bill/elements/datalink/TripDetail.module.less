@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.trip-detail-wrapper {
  width: 100%;
  height: 100%;
  color: @color-black-2;
  display: flex;
  flex-direction: column;
  .font-size-2;
  .font-weight-2;
  text-align: left;
  :global {
    .status-disabled {
      padding: @space-4 @space-5;
      background: rgba(250, 150, 42, 0.1);
      border-radius: @radius-1;
      color: #47413a;
    }

    .green {
      color: #199a82 !important;
    }
    .red {
      color: #f4526b !important;
    }
    .orange {
      color: #ce8b40 !important;
    }
    .gray {
      color: rgba(153, 153, 153, 1);
    }
    .trip-warning {
      background: #fff;
      padding: 20px 40px 0;
      .tips {
        display: flex;
        color: #6c6c6c;
        justify-content: flex-start;
        font-size: 28px;
        border-radius: 8px;
        align-items: center;
        padding: 10px 20px;
        flex-direction: row;
        background-color: rgba(250, 150, 42, 0.1);
        border: solid 2px #fff0e2;
        img {
          width: 40px;
        }
        a {
          margin-left: @space-2;
          color: @color-brand;
        }
      }
    }
    .trip-bill {
      margin-bottom: @space-4;
      background: #fff;
      h5 {
        margin-bottom: @space-6;
        color: @color-black-3;
        .font-size-2;
      }
    }
    .em {
      color: @color-black-1;
      .font-size-3;
      .font-weight-3;
    }
    .mb-4 {
      margin-bottom: @space-2;
    }

    .trip-detail {
      margin: 24px;
      position: relative;
      padding: 34px 32px 32px;
      background: #ffffff;
      border-radius: 16px;

      .trip-detail-head {
        display: flex;
        align-items: center;

        .trip-type-txt {
          margin-left: 16px;
          font: var(--eui-font-body-b1);
          color: var(--eui-text-title);
        }

        .trip-icon {
          font-size: 44px;
        }

        .custom-icon {
          width: 40px;
          height: 40px;
        }
      }

      .trip-detail-content {
        margin-top: 14px;

        .trip-detail-content-city {
          font: var(--eui-font-body-b1);
          color: var(--eui-text-title);
        }

        .trip-detail-content-date {
          font: var(--eui-font-body-r1);
          color: var(--eui-text-title);
          margin-top: 12px;
        }
      }

      .bc {
        position: absolute;
        opacity: 0.2;
        width: 128px;
        bottom: 0;
        right: 50px;
      }
      .trip-card-new {
        margin: 30px;
        display: flex;

        .icon {
          width: 40px;
          height: 40px;
          margin-top: 6px;
        }
        .content-wrapper {
          margin-left: 20px;

          .city {
            color: #333333;
            font-size: 30px;
            font-weight: 600;
          }
          .date {
            color: #999999;
            font-size: 26px;
          }
        }
      }

      .am-button {
        width: 100%;
        height: 80px;
        margin: @space-6 0;
        .font-size-3;
        line-height: 80px;
        border-radius: @radius-2;
      }
    }

    .trip-order {
      padding: 24px 32px 32px;
      background: #ffffff;
      border-radius: 16px;
      .trip-order-title {
        font: var(--eui-font-head-b2);
        color: var(--eui-text-title);
        margin-bottom: 28px;
      }
    }

    .trip-order-item {
      margin-bottom: 16px;
      background: #f7f8fa;
      border-radius: 8px;
      padding: 16px 24px;
      position: relative;
      &:last-of-type {
        margin-bottom: 0px;
      }

      .trip-order-item-name {
        font: var(--eui-font-body-b1);
        color: var(--eui-text-title);
      }

      .trip-order-item-dept {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-placeholder);
        margin-top: 12px;
        width: 584px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .trip-order-item-icon {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        position: absolute;
        padding: 0 8px;
        height: 40px;
        right: 0px;
        top: 0px;
        border-radius: 0px 8px;
        font: var(--eui-font-note-r2);

        &.blue {
          color: var(--eui-function-info-600);
          background: var(--eui-function-info-100);
        }

        &.lightgreen {
          color: var(--eui-function-success-600);
          background: var(--eui-function-success-100);
        }

        &.lightgray {
          color: var(--eui-transparent-n900-60);
          background: var(--eui-transparent-n900-10);
        }
      }

      .order-information {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 12px;

        a {
          font: var(--eui-font-note-r2);
          color: var(--eui-primary-pri-500);
        }

        .order-information-num {
          font: var(--eui-font-note-r2);
          color: var(--eui-text-placeholder);
        }

        .order-information-detail {
          font: var(--eui-font-note-r2);
          color: var(--eui-primary-pri-500);
        }
      }

      .header {
        color: rgba(102, 102, 102, 0.6);
        a {
          color: @color-brand;
          margin-left: @space-4;
          text-decoration: none;
        }
      }
    }

    .trip-bill {
      border-radius: 16px;
      margin-top: 24px;
      padding: 24px 32px 32px;

      .trip-bill-title {
        font: var(--eui-font-head-b2);
        color: var(--eui-text-title);
        margin-bottom: 28px;
      }

      .trip-bill-item {
        background: #f7f8fa;
        border-radius: 8px;
        padding: 16px 24px;
        position: relative;

        .status-icon {
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
          align-items: center;
          padding: 2px 8px;
          position: absolute;
          height: 40px;
          right: 0px;
          top: 0px;
          border-radius: 0px 8px;
          font: var(--eui-font-note-r2);

          &.blue {
            color: var(--eui-function-info-600);
            background: var(--eui-function-info-100);
          }

          &.lightgreen {
            color: var(--eui-function-success-600);
            background: var(--eui-function-success-100);
          }

          &.red {
            color: var(--eui-function-danger-600);
            background: var(--eui-function-danger-100);
          }

          &.lightgray {
            color: var(--eui-transparent-n900-60);
            background: var(--eui-transparent-n900-10);
          }
        }

        .trip-bill-item-name {
          font: var(--eui-font-body-b1);
          color: var(--eui-text-title);
        }

        .trip-bill-item-content {
          display: flex;
          margin-top: 8px;
          align-items: center;
          justify-content: space-between;

          .trip-bill-item-child {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .trip-bill-item-dept {
              font: var(--eui-font-note-r2);
              color: var(--eui-text-placeholder);
              width: 212px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .trip-bill-item-code {
              font: var(--eui-font-note-r2);
              color: var(--eui-text-placeholder);
              margin-left: 8px;
            }
          }

          .trip-bill-money {
            .currency {
              font: var(--eui-font-note-b2);
              color: var(--eui-text-title);
            }

            .value {
              font: var(--eui-num-body-b1);
              color: var(--eui-text-title);
            }
          }
        }

        .document-name {
          font-weight: 500;
          font-size: 28px;
          line-height: 40px;
          color: rgba(29, 33, 41, 0.9);
        }
      }

      label {
        font-size: 20px;
        text-align: center;
        border-radius: @radius-2;
        color: #22b2cc;
        border: 2px solid #22b2cc;
        padding: 6px 12px;
      }

      .status::before {
        content: '';
        display: block;
        width: 16px;
        height: 16px;
        background: #30b4c2;
        border-radius: @radius-5;
        float: left;
        transform: translate(-12px, 14px);
      }
    }
    .other-info {
      flex: 1;
      padding: 0 24px;
    }
    .footer {
      padding: 16px 32px 32px;
      height: 184px;
      background-color: #ffffff;

      .text {
        margin-top: 16px;
        text-align: center;
        font: var(--eui-font-body-r1);
        color: var(--eui-text-placeholder);
      }

      .btn-confirm {
        width: 100%;
        height: 80px;
        border-radius: 8px;
      }
    }
  }
}
