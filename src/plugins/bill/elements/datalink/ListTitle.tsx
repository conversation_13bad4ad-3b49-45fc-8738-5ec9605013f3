import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import styles from './ListTitle.module.less'
import { isArray, isObject } from '@ekuaibao/helpers'
import { get } from 'lodash'
import { OutlinedDirectionRight, FilledGeneralCollect } from '@hose/eui-icons'
import { formatLinkText, getDataLinkAvatar } from '../../../../components/dynamic/inner/dataLinkUtil'
import { thousandBitSeparator } from '../../../../components/utils/fnThousandBitSeparator'
import { getDisplayName, getUserDisplayName } from "../../../../components/utils/fnDataLinkUtil";
const EkbIcon = app.require('@elements/ekbIcon')
 const AVATAR_NULL = app.require('@images/avatar.svg')
interface Props {
  data: any
  label?: any
  template: any
  cls?: any
  isShowAll: boolean
  allCurrencyList: any[]
  isShowDirRight?: boolean
}

interface State {
  isOver?: boolean
}
export default class ListTitle extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      isOver: false
    }
  }

  add0 = (m: string | number) => {
    return m < 10 ? '0' + m : m
  }
  format = (shijianchuo: string | number | Date, withTime: boolean) => {
    if (!shijianchuo) {
      return ''
    }
    const time = new Date(shijianchuo)
    const y = time.getFullYear()
    const m = time.getMonth() + 1
    const d = time.getDate()
    const h = time.getHours()
    const mm = time.getMinutes()
    const s = time.getSeconds()
    return withTime
      ? y + '-' + this.add0(m) + '-' + this.add0(d) + ' ' + this.add0(h) + ':' + this.add0(mm)
      : y + '-' + this.add0(m) + '-' + this.add0(d)
  }
  matchList = (fields: any[]) => {
    const { data } = this.props
    const myArr: any[] = []
    fields.forEach((item: any) => {
      const { source, name, unit, type, entity } = item
      let dsname = get(data, `${source}.${name}`)
      if (type === 'text') {
        const { textValue } = formatLinkText(dsname, name)
        dsname = textValue
      }
      if (
        !data ||
        !data[item.source] ||
        (!data[item.source][item.name] && item.name !== 'useCount' && type !== 'switcher')
      ) {
        myArr.push('')
      } else if (item.source === 'planned') {
        // 计划类型
        let name = item.name
          .split('_')
          .pop()
          .toUpperCase()
        let isOver = false
        if (name === 'PERCENTAGE') {
          myArr.push(`${data[item.source][item.name][name]}%`)
          if (data[item.source][item.name][name] > 100) {
            // 百分比大于100，执行额度超标
            isOver = true
          }
        } else {
          const standard = get(data, item.source + '.' + item.name + '.' + name + '.' + 'standard')
          const foreign = get(data, item.source + '.' + item.name + '.' + name + '.' + 'foreign')
          const isForeign = !!foreign
          const moneyValue = isForeign ? foreign : standard
          // 余额小于0，执行额度超标
          isOver = moneyValue < 0 ? true : false
          if (item.type === 'money') {
            //金额字段有原币展示原币，没有原币展示本位币
            const moneyStrCode = isForeign ? dsname[name].foreignStrCode : dsname[name].standardStrCode
            // const { moneyStr, unit } = formateMoneyDataLink(moneyValue, moneyStrCode)
            myArr.push(`${thousandBitSeparator(moneyValue)} ${moneyStrCode}`)
          } else if (item.type === 'number') {
            myArr.push(unit ? `${Number(dsname[name])} ${unit}` : `${Number(dsname[name])}`)
          } else {
            myArr.push('')
          }
        }
        this.setState({ isOver }) // 请不要在 render 的时候调用 setState  ok? 还在循环里面调用?
      } else {
        if (item.type === 'text' || item.type === 'number' || item.type === 'code') {
          if (item.name === 'useCount') {
            item.source && item.name && myArr.push(`${data[item.source][item.name]}/${data[item.source].totalCount}`)
          } else {
            let text = source === 'ledger' ? Number(dsname) : dsname
            myArr.push(unit ? `${text} ${unit}` : `${text}`)
          }
        } else if (item.type === 'money') {
          if (item && item.source && item.name && data[item.source][item.name]) {
            //金额字段有原币展示原币，没有原币展示本位币
            const standardStrCode = data[item.source][item.name].standardStrCode
            const standard = data[item.source][item.name].standard

            const foreign = data[item.source][item.name].foreign
            const foreignStrCode = data[item.source][item.name].foreignStrCode
            const isForeign = !!foreign
            const moneyValue = isForeign ? foreign : standard

            // const { moneyStr } = formateMoneyDataLink(moneyValue, isForeign?foreignStrCode:standardStrCode)
            myArr.push(`${thousandBitSeparator(moneyValue)} ${isForeign ? foreignStrCode : standardStrCode}`)
          } else {
            myArr.push('')
          }
        } else if (item.type === 'date') {
          const myDate = this.format(data[item.source][item.name], item.withTime)
          item.source && item.name && myArr.push(myDate)
        } else if (item.type === 'switcher') {
          if (item.source && item.name) {
            let _active = data[item.source][item.name]
            if (_active === true) {
              myArr.push(i18n.get('启用中'))
            } else if (_active === false) {
              myArr.push(i18n.get('已停用'))
            }else{
              myArr.push(' ')
            }

          }
        } else if (item.type === 'ref') {
          const path = `${item.source}.${item.name}`
          const fieldData = get(data, path, '')
          if (entity === 'basedata.Enum.currency') {
            myArr.push(`${fieldData.name}(${fieldData.strCode})`)
          } else if (entity === 'organization.Staff') {
            myArr.push(getUserDisplayName(fieldData))
          } else {
            if (isObject(fieldData)) {
              const name = getDisplayName(fieldData)
              myArr.push(name)
            } else if (isArray(fieldData)) {
              myArr.push(fieldData.map((ele: any) => getDisplayName(ele)).join(','))
            }
          }
        } else {
          item.source && item.name && data[item.source][item.name]
            ? myArr.push(data[item.source][item.name])
            : myArr.push('')
        }
      }
    })
    return myArr
  }
  matchSupple = (fields: any[]) => {
    const myArr: any[] = []
    fields.forEach((tem: any) => {
      tem.label && myArr.push(tem.label)
    })
    return myArr
  }
  renderTitle = () => {
    const { template } = this.props
    if (template) {
      const list = template.title ? this.matchList(template.title.fields) : []
      return this.delUndefined(list)
    } else {
      return []
    }
  }
  renderDesc = () => {
    const { description } = this.props.template
    const list = description ? this.matchList(description.fields) : []
    return this.delUndefined(list)
      .map((item, index) => (item && index ? ` | ${item}` : item))
      .join('')
  }
  renderIfTop = () => {
    if (window.PLATFORMINFO?.dataTopFunctionActive) {
      const topDate = get(this.props, 'data.dataLink.topDate')
      return !!topDate && <span className={styles['list_is_top']}>{i18n.get('置顶')}</span>
    }
  }
  renderReplenish = () => {
    const { template } = this.props
    if (template) {
      const list = template.replenish ? this.matchList(template.replenish.fields) : []
      return this.delUndefined(list)
    } else {
      return []
    }
  }
  renderSupple = () => {
    const { replenish } = this.props.template
    const list = replenish ? this.matchSupple(replenish.fields) : []
    return this.delUndefined(list)
  }
  delUndefined = (arr: any[] = []): any[] => {
    return arr.map(item => {
      if (!item) return '-'
      return !!~item.indexOf('undefined') ? '-' : item
    })
  }

  renderAvatar = () => {
     const { template, data } = this.props
    const avatar = getDataLinkAvatar(template,data)
    return <img src={avatar || AVATAR_NULL} />
  }
  getStyle = () => {
    const { template } = this.props
    if (template) {
      const list = template.replenish ? this.matchList(template.replenish.fields) : []
      if (list.length > 0 && template.replenish?.fields?.[0]?.type === 'money') {
        const newList = this.delUndefined(list)
        const money = newList[0]
        if (money !== '-' && money?.split('.')?.[0].length > 7) {
          return { fontSize: 12 }
        }
      }
      return {}
    } else {
      return {}
    }
  }
  render() {
    const { isShowAll = false, isShowDirRight = false, data } = this.props
    const { isShowRemark, isShowPersonalOnLeft } = this.props.template
    const { isOver } = this.state
    const isPlanned = get(this.props.template, 'replenish.fields[0].source') === 'planned'
    const cls = isOver && isPlanned ? '#FC3842' : ''
    const isShowIcon = get(this.props, 'isShowIcon', false)
    const favoriteStatus = data?.dataLink?.favoriteStatus ?? false

    return (
      <div className={styles['list_title']}>
        {isShowPersonalOnLeft && this.renderAvatar()}
        <div className={styles['content-wrapper']}>
          <div className={styles.listname}>
            <div className={styles[`list_name_top${isShowAll ? '_show_all' : ''}`]}>
              {this.renderTitle()}
              {favoriteStatus && <FilledGeneralCollect className={styles['collect-icon']} />}
              {this.renderIfTop()}
            </div>
            <div className={styles['list_name_bottom']}>{this.renderDesc()} </div>
          </div>
          <div className={styles.describe}>
            <div>
              <div style={{ color: cls, ...this.getStyle() }}>{this.renderReplenish()}</div>
              <div>{isShowRemark && this.renderSupple()}</div>
            </div>
            {!isShowIcon && !isShowDirRight && (
              <div className={styles.imgbox}>
                <EkbIcon name="#EDico-info-circle-o" />
              </div>
            )}
            {isShowDirRight && (
              <span className={styles['dirLeftIcon']}>
                <OutlinedDirectionRight />
              </span>)}
          </div>
        </div>
      </div>
    )
  }
}
