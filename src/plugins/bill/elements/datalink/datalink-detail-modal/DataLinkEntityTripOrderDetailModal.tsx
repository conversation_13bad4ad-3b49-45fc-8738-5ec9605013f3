import React, { PureComponent } from 'react'
import <PERSON>hanceT<PERSON>le<PERSON><PERSON> from '../../../../../lib/EnhanceTitleHook'
import TripOrder from './elements/TripOrder'

interface Props {
  field: any
  value: any
  title?: string
}

@EnhanceTitleHook(props => props.title)
export default class DataLinkEntityTripOrderDetailModal extends PureComponent<Props, {}> {
  render() {
    return <TripOrder {...this.props} />
  }
}
