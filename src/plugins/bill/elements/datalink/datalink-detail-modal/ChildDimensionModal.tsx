import styles from './ChildDimensionModal.module.less'
import React, { PureComponent } from 'react'
import classnames from 'classnames'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import DataLinkProgress from '../DataLinkProgress'
import { PlanInterface, PlanColumn } from './types'
import DataLinkPlanTable from './DataLinkPlanTable'

interface PropsType {
  plans: PlanInterface[]
  columns: PlanColumn[]
  index: number
  mode: string
  layer?: any
}

interface StateType {
  index: number
}

@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].me_info.staff,
  pathMap: state['@common'].departments.pathMap
}))
export default class ChildDimensionModal extends PureComponent<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props)
    this.state = {
      index: props.index || 0
    }
  }

  prevTitle: any = api.invokeService('@layout:get:header:title')

  componentDidMount() {
    api.invokeService('@layout:set:header:icon', { showIcon: false })
    api.invokeService('@layout:change:header:title', i18n.get('查看子维度数据'))
  }

  componentWillUnmount() {
    api.invokeService('@layout:set:header:icon', { showIcon: true })
    api.invokeService('@layout:change:header:title', this.prevTitle)
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleChange(index: number, max: number) {
    if (index === max) return null
    return () => this.setState({ index })
  }

  render() {
    const { mode, plans, columns } = this.props
    const { index } = this.state
    const plan = plans[index]

    const handlePrev = this.handleChange(index - 1, -1)
    const handleNext = this.handleChange(index + 1, plans.length)

    return (
      <div className="modal-popup-content">
        <div className="modal-action-bar">
          <div className={classnames('modal-action-bar-btn', { light: handlePrev })} onClick={handlePrev}>
            {i18n.get('上一条')}
          </div>
          <span className="modal-action-bar-title">
            {i18n.get('查看子维度数据')}
            <span className="pl-10">
              {index + 1}
              {i18n.get('／')}
              {plans.length}
            </span>
          </span>
          <div className={classnames('modal-action-bar-btn', { light: handleNext })} onClick={handleNext}>
            {i18n.get('下一条')}
          </div>
        </div>
        <div className={styles['child-dimension']}>
          {mode === 'progress' ? (
            <>
              <DataLinkProgress planned={plan} />
              <div className="progress-list">
                {plan.childPlanNode.map((sub, key) => (
                  <DataLinkProgress key={key} planned={{ ...sub, showForm: plan.showForm }} />
                ))}
              </div>
            </>
          ) : (
            <>
              <div className="table-title">{plan.name}</div>
              <div className="table-list">
                <DataLinkPlanTable key={index} plans={plan.childPlanNode} columns={columns} />
              </div>
            </>
          )}
        </div>
      </div>
    )
  }
}
