import './DataLinkEntityDetail.less'
import React, { PureComponent } from 'react'
import { app, app as api } from '@ekuaibao/whispered'
import { Dynamic } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { isArray, isObject } from '@ekuaibao/helpers'
import createDOMForm from 'rc-form/lib/createDOMForm'
import DataLinkText from './elements/DataLinkText'
import DataLinkAutoNumber from './elements/DataLinkAutoNumber'
import { formateMoneyDataLink } from '../../../utils/CheckFieldPriority'
import DataLinkProgress from '../DataLinkProgress'
import DataLinkPlanTable from './DataLinkPlanTable'
import LedgerRelationList from './LedgerRelationList'
import { getEntityWithTemplate } from '../../../bill.action'
import { formatFieldShowByType } from '../../../utils/dataLinkUtils'
import {
  DataLinkEntityModel,
  DetailTemplateModel,
  MoneyFieldModel,
  TemplateDataResponse,
  TemplateKind,
  PlanInterface,
  PlanColumn
} from './types'
import { get, orderBy } from 'lodash'
import { Fetch } from '@ekuaibao/fetch'
import { getStaffShowByConfig } from '../../../../../components/utils/fnDataLinkUtil'
import { Button, Toast, ActionPanel, Dialog } from '@hose/eui-mobile'
import type { Action } from '@hose/eui-mobile/2x/es/components/action-panel'
import { emitEasoPipeline } from '../server'
import { FilledGeneralCollect, OutlinedGeneralCollection } from '@hose/eui-icons'

const EKBIcon = app.require<any>('@elements/ekbIcon')
const elements = app.require<any>('@components/dynamic/index.readonly.datalink.lite')
export const ExcludeCols = [
  'visibility',
  'ownerId',
  'active',
  '_rErrorMsg',
  '_reconciliationFlowId',
  '_settlementFlowId',
  '_supplierId',
  '_eTypeId',
  '_rMissingField',
  '_rSituation', // 对账状态
  '_rResult', // 对账结果
  '_rWay', // 对账方式
  '_payType', // 结算方式
  '_reconciliationId'
]

export interface DataLinkEntityDetailProps {
  globalFieldsMap: any
  staffMap: any
  value: any
  field: any
  showEdit: boolean
  bus: any
  isSupplier: any
  userInfo: any
  hasCollect?: boolean
}

export interface DataLinkEntityDetailState {
  data: TemplateDataResponse<DetailTemplateModel> | undefined
  value: Record<string, any> | undefined
  reverse: boolean
  moreBtnVisible: boolean
}

@EnhanceConnect(
  (state: any): Partial<DataLinkEntityDetailProps> => ({
    globalFieldsMap: state['@common'].baseDataProperties.baseDataPropertiesMap,
    staffMap: (state['@common'].staffs || []).reduce((map: any, item: any) => ((map[item.id] = item) || 1) && map, {}),
    userInfo: state['@common'].me_info.staff
  })
)
export default class DataLinkEntityDetail extends PureComponent<DataLinkEntityDetailProps, DataLinkEntityDetailState> {
  static defaultProps: DataLinkEntityDetailProps

  constructor(props: DataLinkEntityDetailProps) {
    super(props)
    this.state = { data: void 0, value: void 0, reverse: false, moreBtnVisible: false }
  }

  async componentDidMount() {
    const data: TemplateDataResponse<DetailTemplateModel> = (
      await getEntityWithTemplate(
        this.props.value.id ? this.props.value.id : this.props.value.data.dataLink.id,
        TemplateKind.DETAIL
      )
    ).value
    const value: any = {}
    const entity = data.data

    if (entity.dataLink.hasOwnProperty('useCount')) {
      entity.dataLink.useCount = `${entity.dataLink.useCount}/${entity.dataLink.totalCount}`
    }

    // 供应商类型的value需要特殊处理一下
    const periodMap = {
      WEEK: '周',
      BIWEEKLY: '双周',
      MONTH: '月',
      SEASON: '季度',
      HALFYEAR: '半年',
      YEAR: '年',
      IRREGULAR: '不定期'
    }
    const SettleState = {
      WAITING: i18n.get('待结算'),
      RUNING: i18n.get('结算中'),
      FINISH: i18n.get('结算完成')
    }
    const settlementPeriod = Object.keys(entity.dataLink).find(field => /_settlementPeriod$/.test(field))
    const settlementOpportunity = Object.keys(entity.dataLink).find(field => /_settlementOpportunity$/.test(field))
    if (settlementPeriod) {
      const vv: string = entity.dataLink[settlementPeriod] as string
      entity.dataLink[settlementOpportunity] = i18n.get(`按${(periodMap as any)[vv]}结算`)
    }
    for (const key in entity) {
      if (entity.hasOwnProperty(key) && (entity as any)[key]) {
        for (const field in (entity as any)[key]) {
          if (/_sSituation$/.test(field)) {
            value[`${key}.${field}`] = (SettleState as any)[(entity as any)[key][field]]
          } else if ((entity as any)[key].hasOwnProperty(field)) {
            value[`${key}.${field}`] = (entity as any)[key][field]
          }
        }
      }
    }
    this.setState({ data, value })
  }

  private value(
    field: { source: 'dataLink' | 'planned' | 'ledger'; field: string; type: string },
    data: DataLinkEntityModel
  ) {
    let value: any = data[field.source][field.field]
    if (field.type === 'ref') {
      let result = '-'
      if (isObject(value)) {
        if (field?.entity === 'organization.Staff') {
          result = getStaffShowByConfig(value) || '-'
        } else {
          result = value.name
        }
      } else if (isArray(value)) {
        result = value.map((item: any) => item.name).join(',')
      }
      return result
    }
    if (field.source === 'dataLink' && field.field === 'active') {
      return value ? (
        <span className="color-blue">{i18n.get('启用中')}</span>
      ) : (
        <span className="color-red">{i18n.get('已停用')}</span>
      )
    }
    if (field.source === 'planned') {
      value =
        value[
          field.field
            .split('_')
            .pop()
            .toUpperCase()
        ]
    }
    if (field.type !== 'money') {
      return formatFieldShowByType(field, value)
    }
    if (!value) {
      return '-'
    }
    if (typeof value === 'string') {
      value = { standard: value, standardSymbol: i18n.get('￥'), standardStrCode: 'CNY', standardScale: 2 }
    }
    const { standard, standardStrCode, foreign, foreignStrCode } = (value || {}) as MoneyFieldModel
    const { moneyStr, money } = formateMoneyDataLink(Number(standard), standardStrCode)
    const foreignMoneyStr = foreign ? formateMoneyDataLink(Number(foreign), foreignStrCode)?.moneyStr : ''
    return (
      <div
        className={`money ${money.lt(0) ? 'color-red' : ''}`}
        style={{ textOverflow: 'ellipsis', overflow: 'hidden' }}
      >
        {foreign && <span className="value_money_foreign">{foreignMoneyStr}</span>}
        <span className={`${foreign ? 'value_money_standard' : 'value_money_foreign'}`}>{moneyStr}</span>
      </div>
    )
  }

  private create(T: any) {
    return createDOMForm({
      onValuesChange() {}
    })(T)
  }

  private handleToggle = () => {
    this.setState({ reverse: !this.state.reverse })
  }

  showDimension(plans: PlanInterface[], columns: PlanColumn[], index: number, mode: string) {
    const plan = get(plans, `${index}.childPlanNode.length`)

    if (!plan) {
      return null
    }

    return () => {
      app.open('@bill:ChildDimensionModal', { plans, columns, index, mode })
    }
  }

  get isOwner() {
    const { userInfo } = this.props
    const {
      data: {
        data: { dataLink }
      }
    } = this.state

    return dataLink.ownerId?.id === userInfo?.id
  }

  handleEdit = async () => {
    const { value } = this.props
    const { data: dataLink } = value
    const id = dataLink.ownerId?.id

    const selectedStaff = await app.invokeService('@layout:select:staff', {
      defValue: id,
      isVisibilityStaffs: false
    })
    const { id: staffId } = selectedStaff
    await Fetch.PUT(`/api/v1/datalink/$owner/$${dataLink.id}/updateCategory`, null, { body: { staffIds: [staffId] } })
    history.go(-1)
  }

  handleEmit = async (pipelineId: string, id: string, name?: string) => {
    const confirm = await Dialog.confirm({
      content: `请问确定要执行"${name ?? ''}"吗？`
    })

    if (!confirm) {
      return
    }

    const res = await emitEasoPipeline(pipelineId, id)
    if (res?.code === 200) {
      Toast.show({ icon: 'success', content: '执行成功' })
    } else {
      Toast.show({ icon: 'fail', content: res?.msg || '执行失败' })
    }
  }

  renderFooter() {
    const { showCustomizeAction } = this.props
    const { data, moreBtnVisible } = this.state

    const { actions = [], dataLink } = data?.data || {}

    const id = (dataLink?.id as string) || ''

    if (!showCustomizeAction) {
      return null
    }

    let customActions = actions
      .filter((item: any) => item.type === 'PIPELINE_ACTION')
      .filter((item: any) => item?.active)
      .filter(item => item?.allowedLayout?.includes('PAGE'))

    if (customActions.length === 0) {
      return null
    }

    customActions = orderBy(customActions, ['order', 'createTime', 'id'], ['desc', 'asc', 'asc'])

    if (customActions.length > 2) {
      const [first, second, ...rest] = customActions
      const panelActions: Action[] = rest.map((item: any) => ({
        text: item.name,
        key: item.id,
        onClick: async () => {
          await this.handleEmit(item?.context?.pipelineId, id, item.name)
        }
      }))

      return (
        <>
          <div className="datalink-entity-detail-footer">
            <Button
              category="secondary"
              size="large"
              onClick={() => {
                this.setState({ moreBtnVisible: true })
              }}
            >
              ···
            </Button>
            <div>
              <Button
                category="secondary"
                size="large"
                block
                style={{ width: '100%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                onClick={async () => {
                  await this.handleEmit(first?.context?.pipelineId, id, first.name)
                }}
              >
                {first.name}
              </Button>
            </div>
            <div>
              <Button
                style={{ width: '100%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                block
                category="secondary"
                size="large"
                onClick={async () => {
                  await this.handleEmit(second?.context?.pipelineId, id, second.name)
                }}
              >
                {second.name}
              </Button>
            </div>
          </div>
          <ActionPanel
            cancelText="取消"
            visible={moreBtnVisible}
            actions={panelActions}
            onClose={() => this.setState({ moreBtnVisible: false })}
          />
        </>
      )
    }

    return (
      <div className="datalink-entity-detail-footer">
        {customActions.map((item: any) => (
          <div key={item?.id}>
            <Button
              size="large"
              category="secondary"
              block
              onClick={async () => {
                await this.handleEmit(item?.context?.pipelineId, id, item.name)
              }}
            >
              {item.name}
            </Button>
          </div>
        ))}
      </div>
    )
  }

  handleCollect = async () => {
    const { value } = this.state
    const dataLinkId = value['dataLink.id'] ?? ''
    const enable = !value['dataLink.favoriteStatus'] ?? false

    if (!dataLinkId) return

    try {
      const res = await Fetch.PUT(`/api/v2/datalink/favorites/$${dataLinkId}/$${enable}`)
      if (res && res.value) {
        this.setState({
          value: { ...this.state.value, ['dataLink.favoriteStatus']: enable }
        })
        api.emit('update:dataLinkListOfCollect', true)
        return Toast.show({ content: enable ? i18n.get('收藏成功') : i18n.get('收藏已取消') })
      }
    } catch (e) {
      console.log(e)
    }
  }

  renderCollectItem() {
    const { value } = this.state
    const status = value['dataLink.favoriteStatus'] ?? false

    return (
      <div className="collect-item">
        <div className="btn" onClick={this.handleCollect}>
          {status ? (
            <FilledGeneralCollect className="collected" />
          ) : (
            <OutlinedGeneralCollection className="unCollect" />
          )}
          <span className="status">{status ? i18n.get('已收藏') : i18n.get('收藏')}</span>
        </div>
      </div>
    )
  }

  render() {
    const { data, reverse } = this.state
    if (!data) {
      return null
    }
    const {
      data: entity,
      template: {
        content: { expansion }
      }
    } = data
    const {
      value: { id },
      showEdit,
      bus,
      globalFieldsMap,
      staffMap,
      value,
      isSupplier,
      hasCollect = false
    } = this.props

    const isShowTip = entity.plans.find(v => v.childPlanNode && v.childPlanNode.length)

    return (
      <div className="datalink-entity-detail-modal">
        <div className="dataLink-content">
          {expansion.components.map((section, index) => {
            switch (section.type) {
              case 'panel':
                return section.components.length > 0 && !isSupplier ? (
                  <div className="module panel" key={index}>
                    {section.components.map((field, index) => (
                      <div className={'item ' + field.type} key={index}>
                        <div className="value">
                          {this.value(field, entity)}
                          {field.type === 'ref' && this.isOwner && <a onClick={this.handleEdit}>编辑</a>}
                        </div>
                        <div className="label">{field.label.value}</div>
                      </div>
                    ))}
                  </div>
                ) : null
              case 'section':
                const components = isSupplier
                  ? section.components.filter(el => !~ExcludeCols.findIndex(key => el.field.endsWith(key)))
                  : section.components
                return components.length > 0 ? (
                  <div className="module section" key={index}>
                    <div className="title" onClick={this.handleToggle}>
                      <span>{i18n.get('基本信息')}</span>
                      <EKBIcon className={`toggle ${reverse ? 'reverse' : ''}`} name="#EDico-up-default" />
                    </div>
                    <div className={`content ${reverse ? 'hide' : ''}`}>
                      <Dynamic
                        className="dynamic"
                        value={this.state.value}
                        create={this.create}
                        emptyPlaceholder="-"
                        isDataLinkDetail={true}
                        datalinkId={id}
                        showEdit={showEdit}
                        staffMap={staffMap}
                        bus={bus}
                        entity={entity}
                        planned={entity.planned}
                        elements={[DataLinkText, DataLinkAutoNumber, ...elements] as any[]}
                        template={components.map(component => {
                          let type = component.type
                          let dataType = {}
                          if (component.type === 'settlementOpportunity') {
                            type = 'text'
                          }
                          if (component.type === 'ref') {
                            type = 'newAutoNumber'
                          }
                          if (component.type === 'ref' && typeof component.entity === 'string') {
                            if (component.entity.indexOf('pay.PayeeInfo') === 0) {
                              type = 'payeeInfo'
                            } else if (component.entity.indexOf('datalink.DataLinkEntity') === 0) {
                              type = 'dataLink'
                            } else if (
                              component.entity.indexOf('basedata.Dimension') === 0 ||
                              component.entity.indexOf('basedata.Enum') === 0
                            ) {
                              type = 'dimension'
                            } else if (component.entity.indexOf('basedata.city') === 0) {
                              type = 'city'
                            } else if (component.entity.indexOf('organization.Staff') === 0) {
                              type = 'Staff'
                            } else if (component.entity.indexOf('basedata.Enum.currency') === 0) {
                              type = 'Currency'
                            }
                          }
                          if (component.type === 'list') {
 
                            if (get(component, 'elemType.type') === 'attachment') {
                              type = 'attachments'
                              dataType = { type: 'list', elemType: { ...component.elemType } }
                            } else if (get(component, 'elemType.entity') === 'pay.PayeeInfo') {
                              type = 'list:ref:pay.PayeeInfo'
                            } else if (get(component, 'elemType.entity')?.indexOf('datalink.DataLinkEntity') === 0) {
                              type = 'dataLinks'
                            }
                          }
                          return {
                            ...component,
                            name: `${component.source}.${component.field}`,
                            label: component.label.value,
                            isOwner: component.field === 'ownerId',
                            type,
                            dataType
                          }
                        })}
                      />
                    </div>
                  </div>
                ) : null
              case 'table':
              case 'progress':
                return entity.plans.length > 0 ? (
                  section.show === 'TABLE' ? (
                    <div className="module plan table" key={index}>
                      <div className="title">
                        <span>{i18n.get('执行计划')}</span>
                      </div>
                      <div className="content">
                        <DataLinkPlanTable
                          plans={entity.plans}
                          columns={section.columns}
                          showDimension={this.showDimension}
                        />
                      </div>
                      {isShowTip && <p className="tips">{i18n.get('* 点击高亮行，可查看其子维度数据')}</p>}
                    </div>
                  ) : (
                    <div className="module plan progress" key={index}>
                      <div className="title">
                        <span>{i18n.get('执行计划')}</span>
                      </div>
                      {entity.plans.map((plan, index) => {
                        plan.showForm = section.columns.length > 1 ? 'ALL' : section.columns[0]
                        return (
                          <DataLinkProgress
                            key={index}
                            onClick={this.showDimension(entity.plans, section.columns, index, 'progress')}
                            planned={plan}
                          />
                        )
                      })}
                    </div>
                  )
                ) : null
              case 'ledger':
                return entity.ledgers.length > 0 ? (
                  <LedgerRelationList
                    {...this.props}
                    key={index}
                    ledgerList={entity.ledgers}
                    value={value}
                    globalFieldsMap={globalFieldsMap}
                  />
                ) : null
              default:
                return null
            }
          })}
        </div>
        {hasCollect && this.renderCollectItem()}
        {this.renderFooter()}
      </div>
    )
  }
}
