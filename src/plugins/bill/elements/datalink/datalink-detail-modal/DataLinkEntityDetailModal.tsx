import React, { PureComponent } from 'react'
import <PERSON>hanceT<PERSON>leH<PERSON> from '../../../../../lib/EnhanceTitleHook'
import DataLinkEntityDetail from './DataLinkEntityDetail'

interface Props {
  field: any
  value: any
  title?: string
}

// @ts-ignore
@EnhanceTitleHook(props => props.title)
export default class DataLinkEntityDetailModal extends PureComponent<Props, {}> {
  render() {
    return <DataLinkEntityDetail {...this.props} />
  }
}
