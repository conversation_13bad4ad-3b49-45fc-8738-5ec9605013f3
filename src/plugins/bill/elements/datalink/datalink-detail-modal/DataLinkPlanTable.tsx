import styles from './DataLinkPlanTable.module.less'
import React from 'react'
import classnames from 'classnames'
import { app } from '@ekuaibao/whispered'
const Money = app.require<any>('@elements/puppet/MoneyDataLink')
import { PlanInterface, PlanColumn, PlanFieldLabels } from './types'
import { get, isString } from 'lodash'
import { isZhongYing } from '@ekuaibao/lib/lib/checkZY'
import { Fetch } from '@ekuaibao/fetch'

export interface Props {
  plans: PlanInterface[]
  columns: PlanColumn[]
  showDimension?: any
}

export default function DataLinkPlanTable(props: Props) {
  const { plans, columns, showDimension } = props

  return (
    <table className={styles['plan-table']}>
      <thead>
        <tr>
          <th>{i18n.get('计划名称')}</th>
          {columns.map((item, index) => (
            <th key={index}>{PlanFieldLabels[item]}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {plans.map((plan, index) => (
          <tr key={plan.id + index} onClick={showDimension && showDimension(plans, columns, index, 'table')}>
            <td className={classnames({ light: get(plan, 'childPlanNode.length') })}>
              {showDimension ? plan.name : plan.childTypeName}
            </td>
            {columns.map((item, index) => (
              <td key={index}>
                {item === 'PERCENTAGE' ? (
                  <span className={classnames({ over: Number(plan.PERCENTAGE) > 100 })}>{plan.PERCENTAGE}%</span>
                ) : (
                  isString(plan[item]) ? plan[item] : renderMoney(plan[item])
                )}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  )
}

function renderMoney(value:any) {
  if (window.IS_SMG || isZhongYing(Fetch.ekbCorpId)) {
    const isSmgMoney = Number(value.standard) >= 99999999999.0 //SMG（11位9）
    const isZYMoney = Number(value.standard) >= 9999999999.0 //中影企业（10位9）
    if (isSmgMoney || isZYMoney) {
      return '-'
    } else {
      return <Money value={value} valueSize={14} showSymbol={false} />
    }
  }
  return <Money value={value} />
}
