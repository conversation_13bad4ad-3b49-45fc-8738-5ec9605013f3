import styles from './AddDataLink.module.less'
import React, { PureComponent } from 'react'
import { Dialog, Button } from '@hose/eui-mobile'
import DataLinkEditItem from '../DataLinkEditItem'
import DataLinkEditItemReadOnly from '../DataLinkEditItemReadOnly'
import <PERSON>han<PERSON><PERSON><PERSON>leHook from '../../../../../lib/EnhanceTitleHook'
import MessageCenter from '@ekuaibao/messagecenter/esm/index'
import cloneDeep from 'lodash/cloneDeep'

@EnhanceTitleHook(props => `${props.field.labelCopy}`)
export default class AddDataLinkModal extends PureComponent {
  constructor(props) {
    super(props)
    this.flowBus = props.bus
    const { updateNextSource = {} } = props
    this.state = {
      currentValue: props.value,
      bus: new MessageCenter(),
      error: '',
      currentIndex: updateNextSource?.currentIndex ?? -1
    }
  }

  checkSettleAmount = async data => {
    const { currentValue } = this.state
    const { field, entityFormList, allValueList = [] } = this.props
    const entityType = entityFormList.find(item => item.templateId === currentValue.dataLinkTemplateId)
    if (field?.name === 'E_system_payment_plan_write') {
      const parentId = entityType?.entity?.parentId
      const currentSettleAmount = data[`E_${parentId}_settleAmount`]
      const flowValue = await this.flowBus.getValue()
      const contractAmount = flowValue.contractAmount
      const settleAmountTotal =
        allValueList.reduce((pre, curr) => {
          return pre + Number(curr.dataLinkForm[`E_${parentId}_settleAmount`]?.standard) || 0
        }, 0) + Number(currentSettleAmount?.standard) || 0
      if (contractAmount && settleAmountTotal > Number(contractAmount.standard || 0)) {
        this.flowBus.$_settleAmountTotal = settleAmountTotal
        return false
      }
      return true
    }
    return true
  }

  handleModalUpdateToNext = () => {
    const { currentIndex } = this.state
    const { dataList } = this.props.updateNextSource
    const nextData = dataList?.[currentIndex + 1]
    if (!nextData) {
      return
    }

    this.handleSubmit((data) => {
      this.props.updateNextSource.updateCallback(data, currentIndex)
      this.setState(prevState => ({
        currentIndex: prevState.currentIndex + 1,
        currentValue: nextData
      }))
    }, true)
  }


  handleSubmit = (callback, isUpdateNext = false) => {
    const { type } = this.props
    const { currentIndex } = this.state
    if (type === 'view') {
      this.props.layer.emitOk('back')
    } else {
      const { bus, currentValue } = this.state
      if (!currentValue) {
        this.setState({ error: i18n.get('不可为空') })
        return
      }
      bus.getValueWithValidate(1).then(async res => {
        const checked = await this.checkSettleAmount(res)
        if (!checked) {
          Dialog.alert({ content: i18n.get('结算总额不能大于合同金额') })
          return
        }
        let { currentValue } = this.state
        const { type } = this.props
        currentValue.dataLinkForm = res
        if (type === 'edit') {
          if (typeof callback === 'function') {
            callback(currentValue)
          } else {
            const value = isUpdateNext ? {...currentValue, currentIndex} : currentValue
            this.props.layer.emitOk(value)
          }
        } else {
          this.props.layer.emitOk(cloneDeep(currentValue))
        }
      })
    }
  }
  handleAdd = () => {
    const { bus, currentValue } = this.state
    if (!currentValue) {
      this.setState({ error: i18n.get('不可为空') })
      return
    }
    bus.getValueWithValidate(1).then(async res => {
      const checked = await this.checkSettleAmount(res)
      if (!checked) {
        Dialog.alert({ content: i18n.get('结算总额不能大于合同金额') })
        return
      }
      let { currentValue } = this.state
      let { onChange } = this.props
      currentValue.dataLinkForm = res
      onChange && onChange(cloneDeep(currentValue))
      this.setState({ currentValue: undefined, error: '' })
      console.log(this.ref, 'this.ref')
      bus.resetFields()
      this.ref && this.ref.caculateFormValue?.()
    })
  }
  updateCurrentForm = value => {
    this.setState({ currentValue: value, error: '' })
  }
  render() {
    const { field, type } = this.props
    const { labelCopy, importMode } = field
    const { currentValue, bus, error, currentIndex } = this.state
    const nextData = this.props?.updateNextSource?.dataList?.[currentIndex + 1]
    return (
      <div className={styles['add-datalink-wrapper']}>
        <div className="content-wrapper">
          <div className="label-style">{labelCopy}</div>
          {type === 'view' ? (
            <DataLinkEditItemReadOnly {...this.props} bus={bus} error={error} value={currentValue} />
          ) : (
            <DataLinkEditItem
              ref={ref => { this.ref = ref }}
              {...this.props}
              bus={bus}
              error={error}
              value={currentValue}
              updateCurrentForm={this.updateCurrentForm}
            />
          )}
        </div>
        <div className="footer-wrapper">
          {type !== 'edit' && type !== 'view' && importMode !== 'SINGLE' && (
            <Button className="button-wrapper"  category="secondary"  size='middle' onClick={this.handleAdd}>
              {i18n.get('继续添加')}
            </Button>
          )}
          {type === 'edit' && nextData && <Button category="secondary" size='middle' className="button-wrapper" onClick={this.handleModalUpdateToNext}>
            {i18n.get('修改下一条')}
          </Button>}
          <Button className="button-wrapper" category="primary"  size='middle' onClick={this.handleSubmit}>
            {i18n.get('保存')}
          </Button>
        </div>
      </div>
    )
  }
}
