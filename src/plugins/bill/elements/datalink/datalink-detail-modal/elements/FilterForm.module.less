@import '~@eku<PERSON>bao/eui-styles/less/token-mobile.less';

.datalink-filter-form-wrapper {
  text-align: left;
  overflow: auto;
  padding-bottom: 200px;
  :global {
    .datalink-filter-form-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 24px;
      .datalink-filter-form-item-label {
        margin-left: 30px;
        font-weight: 400;
        margin-bottom: 8px;
        font-size: 28px;
        color: rgba(39, 46, 59, 0.96);
      }
      .datalink-filter-form-item-money{
        display: flex;
        align-items: center;
      }
      .am-list-line {
        padding-left: 30px;
        border: 2px solid rgba(0, 0, 0, 0.15) !important;
        border-radius: 8px;
        margin-right: 30px;
      }
      .datalink-filter-form-item-date{
        padding: 0 30px;
        border: 2px solid rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        margin-right: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 0.9rem;
        margin-left: 0.3rem;
      }
    }
    .datalink-filter-form-bottom{
      display: flex;
      flex-direction: row;
      bottom: 0;
      position: fixed;
      width: 100%;
      background-color: white;
      box-shadow: 0px -1px 4px rgba(0, 0, 0, 0.06);
      padding: 20px 30px;
      .datalink-filter-form-button-reset{
        width: 160px;
        margin-right: 24px;
      }
    }
  }
}
