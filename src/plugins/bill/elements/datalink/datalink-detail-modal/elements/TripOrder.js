// @i18n-ignore-all
import React, { Component } from 'react'
import { get, forIn } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import style from './TripOrder.module.less'
const EKBIcon = api.require('@elements/ekbIcon')
import TripCard from './TripCard'
import TripOrderCard from './TripOrderCard'
import { getId } from '../../../../utils/dataLinkUtils'
import { Button } from 'antd-mobile'
import { Fetch } from '@ekuaibao/fetch'

const SVG_WARNING = api.require('@images/invoice-payerno-warning.svg')
const { getCityAndDate } = api.require('@bill/elements/datalink/dataLinkUtil')
export default class TripOrder extends Component {
  constructor(props) {
    super(props)
    const dataLink = get(this.props, 'value.data.dataLink')
    const type = get(dataLink, 'entity.type', '')
    this.state = {
      orderInfo: (dataLink && this.getData(dataLink)) || null,
      tripInfo: undefined,
      tripType: type,
      tripOrderAll: []
    }
  }

  async componentWillMount() {
    this.getTripInfo()
    const dataLink = get(this.props, 'value.data.dataLink')
    // 查询退改签订单cardList，订单列表页获取业务对象比较卡，就没有放在那边 props传递了...
    const { items } = await api.invokeService('@bill:get:original:order:no', {
      dataLinkId: dataLink.id
    })
    const tripOrderAll = this.constructionTripOrderAll(items)
    this.setState({ tripOrderAll })
  }

  constructionTripOrderAll = (data) => {
    let result = data.map(e => {
      e = Object.assign(e, e.form)
      delete e.form
      return e
    })
    return result
  }

  getData = dataLink => {
    let data = {}
    forIn(dataLink, (value, key) => {
      const lastIndex = key.lastIndexOf('_')
      data[lastIndex > -1 ? key.substring(lastIndex + 1) : key] = value
    })
    return data
  }

  getTripInfo = async () => {
    const { orderInfo, tripType } = this.state
    const trip = orderInfo['关联行程']
    if (!trip) return
    const tripDataLink = get(trip, 'data.dataLink')
    const customTripType = await api.invokeService('@bill:get:travel:management')
    const customTripTypeList = customTripType?.items || []
    const dataLinkId = get(trip, 'data.dataLink.entity.id', '')
    const customTrip = customTripTypeList.find(item => item.entityId === dataLinkId)
    const { fromCity, toCity, startDate, endDate, name } = getCityAndDate({ dataLinkForm: tripDataLink }, customTrip)
    const tripInfo = {
      startTime: startDate,
      endTime: endDate,
      startCity: fromCity,
      endCity: toCity,
      name,
      tripType
    }
    this.setState({
      tripInfo,
      customTrip
    })
  }

  handleTripDetail = () => {
    const { field } = this.props
    const data = this.state.orderInfo['关联行程']
    api.open('@bill:DataLinkEntityDetailModal', { field, value: { id: getId(data) }, title: i18n.get('行程详情') })
  }

  handleOpenMall = async () => {
    const dataLink = get(this.props, 'value.data.dataLink')
    const parentId = get(dataLink, 'entity.parentId')
    const currentPlatform = get(dataLink, `E_${parentId}_订票平台`, '')
    const { tripType } = this.state
    const applyId = ''
    const tripId = dataLink.id || ''
    const white = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI', 'FOOD']
    if (!white.includes(tripType)) {
      return
    }
    const TK = tripType ? `BUTTON_${tripType}_ORDER` : 'BUTTON_TRAVEL_ORDER'
    const result = await Promise.all([
      api.invokeService('@mall:get:travel:intent', { type: TK }),
      api.invokeService('@mall:get:travel:intent:jwt', { type: TK })
    ])
    const items = result[0] && result[0].items ? result[0].items : []
    const token = result[1] ? result[1].id : ''
    const config = applyId ? await api.invokeService('@bill:get:getFlowDetailInfo', applyId) : {}
    const tripPlatform = get(config, 'value.form.specificationId.configs', []).find(
      (i) => i.ability == 'tripPlatform'
    )
    const checked = get(tripPlatform, 'checked')
    const platform = get(tripPlatform, 'platform') || []
    const pp = platform.join(',')
    items.forEach((i) => {
      const type = /\?/.test(i.source) ? '&' : '?'
      if (!i.source.includes('token')) {
        i.source = i.source + `${type}token=${token}&applyId=${applyId}&tripId=${tripId}&ekbAccessToken=${Fetch.accessToken || ''}`
      }
      const power = i.powers && i.powers.length ? i.powers[0] : undefined
      i.disabled = checked && pp.length > 1 ? !pp.includes(power) : false
    })
    const selectItem = items.find(v => v.title === currentPlatform)
    if (selectItem) {
      api.thirdResources.deleteByType(TK)
      api.thirdResources.add([selectItem])
      const services = {
        token: () => {
          return new Promise((resolve, reject) => {
            setTimeout(() => resolve({ RD: { c: 3 } }), 1000)
          })
        }
      }
      await api.request({
        type: TK || 'BUTTON_TRAVEL_ORDER',
        services: services
      })
    }
  }

  render() {
    const { orderInfo, tripType, tripInfo, customTrip, tripOrderAll } = this.state
    if (!orderInfo) {
      return null
    }
    const dataLink = get(this.props, 'value.data.dataLink')
    const parentId = get(dataLink, 'entity.parentId')
    const platform = get(dataLink, `E_${parentId}_订票平台`, '')
    return (
      <div className={style['trip-order-wrapper']}>
        <div className="order-info">
          <div>
            <div className="dis-f jc-sb mb-4 em">
              <span className="green">{orderInfo['订单状态']}</span>
              {orderInfo['订单金额'] && (
                <span>
                  {orderInfo['订单金额'].standardSymbol}
                  {orderInfo['订单金额'].standard}
                </span>
              )}
            </div>
            <div className="dis-f jc-sb">
              <span>
                {i18n.get('订单号')} {orderInfo['订单号']}
              </span>
              <span>{i18n.get('总金额')}</span>
            </div>
            {orderInfo['订单类型'] !== '主订单' && <div className="price">{orderInfo['订单类型']}</div>}
            {platform !== '合思商城' && <Button
              className="btn"
              onClick={this.handleOpenMall}
            >
              {i18n.get(`打开${platform}`)}
            </Button>}
            {orderInfo['超标原因'] && (
              <div className="price">
                <img className="img" src={SVG_WARNING} />
                <span>超标原因: {orderInfo['超标原因']}</span>
              </div>
            )}
          </div>
        </div>
        <TripOrderCard dataLink={get(this.props, 'value.data.dataLink')} tripOrderAll={tripOrderAll} orderInfo={orderInfo} tripType={tripType} />
        {tripInfo && (
          <div className="order-trip">
            <h5>{i18n.get('关联行程')}</h5>
            <div className="cur-p" onClick={this.handleTripDetail}>
              <TripCard trip={tripInfo} customTrip={customTrip}>
                <EKBIcon name="#EDico-right-default" />
              </TripCard>
            </div>
          </div>
       )}
      </div>
    )
  }
}
