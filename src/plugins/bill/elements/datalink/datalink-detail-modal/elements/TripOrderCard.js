// @i18n-ignore-all
import React, { Component } from 'react'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import { toast } from '../../../../../../lib/util'
import { TravelCard } from '../../TravelCard'
import style from './TripOrderCard.module.less'
import { getTravelOrderData } from '../../dataLinkUtil'
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'

// secretStr('*********', 3, 2) -> '123****89
const secretStr = (value, startNum, endNum) => {
  if (!value) {
    return ''
  }
  if (startNum + endNum < value.length) {
    return value.substring(0, startNum) + '****' + value.substring(value.length - endNum)
  } else {
    return value
  }
}
export default class TripOrderCard extends Component {
  handleCopy = () => {
    toast.success(i18n.get('复制成功！'))
  }

  renderTripPeople = tripType => {
    const { orderInfo } = this.props
    if (tripType === 'HOTEL') {
      return (
        <div className={style["hotel-trip-people"]}>
          <div className="em">
            {i18n.get('房间:')} {orderInfo['房型']}
          </div>
          <div>
            {orderInfo['出行人'].map((i, index) => {
              let text = i?.name
              text += index !== orderInfo['出行人'].length - 1 ? i18n.get('、') : ''
              return <span key={i?.id}>{text}</span>
            })}
          </div>
        </div>
      )
    } else {
      return (
        <>
          {orderInfo['出行人'].map(i => (
            <div className={style["trip-people"]} key={i}>
              <div className="dis-f jc-sb">
                <span className="em">{i?.name}</span>
                <span className="green">{orderInfo['订单状态']}</span>
              </div>
              <ol>
                <li>
                  {i18n.get('证件号')} {secretStr(orderInfo['证件信息'], 6, 4)}
                </li>
                <li>
                  {i18n.get('手机号')} {secretStr(i?.cellphone, 3, 4)}
                </li>
                {tripType === 'FLIGHT' && (
                  <li>
                    {i18n.get('客票号')} {orderInfo['票号']}
                    <CopyToClipboard text={orderInfo['票号']}>
                      <a className="btn-copy color-blue" onClick={this.handleCopy}>
                        {i18n.get('复制')}
                      </a>
                    </CopyToClipboard>
                  </li>
                )}
              </ol>
            </div>
          ))}
        </>
      )
    }
  }
  getTypeUrl = (type, token, source, orderId, orderType) => {
    const link = /\?/.test(i.source) ? '&' : '?'
    switch (orderType) {
      case '改签':
        orderType = 2
        break
      case '退票':
        orderType = 3
        break
      default:
        orderType = 1
        break
    }
    return (
      source +
      `${link}token=${token}&ekbAccessToken=${Fetch.accessToken || ''}` +
      (type === 'TRAIN'
        ? `&orderNo=${orderId}&orderType=${orderType}`
        : type === 'HOTEL'
          ? `&detailId=${orderId}`
          : `&orderId=${orderId}`)
    )
  }
  handleCardClick = data => {
    const { type, sourceData } = data
    const parentId = get(sourceData, 'entity.parentId')
    const platform = get(sourceData, `E_${parentId}_订票平台`, '')
    const white = ['FLIGHT', 'HOTEL', 'TRAIN']
    const status = sourceData[Object.keys(sourceData).find(o => !!o.endsWith('订单状态')) || '']
    const statusList = ['退票', '退订', '退订/离店'] // @i18n-ignore
    const isRefund = statusList.includes(status) && sourceData?.useCount < sourceData?.totalCount
    if (platform === '合思商城' && white.includes(type)) {
      if (type !== 'TRAIN' && isRefund) {
        return
      }
      const orderId = get(sourceData, `E_${parentId}_订单号`, '')
      const orderType = get(sourceData, `E_${parentId}_订单状态`, '')
      const TK = `BUTTON_${type}_ORDER_DETAIL`
      Promise.all([
        api.invokeService('@mall:get:travel:intent', { type: TK }),
        api.invokeService('@mall:get:travel:intent:jwt', { type: TK })
      ]).then(async result => {
        const items = result[0] && result[0].items ? result[0].items : []
        const token = result[1] ? result[1].id : ''
        items.forEach(i => {
          i.source = this.getTypeUrl(type, token, i?.source, orderId, orderType)
        })
        api.thirdResources.deleteByType(TK)
        api.thirdResources.add(items)
        if (items.length > 0) {
          const services = {
            token: () => {
              return new Promise((resolve, reject) => {
                setTimeout(() => resolve({ RD: { c: 3 } }), 1000)
              })
            }
          }
          const result2 = await api.request({
            type: TK || 'BUTTON_TRAVEL_ORDER',
            services: services
          })
        } else {
          // 判断是否提供订购渠道
          return toast.info('您暂无权限，请联系贵司管理员开通合思商城')
        }
      })
    }
  }
  renderOrder = (data,i,dataLink,orderInfo) => {
    const travelCardData = getTravelOrderData(data,dataLink,orderInfo)
    return (
      <TravelCard
        key={i}
        data={travelCardData}
        style={{ margin: 0, marginBottom: 24, borderRadius: 6 }}
        handleCardClick={() => {
          this.handleCardClick(travelCardData)
        }}
      />
    )
  }
  render() {
    const { orderInfo, tripType, dataLink, tripOrderAll } = this.props
    if (!orderInfo) return null
    const travelCardData = getTravelOrderData(dataLink)
    return (
      <>
        <div className={style['order-detail-card']}>
          {tripType === 'TRAIN' && (
            <div className="ticket-num">
              {i18n.get('取票号')} {orderInfo['票号']}
            </div>
          )}
          {tripOrderAll.map((e, i) => this.renderOrder(e, i,dataLink,orderInfo))}
        </div>
        <h5>{i18n.get('出行人')}</h5>
        {this.renderTripPeople(travelCardData.type)}
      </>
    )
  }
}
