import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
// import { required } from '../../../../../../components/utils/validatorUtil'
const { required } = app.require('@components/utils/validatorUtil')
//@codemod-sync-{required}
// import MutiRowStaffView from '../../../../../../elements/puppet/MutiStaff/MutiRowStaffView'
const MutiStaff = app.require('@elements/puppet/MutiStaff/MutiStaff')
import styles from './DataLinkAutoNumber.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
const { wrapper } = app.require('@components/layout/FormWrapper')
import { getV } from '@ekuaibao/lib/lib/help'
import { get } from 'lodash'
import { getDisplayName } from "../../../../../../components/utils/fnDataLinkUtil";
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'newAutoNumber'
  },
  validator(field: any, props: any) {
    return [required(field, props)]
  },
  wrapper: wrapper(true, true, false, false)
})
@EnhanceConnect((state: any) => ({
  noRootPathMap: state['@common'].departments.noRootPathMap,
  noRootEnPathMap: state['@common'].departments.noRootEnPathMap
}))
export default class DataLinkAutoNumber extends PureComponent<any, any> {
  handleSelectStaff = () => {
    const {
      field: { name, isOwner },
      value,
      datalinkId,
      bus
    } = this.props
    if (name === 'ownerId' || isOwner) {
      const id = value ? value.id : ''
      api
        .invokeService('@layout:select:staff', {
          defValue: id
        })
        .then((result: any) => {
          const { id } = result
          api
            .invokeService('@mine:put:dataLink:FZRorCYRList', { type: 'owner', id: datalinkId, staffIds: [id] })
            .then(() => {
              bus.invoke('updata:datalink:list', { isFZR: true })
            })
        })
    } else {
      const users = value.staffs || []
      api.invokeService('@layout:select:multiple:staff', { users }).then((res: any) => {
        api
          .invokeService('@mine:put:dataLink:FZRorCYRList', { type: 'visibility', id: datalinkId, staffIds: res })
          .then(() => {
            const visibile = { ...value, staffs: res }
            bus.invoke('updata:datalink:list', { isFZR: false, visibile })
          })
      })
    }
  }
  getFZRStr = (value: any) => {
    const active = value && value.active
    const ownerFlag = active
    const ownerIdStr = active ? '' : '-'
    return { ownerIdStr, ownerFlag }
  }

  getCYRStr = (value: any = {}, staffMap: any) => {
    const staffs = value.staffs || []
    const oStaffs = staffMap && staffs.slice().filter((id: any) => staffMap[id])
    const visibilityData = staffMap ? [].concat(oStaffs.slice().map((id: any) => staffMap[id])) : []
    const CYRStr = !value.staffs || !visibilityData.length ? '-' : value
    const visibilityStr = value.fullVisible ? i18n.get('全部人员') : CYRStr
    const visibilityFlag = value.fullVisible || !visibilityData.length
    return { visibilityStr, visibilityFlag, visibilityData }
  }

  getDepartmentValue = () => {
    const { value, noRootPathMap = {}, noRootEnPathMap = {} } = this.props
    const pathMap = i18n.currentLocale === 'zh-CN' ? noRootPathMap : noRootEnPathMap
    const id = getV(value, 'id', '')
    let result = getDisplayName(value) || '-'
    result = pathMap.hasOwnProperty(id) ? pathMap[id] : result
    return result
  }

  render() {
    const {
      field: { name, isOwner, entity },
      value,
      showEdit = false,
      staffMap
    } = this.props
    let flag = false
    let str = ''
    let dataSource = []
    // 负责人
    if (name === 'ownerId' || isOwner) {
      const result = this.getFZRStr(value)
      flag = !result.ownerFlag
      str = result.ownerIdStr
      dataSource = value ? [value] : []
    } else {
      // 参与人
      const result = this.getCYRStr(value, staffMap)
      flag = result.visibilityFlag
      str = result.visibilityStr
      dataSource = result.visibilityData
    }
    if (entity === 'organization.Department') {
      str = this.getDepartmentValue()
      flag = true
    }
    const isShowEdit = name === 'visibility' ? showEdit && !value.fullVisible : showEdit
    return (
      <div className={styles['autonumber-wrapper']}>
        {flag ? <div className="autonumber-content">{i18n.get(str)}</div> : <MutiStaff dataSource={dataSource} />}
        {isShowEdit && (
          <div className="action" onClick={this.handleSelectStaff}>
            {i18n.get('编辑')}
          </div>
        )}
      </div>
    )
  }
}
