import { app } from '@ekuaibao/whispered'
import './PersonnelAvatar.less'
import React from 'react'
import classnames from 'classnames'

export default function PersonnelAvatar(props: any) {
  const { item = {} } = props
  const EkbIcon = app.require('@elements/ekbIcon')
  const SVG_DEFAULT = app.require('@images/avatar.svg')

  let avatar = item.avatar ? item.avatar : SVG_DEFAULT

  switch (item.type) {
    case 'role':
      avatar = <EkbIcon name="#EDico-role-fill" className="color-role" />
      break
    case 'department':
      avatar = <EkbIcon name="#EDico-folder-fill" className="color-department" />
      break
    default:
      avatar = <img src={avatar} />
      break
  }

  return (
    <div
      className={classnames('personnel-avatar', 'center', {
        department: item.type === 'department',
        role: item.type === 'role'
      })}
    >
      {avatar}
    </div>
  )
}

export function personName(name: any, CHINESENUM: any, ENGLISHNUM: any) {
  const isChina = /.*[\u4e00-\u9fa5]+.*$/.test(name)
  const index = isChina ? CHINESENUM : ENGLISHNUM
  if (name && name.length > index) {
    return name.substring(0, index) + '...'
  }
  return name
}
