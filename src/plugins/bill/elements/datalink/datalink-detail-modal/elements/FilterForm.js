import React, { PureComponent } from 'react'
import styles from './FilterForm.module.less'
import { createForm } from 'rc-form'
import { InputItem, Calendar, <PERSON><PERSON>, Picker, List } from 'antd-mobile'
import CALENDAR from '../../../../images/calendar.svg'
import enUS from 'antd-mobile/lib/calendar/locale/en_US'
import zhCN from 'antd-mobile/lib/calendar/locale/zh_CN'
import moment from 'moment'
import { QuerySelect } from 'ekbc-query-builder'


@createForm()
export default class FilterForm extends PureComponent {
  fieldsTypeMap = {}
  pickerValue = [
    { value: true, label: i18n.get('是') },
    { value: false, label: i18n.get('否') }
  ]
  constructor(props) {
    super(props)
    this.state = { filter: this.props.filter, showCalendar: false, dateKey: undefined }
  }
  componentDidMount() {
    const { fields = [] } = this.props
    fields.forEach(item => {
      this.fieldsTypeMap[item.name] = item
    })
  }
  handleChangeText = (value, key) => {
    const oldValue = this.state.filter
    this.setState({ filter: { ...oldValue, [key]: value } })
  }
  handleChangeNumberStart = (value, key) => {
    const oldValue = this.state.filter
    const oldNumberValue = oldValue[key]
    this.setState({ filter: { ...oldValue, [key]: [value, oldNumberValue?.[1]] ?? 0 } })
  }
  handleChangeNumberEnd = (value, key) => {
    const oldValue = this.state.filter
    const oldNumberValue = oldValue[key]
    this.setState({ filter: { ...oldValue, [key]: [oldNumberValue?.[0] ?? 0, value] } })
  }
  handleChangeDate = key => {
    this.setState({ showCalendar: true, dateKey: key })
  }
  handleChangeSwitcher = (value, key) => {
    this.setState({ filter: { ...this.state.filter, [key]: value[0] } })
  }
  renderItem = item => {
    const { filter } = this.state
    const { type, name } = item
    const itemValue = filter[name]
    switch (type) {
      case 'money':
      case 'number':
        return (
          <div className="datalink-filter-form-item-money">
            <InputItem
              className="flex-1"
              type="money"
              defaultValue={itemValue?.[0]}
              onChange={value => this.handleChangeNumberStart(value, name)}
            ></InputItem>
            -
            <InputItem
              defaultValue={itemValue?.[1]}
              className="flex-1"
              type="money"
              onChange={value => this.handleChangeNumberEnd(value, name)}
            ></InputItem>
          </div>
        )
      case 'date':
      case 'dateRange':
        return (
          <div
            className="datalink-filter-form-item-date"
            onClick={() => {
              this.handleChangeDate(name)
            }}
          >
            <span>
              {itemValue && itemValue.length
                ? `${moment(itemValue[0]).format('YYYY/MM/DD')}~${moment(itemValue[1]).format('YYYY/MM/DD')}`
                : ''}
            </span>
            <img src={CALENDAR}></img>
          </div>
        )
      case 'switcher':
        return (
          <Picker
            data={this.pickerValue}
            cols={1}
            className="forss"
            value={[itemValue]}
            onOk={value => this.handleChangeSwitcher(value, name)}
          >
            <List.Item arrow="horizontal"> </List.Item>
          </Picker>
        )
      default:
        return (
          <InputItem
            placeholder={i18n.get(`请输入关键字`)}
            defaultValue={itemValue}
            onChange={value => this.handleChangeText(value, name)}
          ></InputItem>
        )
    }
  }
  onConfirm = (startTime, endTime) => {
    const { dateKey } = this.state
    this.setState({
      showCalendar: false,
      filter: { ...this.state.filter, [dateKey]: [startTime.valueOf(), endTime.valueOf()] }
    })
  }

  onCancel = () => {
    this.setState({
      showCalendar: false
    })
  }
  getRefFilter = (entity, key, keyValue, query) => {
    if (!keyValue) return
    switch (entity) {
      case 'pay.PayeeInfo': //收款信息
        query.filterBy(
          `form.${key}.name.containsIgnoreCase("${keyValue}") || form.${key}.nameSpell.containsIgnoreCase("${keyValue}") || form.${key}.bank.containsIgnoreCase("${keyValue}") || form.${key}.branch.containsIgnoreCase("${keyValue}")`
        )
        break
      case 'basedata.city':
        query.filterBy(`form.${key}.containsIgnoreCase("${keyValue}")`)
        break
      case 'organization.Department':
        query.filterBy(`form.${key}.name.containsIgnoreCase("${keyValue}") || form.${key}.nameSpell.containsIgnoreCase("${keyValue}")`)
        break
      default:
        query.filterBy(`form.${key}.name.containsIgnoreCase("${keyValue}")`)
    }
  }
  handleSubmit = () => {
    const { handleFilter } = this.props
    const { filter } = this.state
    const query = new QuerySelect()
    Object.keys(filter).forEach(key => {
      if (key !== 'show_full_name') {
        const type = this.fieldsTypeMap[key].type
        const keyValue = filter[key]
        if (['number', 'date'].includes(type)) {
          keyValue?.length && query.filterBy(`form.${key}>= ${keyValue[0]} && form.${key} <= ${keyValue[1]}`)
        } else if (type === 'money') {
          keyValue?.length &&
            query.filterBy(`form.${key}.standard>= ${keyValue[0]} && form.${key}.standard <= ${keyValue[1]}`)
        } else if (type === 'dateRange') {
          keyValue?.length &&
          query.filterBy(`form.${key}.start >= ${keyValue[0]} && form.${key}.start <= ${keyValue[1]}`)
        } else if (type === 'switcher') {
          key !== 'show_full_name' && query.filterBy(`form.${key} == ${keyValue}`)
        } else if (type === 'ref') {
          const entity = this.fieldsTypeMap[key].entity
          this.getRefFilter(entity, key, keyValue, query)
        } else if (keyValue) {
          query.filterBy(`form.${key}.containsIgnoreCase("${keyValue}")`)
        }
      }
    })
    handleFilter && handleFilter(this.state.filter, query.value())
  }
  handelCancel = () => {
    const { handleFilter } = this.props
    this.setState({ filter: {} })
    handleFilter && handleFilter({}, '')
  }
  render() {
    const { fields = [] } = this.props
    const { showCalendar } = this.state
    const en = /en/.test(i18n.currentLocale)
    return (
      <div className={styles['datalink-filter-form-wrapper']}>
        {fields.map((item, index) => {
          const { label, name } = item
          if (name.endsWith('_code') || name.endsWith('_name')) {
            return null
          } else {
            return (
              <div className="datalink-filter-form-item" key={`filter-form-item-${index}`}>
                <div className="datalink-filter-form-item-label">{label}</div>
                {this.renderItem(item)}
              </div>
            )
          }
        })}
        <div className="datalink-filter-form-item" key={`filter-form-item`}>
          <div className="datalink-filter-form-item-label">{i18n.get('列表名称是否全量显示')}</div>
          {this.renderItem({ type: 'switcher', name: 'show_full_name' })}
        </div>
        <Calendar
          locale={en ? enUS : zhCN}
          showShortcut={true}
          visible={showCalendar}
          onCancel={this.onCancel}
          onConfirm={this.onConfirm}
          defaultDate={new Date()}
        />
        <div className="datalink-filter-form-bottom">
          <Button className="datalink-filter-form-button-reset" onClick={this.handelCancel}>
            {i18n.get('重置')}
          </Button>
          <Button className="flex-1" type="primary" onClick={this.handleSubmit}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
