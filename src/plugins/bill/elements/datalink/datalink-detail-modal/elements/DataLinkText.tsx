import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
const { required } = app.require('@components/utils/validatorUtil')
const Money = app.require('@elements/puppet/MoneyDataLink')
import styles from './DataLinkText.module.less'
import { formatFieldShowByType } from '../../../../utils/dataLinkUtils'
const { wrapper } = app.require('@components/layout/FormWrapper')
import { isString } from 'lodash'
import { toast } from '../../../../../../lib/util'
import { isZhongYing } from '@ekuaibao/lib/lib/checkZY'
import { Fetch } from '@ekuaibao/fetch'

let types = [
  'text',
  'date',
  'dateRange',
  'number',
  'money',
  'switcher',
  'autoNumber',
  'dimension',
  'city',
  'list',
  'Staff',
  'Currency'
]

// @ts-ignore
@EnhanceField({
  descriptor: {
    test({ type = '' }) {
      return types.indexOf(type) >= 0
    }
  },
  wrapper: wrapper(true, true, false, false),
  validator(field: any, props: any) {
    return [required(field, props)]
  },
  initialValue(props: any) {
    let { field, initialValue } = props
    let { defaultValue } = field
    return initialValue !== undefined ? initialValue : defaultValue && defaultValue.value
  }
})
@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].me_info.staff
}))
export default class DataLinkText extends PureComponent<any, any> {
  get isOwner() {
    const {
      userInfo,
      entity: { dataLink }
    } = this.props
    return dataLink.ownerId?.id === userInfo?.id
  }

  handleEditParticipant = async () => {
    const {
      entity: { dataLink }
    } = this.props
    const dataLinkId = dataLink.id
    const selected = await app.open('@userPicker:UserPicker', {
      selectedUserIds: [],
      multiple: true,
      title: '选择参与人',
      isByRule: true
    })
  }

  getSMGNCCTextURL = (data: string) => {
    const code = app.getState('@common').me_info?.staff?.code
    const url = data.split('NCC:')[1]
    return url.replace('%empcode%', code)
  }
  renderSMG = () => {
    const { value } = this.props
    // const url = this.getSMGNCCTextURL(value)
    return (
      <div className={styles['datalink-text-wrapper']}>
        {value ? (
          <a
            onClick={() => {
              toast.info(i18n.get('合同详情请在电脑端查看'))
            }}
          >
            {i18n.get('查看合同')}
          </a>
        ) : (
          <div>{'-'}</div>
        )}
      </div>
    )
  }

  renderMoney = (value: any) => {
    if (window.IS_SMG || isZhongYing(Fetch.ekbCorpId)) {
      const isSmgMoney = Number(value.standard) >= 99999999999.0 //SMG（11位9）
      const isZYMoney = Number(value.standard) >= 9999999999.0 //中影企业（10位9）
      if (isSmgMoney || isZYMoney) {
        return '-'
      } else {
        return <Money value={value} />
      }
    }
    return <Money value={value} />
  }

  render() {
    let { value, field, planned, entity } = this.props
    value =
      field.name && field.name.indexOf('planned.') === 0
        ? value[
            field.name
              .split('_')
              .pop()
              .toUpperCase()
          ]
        : value
    if (window.IS_SMG && field.type === 'text' && value && isString(value) && value.indexOf('NCC:') === 0) {
      return this.renderSMG()
    }
    return (
      <div className={styles['datalink-text-wrapper']}>
        {field.type === 'money'
          ? value
            ? this.renderMoney(value)
            : '-'
          : formatFieldShowByType(field, value, planned, entity)}
      </div>
    )
  }
}
