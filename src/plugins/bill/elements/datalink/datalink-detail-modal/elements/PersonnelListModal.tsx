import styles from './PersonnelListModal.module.less'
import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import PersonnelAvatar from './PersonnelAvatar'
import { getStaffShowByConfig } from '../../../../../../components/utils/fnDataLinkUtil'

@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].me_info.staff,
  pathMap: state['@common'].departments.pathMap,
  enPathMap: state['@common'].departments.enPathMap
}))
export default class PersonnelListModal extends PureComponent<any, any> {
  prevTitle: any = api.invokeService('@layout:get:header:title')

  componentDidMount() {
    api.invokeService('@layout:set:header:icon', { showIcon: false })
    api.invokeService('@layout:change:header:title', this.props.title)
  }

  componentWillUnmount() {
    api.invokeService('@layout:set:header:icon', { showIcon: true })
    api.invokeService('@layout:change:header:title', this.prevTitle)
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  renderDec = (item: any) => {
    const { pathMap = {}, enPathMap = {} } = this.props
    const map = i18n.currentLocale === 'zh-CN' ? pathMap : enPathMap
    switch (item.type) {
      case 'role':
        return i18n.get('角色')
      case 'department':
        return i18n.get('部门')
      default:
        return map[item.defaultDepartment]
    }
  }

  render() {
    const { title, dataSource } = this.props
    return (
      <div className="modal-popup-content">
        <div className="modal-action-bar">
          <div className="modal-action-bar-btn" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </div>
          <span className="modal-action-bar-title">
            {title}{i18n.get("（")}{dataSource.length}{i18n.get("）")}
          </span>
        </div>
        <div className={styles['personnel-list']}>
          {dataSource.map((item: any) => (
            <div className="personnel-item" key={item.id}>
              <PersonnelAvatar item={item} />
              <div className="personnel-info">
                <div className="personnel-name">{ getStaffShowByConfig(item) || ''}</div>
                <div className="personnel-dec">{this.renderDec(item)}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }
}
