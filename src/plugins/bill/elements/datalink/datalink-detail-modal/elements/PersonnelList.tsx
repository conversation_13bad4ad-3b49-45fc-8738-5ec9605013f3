import { app } from '@ekuaibao/whispered'
import styles from './PersonnelList.module.less'

import React, { PureComponent } from 'react'
import { app } from '@ekuaibao/whispered'

import PersonnelAvatar from './PersonnelAvatar'
import { getStaffShowByConfig } from '../../../../../../components/utils/fnDataLinkUtil'

interface Props {
  title: string
  dataSource: Array<{
    name: String
    avatar: String
  }>
  maxShowNum: number
}

export default class PersonnelList extends React.PureComponent<Props, {}> {
  handleMore = () => {
    const { title, dataSource } = this.props
    app.open('@bill:PersonnelListModal', { title, dataSource })
  }

  renderMore = () => {
    const { dataSource = [], maxShowNum } = this.props
    if (dataSource.length <= maxShowNum) {
      return null
    }
    return (
      <div className="personnel-item">
        <span className="btn-more" onClick={this.handleMore}>
          {i18n.get('更多')}
        </span>
      </div>
    )
  }

  render() {
    const { dataSource = [], maxShowNum } = this.props
    return (
      <div className={styles['personnel-list']}>
        {dataSource.slice(0, maxShowNum).map((item, index) => (
          <div className="personnel-item" key={index}>
            <PersonnelAvatar item={item} />
            <div className="personnel-name">{ getStaffShowByConfig(item) || ''}</div>
          </div>
        ))}
        {this.renderMore()}
      </div>
    )
  }
}
