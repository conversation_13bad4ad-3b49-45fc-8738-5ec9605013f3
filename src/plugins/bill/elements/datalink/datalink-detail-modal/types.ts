/*!
 * Copyright 2019 yangjun<PERSON> <<EMAIL>>. All rights reserved.
 * @since 2019-05-15 11:11:40
 */

export interface MoneyFieldModel {
  standard: string
  standardUnit: string
  standardScale: number
  standardSymbol: string
  standardNumCode: string
  standardStrCode: string
  foreign?: string
  foreignNumCode?: string
  foreignScale?: string
  foreignStrCode?: string
  foreignSymbol?: string
  foreignUnit?: string
}

export type FieldValue = string | number | MoneyFieldModel

export interface LedgerModel {
  id: string
  active: boolean
  name: string
  [key: string]: any
}

export type PlanColumn = 'PLANNED' | 'EXECUTE' | 'BALANCE' | 'PERCENTAGE'

export interface PlanInterface {
  PLANNED: MoneyFieldModel //计划额度
  EXECUTE: MoneyFieldModel //执行额度
  BALANCE: MoneyFieldModel //余额
  PERCENTAGE: string //百分比
  id: string //计划id
  name: string //计划名称
  plannedId?: string
  plannedName?: string //计划额度名称
  executeName?: string //执行额度名称
  showForm: string //展现方式
  controlType?: any //风险类型
  source?: string
  type?: string
  childTypeName?: any //子维度名称
  childPlanNode?: PlanInterface[] //子维度数据
  [key: string]: any
}

export const PlanFieldLabels: Record<PlanColumn, string> = {
  PLANNED: i18n.get('计划额度'),
  EXECUTE: i18n.get('执行额度'),
  BALANCE: i18n.get('余额'),
  PERCENTAGE: i18n.get('执行比例')
}

export interface DataLinkEntityModel {
  dataLink: Record<string, FieldValue>
  planned: Record<string, FieldValue>
  ledger: Record<string, FieldValue>
  ledgers: LedgerModel[]
  plans: PlanInterface[]
  actions: any[]
}

export interface TemplateField {
  name: string
  label: string
  type: 'money' | 'number' | 'string'
  optional: boolean
  scale: number
  unit: string
  source: 'dataLink' | 'planned' | 'ledger'
}

export interface VisibilityModel {
  fulVisible: boolean
  staffs: null
  roles: null
  departments: null
  departmentsIncludeChildren: boolean
}

export enum PlannedProgressEnum {
  PLANNED_EXECUTE = 'PLANNED_EXECUTE',
  PLANNED_EXECUTE_BALANCE = 'PLANNED_EXECUTE_BALANCE',
  PLANNED_EXECUTE_PERCENTAGE = 'PLANNED_EXECUTE_PERCENTAGE',
  PLANNED_EXECUTE_BALANCE_PERCENTAGE = 'PLANNED_EXECUTE_BALANCE_PERCENTAGE',
  BALANCE = 'BALANCE',
  PERCENTAGE = 'PERCENTAGE',
  ALL = 'ALL'
}

export interface DetailComponentModel {
  elemType?: any
  type: string
  source: 'dataLink' | 'planned' | 'ledger'
  field: string
  label: { type: 'const'; value: string }
  value: { type: string }
  entity: string
}

export interface DetailTemplateModel {
  actions: any[]
  title: {
    fields: TemplateField[]
  }
  master: {
    fields: TemplateField[]
  }
  planned: {
    ids: string[]
    show: 'PROGRESSBAR' | 'TABLE'
    selected: PlannedProgressEnum
    progressbar: PlannedProgressEnum[]
  }
  ledger: {
    ids: string[]
  }
  expansion: {
    dataSources: Array<{
      name: string
      filter: string
      table: string
    }>
    components: [
      {
        type: 'panel' | 'section' | 'progress' | 'table' | 'ledger'
        components: DetailComponentModel[]
        source: 'dataLink' | 'planned' | 'ledger'
        field: string
        label: null
        value: null
        show: 'PROGRESSBAR' | 'TABLE'
        columns: PlanColumn[]
      }
    ]
  }
}

export interface TemplateModel<T> {
  id: string
  version: number
  active: boolean
  name: null
  visibility: VisibilityModel
  entityId: string
  content: T
}

export interface TemplateDataResponse<T> {
  data: DataLinkEntityModel
  total: number
  template: TemplateModel<T>
}

export enum TemplateKind {
  TABLE = 'TABLE',
  CARD = 'CARD',
  DETAIL = 'DETAIL'
}

export interface Dimension {
  active: boolean
  id: string
  name: string
}
