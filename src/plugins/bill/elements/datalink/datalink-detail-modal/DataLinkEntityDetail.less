@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.datalink-entity-detail-modal {
  height: 100%;
  background: #f2f3f5;
  display: flex;
  flex-direction: column;

  .datalink-entity-detail-footer {
    padding: @space-4 @space-6;
    display: flex;

    & > div {
      flex: 1;
      margin: 0 @space-2;
    }
  }

  .dataLink-content {
    flex: 1;
    overflow: auto;
    -webkit-overflow-scrolling: touch;

    > .module {
      border-bottom: @space-4 solid @color-bg-2;
      background-color: #fff;

      &:last-child {
        border-bottom: 0;
      }
    }

    > .panel {
      display: flex;
      min-height: 196px;

      > .item {
        width: 33.33%;
        flex: 1;
        padding: @space-7 0px;
        text-align: center;
        border-right: 2px solid @color-bg-1;

        &:last-child {
          border-right: 0;
        }

        > div {
          white-space: pre;
          word-break: keep-all;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        > .value {
          .font-size-3;
          .font-weight-3;
          color: @color-black-1;
          padding: 0px @space-4 @space-2;

          a {
            color: var(--brand-base);
            margin-left: @space-2;
            .font-size-2;
            .font-weight-2;
          }

          .money {
            display: flex;
            flex-direction: column;
            .value_money_foreign {
              font-weight: 600;
              font-size: 28px;
              color: rgba(47, 50, 55, 0.96);
            }
            .value_money_standard {
              font-size: 24px;
              color: rgba(47, 50, 55, 0.48);
            }
          }
        }

        > .label {
          .font-size-2;
          color: @color-black-3;
          padding: 0 @space-4;
        }
      }
    }

    > .section {
      padding: @space-5 @space-7;

      > .title {
        .font-size-3;
        .font-weight-3;
        color: @color-black-1;
        padding: @space-5 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        > .toggle {
          transform: rotate(0deg);
          transition: transform 0.3s;

          &.reverse {
            transform: rotate(180deg);
          }
        }
      }

      > .content {
        .font-size-2;
        overflow: hidden;
        max-height: 6000px;
        transition: max-height 0.4s;

        &.hide {
          max-height: 0;
        }

        .formitem-wrapper-forFix {
          margin: @space-5 0;
          padding: 0 !important;
          border: 0 !important;
        }

        .vertical-label {
          margin: 0;
          align-items: flex-start;
          padding-right: @space-5;
          .vertical-title {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        .vertical-content {
          margin: 0;
          > div {
            margin: 0;
          }
        }
      }
    }

    > .plan {
      padding: @space-5 @space-7;

      > .title {
        .section .title;
      }

      > .content {
        width: 100%;
        overflow: auto hidden;
        touch-action: pan-x pan-y;
        padding: @space-5 0;
      }
    }

    > .table {
      .tips {
        .font-size-1;
        color: @color-black-3;
        padding-bottom: @space-5;
      }
    }
  }

  .collect-item {
    background-color: #fff;
    padding-bottom: 34px;
    .btn {
      height: 88px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 12px 32px;
      border: 2px solid #2555ff;
      border-radius: 12px;
    }
    .status {
      .font-size-3;
      font-weight: 400;
      color: #2555ff;
      margin-left: 10px;
    }
    .collected {
      .font-size-5;
      color: #fadc19;
    }
    .unCollect {
      .font-size-5;
      color: #2555ff;
    }
  }
}
