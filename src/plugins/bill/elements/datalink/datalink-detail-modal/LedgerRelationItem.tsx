import { ListView, RefetchParams } from '@ekuaibao/eui-isomorphic'
import React, { PureComponent } from 'react'
import moment from 'moment'
import { get } from 'lodash'
import { app as api, app } from '@ekuaibao/whispered'
const Money = app.require<any>('@elements/puppet/MoneyDataLink')
import { ExpenseModel } from '../../../../invoice/expense/types'
import styles from './LedgerRelationItem.module.less'
const EkbIcon = app.require<any>('@elements/ekbIcon')
import { EntityLayoutTemplate, EntityListDataItem, MoneyIF } from '@ekuaibao/ekuaibao_types'
import Popup from '@ekuaibao/popup-mobile'
import ListTitle from '../ListTitle'
import { Dimension } from './types'
import BillEmpty from '../../../bill-content/bill-empty'
import { getChildrenPrimary, getLedgerRelationListAmount } from '../../../bill.action'
import { showLoading, hideLoading } from '../../../../../lib/util'
import { getBoolVariation } from '../../../../../lib/featbit'

interface Props {
  layerManager?: any
  ledgerDetailList?: any[]
  currentLedger?: any
  ledgerList?: any[]
  value?: any
  startIndex?: number
  offset?: number
  globalFieldsMap: any
  ledgerConfigId?: string
  statisticsSource?: string
}

interface State {
  count: number
  amount: MoneyIF
  sort: boolean
  current: Dimension
  dimension?: Dimension[]
}

const all = [
  {
    active: true,
    id: 'all',
    name: i18n.get('全部明细')
  }
]

export default class LedgerRelationItem extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      count: 0,
      amount: { standard: '0' },
      sort: true,
      dimension: all,
      current: all[0]
    }
    this.getChildrenPrimary(props)
  }

  getChildrenPrimary(props: Props) {
    const dataLinkId = get(props, 'value.id')
    const ledgerConfigId = get(props, 'ledgerConfigId')
    getChildrenPrimary(dataLinkId, ledgerConfigId).then(result => {
      this.setState({ dimension: result ? all.concat(result.items) : all })
    })
  }

  refetch = async (props: RefetchParams): Promise<any> => {
    const { startIndex = 0, offset = 100 } = props || {}
    const { sort, current } = this.state
    const dataLinkId = get(this.props, 'value.id')
    const ledgerConfigId = get(this.props, 'ledgerConfigId')
    const statisticsSource = get(this.props, 'statisticsSource')
    const params = {
      dataLinkId,
      ledgerConfigId,
      childrenId: current.id,
      statisticsSource,
      start: startIndex,
      count: offset,
      sort
    }
    const getDetails = statisticsSource === 'DATA_LINK' ? this.getRelationEntityData : this.getMasterAndDetailsData
    const { dataSource, total, amount } = await getDetails(params)
    const state = {
      items: dataSource || [],
      count: total || 0,
      amount: amount || 0
    }
    this.setState({
      count: state.count,
      amount: state.amount
    })
    return state
  }

  getRelationEntityData = async (params: any): Promise<any> => {
    const result0 = await api.invokeService('@bill:get:ledger:relation:entity:list', params)
    const result1 = await getLedgerRelationListAmount(params)
    const items = formatDataLink(result0)
    const total = get(result0, 'total', 0)
    const amount = get(result1, 'items', 0)
    return { dataSource: items, total, amount }
  }

  getMasterAndDetailsData = async (params: any): Promise<any> => {
    const result0 = await api.invokeService('@bill:get:ledger:relation:list', params)
    const result1 = await getLedgerRelationListAmount(params)
    const items = get(result0, 'items', [])
    const total = get(result0, 'count', 0)
    const amount = get(result1, 'items', 0)
    return { dataSource: items, total, amount }
  }

  renderItem = (props: any) => {
    const statisticsSource = get(this.props, 'currentLedger.statisticsSource')
    if (statisticsSource === 'DATA_LINK') {
      return this.renderEntityItem(props)
    }
    return this.renderMasterAndDetailsItem(props)
  }

  renderEntityItem = (props: any) => {
    const { data, key, status, style } = props
    const item = get(data, 'item')
    const template = get(data, 'template')
    if (status === 1) {
      return (
        <div
          key={key}
          className="horizontal ledger-relation-list-entity"
          style={style}
          onClick={() => this.handleClick(item)}
        >
          <ListTitle data={item} template={template} key={key} />
        </div>
      )
    }
    return <div />
  }

  renderMasterAndDetailsItem = (props: any) => {
    const { data, key, status, style } = props
    const { currentLedger, globalFieldsMap, statisticsSource } = this.props
    if (status === 1) {
      if (statisticsSource === 'MASTER') {
        return (
          <BillListItem
            item={data}
            key={key}
            style={style}
            globalFieldsMap={globalFieldsMap}
            currentLedger={currentLedger}
            onClick={this.handleItemClick}
          />
        )
      }
      return (
        <DetailItem
          item={data}
          key={key}
          style={style}
          globalFieldsMap={globalFieldsMap}
          currentLedger={currentLedger}
          onClick={this.handleClickDetailItem}
        />
      )
    } else {
      return <div />
    }
  }

  handleClick = (line: any) => {
    api.open('@bill:DataLinkEntityDetailModal', {
      value: {
        data: line.dataLink,
        id: line.dataLink.id
      },
      title: ''
    })
  }

  handleItemClick = (flowId: string, currentLedger: any) => {
    showLoading()
    api.invokeService('@common:get:flow:detail:info', { id: flowId, isBack: false }).then(() => {
      hideLoading()
      api.open('@bill:BillDetailModal', {
        params: { id: flowId },
        openedDataLinkModalData: this.props.value,
        fromDataLink: true
      })
    })
  }

  handleClickDetailItem = (flowId: string, currentLedger: any, item: any) => {
    showLoading()
    api.invokeService('@common:get:flow:detail:info', { id: flowId, isBack: false }).then(({ value }: any) => {
      hideLoading()
      const detail = value.form.details.find((d: ExpenseModel) => d.feeTypeForm.detailId === item.detailId)
      api.open('@feetype:FeeTypeInfoModal', {
        feetype: detail.feeTypeId,
        value: detail.feeTypeForm,
        isEdit: false,
        risks: [],
        ds: [detail.feeTypeForm],
        template: detail.specificationId,
        idx: 0,
        submitterId: value.submitterId || value.form.submitterId,
        billType: value.formType,
        showBillBar: true,
        formAllData: value.form,
        billForm: value.form,
        flowId
      })
    })
  }

  handleSort = () => {
    const { sort } = this.state
    this.setState({ sort: !sort })
  }

  handleSelect(item: Dimension) {
    return () => {
      if (item.active) {
        this.setState({ current: item })
        Popup.hide()
      }
    }
  }

  selectDimension = () => {
    const { dimension, current } = this.state

    dimension.length > 1 &&
      Popup.show(
        <div className={styles['ledger-dimension']}>
          {dimension.map(item => (
            <div key={item.id} onClick={this.handleSelect(item)} className="ledger-dimension-item">
              <span className="flex">
                {item.name}
                {!item.active && i18n.get('(已停用)')}
              </span>
              {current.id === item.id && <EkbIcon name="#EDico-check-default" />}
            </div>
          ))}
        </div>,
        {
          animationType: 'slide-up',
          maskClosable: true,
          wrapClassName: 'popup-wrapper-style'
        }
      )
  }

  render() {
    const { currentLedger, ledgerConfigId } = this.props
    const { count, amount, sort, dimension, current } = this.state
    const statisticsSource = get(this.props, 'statisticsSource')
    return (
      <div className={styles['ledger-relation-item-wrapper']}>
        {currentLedger.isCalculation && (
          <div className="ledger-risks">
            <EkbIcon name="#EDico-plaint-circle" className="icon-warn" />
            <div>
              {i18n.get('管理员修改了待开发票金额的统计方式，系统正在更新数据中，当前的金额可能会与实际有所偏差')}
            </div>
          </div>
        )}
        <div className="ledger-title">{i18n.get('统计明细')}</div>
        <div className="ledger-sort">
          <div
            className={`flex horizontal ${dimension.length > 1 ? 'all' : 'disabled'}`}
            onClick={this.selectDimension}
          >
            {current.name}
          </div>
          {statisticsSource !== 'DATA_LINK' && (
            <div className="flex horizontal sort" onClick={this.handleSort}>
              {i18n.get('按提交日期')}
              <EkbIcon name="#EDico-sort1" className={`icon-sort ${sort}`} />
            </div>
          )}
        </div>
        {!!count && (
          <div className="ledger-total">
            {i18n.get('筛选出{__k0}条明细', { __k0: count })}
            {/* {currentLedger.fieldType === 'MONEY' ? (
              <Money currencySize={14} valueSize={14} value={amount} />
            ) : (
              <span>
                {Number(amount?.standard || amount) || 0}
                {currentLedger.unit}
              </span>
            )} */}
          </div>
        )}
        <ListView
          rowHeight={72}
          noRows={<BillEmpty emptyText={i18n.get('无统计明细')} />}
          refetch={this.refetch}
          renderItem={this.renderItem}
          key={`${ledgerConfigId}-${current.id}-${sort}`}
        />
      </div>
    )
  }
}

function BillListItem(props: any) {
  const { item, style, globalFieldsMap, onClick, currentLedger } = props
  let submitDates = get(item, 'flowId.form.submitDate')
  let name = get(item, 'flowId.form.submitterId.name')
  let title = get(item, 'flowId.form.title')
  const submitDate = moment(submitDates).format('YYYY/MM/DD')
  const desc = `${submitDate} ${name}`
  return (
    <div
      className="horizontal ledger-relation-list-item"
      style={style}
      onClick={() => onClick(item.flowId.id, currentLedger)}
    >
      <div className="w-40p flex">
        <div className="text-ellipsis">
          {title}
          {item.active ? '' : i18n.get('(已停用)')}
        </div>
        <div className="text-ellipsis sub-text">{desc}</div>
      </div>
      <div className="ta-r">
        {item.fieldType === 'MONEY' ? (
          <Money
            value={item.amount}
            currencySize={16}
            valueSize={16}
            color="#262626"
            apportionTotal={item.fromApporation ? item.originalApportionAmount : undefined}
          />
        ) : (
          <div>
            {item.fromApporation ? (
              <div className="center-view">
                <span className="r1">{`实际统计数值：${item.totalNum || 0} ${item?.unit}`}</span>
                <span className="r2 right mt4">{`原始数值：${item.originalApportionTotalNum || 0} ${item?.unit}`}</span>
              </div>
            ) : (
              <span>{`${item.totalNum || 0} ${item?.unit}`}</span>
            )}
          </div>
        )}
        {!item.fromApporation && (
          <div className="text-ellipsis sub-text">{
            getBoolVariation('aprd-5665-datalink') && item.manualApporationLabel ? item.manualApporationLabel :  globalFieldsMap[item.sumFieldName]?.label
          }</div>
        )}
      </div>
    </div>
  )
}

function DetailItem(props: any) {
  const { onClick, item, globalFieldsMap, style, currentLedger } = props
  if (!item.detail) {
    return <div />
  }
  let feeDateStr = fnGetDetailsByType(item.detail.feeTypeForm)
  return (
    <div
      className="horizontal ledger-relation-list-item"
      style={style}
      onClick={() => onClick(item.flowId, currentLedger, item)}
    >
      <img className="sub-icon" style={{ background: item.feeTypeId.color }} src={item.feeTypeId.icon} />
      <div className="w-40p flex">
        <div className="text-ellipsis">
          {item.feeTypeId.fullname}
          {item.active ? '' : i18n.get('(已停用)')}
        </div>
        <div className="text-ellipsis sub-text">{feeDateStr}</div>
      </div>
      <div className="ta-r">
        {item.fieldType === 'MONEY' ? (
          <Money
            value={item.amount}
            currencySize={16}
            valueSize={16}
            color="#262626"
            apportionTotal={item.fromApporation ? item.originalApportionAmount : undefined}
          />
        ) : (
          <div>
            {item.fromApporation ? (
              <div className="center-view">
                <span className="r1">{`实际统计数值：${item.totalNum || 0} ${item?.unit}`}</span>
                <span className="r2 right mt4">{`原始数值：${item.originalApportionTotalNum || 0} ${item?.unit}`}</span>
              </div>
            ) : (
              <span>{`${item.totalNum || 0} ${item?.unit}`}</span>
            )}
          </div>
        )}
        {!item.fromApporation && (
          <div className="text-ellipsis sub-text">{globalFieldsMap[item.sumFieldName]?.label}</div>
        )}
      </div>
    </div>
  )
}

export function fnGetDetailsByType(feeTypeForm: any) {
  let { feeDate, feeDatePeriod } = feeTypeForm
  let feeDateString = ''
  if (feeDatePeriod) {
    feeDateString = buildFeeDate2String(feeDatePeriod, true)
  } else if (feeDate && !feeDatePeriod) {
    feeDateString = buildFeeDate2String(feeDate)
  } else {
    feeDateString = i18n.get('无消费日期')
  }

  return feeDateString
}

function buildFeeDate2String(feeDate: any, isPeriod = false) {
  if (isPeriod && typeof feeDate === 'object') {
    let { start, end } = feeDate
    start = moment(start).format('YYYY/MM/DD')
    end = moment(end).format('YYYY/MM/DD')
    return `${start} ~ ${end}`
  }
  return moment(feeDate).format('YYYY/MM/DD')
}

export interface LedgerEntityListItem {
  item: EntityListDataItem
  template: EntityLayoutTemplate
}

function formatDataLink(data: any): LedgerEntityListItem[] {
  const items = get(data, 'data', [])
  const template = get(data, 'template.content', {})
  return items.map(
    (item: EntityListDataItem): LedgerEntityListItem => {
      return {
        item,
        template
      }
    }
  )
}
