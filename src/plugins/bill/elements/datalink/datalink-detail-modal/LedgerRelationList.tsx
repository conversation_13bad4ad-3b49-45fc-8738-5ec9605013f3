import styles from './LedgerRelationList.module.less'
import React, { PureComponent } from 'react'
import { app } from '@ekuaibao/whispered'
const ETabs = app.require<any>('@elements/ETabs')
const Money = app.require<any>('@elements/puppet/MoneyDataLink')
import LedgerRelationItem from './LedgerRelationItem'

interface Props {
  globalFieldsMap: any
  ledgerList: any[]
  value: any
}

interface State {
  activeKey: string
  currentLedger: any
}

export default class LedgerRelationList extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    const { ledgerList } = props
    this.state = {
      activeKey: ledgerList[0].ledgerConfigId,
      currentLedger: ledgerList[0]
    }
  }

  onTabClick = (tabActiveKey: any) => {
    const { ledgerList } = this.props
    const ledgers = ledgerList.find(o => o.id === tabActiveKey.key)
    this.setState({ activeKey: tabActiveKey.key, currentLedger: ledgers })
  }

  renderTab = (item: any) => {
    const { name, fieldType, totalAmount = {}, unit } = item
    return (
      <div className="center w-100p ta-c">
        <span className="w-100p text-ellipsis">{name}</span>
        {fieldType === 'MONEY' ? (
          <Money value={totalAmount} showShorthand={true} />
        ) : (
          <span>{Number(totalAmount) || 0}{unit}</span>
        )}
      </div>
    )
  }

  render() {
    const { ledgerList, globalFieldsMap } = this.props
    const { currentLedger, activeKey } = this.state
    const tabData = ledgerList.map((item, index) => {
      return {
        tab: this.renderTab(item),
        key: item.id,
        children: (
          <LedgerRelationItem
            {...this.props}
            key={index}
            ledgerConfigId={item.id}
            statisticsSource={item.statisticsSource}
            currentLedger={currentLedger}
            globalFieldsMap={globalFieldsMap}
          />
        )
      }
    })

    return (
      <div className={styles['ledger-relation-wrapper']}>
        <ETabs
          animated={true}
          swipeable={true}
          activeKey={activeKey}
          dataSource={tabData}
          prerenderingSiblingsNumber={0}
          onTabClick={this.onTabClick}
        />
      </div>
    )
  }
}
