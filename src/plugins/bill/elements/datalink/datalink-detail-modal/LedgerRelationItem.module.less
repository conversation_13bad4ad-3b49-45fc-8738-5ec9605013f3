@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.ledger-relation-item-wrapper {
  width: 100%;
  padding-bottom: 300px;
  :global {
    .ledger-title {
      .font-size-3;
      .font-weight-3;
      color: @color-black-1;
      padding: @space-7 @space-7 @space-5;
    }
    .ledger-risks {
      display: flex;
      height: 136px;
      padding: 0 32px;
      font-size: 28px;
      color: #fa962a;
      justify-content: space-between;
      align-items: center;
      background: rgba(250, 150, 42, 0.1);
      .icon-warn {
        width: 52px;
        height: 52px;
        margin-right: 24px;
      }
    }
    .ledger-sort {
      display: flex;
      height: 80px;
      padding: 0 @space-7 - @space-2;
      .horizontal {
        .font-size-3;
        margin: 0 @space-2;
        color: @color-inform;
        background-color: @color-bg-3;
        border-radius: @radius-1;
        justify-content: center;
      }
      .disabled {
        color: @color-inform-4;
      }
      .icon-sort {
        margin-left: @space-2;
        transform: rotate(0deg);
        transition: transform 0.3s;
        &.false {
          transform: rotate(180deg);
        }
      }
    }
    .ledger-total {
      .font-size-2;
      color: @color-black-3;
      padding: @space-5 @space-7;
      display: flex;
      align-items: center;
    }
    .ledger-relation-list-item {
      height: 144px;
      padding: 0 @space-7;
      border-bottom: 2px solid @color-line-2;
      color: @color-black-1;
      &:active {
        background-color: @color-bg-2;
      }
      .font-size-2;
      .sub-icon {
        width: @space-8;
        height: @space-8;
        border-radius: @space-6;
        margin-right: @space-6;
      }
      .sub-text {
        color: @color-black-3;
      }
    }
    .ledger-relation-list-entity {
      padding: 0 @space-7;
      border-bottom: 2px solid @color-line-2;
      &:active {
        background-color: @color-bg-2;
      }
    }
  }
}

.ledger-dimension {
  max-height: @space-10 * 5;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  :global {
    .ledger-dimension-item {
      height: @space-10;
      padding: 0 @space-7;
      display: flex;
      align-items: center;
      position: relative;
      &:active {
        background-color: @color-bg-2;
      }
      .flex {
        .font-size-3;
      }
      .icon {
        position: absolute;
        right: @space-7;
        top: (@space-10 - 40) / 2;
        font-size: 40px;
        color: @color-brand;
      }
    }
  }
}
