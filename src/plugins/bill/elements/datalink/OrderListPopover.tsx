import React, { PureComponent } from 'react'
import { forIn } from 'lodash'
import { app } from '@ekuaibao/whispered'
import { Popover } from 'antd-mobile'
import moment from 'moment'
import styles from './OrderListPopover.module.less'

const DATEFORMAT = 'YYYY-MM-DD'
interface Props {
  orders: any
  tripType: string
}

export default class OrderListPopover extends PureComponent<Props, any> {
  constructor(props: Props) {
    super(props)
    this.state = {
      visible: false,
    }
  }
  formatDataLink = (dataLink: any) => {
    const data: any = {}
    forIn(dataLink, (value, key) => {
      const lastIndex = key.lastIndexOf('_')
      data[lastIndex > -1 ? key.substring(lastIndex + 1) : key] = value
    })
    return data
  }
  handleOrderDetail = (value: any) => {
    app.open('@bill:DataLinkEntityTripOrderDetailModal', {
      field: value.entity.fields,
      value: { data: { dataLink: value } },
      title: i18n.get('订单详情')
    })
    app.invokeService('@common:set:track', {
        key: 'mytrips_order_view',
        actionName: '订单详情页pv',
        from: 'detail',
    })
  }
  renderPopover = () => {
    const { orders, tripType } = this.props
    return [(
      <Popover.Item>
        <div className="title">{i18n.get('订单列表')}</div>
          {orders.map((item: any, index: number) => {
            const order = this.formatDataLink(item.data.dataLink)
            return (
              <div className="order-list-item" key={index}>
                <div>{i18n.get('订单号')}:
                  <a className="num" onClick={() => this.handleOrderDetail(item.data.dataLink)}>{
                    order['订单号'] // @i18n-ignore
                  }</a>
                </div>
                {(tripType === 'HOTEL' || tripType === 'FOOD' || tripType === 'TAXI') ? (
                  <div className="fw-b">{
                    moment(order['入住日期']).format(DATEFORMAT) // @i18n-ignore
                  } - {
                    moment(order['离店日期']).format(DATEFORMAT) // @i18n-ignore
                  }, {order.name}</div>
                ) : (
                  <div className="fw-b">{
                    moment(order['出发时间']).format(DATEFORMAT) // @i18n-ignore}
                  }, {order.name}</div>
                )}
              </div>
            )
          })}
      </Popover.Item>
    )]
  }
  render() {
    return (
      <Popover
        overlayClassName={styles['order-list-wrapper']}
        getPopupContainer={(trigger: any) => trigger.parentElement}
        overlay={this.renderPopover()}
        >
        <a>{i18n.get('查看更多')}</a>
      </Popover>

    )
  }
}
