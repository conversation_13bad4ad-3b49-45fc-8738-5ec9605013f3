import React, { Component } from 'react'
import styles from './TripDetail.module.less'
import { app } from '@ekuaibao/whispered'
import { get, forIn, uniq, size, isBoolean } from 'lodash'
import { stateMap } from '@ekuaibao/lib/lib/enums'
import EnhanceTitleHook from '../../../../lib/EnhanceTitleHook'
import OrderListPopover from './OrderListPopover'
import { TemplateKind } from './datalink-detail-modal/types'
import moment from 'moment'
import { TravelDetail, Traveler } from './types'
import {
  TwoToneGeneralAirplane,
  TwoToneGeneralTrain,
  TwoToneGeneralTaxi,
  TwoToneGeneralHotel,
  TwoToneGeneralFood
} from '@hose/eui-icons'
import { Button } from '@hose/eui-mobile'
import { Fetch } from '@ekuaibao/fetch'
import { processMallResourceAuth } from '../../../../lib/mallSourceAuth'

const getFlowState: any = stateMap()
const flightIcon = require('../../images/flight_new.svg')
const hotelIcon = require('../../images/hotel_new.svg')
const trainIcon = require('../../images/train_new.svg')
const carIcon = require('../../images/car_new.svg')
const commonIcon = require('../../images/common_new.svg')
const foodIcon = require('../../images/food_new.svg')

const Money: any = app.require('@elements/puppet/Money')
const { getCityAndDate } = app.require('@bill/elements/datalink/dataLinkUtil')
const SVG_WARING = app.require('@images/invoice-payerno-warning.svg')
const { getDays } = app.require('@bill/elements/datalink/dataLinkUtil')

const TripMapLists: { [key: string]: any } = {
  飞机: '机票',
  酒店: '酒店',
  火车: '火车',
  用车: '用车',
  餐饮: '餐饮',
  通用: '通用'
}

const formTypeMap: StringStringProps = {
  expense: i18n.get('报销'),
  loan: i18n.get('借款'),
  requisition: i18n.get('申请'),
  custom: i18n.get('基础'),
  permit: i18n.get('授权'),
  payment: i18n.get('付款')
}

const ICON_MAP: any = {
  飞机: flightIcon,
  酒店: hotelIcon,
  火车: trainIcon,
  用车: carIcon,
  通用: commonIcon,
  餐饮: foodIcon
}

// 新行程type是中文的，有差异自己转吧
export const travelTypeCN2EN = (type: string): string => {
  switch (type) {
    case '飞机':
      return 'FLIGHT'
    case '火车':
      return 'TRAIN'
    case '酒店':
      return 'HOTEL'
    case '用车':
      return 'TAXI'
    case '餐饮':
      return 'FOOD'
    case '通用':
      return 'COMMON'
    default:
      return 'CUSTOM'
  }
}

interface States {
  deptList: any[]
  showButton: boolean
  travelDetail: Partial<TravelDetail>
  customTrip: any
  tripOrders: any
}
interface Props {
  params: any
}

@EnhanceTitleHook(i18n.get('行程详情'))
export default class TripDetail extends Component<Props, States> {
  private TK = ''
  private type = ''
  constructor(props: Props) {
    super(props)
    this.state = {
      showButton: false,
      deptList: [],
      travelDetail: {},
      customTrip: {},
      tripOrders: {}
    }
  }

  async componentWillMount() {
    const {
      params: { id }
    } = this.props

    this.getTravelDetail(id)
  }

  /**
   *
   * @param orderId 以前的dataLinkId
   * @param traveler 出行人
   */
  getDataLinkOrders = async (orderId: string, traveler: Traveler) => {
    if (!orderId) {
      return []
    }
    const orderResult = await app.invokeService('@bill:get:datalink:template:byId', {
      entityId: orderId,
      type: TemplateKind.DETAIL
    })
    const tripDataLink = this.formatDataLink(get(orderResult?.value, 'data.dataLink'))
    let tripOrders = tripDataLink['订单'] || [] // @i18n-ignore
    tripOrders = this.getOrders(tripOrders)
    return tripOrders
  }

  getTravelDetail = async (id: string) => {
    let departmentResult = null
    // 新行程详情
    const result = await app.invokeService('@bill:get:travel:management:detail', { id: id })
    const { value } = result
    const applyId = value?.flowDetail.id || value.flowDetail?.flowId
    const tripId = value.id
    const tripType = value.type
    const deptIds = [value?.corporationId]
    if (deptIds?.length > 0) {
      departmentResult = await app.invokeService('@common:get:departments:fullName:by:ids', uniq(deptIds))
    }
    // 获取行程类型
    const { items = [] } = await app.invokeService('@bill:get:travel:management')
    const customTrip = items.find((item: { name: string }) => item.name === tripType)
    this.TK = `BUTTON_${travelTypeCN2EN(tripType)}_ORDER`

    // 注册前往商城信息
    this.registerTripType(applyId, tripId, tripType)
    // 查询订单
    // const orderId = value?.orders[0]?.orderId
    const orders = value?.orders
    const tripOrders = this.getOrders(orders)

    this.setState({
      travelDetail: value,
      deptList: departmentResult.items,
      customTrip,
      tripOrders
    })
  }

  registerTripType = (applyId: string, tripId: string, tripType: string) => {
    const white = ['飞机', '酒店', '火车', '用车', '餐饮']
    if (!white.includes(tripType)) {
      return
    }
    Promise.all([
      app.invokeService('@mall:get:travel:intent', { type: this.TK }),
      app.invokeService('@mall:get:mall:auth:query', { type: this.TK }),
    ]).then(async result => {
      const items = result?.[0]?.items || []
      const authQuery = result?.[1]|| ''
      const config = await app.invokeService('@bill:get:getFlowInfo:tripPlatform', applyId)
      const checked = get(config, 'value.ability.checked')
      const platform = (get(config, 'value.ability.platform') || []).join(',')
      items.forEach((i: any) => {
        const type = /\?/.test(i.source) ? '&' : '?'
        if (!i.source.includes('token')) {
          i.source = i.source + `${type}${authQuery}&applyId=${applyId}&tripId=${tripId}`
        }
        const power = i.powers && i.powers.length ? i.powers[0] : undefined
        i.disabled = checked && platform.length > 1 ? !platform.includes(power) : false
      })
      this.setState({ showButton: items.length > 0 })
      app.thirdResources.deleteByType(this.TK)
      app.thirdResources.add(items)
    })
  }

  getDeptName = (traveler: Traveler) => {
    const { deptList } = this.state
    const dept = deptList.find((item: any) => traveler.id.indexOf(item.id) > -1)
    return dept ? dept.name : i18n.get('无')
  }

  getOrders = (data: any) => {
    const orders: any = {}
    data.forEach((i: any) => {
      // let dataLink = get(i, 'dataLink.form')
      const dataLink: any = this.composeDataLinkOrder(i)
      // dataLink = this.formatDataLink(dataLink)
      const traveler = dataLink['出行人'] // @i18n-ignore
      traveler?.forEach((e: any) => {
        if (!orders[e]) {
          orders[e] = []
        }
        orders[e].push(dataLink)
      })
    })
    return orders
  }

  composeDataLinkOrder = (orders: any) => {
    const {
      dataLink: { form, entityId }
    } = orders
    let dataLinkOrder = {}
    dataLinkOrder = {
      entity: entityId,
      ...form
    }
    dataLinkOrder = this.formatDataLink(dataLinkOrder)
    return dataLinkOrder
  }
  formatDataLink = (dataLink: any) => {
    const data: any = {}
    forIn(dataLink, (value, key) => {
      const lastIndex = key.lastIndexOf('_')
      data[lastIndex > -1 ? key.substring(lastIndex + 1) : key] = value
    })
    return data
  }

  getActionText = (tripType: string) => {
    const changeType = travelTypeCN2EN(tripType)
    switch (changeType) {
      case 'HOTEL':
        return i18n.get('订店')
      case 'FOOD':
        return i18n.get('订餐')
      case 'TAXI':
        return i18n.get('用车')
      default:
        return i18n.get('订票')
    }
  }

  // 订购文案
  getBookingBtnText = (tripType: string) => {
    const { showButton } = this.state
    if (!showButton) {
      return ''
    }

    const changeType = travelTypeCN2EN(tripType)

    switch (changeType) {
      case 'FLIGHT':
        return i18n.get('订机票')
      case 'HOTEL':
        return i18n.get('订酒店')
      case 'TRAIN':
        return i18n.get('订火车票')
      case 'TAXI':
        return ['DING_TALK', 'WEIXIN'].includes(window.__PLANTFORM__) ? '' : i18n.get('用车')
      // case 'FOOD':
      //   return i18n.get('订餐')
      default:
        return ''
    }
  }

  // 前往商城
  handleGoBooking = async () => {
    app.invokeService('@common:set:track', {
      key: 'mytrips_mall_click',
      actionName: '订购按钮点击量',
      from: 'other',
      type: this.type
    })
    const services = {
      token: () =>
        new Promise((resolve, reject) => {
          setTimeout(() => resolve({ RD: { c: 3 } }), 1000)
        })
    }
    const result = await app.request({
      type: this.TK || 'BUTTON_TRAVEL_ORDER',
      prepareResource: processMallResourceAuth,
      services: services
    })
  }

  handleOrderDetail = (value: any) => {
    app.open('@bill:DataLinkEntityTripOrderDetailModal', {
      field: value.entity.fields,
      value: { data: { dataLink: value } },
      title: i18n.get('订单详情')
    })
    app.invokeService('@common:set:track', {
      key: 'mytrips_order_view',
      actionName: '订单详情页pv',
      from: 'detail'
    })
  }

  handleBillDetail = (flowId: string) => {
    app.go('/requisitionToDetail/formRequisition/' + flowId)
  }

  handleRiskDetail = (riskContent: any) => {
    app.open('@bill:TripRiskModal', { riskContent })
  }

  /***订单数据有点问题，等待修复 */
  renderTravelers = () => {
    const { travelDetail, tripOrders } = this.state
    const { type, orderStatus, traveler, orders, departure } = travelDetail
    const buttonText = this.getBookingBtnText(type)
    const actionText = this.getActionText(type)
    return (
      <>
        <section className="trip-order">
          <div className="trip-order-title"> {i18n.get('出行人')}</div>
          {traveler?.length > 0 &&
            traveler.map((item: Traveler, i: number) => {
              const ownOrders = tripOrders?.[item.id]
              let orderStatusCls = ''
              let currentOrder = null
              if (ownOrders) {
                currentOrder = this.formatDataLink(ownOrders[0])
                orderStatusCls =
                  currentOrder['订单状态'] === '已出票' || currentOrder['订单状态'] === '出票'
                    ? 'trip-order-item-icon lightgreen'
                    : 'trip-order-item-icon lightgray'
              }
              return (
                <div className="trip-order-item" key={i}>
                  <div className="trip-order-item-name">{item.name}</div>
                  <div className="trip-order-item-dept">{this.getDeptName(item)}</div>
                  {currentOrder && <span className={orderStatusCls}>{orderStatus} </span>}
                  {!currentOrder && buttonText && (
                    <span className="trip-order-item-icon blue">{i18n.get(`待{__k0}`, { __k0: actionText })}</span>
                  )}
                  {currentOrder && (
                    <div className="order-information">
                      <div className="order-information-num">
                        {i18n.get(`订单号 {__k0}`, {
                          __k0: currentOrder['订单号']
                        })}
                      </div>
                      {/* {ownOrders?.length === 1 ? (
                        <span
                          className="order-information-detail"
                          onClick={() => this.handleOrderDetail(ownOrders?.[0])}
                        >
                          {i18n.get('订单详情')}
                        </span>
                      ) : (
                        <OrderListPopover orders={ownOrders} tripType={type} />
                      )} */}
                    </div>
                  )}
                </div>
              )
            })}
        </section>
      </>
    )
  }

  renderCurrentTravel = (type: string, name: string) => {
    let showTripTypeTxt: string = TripMapLists[type] || type
    const conventionTypeLists = ['飞机', '酒店', '火车', '用车', '餐饮', '通用']
    return (
      <section className="trip-detail">
        <div className="trip-detail-head">
          {type === '飞机' && <TwoToneGeneralAirplane className="trip-icon" />}
          {type === '酒店' && <TwoToneGeneralHotel className="trip-icon" />}
          {type === '火车' && <TwoToneGeneralTrain className="trip-icon" />}
          {type === '用车' && <TwoToneGeneralTaxi className="trip-icon" />}
          {type === '餐饮' && <TwoToneGeneralFood className="trip-icon" />}
          {type === '通用' && <img src={commonIcon} className="custom-icon" />}
          {!conventionTypeLists.includes(type) && <img src={commonIcon} className="custom-icon" />}
          <span className="trip-type-txt">{showTripTypeTxt}</span>
        </div>
        <div className="trip-detail-content">
          <div className="trip-detail-content-city">{name}</div>
          <div className="trip-detail-content-date">{this.renderDate()}</div>
        </div>
      </section>
    )
  }

  renderDate() {
    let dateString = ''
    const { customTrip, travelDetail } = this.state
    const { startTime, endTime, type } = travelDetail
    const typeList = ['餐饮', '用车', '通用', '酒店']
    if (customTrip?.dateTypeField?.dateType === 'multiple' || typeList.includes(type)) {
      const days = getDays(startTime, endTime)
      dateString = i18n.get(`{__k0}~{__k1} 共{__k2}天`, {
        __k0: moment(startTime).format(i18n.get('MM月DD日')),
        __k1: moment(endTime).format(i18n.get('MM月DD日')),
        __k2: days + 1
      })
      if (type === '酒店') {
        dateString = dateString + `${days}${i18n.get('晚')}`
      }
    } else {
      dateString = `${moment(startTime).format(i18n.get('MM月DD日'))}`
    }
    return <span>{dateString}</span>
  }

  // 备注
  renderRemake = () => {
    const {
      travelDetail: { remarks }
    } = this.state
    return (
      <section className="trip-warning">
        <div className="tips">
          <img src={SVG_WARING} />
          {i18n.get('行程单未同步')}
          <a onClick={() => this.handleRiskDetail(remarks)}>{i18n.get('查看')}</a>
          {i18n.get('，')}
          {i18n.get('请注意')}
        </div>
      </section>
    )
  }

  // 预算金额
  renderBudgetAmount = (budgetAmount: string) => {
    return (
      <section className="trip-bill">
        <h5>{i18n.get('餐饮金额')}</h5>
        <div className="option-line">
          <div className="grow">
            <div className="dis-f jc-e mb-4">
              <div className="em">
                <Money value={budgetAmount} />
              </div>
            </div>
            <div className="dis-f jc-e">
              <span>{i18n.get('总金额')}</span>
            </div>
          </div>
        </div>
      </section>
    )
  }
  // 单据列表
  renderBillDetail = () => {
    const {
      travelDetail: { flowDetail }
    } = this.state
    const applicationFormCode = get(flowDetail, 'flowCode')
    const tripStatus: string = getFlowState[get(flowDetail, 'state', '')]?.text
    // 单据状态分类
    const finishStatus = ['PROCESS', 'archived', 'paid']
    const unusualStatus = ['failure', 'nullify', 'receivingExcep', 'rejected']
    const todoStatus = ['draft', 'paying', 'receiving', 'sending']
    const processStatus = ['approving', 'pending']
    const cur_state = get(flowDetail, 'state', '')
    // 根据不同单据状态添加不同类名
    const tripStatusClassName = `status-icon ${finishStatus.includes(cur_state) ? 'lightgreen' : ''} ${unusualStatus.includes(cur_state) ? 'red' : ''
      } ${todoStatus.includes(cur_state) ? 'lightgray' : ''} ${processStatus.includes(cur_state) ? 'blue' : ''}`

    return (
      <section className="trip-bill">
        <div className="trip-bill-title">{i18n.get('关联单据')}</div>
        <div className="trip-bill-item" onClick={() => this.handleBillDetail(flowDetail.flowId)}>
          <span className={tripStatusClassName}>{tripStatus}</span>
          <div className="trip-bill-item-name">{get(flowDetail, 'title', '')}</div>
          <div className="trip-bill-item-content">
            <div className="trip-bill-item-child">
              <span className="trip-bill-item-dept">{i18n.currentLocale === 'en-US' ? flowDetail?.specificatioEnName || flowDetail?.specificationName : flowDetail?.specificationName}</span>
              <span className="trip-bill-item-code">{applicationFormCode}</span>
            </div>

            <Money className="trip-bill-money" value={get(flowDetail, `requisitionMoney.standard`, '')} />
          </div>
        </div>
      </section>
    )
  }

  render() {
    const { travelDetail } = this.state
    // 新行程详情
    const { type, name, traveler, orders, remarks, budgetAmount, active, flowDetail, dataLink } = travelDetail
    const buttonText = this.getBookingBtnText(type)
    const disabled = isBoolean(active) ? !active : false
    // const syncRemarks = remarks && remarks.indexOf('[未同步]') > -1
    const form: any = dataLink?.form
    const dataLinkRemarks = form?.[Object.keys(form).find(o => !!o.endsWith('备注'))!] || '' // @i18n-ignore
    const syncRemarks = dataLinkRemarks.indexOf('[未同步]') > -1
    return (
      <div className={styles['trip-detail-wrapper']}>
        {syncRemarks && this.renderRemake()}

        {disabled && <div className="status-disabled">{i18n.get('已停用')}</div>}

        {this.renderCurrentTravel(type, name)}

        <div className="other-info">
          {this.renderTravelers()}

          {type === '餐饮' && !!budgetAmount && this.renderBudgetAmount(budgetAmount)}

          {flowDetail && this.renderBillDetail()}
        </div>
        {buttonText && (
          <div className="footer">
            <Button category="primary" className="btn-confirm" disabled={disabled} onClick={this.handleGoBooking}>
              {i18n.get(`一键{__k0}`, { __k0: buttonText })}
            </Button>
            <div className="color-gray text">
              {i18n.get(`当前共 {__k0}/{__k1} 人已成功{__k2}`, {
                __k0: size(orders),
                __k1: traveler?.length,
                __k2: buttonText
              })}
            </div>
          </div>
        )}
      </div>
    )
  }
}
