import React, { PureComponent } from 'react'
import styles from './TripRiskModal.module.less'

interface Props {
  riskContent: any
}

export default class TripRiskModal extends PureComponent<Props> {
  handleCancel = () => {
    this.props.layer.emitCancel()
  }
  render() {
    const { riskContent } = this.props
    const value = riskContent.replace('[未同步]', '').replace('餐补标准:', '') // @i18n-ignore
    const allList = value.split(',')
    const list = allList.filter((item: any) => item.indexOf('差标未维护') > -1) // @i18n-ignore
    const other = allList.find((item: any) => item.indexOf('接口调用失败') > -1) // @i18n-ignore
    return (
      <div className={styles['trip-risk-wrapper']}>
        <div className="risk-header">{i18n.get('风险详情')}</div>
        <div className="risk-content">
          {list.length > 0 && <h3>{`当前行程有${list.length}个员工差标未维护`}</h3>}
          {list.length > 0 &&
            list.map((item: any, index: number) => {
              return <span>{`${index + 1}、${item.replace(':差标未维护', '的差标')}`}</span> // @i18n-ignore
            })}
          {other && <h3>{other}</h3>}
        </div>
        <div className="risk-footer" onClick={this.handleCancel}>
          {i18n.get('知道了')}
        </div>
      </div>
    )
  }
}
