/**************************************************
 * Created by nanyuanting<PERSON> on 10/07/2017 18:38.
 **************************************************/

const ID = '@bill'

export default {
  ID,
  SAVE_PAYEE: `${ID}/SAVE_PAYEE`,
  DEL_PAYEE: `${ID}/DEL_PAYEE`,
  GET_BANK_LIST: `${ID}/GET_BANK_LIST`,

  GET_SPECIFICATION_LIST: `${ID}/GET_SPECIFICATION_LIST`,
  GET_BILL_LIST: `${ID}/GET_BILL_LIST`,
  POST_PRINTJOB: `${ID}/POST_PRINTJOB`,
  EDIT_FLOW: `${ID}/EDIT_FLOW`,
  SAVE_AND_SUBMIT: `${ID}/SAVE_AND_SUBMIT`,
  SUBMIT_FLOW: `${ID}/SUBMIT_FLOW`,
  DELETE_FLOW: `${ID}/DELETE_FLOW`,
  RETRACT_FLOW: `${ID}/RETRACT_FLOW`,
  WITHDRAWN_FLOW: `${ID}/WITHDRAWN_FLOW`,
  COMMENT_FLOW: `${ID}/COMMENT_FLOW`,
  GET_FLOW: `${ID}/GET_FLOW`,
  GET_FLOW_LITE: `${ID}/GET_FLOW_LITE`,
  GET_FLOW_TRIPPLATFORM: `${ID}/GET_FLOW_TRIPPLATFORM`,

  FAV_PROJECTS: `${ID}/FAV_PROJECTS`,
  GET_TREVELMANAGEMENT_CONFIG: `${ID}/GET_TREVELMANAGEMENT_CONFIG`,
  CHECK_RETURN_CITY: `${ID}/CHECK_RETURN_CITY`,
  GET_CUSTOMTRAVELTYPE: `${ID}/GET_CUSTOMTRAVELTYPE`,
  PULL_TRAVEL_ORDER: `${ID}/PULL_TRAVEL_ORDER`,

  COMPUTED_FLOW_PLAN: `${ID}/COMPUTED_FLOW_PLAN`,
  LOANPACKAGE_LIST: `${ID}/LOANPACKAGE_LIST`,

  GET_QR_INVOICE_DATA: `${ID}/GET_QR_INVOICE_DATA`,
  GET_INPUT_INVOICE_DATA: `${ID}/GET_INPUT_INVOICE_DATA`,
  GET_QR_INVOICE_CAPTCHA: `${ID}/GET_QR_INVOICE_CAPTCHA`,
  GET_INPUT_INVOICE_CAPTCHA: `${ID}/GET_INPUT_INVOICE_CAPTCHA`,
  GET_APPLY_BY_EXPENSE: `${ID}/GET_APPLY_BY_EXPENSE`,
  GET_APPLY_BY_EXPENSE_FILTER: `${ID}/GET_APPLY_BY_EXPENSE_FILTER`,
  LOAD_MORE_APPLY_BY_EXPENSE: `${ID}/LOAD_MORE_APPLY_BY_EXPENSE`,
  GET_APPLY_EVENT: `${ID}/GET_APPLY_EVENT`,
  GET_EXPENSE_LINK: `${ID}/GET_EXPENSE_LINK`,
  GET_CURRENT_REQUISITION_INFO: `${ID}/GET_CURRENT_REQUISITION_INFO`,

  SET_REQUISITION_INFO: `${ID}/SET_REQUISITION_INFO`,

  BILL_REMIND: `${ID}/BILL_REMIND`,
  GET_BATCH_AUTO_CAL_RESULT: `${ID}/GET_BATCH_AUTO_CAL_RESULT`,
  GET_AUTO_CAL_FIELDS: `${ID}/GET_AUTO_CAL_FIELDS`,

  GET_CUSTOMIZE_CAL_RESULT: `${ID}/GET_CUSTOMIZE_CAL_RESULT`,
  GET_DIMENSION_BY_ID: `${ID}/GET_DIMENSION_BY_ID`,
  GET_DEPARTMENT_BY_ID: `${ID}/GET_DEPARTMENT_BY_ID`,
  GET_ENUMITEM_BY_ID: `${ID}/GET_ENUMITEM_BY_ID`,

  GET_ENTITY_VALUE_BY_ID: `${ID}/GET_ENTITY_VALUE_BY_ID`,
  GET_ENTITY_FORM_BY_ID: `${ID}/GET_ENTITY_FORM_BY_ID`,
  GET_ENTITY_VALUE_LIST_BY_ID: `${ID}/GET_ENTITY_VALUE_LIST_BY_ID`,
  GET_ENTITY_VALUE_LIST_V2: `${ID}/GET_ENTITY_VALUE_LIST_V2`,
  GET_FIELD_MAPPING_BY_ID: `${ID}/GET_FIELD_MAPPING_BY_ID`,
  GET_ENTITY_LIST_BY_PLATFORM: `${ID}/GET_ENTITY_LIST_BY_PLATFORM`,

  SAVE_CURRENT_FLOW: `${ID}/SAVE_CURRENT_FLOW`,
  SAVE_COPIED_VALUE: `${ID}/SAVE_COPIED_VALUE`,

  SAVE_CURRENT_PAYEES: `${ID}/SAVE_CURRENT_PAYEES`,

  GET_EXPRESS_NUMS: `${ID}/GET_EXPRESS_NUMS`,
  UPDATE_EXPRESS_NUMS: `${ID}/UPDATE_EXPRESS_NUMS`,
  REWRITE_EXPRESS_NUMS: `${ID}/REWRITE_EXPRESS_NUMS`,
  GET_EXPRESS: `${ID}/GET_EXPRESS`,
  GET_FEETYPE_IMPORT_RULE_BY_ID: `${ID}/GET_FEETYPE_IMPORT_RULE_BY_ID`,
  GET_INVOICE_STATE_BYID: `${ID}/GET_INVOICE_STATE_BYID`,
  GET_VISIBLE_DATALINK_FEETYPE: `${ID}/GET_VISIBLE_DATALINK_FEETYPE`,
  GET_ISEBOT_SHIFT: `${ID}/GET_ISEBOT_SHIFT`,
  GET_FLOW_SUBMIT_CHECK_STATE: `${ID}/GET_FLOW_SUBMIT_CHECK_STATE`,
  GET_CURRENT_BACKLOG: `${ID}/GET_CURRENT_BACKLOG`,
  GET_LEDGER_PLANED_BYID: `${ID}/GET_LEDGER_PLANED_BYID`,
  GET_LEDGER_RELATION_LIST: `${ID}/GET_LEDGER_RELATION_LIST`,
  GET_ENTITY_BY_PLATFORM_NAME: `${ID}/GET_ENTITY_BY_PLATFORM_NAME`,
  GET_OBJECT_FORM_BY_ID: `${ID}/GET_OBJECT_FORM_BY_ID`,
  GET_RECORDLINK: `${ID}/GET_RECORDLINK`,
  SET_PAYEE_COMPONENT_VISIBILITY: `${ID}/SET_PAYEE_COMPONENT_VISIBILITY`,
  GET_DATALLINK_TEMPLATE_BYID: `${ID}/GET_DATALLINK_TEMPLATE_BYID`,
  GET_DATALLINK_TEMPLATE_BYIDS: `${ID}/GET_DATALLINK_TEMPLATE_BYIDS`,
  GET_EXPRESS_BUDGET_CHILD_LIST: `${ID}GET_EXPRESS_BUDGET_CHILD_LIST`,
  GET_LOAN_PACKAGE_BY_FLOWID: `${ID}GET_LOAN_PACKAGE_BY_FLOWID`,
  GET_CALCULATE_FIELD: `${ID}/GET_CALCULATE_FIELD`,
  GET_ASSIGNMENT_RULE_BY_ID: `${ID}/GET_ASSIGNMENT_RULE_BY_ID`,
  VALIDATE_ERROR: `${ID}/VALIDATE_ERROR`,
  GET_SPECIFICATION_BY_IDS: `${ID}/GET_SPECIFICATION_BY_IDS`,
  CHECK_LOAN_PACKAGE_EXIST: `${ID}/CHECK_LOAN_PACKAGE_EXIST`,
  GET_EXPENSE_LINK_DETAILS: `${ID}/GET_EXPENSE_LINK_DETAILS`,
  SAVE_EXPENSE_LINK_RECORD_DETAILS: `${ID}/SAVE_EXPENSE_LINK_RECORD_DETAILS`,
  SAVE_FEETYPE_VISIBLE_LIST: `${ID}/SAVE_FEETYPE_VISIBLE_LIST`,

  GET_ACTIVE_CREDIT_RULES_GROUP: `${ID}/GET_ACTIVE_CREDIT_RULES_GROUP`,
  GET_BILL_NOTES: `${ID}/GET_BILL_NOTES`,
  POST_ADD_NOTE: `${ID}/POST_ADD_NOTE`,
  CHANGE_ADD_NOTE_MODE: `${ID}/CHANGE_ADD_NOTE_MODE`,
  SEARCH_NOTE_FORM_HISTORY: `${ID}/SEARCH_NOTE_FORM_HISTORY`,
  CHANGE_STATUS_OF_SHOWBILLNOTESINHISTORY: `${ID}/CHANGE_STATUS_OF_SHOWBILLNOTESINHISTORY`,
  GET_MY_CREDIT_POINT: `${ID}/GET_MY_CREDIT_POINT`,

  SAVE_WRITTENOFF_SUMMARY: `${ID}/SAVE_WRITTENOFF_SUMMARY`,
  CLEAR_WRITTENOFF_SUMMARY: `${ID}/CLEAR_WRITTENOFF_SUMMARY`,

  SAVE_CROSS_WRITTENOFF_SUMMARY: `${ID}/SAVE_CROSS_WRITTENOFF_SUMMARY`,
  CLEAR_CROSS_WRITTENOFF_SUMMARY: `${ID}/CLEAR_CROSS_WRITTENOFF_SUMMARY`,

  GET_TRAVEL_BACK_INFO: `${ID}/GET_TRAVEL_BACK_INFO`,
  GET_PAYEE_CONFIG_CHECK: `${ID}/GET_PAYEE_CONFIG_CHECK`,
  UPDATE_DIMENTION_CURRECY: `${ID}/UPDATE_DIMENTION_CURRECY`,
  GET_LOAN_LOGS: `${ID}/GET_LOAN_LOGS`,
  SAVE_TRIPS_INFO: `${ID}/SAVE_TRIPS_INFO`,
  GET_FIELDS_GROUP_DATA: `${ID}/GET_FIELDS_GROUP_DATA`,
  // GET_ALL_ORDERS: `${ID}/GET_ALL_ORDERS`,
  AUTO_GENERATION_FEE_DETAIL_RULES: `${ID}/AUTO_GENERATION_FEE_DETAIL_RULES`,
  AUTO_GENERATION_FEE_DETAIL: `${ID}/AUTO_GENERATION_FEE_DETAIL`,
  SET_PRINT_PREVIEW_URL: `${ID}/SET_PRINT_PREVIEW_URL`,
  // GET_ALL_ORDERS: `${ID}/GET_ALL_ORDERS`,
  GET_INVOICE_REASON: `${ID}/GET_INVOICE_REASON`,
  SEARCH_EXTENSION_CENTER_CONFIG: `${ID}/SEARCH_EXTENSION_CENTER_CONFIG`,
  GET_FEE_TYPE_CHANGE: `${ID}/GET_FEE_TYPE_CHANGE`,
  GET_DEFAULT_PAYEE: `${ID}/GET_DEFAULT_PAYEE`,
  GET_DELEGATE_CONFIG: `${ID}/GET_DELEGATE_CONFIG`,
  SET_SUBMITTER_DATA: `${ID}/SET_SUBMITTER_DATA`,
  GET_CAlCULATE_CORP_ID_WHITELIST: `${ID}/GET_CAlCULATE_CORP_ID_WHITELIST`,
  GET_FLOW_LINK_LIST: `${ID}/GET_FLOW_LINK_LIST`,
  CALCULATE_RECORD_LINK: `${ID}/CALCULATE_RECORD_LINK`,
  APPEND_REPAYMENT_LIST: `${ID}/APPEND_REPAYMENT_LIST`,
  NULLIFY_FLOW: `${ID}/NULLIFY_FLOW`,
  GET_NULLIFY_RULE_BYSPECID: `${ID}/GET_NULLIFY_RULE_BYSPECID`,
  GET_NULLIFY_RULE_BY_FLOWID: `${ID}/GET_NULLIFY_RULE_BY_FLOWID`,
  SET_SYNCTRIPMANUALLY: `${ID}/SET_SYNCTRIPMANUALLY`,
  GET_SYNCTRIP_RESULT: `${ID}/GET_SYNCTRIP_RESULT`,
  GET_SYNC_VALID: `${ID}/GET_SYNC_VALID`,
  GET_INVOICE_CORPORATION: `${ID}/GET_INVOICE_CORPORATION`,
  SAVE_HISTORY_CURRECY_RATES: `${ID}/SAVE_HISTORY_CURRECY_RATES`,
  ADDORCANCELCOLLECT: `${ID}/ADDORCANCELCOLLECT`,
  GET_TRAVEL_BACK_INFO_V3: `${ID}/GET_TRAVEL_BACK_INFO_V3`,
  GET_MY_BILLS_ENTRY:`${ID}/GET_MY_BILLS_ENTRY`,
  UPDATE_BILL_ADDEND_INFO: `${ID}/UPDATE_BILL_ADDEND_INFO`,
  SET_CURRENCY_RATES: `${ID}/SET_CURRENCY_RATES`,
}
