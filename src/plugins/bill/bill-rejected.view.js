import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
const { Box, Footer } = app.require('@elements/layout/Box')
const ETabs = app.require('@elements/EUITabs')
const parseAsFormValue = app.require('@lib/parser.parseAsFormValue')
const BillApprove = app.require('@elements/puppet/Approve/BillApprove')
const BillExpress = app.require('@elements/puppet/Express/BillExpress')
const RiskNotice = app.require('@elements/puppet/RiskNotice')
import RiskAlert from '../../elements/puppet/RiskNotice/RiskAlert'
import LogsCardView from './panels/LogsCardView'
import BillInfoEditable from './panels/BillInfoEditable'
import ActionsPanelEditable from './panels/ActionsPanelEditable'
import { Dialog } from '@hose/eui-mobile'
import { clone, isEqual } from 'lodash'
import { publicInitiFlow, fnDelegatorList, resetRiskNotice } from './utils/draftAndRejectedUtil'
import { checkIsRemuneration, fixRemunerationSpecification } from './utils/billUtils'
import { fnIsRiskError } from './utils/formatRiskData.formatRiskNotice'

@EnhanceConnect(
  state => ({
    current_flow: state['@common'].current_flow,
    delegators: state['@common'].delegators,
    expressPower: state['@common'].powers.Express,
    packageList: state['@loanpackage'].packageList,
    specification_current: state['@home'].specification_current,
    userInfo: state['@common'].me_info,
    globalFieldsMap: state['@common'].baseDataProperties.baseDataPropertiesMap,
    specification_group: state['@home'].specificationWithVersion.specification_group,
    RiskPromptOptimization: state['@common'].powers.RiskPromptOptimization
  }),
  {},
  '@bill/current/page/state'
)
@EnhanceTitleHook()
export default class BillRejected extends PureComponent {
  bus = new MessageCenter()

  state = {
    show: false,
    activeKey: 'detail',
    isBillSumbitLock: false
  }

  componentDidMount() {
    this.bus.on('template:change', this.handleTemplateChange)
  }

  async componentWillMount() {
    this._$UUID = String(Math.random())
    api.backControl.create(this._$UUID, () => {
      this.backHookCallback()
    })
    let { setState, delegators, params } = this.props
    let { id, type, delegatorId, canEditExpress } = params
    setState({ flowId: id, formType: type, data: null, canEditExpress })
    api.dataLoader('@common.delegators').load()
    await api.dataLoader('@common.baseDataProperties').load()
    api.invokeService('@common:list:tripTypes')
    if (id) {
      this.initiFlow(id, type)
    } else {
      this.setState({ show: true })
    }
    if (delegatorId) {
      let delegator = delegators.filter(o => o.id === delegatorId)[0]
      setState({ delegatorId: delegator })
    }
  }

  initiFlow = (id, type) => {
    const { globalFieldsMap = {}, specification_group, setState, isModifyBill, RiskPromptOptimization } = this.props
    publicInitiFlow({
      id,
      type,
      globalFieldsMap,
      specification_group,
      setState,
      bus: this.bus,
      isModifyBill,
      RiskPromptOptimization,
      isForbid: true
    }).then(currentSpecification => {
      const isRemuneration = checkIsRemuneration(currentSpecification?.id)
      isRemuneration && fixRemunerationSpecification(currentSpecification)
      api.invokeService('@home:save:specification', currentSpecification).then(_ => {
        this.setState({ show: true })
      })
    })
  }

  componentWillReceiveProps(nextProps) {
    let { formType } = this.props.state
    let { current_flow } = nextProps
    if (this.props.current_flow !== current_flow && current_flow?.formType === formType) {
      if (nextProps.current_flow) {
        if (nextProps.current_flow.plan) {
          if (nextProps.expressPower) {
            // 判断是否有寄送节点
            const { nodes } = nextProps.current_flow.plan
            nodes.forEach(node => {
              if (node.expressConfig && node.expressConfig.type === 'send') this.hasExpressNode = true
            })
          }
          this.props.setState({
            data: nextProps.current_flow,
            value: parseAsFormValue(nextProps.current_flow)
          })
        }
      }
    }
  }

  componentWillUnmount() {
    api.backControl.remove(this._$UUID)
    let { setState } = this.props
    setState({ flowId: null, formType: null, data: null, noticeList: null, flowRiskList: null })
    this.bus.un('template:change', this.handleTemplateChange)
    api.invokeService('@common:clear:flow:detail:info')
  }

  handleTemplateChange = writtenOff => {
    const { data } = this.props.state
    if (data) {
      data.writtenOff = writtenOff
    }
    this.props.setState({ data })
  }

  backHookCallback = () => {
    if (this.bus?.getValue) {
      this.bus?.getValue().then(newValue => {
        if (newValue?.details?.length === 0) {
          newValue.details = undefined
        }
        const oldValue = this.formatOldValueStr(newValue)
        if (!isEqual(oldValue, newValue) && !this.state.isBillSumbitLock) {
          this.showModifyModal()
        } else {
          this.closeModal()
          api.backControl.remove(this._$UUID)
          api.backControl.invoke()
        }
      })
    } else {
      this.closeModal()
      api.backControl.remove(this._$UUID)
      api.backControl.invoke()
    }
  }

  formatOldValueStr = newValue => {
    let oldValue = api.getState('@common.current_flow')
    let cloneOldValue = clone(oldValue)
    let oldFormValue = cloneOldValue?.form || {}
    let compareObj = {}
    for (let key in newValue) {
      compareObj[key] = oldFormValue[key]
    }
    return compareObj
  }

  showModifyModal = () => {
    if (this.showHandler) return
    const { flowId = getBillKey() } = this.props.state
    this.showHandler = Dialog.show({
      title: i18n.get('提示'),
      content: i18n.get('有内容未保存，是否要保存到草稿？'),
      closeOnAction: true,
      actions: [
        {
          key: 'save_exit',
          text: i18n.get('保存草稿并退出'),
          confirm: true
        },
        {
          key: 'save_no',
          text: i18n.get('不保存'),
          cancel: true
        },
        {
          key: 'cancel',
          text: i18n.get('取消'),
          cancel: true
        }
      ],
      onAction: action => {
        switch (action.key) {
          case 'save_exit':
            this.bus.emit('footer:action:save')
            break
          case 'save_no':
            localStorage.removeItem(flowId)
            api.backControl.remove(this._$UUID)
            api.backControl.invoke()
            break
          case 'cancel':
            break
        }
      },
      onClose: () => {
        this.showHandler = null
      }
    })
  }

  closeModal = () => {
    const { flowId = getBillKey() } = this.props.state
    localStorage.removeItem(flowId)
    Dialog.clear()
    const modal = document.getElementsByClassName('am-modal-mask')
    if (modal && modal.length) {
      const layer = modal[0].parentNode && modal[0].parentNode.parentNode
      if (layer && layer.getAttribute('id') && layer.getAttribute('id').indexOf('am-modal-container-') > -1) {
        layer.parentNode.removeChild(layer)
      }
    }
  }

  onTabClick = activeKey => {
    this.setState({ activeKey })
    this.bus.emit('on:tab:change')
  }

  handleGoToHistory = () => {
    this.setState({ activeKey: 'approve' })
    this.bus.emit('history:type:change', 'comment')
  }

  handleRiskNoticeChange = (external, newTemplate) => {
    let { globalFieldsMap = {}, setState, RiskPromptOptimization } = this.props
    let {
      data: { form },
      template
    } = this.props.state
    resetRiskNotice({
      external,
      newTemplate,
      form,
      setState,
      template,
      globalFieldsMap,
      bus: this.bus,
      RiskPromptOptimization,
      isForbid: true
    })
  }

  handleSubmitResult = flow => {
    const { id, formType } = flow
    this.initiFlow(id, formType)
  }

  isShowDialog = (entries, length) => {
    //只有从首页进入和钉钉消息进入的单据才弹出是否继续编辑对话框，其余情况直接用localStorage数据替换
    return entries[entries.length - 1].pathname.startsWith('/rejected/')
  }

  changeSumbitLock = value => {
    this.setState({ isBillSumbitLock: value })
  }

  render() {
    let { data, formType, noticeList = [], flowRiskList, canEditExpress, value } = this.props.state
    if (!this.state.show) return null
    let { delegators, userInfo: user, expressPower, history, specification_current, isModifyBill } = this.props
    const { entries, length } = history
    if (!data) {
      return null
    }
    if (!data.active) {
      return (
        <Dialog title={i18n.get('友情提示')} visible={true}>
          <p>{i18n.get('您所查看的单据已删除～～')}</p>
        </Dialog>
      )
    }
    const invoiceRiskData = noticeList?.originalRisk
    const { activeKey } = this.state
    let { writtenOff } = data
    let userInfo = data && data.ownerId
    let tags = userInfo && fnDelegatorList(userInfo, delegators, specification_current)
    const riskType = fnIsRiskError(flowRiskList) ? 'error' : 'warning'
    let tabData = [
      {
        tab: i18n.get('单据详情'),
        key: 'detail',
        children: (
          <div className="flex ovr-y-a h-100p">
            <div className="inertial-rolling h-100-percent w-100p h-100p">
              {/* <RiskNotice notices={noticeList || []} type={riskType} /> */}
              <RiskAlert
                billDetails={data}
                riskWarningV2={invoiceRiskData?.riskWarningV2}
                flowAllRiskType={invoiceRiskData || {}}
              />
              <LogsCardView dataSource={data} onLogMsgClick={this.handleGoToHistory} userInfo={user} />
              <BillInfoEditable
                notScroll
                type={formType}
                isRejected={true}
                bus={this.bus}
                showDialg={this.isShowDialog(entries, length)}
                value={value}
                tags={tags}
                writtenOff={writtenOff}
                external={flowRiskList}
                submitter={value.submitterId}
                handleRiskNoticeChange={this.handleRiskNoticeChange}
                invoiceRiskData={invoiceRiskData}
                isModifyBill={isModifyBill}
                remunerationData={data}
              />
            </div>
          </div>
        )
      }
    ]

    tabData.push({
      tab: i18n.get('审批流程'),
      key: 'approve',
      children: <BillApprove value={data} bus={this.bus} />
    })

    if (expressPower && this.hasExpressNode) {
      tabData.push({
        tab: i18n.get('寄送信息'),
        key: 'bill-express',
        children: <BillExpress flow={data} canEditExpress={canEditExpress} />
      })
    }

    return (
      <Box bus={this.bus}>
        <Box>
          <HeaderTab activeKey={activeKey} tabData={tabData} onTabClick={this.onTabClick} />
        </Box>
        <Footer>
          <ActionsPanelEditable
            scene={'OWNER'}
            specification_current={specification_current}
            handleSubmitResult={this.handleSubmitResult}
            isBillSumbitLock={this.state.isBillSumbitLock}
            changeSumbitLock={this.changeSumbitLock}
          />
        </Footer>
      </Box>
    )
  }
}

function HeaderTab(props) {
  let { tabData, activeKey, onTabClick } = props
  return (
    <div className="layout-content-wrapper">
      <ETabs activeKey={activeKey} dataSource={tabData} onChange={onTabClick} />
    </div>
  )
}
