.check-payee-action-wrapper {
  width: 100%;
  height: 90px;
  position: fixed;
  bottom: 27px;
  display: flex;
  :global {
    .wrap {
      flex: 1;
      padding: 14px;
      //.am-button:after {
      //  border : none;
      //}

      .content {
        height: 100%;
        line-height: 1;
        display: flex;
        background-color: var(--brand-base);
        justify-content: center;
        align-items: center;
        font-size: 32px;
        color: #FFFFFF;
        div:first-child {
          margin-right: 8px;
          img {
            width: 44px;
            height: 44px;
          }
        }
      }
    }
  }

}

.can-edit {
  color: rgba(0, 0, 0, 0.65);
  text-align: center;
  margin-top: 20px;
}

.check-payment-modal-alert {
  height: 120px;
  background-color: #fff7e6;
  color: rgba(0, 0, 0, 0.65);
  display: flex;
  align-items: center;
  padding-left: 32px;
  font-size: 28px;
  :global {
    img {
      margin-right: 10px;
      width: 30px;
    }
  }
}

.check-body {
  margin: 0 32px;
  :global {
    .am-list-item {
      padding-left: 0
    }
    .am-list-line {
      padding-right: 0
    }
  }
}