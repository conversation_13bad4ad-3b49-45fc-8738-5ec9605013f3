/**************************************
 * Created By LinK On 2020/9/1 16:08.
 **************************************/

import React, { Component } from 'react'
import styles from './mutiCurrencyWrittenOffModal.module.less'

interface Props extends StringAnyProps {
  cardConfigs: any
  data: any
  layer: any
}

export default class MutiCurrencyWrittenOffModal extends Component<Props, StringAnyProps> {

  handleCancel = () => {
    this.props.layer.emitOk('cancel')
  }

  handleOk = () => {
    const { data } = this.state
    this.props.layer.emitOk(data)
  }

  componentWillUnmount() {
    this.props.layer.emitOk('cancel')
  }

  renderActionBar = () => {
    return (
      <div className="cardEditModal-actionBar">
        <div className="cardEditModal-actionBar-btn grayout" onClick={this.handleCancel}>
          {i18n.get('取消')}
        </div>
        <span className="cardEditModal-title">{i18n.get('查看明细')}</span>
        <div className="cardEditModal-actionBar-btn"/>
      </div>
    )
  }

  render() {
    const { content } = this.props
    return (
      <div className={styles['mutiCurrencyWrittenOffModal-wrap']}>
        {this.renderActionBar()}
        <div className='mutiCurrencyWrittenOffModal-content'>{content}</div>
      </div>
    )
  }
}
