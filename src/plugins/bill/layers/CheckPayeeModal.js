import { app } from '@ekuaibao/whispered'
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/10/25.
 */
import React, { PureComponent } from 'react'
// import EnhanceFormCreate from '../../../elements/enhance/enhance-form-create'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
// import BankCard from '../../basic-elements/bank-card'
const BankCard = app.require('@basic-elements/bank-card')
// import InputWrapper from '../../../elements/form/input-wrapper'
const InputWrapper = app.require('@elements/form/input-wrapper')
import { List, Picker, Button } from 'antd-mobile'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
// import LayerContainer from '../../basic-elements/layer-container'
const LayerContainer = api.require('@basic-elements/layer-container')
// import SVG_SAVE_O from '../../../images/save.svg'
const SVG_SAVE_O = app.require('@images/save.svg')
import styles from './CheckPayeeModal.module.less'
import SVG_MESSAGE_WARN from '../images/message-warn.svg'
import _uniqBy from 'lodash/uniqBy'
import { get } from 'lodash'

@EnhanceFormCreate()
@EnhanceConnect(state => ({
  certificateTypeList: state['@common'].CertificateTypeList
}))
export default class CheckPayeeModal extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      showAddInfo: false,
      payeeInfo: {},
      payeeList: _uniqBy(props.payeeIdList, 'id') || []
    }
    api.invokeService(`@common:get:CertificateTypeList`)
    props.overrideGetResult(this.getResult)
    this.finishComplementPayee = []
  }

  getResult = () => {
    return this.result
  }

  checkcertificateNo = (rule, value, callback) => {
    const { form } = this.props
    const certificateType = form.getFieldValue('certificateType') && form.getFieldValue('certificateType')[0]
    if (certificateType === '01' && value) {
      if (!value.length) return callback(i18n.get('身份证号不能为空'))
      if (value.length > 18) return callback(i18n.get('身份证号不能超过18位'))
      if (!/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
        return callback(i18n.get('身份证号格式不正确'))
      }
    }
    callback()
  }

  update(payeeId) {
    if (!payeeId) {
      return
    }
    const { billsBus, formValue } = this.props
    const { details } = formValue
    details.length > 0 &&
      details.forEach(item => {
        if (item.feeTypeForm && get(item, 'feeTypeForm.feeDetailPayeeId.id') === payeeId.id) {
          item.feeTypeForm.feeDetailPayeeId = payeeId
        }
      })
    if (billsBus) {
      billsBus.setFieldsValue({ details: [...details] })
    }
  }

  handleSubmit = () => {
    const { value, canEdit, multiplePayeesMode } = this.props
    const payeeInfo = multiplePayeesMode ? this.state.payeeInfo : value
    const { id } = payeeInfo
    if (canEdit) {
      this.props.form.validateFields((error, values) => {
        if (error) {
          return
        }
        let { certificateType, certificateNo } = values

        api
          .invokeService('@common:set:certificate', {
            id,
            certificateNo,
            certificateType: certificateType[0]
          })
          .then(resp => {
            this.result = resp.value
            const { payeeList } = this.state
            const updatePayeeList = payeeList.filter(item => item.id !== id)
            this.setState({ payeeList: updatePayeeList }, () => {
              //  多收款人模式下补充完所有的收款人信息方可提交
              if (multiplePayeesMode) {
                this.setState({ showAddInfo: false, payeeInfo: {} })
                this.finishComplementPayee.push(resp.value)
                this.update(resp.value)
                if (!updatePayeeList.length) {
                  this.props.layer.emitOk(this.finishComplementPayee)
                }
              } else {
                this.props.layer.emitOk()
              }
            })
          })
      })
    } else {
      this.props.layer.emitOk()
    }
  }

  renderSinglePayee = () => {
    const { value, form, certificateTypeList, canEdit, multiplePayeesMode } = this.props
    const list = certificateTypeList.map(v => ({
      value: v.code,
      label: i18n.get(v.name)
    }))
    const payee = multiplePayeesMode ? this.state.payeeInfo : value
    return (
      <>
        <BankCard dataSource={payee} key={payee.id} />
        {canEdit ? (
          <div className={styles['check-body']}>
            <Picker
              cols={1}
              extra={<span className="placeholder-color">{i18n.get('请选择证件类型')}</span>}
              data={list}
              {...form.getFieldProps('certificateType', {
                rules: [{ required: true }]
              })}
            >
              <List.Item arrow="horizontal">{i18n.get('证件类型')}</List.Item>
            </Picker>
            <InputWrapper
              form={form}
              name={'certificateNo'}
              disabled={!form.getFieldValue('certificateType')}
              placeholder={i18n.get('请输入证件号码')}
              {...form.getFieldProps('certificateNo', {
                rules: [
                  { required: true, message: i18n.get('请输入账号') },
                  { max: 100, message: i18n.get('账号不能超过100个字') },
                  { validator: this.checkcertificateNo }
                ]
              })}
            >
              {i18n.get('证件号码')}
            </InputWrapper>
          </div>
        ) : (
          <div className={styles['can-edit']}>{i18n.get('此账户无编辑权限，请通知所有者补充证件信息')}</div>
        )}
        <div className={styles['check-payee-action-wrapper']}>
          <div className="wrap">
            <Button onClick={this.handleSubmit}>
              <div className="content">
                <div>
                  <img src={SVG_SAVE_O} />
                </div>
                <div>{canEdit ? i18n.get('保存') : i18n.get('取消')}</div>
              </div>
            </Button>
          </div>
        </div>
      </>
    )
  }

  handleShowAddInfo = data => {
    this.setState({ showAddInfo: true, payeeInfo: data })
  }

  renderMultiplePayee = () => {
    const { multiplePayeesMode } = this.props
    return this.state.payeeList.map(item => {
      return (
        <BankCard
          dataSource={item}
          key={item.id}
          shouldComplement={multiplePayeesMode}
          onLineClick={this.handleShowAddInfo}
        />
      )
    })
  }

  render() {
    const { multiplePayeesMode } = this.props
    const showMultiplePayees = multiplePayeesMode && !this.state.showAddInfo
    return (
      <LayerContainer>
        <div className="display-flex">
          <div className={styles['check-payment-modal-alert']}>
            <img className="img" src={SVG_MESSAGE_WARN} />
            <div>{i18n.get('为保证支付成功，请补充收款信息中的证件信息')}</div>
          </div>
          {showMultiplePayees ? this.renderMultiplePayee() : this.renderSinglePayee()}
        </div>
      </LayerContainer>
    )
  }
}
