/**************************************
 * Created By LinK On 2019/1/24 14:29.
 **************************************/
import React, { PureComponent } from 'react'
import { EnhanceModal, ILayerProps } from '@ekuaibao/enhance-layer-manager-mobile'
import './ImportTripOrderConfirmModal.less'

interface Props {
  content: string
  headerTitle:string
}

export default class ImportTripOrderConfirmModal extends PureComponent<Props & ILayerProps> {

  handleCancel = () => {
    this.props.layer.emitOk({ type: 'cancel' })
  }

  handleOk = () => {
    this.props.layer.emitOk({ type: 'ok' })
  }

  render() {
    const {content,headerTitle} = this.props
    return (
      <div className='import-trip-order-confirm-modal-wrapper'>
         <div className="header-wrapper">
          <span className="title">{headerTitle}</span>
        </div>
        <div className="body-wrapper" style={{ maxHeight: 300 }}>
          {content}
        </div>
        <div className="footer-wrapper">
          <div className="btn" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </div>
          <div className="btn" onClick={this.handleOk}>
            {i18n.get('确定')}
          </div>
        </div>
      </div>
    )
  }
}
