@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.addPayPlanModal-wrap {
  overflow-y: auto;
  height: 100%;
  background-color: @color-bg-1;
  position: relative;
  :global {
    .recipt-wrap{
      margin-top: 32px;
      padding: 16px 16px 16px 32px;
      background-color: #ffffff;
      .recipt-title{
        color: rgba(29, 43, 61, 0.75);
      }
      .receipt-list {
        margin-top: @space-2;
        .receipt-item{
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-right: @space-5;
          height: 64px;
        }
      }
      .link{
        display: block;
        color:var(--brand-base);
      }
      .recipt-cont{
        padding: 16px 0px;
        text-align: center;
      }
    }
    .addPayPlanModal-btn {
      .font-size-3;
      color: @color-black-1;
      height: 80px;
      line-height: 80px;
      margin: 0 @space-6 @space-4;
      border-radius: 8px;
      background-color: @color-white-1;
      text-align: center;
      user-select: none;
      &.addPayPlanModal-btn-primary {
        margin-top: @space-6;
        color: @color-white-1;
        background-color: @color-brand-2;
      }
    }
    .addPayPlanModal-summary {
      background-color: @color-white-1;
      margin-bottom: @space-4;
      padding: @space-6 0;
      border-bottom: 2px solid @color-line-2;
      .billPayPlan-header-forfix {
        margin-bottom: 0;
      }
    }
    .addPayPlanModal-form-title {
      background-color: @color-white-1;
      padding-left: @space-6;
      border-top: 2px solid @color-line-2;
      border-bottom: 2px solid @color-line-2;
      .font-size-3;
      .font-weight-3;
      height: 100px;
      line-height: 100px;
    }
    .addPayPlanModal-form {
      background-color: @color-white-1;
      .formitem-wrapper-forFix {
        .am-list {
          border: none !important;
        }
        .am-list-item {
          &::after {
            display: none;
          }
        }
      }
    }
  }
}