import { app, app as api } from '@ekuaibao/whispered'
import styles from './SelectAssociatedDataLinkModal.module.less'
import React, { PureComponent } from 'react'
import {
  formatEntityList,
  getNameCodeSearchKey,
  filterDataLinkUseCount,
  handleDataLinkUseCount
} from '../utils/dataLinkUtils'
import {
  getDataLinkListTitle,
  getDataLinkListDesc,
  getDataLinkListReplenish,
  getDataLinkListSupple,
  getDataLinkAvatar
} from '../elements/datalink/dataLinkUtil'
import { EnhanceConnect } from '@ekuaibao/store'
import {
  OutlinedTipsClose,
  FilledDirectionExpandDown,
  FilledDirectionExpandUp,
  OutlinedEditSearch,
  OutlinedEditFilter,
  FilledGeneralCollect
} from '@hose/eui-icons'
import { get, debounce } from 'lodash'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
const { getStrLastWord } = app.require('@components/utils/fnDataLinkUtil')
import {
  Dialog,
  Button,
  Popup as PopupEUI,
  NavBar,
  Selector,
  List,
  Checkbox,
  SearchBar,
  Avatar
} from '@hose/eui-mobile'
const packageSize = 99
const packageCount = 999
const tripOrderPackageSize = 9999 //行程订单一次性取值
import { QuerySelect } from 'ekbc-query-builder'
import Popup from '@ekuaibao/popup-mobile/esm/index'
import FilterForm from '../elements/datalink/datalink-detail-modal/elements/FilterForm'
import { isString } from '@ekuaibao/helpers'
import { selectorOptions } from './SelectAssociatedDataLinkStep1Modal'
const SkeletonListEUI = app.require('@home5/SkeletonList')
const EmptyWidget = api.require('@home5/EmptyWidget')

@EnhanceTitleHook()
@EnhanceConnect(state => ({
  powerCodeMap: state['@common'].powers.powerCodeMap
}))
export default class SelectAssociatedDataLinkStep2Modal extends PureComponent {
  constructor(props) {
    super(props)
    const { sourceEntity } = props
    this.state = {
      listData: [],
      selectedList: {},
      entityId: props.id,
      searchValue: '',
      isLoading: false,
      hasMore: true,
      isAllChecked: false,
      template: {},
      start: 0,
      loading: false,
      tripOptions: [],
      isChecked: false,
      typeFilter: null,
      filterByOther: '',
      filter: {},
      activeText: selectorOptions[0].label,
      activeType: 'ALL',
      checkedIds: sourceEntity?.linkDataLink?.map(item => item.dataLink.id) ?? [],
      checkedDataList: sourceEntity?.linkDataLink ?? [],
      popupVisible: false,
      showSearch: false
    }
    this.filterByKey = undefined
    this.selectorKey = selectorOptions[0]
    this.searchRef = React.createRef()
  }

  componentWillMount() {
    let { value } = this.props
    if (value && value.disableStrategy === 'LIMIT_COUNT') {
      api.invoke('get:bills:value').then(result => {
        this.map = handleDataLinkUseCount(result)
        this.getEntityList()
      })
    } else {
      this.getEntityList()
    }
    this.isTripOrder() && this.getTripOptions()
  }

  getEntityList(filterBy, start = 0) {
    const { value, flowId, filterId, expenseIds = [], formData = {}, otherFilters } = this.props
    if (start === 0) {
      this.setState({ loading: true })
    }
    const isPrivateCar = get(value, 'platformId.type') === 'PRIVATE_CAR'
    const entityId = get(value, 'id')
    const { submitterId } = formData
    const { typeFilter, filterByOther } = this.state
    let currentEntityId = '' // 行程订单可按机酒火等类型筛选
    if (this.isTripOrder() && typeFilter) {
      currentEntityId = typeFilter.id
    }
    let params = { entityId: currentEntityId || entityId, isPrivateCar: isPrivateCar }
    if (submitterId) {
      params = { ...params, submitterId }
    }
    if (flowId) {
      params = { ...params, flowId }
    }
    if (isPrivateCar) {
      params = filterBy ? { ...params, filterBy } : { ...params, start, count: packageCount }
      //我的收藏
      const isCollectType = this.getIsCollectType()
      if (isCollectType) {
        params.favoriteStatus = true
      }
      api.invokeService('@bill:get:entity:list', params).then(res => {
        this.handleFormatDataLink(res)
        this.setState({ loading: false })
      })
    } else {
      const isOrder = this.isTripOrder()
      let orderBy = undefined
      let filter = 'active==true'
      const query = new QuerySelect().filterBy(filter)
      if (isOrder) {
        const filterCode =
          expenseIds && expenseIds.length
            ? expenseIds.map(item => `form.E_${entityId}_申请单编号.containsIgnoreCase("${item}")`).join(' || ') //@i18n-ignore
            : ''
        if (filterCode) {
          query.filterBy(filterCode)
        }
        orderBy = [
          { value: `form.E_${entityId}_出发时间`, order: 'DESC' }, //@i18n-ignore
          { value: `form.E_${entityId}_入住日期`, order: 'DESC' } //@i18n-ignore
        ]
        params = { ...params, params: { type: 'order' } } //传参数给后台表明是行程管理里面的订单，需要后台根据配置来过滤数据 }
      }
      if (filterBy) {
        query.filterBy(filterBy)
      }
      if (filterByOther) {
        query.filterBy(filterByOther)
      }
      if (otherFilters?.length) {
        if (Array.isArray(otherFilters)) {
          otherFilters.forEach(filter => {
            query.filterBy(filter)
          })
        } else if (isString(otherFilters)) {
          query.filterBy(otherFilters)
        }
      }
      params = {
        ...params,
        type: 'LIST',
        start,
        count: isOrder ? tripOrderPackageSize : packageSize
      }
      params = { ...params, ...query.value() }
      if (filterId) {
        params = { ...params, filterId, form: formData }
      }
      if (isOrder) {
        params = { ...params, orderBy }
      }
      //我的收藏
      const isCollectType = this.getIsCollectType()
      if (isCollectType) {
        params.favoriteStatus = true
      }
      return api.invokeService('@mine:post:serach:dataLink', params).then(res => {
        this.setState({ loading: false })
        this.handleFormatDataLinknew(res, start)
        const temp = res.items.template.content.expansion
        this.setState({
          template: temp,
          start: start + packageSize
        })
      })
    }
  }

  handleFormatDataLink(result) {
    let { listData } = this.state
    let { value } = this.props
    let entityList = get(result, 'items') || []
    entityList = entityList.slice()
    let hasMore = listData.length + entityList.length === result.count
    entityList =
      get(value, 'disableStrategy') === 'LIMIT_COUNT' ? filterDataLinkUseCount(this.map, entityList) : entityList
    let newEntityList = formatEntityList(value.fields, entityList)
    let cListData = listData.concat(newEntityList)
    if (cListData.every(v => v?.dataLink?.id)) {
      const map = new Map()
      cListData.forEach(item => {
        if (!map.has(item?.dataLink?.id)) {
          map.set(item?.dataLink?.id, item)
        }
      })
      cListData = [...map.values()]
    }
    this.setState({ listData: cListData, isLoading: false, hasMore: !hasMore })
    this.filterByKey = this.filterByKey ? this.filterByKey : getNameCodeSearchKey(entityList)
  }
  handleFormatDataLinknew(result, start) {
    let { listData } = this.state
    let { value } = this.props
    if (start === 0) {
      listData = []
    }
    let entityList = get(result, 'items.data') || []
    entityList = entityList.slice()
    let hasMore = listData.length + entityList.length === result.items.total
    entityList =
      get(value, 'disableStrategy') === 'LIMIT_COUNT' ? filterDataLinkUseCount(this.map, entityList) : entityList
    let newEntityList = formatEntityList(value.fields, entityList)
    let cListData = listData.concat(newEntityList)
    if (cListData.every(v => v?.dataLink?.id)) {
      const map = new Map()
      cListData.forEach(item => {
        if (!map.has(item?.dataLink?.id)) {
          map.set(item?.dataLink?.id, item)
        }
      })
      cListData = [...map.values()]
    }
    let { template } = result.items
    this.setState({ listData: cListData, isLoading: false, hasMore: !hasMore, template })
    if (entityList.length > 0) {
      this.filterByKey = this.filterByKey ? this.filterByKey : this.getSearchKey(entityList[0].dataLink)
    }
  }
  getSearchKey = form => {
    let nameKey = '',
      codeKey = '',
      formKey = '',
      toKey = ''
    for (let key in form) {
      if (getStrLastWord(key, '_') === 'name') {
        nameKey = key
      } else if (getStrLastWord(key, '_') === 'code') {
        codeKey = key
      } else if (getStrLastWord(key, '_') === i18n.get('出发地')) {
        // @i18n-ignore
        formKey = key
      } else if (getStrLastWord(key, '_') === i18n.get('目的地')) {
        // @i18n-ignore
        toKey = key
      }
    }
    return { nameKey, codeKey, formKey, toKey }
  }

  getIsCollectType = () => {
    return this.state.activeType === 'COLLECT'
  }

  //实体查看详情
  handleOnnewItemClick = rowData => {
    let field = this.props.value
    let rowValue = {}
    rowValue.data = rowData.dataLink
    rowValue.id = rowData.dataLink.id
    rowValue.template = this.state.template
    const title = get(field, 'referenceData.name')
    const isOrder = this.isTripOrder()
    if (isOrder) {
      api.open('@bill:DataLinkEntityTripOrderDetailModal', {
        field,
        value: { ...rowValue, data: { dataLink: rowData.dataLink } },
        title
      })
    } else {
      api.open('@bill:DataLinkEntityDetailModal', { field, value: rowValue })
    }
  }

  renderEmpty() {
    const { loading } = this.state
    if (loading) {
      return <SkeletonListEUI />
    }
    return <EmptyWidget size={200} type="noCentent" tips={'暂无数据'} />
  }

  renderListFooter = () => {
    let { isLoading, hasMore } = this.state
    let endStr = hasMore ? i18n.get('加载完毕') : i18n.get('没有更多数据了')
    return <div style={{ padding: 30, textAlign: 'center' }}>{isLoading ? i18n.get('加载更多') : endStr}</div>
  }

  isTripOrder = () => {
    const type = get(this.props.value, 'platformId.type', '')
    const referenceType = get(this.props.value, 'type', '')
    const isOrder = type === 'TRAVEL_MANAGEMENT' && referenceType === 'ORDER'
    return isOrder
  }

  isPrivateCar = () => {
    const type = get(this.props.value, 'platformId.type', '')
    return 'PRIVATE_CAR' === type
  }

  getTripOptions = () => {
    const { value } = this.props
    const id = get(this.props.value, 'platformId.id')
    api.invokeService('@bill:get:entity:details:list', { id }).then(res => {
      const entity = res.items.find(i => i.id === value.id)
      entity.name = '全部' // @i18n-ignore
      const tripOptions = [entity, ...entity.children]
      this.setState({
        tripOptions
      })
    })
  }
  handlePopupCancel = () => {
    Popup.hide()
  }
  handleFilterData = (filter, filterByOther) => {
    Popup.hide()
    const { filterBy } = this.state
    this.setState({ filter, isChecked: filter.show_full_name, filterByOther: filterByOther?.filterBy }, () => {
      this.getEntityList(filterBy)
    })
  }
  hanldeFilter = () => {
    const fields = this.props?.value?.referenceData?.fields || []
    Popup.show(
      <div className={styles['popup-modal']} onCancel={this.handlePopupCancel}>
        <div className="modal-title">
          <div className="title ">{i18n.get('筛选条件')}</div>
          <OutlinedTipsClose onClick={this.handlePopupCancel} />
        </div>
        <span className="modal-title-tips">{i18n.get('您可对当前业务对象下的字段进行筛选，')}</span>
        <span className="modal-title-tips mb-8">{i18n.get('展示结果等于您输入所有条件的组合')}</span>
        <FilterForm fields={fields} filter={this.state.filter} handleFilter={this.handleFilterData}></FilterForm>
      </div>,
      {
        animationType: 'slide-up',
        maskClosable: true,
        wrapClassName: 'popup-wrapper-style'
      }
    )
  }

  hanldeSetPopupVisible = visible => {
    this.setState({ popupVisible: visible })
  }
  handleShowSearch = () => {
    this.setState({ showSearch: true }, () => {
      this.searchRef.current?.focus()
    })
  }
  renderHeader = () => {
    const { activeText, popupVisible } = this.state
    const { title, sourceEntity, sourceTemplate, readOnly } = this.props
    const { isShowPersonalOnLeft } = sourceTemplate
    return (
      <>
        <div className="top-datalink-card-wrapper">
          <div className="top-datalink-card">
            <List>
              <List.Item
                prefixIcon={
                  isShowPersonalOnLeft ? (
                    <Avatar src={getDataLinkAvatar(sourceTemplate, sourceEntity)} shape="circle" />
                  ) : null
                }
                description={getDataLinkListDesc(sourceTemplate, sourceEntity)}
                extra={getDataLinkListSupple(sourceTemplate)}
                extraTitle={getDataLinkListReplenish(sourceTemplate, sourceEntity)}
                clickable={false}
              >
                <div>
                  <span className="eui-list-item-conent-tilte-ellipsis">
                    {getDataLinkListTitle(sourceTemplate, sourceEntity)}
                  </span>
                </div>
              </List.Item>
            </List>
          </div>
        </div>
        <div className="header-title">{title}</div>
        {!readOnly && (
          <div className="search-wrapper">
            <div
              className="type_name"
              onClick={() => {
                this.hanldeSetPopupVisible(true)
              }}
            >
              <span className="type_name_text">{activeText}</span>
              {popupVisible ? (
                <FilledDirectionExpandUp color="var(--eui-icon-n1)" fontSize={10} />
              ) : (
                <FilledDirectionExpandDown color="var(--eui-icon-n1)" fontSize={10} />
              )}
            </div>
            <div className="header-right">
              <OutlinedEditSearch color="var(--eui-icon-n1)" fontSize={16} onClick={this.handleShowSearch} />
              <OutlinedEditFilter
                color="var(--eui-icon-n1)"
                style={{ marginLeft: 16 }}
                fontSize={16}
                onClick={this.hanldeFilter}
              />
            </div>
          </div>
        )}
      </>
    )
  }
  handleCheckBoxChange = (checked, item) => {
    const id = item.dataLink.id
    if (checked) {
      this.setState({
        checkedIds: this.state.checkedIds.concat([id]),
        checkedDataList: this.state.checkedDataList.concat([item])
      })
    } else {
      this.setState({
        checkedIds: this.state.checkedIds.filter(item => item !== id),
        checkedDataList: this.state.checkedDataList.filter(item => item.dataLink.id !== id)
      })
    }
  }
  getPrefixIcon = item => {
    const { template, checkedIds = [] } = this.state
    const { isShowPersonalOnLeft } = template
    const { readOnly } = this.props
    return (
      <div
        onClick={e => {
          e.stopPropagation()
        }}
        style={{ display: 'flex', padding: 12, paddingLeft: 16 }}
      >
        {!readOnly && (
          <Checkbox
            checked={checkedIds.includes(item?.dataLink?.id)}
            onChange={check => this.handleCheckBoxChange(check, item)}
          />
        )}
        {isShowPersonalOnLeft ? (
          <Avatar src={getDataLinkAvatar(template, item)} style={{ marginLeft: 12 }} shape="circle" />
        ) : null}
      </div>
    )
  }
  renderListItem = item => {
    const { template, isChecked } = this.state
    const favoriteStatus = item?.dataLink?.favoriteStatus ?? false
    return (
      <List.Item
        key={item.dataLink.id}
        style={{ flex: 1 }}
        prefixIcon={this.getPrefixIcon(item)}
        onClick={() => this.handleOnnewItemClick(item)}
        description={getDataLinkListDesc(template, item)}
        extra={getDataLinkListSupple(template)}
        extraTitle={getDataLinkListReplenish(template, item)}
        clickable
      >
        <div style={{ alignItems: 'center' }}>
          <span className={!isChecked ? 'eui-list-item-conent-tilte-ellipsis' : ''}>
            {getDataLinkListTitle(template, item)}
          </span>
          {favoriteStatus && <FilledGeneralCollect color="var(--eui-decorative-yel-500)" style={{ marginLeft: 4 }} />}
        </div>
      </List.Item>
    )
  }
  renderList = () => {
    const { listData, checkedDataList } = this.state
    const { readOnly } = this.props
    if (!listData?.length) {
      return this.renderEmpty()
    }
    const showList = readOnly ? checkedDataList : listData
    return (
      <List>
        {showList.map(item => {
          return this.renderListItem(item)
        })}
      </List>
    )
  }
  handleCheckedAll = check => {
    if (check) {
      this.setState({
        checkedIds: this.state.listData?.map(item => item.dataLink.id) ?? [],
        checkedDataList: this.state.listData ?? []
      })
    } else {
      this.setState({ checkedIds: [], checkedDataList: [] })
    }
  }
  renderContent = () => {
    const { readOnly } = this.props
    return (
      <>
        {this.renderHeader()}
        <div className="multi-list" style={readOnly ? { marginBottom: 0 } : {}}>
          {this.renderList()}
        </div>
        {!readOnly && this.renderBottomBar()}
      </>
    )
  }
  renderBottomBar = () => {
    const { listData = [], checkedIds = [] } = this.state
    const { sourceEntity } = this.props
    return (
      <div className="bottom_bar">
        <Checkbox
          disabled={listData.length === 0}
          indeterminate={checkedIds.length > 0 && checkedIds.length < listData.length}
          checked={listData.length === checkedIds.length && listData.length > 0}
          style={{ flexShrink: 0, marginRight: 24, flex: 1 }}
          onChange={this.handleCheckedAll}
        >
          {i18n.get(`全选 已选(${checkedIds.length})`)}
        </Checkbox>
        <Button style={{ width: 166 }} onClick={this.handleAdd} disabled={checkedIds.length === 0} size="large">
          {sourceEntity?.linkDataLink?.length ? i18n.get('重新关联完成') : i18n.get('完成关联')}
        </Button>
      </div>
    )
  }
  onConfirm = isFinish => {
    //isFinish === true 生成明细，关闭当前对话框，并将关联数据传给前一个对话框，前一个对话框生成费用明细并关闭自己
    //isFinish === false 继续关联，关闭当前对话框，并将关联数据传给前一个对话框
    const { checkedDataList = [] } = this.state
    this.props.layer.emitOk({ checkedDataList, isFinish })
  }
  handleAdd = () => {
    Dialog.confirm({
      iconType: 'success',
      title: i18n.get('关联成功'),
      confirmText: i18n.get('生成明细'),
      cancelText: i18n.get('继续关联'),
      onConfirm: () => this.onConfirm(true),
      onCancel: () => this.onConfirm(false)
    })
  }
  handleChangeMenuType = () => {
    this.setState(
      {
        activeType: this.selectorKey.value,
        activeText: this.selectorKey.label,
        searchValue: '',
        filter: {},
        filterBy: '',
        filterByOther: '',
        popupVisible: false
      },
      _ => {
        this.getEntityList()
      }
    )
  }
  renderSearchView = () => {
    return (
      <div className={styles['dataLink-wrapper']}>
        <div className="search-bar-wrapper">
          <SearchBar
            ref={this.searchRef}
            placeholder={i18n.get('请输入名称或编码')}
            showCancelButton={() => true}
            onCancel={this.handleOnSearchCancel}
            onChange={this.handleOnSearch}
          />
        </div>
        <div className="multi-list">{this.renderList()}</div>
        {this.renderBottomBar()}
      </div>
    )
  }
  handleOnSearchCancel = () => {
    this.setState({ searchValue: '', showSearch: false }, () => {
      this.getEntityList()
    })
  }

  handleOnSearch = debounce(text => {
    const value = text.trim()
    if (!this.filterByKey) return this.setState({ searchValue: value })
    let { nameKey, codeKey } = this.filterByKey || {}
    const filterBy = value ? `(form.${nameKey}.containsIgnoreCase("${value}") || form.${codeKey}.containsIgnoreCase("${value}"))` : ''
    this.setState({ searchValue: value }, () => {
      this.getEntityList(filterBy)
    })
  }, 400)

  render() {
    const { popupVisible, showSearch } = this.state
    if (showSearch) {
      return this.renderSearchView()
    }
    return (
      <div className={styles['dataLink-wrapper']}>
        {this.renderContent()}
        <PopupEUI
          destroyOnClose
          visible={popupVisible}
          title={
            <NavBar
              style={{
                width: '100%',
                borderRadius: '10px 10px 0 0',
                padding: 0,
                borderBottom: '1px solid var(--eui-line-divider-default)'
              }}
              back={
                <Button category="text" theme="highlight">
                  {i18n.get('取消')}
                </Button>
              }
              onBack={() => this.hanldeSetPopupVisible(false)}
              backArrow={false}
              right={
                <Button category="text" theme="highlight" onClick={this.handleChangeMenuType}>
                  {i18n.get('确认')}
                </Button>
              }
              title="标题"
            ></NavBar>
          }
          onMaskClick={() => {
            this.hanldeSetPopupVisible(false)
          }}
          radius
        >
          <Selector
            columns={3}
            style={{ marginTop: 16 }}
            options={selectorOptions}
            defaultValue={this.selectorKey.value}
            onChange={(_arr, extend) => {
              this.selectorKey = extend.items[0]
            }}
          />
        </PopupEUI>
      </div>
    )
  }
}
