import {FormAttrCalHideResult, HideResultValueMap} from './utils'
import {fnHideFieldsNote} from '../../../../components/utils/fnHideFields'
import {ControlPosition} from 'react-draggable'
import React from 'react'
import {BillAdditionalMessageConfigItem, BillAdditionalMessageRule, BillAdditionalMessageDisplayNameMode} from '@ekuaibao/ekuaibao_types'

export interface BillVersionDiffProp {
  versions?: any
  riskData?: any
  dataSource?: any
  getDiffs?: Function,

}

/**
 * 是否是隐藏字段
 * @param fieldName
 * @param specification
 * @param result
 */
export function isHiddenFiled(fieldName: string, specification: any, result?: HideResultValueMap) {
  if (!fieldName || !specification) {
    return true
  }
  const field = specification.components.find(item => item.field === fieldName)
  if (!field) {
    return true
  }
  if (!field.hide) {
    return false
  }
  if (field.hideVisibility && fnHideFieldsNote(field.hideVisibility)) {
    return false
  }
  return result?.[fieldName] ?? field.hide
}

export type BillVersionDiffModalProps = {
  onClose?: () => void
  visible?: boolean
  initPosition?: ControlPosition
} & BillVersionDiffProp



const customDisplaiesTypes = [
  { id: 'bills', name: '单据信息' },
  { id: 'details', name: '费用明细信息' }
]

/**
 * 自定义展示数据
 */
export type BillInfoCustomDisplaiesDataItem = {
  meta: { id: string, name: string }, // meta
  data: Array<{ name: string, value: string }> // 自定义展示 entry
}

export type BillInfoAdditionalMessagePanelProps = {
  versions?: any
  riskData?: any
  privilegeId?: string
  dataSource?: any
  config?: any
}

/**
 * 辅助信息的 context
 */
export type BillInfoAdditionalMessagePanelContextValue = {
  rule: BillAdditionalMessageRule
}

export const BillInfoAdditionalMessagePanelContext = React.createContext<BillInfoAdditionalMessagePanelContextValue>({
  rule: {
    id: '',
    name: '',
    specificationIds: [],
    staffs: [],
    invoiceConfig: {},
    customDisplaies: {},
    riskTips: {},
    flowModifyContrasts: {},
    otherConfig: { showMobileShortcut: false }
  }
})

/**
 * 临时使用的风险数据类型，目前并没有风险的  type 定义
 */
export type BillAdditionalMessageRiskData = {
  controlField: string,
  controlName: string,
  messages: any[],
  path: string,
  pathValueId: string,
  type: string
}

/**
 * 根据配置获取字段的 显示名称 与 字段名称
 * @param display
 * @param specification
 * @param config
 */
function getDisplayName(
  display: { id: string, name: string },
  specification: any,
  config: BillAdditionalMessageConfigItem
): string {
  const {
    displayName = BillAdditionalMessageDisplayNameMode.description
  } = config

  switch (displayName) {
    case BillAdditionalMessageDisplayNameMode.label: {
      const component = specification.components.find(item => item.field === display.id)
      return component?.cnLabel ?? display.name
    }
    case BillAdditionalMessageDisplayNameMode.description:
    default: return display.name
  }
}

/**
 * 对 datasource 里面的数据进行 formatter
 * @param dataSource
 * @param customDisplaies
 * @param hideResult
 * @param config
 */
export function getCustomDisplaiesData(
  dataSource: any,
  customDisplaies: any[],
  hideResult: FormAttrCalHideResult,
  config: BillAdditionalMessageConfigItem
): BillInfoCustomDisplaiesDataItem[] {
  const datas = config?.datas || []
  return customDisplaiesTypes?.map(item => {
    const _customDisplaies = customDisplaies?.filter(display => datas?.includes(display.id))
    const data = (_customDisplaies as any[]).map<Array<{ name: string, value: string }>>(display => {
      const {id: field} = display || {}
      switch (item.id) {
        case 'bills': {
          const _name = getDisplayName(display, dataSource?.specificationId, config)
          const value = !isHiddenFiled(field, dataSource?.specificationId, hideResult.form) ? dataSource?.[field] : undefined
          return [{name: _name, value}]
        }
        case 'details': {
          return dataSource?.details?.map((item: any) => {
            const detailId = item?.feeTypeForm?.detailId
            const _name = getDisplayName(display, item?.specificationId, config)
            const value = !isHiddenFiled(field, item?.specificationId, hideResult.details[detailId]) ? item?.feeTypeForm?.[field] : undefined
            return ({
              name: `${item?.feeTypeId?.name}-${_name}`,
              value
            })
          })
        }
      }
    })

    /**
     * 字段的值为 false 不应该展示
     */
    const result = data.reduce((result, data) => result.concat(data?.filter(item => !!item.value && item.value !== 'false') ?? []), [])

    return {
      meta: item,
      data: result ?? []
    }
  })
}
