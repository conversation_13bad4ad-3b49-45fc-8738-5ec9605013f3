import React, { useEffect, useMemo, useState } from 'react'
import {
  BillInfoAdditionalMessagePanelContext,
  BillInfoAdditionalMessagePanelProps,
  getCustomDisplaiesData
} from './types'
import { BillAdditionalMessageDisplay, BillAdditionalMessageRuleUtils, FieldIF } from '@ekuaibao/ekuaibao_types'
import styles from './bill-version-diff.module.less'
import { get } from 'lodash'
import { Accordion as Collapse } from 'antd-mobile'
import { Spin } from './components/spin'
import classNames from 'classnames'
import { BillInvoiceViewer } from './components/bill-invoice-viewer'
import { BillVersionViewer } from './components/bill-version-viewer'
import { BillRiskViewerPanel } from './components/bill-risk-viewer'
import { BillDataViewer } from './components/bill-data-viewer'
import { PanelHeader } from './components/panel-header'
import { formatAutoCalHideResult, FormAttrCalHideResult } from './utils'
import { getCustomDisplaies } from './constants'
import { getAllAutoCalResultForBillDiff } from '../../utils/autoCalResult'

const { Panel } = Collapse

/**
 * 辅助信息
 * @param props
 * @constructor
 */
export const BillInfoAdditionalMessagePanel: React.FC<BillInfoAdditionalMessagePanelProps> = props => {
  const { riskData, dataSource, privilegeId, versions, config } = props

  /**
   * 通过自动计算获取的隐藏字段信息
   */
  const [hideResult, setHideResult] = useState<FormAttrCalHideResult>({ form: {}, details: {} })
  /**
   * 自定义信息字段总数据
   */
  const [customDisplaies, setCustomDisplaies] = useState<FieldIF[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | undefined>()

  /** 默认展开的 key */
  const defaultExpandedKeys = useMemo<BillAdditionalMessageDisplay[]>(() => {
    const result: BillAdditionalMessageDisplay[] = []
    if (!config) {
      return result
    }

    if (config?.flowModifyContrasts?.mobileDefaultDisplayExpand) {
      result.push(BillAdditionalMessageDisplay.flowModifyContrasts)
    }
    if (config?.riskTips?.mobileDefaultDisplayExpand) {
      result.push(BillAdditionalMessageDisplay.riskTips)
    }
    if (config?.customDisplaies?.mobileDefaultDisplayExpand) {
      result.push(BillAdditionalMessageDisplay.customDisplaies)
    }
    if (config?.invoiceConfig?.mobileDefaultDisplayExpand) {
      result.push(BillAdditionalMessageDisplay.invoiceConfig)
    }

    return result
  }, [config])

  /**
   * 初始化
   */
  useEffect(() => {
    async function init() {
      const specId = get(dataSource, 'specificationId.originalId.id')
      // 获取配置数据
      const [customDisplaies] = await Promise.all([await getCustomDisplaies()])
      if (!config) {
        setError('获取配置失败')
        setLoading(false)
        return
      }

      let hideResult: FormAttrCalHideResult = { form: {}, details: {} }
      try {
        // 获取自动计算结果
        const calResult = await getAllAutoCalResultForBillDiff(dataSource)
        hideResult = formatAutoCalHideResult(calResult.items ?? [])
      } catch (e) {
        setError('小组件计算失败')
      }

      setCustomDisplaies(customDisplaies)
      setHideResult(hideResult)
      setLoading(false)
    }

    init()
  }, [])

  /**
   * 自定义数据展示列表
   */
  const customDisplaiesDataList = useMemo(() => {
    if (!dataSource || customDisplaies.length === 0 || !config?.customDisplaies) {
      return []
    }
    return getCustomDisplaiesData(dataSource, customDisplaies, hideResult, config.customDisplaies)
  }, [dataSource, customDisplaies, config, hideResult])

  const contextValue = useMemo(() => ({ rule: config }), [config])

  if (loading) {
    return (
      <div className="spin-loading">
        <Spin />
      </div>
    )
  }

  if (error) {
    return <div className="bill-info-additional-message-panel">{error}</div>
  }

  const cls = classNames('bill-info-additional-message')
  const cardRenderOrderList = BillAdditionalMessageRuleUtils.getOrderListFromWidgetRuleValue(config)
  return (
    <BillInfoAdditionalMessagePanelContext.Provider value={contextValue}>
      <div className={cls}>
        <Collapse bordered={false} defaultActiveKey={defaultExpandedKeys} className="ant-collapse-icon-position-right">
          {cardRenderOrderList.map((displayType: BillAdditionalMessageDisplay) => {
            switch (displayType) {
              case BillAdditionalMessageDisplay.customDisplaies:
                // 数据字段量
                const dataCounts = customDisplaiesDataList.reduce((result, item) => result + item.data.length, 0)
                return (
                  config?.customDisplaies?.active && (
                    <Panel
                      header={<PanelHeader type={displayType} count={dataCounts} />}
                      key="customDisplaies"
                      className="bill-info-additional-message-panel"
                    >
                      <BillDataViewer data={customDisplaiesDataList} />
                    </Panel>
                  )
                )
              case BillAdditionalMessageDisplay.flowModifyContrasts:
                return (
                  config?.flowModifyContrasts?.active && (
                    <Panel
                      header={<PanelHeader type={displayType} count={versions.length > 0 ? versions.length - 1 : 0} />}
                      key="flowModifyContrasts"
                      className="bill-info-additional-message-panel"
                    >
                      <BillVersionViewer privilegeId={privilegeId} versions={versions} />
                    </Panel>
                  )
                )
              case BillAdditionalMessageDisplay.invoiceConfig:
                return (
                  config?.invoiceConfig?.active && (
                    <Panel
                      header={<PanelHeader type={displayType} />}
                      key="invoiceConfig"
                      className="bill-info-additional-message-panel bill-invoice-summarize"
                    >
                      <BillInvoiceViewer
                        dataSource={dataSource}
                        group={config.invoiceConfig?.groupDisplayByInvoiceType}
                      />
                    </Panel>
                  )
                )
              case BillAdditionalMessageDisplay.riskTips:
                return (
                  config?.riskTips?.active && (
                    <BillRiskViewerPanel
                      key="riskTips"
                      className="bill-info-additional-message-panel"
                      riskData={riskData}
                      dataSource={dataSource}
                    />
                  )
                )
              default:
                return null
            }
          })}
        </Collapse>
      </div>
    </BillInfoAdditionalMessagePanelContext.Provider>
  )
}
