import React from 'react'
import { BillAdditionalMessageDisplay } from '@ekuaibao/ekuaibao_types'
import { app as api } from '@ekuaibao/whispered'
const Display = BillAdditionalMessageDisplay
const EKBIcon = api.require('@elements/ekbIcon')

export const BillVersionPanelIcon: Record<string, React.FC<React.SVGProps<any>>> = {
  [Display.invoiceConfig]: props => <EKBIcon {...props} name="#fapiaoxinxi" />,
  [Display.flowModifyContrasts]: props => <EKBIcon {...props} name="#banben" />,
  [Display.riskTips]: props => <EKBIcon {...props} name="#fengxian" />,
  [Display.customDisplaies]: props => <EKBIcon {...props} name="#yichang" />
}
