import React from 'react'
import { app } from '@ekuaibao/whispered'

// 移动端打开方式
export const appOpenModes = [
  { id: 'MORE', name: i18n.get('更多-小组件') },
  { id: 'PAGE_FLOATING', name: i18n.get('页面-浮窗') }
]
// 风险提示
export const riskTips = [
  { id: 'loan', name: i18n.get('单据风险') },
  { id: 'budget', name: i18n.get('预算风险') },
  { id: 'invoice', name: i18n.get('发票风险') },
  { id: 'costControl', name: i18n.get('费用控制风险') },
  { id: 'dataLinkLedger', name: i18n.get('业务对象风险') },
  { id: 'other', name: i18n.get('其他风险') }
]
// 单据修改对比
export const flowModifyContrasts = [
  { id: 'documentType', name: i18n.get('费用明细') },
  { id: 'invoice', name: i18n.get('发票信息') }
]

export const getCustomDisplaies = async () => {
  const { data } = await app.dataLoader('@common.baseDataProperties').load()
  // 只处理字段类型为 text 和 激活的字段
  const customDisplaies = data
    ?.filter(item => item?.dataType?.type == 'text' && item.active)
    ?.map(item => ({ id: item.name, name: item.label }))
  return customDisplaies
}

export const customDisplaiesTypes = [
  { id: 'bills', name: '单据信息' },
  { id: 'details', name: '费用明细信息' }
]
