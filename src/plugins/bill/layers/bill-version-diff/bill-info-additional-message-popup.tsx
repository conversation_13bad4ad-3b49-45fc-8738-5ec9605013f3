import { BillInfoAdditionalMessagePanelProps } from './types'
import React, { useCallback } from 'react'
import { BillInfoAdditionalMessagePanel } from './bill-info-additional-message'
import { app as api } from '@ekuaibao/whispered'
import style from './bill-version-diff.module.less'
const EKBIcon = api.require<any>('@elements/ekbIcon')
export const BillInfoAdditionalMessagePopup: React.FC<BillInfoAdditionalMessagePanelProps> = props => {
  const {
    layer
  } = props
  const handleClose = useCallback(() => {
    layer?.emitCancel?.()
  }, [layer])


  return (
    <div className="bill-info-additional-message-popup-wrapper">
      <div className="bill-info-additional-message-popup-header">
        <div className="bill-info-additional-message-popup-header-title">{i18n.get('辅助信息')}</div>
        <div className="bill-info-additional-message-popup-header-close">
          <EKBIcon name="#close" onClick={handleClose} />
        </div>
      </div>
      <div className="bill-info-additional-message-popup-content">
        <BillInfoAdditionalMessagePanel {...props} />
      </div>
    </div>
  )
}
