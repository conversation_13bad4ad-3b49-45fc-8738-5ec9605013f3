/** 自动计算映射结果 */
import {InvoiceEntityType, InvoiceSimpleData} from '../../utils/types'

export type HideResultValueMap = {
  [fieldName: string]: any
}

/** 自动计算结果 */
export type FormAttrCalHideResult = {
  /** form 的结果 */
  form: HideResultValueMap,
  /**  detail 的结果 */
  details: {
    [detailId: string]: HideResultValueMap
  }
}

/**
 * 自动计算接口 item
 */
type AutoCalResultItem = {
  resultType: 'SPECIFICATION_ATTRIBUTE' | 'VALUE'
  onField: string,
  detailId?: string
  attribute?: string
  result?: any
}

/**
 * 发票类型分类
 */
export enum BillVersionDiffInvoiceShowType {
  paper= 'paper',
  digital = 'digital',
  blockchain = 'blockchain',
  other = 'other'
}

/**
 * 针对展示设置的发票数据
 */
export type BillInvoiceSummarizeDataItem = {
  detailId: string
  item: any
  showType: BillVersionDiffInvoiceShowType
  data: InvoiceSimpleData
  id: string
}

/**
 * 是否是 form 计算的结果
 * @param value
 */
function isCalResultForDetail(value: AutoCalResultItem) {
  return !!value.detailId
}

/**
 * 是否是 form 计算的结果
 * @param value
 */
export function formatAutoCalHideResult(data: AutoCalResultItem[]): FormAttrCalHideResult {
  const formResult: FormAttrCalHideResult['form'] = {}
  const detailsResult: FormAttrCalHideResult['details'] = {}
  const resultItemOfHide = data.filter(item => item.resultType === 'SPECIFICATION_ATTRIBUTE' && item.attribute === 'hide')

  resultItemOfHide.filter(item => !isCalResultForDetail(item)).forEach((item) => {
    const r = item.result === '' || item.result === 'null' ? undefined : item.result // 为了匹配老的自动计算接口奇奇怪怪的返回值
    formResult[item.onField] = r === 'true'
  })

  resultItemOfHide.filter(item => isCalResultForDetail(item)).forEach((item) => {
    const detailResult = detailsResult[item.detailId] ?? {}
    if (!detailsResult[item.detailId]) {
      detailsResult[item.detailId] = detailResult
    }

    const r = item.result === '' || item.result === 'null' ? undefined : item.result // 为了匹配老的自动计算接口奇奇怪怪的返回值
    detailResult[item.onField] = r === 'true'

  })


  return {
    form: formResult,
    details: detailsResult
  }
}


export const BillVersionDiffInvoiceShowTypeName: Record<BillVersionDiffInvoiceShowType, string> = {
  [BillVersionDiffInvoiceShowType.digital]: i18n.get('电子发票'),
  [BillVersionDiffInvoiceShowType.other]: i18n.get('其他发票'),
  [BillVersionDiffInvoiceShowType.blockchain]: i18n.get('区块链发票'),
  [BillVersionDiffInvoiceShowType.paper]: i18n.get('纸质发票'),
}

/**
 * 根据发票进行分类
 * @param item
 */
export const getBillVersionDiffInvoiceShowTypeFromInvoice = (item: any) => {
  const type: typeof InvoiceEntityType[keyof typeof InvoiceEntityType] = item.invoiceId?.entityId
  const invoiceType = item.invoiceId?.form?.['E_system_发票主体_发票类别']
  switch (type) {
    case InvoiceEntityType.出租车票:
    case InvoiceEntityType.铁路客票:
    case InvoiceEntityType.定额发票:
    case InvoiceEntityType.客运汽车发票:
    case InvoiceEntityType.机打发票:
    case InvoiceEntityType.航空运输电子客票行程单:
    case InvoiceEntityType.过路费发票:
      return BillVersionDiffInvoiceShowType.paper
    case InvoiceEntityType.增值税发票:
      switch (invoiceType) {
        case 'DIGITAL_NORMAL':
        case 'PAPER_FEE':
        case 'FULL_DIGITAl_NORMAL':
        case 'FULL_DIGITAl_SPECIAL':
        case 'DIGITAL_SPECIAL': return BillVersionDiffInvoiceShowType.digital
        case 'BLOCK_CHAIN': return BillVersionDiffInvoiceShowType.blockchain
        default: return BillVersionDiffInvoiceShowType.paper
      }
    case InvoiceEntityType.医疗发票:
    case InvoiceEntityType.财政票据:
      return BillVersionDiffInvoiceShowType.digital
    case InvoiceEntityType.消费小票:
    case InvoiceEntityType.其他发票:
    default:
      return BillVersionDiffInvoiceShowType.other
  }
}

