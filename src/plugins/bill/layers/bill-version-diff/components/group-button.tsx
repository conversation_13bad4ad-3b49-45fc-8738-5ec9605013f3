import React from 'react'
import classNames from 'classnames'

type ButtonGroupProps = {
  options: Array<{ label: string, value: any }>
  value: string
  onChange?: (value: string) => void
}
/**
 * 新的 Button UI 组件
 * @param props
 * @constructor
 */
export const ButtonGroup: React.FC<ButtonGroupProps> = props => {
  const {
    options,
    value,
    onChange
  } = props
  return (
    <div className="additional-message-button additional-message-button-group">
      {options.map(option => (
        <span
          key={option.value}
          className={classNames({
            'additional-message-button-item': true,
            'additional-message-button-item_active': option.value === value
          })}
          onClick={() => onChange?.(option.value)}
        >{option.label}</span>
      ))}
    </div>
  )
}

