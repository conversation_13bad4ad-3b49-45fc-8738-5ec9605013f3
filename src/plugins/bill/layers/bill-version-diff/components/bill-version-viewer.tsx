import React, { useContext, useEffect, useMemo, useState} from 'react'
import { EmptyBody } from './empty-body'
import {BillInfoAdditionalMessagePanelContext, BillInfoAdditionalMessagePanelProps} from '../types'
import {BillInfoAdditionalCollapse as Collapse} from './bill-info-additional-collapse'
import moment from 'moment'
import {flowModifyContrasts} from '../constants'
import {ButtonGroup} from './group-button'
import { Spin } from './spin'
import {AmountProps} from '@ekuaibao/money-math/lib/interface'
import classNames from 'classnames'
import {app} from '@ekuaibao/whispered'
import {getDiffsBetweenVersions} from "../../../utils/billUtils";
import {getBillHistoryVersionDetail} from "../../../bill.action";
import {uuid} from "@ekuaibao/helpers";

const {Panel} = Collapse

type BillVersionViewerProps = Pick<BillInfoAdditionalMessagePanelProps, 'privilegeId' | 'versions'>


enum DiffType {
  documentType = 'documentType',
  invoice = 'invoice'
}

/**
 * 旧版本的差异数据 type base
 */
type DiffDataItemBase = {
  subText?: string[]
  text?: string[]
  code?: string
  typeName?: string
  time: number,
  name: string
  amount: AmountProps
  amountLabel?: string
  url?: string
  thumbUrl?: string
}

type DiffDataAddItem = { type: 'add' } & DiffDataItemBase
type DiffDataDeleteItem = { type: 'deled' } & DiffDataItemBase
type DiffDataChangeItem = {
  type: 'change',
  before: DiffDataItemBase
} & DiffDataItemBase

/**
 * 旧版本的差异数据 type
 */
type DiffDataItem = DiffDataAddItem | DiffDataDeleteItem | DiffDataChangeItem

/**
 * 展示的 entry data
 */
type DiffPreviewDataInfoEntry = {
  name: string,
  label: string,
  value: React.ReactNode, // 可能是预览，所以只能用 ReactNode
}

/**
 * 更通用的 preview data
 */
type DiffPreviewData = {
  id: string,
  originData: DiffDataItem,
  time: number,
  name: string,
  amount: AmountProps,
  type: DiffType,
  status: 'add' | 'deled' | 'change'
  info: DiffPreviewDataInfoEntry[]
  beforeInfo?: DiffPreviewDataInfoEntry[]
}

/**
 * status 的名字
 */
const DiffPreviewDataStatusNameMap: Record<DiffPreviewData['status'], string> = {
  add: i18n.get('新增'),
  change: i18n.get('修改'),
  deled: i18n.get('已删除')
}

/**
 * 针对差异数据的 info entry 提取出来转换成好展示的数据
 * @param item
 * @param type
 */
function formatDiffDataItemEntry(item: DiffDataItem | DiffDataItemBase, type: DiffType): DiffPreviewDataInfoEntry[] {
  const amountStr = `${item.amount?.standard ?? '0'} ${item.amount?.standardStrCode}`
  switch (type) {
    case DiffType.documentType:
      return [
        {label: i18n.get('业务类型'), name: 'name', value: item.name},
        {label: i18n.get('创建时间'), name: 'createTime', value: moment(item.time).format('YYYY年MM月DD日')},
        {label: i18n.get('金额'), name: 'amount', value: amountStr}
      ]
    case DiffType.invoice:
      if (item.thumbUrl) {
        // 如果有 thumbUrl 则是针对单纯只是上传的发票，并没有以下解析出来的数据，只能预览
        const url = item.url
        return [
          {label: i18n.get('发票名称'), name: 'name', value: item.name},
          {label: '', name: 'preview', value: (<a href="javascript:;" onClick={() => app.invokeService('@layout:preview:file', url, false, '')}>{i18n.get('预览')}</a>)},
        ]
      } else {
        return [
          {label: i18n.get('日期'), name: 'time', value: item.time ? moment(item.time).format('YYYY年MM月DD日') : '-'},
          {label: i18n.get('发票号码'), name: 'code', value: item.code ?? '-'},
          {label: i18n.get('发票类型'), name: 'type', value: item.typeName ?? ''},
          {label: i18n.get('税价合计'), name: 'amount', value: amountStr}
        ]
      }
    default:
      return []
  }
}

/**
 * 将旧版本差异数据转换成更通用的 preview data
 * @param list
 * @param type
 */
function convertDiffDataItemToPreviewData(list: DiffDataItem[], type: DiffType): DiffPreviewData[] {
  const result: DiffPreviewData[] = []

  for (const dataItem of list) {
    switch (dataItem.type) {
      case 'change': {
        result.push({
          id: uuid(),
          originData: dataItem,
          amount: dataItem.amount,
          info: formatDiffDataItemEntry(dataItem, type),
          name: dataItem.name,
          status: dataItem.type,
          time: dataItem.time,
          type: type,
          beforeInfo: formatDiffDataItemEntry(dataItem!.before, type)
        })
        break
      }
      case 'add':
      case 'deled':
        result.push({
          id: uuid(),
          originData: dataItem,
          amount: dataItem.amount,
          info: formatDiffDataItemEntry(dataItem, type),
          name: dataItem.name,
          status: dataItem.type,
          time: dataItem.time,
          type: type
        })
        break
    }
  }
  return result
}

/**
 * 修改历史查看
 * @param props
 * @constructor
 */
export const BillVersionViewer: React.FC<BillVersionViewerProps> = props => {
  const {
    privilegeId,
    versions
  } = props

  /**
   * 历史版本列表（不包含当前版本）
   */
  const versionsWithoutFirst = useMemo(() => versions?.slice(0, -1) ?? [], [versions])


  if (versionsWithoutFirst.length === 0) {
    return (
      <div className="bill-version-viewer">
        <EmptyBody label={i18n.get('暂无数据')} imageStyle={{width: '80px', height: '80px'}}/>
      </div>
    )
  }


  return (
    <div className="bill-version-viewer">
      <Collapse>
        {versionsWithoutFirst.map(version => (
          <Panel title={moment(version.createTime).format('YYYY-MM-DD HH:mm:ss')} key={version.id}>
            <BillVersionItem data={version} versions={versions} privilegeId={privilegeId}/>
          </Panel>
        ))}
      </Collapse>
    </div>
  )
}


/**
 * 单个版本历史记录组件
 * @param props
 * @constructor
 */
const BillVersionItem: React.FC<{
  data: any
  versions: any[]
  privilegeId?: string
}> = props => {
  const {
    data,
    versions,
    privilegeId
  } = props

  const {rule} = useContext(BillInfoAdditionalMessagePanelContext)

  const config = rule.flowModifyContrasts

  const [type, setType] = useState<DiffType>()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | undefined>()

  /**
   * 差异数据
   */
  const [diffData, setDiffData] = useState<{
    documentType: DiffPreviewData[],
    invoice: DiffPreviewData[]
  }>({
    documentType: [],
    invoice: []
  })

  /**
   * 初始化
   */
  useEffect(() => {
    async function init() {
      try {
        const index = versions?.findIndex(item => item.id === data.id)
        const curId = versions?.[index]?.id
        const prevId = versions?.[index + 1]?.id

        setLoading(true)
        const curVersion = await getBillHistoryVersionDetail(curId, privilegeId)
        const prevVersion = await getBillHistoryVersionDetail(prevId, privilegeId)
        const detailDiffData = getDiffsBetweenVersions(DiffType.documentType, curVersion, prevVersion)
        const invoiceDiffData = getDiffsBetweenVersions(DiffType.invoice, curVersion, prevVersion)
        setDiffData({
          documentType: convertDiffDataItemToPreviewData(detailDiffData, DiffType.documentType),
          invoice: convertDiffDataItemToPreviewData(invoiceDiffData, DiffType.invoice),
        })
        setType(DiffType.documentType)
        setLoading(false)
      } catch (e) {
        setError('获取数据失败')
        setLoading(false)
      }
    }

    init()
  }, [])

  /**
   * 查看的模式列表
   */
  const viewModeOptions = useMemo(() => {
    return flowModifyContrasts
      .map(v => ({
        label: `${v.name} ${diffData[v.id]?.length ?? 0}` ,
        value: v.id
      }))
      .filter((v) => config?.datas?.includes(v.value))
  }, [config, diffData])

  if (loading) {
    return (
      <div className="spin-loading">
        <Spin />
      </div>
    )
  }

  return (
    <div>
      <div className="header">
        <ButtonGroup options={viewModeOptions} value={type} onChange={setType as any}/>
      </div>
      <div className="content">
        {type && diffData[type].map(item => <DiffItemPreview data={item} key={item.id}/>)}
      </div>
    </div>
  )
}

/**
 * 差异数据展示组件
 * @param props
 * @constructor
 */
const DiffItemPreview: React.FC<{
  data: DiffPreviewData
}> = props => {
  const {
    data
  } = props

  const tagName = DiffPreviewDataStatusNameMap[data.status] ?? DiffPreviewDataStatusNameMap.add

  return (
    <div
      className={classNames({
        'diff-item-preview': true,
        [`diff-item-preview_${data.status}`]: true
      })}
    >
      <div className="diff-item-preview-content">
        <div className="diff-item-preview-header">
          <div className="diff-item-preview-tag">{tagName}</div>
          <div className="diff-item-preview-header-extra">
            {data.time ? moment(data.time).format('HH:mm') : ''}
          </div>
        </div>
        <div className="diff-item-preview-info">
          {data.info.map((item) => (
            <div className="diff-item-preview-info-entry" key={item.label}>
              <div className="diff-item-preview-info-entry-label">
                {!!item.label ? `${item.label}：` : ''}
              </div>
              <div className="diff-item-preview-info-entry-value">
                {item.value}
              </div>
            </div>
          ))}
        </div>
      </div>
      {data.status === 'change' && (
        <div className="diff-item-preview-before-content">
          <div className="diff-item-preview-tag">{i18n.get('修改前')}</div>
          <div className="diff-item-preview-info">
            {data.beforeInfo.map((item) => (
              <div className="diff-item-preview-info-entry" key={item.label}>
                <div className="diff-item-preview-info-entry-label">
                  {!!item.label ? `${item.label}：` : ''}
                </div>
                <div className="diff-item-preview-info-entry-value">
                  {!!item.value ? item.value : '-'}
                </div>
              </div>
            ))}
          </div>

        </div>

      )}
    </div>
  )
}