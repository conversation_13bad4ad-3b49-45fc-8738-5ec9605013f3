import { BillInfoCustomDisplaiesDataItem } from '../types'
import React from 'react'
import { EmptyBody } from './empty-body'
import { BillInfoAdditionalCollapse as Collapse } from './bill-info-additional-collapse'

const { Panel } = Collapse

type BillDataViewerProps = {
  data: BillInfoCustomDisplaiesDataItem[]
}
export const BillDataViewer: React.FC<BillDataViewerProps> = props => {
  const { data = [] } = props

  if (data.length === 0) {
    return <EmptyBody />
  }

  return (
    <div className="bill-data-viewer">
      <Collapse>
        {data.map(group => (
          <Panel title={group.meta.name} key={group.meta.id} counts={group.data.length}>
            {group.data.length > 0 &&
              group.data.map(item => (
                <div className="bill-data-viewer-item">
                  <div className="bill-data-viewer-item-label">{item.name}</div>
                  <div className="bill-data-viewer-item-value">{item.value}</div>
                </div>
              ))}
            {group.data.length === 0 && (
              <EmptyBody />
            )}
          </Panel>
        ))}
      </Collapse>
    </div>
  )
}
