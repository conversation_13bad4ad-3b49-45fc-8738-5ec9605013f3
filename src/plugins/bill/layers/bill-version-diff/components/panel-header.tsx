import React, {useMemo} from 'react'
import {BillAdditionalMessageDisplay} from '@ekuaibao/ekuaibao_types'
import {BillVersionPanelIcon} from '../icons'
import styles from '../bill-version-diff.module.less'

/**
 * 统一使用的 panel header
 * @param props
 * @constructor
 */
export const PanelHeader: React.FC<{ type: BillAdditionalMessageDisplay, count?: number }> = props => {
  const {type, count} = props

  const icon = BillVersionPanelIcon[type]?.({
    className: styles['header-icon']
  })

  const title = useMemo(() => {
    const countString = count ? ` [${count}]` : ' [0]'
    switch (type) {
      case BillAdditionalMessageDisplay.riskTips:
        return i18n.get('风险提示') + countString
      case BillAdditionalMessageDisplay.invoiceConfig:
        return i18n.get('发票信息')
      case BillAdditionalMessageDisplay.flowModifyContrasts:
        return i18n.get('修改记录') + countString
      case BillAdditionalMessageDisplay.customDisplaies:
        return i18n.get('自定义提示') + countString
      default:
        return null
    }
  }, [type, count])

  if (!icon) {
    return null
  }

  return (
    <header className={styles['diff-section-header']}>
      <div className={styles['header-title-wrapper']}>
        {icon}
        <span className={styles['header-title']}>{title}</span>
      </div>
    </header>
  )
}