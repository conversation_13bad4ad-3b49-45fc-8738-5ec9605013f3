import React, {useMemo} from 'react'
import {InvoiceSimpleData} from '../../../utils/types'
import Money from '../../../../../elements/puppet/Money'
import classNames from 'classnames'
import {
  BillInvoiceSummarizeDataItem, BillVersionDiffInvoiceShowType,
  BillVersionDiffInvoiceShowTypeName,
  getBillVersionDiffInvoiceShowTypeFromInvoice
} from '../utils'
import {formatInvoiceInfoData} from '../../../utils/billUtils'
import {uuid} from '@ekuaibao/helpers'
import {groupBy} from 'lodash'
import {BillInfoAdditionalCollapse as Collapse} from './bill-info-additional-collapse'
import {EmptyBody} from './empty-body'

const { Panel } = Collapse

/**
 * 发票信息展示组件
 * @param props
 * @constructor
 */
export const BillInvoiceViewer: React.FC<{ dataSource: any, group?: boolean | '' }> = props => {
  const {dataSource, group} = props
  const isGroupDisplay = group === '' || group === true

  const list = useMemo<BillInvoiceSummarizeDataItem[]>(() => {
    if (!dataSource?.details?.length) {
      return []
    }
    return dataSource.details.reduce((r, item) => {
      const feeTypeForm = item.feeTypeForm
      return r.concat((feeTypeForm?.invoiceForm?.invoices ?? []).map((invoice: any) => {
        const showType = getBillVersionDiffInvoiceShowTypeFromInvoice(invoice)
        return {
          detailId: feeTypeForm?.detailId,
          item: invoice,
          showType,
          data: formatInvoiceInfoData(invoice),
          id: uuid()
        }
      }))
    }, [])
  }, [dataSource])

  if (!dataSource) {
    return null
  }

  if (list.length === 0) {
    return <EmptyBody />
  }

  return (
    <>
      {!isGroupDisplay && (
        <div className="bill-invoice-summarize-info-content">
          <div className="bill-invoice-summarize-info-list">
            {list.map(item => <BillInvoiceSummarizeInfoItem key={item.id} invoice={item.data}/>)}
          </div>
        </div>
      )}
      {isGroupDisplay && (
        <BillInvoiceSummarizeInfoGroup data={list}/>
      )}
    </>
  )
}

/**
 * 发票数据分组展示组件
 * @param props
 * @constructor
 */
const BillInvoiceSummarizeInfoGroup: React.FC<{
  data: BillInvoiceSummarizeDataItem[]
}> = props => {
  const {
    data
  } = props

  const groupList = useMemo(() => {
    const group = groupBy(data, (item) => item.showType as any)
    return Object.keys(group).map((type: BillVersionDiffInvoiceShowType) => {
      const list = group[type]
      return {
        title: BillVersionDiffInvoiceShowTypeName[type],
        data: list ?? [],
        amount: list.reduce((r, item) => r + Number.parseFloat(item.data.amount.standard), 0),
        id: uuid()
      }
    }).filter((group) => group.data.length > 0)
  }, [data])

  console.log('小组件发票数据=============', groupList)

  return (
    <Collapse>
      {groupList.map((group) => (
        <Panel
          key={group.id}
          title={group.title}
          counts={group.data.length}
          extra={(
            <span className="summarize-amount font-bold">
               <Money showSymbol={false} withoutStyle={true} value={group.amount}/>
            </span>
          )}
        >
          <div className="bill-invoice-summarize-info-content">
            <div className="bill-invoice-summarize-info-list">
              {group.data.map(item => <BillInvoiceSummarizeInfoItem key={item.id} invoice={item.data}/>)}
            </div>
          </div>
        </Panel>
      ))}
    </Collapse>

  )
}

/**
 * 发票展示 item
 * @param props
 * @constructor
 */
const BillInvoiceSummarizeInfoItem: React.FC<{
  invoice: InvoiceSimpleData
}> = props => {
  const {
    invoice,
  } = props

  const invoiceDisplayItemList = useMemo(() => {
    return [
      {label: i18n.get('发票类型'), value: !!invoice.typeName ? invoice.typeName : '-'},
      {label: i18n.get('发票号码'), value: !!invoice.code ? invoice.code : '-'},
      {
        label: i18n.get('发票金额'), type: 'amount', value: (
          <Money showSymbol={false} withoutStyle={true} value={invoice.amount}/>
        ),
      },
    ]
  }, [invoice])

  return (
    <div className="bill-invoice-summarize-info-item">
      {invoiceDisplayItemList.map((item) => (
        <div className="bill-invoice-summarize-info-entity" key={item.label}>
          <div className="bill-invoice-summarize-info-item-label">
            {item.label}：
          </div>
          <div className={classNames({
            'bill-invoice-summarize-info-item-value': true,
            'font-bold': item.type === 'amount',
            'summarize-amount': item.type === 'amount',
          })}>
            {item.value}
          </div>
        </div>
      ))}
    </div>
  )
}