import React, {useContext, useMemo} from 'react'
import {BillAdditionalMessageConfigItem, BillAdditionalMessageDisplay} from '@ekuaibao/ekuaibao_types'
import {BillInfoAdditionalCollapse as Collapse} from './bill-info-additional-collapse'
import { Accordion as AntdCollapse } from 'antd-mobile'
import {riskTips} from '../constants'
import {PanelHeader} from './panel-header'
import { EmptyBody } from './empty-body'
import classNames from 'classnames'
import {app} from '@ekuaibao/whispered'
import {BillAdditionalMessageRiskData, BillInfoAdditionalMessagePanelContext} from '../types'
const EkbIcon = app.require<any>('@elements/ekbIcon')
const { Panel } = Collapse

/**
 * 加强版的 risk data ，新增对应的 feeType id 和 name
 */
type BillRiskViewerDataItem = {
  feeTypeId?: string,
  feeTypeName?: string
} & BillAdditionalMessageRiskData

type BillRiskViewerProps = {
  config: BillAdditionalMessageConfigItem
  data: BillRiskViewerDataItem[]
}

type BillRiskViewerPanelProps = {
  riskData: any,
  dataSource: any,
  className?: string
}

/**
 * 风险展示组件
 * @param props
 * @constructor
 */
export const BillRiskViewer: React.FC<BillRiskViewerProps> = props => {
  const {
    config,
    data
  } = props

  const fields = config?.datas ?? []
  const riskGroupList = useMemo<Array<{ meta: {id: string, name: string}, list: BillRiskViewerDataItem[]}>>(() => {
    return riskTips?.map(item => ({
      meta: item,
      list: data?.filter(risk => risk.type === item.id) ?? []
    }))

  }, [data])

  return (
    <div className="bill-risk-viewer">
      <Collapse>
        {riskGroupList.map(group => {
          if (!fields.includes(group.meta.id)) {
            return null
          }
          switch (group.meta.id) {
            case 'invoice':
              // 发票单独走 invoice risk viewer
              return (
                <Panel
                  title={group.meta.name}
                  key={group.meta.id}
                  counts={group.list.length}
                >
                  {group.list.length === 0 && (
                    <EmptyBody label={i18n.get('暂无数据')} imageStyle={{ width: '80px', height: '80px' }} />
                  )}
                  <InvoiceRiskViewer list={group.list} />
                </Panel>
              )
            case 'loan':
              // 单据风险走不需要分组的 normal risk viewer
              return (
                <Panel
                  title={group.meta.name}
                  key={group.meta.id}
                  counts={group.list.length}
                >
                  {group.list.length === 0 && (
                    <EmptyBody label={i18n.get('暂无数据')} imageStyle={{ width: '80px', height: '80px' }} />
                  )}
                  <NormalRiskViewer list={group.list} />
                </Panel>
              )
            default:
              // 其他风险走分组的 group normal risk viewer
              return (
                <Panel
                  title={group.meta.name}
                  key={group.meta.id}
                  counts={group.list.length}
                >
                  {group.list.length === 0 && (
                    <EmptyBody label={i18n.get('暂无数据')} imageStyle={{ width: '80px', height: '80px' }} />
                  )}
                  <GroupNormalRiskViewer list={group.list} />
                </Panel>
              )
          }

        })}
      </Collapse>
    </div>
  )
}

/**
 * 风险展示组件（被 panel 封装）
 * 因为需要统计数量的原因，只能把数据计算单独提取出来
 * @param props
 * @constructor
 */
export const BillRiskViewerPanel: React.FC<BillRiskViewerPanelProps> = props => {
  const {
    riskData,
    dataSource,
    className,
    ...otherProps
  } = props
  const { rule } = useContext(BillInfoAdditionalMessagePanelContext)
  const datas = rule.riskTips?.datas || []

  /**
   * 转换后的 preview data ，以 feeType id 来分组
   */
  const riskWarningList  = useMemo(() => {
    const riskTipsTypes = riskTips.map(item => item.id)
    const feeTypeList = dataSource?.details?.map?.(item => ({
      id: item?.feeTypeForm?.detailId,
      name: item?.feeTypeId?.name
    })) ?? []
    if ((riskData?.value?.riskWarning?.length ?? 0) === 0) {
      return []
    }
    const feeTypeMap = feeTypeList.reduce((r, item) => Object.assign(r, {[item.id]: item}), {})
    const resultList: BillRiskViewerDataItem[] = riskData?.value?.riskWarning
      ?.map((item: BillRiskViewerDataItem) => {
        if(!riskTipsTypes.includes(item.type)) {
          item.type = 'other'
        }
        if (!!item.pathValueId) {
          item.feeTypeId = feeTypeMap[item.pathValueId]?.id
          item.feeTypeName =  feeTypeMap[item.pathValueId]?.name
        }
        return item
      })
      ?.filter(item => datas.includes(item.type)) ?? []

    return resultList
  }, []);


  return (
    <AntdCollapse.Panel
      {...otherProps}
      header={<PanelHeader type={BillAdditionalMessageDisplay.riskTips} count={riskWarningList.length}/>}
      key="riskTips"
      className={classNames(
        'bill-info-additional-message-panel',
        className
      )}
    >
      <BillRiskViewer
        config={rule.riskTips}
        data={riskWarningList}
      />
    </AntdCollapse.Panel>
  )
}

/**
 * 平铺的风险展示组件
 * @param props
 * @constructor
 */
const NormalRiskViewer: React.FC<{list: BillRiskViewerDataItem[]}> = props => {
  const { list } = props

  return (
    <div className="normal-risk-viewer">
      <div className="normal-risk-viewer-list">
        {list.map((item) => (
          <React.Fragment key={item.pathValueId}>
            {item.messages.map(msg => <RiskPreviewCard title={msg} />)}
          </React.Fragment>
        ))}
      </div>
    </div>
  )
}

const GroupNormalRiskViewer: React.FC<{list: BillRiskViewerDataItem[]}> = props => {
  const {
    list
  } = props
  const groupList = useMemo(() => {
    const groupData: Record<string, { meta: { name: string, id: string }, data: BillRiskViewerDataItem[] }> = {}
    for (const riskData of list) {
      const {
        feeTypeId = 'spec',
        feeTypeName = i18n.get('单据')
      } = riskData
      if (!groupData[feeTypeId]) {
        groupData[feeTypeId] = {
          meta: { name: feeTypeName, id: feeTypeId },
          data: []
        }
      }
      groupData[feeTypeId].data.push(riskData)
    }

    return Object.keys(groupData).map(key => groupData[key])
  }, [list])

  return (
    <div className="normal-risk-viewer">
      <div className="normal-risk-viewer-list">
        {groupList.map((group) => (
          <div className="normal-risk-viewer-list-item" key={group.meta.id}>
            <div className="normal-risk-viewer-list-item-title">
              {group.meta.name}
            </div>
            {group.data.map((item) => (
              <React.Fragment key={item.pathValueId}>
                {item.messages.map(msg => <RiskPreviewCard title={msg} />)}
              </React.Fragment>
            ))}
          </div>
        ))}

      </div>
    </div>
  )

}

/**
 * 发票风险展示数据，分组仍然以 feeType ID 来分组
 * @param props
 * @constructor
 */
const InvoiceRiskViewer: React.FC<{list: BillRiskViewerDataItem[]}> = props => {
  const {
    list
  } = props

  const groupList = useMemo(() => {
    const groupData: Record<string, {meta: {name: string, id: string}, data: BillRiskViewerDataItem[]}> =  {}
    for (const riskData of list) {
      if (!groupData[riskData.feeTypeId]) {
        groupData[riskData.feeTypeId] = {
          meta: { name: riskData.feeTypeName, id: riskData.feeTypeId },
          data: []
        }
      }
      groupData[riskData.feeTypeId].data.push(riskData)
    }

    return Object.keys(groupData).map(key => groupData[key])
  }, [list])

  return (
    <div className='invoice-risk-viewer'>
      {groupList.map(group => (
        <div className="invoice-risk-viewer-item" key={group.meta.id}>
          <div className="invoice-risk-viewer-item-title">
            {group.meta.name}
          </div>

            {group.data.map(item => (
              <RiskPreviewCard
                title={item.controlName}
                key={item.controlField}
              >
                {item.messages.map(msg => (
                  <div className="invoice-risk-message-item">
                    {msg}
                  </div>
                ))}

              </RiskPreviewCard>
            ))}
        </div>
      ))}
    </div>
  )
}


type RiskPreviewCardProps = {
  title: string,
  className?: string,
  icon?: React.ReactNode
}
/**
 * 统一使用的 preview card
 * @param props
 * @constructor
 */
const RiskPreviewCard:React.FC<RiskPreviewCardProps> = props => {
  const { title, className, icon, children } = props

  return (
    <div className={classNames('bill-risk-viewer-item', className)}>
      <div className="bill-risk-viewer-item-header">
       <span className='bill-risk-viewer-item-header-icon'> {icon ?? <EkbIcon name='#EDico-fail1'/>}</span>
       <div className='bill-risk-viewer-item-header-title'> {title}</div>
      </div>
      {!!children && (
        <div className="bill-risk-viewer-item-content">
          {children}
        </div>
      )}
    </div>
  )
}


