import { app } from '@ekuaibao/whispered'
import { Accordion as AntdCollapse } from 'antd-mobile'
import React from 'react'


const { Panel: AntdPanel } = AntdCollapse
const EkbIcon = app.require<any>('@elements/ekbIcon')

/**
 * 封装后的 collapse 组件
 * @param props
 * @constructor
 */
const Collapse: React.FC = props => {
  return (
    <AntdCollapse bordered={false} className="bill-info-additional-message-collapse" >
      {props.children}
    </AntdCollapse>
  )
}

type PanelProps = {
  title: string,
  counts?: number,
  extra?: React.ReactElement,
  key: string
}

const Panel: React.FC<PanelProps> = props => {
  const {
    title,
    counts,
    extra,
    children,
    ...otherProps
  } = props
  return (
    <AntdPanel
      {...otherProps}
      header={
        <div>
          <EkbIcon name="#caret-down" className="header-caret-icon" />
          <span className="bill-info-additional-message-info-group-title">
            <span className="font-bold">{title}</span>
            {!!counts && <span className="sub-title">（{counts}）</span>}
            {extra}
          </span>
        </div>
      }
    >
      {children}
    </AntdPanel>
  )
}

interface BillInfoAdditionalCollapseDefine extends React.FC {
  Panel: React.FC<PanelProps>
}

const BillInfoAdditionalCollapse: BillInfoAdditionalCollapseDefine = Collapse as any
BillInfoAdditionalCollapse.Panel = Panel

export {
  BillInfoAdditionalCollapse
}