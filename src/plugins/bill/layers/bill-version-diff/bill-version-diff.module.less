/*
Author: <PERSON> (<EMAIL>)
bill-version-diff.module.less (c) 2022
Desc: description
Created:  2/16/2022, 11:23:36 AM
Modified: 3/2/2022, 11:33:15 AM
*/
@pixel-radio: 2;
.bill-version-diff-empty{
  background-color: var(--white);
  border-radius: 32px;
  padding: 40px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28px;
  color:rgba(20, 34, 52, 0.28);
  flex-direction: column;

  .empty-icon{
    width: 80px;
    height: 80px;
  }
}

.diff-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgba(78, 89, 105, 0.8);
  backdrop-filter: blur(8px);
  overflow: hidden;
  padding: 32px;
  :global {
    .am-accordion{
      overflow: auto;
    }
    .modal-action-bar{
      text-align: center;
      background-color: transparent;
      .modal-action-bar-title,.modal-action-bar-btn{
        color:var(--white);
      }
    }

    h2,
    h3,
    h4 {
      margin: 0 !important;
    }
    .am-accordion::before {
      background-color: transparent !important;
    }
    .am-accordion .am-accordion-item .am-accordion-header {
      .arrow {
        top: 0.4rem;
      }
      background-color: transparent !important;
      height: 112px;
      line-height: 112px;
      color: var(--white);
      &::after {
        background-color: transparent !important;
      }
    }
    .am-accordion .am-accordion-item .am-accordion-content {
      border-radius: 32px;
      .am-accordion-content-box:after {
        background-color: transparent !important;
      }
      .am-accordion-content {
        padding: 32px;
      }
      .am-accordion-header {
        padding: 0 32px;
        border-bottom: 2px solid var(--line-color-base);
      }
    }



  }
}

.detail-loading{
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24px;
  &-left {
    font-weight: 500;
    color: #272e3b;
  }
  &-right {
    color: rgba(39, 46, 59, 0.48);
  }
}

.diff-detail-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--white);
}

.diff-item {
  margin-bottom: 40px;

  & + & {
    border-top: 2px solid rgba(81, 92, 108, 0.08);
  }
}

.icon-right {
  width: 24px;
  height: 24px;
  color: rgba(81, 92, 108, 0.4);
  margin-right: 24px;
}

:global {
  .ant-drawer-content-wrapper {
    min-width: auto;
  }
}

.diff-section-header {
  line-height: normal;

  .header-title-wrapper {

  }

  .header-title {
    display: inline;
    font-size: 14px * @pixel-radio;
    font-weight: bold;
  }

  .header-icon {
    width: 32px;
    height: 32px;
    margin-right: 18px;
  }

}

// 修改版本模块
.version-body {
  &__item {

    .date {
      font-weight: 600;
      color: #2d3139;
      margin-bottom: 16px;
      font-size: 24px;
    }

    .tag-button {
      border-radius: 32px;
      padding: 12px 24px;
      font-size: 24px;
      background: var(--white);
      color: var(--font-color-primary);
      user-select: none;
      display: inline-flex;
      margin-right: 16px;
      margin-bottom: 16px;
      border: 2px solid var(--line-color-base);

      &-active {
        border-color: var(--brand-fadeout-10);
        background: var(--brand-fadeout-10);
        color: var(--brand-base);
      }
    }
  }
}

// 风险提示模块
.risk-warning-body {
  &__alert {
    background: #fff6e8;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    color: #f6903d;
    margin-bottom: 8px;
    cursor: pointer;
  }

  .alert-info {
    flex: 1;
    margin-right: 24px;
  }
}

// 异常信息模块
.abnormal-info-body {
  &__item {
    :global {
      .ant-timeline-item-content {
        display: inline-flex;
        align-items: flex-start;
      }
    }

    .date {
      font-weight: 600;
      color: #2d3139;
      margin-bottom: 36px;
    }

    .date,
    .time,
    .info {
      font-size: 28px;
    }

    .time {
      font-weight: normal;
      color: rgba(45, 49, 57, 0.5);
      margin-right: 24px;
    }

    .info {
      color: #2d3139;
      font-weight: normal;
    }
  }
}

.diff-detail-body {
  flex: 1;
  overflow-y: auto;
}

.diff-card {
  background-color: rgba(78, 89, 105, 0.04);
  border-radius: 16px;
  padding: 24px 32px 24px 64px;
  display: flex;
  flex-direction: column;
  position: relative;
  margin-top: 20px;
  cursor: pointer;

  .diff-invoice-img {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .invoice-img {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      overflow: hidden;
    }
  }

  &-selected {
    border-color: var(--info-base);
  }

  .card-tag {
    position: absolute;
    left: 0;
    top: 24px;
    width: 40px;
    height: 40px;
    background-color: var(--info-base);
    border-radius: 0px 8px 8px 0px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white);
    font-style: normal;
  }

  &-deled {
    background: #f8f9f9;
    cursor: not-allowed;

    .card-tag {
      background-color: #d8d8d8;
    }

    .diff-header-title {
      font-size: 28px;
    }

    .diff-header-title,
    .diff-body-subtext,
    .diff-body-money,
    .diff-body-text {
      color: rgba(45, 49, 57, 0.5);
      text-decoration: line-through;
      text-decoration-color: rgba(45, 49, 57, 0.5);
    }

    .diff-change-before {
      color: rgba(45, 49, 57, 0.25);
      margin-bottom: 24px;
    }
  }

  &-change {
    .card-tag {
      background-color: var(--warning-base);
    }

    &.diff-card-selected {
      border-color: var(--warning-base);
    }

    .diff-card-deled {
      padding: 24px;
      border-radius: 8px;
      margin-top: 24px;
    }
  }

  .diff-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 24px;

    &-title {
      flex: 1;
      margin-right: 24px;
    }

    &-time {
      font-size: 24px;
      color: rgba(45, 49, 57, 0.5);
    }
  }
}
.diff-body {
  padding: 0;
  color: #2d3139;
  font-size: 28px;

  :global {
    .arrow {
      display: none !important;
    }

  }

  &-subtext {
    color: rgba(45, 49, 57, 0.75);
  }

  &-text {
  }

  &-money {
    font-weight: 600;
    margin-top: 24px;
  }
}
.body__ul-title {
  font-size: 28px;
  font-weight: 600;
  line-height: 40px;
  padding-bottom: 16px;
}

.body__ul-list {
  background: var(--warning-bg);
  position: relative;
  border-radius: 16px;
  color: var(--warning-base);
  .body__ul-list-icon {
    display: block;
    width: 6px;
    height: 32px;
    background-color: var(--warning-base);
    border-radius: 0px 8px 8px 0px;
    margin-right: 18px;
  }
  &-info {
    background: rgba(78, 89, 105, 0.04);
    color: #272e3b;
    .body__ul-list-icon {
      background-color: var(--brand-base);
    }
  }
  & + .body__ul-title {
    padding-top: 32px;
  }

  .body__ul-list-item {
    margin: 0;
    padding: 32px 0;
    h4 {
      display: inline-flex;
      align-items: center;
    }
    ul {
      padding: 24px 0 0 24px;
      color: rgba(39, 46, 59, 0.6);
    }

    & + .body__ul-list-item {
      position: relative;
      &::before {
        position: absolute;
        content: '';
        border-top: 2px dashed rgba(78, 89, 105, 0.16);
        width: 90%;
        transform: translateX(-50%);
        padding: 0 0.1rem;
        left: 50%;
        top: 0;
      }
    }
  }
}


:global {
  .spin-loading {
    height: 100%;
    text-align: center;
    .ant-spin {
      position: relative;
      top: 50%;
    }
  }

  .additional-message-button-item {
    padding: 8px * @pixel-radio 12px * @pixel-radio;
    background: rgba(81, 92, 108, 0.04);
    border-radius: 4px * @pixel-radio;
    color: rgba(45, 49, 57, 0.5);
    cursor: pointer;
    font-size: 12px * @pixel-radio;

    & + .additional-message-button-item {
      margin-left: 8px * @pixel-radio;
    }


    &_active {
      background: rgba(34, 178, 204, 0.08);
      color: #22B2CC;
    }
  }

  .diff-item-preview {
    background: #FFFFFF;
    border: 1px * @pixel-radio solid #F2F2F2;
    border-radius: 4px * @pixel-radio;
    margin-top: 12px * @pixel-radio;

    &-header {
      background: #F8F9F9;
      padding: 8px * @pixel-radio 0;

      &-extra {
        float: right;
        margin-right: 12px * @pixel-radio;
        padding: 2px * @pixel-radio 0;
        color: rgba(39, 46, 59, 0.48);
      }
    }

    &-tag {
      display: inline-block;
      color: white;
      padding: 2px * @pixel-radio 8px * @pixel-radio;
      border-radius: 0px * @pixel-radio 4px * @pixel-radio 4px * @pixel-radio 0px * @pixel-radio;
    }

    &_change {
      .diff-item-preview-tag {
        background: #FFAD0D;
      }

      .diff-item-preview-before-content {
        .diff-item-preview-tag {
          background: #E8E8E8;
          color: rgba(39, 46, 59, 0.36);
        }
      }
    }

    &_deled {
      .diff-item-preview-tag {
        background: #E8E8E8;
        color: rgba(39, 46, 59, 0.36);
      }
    }

    &_add {
      .diff-item-preview-tag {
        background: #4C8EFF;
      }
    }

    &-info {
      padding: 16px * @pixel-radio;

      &-entry {

        &-label {
          float: left;
          color: rgba(39, 46, 59, 0.48);
        }

        &-value {
          text-align: right;
        }

        & + .diff-item-preview-info-entry {
          margin-top: 8px * @pixel-radio;
        }
      }
    }



    &-before-content {
      margin-top: 10px * @pixel-radio;
      border-top: 1px * @pixel-radio dashed rgba(78, 89, 105, 0.16);

      .diff-item-preview-tag {
        position: relative;
        top: -12px * @pixel-radio;
      }

    }

    &-before-content,
    &_deled {
      .diff-item-preview-info-entry-value {
        text-decoration-line: line-through;
        color: rgba(39, 46, 59, 0.36);
      }
    }
  }



  .bill-risk-viewer-item {
    background: #FFFFFF;
    border: 1px * @pixel-radio solid #FFEAB3;
    border-radius: 4px * @pixel-radio;

    & + .bill-risk-viewer-item {
      margin-top: 12px * @pixel-radio;
    }

    &-header {
      background: #FFF9EE;
      border-radius: 4px * @pixel-radio;
      padding: 10px * @pixel-radio 12px * @pixel-radio;
      color: #FFAD0D;

      &-icon {
        font-size: 14px * @pixel-radio;
        float: left;

        svg {
          //vertical-align: baseline;

        }
      }
      &-title {

        margin-left: 24px * @pixel-radio;
      }
    }

    &-content {
      padding: 12px * @pixel-radio;
      color: rgba(39, 46, 59, 0.88);
    }
  }


  .invoice-risk-viewer {


    &-item {

      &-title {
        font-size: 14px * @pixel-radio;
        font-weight: bold;
        margin-bottom: 4px * @pixel-radio;
      }
    }
  }

  .normal-risk-viewer {
    &-list {
      &-item {

        &-title {
          font-size: 14px * @pixel-radio;
          font-weight: bold;
          margin-bottom: 4px * @pixel-radio;
        }
      }
    }
  }

  .bill-info-additional-message {

    display: flex;
    flex-direction: column;
    background-color: var(--white);
    overflow-y: auto;
    text-align: left;

    div {
      font-size: 14px * @pixel-radio;
    }

    .header-caret-icon {
      transform: rotate(-90deg);
      transition: transform 0.2s ease;
      color: rgba(39, 46, 59, 0.48);
      margin-right: 8px;
    }
    .am-accordion-item .am-accordion-item-active {
      .header-caret-icon {
        transform: rotate(0deg);
        color: rgba(39, 46, 59, 0.84);
      }
    }


    .font-bold {
      font-weight: bold;
    }

    .summarize-amount {
      font-size: 14px * @pixel-radio;
    }

    &-popup {

      .am-modal {
        border-radius: 1em 1em 0 0;
        overflow: hidden;
      }

      &-wrapper {

      }
      &-header {
        padding: 16px * @pixel-radio;

        &-title {
          color: rgba(20, 34, 52, 0.92);
          font-size: 16px * @pixel-radio !important;
          font-weight: bold;

        }
        &-close {
          width: 24px * @pixel-radio;
          height: 24px * @pixel-radio;
          position: absolute;
          top: 16px * @pixel-radio;
          right: 24px * @pixel-radio;

          svg {
            width: 14px * @pixel-radio;
            height: 14px * @pixel-radio;
          }
        }
      }

      &-content {
        height: 80vh;
        overflow: auto;
      }
    }

    &-panel {

      &>.am-accordion-header {
        padding: 16px * @pixel-radio 24px * @pixel-radio !important;
        height: auto!important;

        i.arrow {
          top: 0.4rem;
          right: 24px * @pixel-radio;
          transform: rotate(0);
        }
        &[aria-expanded~=true] i.arrow {
          transform: rotate(90deg);
        }
      }

      &>.am-accordion-content {
        background: rgba(78, 89, 105, 0.04) !important;
      }
    }

    .am-accordion-content-box {
      padding: 16px * @pixel-radio;
    }

    &-collapse.am-accordion {
      i.arrow {
        display: none;
      }

     .am-accordion-item + .am-accordion-item  {
        margin-top: 8px * @pixel-radio;
      }

      .am-accordion {

        &-header {
          background: white !important;
        }

        &-content {
          padding-top: 8px * @pixel-radio;

          &-box {
            padding-top: 0;
          }
        }

        .arrow {
          display: none !important;
        }

      }
    }


    &-info {
      @content-class-name: bill-info-additional-message-panel-info-content;
      @list-class-name: bill-info-additional-message-panel-info-item;
      @group-class-name: bill-info-additional-message-panel-info-group;
      &-title {
        font-weight: bold;
      }
      &-group {

        .@{content-class-name} {
          padding: 0 16px * @pixel-radio 16px * @pixel-radio 16px * @pixel-radio;
        }

        &-title {
          font-size: 12px * @pixel-radio;
          .sub-title {
            color: rgba(39, 46, 59, 0.48);
          }
          .summarize-amount {
            float: right;
          }
        }

        & + .bill-info-additional-message-panel-info-group {
          margin-top: 8px * @pixel-radio;
        }
      }

      &-list {
        border-radius: 8px * @pixel-radio;
        background: rgba(146, 86, 46, 0.04);
        overflow: hidden;
      }

      &-content {
        background: white;
        padding: 16px * @pixel-radio;
      }

      &-item {
        font-size: 14px * @pixel-radio;
        padding: 16px * @pixel-radio;
        position: relative;

        & + .@{list-class-name} {
          border-top: 1px * @pixel-radio dashed rgba(146, 86, 46, 0.2);


        }

        & + .@{list-class-name}:before {
          content: '';
          display: block;
          width: 8px * @pixel-radio;
          height: 8px * @pixel-radio;
          background: white;
          position: absolute;
          top: -4px * @pixel-radio;
          left: -4px * @pixel-radio;
        }

        & + .@{list-class-name}:after {
          content: '';
          display: block;
          width: 8px * @pixel-radio;
          height: 8px * @pixel-radio;
          background: white;
          position: absolute;
          right: -4px * @pixel-radio;
          top: -4px * @pixel-radio;
        }

        &-label {
          color: rgba(146, 86, 46, 0.75);
          font-size: 14px * @pixel-radio;
          float: left;
        }

        &-value {
          text-align: right;
          color: rgba(39, 46, 59, 0.88);
          font-size: 14px * @pixel-radio;
        }
      }

      &-entity {
        & + .bill-info-additional-message-panel-info-entity {
          margin-top: 8px * @pixel-radio;
        }
      }
    }


    .bill-data-viewer {


      &-item {
        &-label {
          font-size: 12px * @pixel-radio;
        }

        &-value {
          background: #FFFFFF;
          border: 1px * @pixel-radio solid #F2F2F2;
          border-radius: 4px * @pixel-radio;
          padding: 16px * @pixel-radio;
          margin-top: 8px * @pixel-radio;
          font-size: 14px * @pixel-radio;
        }
      }

      .bill-data-viewer-item + .bill-data-viewer-item {
        margin-top: 16px * @pixel-radio;
      }


    }
  }

  .bill-invoice-summarize {

    &-info {
      @content-class-name: bill-invoice-summarize-info-content;
      @list-class-name: bill-invoice-summarize-info-item;
      &-title {
        font-weight: bold;
      }
      &-group {

        .@{content-class-name} {
          padding: 0 16px * @pixel-radio 16px * @pixel-radio 16px * @pixel-radio;
        }

        &-title {
          font-size: 12px * @pixel-radio;
          .sub-title {
            color: rgba(39, 46, 59, 0.48);
          }
          .summarize-amount {
            float: right;
          }
        }

        & + .bill-invoice-summarize-info-group {
          margin-top: 8px * @pixel-radio;
        }
      }

      &-list {
        border-radius: 8px * @pixel-radio;
        background: rgba(146, 86, 46, 0.04);
        overflow: hidden;
      }

      &-content {
        background: white;
      }

      &-item {
        font-size: 14px * @pixel-radio;
        padding: 16px * @pixel-radio;
        position: relative;

        & + .@{list-class-name} {
          border-top: 1px * @pixel-radio dashed rgba(146, 86, 46, 0.2);


        }

        & + .@{list-class-name}:before {
          content: '';
          display: block;
          width: 8px * @pixel-radio;
          height: 8px * @pixel-radio;
          background: white;
          position: absolute;
          top: -4px * @pixel-radio;
          left: -4px * @pixel-radio;
        }

        & + .@{list-class-name}:after {
          content: '';
          display: block;
          width: 8px * @pixel-radio;
          height: 8px * @pixel-radio;
          background: white;
          position: absolute;
          right: -4px * @pixel-radio;
          top: -4px * @pixel-radio;
        }

        &-label {
          color: rgba(146, 86, 46, 0.75);
          font-size: 14px * @pixel-radio;
          float: left;
        }

        &-value {
          text-align: right;
          color: rgba(39, 46, 59, 0.88);
          font-size: 14px * @pixel-radio;

          &>div {
            justify-content: end;
          }
        }
      }

      &-entity {
        & + .bill-invoice-summarize-info-entity {
          margin-top: 8px * @pixel-radio;
        }
      }
    }
  }
}