import React from 'react'
import { TwoToneGeneralAirplane, TwoToneGeneralHotel, TwoToneGeneralTaxi, TwoToneGeneralTrain } from '@hose/eui-icons'
import { getDateFormater } from '@ekuaibao/lib/lib/help'
import moment from 'moment/moment'
import { Resource } from '@ekuaibao/fetch'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import styles from '../all-travel.view.module.less'
import RelevantApplyItem from '../elements/RelevantApplyItem'
const datalinkAction = new Resource('/api/v2/datalink')
const orderTemplate = new Resource('/api/tpp/v2/order')

interface Props {
  submitterId: string
  requisitionId: string
  submitterName: string
}

interface State {
  allOrders: any
  dataArr: string[]
  dataKey: string
  template: any[]
}

export class AllTravelLayer extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      allOrders: null,
      dataArr: [],
      dataKey: '',
      template: []
    }
  }
  componentDidMount() {
    this._getTemplate()
    this._getData()
  }

  _getData = async () => {
    const { submitterId, requisitionId } = this.props
    const allOrders = await datalinkAction.POST('/searchByRequistion', undefined, { requisitionId, submitterId })
    const dataArr = allOrders ? Object.keys(allOrders) : []
    const dataKey = allOrders[dataArr[dataArr.length - 1]]
    dataArr.pop()
    this.setState({
      dataArr,
      dataKey,
      allOrders
    })
  }
  _getTemplate = async () => {
    const res = await orderTemplate.GET('/template')
    const template = res?.items || []
    this.setState({ template })
  }
  render() {
    const dataFormater = getDateFormater(false)
    const { dataArr, dataKey, allOrders, template } = this.state
    const { submitterName } = this.props
    return (
      <div className={styles.allTravelViewWrapper}>
        {dataArr.length > 0 ? (
          dataArr.map(date => {
            const data = allOrders[date]
              .map((item: any) => {
                const sortId = setSortId(item.form['订单类型'])
                return {
                  ...item,
                  sortId
                }
              })
              .sort((pre, next) => {
                return pre.sortId - next.sortId
              })
            return (
              <div key={date}>
                <h4 className={styles.date}>{moment(date).format(dataFormater)}</h4>
                {data.map(order => {
                  const type = order.form['订单类型']
                  const fields = template?.find(v => v.type === typeMap[type]?.type)?.fields || []
                  return (
                    <div className={styles.listContainer}>
                      <div className="type">
                        {typeMap[type]?.icon}
                        <span className="name">{typeMap[type]?.name}</span>
                      </div>
                      <RelevantApplyItem
                        key={order.id}
                        date={date}
                        item={order.form}
                        dataKey={dataKey}
                        submitterName={submitterName}
                        fields={fields}
                        from={'all'}
                      />
                    </div>
                  )
                })}
              </div>
            )
          })
        ) : (
          <></>
        )}
      </div>
    )
  }
}

const setSortId = (type: string) => {
  switch (type) {
    case '酒店':
      return 1
    case '飞机':
      return 2
    case '火车':
      return 3
    case '用车':
      return 4
    default:
      return 0
  }
}

const typeMap = {
  飞机: {
    type: 'FLIGHT',
    icon: <TwoToneGeneralAirplane />,
    name: i18n.get('机票')
  },
  火车: {
    type: 'TRAIN',
    icon: <TwoToneGeneralTrain />,
    name: i18n.get('火车')
  },
  酒店: {
    type: 'HOTEL',
    icon: <TwoToneGeneralHotel />,
    name: i18n.get('酒店')
  },
  用车: {
    type: 'TAXI',
    icon: <TwoToneGeneralTaxi />,
    name: i18n.get('用车')
  }
}

@((EnhanceTitleHook as any)(i18n.get('全部行程消费信息')))
export default class AllTravelLayerClass extends React.Component<Props> {
  render() {
    return <AllTravelLayer {...this.props} />
  }
}
