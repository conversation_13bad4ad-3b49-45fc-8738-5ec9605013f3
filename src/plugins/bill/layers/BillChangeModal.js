import React, { PureComponent } from 'react';
import { app as api } from '@ekuaibao/whispered'
import <PERSON>hanceTit<PERSON>Hook from '../../../lib/EnhanceTitleHook'
import { toast } from '../../../lib/util';
import { Button } from 'antd-mobile';
import styles from './BillChangeModal.module.less';
import { flowDoAction } from './../bill.action'
import { NoticeBar, Form, TextArea } from '@hose/eui-mobile'
@EnhanceTitleHook(i18n.get('变更申请'))
export default class BillChangeModal extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            value: '',
            placeholder: '请填写变更申请理由',
            loading: false
        }
    }
    onChange = (value) => {
        this.setState({ value })
    }
    handleSubmit = async () => {
        const { value, placeholder } = this.state
        const { flowId, formType, mustRequire } = this.props
        if (!value && mustRequire) {
            toast.info(placeholder)
            return
        }
        const data = {
            flowIds: [
                flowId
            ],
            action: {
                name: "freeflow.alter",
                comment: value
            }
        }
        this.setState({
            loading: true
        });
        const result = await flowDoAction(data).catch(e => {
            this.setState({
                loading: false
            })
            toast.info(e.message || e.msg)
        });

        if (result?.value?.success) {
            api.invokeService('@requisition:save:current:requisition:id', flowId)
            setTimeout(() => {
                api.go(`/rejected/${formType}/${flowId}`, true)
                this.props.layer.emitCancel();
            }, 500);
        } else {
            toast.info(result?.value?.messages.join(','))
            this.setState({
                loading: false
            })
        }
    }

    render() {
        const { value, loading } = this.state;
        return (
            <div className={styles.billChangeContainer}>
                <NoticeBar content={i18n.get('发起变更将使申请单进入待提交状态，您可以对申请单进行编辑编辑完成后，单据需要重新审批，审批完成前，申请单将不可关联订票或报销【变更申请】操作不可撤回，请谨慎操作。')} color="alert" />
                <Form name="form" >
                    <Form.Item
                        name="name33"
                        label={i18n.get('变更申请理由')}
                        rules={[{ required: true, message: '请填写变更申请理由' }]}
                    >
                        <TextArea
                            placeholder="请填写变更申请理由"
                            value={value}
                            onChange={this.onChange}
                            showCount
                            maxLength={1000}
                        />
                    </Form.Item>
                </Form>
                <div className={styles.btnContainer}>
                    <Button loading={loading} type='primary' onClick={this.handleSubmit}>确定</Button>
                </div>
            </div>
        )
    }
}
