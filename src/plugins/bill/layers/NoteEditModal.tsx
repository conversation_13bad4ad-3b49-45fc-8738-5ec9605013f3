import { app } from '@ekuaibao/whispered'
/**************************************
 * Created By LinK On 2020/4/17 19:55.
 **************************************/

import React, { PureComponent } from 'react'
import styles from './NoteListModal.module.less'
import { <PERSON><PERSON>, Picker, TextareaItem } from 'antd-mobile'
import { app as api } from '@ekuaibao/whispered'
const EKBIcon = app.require<any>('@elements/ekbIcon')
const EnhanceFormCreate = app.require<any>('@elements/enhance/enhance-form-create')
import { toast } from '../../../lib/util'
import { EnhanceConnect } from '@ekuaibao/store'
import { deleteNoteById } from '../utils/billUtils'

interface Props extends StringAnyProps {
  cardConfigs: any
  data: any
  layer: any
  myCreditPoint: any
}

@EnhanceConnect((state: any) => ({
  Credit: state['@common'].powers.Credit,
  detailReadable: state['@bill'].detailReadable,
  creditNoteAvailable: state['@bill'].creditNoteAvailable,
  myCreditPoint: state['@bill'].myCreditPoint,
  activeCreditRuleWithGroups: state['@bill'].activeCreditRuleWithGroups
}))

@EnhanceFormCreate()
export default class NoteEditModal extends PureComponent<Props, StringAnyProps> {
  state = {
    type: ['NORMAL'],
    typeMap: { NORMAL: i18n.get('普通批注'), CREDIT: i18n.get('信用批注') },
    text: '',
    id: ''
  }

  componentDidMount() {
    const type = this.getCanEditNoteType() ? ['CREDIT'] : ['NORMAL']
    const { activeCreditRuleWithGroups } = this.props
    const defaultCreditRule = activeCreditRuleWithGroups[0] || {}
    const el = defaultCreditRule.rules?.[0]
    el && this.formatData(el)
    this.setState({ type })

  }

  formatData = (el: any) => {
    const { detailReadable } = this.props
    const { id, label, score } = el
    let text = label
    if (detailReadable) {
      const scoreValue = score >= 0 ? `+${score}` : score
      const fontColor = score >= 0 ? { color: '#00B42A' } : {}
      text = (
        <div className="note-option">
          <div className="note-option-text">{text}</div>
          <div className="note-option-score" style={fontColor}>{scoreValue}</div>
        </div>
      )
    }
    this.setState({ text, id })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleAdd = () => {
    this.props.layer.emitCancel()
  }

  handleSubmit = () => {
    this.props.form.validateFieldsAndScroll((errors: any, values: any) => {
      const { page, flowId, detailId, isDetail, field, noteId } = this.props
      const { type, id } = this.state
      if (page === 'add') {
        //添加批注
        let params: any = {
          dataType: 'FLOW',
          dataId: flowId,
          type: type[0],
          subDataType: isDetail ? 'FEETYPE' : 'FLOW',
          subDataId: isDetail ? detailId : flowId,
          onField: field.field
        }
        if (type[0] === 'NORMAL') {
          // 普通批注
          const { contentValue } = values
          if (!contentValue || !contentValue.trim()) {
            return toast.info(i18n.get('请输入批注'), 1000)
          }
          params.content = contentValue
        } else {
          // 信用批注
          if (!id) {
            return toast.info(i18n.get('请选择信用批注'), 1000)
          }
          params.ruleId = id
        }
        api.invokeService('@bill:add:bill:note', params).then(() => {
          if (type[0] === 'CREDIT') {
            //@ts-ignore
            this.fnRefreshMyCreditPoint()
          }
        })
      } else {
        //删除批注
        const { deleteReason } = values
        if (!deleteReason) {
          return toast.info(i18n.get('请输入删除原因'), 1000)
        }
        deleteNoteById({ flowId, noteId, reason: deleteReason }).then(() => {
          //@ts-ignore
          this.fnRefreshMyCreditPoint(type[0])
        })
      }
      this.props.layer.emitCancel()
    })
  }

  fnRefreshMyCreditPoint = () => {
    const { myCreditPoint } = this.props
    const { submitterId } = myCreditPoint
    setTimeout(() => {
      api.invokeService('@bill:get:my:credit:point', submitterId)
    })
  }

  renderHeader = () => {
    const { page } = this.props
    const text = page === 'add' ? i18n.get('添加批注') : i18n.get('删除批注')
    return (
      <div className="noteListModal-header noteEditModal-header">
        <div className="noteListModal-actionBar-btn grayout" onClick={this.handleCancel}>
          <EKBIcon name="#EDico-back" />
        </div>
        <span className="noteListModal-title">{text}</span>
      </div>
    )
  }

  renderDelContent = () => {
    const { form } = this.props
    const { getFieldDecorator } = form
    return (
      <div className="note-edit-content-wrap">
        {getFieldDecorator('deleteReason', {
          rules: [{ required: true }]
        })(<TextareaItem rows={5} count={100} className="note-edit-input" placeholder={i18n.get('请输入删除原因')} />)}
      </div>
    )
  }

  handleTypeOnChange = (value: string[]) => {
    this.setState({ type: value })
  }

  getCanEditNoteType = () => {
    const { Credit = false, creditNoteAvailable = false } = this.props
    return (Credit && creditNoteAvailable)
  }

  handleOpenSelectActiveCreditRuleModal = () => {
    const { activeCreditRuleWithGroups, detailReadable } = this.props
    api.open('@home5:SelectActiveCreditRule', { params: { activeCreditRuleWithGroups, detailReadable } }).then((res: any) => {
      this.formatData(res)
    })
  }

  renderEditContent = () => {
    const { form } = this.props
    const { type, typeMap, text } = this.state
    const { getFieldDecorator } = form
    //@ts-ignore
    const noteText = typeMap[type[0]]
    //@ts-ignore
    const canSeletNoteType = this.getCanEditNoteType()

    return (
      <div className="note-edit-content-wrap">
        <Picker
          data={[
            { label: <span className="note-option">{i18n.get('普通批注')}</span>, value: 'NORMAL' },
            { label: <span className="note-option">{i18n.get('信用批注')}</span>, value: 'CREDIT' }
          ]}
          prefixCls={styles['note-picker-popup-wrap']}
          disabled={!canSeletNoteType}
          cols={1}
          value={type}
          onChange={this.handleTypeOnChange}
        >
          <div className="note-edit-select">
            {noteText}
            {canSeletNoteType && <EKBIcon name="#EDico-right-default" />}
          </div>
        </Picker>
        {type[0] === 'NORMAL' ? (
          <>
            {getFieldDecorator('contentValue', {
              rules: [{ required: true }]
            })(<TextareaItem rows={5} count={100} className="note-edit-input" placeholder={i18n.get('请输入批注')} />)}
          </>
        ) : (
          <div onClick={this.handleOpenSelectActiveCreditRuleModal}>
            <div className="note-edit-select" style={{ borderBottom: 'none' }}>
              {text}
              <EKBIcon name="#EDico-right-default" />
            </div>
          </div>
        )}
      </div>
    )
  }

  renderContent = () => {
    const { page } = this.props
    if (page === 'add') {
      return this.renderEditContent()
    } else {
      return this.renderDelContent()
    }
  }

  renderFooter = () => {
    return (
      <div className="note-footer">
        <Button type="primary" onClick={this.handleSubmit}>
          {i18n.get('确定')}
        </Button>
      </div>
    )
  }

  render() {
    return (
      <div className={styles['noteListModal-wrapper']}>
        {this.renderHeader()}
        {this.renderContent()}
        {this.renderFooter()}
      </div>
    )
  }
}
