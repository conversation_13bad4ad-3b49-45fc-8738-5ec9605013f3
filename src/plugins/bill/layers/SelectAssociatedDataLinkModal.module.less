@import '../../../styles/ekb-colors.less';
@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.dataLink-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f2f3f5;
  overflow: hidden;
  :global {
    .eui-tabs {
      display: flex;
      flex-direction: column;
      background-color: transparent;
      height: 100%;
      overflow: hidden;
      .eui-tabs-header {
        background-color: white;
        flex-shrink: 0;
      }
      .eui-tabs-content {
        flex: 1;
        padding: 0;
        margin-top: 16px;
        overflow: hidden;
        .tabs-content-wrapper {
          display: flex;
          height: 100%;
          flex-direction: column;
        }
      }
    }

    .top-datalink-card-wrapper {
      background-color: white;
      padding: 18px 24px 24px;
      .top-datalink-card {
        border-radius: 16px;
        background: var(--eui-bg-body-overlay, #f7f8fa);
        .eui-list-body {
          border-top: 0;
          border-bottom: 0;
          background-color: transparent;
          .eui-list-item {
            .eui-list-item-content-wrapper{
              overflow: hidden;
              .eui-list-item-content-main{
                overflow: hidden;
                .eui-list-item-description{
                  span{
                    white-space   : nowrap;
                    text-overflow : ellipsis;
                    overflow      : hidden;
                  }
                }
              }
            }
            .eui-list-item-conent-tilte-ellipsis{
              white-space   : nowrap;
              text-overflow : ellipsis;
              overflow      : hidden;
            }
          }
        }
      }
    }
    .header-title {
      padding: 32px 32px 16px;
      font: var(--eui-font-body-r1);
      color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
    }
    .search-bar-wrapper {
      background-color: white;
      width: 100%;
      padding: 16px 32px;
    }
    .search-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: white;
      flex-shrink: 0;
      padding: 0px 32px;
      height: 72px;
      width: 100%;
      z-index: 3;
      .header-right {
        display: flex;
        cursor: pointer;
        .filterItem {
          margin-left: 28px;
        }
      }
      .type_name {
        display: flex;
        align-items: center;
        .type_name_text{
          font: var(--eui-font-body-r1);
          color: var(--eui-text-title);
        }
        svg {
          margin-left: 4px;
          transition: 0.2s ease all;
          &.rotate {
            transform: rotate(180deg);
          }
        }
      }
    }
    .multi-list {
      flex: 1;
      background-color: #f2f3f5;
      margin-bottom: 128px;
      overflow: scroll;
      .eui-list-body {
        border-top: 0;
        border-bottom: 0;
      }
      .eui-list-item {
        padding-left: 0px;
        &:first-of-type {
          .eui-list-item-content-wrapper {
            border-top: 0px;
          }
        }
        .eui-list-item-content-prefix-icon{
          padding-right: 0;
        }
        .eui-list-item-content-wrapper{
          overflow: hidden;
          .eui-list-item-content-main{
            overflow: hidden;
            .eui-list-item-description{
              span{
                white-space   : nowrap;
                text-overflow : ellipsis;
                overflow      : hidden;
              }
            }
          }
        }
        .eui-list-item-conent-tilte-ellipsis{
          white-space   : nowrap;
          text-overflow : ellipsis;
          overflow      : hidden;
        }
      }
    }
    .bottom_bar {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      padding: 20px 32px;
      display: flex;
      z-index: 999;
      box-shadow: var(--eui-shadow-up-1);
      justify-content: center;
      align-items: center;
      background-color: var(--eui-static-white);
      transform: translate3d(0, 0, 0);
    }
  }
}

.popup-modal {
  overflow: hidden;
  height: 85vh;
  display: flex;
  flex-direction: column;
  :global {
    .modal-title {
      display: flex;
      padding: @space-5 @space-6 @space-4;
      align-items: center;
      .cancel {
        .font-weight-2;
        .font-size-3;
        color: @color-black-3;
      }
      .title {
        flex: 1;
        .font-weight-3;
        .font-size-4;
        text-align: left;
        color: @color-black-1;
      }
    }
    .modal-title-tips {
      .font-weight-2;
      font-size: 24px;
      text-align: left;
      padding-left: @space-6;
      line-height: 32px;
      color: rgba(39, 46, 59, 0.48);
    }
  }
}
