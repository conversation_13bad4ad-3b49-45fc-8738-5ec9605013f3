import { app } from '@ekuaibao/whispered'
const className = window.isPC && window.__PLANTFORM__ === 'KD_CLOUD' ? 'debugger_top_fix mt-45' : 'debugger_top_fix'

export default [
  {
    key: 'ModifyModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: true,
      animated: true,
      visible: true,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper',
      width: 340
    },
    getComponent: () => import('../elements/modify-modal')
  },
  {
    key: 'FollowWeChatModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: true,
      animated: true,
      visible: true,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper',
      width: 280
    },
    getComponent: () => import('../elements/followWeChat-modal')
  },
  {
    key: 'BillComment',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../elements/bill-comment.view')
  },
  {
    key: 'DataLinkModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../elements/datalink/DataLinkList')
  },
  {
    key: 'BlockUIModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./BlockUIModal')
  },
  {
    key: 'CheckPayeeModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix', loadingText: i18n.get('保存中') },
    getComponent: () => import('./CheckPayeeModal'),
    title: i18n.get('补全收款信息')
  },
  {
    key: 'FeetypeRiskModal',
    enhancer: 'modal',
    enhancerOptions: {
      width: 340,
      visible: true,
      transparent: true,
      animated: true,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper'
    },
    getComponent: () => import('../elements/FeetypeRiskModal')
  },
  {
    key: 'TripTmcGetMoney',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../elements/TripTmcGetMoney')
  },
  {
    key: 'SingleFeetypeRiskModal',
    enhancer: 'modal',
    enhancerOptions: {
      width: 340,
      visible: true,
      transparent: true,
      animated: true,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper'
    },
    getComponent: () => import('../elements/SingleFeetypeRiskModal')
  },
  {
    key: 'MultiSelectDataLinkModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../elements/MultiSelectDataLinkList')
  },
  {
    key: 'SelectAssociatedDataLinkStep1Modal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./SelectAssociatedDataLinkStep1Modal')
  },
  {
    key: 'SelectAssociatedDataLinkStep2Modal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./SelectAssociatedDataLinkStep2Modal')
  },
  {
    key: 'NewOrderQuoteSelectList',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../elements/newMicroOrder/NewOrderQuoteSelectList')
  },
  {
    key: 'ImportTripOrderConfirmModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: true,
      animated: false,
      visible: true,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper modal-container'
    },
    getComponent: () => import('./ImportTripOrderConfirmModal')
  },
  {
    key: 'BillSubmittingWithDialogModal',
    enhancer: 'modal',
    enhancerOptions: {
      width: '100%',
      height: '100%',
      visible: true,
      transparent: true,
      maskClosable: false,
      animated: false,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper fix-ekb-enhance-modal-clean-modal-content-background'
    },
    getComponent: () => import('../elements/BillSubmittingWithDialogModal')
  },
  {
    key: 'BillRiskReasonModal',
    enhancer: 'layer',
    getComponent: () => import('../elements/BillRiskReasonModal')
  },
  {
    key: 'ConfirmModal',
    enhancer: 'modal',
    enhancerOptions: {
      width: 263,
      visible: true,
      transparent: true,
      animated: false,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper'
    },
    getComponent: () => import('./ConfirmModal')
  },
  {
    key: 'DataLinkEntityDetailModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../elements/datalink/datalink-detail-modal/DataLinkEntityDetailModal')
  },
  {
    key: 'TripRiskModal',
    enhancer: 'modal',
    enhancerOptions: {
      width: 340,
      visible: true,
      transparent: true,
      animated: true,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper'
    },
    getComponent: () => import('../elements/datalink/TripRiskModal')
  },
  {
    key: 'DataLinkEntityTripOrderDetailModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../elements/datalink/datalink-detail-modal/DataLinkEntityTripOrderDetailModal')
  },
  {
    key: 'NewOrderDetail',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../elements/newMicroOrder/NewOrderDetail')
  },
  {
    key: 'ChildDimensionModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'modal-popup-wrapper'
    },
    getComponent: () => import('../elements/datalink/datalink-detail-modal/ChildDimensionModal')
  },
  {
    key: 'AddDataLinkModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../elements/datalink/datalink-detail-modal/AddDataLinkModal')
  },
  {
    key: 'PersonnelListModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'modal-popup-wrapper'
    },
    getComponent: () => import('../elements/datalink/datalink-detail-modal/elements/PersonnelListModal')
  },
  {
    key: 'AddPayPlanModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./AddPayPlanModal')
  },
  {
    key: 'RemunerationDetail',
    enhancer: 'layer',
    enhancerOptions: { className: className },
    getComponent: () => Promise.resolve({ default: app.require('@elements/puppet/Remuneration/Detail') })
  },
  {
    key: 'Remuneration',
    enhancer: 'layer',
    enhancerOptions: { className: className },
    getComponent: () => Promise.resolve({ default: app.require('@elements/puppet/Remuneration') })
  },
  {
    key: 'NoteListModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      wrapClassName: 'popup-modal-border-radius'
    },
    getComponent: () => import('./NoteListModal')
  },
  {
    key: 'NoteEditModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      wrapClassName: 'popup-modal-border-radius  note-edit-modal'
    },
    getComponent: () => import('./NoteEditModal')
  },
  {
    key: 'ExpenseDeleteWithDialogModal',
    enhancer: 'modal',
    enhancerOptions: {
      width: 270,
      visible: true,
      transparent: true,
      animated: false,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper'
    },
    getComponent: () => import('./ExpenseDeleteWithDialogModal')
  },
  {
    key: 'ExpenseNullifyModal',
    enhancer: 'modal',
    enhancerOptions: {
      width: 270,
      visible: true,
      transparent: true,
      animated: false,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper'
    },
    getComponent: () => import('./ExpenseNullifyModal')
  },
  {
    key: 'ReportsDetailListModal',
    enhancer: 'modal',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('./ReportsDetailListModal')
  },
  {
    key: 'MutiCurrencyWrittenOffModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      wrapClassName: 'popup-modal-border-radius'
    },
    getComponent: () => import('./MutiCurrencyWrittenOffModal')
  },
  {
    key: 'BillDetailModal',
    enhancer: 'layer',
    enhancerOptions: { className: className },
    getComponent: () => import('./BillDetailModal')
  },
  {
    key: 'BillChangeModal',
    enhancer: 'layer',
    enhancerOptions: { className: className },
    getComponent: () => import('./BillChangeModal')
  },
  {
    key: 'BillVersionDiff',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'bill-info-additional-message-popup'
    },
    getComponent: () =>
      import('./bill-version-diff/bill-info-additional-message-popup').then(v => v.BillInfoAdditionalMessagePopup)
  },
  {
    key: 'BillHistoryModal',
    getComponent: () => import('../history'),
    enhancer: 'modal',
    title: '修改记录',
    bodyStyle: {
      height: '70vh'
    },
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: true,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'modal-popup-wrapper new-popup'
    }
  },
  {
    key: 'AllTravelLayer',
    enhancer: 'layer',
    enhancerOptions: { className: className },
    getComponent: () => import('./AllTravelLayer')
  }
]
