import styles from './ConfirmModal.module.less'
import React, { PureComponent } from 'react'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager-mobile'
import PNG_CONFIRM_MODAL from '../images/confirmModal.png'
import { app as api } from '@ekuaibao/whispered'

interface Props {
  type: 'create' | 'edit'
}

export default class ConfirmModal extends PureComponent<Props & ILayerProps> {
  _$UUID: string

  componentWillUnmount() {
    api.backControl.remove(this._$UUID)
  }

  componentWillMount() {
    this._$UUID = String(Math.random())
    api.backControl.create(this._$UUID, () => {
      this.backHookCallback()
    })
  }

  backHookCallback = () => {
    api.backControl.remove(this._$UUID)
    api.backControl.invoke()
    return true
  }

  handleCancel = () => {
    this.props.layer.emitOk({ type: 'cancel' })
  }

  handleOk = () => {
    this.props.layer.emitOk({ type: 'success' })
  }

  typeMap = {
    create: i18n.get('你上一次编辑的单据还未完成，是否继续编辑？'),
    edit: i18n.get('此单据有未保存的草稿，是否继续编辑？')
  }
  cancelTextMap = {
    create: i18n.get('新建单据'),
    edit: i18n.get('取消编辑')
  }
  render() {
    const { type } = this.props
    const title = this.typeMap[type || 'create']
    const cancelText = this.cancelTextMap[type || 'create']
    return (
      <div className={styles['ConfirmModal_wrapper']}>
        <img src={PNG_CONFIRM_MODAL} />
        <div className="confirmModal_content">
          <div className="text">{i18n.get(title)}</div>
          <div className="btn" onClick={this.handleOk}>
            {i18n.get('继续编辑')}
          </div>
          <div className="btn btn_no_boder" onClick={this.handleCancel}>
            {i18n.get(cancelText)}
          </div>
        </div>
      </div>
    )
  }
}
