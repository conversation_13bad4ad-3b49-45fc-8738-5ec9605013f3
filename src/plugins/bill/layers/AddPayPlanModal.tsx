import styles from './AddPayPlanModal.module.less'
import React, { PureComponent } from 'react'
import <PERSON>han<PERSON><PERSON><PERSON><PERSON><PERSON>ook from '../../../lib/EnhanceTitleHook'
import { Dynamic } from '@ekuaibao/template'
import { app, app as api } from '@ekuaibao/whispered'
const editable = app.require('@components/dynamic/index.editable')
const readonly = app.require('@components/dynamic/index.readonly')
import BillPayPlanHeader from '../components/payPlan/BillPayPlanHeader'
import createDOMForm from 'rc-form/lib/createDOMForm'
import MessageCenter from '@ekuaibao/messagecenter'
import { uuid } from '@ekuaibao/helpers'
import { getCurrentMoney } from '../components/payPlan/payPlanUtil'
import { Fetch } from '@ekuaibao/fetch'
import { getV } from '@ekuaibao/lib/lib/help'
import { toast } from '../../../lib/util'
import moment from 'moment'
import { checkBaiwangPreviewUrl } from '../../../components/dynamic/inner/dataLinkUtil'
import { getBoolVariation } from '../../../lib/featbit'
import { getRecieptPrintUrl } from '../bill.action'
const { getPreviewUrl } = app.require('@components/utils/fnAttachment')

export interface Props {
  fnSetState?: (state: any, fn?: () => void) => void
  fnSetFormValue?: () => void
  summaryMoney?: any
  currentMoney?: number
  isModifyBill?: boolean
  payPlanList: any[]
  layer: any
  payPlan?: any
  index?: number
  inReadonly?: boolean
  flowId: string
  inPartialPayState?: boolean
  isHaveRecipt: boolean
  billState: String
  receiptURL?: any
  paymentChannelIsNSTC: boolean
  billSpecification?: any
  receiptList?: any[]
}

export interface State {
  value: any
  payPlanList: any[]
  currentMoney: number
  reOpen: boolean
}

// @ts-ignore
@EnhanceTitleHook((props: Props) => {
  const { payPlanList = [], index, inReadonly, isHaveRecipt, billState } = props
  let title =
    inReadonly && (isHaveRecipt || billState === 'paid' || billState === 'archived')
      ? i18n.get('支付计划/记录')
      : i18n.get('支付计划')
  if (typeof index === 'number') {
    // 编辑
    title += i18n.get(` {__k0}（共{__k1}条）`, { __k0: index + 1, __k1: payPlanList.length })
  } else {
    // 新建
    title += ` ${payPlanList.length + 1}`
    if (payPlanList.length) {
      title += i18n.get(`（共{__k0}条）`, { __k0: payPlanList.length })
    }
  }
  return title
})
export default class AddPayPlanModal extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    const { payPlan = {}, payPlanList = [], currentMoney } = props
    this.state = {
      value: {
        ...payPlan,
        payPlanStatus: payPlan.status
      },
      payPlanList,
      currentMoney,
      reOpen: false
    }
    this.initTemplate()
  }

  bus: any = new MessageCenter()
  template: any[]
  createAgain: boolean = false // 是否点击了再记一笔

  initTemplate = () => {
    const { inPartialPayState, payPlanList } = this.props
    let template: any = [
      {
        active: true,
        canAsDimension: false,
        dataType: { type: 'ref', entity: 'pay.PayeeInfo' },
        editable: true,
        field: 'payeeId',
        labelCopy: i18n.get('收款信息'),
        name: 'payeeId',
        optional: false,
        placeholder: i18n.get('请选择收款信息'),
        showInDetails: false,
        type: 'payeeInfo'
      },
      {
        canAsDimension: false,
        dataType: { type: 'money' },
        defaultValue: { type: 'none' },
        editable: true,
        field: 'amount',
        isShowThousandsSeparator: false,
        labelCopy: i18n.get('支付金额'),
        max: '10000000000.00',
        min: '0.00',
        name: 'amount',
        optional: false,
        placeholder: i18n.get('请输入支付金额'),
        showInDetails: false,
        type: 'money'
      }
    ]

    // 点击支付计划时，payPlan有值；添加支付计划时，payPlan没有值
    // 所有支付计划中的某条有带法人实体字段时，当前计划展示法人实体字段
    const haveLegalEntity = payPlanList.some(el => el.E_system_支付计划_legalEntity)
    if (haveLegalEntity) {
      // @ts-ignore
      template.push({
        canAsDimension: true,
        dataType: { type: 'ref', entity: 'basedata.Dimension.法人实体' },
        defaultValue: { type: 'none' },
        editable: true,
        field: 'E_system_支付计划_legalEntity',
        labelCopy: i18n.get('法人实体'),
        multiple: false,
        name: 'E_system_支付计划_legalEntity',
        optional: true,
        placeholder: i18n.get('请选择法人实体'),
        selectRange: 'all',
        showInDetails: false,
        type: 'ref:basedata.Dimension.法人实体'
      })
    }

    if (inPartialPayState) {
      // @ts-ignore
      template.push({
        canAsDimension: false,
        dataType: { type: 'payPlanStatus' },
        defaultValue: { type: 'none' },
        editable: false,
        field: 'payPlanStatus',
        labelCopy: i18n.get('支付计划状态'),
        name: 'payPlanStatus',
        optional: true,
        placeholder: i18n.get('请输入支付金额'),
        showInDetails: false,
        type: 'payPlanStatus'
      })
    }

    if (payPlanList?.[0]?.isShowAPInfo) {
      const currencyList = [
        {
          canAsDimension: false,
          dataType: { type: 'money' },
          defaultValue: { type: 'none' },
          editable: false,
          field: 'amount',
          isShowThousandsSeparator: false,
          labelCopy: i18n.get('应付币种金额'),
          name: 'payableCurrencyStandard',
          optional: false,
          placeholder: i18n.get('请输入应付币种金额'),
          showInDetails: false,
          type: 'money'
        },
        {
          canAsDimension: false,
          dataType: { type: 'money' },
          defaultValue: { type: 'none' },
          editable: false,
          field: 'amount',
          isShowThousandsSeparator: false,
          labelCopy: i18n.get('实付币种金额'),
          name: 'paidAmount',
          optional: false,
          placeholder: i18n.get('请输入实付币种金额'),
          showInDetails: false,
          type: 'money'
        },
        {
          name: 'paidOffLineFinishedTime',
          type: 'text',
          editable: false,
          label: i18n.get('实际支付时间'),
          optional: false,
          isvalidatorLev: 1
        },
        {
          name: 'paidRate',
          type: 'text',
          editable: false,
          label: i18n.get('实际实付币种汇率支付时间'),
          optional: false,
          isvalidatorLev: 1
        }
      ]
      template = template.concat(currencyList)
    }

    this.template = template
  }

  componentWillMount() {
    this.bus.on('element:ref:select:payee', this.handleSelectPayeeInfo)
    this.bus.on('element:money:value:changed', this.handleMoneyChange)
    this.bus.watch('element:ref:select:property', this.handleSelectProperty)
  }

  componentWillUnmount() {
    this.bus.un('element:ref:select:payee')
    this.bus.un('element:money:value:changed')
    this.bus.un('element:ref:select:property')
  }

  handleSelectProperty = (field: any) => {
    return api
      .invokeService('@common:get:property:by:name', { name: 'basedata.Dimension.法人实体' })
      .then((data: any) => {
        return api.open('@basic:SelectProperty', {
          data,
          label: '法人实体',
          type: field.type,
          showBottom: true,
          canSelectParent: true
        })
      })
      .then((data: any) => {
        this.setState(preState => ({ value: { ...preState.value, E_system_支付计划_legalEntity: data } }))
        return data
      })
  }

  handleMoneyChange = (money: any) => {
    if (!money) {
      return this.setState({ reOpen: true }, () => this.setState({ reOpen: false }))
    }
    this.setState(preState => ({ value: { ...preState.value, amount: money } }))
  }

  handleSelectPayeeInfo = (selectedPayeeId: string, dependenceList: any[], dependence: any) => {
    const { isModifyBill = false, flowId, billSpecification } = this.props
    const templateid = billSpecification?.id

    return api
      .open(getBoolVariation('new_version_of_payment_account') ? '@basic:SelectPayee' : '@basic:SelectPayeeOld', {
        isModifyBill,
        flowId,
        selectedPayeeId,
        dependenceList,
        dependence,
        templateid,
        billSpecification
      })
      .then((dataSource: any) => this.setState(preState => ({ value: { ...preState.value, payeeId: dataSource } })))
  }

  handleSave = (createAfterSave?: boolean) => {
    const { fnSetState, fnSetFormValue, payPlan, index } = this.props
    const { payPlanList } = this.state
    this.bus
      .getValueWithValidate(1)
      .then((currentValue: any) => {
        if (payPlan && !this.createAgain) {
          payPlanList[index] = { ...currentValue, id: payPlan.id }
        } else {
          payPlanList.push({ ...currentValue, id: uuid(14) })
        }
        const currentMoney = getCurrentMoney(payPlanList)
        fnSetState({ payPlanList: [...payPlanList], currentMoney }, fnSetFormValue)
        if (createAfterSave) {
          // 再记一笔
          const title =
            i18n.get('支付计划') +
            i18n.get(` {__k0}（共{__k1}条）`, { __k0: payPlanList.length + 1, __k1: payPlanList.length })
          api.invokeService('@layout:change:header:title', title)
          this.createAgain = true
          this.setState({ value: {}, payPlanList, currentMoney })
        } else {
          this.props.layer.emitCancel()
        }
      })
      .catch((e: any) => {
        console.log('handleSave:catch:error', e)
      })
  }

  handlePreviewReceipt = async () => {
    const { receiptURL } = this.props
    const previewUrl = getV(receiptURL, 'value.form.E_system_电子回单_预览地址.E_system_电子回单_h5PreUrl', '')
    if (previewUrl) {
      const url = await checkBaiwangPreviewUrl(previewUrl)
      api.invokeService('@layout:open:link', url)
    }
  }

  printReceiptLine = () => {
    const { payPlan } = this.props
    getRecieptPrintUrl(payPlan?.receiptId?.[0])
      .then(res => {
        const url = res?.id
        url && api.invokeService('@layout:preview:file', url, false, i18n.get('预览'))
      })
      .catch(e => {
        toast.error(e?.errorMessage || i18n.get('预览失败'))
      })
  }

  handleOpenPDF = () => {
    const { payPlan, receiptURL, paymentChannelIsNSTC } = this.props
    if (paymentChannelIsNSTC) {
      return this.handlePreviewReceipt()
    }
    const receiptIds = payPlan.receiptId
    this.fnOpenFile(receiptIds, receiptURL)
  }

  fnOpenFile = (receiptIds: any, receiptURL: any) => {
    const { url, fileName } = receiptURL.value
    // TODO: 此if最终要删除
    if (url?.includes('hose-hdt-payment')) {
      const prefix = 'https://doc.ekuaibao.com/view/url?url='
      api.invokeService('@layout:open:link', `${prefix}${encodeURIComponent(url)}`)
      return
    }
    const openFilePreview = (previewUrl?: string) => {
      if (previewUrl) {
        api.invokeService('@layout:preview:file', previewUrl, false, i18n.get('预览PDF'))
      } else {
        let link = `${window.PREVIEW_DOMAIN}/view/url?url=${encodeURIComponent(url)}&name=${fileName}`
        const watermark = api.getState()['@common'].waterMark
        link = watermark && watermark !== '' ? link + '&watermark=' + encodeURIComponent(watermark) : link
        api.invokeService('@layout:preview:file', link, false, i18n.get('预览PDF'))
      }
    }

    Fetch.GET(`/api/pay/v1/receipt/getFileKeysByIds/[${receiptIds}]`).then(res => {
      if (res.value) {
        const params = { key: fileName, corpId: Fetch.ekbCorpId }
        getPreviewUrl(params)
          .then((result: any) => {
            const link = result.value?.url
            openFilePreview(link)
          })
          .catch((err: any) => {
            console.log(err)
            openFilePreview()
          })
      } else {
        toast.info(i18n.get('请去回单管理中心重新获取回单文件'))
      }
    })
  }

  handleOpenReceiptFile = async (receipt: any) => {
    const previewUrl = getV(receipt, 'dataLink.E_system_电子回单_预览地址.E_system_电子回单_h5PreUrl', '')
    if (previewUrl) {
      const url = await checkBaiwangPreviewUrl(previewUrl)
      return api.invokeService('@layout:open:link', url)
    }
    const id = getV(receipt, 'dataLink.id')
    const receiptURL = await Fetch.POST(`/api/pay/v3/receipt/getFileUrl/[${id}]/${id}`)
    this.fnOpenFile(id, receiptURL)
  }

  renderReceipt = () => {
    const { receiptList, inReadonly, payPlan } = this.props
    const flag = getBoolVariation('pay-receipt-print-flag')
    if (receiptList) {
      return (
        <div className="recipt-wrap">
          <div className="recipt-title">{i18n.get('回单文件')}</div>
          <div className="receipt-list">
            {receiptList.map((receipt: any) => {
              const flag = getV(receipt, 'dataLink.E_system_借贷标志')
              const time = getV(receipt, 'dataLink.E_system_交易日期')
              const timeStr = time ? moment(time)?.format('YYYY-MM-DD') : '-'
              const label = `${timeStr}（${flag}）`
              return (
                <div className="receipt-item">
                  <span>{label}</span>
                  <div className="link" onClick={() => this.handleOpenReceiptFile(receipt)}>
                    {i18n.get('预览')}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )
    }
    if (inReadonly && !!payPlan?.receiptId?.length) {
      return (
        <div className="recipt-wrap">
          <div className="recipt-title">{i18n.get('回单文件')}</div>
          <div className="recipt-cont">
            <div className="link" onClick={flag ? this.printReceiptLine : this.handleOpenPDF}>
              {i18n.get('预览')}
            </div>
          </div>
        </div>
      )
    }
    return null
  }

  render() {
    const { summaryMoney, inReadonly = false, isHaveRecipt, billState } = this.props
    const { value, currentMoney, reOpen } = this.state
    if (reOpen) return null

    const components: any = inReadonly ? readonly : editable
    const titleStyle = inReadonly ? { marginTop: 16 } : {}
    return (
      <div className={styles['addPayPlanModal-wrap']}>
        {!inReadonly && (
          <div className="addPayPlanModal-summary">
            <BillPayPlanHeader
              hasValidation={false}
              summaryMoney={summaryMoney}
              currentMoney={summaryMoney.standard * 1 - currentMoney}
            />
          </div>
        )}
        <div className="addPayPlanModal-form-title" style={titleStyle}>
          {inReadonly && (isHaveRecipt || billState === 'paid' || billState === 'archived')
            ? i18n.get('支付计划/记录明细')
            : i18n.get('支付计划明细')}
        </div>
        <Dynamic
          className="addPayPlanModal-form"
          bus={this.bus}
          elements={components}
          template={this.template}
          value={value}
          noPayInfo={!value?.payeeId}
          create={T => createDOMForm()(T)}
        />
        {this.renderReceipt()}
        {!inReadonly && (
          <>
            <div className="addPayPlanModal-btn addPayPlanModal-btn-primary" onClick={() => this.handleSave()}>
              {i18n.get('确 定')}
            </div>
            <div className="addPayPlanModal-btn" onClick={() => this.handleSave(true)}>
              {i18n.get('再记一笔')}
            </div>
          </>
        )}
      </div>
    )
  }
}
