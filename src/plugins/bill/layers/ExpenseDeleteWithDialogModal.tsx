import React, { PureComponent } from 'react'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager-mobile'
import { batchSaveWithNotes } from '../bill.action'
import { toast } from '../../../lib/util'
import { deleteFeeDetail2RecordNote } from '../../../lib/dataflux/billLogEvent'
import { Fetch } from '@ekuaibao/fetch'
import { get } from 'lodash'
import { Dialog } from '@hose/eui-mobile'
import { app } from '@ekuaibao/whispered'

interface Props {
  deleteFlow: Function
  state: any
  data: any[]
  submitterId: string
  details?: any
}

export default class ExpenseDeleteWithDialogModal extends PureComponent<Props & ILayerProps> {
  state = {
    isQuickExpense: false,
    loading: true
  }

  async componentDidMount() {
    const { state } = this.props
    if (this.state.loading) {
      const { flowId } = state
      const { value } = await Fetch.GET(`/api/v1/detailFLowRelation/$${flowId}`)
      this.setState({ isQuickExpense: value === 'QUICK_EXPENSE', loading: false })
    }
  }

  get tagText() {
    return this.state.isQuickExpense ? '快速报销' : '随手记'
  }

  handleDeleteAndMove = () => {
    const { data, details } = this.props
    // 单据明细中有公务卡时，删除单据到随手记，将明细中的取公务卡金额字段和取公务卡结算方式字段置空
    if (details?.length && JSON.stringify(details)?.includes('transact')) {
      Dialog.confirm({
        title: i18n.get('提示'),
        content: (
          <>
            <p>{i18n.get('确认将消费明细移入随手记？')}</p>
            <p>{i18n.get('移入后，消费明细中的公务卡订单信息、取公务卡金额字段和结算方式将被清除')}</p>
          </>
        ),
        onConfirm: () => this.handleDeleteCSC(data, details)
      })
    } else {
      this.handleDeleteAndMoveAction(data)
    }
  }

  handleDeleteCSC = (data: any, details: any) => {
    details?.forEach((item: any) => {
      const components = get(item, 'specificationId.components', [])
      const CSCRelated = components?.filter((v: any) => {
        const dataType = get(v, 'type')
        const defaultValueType = get(v, 'defaultValue.type')
        const isOfficialCardMoney = dataType === 'money' && defaultValueType === 'officialCardMoney'
        const isOfficialCardSettlement = dataType === 'select' && defaultValueType === 'officialCardSettlement'
        return isOfficialCardMoney || isOfficialCardSettlement
      })
      const detailId = get(item, 'feeTypeForm.detailId')
      let paramsItem = data?.find((el: any) => el?.data?.detailId === detailId)
      if (CSCRelated?.length && paramsItem) {
        CSCRelated.forEach((el: any) => {
          if (get(paramsItem, `data.${el.field}`)) {
            paramsItem.data[el.field] = undefined
          }
        })
      }
    })
    this.handleDeleteAndMoveAction(data)
  }

  logDeleteDetail = () => {
    try {
      const { state, details } = this.props
      const userInfo = app.getState()['@common'].me_info?.staff
      if (!details?.length) {
        return
      }
      const logObj = {
        corpId: userInfo?.corporationId?.id,
        staffId: userInfo?.id,
        logTime: Date.now(),
        billSpecificationId: state?.data?.form?.specificationId?.id,
        flowId: state?.flowId,
        billCode: state?.data?.form?.code,
        details: details.map((item: any) => {
          const obj = {
            detailId: item.feeTypeForm?.detailId,
            specificationId: item.specificationId?.id
          } as any
          if (!!item.feeTypeForm?.invoiceForm?.invoices?.length) {
            obj['invoiceIds'] = item.feeTypeForm.invoiceForm.invoices.map((el: any) => el?.invoiceId?.id)
          }
          return obj
        })
      }
      deleteFeeDetail2RecordNote(logObj)
    } catch (e) {
      console.log(e)
    }
  }

  handleDeleteAndMoveAction = (data: any) => {
    const { isQuickExpense } = this.state
    const tag = this.tagText
    const { submitterId, state } = this.props
    this.logDeleteDetail()
    data.length
      ? batchSaveWithNotes(data, isQuickExpense, { submitterId }).then(() => {
          setTimeout(() => {
            // 判断是否有权限删除并移动到随手记
            const owner_id = state?.data?.ownerId?.id
            const staff_id = app.getState('@common')?.me_info?.staff?.id
            const canMoveToNote = owner_id === staff_id ? true : false
            if (canMoveToNote) {
              toast.success(i18n.get(`费用已被移至${tag}`))
            }
          }, 800)
          this.fnDelete()
        })
      : this.fnDelete()
  }

  handleDelete = () => {
    this.fnDelete()
  }

  fnDelete = () => {
    const { deleteFlow, state = {} } = this.props
    if (state && state.flowId) {
      deleteFlow(state.flowId).then(() => {
        this.props.layer.emitOk({})
      })
    } else {
      this.props.layer.emitOk({})
    }
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  render() {
    const tag = this.tagText
    if (this.state.loading) return null
    const Universal = app.getState()['@common']?.powers?.Universal
    const actions = [
      {
        key: 'cancel',
        text: i18n.get('取消'),
        onClick: this.handleCancel,
        style: {
          color: 'var(--eui-text-link-normal)'
        }
      }
    ] as any

    const { autoExpenseWithBillStriction, state } = this.props
    if (!(state?.formType === 'expense' && autoExpenseWithBillStriction)) {
      actions.unshift({
        key: 'deleteFlowFee',
        text: i18n.get('删除单据及费用'),
        onClick: this.handleDelete,
        style: {
          color: 'var(--eui-text-link-normal)'
        }
      })
    }

    if (!Universal) {
      actions.unshift({
        key: 'deleteFlow',
        text: i18n.get('仅删除单据'),
        bold: true,
        style: {
          color: 'var(--eui-text-link-normal)',
          fontWeight: 'bold'
        },
        onClick: this.handleDeleteAndMove
      })
    }

    return (
      <Dialog
        visible={true}
        title={i18n.get('确认删除单据及费用？')}
        content={!Universal ? i18n.get(`仅删除单据，费用将被移至${tag}`) : ''}
        closeOnAction={true}
        iconType="warn"
        actions={actions}
        maskStyle={{ background: 'unset' }}
      />
    )
  }
}
