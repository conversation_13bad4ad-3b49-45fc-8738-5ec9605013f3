@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.mutiCurrencyWrittenOffModal-wrap{
  flex: 1;
  background-color: @color-white-1;
  min-width: 0;
  :global{
    .mutiCurrencyWrittenOffModal-content {
      padding: @space-6 @space-7;
      text-align: left;
      .writtenOffTitle, .profitTitle, .payMoneyTitle {
        &:first-child {
          padding-top: 0;
        }
        .font-size-4;
        .font-weight-2;
        padding: @space-8 0 @space-5;
        color: @color-black-1;
      }
    }
    .cardEditModal-actionBar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: @color-white-1;
      height: 96px;
      margin-bottom: @space-6;
      min-width: 0;
      .cardEditModal-title {
        .font-size-4;
        .font-weight-3;
        color: @color-black-1;
        overflow: hidden;
        white-space: nowrap;
        min-width: 0;
        text-overflow: ellipsis;
      }
      .cardEditModal-actionBar-btn {
        color: @color-brand-2;
        padding: 0 @space-6;
        .font-size-3;
        height: 96px;
        min-width: 128px;
        line-height: 96px;
        white-space: nowrap;

        &.grayout {
          color: @color-black-4;
        }
      }
    }
  }
}

