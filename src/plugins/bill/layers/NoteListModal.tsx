/**************************************
 * Created By LinK On 2020/4/17 19:55.
 **************************************/

import React, { PureComponent } from 'react'
import styles from './NoteListModal.module.less'
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import NoteContentLine from '../elements/NoteContentLine'
import classnames from 'classnames'
import { IllustrationMiddleNoContent, OutlinedTipsNo } from '@hose/eui-icons'
import RiskWarningItem from './NoteRiskWarning'
import RiskWarningBudgetItem from './NoteRiskWarningBudget'
import { riskType } from '../../../elements/puppet/RiskNotice/type'
import { Tag } from '@hose/eui-mobile'
import Markdown from '../../../elements/Markdown'
import { isErrorItem } from '../../../elements/puppet/RiskNotice/ai-audit-result/utils'
interface Props extends StringAnyProps {
  cardConfigs: any
  data: any
  layer: any
}

interface PricesProps {
  name:string,
  percent:string,
  usedBudget:string,
  symbol:string
}

const renderItem = (item: {
  messages?:any[],
  messagesV2?: Array<{
    invoiceMsg: string,
    invoiceId: string,
    pathValueId: string,
    relatedFlows: Array<{flowCode: string, invoiceNum: string}>
    invoiceRiskExplainContent?: string
  }>,
  prices?: PricesProps[],
  type?: string,
  isOutOfLimitReject?:Boolean
}) => {
  const { messages = [], messagesV2 = [] , prices = [], type, } = item
  if (type === 'budget' && !!prices?.length){
    return <RiskWarningBudgetItem risk={item} />
  } else if (messagesV2.length > 0) {
    return messagesV2.map((message, idx) => (
      <div className="risk-item-content" key={message.invoiceMsg}>
        <div className="flex">
          <div className="risk-item-label">
            {i18n.get('风险信息')}:
          </div>
          <div className="flex-1 risk-item-info">
            {message.invoiceMsg}
          </div>
        </div>
        {message.invoiceRiskExplainContent && (
          <div className="flex">
            <div className="risk-item-label">
              {i18n.get('原因')}:
            </div>
            <div className="flex-1 risk-item-info">
              {message.invoiceRiskExplainContent}
            </div>
          </div>
        )}
        {!!message.relatedFlows?.length && (<div className="flex">
          <div className="risk-item-label">
            {i18n.get('相关单据')}:
          </div>
          <div className="flex-1 risk-item-info">
            {message.relatedFlows.map(item => `${item.flowCode}${item.invoiceNum ? `(${item.invoiceNum})` : ''}`).join('，')}
          </div>
        </div>)}

      </div>
    ))
  } else if (type === 'costControl'){
    return  <RiskWarningItem risk={item} />
  } else if(messages.length > 0){
    return (
      <div className={`risk-item-content ${isErrorItem(item as any) ? 'error' : ''}`} >
        {
          messages.map((v, idx) => (
            <div className="risk-content-detail-item" key={idx}>
              { messages.length === 1 ? <Markdown context={v} type="risk" /> : `${(idx + 1)}、` + v }
            </div>
          ))
        }
      </div>
    )
  }
  return <></>
}


export default class NoteListModal extends PureComponent<Props, StringAnyProps> {
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleAdd = () => {
    const { flowId, detailId, isDetail, field, canEditNote = false } = this.props
    if (!canEditNote) {
      return
    }
    this.props.layer.emitCancel()
    setTimeout(() => {
      // @ts-ignore
      api.open('@bill:NoteEditModal', { page: 'add', flowId, detailId, isDetail, field })
    })
  }

  handleChangePageToDelete = (noteId: string) => {
    const { flowId } = this.props
    this.props.layer.emitCancel()
    setTimeout(() => {
      // @ts-ignore
      api.open('@bill:NoteEditModal', { page: 'delete', flowId, noteId })
    })
  }

  renderHeader = () => {
    const { canEditNote = false, showBillNotesInHistory, field } = this.props
    const addBtnStyle = !showBillNotesInHistory && canEditNote ? {} : { opacity: 0 }
    const fnAdd = !showBillNotesInHistory && canEditNote ? this.handleAdd : () => {}

    return (
      <div className="noteListModal-header">
        <div className="noteListModal-actionBar-btn grayout" onClick={this.handleCancel}>
          {i18n.get('关闭')}
        </div>
        <span className="noteListModal-title">{this.props.headerTitle || i18n.get('查看批注')}</span>
        {field !== 'Credit-Note-Pop' ? <div className="noteListModal-actionBar-btn" style={addBtnStyle} onClick={fnAdd}>
          {i18n.get('添加批注')}
        </div> : <div className="noteListModal-actionBar-btn" />}
      </div>
    )
  }

  renderNoteByType = (noteType: 'NORMAL' | 'CREDIT') => {
    const { noteArr, canEditNote, authorRemovable, detailReadable, showBillNotesInHistory, field } = this.props
    const singleNoteArr = noteArr.filter((el: any) => el.type === noteType)
    if (!singleNoteArr.length) {
      return null
    }
    const canDeleteNote = !showBillNotesInHistory && canEditNote && (authorRemovable || noteType === 'NORMAL')
    console.log(`[ 系统配置是否让编辑 ] >: canDeleteNote: ${canDeleteNote}
    !showBillNotesInHistory: ${!showBillNotesInHistory}
    canEditNote: ${canEditNote}
    (authorRemovable || noteType === 'NORMAL'): ${(authorRemovable || noteType === 'NORMAL')}
    authorRemovable: ${authorRemovable}
    noteType: ${noteType}
    `)
    return (
      <div className="note-item">
        {field !== 'Credit-Note-Pop' && <div className="note-type">{noteType === 'NORMAL' ? i18n.get('普通批注') : i18n.get('信用批注')}</div>}
        {singleNoteArr.map((note: any, idx: number) => {
          const { content, authorName, rule, authorId } = note
          const name = i18n.get(`{__k0}：`, { __k0: authorName })
          // @ts-ignore
          const staffId = api.getState('@common.me_info.staff.id')
          console.log(canDeleteNote, '[ 系统配置的 canDeleteNote  true 才对比 用户信息 ] >')
          let isShowDelete = canDeleteNote
          if (canDeleteNote) {
            isShowDelete = authorId === staffId
            console.log(`[ authorId === staffId ] >: isShowDelete: ${isShowDelete}
          ${authorId} ${isShowDelete ? '同一个人' : "不同一个人"} ${staffId}
          `)
          }
          let text
          if (noteType === 'NORMAL') {
            text = <NoteContentLine name={name} text={content} />
          } else {
            const permissions = api.getState('@common.me_info.permissions') || []
            isShowDelete = isShowDelete || !!~permissions?.indexOf('CREDIT_MANAGE')
            console.log(`[ permissions isShowDelete ] >: isShowDelete: ${isShowDelete}
          用户权限permissions: ${permissions}
          !!~permissions?.indexOf('CREDIT_MANAGE'): ${!!~permissions?.indexOf('CREDIT_MANAGE')}
          `)
            const label = get(rule, 'label', '')
            const score = get(rule, 'score', '')
            text = detailReadable ? (
              <NoteContentLine name={name} text={label} score={score} />
            ) : (
              <NoteContentLine name={name} text={content} />
            )
          }
          console.log('[最后展示: isShowDelete ] >', isShowDelete)
          return (
            <div className='note-container'>
              {isShowDelete && <OutlinedTipsNo className="note-delete-btn" fontSize={22} onClick={() => this.handleChangePageToDelete(note.id)} />}
              <div className={`note-content ${!canDeleteNote ? 'note-content-no-icon' : ''}`}>
                {text}
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  renderRisk = () => {
    const { riskList = [], riskTitle = i18n.get('超标详情') } = this.props
    if (!riskList.length) {
      return null
    }
    return (
      <div className="note-item">
        {riskTitle && <div className="note-type">{riskTitle}</div>}
        <div className="risk-content">
          {riskList.map((item: any, index: number) => {
            const isCostControl = item?.type === 'costControl'
            const riskTypeString = i18n.get(riskType[item?.type] || item?.type)

            return (
              <div className="risk-items" key={index}>
                {!isCostControl ? (
                  <div className={'risk-item-title'}>
                    <div className={classnames({
                      'risk-item-text': item.controlVersion,
                      'dis-f': true,
                      'ai-c': true,
                    })}>
                      {
                        !item?.prices?.length && <Tag color={isErrorItem(item) ? 'danger' : 'warning'} fill="invert" className="mr-8 fs-12" size="small">
                          {riskTypeString}
                        </Tag>
                      }

                       <span>{item.controlName}</span>
                      {!!item.controlVersion && <span>:</span>}
                    </div>
                    {!!item.controlVersion && (
                      <div className="version">{i18n.get(` 依据版本{__k0}`, { __k0: item.controlVersion })}</div>
                    )}
                  </div>
                ) : null}
                {renderItem(item)}
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  renderEmptyNote = () => {
    const { field, noteArr = [] } = this.props
    if (field === 'Credit-Note-Pop' && noteArr?.length === 0) {
      return <div className="empty-note">
        <IllustrationMiddleNoContent fontSize={200} />
        <div className="no-content">{i18n.get('无信用批注')}</div>
      </div>
    }
  }
  render() {
    const { field } = this.props
    return (
      <div className={styles['noteListModal-wrapper']}>
        {this.renderHeader()}
        <div className="note-content-wrap">
          {this.renderRisk()}
          {field !== 'Credit-Note-Pop' && this.renderNoteByType('NORMAL')}
          {this.renderNoteByType('CREDIT')}
          {this.renderEmptyNote()}
        </div>
      </div>
    )
  }
}
