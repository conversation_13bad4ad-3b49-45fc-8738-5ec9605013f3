/**************************************************
 * Created by nanyuanting<PERSON> on 28/12/2017 14:48.
 **************************************************/
import React, { PureComponent } from 'react'
import { Fetch } from '@ekuaibao/fetch'
import { <PERSON> } from '@ekuaibao/painter'
import { app as api } from '@ekuaibao/whispered'
export default class BlockUIModal extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { value: undefined }
  }
  componentDidMount() {
    const { data } = this.props
    const { apiKey, ...others } = data
    Fetch.POST(`/api/engine/blockUI/$${apiKey}`, {}, { body: others }).then(res => {
      if (res?.value) {
        this.setState({ value: res?.value })
      }
    })
  }
  getActions = () => {
    const {flowId:oldFlowId} = this.props
    return {
      'app:open:flow': value => {
        const { flowId } = value
         api.invokeService('@common:get:flow:detail:info', { id: flowId, isBack: true }).then(() => {
         api.open('@bill:BillDetailModal', {
          params: { id: flowId , oldFlowId,noUpdate:true}
          })
        })
      }
    }
  }
  render() {
    const { value } = this.state
    if (!value) {
      return null
    }
    return (
      <div className="display-flex-wh p-20">
        <Painter {...value} actions={this.getActions()}></Painter>
      </div>
    )
  }
}
