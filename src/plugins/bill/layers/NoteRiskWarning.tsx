import React, { memo } from 'react'
import { riskTypeMap, riskColorMap, getRiskComponent } from '../utils/noteRiskWarning'
import styles from './NoteRiskWarning.module.less'
import { isErrorItem } from '../../../elements/puppet/RiskNotice/ai-audit-result/utils'
interface IProps {
  risk: any;
  riskType?: string;
  riskName?: string;
  riskStatus?: string;
  riskVersion?: number;
  children?: any;
}
interface TitleProps {
  riskType: string;
  riskName?: string;
  riskVersion?: number;
}

const RiskWarningItem: React.FC<IProps> = memo(props => {
  const { risk = {}, riskType = '', riskName = '', riskStatus = '', riskVersion = 0, children = null } = props
  const name = riskName || risk.controlName
  let version = riskVersion || risk.controlVersion
  const type = riskType || risk.type
  const status = riskStatus || (isErrorItem(risk) ? 'error' : 'warn')
  const classnames = riskColorMap[type][status] || ''
  const componentMap: any = getRiskComponent()
  if (type === 'costControl' && version) {
    const ruleDetail = risk?.ruleDetail ?? ''
    if (ruleDetail.indexOf('版本') > -1 || ruleDetail.indexOf('version') > -1) {
      version = 0
    }
  }
  return (
    <div className={`${styles['risk-warning-item-wrapper']} ${styles[classnames]}`}>
      <RiskWarningItemTitle riskType={type} riskName={name} riskVersion={version} />
      <div className="risk-warning-item-content">
        {componentMap[type]?.(risk) || children}
      </div>
    </div>
  )
})
export default RiskWarningItem

export const RiskWarningItemTitle = (props: TitleProps) => {
  const { riskType, riskName, riskVersion } = props
  return (
    <div className="risk-warning-item-title">
      <span className="risk-tag">{riskTypeMap[riskType]}</span>
      {riskName && (
        <span className="risk-name">{riskName}</span>
      )}
      {riskVersion && riskVersion > 0 ? (
        <span className="risk-version"> {i18n.get(`[版本{__k0}]`, { __k0: riskVersion })}</span>
      ) : null}
    </div>
  )
}