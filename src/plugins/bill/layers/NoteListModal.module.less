@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.noteListModal-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: @color-white-1;
  min-width: 0;
  :global {
    .noteEditModal-header {
      justify-content: center !important;
      .noteListModal-actionBar-btn {
        position: absolute;
        left: 0;
        top: 0;
        .icon {
          font-size: 40px;
          color: @color-black-1;
        }
      }
    }
    .noteListModal-header {
      flex-shrink: 0;
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: @color-white-1;
      height: 96px;
      min-width: 0;
      .noteListModal-title {
        .font-size-3;
        font-weight: 500;
        color: rgba(39, 46, 59, 0.96);
        overflow: hidden;
        white-space: nowrap;
        min-width: 0;
        text-overflow: ellipsis;
      }
      .noteListModal-actionBar-btn {
        padding: 0 @space-6;
        .font-size-3;
        height: 96px;
        line-height: 96px;
        white-space: nowrap;
        color: var(--brand-base);
        min-width: 60px;

        &.grayout {
          color: rgba(39, 46, 59, 0.72);
        }
      }
    }
    .note-content-wrap {
      flex: 1;
      overflow: auto;
      .note-item {
        margin-bottom: @space-6;
        .note-type {
          padding: 0 @space-6;
          margin-bottom: @space-4;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: rgba(39, 46, 59, 0.96);
          font-weight: 500;
          .font-size-3;
        }
        .note-container {
          display: flex;
          flex-direction: row;
          width: 100%;
          min-height: 108px;
        }

        .note-delete-btn {
          margin: 32px;
          color: #F53F3F;
        }
        .note-content {
          position: relative;
          padding: @space-6 @space-6 @space-6 0; // 已与严老师确认：上下padding10px为实现最小行高32px
          display: flex;
          align-items: center;
          justify-content: flex-start;
          min-height: 108px;
          width: 100%;
          color: rgba(39, 46, 59, 0.72);
          .font-size-2;
          border-bottom: 2px solid rgba(39, 46, 59, 0.06);
          .note-item-content-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: left;
          }
          .note-item-content-score {
            flex-shrink: 0;
            .font-weight-3;
            margin-left: @space-2;
            color: #CDAA72;
          }

          >div {
            flex: 1;
            text-align: left;
          }
        }
        .note-content-no-icon {
          padding: @space-6;
        }
        //风险提示中复制过来的
        .risk-content {
          padding: @space-6;
          padding-bottom: 0;
          .risk-items {
            &:not(:first-child) {
              margin-top: 32px;
            }

            &:last-child {
              border-bottom: none;
            }

            .risk-item-title {
              display: flex;
              flex-direction: row;
              align-items: center;
              font-size: 28px;
              color: rgba(39, 46, 59, 0.72);
              margin-bottom: 32px;
              justify-content: space-between;
              .risk-item-text {
                text-align: left;
                width: 400px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis
              }
              .version {
                font-size: 24px;
                color: rgba(0, 0, 0, 0.45);
              }
            }
            .risk-item-content {
              display: flex;
              flex-direction: column;
              align-items: self-start;
              padding: 24px;
              font-size: 28px;
              line-height: 48px;
              color: rgba(39, 46, 59, 0.72);
              background-color: var(--eui-function-warning-50);
              border-radius: 8px;
              max-height: 680px;
              overflow: auto;
              >div {
                text-align: left;
                width: 100%;
              }
              .warning{
                color: var(--eui-function-warning-500);
              }

              &.error{
                background-color:var(--eui-function-danger-50); ;
              }

              .risk-item-label {
                padding-right: 20px;
                color: rgb(152 152 152);
              }
              .risk-item-info {
                color: rgb(47 54 66);
              }

            }
            .risk-item-content + .risk-item-content {
              margin-top: 20px;
            }
            .flex {
              display: flex;
            }
          }
        }
      }
    }
    .note-edit-content-wrap {
      flex: 1;
      .note-edit-select {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: @space-4 0 @space-4 @space-3;
        min-height: 96px;
        color: @color-black-1;
        //border-top: 1px @color-line-1 solid;
        border-bottom: 1px @color-line-1 solid;
        margin: 0 @space-6;
        .font-weight-3;
        .note-option {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .note-option-text {
            overflow: hidden;
            flex: 1;
            text-align: left;
            .font-weight-2;
          }
          .note-option-score {
            .font-weight-3;
            flex-shrink: 0;
            margin-right: @space-4;
            margin-left: @space-6;
            color: #FF7D00;
          }
        }
      }
      .note-edit-input {
        padding-left: @space-3;
        margin: 0 @space-6;
        background-color: transparent;
        border-bottom: 1px @color-line-1 solid;
        textarea {
          color: @color-black-1;
        }
      }
    }
    .note-footer {
      flex-shrink: 0;
      position: relative;
      display: block;
      background-color: @color-white-1;
      padding: @space-4;
      height: 116px;
      .shadow-3;
      * {
        user-select: none;
      }
      .am-button {
        user-select: none;
        display: flex;
        align-items: center;
        justify-content: center;
        .font-size-3;
        .font-weight-3;
      }
    }
    .empty-note {
      height: calc(100% - 96px);
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .no-content {
        margin-top: 20px;
      }
    }
  }
}

.note-picker-popup-wrap {
  :global {
    .note-option {
      color: @color-black-1;
.note-option-score {
  .font-weight-3;
  margin-left: @space-3;
  color: #FF7D00;
}
}
}
}
.note-picker-popup-wrap-new {
  :global {


    .am-picker-col {
      height: 540px;

      .am-picker-col-indicator {
        height: 108px;
      }

      .am-picker-col-content {
        .am-picker-col-item {
          height: 108px;

          .note-option {
            color: @color-black-1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 32px;
            font-size: 30px;
            line-height: 44px;

            .note-option-score {
              .font-weight-3;
              color: #FF7D00;
            }
          }
        }
      }
    }

  }
}