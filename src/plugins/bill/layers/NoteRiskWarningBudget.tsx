import React, { memo } from 'react'
import { riskTypeMap, riskColorMap, getRiskComponent } from '../utils/noteRiskWarning'
import styles from './NoteRiskWarning.module.less'
interface IProps {
  risk: any;
  riskType?: string;
  riskName?: string;
  riskStatus?: string;
  riskVersion?: number;
  children?: any;
}
interface TitleProps {
  riskType: string;
  riskName?: string;
  riskVersion?: number;
}

const RiskWarningBudgetItem: React.FC<IProps> = memo(props => {
  const { risk = {}, riskType = '', riskName = '', children = null } = props
  const type = riskType || risk.type
  return (
    <div>
      {risk?.prices?.map((item, idx) => {
        const componentMap = getRiskComponent()
        const classnames = riskColorMap[type][item.color] || ''
        return (
          <div className={`${styles['risk-warning-item-wrapper']} ${styles[classnames]}`} key={idx}>
            <RiskWarningItemTitle riskType={type} riskName={item?.name} />
            <div className="risk-warning-budget-content">
              {componentMap[type]?.(item) || children}
            </div>
          </div>
        )
      })}
    </div>
  )
})
export default RiskWarningBudgetItem

export const RiskWarningItemTitle = (props: TitleProps) => {
  const { riskType, riskName } = props
  return (
    <div className="risk-warning-item-title">
      <span className="risk-tag">{riskTypeMap[riskType]}</span>
      {riskName && (
        <span className="risk-name">{riskName}</span>
      )}
    </div>
  )
}