/**
 *  Created by <PERSON><PERSON><PERSON> on 2022/1/26 14:17.
 */
import React, { PureComponent } from 'react'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager-mobile'
import './ExpenseNullifyModal.less'
import { batchSaveWithNotes } from '../bill.action'
import { toast } from '../../../lib/util'

interface Props {
  nullifyFlow: Function
  state: any
  data: any[]
  submitterId: string
}

export default class ExpenseNullifyModal extends PureComponent<Props & ILayerProps> {
  handleNullifyAndMove = () => {
    const { data, submitterId } = this.props
    data.length
      ? batchSaveWithNotes(data, false, { submitterId }).then(() => {
          setTimeout(() => {
            toast.success(i18n.get('费用已被移至随手记'))
          }, 800)
          this.fnNullify()
        })
      : this.fnNullify()
  }

  handleNullify = () => {
    this.fnNullify()
  }

  fnNullify = () => {
    const { nullifyFlow, state = {} } = this.props
    if (state && state.flowId) {
      nullifyFlow(state.flowId).then(() => {
        this.props.layer.emitOk({})
      })
    } else {
      this.props.layer.emitOk({})
    }
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  render() {
    return (
      <div className="expense-nullify-wrapper">
        <div className="expense-nullify-title">{i18n.get('确认作废单据及所有费用？')}</div>
        <div className="expense-nullify-content">
          {i18n.get('费用被移至「随手记」后，您可在「随手记」中继续编辑这些费用')}
        </div>
        <div className="nullify-actions">
          <div className="action" onClick={this.handleNullifyAndMove}>
            {i18n.get('作废单据并将费用移至「随手记」')}
          </div>
          <div className="action" onClick={this.handleNullify}>
            {i18n.get('作废单据及所属费用')}
          </div>
          <div className="action" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </div>
        </div>
      </div>
    )
  }
}
