/**************************************
 * Created By LinK On 2020/4/17 19:55.
 **************************************/

import React, { PureComponent } from 'react'
import { LightTable } from '@ekuaibao/eui-isomorphic'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import './ReportsDetailListModal.less'

interface Props extends StringAnyProps {
  cardConfigs: any
  data: any
  layer: any
}

export default class ReportsDetailListModal extends PureComponent<Props, StringAnyProps> {
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  renderTable() {
    const {
      data: { table },
      tableMaxHeight
    } = this.props
    if (!table) {
      return null
    }
    const columns = table.schema.map((schema: any) => {
      const column: any = {
        title: schema.caption,
        dataIndex: schema.name,
        width: schema.width * 8
      }

      if (schema.caption === '操作') {
        column.render = (text: any, line: any) => {
          return (
            <span
              style={{ color: 'var(--brand-base)' }}
              onClick={() => {
                api.open('@bill:BillDetailModal', {
                  // isShowWaitInvoice: false,
                  // isNeedBudget: false,
                  params: { id: line[schema.name] }
                })
              }}
            >
              {i18n.get('单据详情')}
            </span>
          )
        }
      }

      if (schema.dataType === 'datetime') {
        column.render = (text: any) => moment(text).format('YYYY/MM/DD')
      }

      if (schema.dataType === 'boolean') {
        column.render = (text: any) => (text ? '是' : '否')
      }

      return column
    })
    const dataSource = table.data
    return <LightTable columns={columns} rowHeight={40} dataSource={dataSource} tableMaxHeight={tableMaxHeight}/>
  }

  render() {
    return (
      <div className="reportsDetailListModal-wrap" style={{ padding: '16px' }}>
        {this.renderTable()}
      </div>
    )
  }
}
