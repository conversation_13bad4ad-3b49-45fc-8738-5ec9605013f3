import { app, app as api } from '@ekuaibao/whispered'
import styles from './SelectAssociatedDataLinkModal.module.less'
import React, { PureComponent } from 'react'
import {
  OutlinedTipsClose,
  FilledDirectionExpandDown,
  FilledDirectionExpandUp,
  OutlinedEditSearch,
  OutlinedEditFilter,
  FilledGeneralCollect
} from '@hose/eui-icons'
import {
  Tabs,
  Button,
  Popup as PopupEUI,
  NavBar,
  Selector,
  List,
  Radio,
  Tag,
  SearchBar,
  Dialog,
  Badge,
  Avatar
} from '@hose/eui-mobile'
import {
  formatEntityList,
  getNameCodeSearchKey,
  filterDataLinkUseCount,
  handleDataLinkUseCount
} from '../utils/dataLinkUtils'
import { EnhanceConnect } from '@ekuaibao/store'
import { get, debounce } from 'lodash'
import { toast } from '../../../lib/util'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
const { getStrLastWord } = app.require('@components/utils/fnDataLinkUtil')
import {
  getDataLinkListTitle,
  getDataLinkListDesc,
  getDataLinkListReplenish,
  getDataLinkListSupple,
  getDataLinkAvatar
} from '../elements/datalink/dataLinkUtil'
const packageSize = 99
const packageCount = 999
const tripOrderPackageSize = 9999 //行程订单一次性取值
import { QuerySelect } from 'ekbc-query-builder'
import Popup from '@ekuaibao/popup-mobile/esm/index'
import FilterForm from '../elements/datalink/datalink-detail-modal/elements/FilterForm'
import { isString } from '@ekuaibao/helpers'
const SkeletonListEUI = app.require('@home5/SkeletonList')
const EmptyWidget = api.require('@home5/EmptyWidget')

export const selectorOptions = [
  {
    label: '全部',
    value: 'ALL'
  },
  {
    label: '我的收藏',
    value: 'COLLECT'
  }
]
@EnhanceTitleHook()
@EnhanceConnect(state => ({
  powerCodeMap: state['@common'].powers.powerCodeMap
}))
export default class SelectAssociatedDataLinkStep1Modal extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      listData: [],
      searchValue: '',
      isLoading: false,
      hasMore: true,
      isAllChecked: false,
      template: {},
      start: 0,
      loading: false,
      tripOptions: [],
      isChecked: false,
      typeFilter: null,
      filterByOther: '',
      popupVisible: false,
      filter: {},
      tabSelectedKey: 'Unassociated',
      activeText: selectorOptions[0].label,
      activeType: 'ALL',
      checkedItem: undefined,
      associatedList: [],
      showSearch: false,
      showBadge: false,
      isExchange: false, //是否调换数据
      value: props.value,
      linkDataLinkEntity: props.linkDataLinkEntity
    }
    this.filterByKey = undefined
    this.selectorKey = selectorOptions[0]
    this.searchRef = React.createRef()
    this._$UUID = String(Math.random())
    api.backControl.create(this._$UUID, () => {
      this.backHookCallback()
    })
  }
  backHookCallback = () => {
    const { associatedList = [] } = this.state
    if (associatedList.length > 0) {
      Dialog.confirm({
        title: i18n.get('确定退出关联？'),
        content: i18n.get('退出后，已关联关系将被清空'),
        confirmText: i18n.get('退出'),
        onConfirm: () => {
          api.backControl.remove(this._$UUID)
          api.backControl.invoke()
        }
      })
    } else {
      api.backControl.remove(this._$UUID)
      api.backControl.invoke()
    }
  }
  componentWillMount() {
    this.initValue()
  }

  initValue = () => {
    const { value } = this.state
    if (value && value.disableStrategy === 'LIMIT_COUNT') {
      api.invoke('get:bills:value').then(result => {
        this.map = handleDataLinkUseCount(result)
        this.getEntityList()
      })
    } else {
      this.getEntityList()
    }
    this.isTripOrder() && this.getTripOptions()
  }

  componentWillUnmount() {
    api.backControl.remove(this._$UUID)
  }
  getIsCollectType = () => {
    return this.state.activeType === 'COLLECT'
  }

  getEntityList(filterBy, start = 0) {
    const { value, isExchange } = this.state
    const { flowId, filterId: sourceFilterId, expenseIds = [], formData = {}, otherFilters, linkFilterId } = this.props
    const filterId = isExchange ? linkFilterId : sourceFilterId
    if (start === 0) {
      this.setState({ loading: true })
    }
    const isPrivateCar = get(value, 'platformId.type') === 'PRIVATE_CAR'
    const entityId = get(value, 'id')
    const { submitterId } = formData
    const { typeFilter, filterByOther } = this.state
    let currentEntityId = '' // 行程订单可按机酒火等类型筛选
    if (this.isTripOrder() && typeFilter) {
      currentEntityId = typeFilter.id
    }
    let params = { entityId: currentEntityId || entityId, isPrivateCar: isPrivateCar }
    if (submitterId) {
      params = { ...params, submitterId }
    }
    if (flowId) {
      params = { ...params, flowId }
    }
    if (isPrivateCar) {
      params = filterBy ? { ...params, filterBy } : { ...params, start, count: packageCount }
      //我的收藏
      const isCollectType = this.getIsCollectType()
      if (isCollectType) {
        params.favoriteStatus = true
      }
      api.invokeService('@bill:get:entity:list', params).then(res => {
        this.handleFormatDataLink(res)
        this.setState({ loading: false })
      })
    } else {
      const isOrder = this.isTripOrder()
      let orderBy = undefined
      let filter = 'active==true'
      const query = new QuerySelect().filterBy(filter)
      if (isOrder) {
        const filterCode =
          expenseIds && expenseIds.length
            ? expenseIds.map(item => `form.E_${entityId}_申请单编号.containsIgnoreCase("${item}")`).join(' || ') //@i18n-ignore
            : ''
        if (filterCode) {
          query.filterBy(filterCode)
        }
        orderBy = [
          { value: `form.E_${entityId}_出发时间`, order: 'DESC' }, //@i18n-ignore
          { value: `form.E_${entityId}_入住日期`, order: 'DESC' } //@i18n-ignore
        ]
        params = { ...params, params: { type: 'order' } } //传参数给后台表明是行程管理里面的订单，需要后台根据配置来过滤数据 }
      }
      if (filterBy) {
        query.filterBy(filterBy)
      }
      if (filterByOther) {
        query.filterBy(filterByOther)
      }
      if (otherFilters?.length) {
        if (Array.isArray(otherFilters)) {
          otherFilters.forEach(filter => {
            query.filterBy(filter)
          })
        } else if (isString(otherFilters)) {
          query.filterBy(otherFilters)
        }
      }
      params = {
        ...params,
        type: 'LIST',
        start,
        count: isOrder ? tripOrderPackageSize : packageSize
      }
      params = { ...params, ...query.value() }
      if (filterId) {
        params = { ...params, filterId, form: formData }
      }
      if (isOrder) {
        params = { ...params, orderBy }
      }
      //我的收藏
      const isCollectType = this.getIsCollectType()
      if (isCollectType) {
        params.favoriteStatus = true
      }
      return api.invokeService('@mine:post:serach:dataLink', params).then(res => {
        this.setState({ loading: false })
        this.handleFormatDataLinknew(res, start)
        const temp = res.items.template.content.expansion
        this.setState({
          template: temp,
          start: start + packageSize
        })
      })
    }
  }

  handleFormatDataLink(result) {
    let { listData } = this.state
    let { value } = this.state
    let entityList = get(result, 'items') || []
    entityList = entityList.slice()
    let hasMore = listData.length + entityList.length === result.count
    entityList =
      get(value, 'disableStrategy') === 'LIMIT_COUNT' ? filterDataLinkUseCount(this.map, entityList) : entityList
    let newEntityList = formatEntityList(value.fields, entityList)
    let cListData = listData.concat(newEntityList)
    if (cListData.every(v => v?.dataLink?.id)) {
      const map = new Map()
      cListData.forEach(item => {
        if (!map.has(item?.dataLink?.id)) {
          map.set(item?.dataLink?.id, item)
        }
      })
      cListData = [...map.values()]
    }
    this.setState({ listData: cListData, isLoading: false, hasMore: !hasMore })
    this.filterByKey = getNameCodeSearchKey(entityList)
  }
  handleFormatDataLinknew(result, start) {
    let { listData, value } = this.state
    if (start === 0) {
      listData = []
    }
    let entityList = get(result, 'items.data') || []
    entityList = entityList.slice()
    let hasMore = listData.length + entityList.length === result.items.total
    entityList =
      get(value, 'disableStrategy') === 'LIMIT_COUNT' ? filterDataLinkUseCount(this.map, entityList) : entityList
    let newEntityList = formatEntityList(value.fields, entityList)
    let cListData = listData.concat(newEntityList)
    if (cListData.every(v => v?.dataLink?.id)) {
      const map = new Map()
      cListData.forEach(item => {
        if (!map.has(item?.dataLink?.id)) {
          map.set(item?.dataLink?.id, item)
        }
      })
      cListData = [...map.values()]
    }
    let { template } = result.items
    this.setState({ listData: cListData, isLoading: false, hasMore: !hasMore, template })
    if (entityList.length > 0) {
      this.filterByKey = this.getSearchKey(entityList[0].dataLink)
    }
  }
  getSearchKey = form => {
    let nameKey = '',
      codeKey = '',
      formKey = '',
      toKey = ''
    for (let key in form) {
      if (getStrLastWord(key, '_') === 'name') {
        nameKey = key
      } else if (getStrLastWord(key, '_') === 'code') {
        codeKey = key
      } else if (getStrLastWord(key, '_') === i18n.get('出发地')) {
        // @i18n-ignore
        formKey = key
      } else if (getStrLastWord(key, '_') === i18n.get('目的地')) {
        // @i18n-ignore
        toKey = key
      }
    }
    return { nameKey, codeKey, formKey, toKey }
  }

  //实体查看详情
  handleOnnewItemClick = rowData => {
    let field = this.state.value
    let rowValue = {}
    rowValue.data = rowData.dataLink
    rowValue.id = rowData.dataLink.id
    rowValue.template = this.state.template
    const title = get(field, 'referenceData.name')
    const isOrder = this.isTripOrder()
    if (isOrder) {
      api.open('@bill:DataLinkEntityTripOrderDetailModal', {
        field,
        value: { ...rowValue, data: { dataLink: rowData.dataLink } },
        title
      })
    } else {
      api.open('@bill:DataLinkEntityDetailModal', { field, value: rowValue })
    }
  }

  renderEmpty() {
    const { loading } = this.state
    if (loading) {
      return <SkeletonListEUI />
    }
    return <EmptyWidget size={200} type="noCentent" tips={'暂无数据'} />
  }

  renderListFooter = () => {
    let { isLoading, hasMore } = this.state
    let endStr = hasMore ? i18n.get('加载完毕') : i18n.get('没有更多数据了')
    return <div style={{ padding: 30, textAlign: 'center' }}>{isLoading ? i18n.get('加载更多') : endStr}</div>
  }

  isTripOrder = () => {
    const { value } = this.state
    const type = get(value, 'platformId.type', '')
    const referenceType = get(value, 'type', '')
    const isOrder = type === 'TRAVEL_MANAGEMENT' && referenceType === 'ORDER'
    return isOrder
  }

  isPrivateCar = () => {
    const type = get(this.state.value, 'platformId.type', '')
    return 'PRIVATE_CAR' === type
  }
  generateExpenseDetails = () => {
    const { associatedList, value, linkDataLinkEntity } = this.state
    const { getRuleDataLinkFeetype, specificationId, formType, formData } = this.props

    const newDataList = []
    associatedList.forEach(item => {
      const { linkDataLink, ...others } = item
      linkDataLink.forEach(o => {
        newDataList.push({ ...others, linkDataLinkEntity: o })
      })
    })

    getRuleDataLinkFeetype({
      specificationId,
      dataLinkEntityId: value.id,
      linkDataLinkEntityId: linkDataLinkEntity.id,
      type: formType,
      billData: formData
    }).then(async resp => {
      const dataList = newDataList.map(item => {
        return { ...item.dataLink, linkDataLinkEntity: item.linkDataLinkEntity }
      })
      if (!resp.error) {
        let items = get(resp, 'payload.items') || []
        const orgItems = items
        let feetypeConfigMap = undefined
        if (get(value, 'type') === 'ORDER' && get(value, 'platformId.type') === 'TRAVEL_MANAGEMENT') {
          //行程订单，在行程管理里面有订单类型对应的消费类型的配置
          const feetypeConfig = await api.invokeService('@bill:get:travelManagementConfig', {
            type: 'associatedFeeType'
          })
          const contextDetail = feetypeConfig?.value?.contextDetail
          if (contextDetail && contextDetail.length) {
            //如果配置了导入订单的费用类型，过滤掉不能用的费用类型
            for (let item of contextDetail) {
              const feetype = orgItems.find(o => o.id === item.feeTypeId)
              if (feetype) {
                const paymentMethod = item.paymentMethod
                const travelTypes = item.travelTypes
                if (!feetypeConfigMap) {
                  feetypeConfigMap = {}
                }
                travelTypes.forEach(o => {
                  feetypeConfigMap[`${paymentMethod}${o}`] = feetype
                })
                items = items.filter(i => i.id !== feetype.id)
              }
            }
          }
        }
        //@i18n-ignore
        const payMap = {
          个人支付: 'personalPayment',
          企业支付: 'enterprisePayment',
          混合支付: 'mixedPayment',
          个人: 'personalPayment',
          企业: 'enterprisePayment'
        }
        const orderListFeetype = []
        const orderListOther = []
        if (feetypeConfigMap) {
          dataList.forEach(item => {
            const parentId = get(item, 'entity.parentId') || get(item, 'entity.id')
            const type = get(item, 'entity.type')
            //@i18n-ignore
            const payType = get(item, `E_${parentId}_支付方式`)
            const feetype = feetypeConfigMap[`${payMap[payType]}${type}`] || feetypeConfigMap[`all${type}`]
            if (feetype) {
              item.feeType = feetype
              orderListFeetype.push(item)
            } else {
              orderListOther.push(item)
            }
          })
        }
        if (orderListFeetype.length === dataList.length) {
          this.updateCousume(undefined, orderListFeetype, value)
        } else {
          let copySelectedDatas = dataList
          if (orderListFeetype.length > 0) {
            copySelectedDatas = [...orderListFeetype, ...orderListOther]
          }
          if (items.length === 0) {
            if (orderListFeetype.length > 0) {
              api
                .open('@bill:ImportTripOrderConfirmModal', {
                  headerTitle: i18n.get('提示'),
                  content: i18n.get(`有${orderListOther.length}条行程订单没有匹配到对应的费用模板,是否继续？`)
                })
                .then(result => {
                  const { type } = result
                  if (type === 'ok') {
                    this.updateCousume(undefined, orderListFeetype, value)
                  }
                })
            } else {
              toast.error(i18n.get('无可用消费类型，请联系管理员'))
            }
          } else if (items.length === 1) {
            const feeType = items[0]
            this.updateCousume(feeType, copySelectedDatas, value)
          } else {
            api
              .open('@feetype:SelectFeeTypeModal', {
                feetype: resp.payload.items,
                isShowCancelBtn: true,
                parentLayer: this.props.layer,
                formType
              })
              .then(feetype => {
                if (feetype === 'cancel') {
                  return this.props.layer.emitCancel()
                }
                this.updateCousume(feetype, copySelectedDatas, value)
              })
          }
        }
      }
    })
  }

  updateCousume = (feetype, copySelectedDatas, value) => {
    const { bus } = this.props
    const { linkDataLinkEntity } = this.state
    const val = {
      type: 'dataLink',
      feetype,
      dataLinkList: copySelectedDatas,
      dataLinkEntity: value,
      linkDataLinkEntity
    }
    bus.invoke('element:details:import:data:format', val).then(consume => {
      this.props.layer.emitOk({ consume })
    })
  }

  handleChangeMenuType = () => {
    this.setState(
      {
        activeType: this.selectorKey.value,
        activeText: this.selectorKey.label,
        searchValue: '',
        filter: {},
        filterBy: '',
        filterByOther: '',
        popupVisible: false
      },
      _ => {
        this.getEntityList()
      }
    )
  }

  getTripOptions = () => {
    const { value } = this.state
    const id = get(this.state.value, 'platformId.id')
    api.invokeService('@bill:get:entity:details:list', { id }).then(res => {
      const entity = res.items.find(i => i.id === value.id)
      entity.name = '全部' // @i18n-ignore
      const tripOptions = [entity, ...entity.children]
      this.setState({
        tripOptions
      })
    })
  }

  handlePopupCancel = () => {
    Popup.hide()
  }
  handleFilterData = (filter, filterByOther) => {
    Popup.hide()
    const { filterBy } = this.state
    this.setState({ filter, isChecked: filter.show_full_name, filterByOther: filterByOther?.filterBy }, () => {
      this.getEntityList(filterBy)
    })
  }
  hanldeFilter = () => {
    const fields = this.state?.value?.referenceData?.fields || []
    Popup.show(
      <div className={styles['popup-modal']} onCancel={this.handlePopupCancel}>
        <div className="modal-title">
          <div className="title ">{i18n.get('筛选条件')}</div>
          <OutlinedTipsClose onClick={this.handlePopupCancel} />
        </div>
        <span className="modal-title-tips">{i18n.get('您可对当前业务对象下的字段进行筛选，')}</span>
        <span className="modal-title-tips mb-8">{i18n.get('展示结果等于您输入所有条件的组合')}</span>
        <FilterForm fields={fields} filter={this.state.filter} handleFilter={this.handleFilterData}></FilterForm>
      </div>,
      {
        animationType: 'slide-up',
        maskClosable: true,
        wrapClassName: 'popup-wrapper-style'
      }
    )
  }
  handleShowSearch = () => {
    this.setState({ showSearch: true }, () => {
      this.searchRef.current?.focus()
    })
  }
  renderHeader = () => {
    const { activeText, popupVisible } = this.state
    return (
      <div className="search-wrapper">
        <div
          className="type_name"
          onClick={() => {
            this.hanldeSetPopupVisible(true)
          }}
        >
          <span className="type_name_text">{activeText}</span>
          {popupVisible ? (
            <FilledDirectionExpandUp color="var(--eui-icon-n1)" fontSize={10} />
          ) : (
            <FilledDirectionExpandDown color="var(--eui-icon-n1)" fontSize={10} />
          )}
        </div>
        <div className="header-right">
          <OutlinedEditSearch color="var(--eui-icon-n1)" fontSize={16} onClick={this.handleShowSearch} />
          <OutlinedEditFilter
            color="var(--eui-icon-n1)"
            style={{ marginLeft: 16 }}
            fontSize={16}
            onClick={this.hanldeFilter}
          />
        </div>
      </div>
    )
  }
  handleRadioChange = item => {
    this.setState({ checkedItem: item })
  }
  renderList = () => {
    const { listData, tabSelectedKey, associatedList, showSearch } = this.state

    let filterListData =
      tabSelectedKey === 'Unassociated' ? listData.filter(item => !this.isItemAssociated(item)) : associatedList

    if (showSearch) {
      filterListData = listData
    }
    if (!filterListData?.length) {
      return this.renderEmpty()
    }
    return (
      <List>
        {filterListData.map(item => {
          return this.renderListItem(item)
        })}
      </List>
    )
  }
  getPrefixIcon = item => {
    const { template, checkedItem } = this.state
    const { isShowPersonalOnLeft } = template
    return (
      <div
        onClick={e => {
          this.handleRadioChange(item)
          e.stopPropagation()
        }}
        style={{ display: 'flex', padding: 12, paddingLeft: 16 }}
      >
        <Radio
          checked={item?.dataLink?.id === checkedItem?.dataLink?.id}
          onChange={() => this.handleRadioChange(item)}
        />
        {isShowPersonalOnLeft ? (
          <Avatar src={getDataLinkAvatar(template, item)} style={{ marginLeft: 12 }} shape="circle" />
        ) : null}
      </div>
    )
  }
  renderListItem = item => {
    const { template, isChecked } = this.state
    const favoriteStatus = item?.dataLink?.favoriteStatus ?? false
    return (
      <List.Item
        key={item.dataLink.id}
        style={{ flex: 1 }}
        prefixIcon={this.getPrefixIcon(item)}
        onClick={() => this.handleOnnewItemClick(item)}
        description={getDataLinkListDesc(template, item)}
        extra={getDataLinkListSupple(template)}
        extraTitle={getDataLinkListReplenish(template, item)}
        clickable
      >
        <div style={{ alignItems: 'center' }}>
          <span className={!isChecked ? 'eui-list-item-conent-tilte-ellipsis' : ''}>
            {getDataLinkListTitle(template, item)}
          </span>
          {this.isItemAssociated(item) && (
            <Tag color="pri" size="small" style={{ marginLeft: 4 }}>
              {i18n.get('已关联')}
            </Tag>
          )}
          {favoriteStatus && <FilledGeneralCollect color="var(--eui-decorative-yel-500)" style={{ marginLeft: 4 }} />}
        </div>
      </List.Item>
    )
  }
  getLinkData = itemValue => {
    const { associatedList } = this.state
    const data = associatedList.find(item => (item.dataLink.id = itemValue.dataLink.id))
    return data.linkDataLink ?? []
  }
  handleAddAssociation = (isEdit = false, readOnly = false) => {
    const {
      linkFilterId,
      flowId,
      specificationId,
      formType,
      bus,
      expenseIds,
      getRuleDataLinkFeetype,
      formData,
      filterId,
      otherFilters
    } = this.props
    const { checkedItem, template, linkDataLinkEntity, isExchange } = this.state
    const name = get(linkDataLinkEntity, 'name', '')
    const type = get(linkDataLinkEntity, 'platformId.type', '')
    const isPrivateCar = type === 'PRIVATE_CAR'
    let linkDataLink = []
    if (isEdit) {
      linkDataLink = checkedItem.linkDataLink ?? this.getLinkData(checkedItem)
    }
    api
      .open('@bill:SelectAssociatedDataLinkStep2Modal', {
        value: linkDataLinkEntity,
        filterId: isExchange ? filterId : linkFilterId,
        sourceEntity: { ...checkedItem, linkDataLink },
        sourceTemplate: template,
        readOnly,
        flowId,
        title: `选择${isPrivateCar ? i18n.get('行车记录') : name || '行程订单'}`,
        bus,
        expenseIds,
        getRuleDataLinkFeetype,
        specificationId,
        formType,
        formData,
        otherFilters
      })
      .then(result => {
        const { checkedDataList, isFinish } = result
        const { associatedList, checkedItem, tabSelectedKey } = this.state
        let list = undefined
        if (isEdit) {
          list = associatedList.map(item => {
            if (item.dataLink.id === checkedItem.dataLink.id) {
              return { ...item, linkDataLink: checkedDataList }
            } else {
              return item
            }
          })
        } else {
          list = associatedList.concat([{ ...checkedItem, linkDataLink: checkedDataList }])
        }
        if (isFinish) {
          //直接生成费用明细，并关闭当前对话框
          this.setState(
            {
              associatedList: list,
              checkedItem: undefined
            },
            () => {
              this.generateExpenseDetails()
            }
          )
        } else {
          //继续关联，更新页面的数据
          this.setState({
            associatedList: list,
            showBadge: tabSelectedKey === 'Unassociated',
            checkedItem: undefined
          })
        }
      })
  }
  isItemAssociated = item => {
    const { associatedList } = this.state
    const ids = associatedList.map(item => item.dataLink.id)
    return ids.includes(item.dataLink.id)
  }
  renderSearchView = () => {
    const { checkedItem } = this.state
    return (
      <div className={styles['dataLink-wrapper']}>
        <div className="search-bar-wrapper">
          <SearchBar
            ref={this.searchRef}
            placeholder={i18n.get('请输入名称或编码')}
            showCancelButton={() => true}
            onCancel={this.handleOnSearchCancel}
            onChange={this.handleOnSearch}
          />
        </div>
        <div className="multi-list">{this.renderList()}</div>
        <div className="bottom_bar">
          <Button
            disabled={!checkedItem}
            onClick={() => this.handleAddAssociation(this.isItemAssociated(checkedItem))}
            block
            size="large"
          >
            {i18n.get('添加关联')}
          </Button>
        </div>
      </div>
    )
  }

  handleOnSearchCancel = () => {
    this.setState({ searchValue: '', showSearch: false, checkedItem: undefined }, () => {
      this.getEntityList()
    })
  }

  handleOnSearch = debounce(text => {
    const value = text.trim()
    if (!this.filterByKey) return this.setState({ searchValue: value })
    let { nameKey, codeKey } = this.filterByKey || {}
    const filterBy = value ? `(form.${nameKey}.containsIgnoreCase("${value}") || form.${codeKey}.containsIgnoreCase("${value}"))` : ''
    this.setState({ searchValue: value }, () => {
      this.getEntityList(filterBy)
    })
  }, 400)
  handleExchangeData = async () => {
    const { associatedList } = this.state
    if (!associatedList?.length) {
      //如果没有关联数据的话直接进行数据交换
      this.fnExchangeData()
      return
    }
    //有关联数据的话就会弹出确认框
    const result = await Dialog.confirm({
      title: i18n.get('确定进行数据交换吗？'),
      content: i18n.get('数据交换后原有关联关系将被清空')
    })
    if (result) {
      this.fnExchangeData()
    }
  }
  fnExchangeData = () => {
    //点击确定按钮,清空关联数据
    const { linkDataLinkEntity, value } = this.props
    const isExchange = !this.state.isExchange
    this.setState(
      {
        associatedList: [],
        listData: [],
        isExchange,
        loading: true,
        showBadge: false,
        value: isExchange ? linkDataLinkEntity : value,
        linkDataLinkEntity: isExchange ? value : linkDataLinkEntity,
        checkedItem: undefined
      },
      () => {
        this.setHeaderTitle()
        this.initValue()
      }
    )
  }
  setHeaderTitle = () => {
    const entityInfo = this.state.value
    const name = get(entityInfo, 'name', '')
    const type = get(entityInfo, 'platformId.type', '')
    const isPrivateCar = type === 'PRIVATE_CAR'
    api.invokeService('@layout:set:header:title', `选择${isPrivateCar ? i18n.get('行车记录') : name || '行程订单'}`)
  }
  renderTabContent = () => {
    const { tabSelectedKey, checkedItem, associatedList } = this.state
    return (
      <div className="tabs-content-wrapper">
        {this.renderHeader()}
        <div className="multi-list">{this.renderList()}</div>
        <div className="bottom_bar">
          {tabSelectedKey === 'Unassociated' ? (
            <>
              <Button
                category="secondary"
                style={{ marginRight: 16 }}
                onClick={this.handleExchangeData}
                block
                size="large"
              >
                {i18n.get('数据交换')}
              </Button>
              <Button disabled={!checkedItem} onClick={() => this.handleAddAssociation(false)} block size="large">
                {i18n.get('添加关联')}
              </Button>
            </>
          ) : (
            <>
              <Button
                category="secondary"
                disabled={!checkedItem}
                onClick={() => this.handleAddAssociation(true)}
                block
                size="large"
                style={{ marginRight: 16 }}
              >
                {i18n.get('编辑关联')}
              </Button>
              <Button
                category="secondary"
                onClick={() => this.handleAddAssociation(true, true)}
                disabled={!checkedItem}
                block
                size="large"
                style={{ marginRight: 16 }}
              >
                {i18n.get('关联详情')}
              </Button>
              <Button block size="large" disabled={!associatedList?.length} onClick={this.generateExpenseDetails}>
                {i18n.get('生成明细')}
              </Button>
            </>
          )}
        </div>
      </div>
    )
  }

  handleOnTabChange = key => {
    this.setState({ tabSelectedKey: key, checkedItem: undefined, showBadge: false })
  }
  hanldeSetPopupVisible = visible => {
    this.setState({ popupVisible: visible })
  }
  render() {
    const { tabSelectedKey, popupVisible, showSearch, showBadge } = this.state

    if (showSearch) {
      return this.renderSearchView()
    }

    return (
      <div className={styles['dataLink-wrapper']}>
        <Tabs defaultActiveKey={tabSelectedKey} onChange={this.handleOnTabChange}>
          <Tabs.Tab title={i18n.get('未关联')} key="Unassociated">
            {this.renderTabContent()}
          </Tabs.Tab>
          <Tabs.Tab
            title={
              showBadge ? (
                <Badge dot style={{ '--right': '-7px', '--top': '5px' }}>
                  {i18n.get('已关联')}
                </Badge>
              ) : (
                i18n.get('已关联')
              )
            }
            key="associated"
          >
            {this.renderTabContent()}
          </Tabs.Tab>
        </Tabs>
        <PopupEUI
          destroyOnClose
          visible={popupVisible}
          title={
            <NavBar
              style={{
                width: '100%',
                borderRadius: '10px 10px 0 0',
                padding: 0,
                borderBottom: '1px solid var(--eui-line-divider-default)'
              }}
              back={
                <Button category="text" theme="highlight">
                  {i18n.get('取消')}
                </Button>
              }
              onBack={() => this.hanldeSetPopupVisible(false)}
              backArrow={false}
              right={
                <Button category="text" theme="highlight" onClick={this.handleChangeMenuType}>
                  {i18n.get('确认')}
                </Button>
              }
              title={i18n.get('筛选')}
            ></NavBar>
          }
          onMaskClick={() => {
            this.hanldeSetPopupVisible(false)
          }}
          radius
        >
          <Selector
            columns={3}
            style={{ marginTop: 16 }}
            options={selectorOptions}
            defaultValue={this.selectorKey.value}
            onChange={(_arr, extend) => {
              this.selectorKey = extend.items[0]
            }}
          />
        </PopupEUI>
      </div>
    )
  }
}
