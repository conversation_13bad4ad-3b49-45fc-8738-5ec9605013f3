import { app, app as api } from '@ekuaibao/whispered'
import React, { Fragment, PureComponent } from 'react'
import MessageCenter from '@ekuaibao/messagecenter'
const { Box, Footer } = app.require('@elements/layout/Box')
import { Dialog } from '@hose/eui-mobile'
import OrderButtonWrapper from './elements/orderButtonWrapper'
import { EnhanceConnect } from '@ekuaibao/store'
const ETabs = app.require('@elements/EUITabs')
const parseAsFormValue = app.require('@lib/parser.parseAsFormValue')
import BillInfoReadonly from './panels/BillInfoReadonly'
const ActionsPanel = app.require('@elements/ActionsPanel')
import { cloneDeep, sortBy, get } from 'lodash'
import { getBillHistoryVersionDetail, getBillHistoryVersionList } from './bill.action'
const BillApprove = app.require('@elements/puppet/Approve/BillApprove')
const PrintPreview = app.require('@elements/puppet/PrintPreview')
const BillExpress = app.require('@elements/puppet/Express/BillExpress')
const ReportListView = app.require('@basic-elements/budget/report-list-view')
import { formatRiskNotice } from './utils/formatRiskData'
import { formatFormForCopy, confirmCopy } from './utils/formatUtil'
import WarningSensitive from '../../elements/puppet/WarningSensitive'
import NoPermissionViewBillDetail from './elements/NoPermissionViewBillDetail'
import RiskAlert from '../../elements/puppet/RiskNotice/RiskAlert'
import TripOrderAction from './elements/TripOrderAction'
import LogsCardView from './panels/LogsCardView'
import { toast, getUrlParamString } from '../../lib/util'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import { getBoolVariation } from '../../lib/featbit'
const {
  reminder,
  retract,
  carbonCopyRead,
  print,
  printInvoice,
  comment,
  confirm,
  copy,
  addNote,
  cancelAddNote,
  changeBtn,
  handleAllowWithdraw,
  billVersionBtn,
  signShift
} = app.require('@basic-elements/basic-state-const')
import UrgentView from '../bill/panels/UrgentView'
import * as actions from './bill.action'
import { getV } from '../../lib/help'
import { billActions } from '../../lib/util/billActions'
import { getLayoutConfig } from '../../plugins/approve/approve.action'
const LoanPackageDetail = app.require('@loanpackage/detail')
import {
  showSensitiveContent,
  STATE_LIST,
  logInfo,
  checkIsRemuneration,
  fixRemunerationSpecification,
  getDiffsBetweenVersions,
  getRiskReasonDataForVersionDiffModal,
  fnShareBillInfo,
  checkSpecificationActive,
  getSpecificationName,
  getBacklogByFlowId,
} from './utils/billUtils'
import { isObject } from '@ekuaibao/helpers'
import { trackBillReviewTime, trackLoadBillFlowTime } from './utils/trackBill'
import { isPrintShow } from '../approve/util'
import { formatRiskWarningsV2 } from './utils/formatRiskData.formatRiskWarnings'
import { BillAdditionalMessageApi } from '@ekuaibao/ekuaibao_types'
import BillInfoButtons from '../../elements/FlowActionButton/BillInfoButtons'
import { FlowAction } from './panels/FlowAction'
import qs from 'qs'
import { actionsMap } from './panels/bill-actions/actions'
import * as approveActions from '../approve/approve.action'

const EkbIcon = api.require('@elements/ekbIcon')

const checkFromRequisionDetailTabPage = params => {
  return params.type === 'formRequisition' || params.type === 'expenseRequisition'
}

@EnhanceConnect(
  state => ({
    current_flow: state['@common'].current_flow,
    delegators: state['@common'].delegators,
    packageList: state['@loanpackage'].packageList,
    reportChargePower: state['@common'].powers.Budget,
    expressPower: state['@common'].powers.Express,
    KA_PREVIEW_PRINT_IN_MODAL: state['@common'].powers.KA_PREVIEW_PRINT_IN_MODAL,
    flowReportList: state['@common'].flowReportList,
    me_info: state['@common'].me_info,
    currentRequisition: state['@requisition'].currentRequisition,
    standardCurrency: state['@common'].standardCurrency,
    originFlowForHistory: state['@bill'].originFlowForHistory,
    globalFieldsMap: state['@common'].baseDataProperties.baseDataPropertiesMap,
    expressNums: state['@bill'].expressNums,
    loanInfo: state['@bill'].loanInfo,
    remunerationSetting: state['@home'].remunerationSetting,
    RiskPromptOptimization: state['@common'].powers.RiskPromptOptimization,
    KA_DETAILS_LAYOUT_POWER: state['@common'].powers.KA_DETAILS_LAYOUT,
    printPreviewUrl: state['@bill'].printPreviewUrl,
    expenseSpecAfterFiltered: state['@home'].expenseSpecAfterFiltered,
    autoExpenseWithBillStriction: state['@common'].powers.autoExpenseWithBillStriction,
    skipCheck: state['@bill'].skipCheck,
    billAdditionalInfo: state['@bill'].billAdditionalInfo
  }),
  {},
  '@bill/current/page/state'
)
@EnhanceTitleHook(i18n.get('单据详情'))
export default class BillDetail extends PureComponent {
  static defaultProps = {
    isNeedBudget: true
  }

  __FIRST_TIME = 0

  constructor(props, ...args) {
    super(props, ...args)
    // 开通charge，展示小组件单据信息按钮
    const powerCodeMap = app.getState('@common').powers?.powerCodeMap || []
    const showWidget = powerCodeMap?.includes('160013')
    this.bus = new MessageCenter()
    this.state = {
      lastTime: 0,
      loanInfo: null,
      package_detail: props.loanInfo,
      existBudgetOccupy: false,
      inAddNoteMode: false,
      arrangeLayout: [],
      widgetConfig: {
        config: undefined,
        loading: showWidget,
        visible: showWidget
      },
      skipCheck: props.skipCheck,
      showPrintBtn: false
    }
    this.defaultArrangeLayout = [
      {
        label: '风险提示',
        key: 'risks',
        type: 'default'
      },
      {
        label: '单据状态',
        key: 'status',
        type: 'default'
      },
      {
        label: '差旅订购',
        key: 'travel_order',
        type: 'default'
      },
      {
        label: '单据详情',
        key: 'details',
        type: 'default'
      },
      {
        label: '支付计划',
        key: 'payPlan',
        type: 'default'
      },
      {
        label: '核销组件',
        key: 'writtenOff',
        type: 'default'
      }
    ]
    this.__FIRST_TIME = Date.now()
    performance.mark('bill-detail-wrapper-start')
    this.__PAGE_UNMOUNT = false
  }

  // 准备审批预览页签需要的数据
  initPrintPreviewData = async current_flow => {
    const { KA_PREVIEW_PRINT_IN_MODAL, state } = this.props
    if (!KA_PREVIEW_PRINT_IN_MODAL) return
    const { data, flowId } = state
    const select = {}
    select[current_flow?.id] = current_flow
    const adata = await api.invokeService('@approve:dealPrintData', [flowId], select)
    adata.isTablePreview = true
    api.invokeService('@approve:doPrint', adata)
  }

  componentDidMount() {
    api.dataLoader('@common.delegators').load()
    let { setState, delegators = [], params, isNeedBudget, fromHistory, fromDataLink } = this.props

    let {
      id,
      type,
      delegatorId,
      budgetId,
      nodeId,
      flowVersionedId,
      sourcePage,
      carbonCopyReadType,
      carbonCopyType,
      carbonCopyId,
      canEditExpress,
      messageSource
    } = params

    setState({
      messageSource,
      flowId: id,
      formType: type,
      data: null,
      tabActiveKey: 'bill-detail',
      sourcePage,
      fromHistory,
      carbonCopyType,
      carbonCopyReadType,
      noticeList: [],
      flowRiskList: [],
      canEditExpress
    })

    api.invokeService('@common:list:tripTypes')
    let isModifyBill = false //是否是查看修改过的单据
    if (id) {
      this.initBacklog(id)
      let data = { type, id }
      const flowReportList = api.getState()['@common'].flowReportList || []
      const anunalReport = api.getState()['@report'].anunalReport || []
      //单据查询组件看详情的时候params.sourcePage 为 flowLinks，不控制权限

      if (fromDataLink || params?.sourcePage === 'flowLinks' || ((params?.sourcePage === 'fromLoanDetail' || params?.sourcePage === 'fromSubmitterLoan') && getBoolVariation('hailiang_loan_permission'))) {
        data.checkPermissions = false
      }
      if (isNeedBudget && budgetId && nodeId) {
        const currentRoot = flowReportList.find(v => v?.budgetId === budgetId)
        const anunalReportRoot = anunalReport.find(v => v?.budgetId === budgetId)
        const budgetRootNode = api.getState()['@@states'].budgetRootNode
        data = {
          ...data,
          budgetId,
          nodeId,
          fromNodeId: budgetRootNode?.fromNodeId || currentRoot?.fromNodeId || nodeId || anunalReportRoot?.nodeId
        }
      }
      //判断是否是查看历史修改过的单据，如果是就不显示Footer
      isModifyBill = params ? params.flowVersionedId : false
      if (isModifyBill) {
        data = { ...data, flowVersionedId }
      }
      api
        .dataLoader('@common.baseDataProperties')
        .load()
        .then(() => {
          if (this.__PAGE_UNMOUNT) {
            return
          }
          if (carbonCopyType === 'carbonCopy') {
            data = { ...data, id: carbonCopyId, type: carbonCopyType, __flowId: id }
            this.getFlowDetailInfo(data, id)
          } else {
            this.getFlowDetailInfo(data, id)
          }
        })
    }

    if (delegatorId) {
      let delegator = delegators.filter(o => o.id === delegatorId)[0]
      setState({ delegatorId: delegator })
    }
    api
      .dataLoader('@common.powers')
      .load()
      .then(() => {
        if (this.props.reportChargePower && !isModifyBill) {
          api.invokeService('@common:get:budget:status', { id }).then(res => {
            this.setState({ existBudgetOccupy: res.value })
          })
        }
      })
    api.dataLoader('@common.standardCurrency').load()
    api.dataLoader('@common.allStandardCurrency').load()

    this.bus.on('element:click:message:panel', this.handleMessagePanel)
    this.bus.on('element:expense:link:click', this.handleExpenseLinkClick)
    this.bus.watch('invoice:updata:bills', this.handleUpDataBills)
    api.on('invoice:updata:bills', this.handleUpDataBills)
    this.props.skipCheck === null &&
      api.invokeService('@bill:get:calculate:corpId:whitelist').then(res => {
        const skipCheck = res.value
        this.setState({ skipCheck })
      })
  }


  initBacklog = async (id) => {
    if (getBoolVariation('custom-extend-button', false)) {
      this._getBacklogPromise = getBacklogByFlowId(id)
    }
  }

  componentWillReceiveProps(nextProps) {
    const { widgetConfig } = this.state
    if (!this.props.noUpdate && this.props.current_flow !== nextProps.current_flow) {
      if (nextProps.current_flow) {
        this.initPrintPreviewData(nextProps.current_flow)
        if (nextProps.state && !nextProps.state.fromHistory) {
          api.invokeService('@bill:save:current:flow', nextProps.current_flow)
          this.initBacklog(nextProps.current_flow.id)
        }

        if (nextProps.expressPower) {
          // 判断是否有寄送节点
          if (nextProps.current_flow.plan) {
            const { nodes } = nextProps.current_flow.plan
            nodes.forEach(node => {
              if (node.expressConfig && node.expressConfig.type === 'send') this.hasExpressNode = true
            })
          }
        }

        this.props.setState({
          data: nextProps.current_flow,
          formType: nextProps.current_flow && nextProps.current_flow.formType
        })

        let { form } = nextProps.current_flow
        const name = getSpecificationName(form?.specificationId)
        api.invokeService('@layout:set:header:title', name)
        this.getPrintInvoice(nextProps.current_flow)
      }
    }

    const specificationNext = get(nextProps, 'current_flow.form.specificationId')
    const specificationIdNext = specificationNext?.id || specificationNext
    if (widgetConfig.visible && specificationNext?.originalId?.id) {
      BillAdditionalMessageApi.fetchUserConfig(specificationNext?.originalId?.id).then(res => {
        let widgetConfig = {
          config: undefined,
          loading: false,
          visible: false
        }
        if (!!res) {
          widgetConfig = {
            ...(this.state?.widgetConfig ?? {}),
            config: res,
            loading: false,
            visible: true
          }
        }
        this.setState({ widgetConfig })
      })
    } else if (specificationIdNext) {
      this.setState({
        config: undefined,
        loading: false,
        visible: false
      })
    }
    if (nextProps.KA_DETAILS_LAYOUT_POWER && specificationIdNext) {
      api.dispatch(getLayoutConfig({ type: 'ARRANGE', specificationId: specificationIdNext })).then(resp => {
        const { arrangeLayout: arrangeLayoutResp } = resp.value?.configDetail || {}
        if (arrangeLayoutResp) {
          this.setState({
            arrangeLayout: arrangeLayoutResp
          })
        }
      })
    }
  }

  componentWillUnmount() {
    this.__PAGE_UNMOUNT = true
    let { setState, isNeedBudget, originFlowForHistory } = this.props
    const oldFlowId = this.props.params?.oldFlowId
    const state =
      isNeedBudget === false
        ? { formType: originFlowForHistory && originFlowForHistory.formType, data: originFlowForHistory }
        : {
          flowId: null,
          formType: null,
          data: null,
          tabActiveKey: null,
          noticeList: null,
          flowRiskList: null
        }
    setState(state)
    api.invokeService('@layout:get:header:title')
    api.invokeService('@bill:change:add:note:mode', false)
    api.invokeService('@bill:change:status:of:notes', false)
    api.invokeService('@bill:update:flow:append:info', {})
    this.bus.un('element:click:message:panel', this.handleMessagePanel)
    this.bus.un('element:expense:link:click', this.handleExpenseLinkClick)
    this.bus.un('invoice:updata:bills', this.handleUpDataBills)
    if (oldFlowId) {
      localStorage.setItem('bill-detail-back', 'true')
      const val = {
        id: oldFlowId,
        isBack: false
      }
      //单据查询组件看详情的时候params.sourcePage 为 flowLinks，不控制权限
      if (this.props.params?.sourcePage === 'flowLinks' || ((this.props.params?.sourcePage === 'fromLoanDetail' || this.props.params?.sourcePage === 'fromSubmitterLoan') && getBoolVariation('hailiang_loan_permission'))) {
        val.checkPermissions = false
      }
      api.invokeService('@common:get:flow:detail:info', { ...val })
    } else {
      api.invokeService('@common:clear:flow:detail:info')
    }
    trackBillReviewTime({ startTime: this.__FIRST_TIME, endTime: Date.now() })
    this.clearPrintPreviewUrl()
  }

  getPrintInvoice(current_flow) {
    current_flow?.id &&
      api.invokeService('@bill:get:show:print:invoice', { flowId: current_flow?.id }).then(data => {
        this.setState({ showPrintBtn: data?.value || false })
      })
  }

  clearPrintPreviewUrl = () => {
    if (!this.props.KA_PREVIEW_PRINT_IN_MODAL) return
    api.invokeService('@bill:set:print:preview:url', '')
  }

  fnTrackBillLoadTime() {
    trackLoadBillFlowTime({ startTime: this.__FIRST_TIME, endTime: Date.now() })
  }

  getFlowDetailInfo = (data, id) => {
    const { type, flowVersionedId } = data
    const { fromDataLink, params = {} } = this.props
    if (flowVersionedId) {
      api.invokeService('@bill:search:note:form:history', { id: flowVersionedId })
    }
    if (type === 'formRequisition') {
      const flowId = this.props.currentRequisition && this.props.currentRequisition.flowId
      if (flowId) {
        const id = isObject(flowId) ? flowId?.id : flowId
        data = { ...data, flowId: id }
      }
    }
    if (this.props.params?.sourcePage === 'recyclePage') {
      data.inRecycle = true
    }
    const promiseArr = [api.invokeService('@common:get:flow:detail:info', data, undefined, true)]
    Promise.all(promiseArr)
      .then(result => {
        if (this.__PAGE_UNMOUNT) {
          return
        }
        let data = result[0]
        const flowId = get(data, 'value.flowId')
        data = isObject(flowId) ? flowId : get(data, 'value')
        // 单据查询组件看详情的时候params.sourcePage 为 flowLinks，不查看风险提示
        if (!fromDataLink && params?.sourcePage !== 'flowLinks') {
          // 异步请求风险数据，billinfo 模板未加载出来，风险数据设置失效
          this.handleRiskWarnings(data, id)
        }

        this.fnTrackBillLoadTime()
      })
      .catch(res => {
        if (res.errorCode === 403) {
          this.setState({ noPermission: true })
        }
      })
  }

  handleRiskWarnings = (data, flowId) => {
    let { globalFieldsMap = {}, RiskPromptOptimization } = this.props
    let form = (data && data.form) || {}
    let { specificationId, submitterId } = form
    let components = getV(specificationId, 'components', [])
    const details = form?.details
    api.invokeService('@common:get:riskwarningById', { flowId: flowId }).then(riskAll => {
      if (!riskAll) return
      let flowRiskList = formatRiskWarningsV2(cloneDeep(riskAll), details)
      let noticeList = formatRiskNotice({
        riskData: flowRiskList,
        components,
        globalFieldsMap,
        bus: this.bus,
        submitter: submitterId,
        RiskPromptOptimization
      })

      this.props.setState({ data, noticeList, flowRiskList, invoiceRiskData: riskAll })
    })
  }

  handleUpDataBills = () => {
    let { params } = this.props
    let { id, type } = params
    let data = { type, id }
    this.getFlowDetailInfo(data, id)
  }

  handleGoToHistory = () => {
    const { setState } = this.props
    setState({ tabActiveKey: 'bill-approve' })
    this.bus.emit('history:type:change', 'comment')
  }

  handleGoToDetail = line => {
    api.go(
      '/flow-report-detail/' +
      this.props.state.flowId +
      '/' +
      line.budgetId +
      '/' +
      line.fromNodeId +
      '/' +
      line.nodeId +
      '/' +
      line.periodTime
    )
  }

  handlePrint = async () => {
    const { data, flowId } = this.props.state
    const select = {}
    select[data.id] = data
    const adata = await api.invokeService('@approve:dealPrintData', [flowId], select)
    api
      .invokeService('@approve:doPrint', adata, null, this.handleUpDataBills, '0')
      .then(logInfo(`打印${data.form.title}单据`))
  }

  handlePrintAndInvoice = async () => {
    const { data, flowId } = this.props.state
    const select = {}
    select[data.id] = data
    const adata = await api.invokeService('@approve:dealPrintData', [flowId], select)
    api
      .invokeService('@approve:doPrint', adata, null, this.handleUpDataBills, '1')
      .then(logInfo(`打印${data.form.title}单据`))
  }

  handleReminder = () => {
    Dialog.confirm({
      title: i18n.get('发送催办消息'),
      content: (
        <div style={{ textAlign: 'left' }}>
          <span>{i18n.get('系统将发送一条消息提醒')} </span>
          <span style={{ fontWeight: 'bold' }}>{this.getApproveMember()} </span>
          <span>{i18n.get('审批。若长时间未审批，建议通过其他方式联系审批人。')}</span>
        </div>
      ),
      confirmText: i18n.get('发送'),
      onConfirm: async () => {
        const { flowId, data } = this.props.state
        logInfo(`催办${data.form.title}单据`)
        let { lastTime } = this.state
        let newTime = new Date().valueOf()
        if (newTime - lastTime > 60000) {
          //60秒内只能执行一次催办功能
          let { taskId } = data.plan
          await api.dispatch(actions.billRemind(flowId, taskId))
          this.setState({ lastTime: newTime })
        } else {
          toast.error(i18n.get('操作频繁'))
        }
      }
    })
  }

  handleRetract = () => {
    const { flowId, data } = this.props.state
    Dialog.confirm({
      title: i18n.get('撤回单据'),
      content: <div style={{ textAlign: 'left' }}>{i18n.get('撤回后若重新提交，将从第一位审批人开始审批。')}</div>,
      cancelText: i18n.get('取消'),
      confirmText: i18n.get('撤回'),
      onConfirm: async () => {
        logInfo(`撤回${data.form.title}单据`)
        await api.dispatch(actions.retractFlow(flowId))
      }
    })
  }

  /**
   * 已审批详情点击撤回审批按钮弹出弹出提示
   * 点击确定时撤回
   * */
  handleApproveWithdraw = () => {
    const { current_flow } = this.props
    const { taskId, nodes } = current_flow.plan
    const node = nodes.find(node => node.id === taskId) || {}
    const index = nodes.findIndex(v => v.id === node.id)
    const prevNode = index > 0 ? nodes[index - 1] : {}
    const { flowId, data } = this.props.state
    const nextId = data.plan.taskId
    const rejectTo = prevNode?.id
    return Dialog.confirm({
      title: i18n.get('提示'),
      content: i18n.get('确认发起撤回吗?'),
      onConfirm: () => api.dispatch(actions.approveWithdraw(flowId, rejectTo, nextId))
    })
  }

  handleCarbonCopyRead = () => {
    let { flowId } = this.props.state
    api.invokeService('@approve:marked:read', { type: '', ids: [flowId] }).then(_ => {
      api.go(-1)
    })
  }

  handleConfirm = () => {
    let { flowId } = this.props.state
    api.invokeService('@approve:do:confirm', [flowId], { name: 'freeflow.archive' }).then(_ => {
      api.go(-1)
    })
  }

  handleCopyClick = async () => {
    const { data: dataSource } = this.props.state

    // 单据使用的模板被停用时，拦截复制动作
    const specOriginalId = get(dataSource, 'form.specificationId.originalId')
    const specActive = await checkSpecificationActive(specOriginalId)
    if (!specActive) return

    const flow = cloneDeep(dataSource)

    delete flow.form.systemGeneration
    let value = parseAsFormValue(flow)
    value = await formatFormForCopy(value)
    const isCopyBill = 'isCopyBill'
    const { specificationId } = value
    const isRemuneration = checkIsRemuneration(specificationId?.id || specificationId)
    isRemuneration && fixRemunerationSpecification(specificationId)
    api.invokeService('@home:save:specification', specificationId).then(_ => {
      confirmCopy(flow).then(_ => {
        api.invokeService('@bill:save:copied:value', value).then(() => {
          api.go(`/copy/bill/${specificationId.type}/${isCopyBill}`)
          logInfo(`复制${dataSource.form.title}单据`)
        })
      })
    })
  }

  fnGetActionMap = () => {
    const { flowActionMap = {} } = this.props
    return {
      ...flowActionMap,
      [FlowAction.Comment]: { onClick: () => this.handleCommentBill() },
      [FlowAction.Print]: { onClick: () => this.handlePrint() },
      [FlowAction.PrintInvoice]: { onClick: () => this.handlePrintAndInvoice() },
      [FlowAction.Copy]: { onClick: () => this.handleCopyClick() },
      [FlowAction.Urge]: { onClick: () => this.handleReminder() },
      [FlowAction.Retract]: { onClick: () => this.handleRetract() },
      [FlowAction.AddAnnotation]: {
        onClick: button => {
          const { inAddNoteMode } = this.state
          inAddNoteMode ? this.handleCancelAddNote() : this.handleAddNote()
          return {
            id: button.id,
            changeButton: true,
            name: inAddNoteMode ? i18n.get('添加批注') : i18n.get('退出批注模式'),
            showCurrentButton: !inAddNoteMode,
            currencyCategory: 'primary'
          }
        }
      },
      [FlowAction.MarkRead]: { onClick: () => this.handleCarbonCopyRead() },
      [FlowAction.Share]: {
        onClick: () => {
          const { flowId } = this.props.state
          fnShareBillInfo(flowId)
        }
      },
      [FlowAction.Alter]: {
        onClick: () => this.handleChange()
      }
    }
  }

  _buttonsClick = params => {
    const { name = '' } = params
    switch (name) {
      case 'print':
        return this.handlePrint()
      case 'printInvoice':
        return this.handlePrintAndInvoice()
      case 'copy':
        return this.handleCopyClick()
      case 'reminder':
        return this.handleReminder()
      case 'retract':
        return this.handleRetract()
      case 'comment':
        return this.handleCommentBill()
      case 'carboncopy_read':
        return this.handleCarbonCopyRead()
      case 'confirm':
        return this.handleConfirm()
      case 'addNote':
        return this.handleAddNote()
      case 'cancelAddNote':
        return this.handleCancelAddNote()
      case 'changeBtn':
        return this.handleChange()
      case 'isAllowWithdraw':
        return this.handleApproveWithdraw()
      case 'billVersionBtn':
        return this.handleViewDiff()
      case 'share':
        const { flowId } = this.props.state
        return fnShareBillInfo(flowId)
    }
  }
  handleChange = () => {
    const { flowId, formType, data } = this.props.state
    const requisitionObj = data.form?.specificationId?.configs?.find(i => i.ability === 'requisition') || {}
    const mustRequire = !requisitionObj.optionalComment
    api.open('@bill:BillChangeModal', { flowId, formType, mustRequire })
  }
  handleCancelAddNote = () => {
    api.invokeService('@bill:change:add:note:mode', false)
    this.setState({ inAddNoteMode: false })
  }

  handleAddNote = () => {
    api.invokeService('@bill:change:add:note:mode', true)
    this.setState({ inAddNoteMode: true })
  }

  handleCommentBill = () => {
    const { flowId, data } = this.props.state
    api.open('@bill:BillComment').then(params => {
      api.invokeService('@bill:comment:bill', { id: flowId, params }).then(_ => {
        logInfo(`评论${data.form.title}单据`)
        api.invokeService('@common:get:flow:detail:info', { id: flowId })
      })
    })
  }

  handleMessagePanel = type => {
    let { setState, current_flow, isNeedBudget } = this.props
    if (type === 'budget') {
      if (isNeedBudget) {
        if (this.state.existBudgetOccupy) {
          setState({ tabActiveKey: 'bill-takeUp' })
        } else {
          Dialog.alert({ content: i18n.get('您不是该预算的负责人，无法查看详情。如需查看请联系预算管理员。') })
        }
      }
    } else if (type === 'loan') {
      let { id, form } = current_flow
      api.go('/submitter/' + form.submitterId.id + '/' + id)
    }
  }
  handleExpenseLinkClick = value => {
    const { isNeedBudget } = this.props
    const { flowId } = this.props.state
    value.flowId = flowId
    if (isNeedBudget) {
      api.invokeService('@requisition:save:current:requisition', value).then(() => {
        api.go('/requisitionDetail/' + false)
      })
    }
  }

  onTabClick = tabActiveKey => {
    let { setState } = this.props
    setState({ tabActiveKey })
    tabActiveKey === 'bill-takeUp' && this.bus.emit('budget:tab:click')
  }

  getApproveMember = () => {
    const { current_flow } = this.props
    const { taskId, nodes } = current_flow.plan
    const currentNode = nodes.find(node => node.id === taskId)
    if (currentNode.type === 'countersign') {
      const counterSigners = getV(currentNode, 'counterSigners', [])
      const approvingSigners = counterSigners
        .filter(item => item.state === 'APPROVING' || item.state === null)
        .map(item => item.signerId.name)
      return i18n.get(`{__k0}等{__k1}人`, {
        __k0: approvingSigners.slice(0, 10).join(),
        __k1: approvingSigners.length
      })
    } else if (currentNode.type === 'ebot') {
      return 'Ebot'
    } else if (currentNode.type === 'invoicingApplication') {
      return i18n.get('开票申请')
    } else {
      return currentNode.approverId ? currentNode.approverId.name : i18n.get('未选择')
    }
  }

  render() {
    if (this.state.noPermission) {
      return <NoPermissionViewBillDetail />
    }
    let {
      data,
      formType,
      tabActiveKey,
      sourcePage,
      canEditExpress,
      noticeList,
      flowRiskList,
      invoiceRiskData
    } = this.props.state

    if (!data) {
      return null
    }

    let {
      me_info,
      standardCurrency,
      params,
      isNeedBudget,
      openedDataLinkModalData,
      printPreviewUrl,
      fromDataLink,
      showFlowPlan = true
    } = this.props
    const { widgetConfig, skipCheck, arrangeLayout } = this.state
    const { isAlwaysPrint } = params
    //判断当前用户是否是单据拥有者，是的话可以显示催办按钮
    let isShowReminderOwner = data.ownerId.id === (me_info && me_info.staff && me_info.staff.id)
    //判断是否是查看历史修改过的单据，如果是就不显示Footer
    let isModifyBill = params ? params.flowVersionedId : false
    //判断是否要显示全量的预警信息，从扫描查询进来时为true
    let value = parseAsFormValue(data)

    const specificationCurrent = get(value, 'specificationId')
    const isRemuneration = checkIsRemuneration(specificationCurrent?.id || specificationCurrent)
    isRemuneration && fixRemunerationSpecification(specificationCurrent)

    let {
      writtenOff,
      plan,
      ownerId,
      state,
      logs = [],
      flowRulePerformLogs,
      actions,
      form = {},
      loanManualRepayment = []
    } = data
    //支付中分两种状态，待支付和支付中，这里排除支付中状态
    const isStatePaying = state === 'paying' && logs?.[logs?.length - 1]?.action !== 'freeflow.paying'
    const isFromeHomePage = sourcePage === 'homePage' || sourcePage === 'mall'
    let isShowReminderState = state === 'approving' || state === 'sending' || state === 'receiving' || isStatePaying
    let isShowReminder =
      isShowReminderOwner && isShowReminderState && (isFromeHomePage || checkFromRequisionDetailTabPage(params))
    let isEditTaxRate = !(state === 'archived' || state === 'paid' || state === 'paying')
    let isRetractNode = false
    if (plan) {
      let { nodes, taskId } = plan
      let node = nodes.find(node => node.id === taskId)
      let config = (node && node.config) || {}
      isRetractNode =
        node &&
        config.allowSubmitterRetract &&
        (isFromeHomePage || (checkFromRequisionDetailTabPage(params) && isShowReminderOwner))
    }

    let { current_flow, expressPower } = this.props
    let submitNodes = logs?.filter(v => v.action === 'freeflow.submit').map(v => v.attributes) || []
    let { isUrgent, urgentReason, sensitiveContent, sensitiveAttachment } = submitNodes[submitNodes.length - 1] || {}
    let tags = { submitterId: { ownerId } }
    let errorMessages = [],
      notices = []
    if (flowRulePerformLogs) {
      const results = getV(flowRulePerformLogs, 'results', [])
      errorMessages = results.filter(element => element.type !== 'calculate')
      notices = notices.concat(errorMessages.map(o => ({ content: o.errorMsg })))
    }
    notices = notices.concat(noticeList)
    // 在这些页面跳转过来的需要显示补充发票的按钮
    let whiteList = [
      'homePage',
      'mine-archived',
      'mine-approved',
      'approving',
      'carbonCopyView',
      'approve-paying',
      'message',
      'search',
      'paidList'
    ]
    let isShowWait = whiteList.indexOf(params.sourcePage) >= 0
    let isHideWait = !(isShowWait && state !== 'rejected')
    if (specificationCurrent?.appId === '320-corporateReceipt') {
      // 收款管理下的发票组件要补充发票按钮要隐藏
      isHideWait = true
    }
    const showSensitive = showSensitiveContent(data, me_info?.staff?.id) && STATE_LIST.indexOf(state) < 0
    const arrangeLayoutList = arrangeLayout?.length ? arrangeLayout : this.defaultArrangeLayout

    let tabData = [
      {
        tab: i18n.get('单据详情'),
        key: 'bill-detail',
        children: (
          <div className="w-100p h-100p ovr-y-a">
            <div className="inertial-rolling h-100-percent">
              <OrderButtonWrapper
                formType={formType}
                bus={this.bus}
                billState={data.state}
                isBillOwner={isShowReminderOwner}
                flowCode={data.form && data.form.code}
              />
              <BillInfoReadonly
                openedDataLinkModalData={openedDataLinkModalData}
                type={formType}
                loanManualRepayment={loanManualRepayment}
                errorMessages={errorMessages}
                isRejected={false}
                bus={this.bus}
                value={value}
                flowId={current_flow?.flowId || current_flow?.id}
                ownerId={current_flow?.ownerId}
                plan={plan}
                tags={tags}
                writtenOff={writtenOff}
                flowRulePerformLogs={flowRulePerformLogs}
                isHideWait={isHideWait}
                isEditTaxRate={isEditTaxRate}
                external={flowRiskList}
                showLoan={true}
                carbonCopyType={params.carbonCopyType}
                billState={state}
                invoiceRiskData={invoiceRiskData}
                auto={data?.form?.systemGeneration}
                remunerationData={current_flow}
                isModifyBill={isModifyBill}
                arrangeLayout={arrangeLayoutList}
                skipCheck={skipCheck}
                sourcePage={params.sourcePage}
                renderRiskNotice={() => (
                  <Fragment key="RiskNotice">
                    {showSensitive && <WarningSensitive content={sensitiveContent} attachments={sensitiveAttachment} />}
                    {/* <RiskNotice notices={notices} /> */}
                    <RiskAlert
                      bus={this.bus}
                      billDetails={data}
                      riskWarningV2={invoiceRiskData?.riskWarningV2}
                      flowAllRiskType={invoiceRiskData || {}}
                    />
                    {isUrgent && <UrgentView state={state} urgentReason={urgentReason} />}
                  </Fragment>
                )}
                renderLogsCardView={() =>
                  isNeedBudget && !isModifyBill ? (
                    <LogsCardView
                      key="LogsCardView"
                      dataSource={data}
                      onLogMsgClick={this.handleGoToHistory}
                      userInfo={me_info}
                    />
                  ) : (
                    <div key="LogsCardDiv" />
                  )
                }
                renderFlowNodeView={() =>
                  showFlowPlan && (
                    <BillApprove
                      key="FlowNodeView"
                      bus={this.bus}
                      value={data}
                      isNeedBudget={isNeedBudget}
                      fromDataLink={fromDataLink}
                      isSimpleMode={true}
                    />
                  )
                }
                renderTripOrderAction={() => <TripOrderAction key="TripOrderAction" data={data} />}
              />
            </div>
          </div>
        )
      }
    ]

    if (this.props.KA_PREVIEW_PRINT_IN_MODAL) {
      tabData.splice(1, 0, {
        tab: i18n.get('审批预览'),
        children: <PrintPreview url={printPreviewUrl} />,
        key: 'printPreview'
      })
    }

    // 4.1 后报销单可能没有审批流
    if (data.plan && data.plan.id) {
      tabData.push({
        tab: i18n.get('审批流程'),
        key: 'bill-approve',
        children: <BillApprove bus={this.bus} value={data} isNeedBudget={isNeedBudget} fromDataLink={fromDataLink} />
      })
    }

    if (isNeedBudget && this.state.existBudgetOccupy && !isModifyBill) {
      tabData.push({
        tab: i18n.get('预算占用'),
        key: 'bill-takeUp',
        children: (
          <div className="detail-budget-wrapper-new-home">
            <ReportListView
              bus={this.bus}
              form={form}
              flowId={current_flow.flowId || current_flow.id}
              standardCurrency={standardCurrency}
              handleGotoDetail={this.handleGoToDetail}
            />
          </div>
        )
      })
    }

    if (expressPower && data.plan && data.plan.id && this.hasExpressNode) {
      tabData.push({
        tab: i18n.get('寄送信息'),
        key: 'bill-express',
        children: <BillExpress flow={data} canEditExpress={canEditExpress} />
      })
    }

    const configsList = get(data, 'form.specificationId.configs')
    const isLoan = configsList?.find(item => item.ability === 'loan')
    const loanMoney = get(data, 'form.loanMoney.standard')
    const stateLoan = get(data, 'state')
    let hide = false
    if ((params?.sourcePage === 'fromLoanDetail' || params?.sourcePage === 'fromSubmitterLoan') && getBoolVariation('hailiang_loan_permission')) {
      hide = true
    }
    if (isLoan && loanMoney > 0 && !hide && (stateLoan === 'paid' || stateLoan === 'archived')) {
      tabData.push({
        tab: i18n.get('借还详情'),
        key: 'bill-loan',
        children: <LoanPackageDetail params={params} fromBill={true} hiddenBottom={true} hiddenTitle={true} />
      })
    }

    if (tabData.length === 1 && isModifyBill) {
      return tabData[0].children
    }

    const isShowActionPanel = !widgetConfig.visible || (widgetConfig.visible && !widgetConfig.loading)
    return (
      <Box bus={this.bus}>
        <Box>
          <div className="layout-content-wrapper display-flex-1-hidden">
            <ETabs
              animated={true}
              swipeable={false}
              activeKey={tabActiveKey}
              dataSource={tabData}
              onChange={this.onTabClick}
            />
          </div>
        </Box>
        {params?.sourcePage === 'recyclePage' || params?.sourcePage === 'flowLinks'
          ? null
          : !isModifyBill &&
          formType !== 'permit' &&
          !fromDataLink && (
            <Footer>
              {isShowActionPanel &&
                this.renderActionPanel(
                  isShowReminder,
                  isRetractNode,
                  actions,
                  state,
                  isAlwaysPrint || params.sourcePage === 'message' ? false : form.forbidOwnerPrint,
                  data
                )}
            </Footer>
          )}
      </Box>
    )
  }

  pushSignShift = () => {
    const { state, billActions } = this.props
    const { data } = state
    // 是否在分享的消息中
    const inSharingMessage = getUrlParamString(window.location.search, 'messagePurpose') === 'sharing'
    const showShift = billActions.find(item => item.name === 'freeflow.addnode')
    const nodes = get(data, 'plan.nodes', [])
    const taskId = get(data, 'plan.taskId', '')
    const planNode = nodes.find(v => v.id === taskId) || {}
    if (!inSharingMessage && showShift && (!planNode.forbidBeforeAddNode || !planNode.forbidAftAddNode)) {
      return true
    }
    return false
  }

  renderDetailActions(isShowReminder, isRetractNode, forbidOwnerPrint) {
    const {
      billActions,
      buttonsClickInScan,
      state,
      params,
      expenseSpecAfterFiltered,
      autoExpenseWithBillStriction,
      me_info
    } = this.props
    let { carbonCopyType, carbonCopyReadType, formType, data } = state
    let { sourcePage } = params
    let isShowCarbonCopy = carbonCopyType === 'carbonCopy' && carbonCopyReadType === 'UNREAD'

    const {
      data: {
        formType: type,
        state: status,
        form: { specificationId }
      }
    } = state
    const allowSubmitterRetract = specificationId?.configs?.find(line => line.ability === 'requisition')
      ?.allowSubmitterRetract
    let arr = []
    let fnButtonClick = _ => { }

    if (billActions) {
      //billActions有值时代表入口为：扫描单据
      fnButtonClick = buttonsClickInScan
      arr = cloneDeep(billActions)
      if (this.pushSignShift()) {
        arr.push(signShift())
      }
    } else {
      fnButtonClick = this._buttonsClick
      if (!forbidOwnerPrint) {
        arr = [print()]
      }
      const isOwner = data?.form?.submitterId?.id === (me_info && me_info.staff && me_info.staff.id)
      if (
        (sourcePage === 'mine-archived' ||
          sourcePage === 'homePage' ||
          (checkFromRequisionDetailTabPage(params) && isOwner)) &&
        !window.isMessage &&
        !['reconciliation', 'settlement'].includes(formType) &&
        expenseSpecAfterFiltered?.extendType !== 'QuickExpense' &&
        !(autoExpenseWithBillStriction && type === 'expense')
      ) {
        arr.push(copy())
      }

      if (
        type === 'requisition' &&
        (status === 'archived' || status === 'paid') &&
        allowSubmitterRetract &&
        (sourcePage === 'homePage' || sourcePage === 'mall' || (checkFromRequisionDetailTabPage(params) && isOwner))
      ) {
        arr.push(changeBtn())
      }

      if (isShowReminder) {
        arr.push(reminder())
      }
      this.getWithdrawFooter(data.state, arr)
      if (isRetractNode) {
        arr.push(retract())
      }
      if (sourcePage === 'paidList' && carbonCopyType !== 'carbonCopy') {
        arr.push(confirm())
      }
      const buttonType = isShowCarbonCopy ? '' : 'main'
      arr.push(commentBtn(buttonType))
      if (isShowCarbonCopy) {
        arr.push(carbonCopyRead())
      }
    }
    return { actionArr: arr, buttonClick: fnButtonClick }
  }

  /**
   * @description 判断是否显示撤回按钮
   * 当前审批流中勾选了可以撤回审批多选框
   * 是从已审批列表进入
   * 当前审批流允许撤回并且上一个审批的节点是非ebot节点或者非重算节点时
   * 并且上一个已审批的节点不是自动跳过或者自动同意节点 可以撤回
   * @param state 审批状态
   * @param footer  底部按钮数组
   * */
  getWithdrawFooter = (state, arr) => {
    const { current_flow, me_info = {}, params = {} } = this.props
    // const { taskId = '', nodes = [] } = current_flow.plan
    const taskId = get(current_flow, 'plan.taskId', '')
    const nodes = get(current_flow, 'plan.nodes', [])
    const node = nodes.find(node => node.id === taskId) || {}
    // 当前审批流中勾选了可以撤回审批多选框
    const isAllowWithdraw = get(current_flow, 'plan.submitNode.isAllowWithdraw', false)
    //会签节点是否自己已经审批过  审批过才显示按钮
    const currentSigner = node?.counterSigners?.find(
      signer => signer?.signerId?.id === me_info?.staff?.id && signer?.state === 'PROCESSED'
    )
    // 获取上一个节点 不能用preId的原因是如果是自动审批节点 那么preId指向的是前个节点而不是上个节点
    const index = nodes.findIndex(v => v.id === node.id)
    const prevNode = index > 0 ? nodes[index - 1] : {}
    // 当前审批流允许撤回并且上一个节点是非ebot节点和非重算节点时 可以撤回
    const notEbotNode =
      prevNode.type !== 'ebot' && prevNode.type !== 'recalculate' && prevNode.type !== 'invoicingApplication' && prevNode.type !== 'aiApproval'
    // 是否是自动跳过样式
    const { agreeType = 'NO_AUTO_AGREE', skippedType = 'NO_SKIPPED' } = prevNode
    const agreeTypeFlag = agreeType && agreeType !== 'NO_AUTO_AGREE'
    const autoPass = skippedType !== 'NO_SKIPPED' || agreeTypeFlag
    // 是否是从待审批列表进入的
    const __status = params?.sourcePage === 'mine-approved'
    // 审批状态
    const approvingMap = {
      approving: 'approving',
      sending: 'sending',
      paying: 'paying'
    }
    const isApprovingStatus = !!approvingMap[state]
    let showApproveBtnCondition = isAllowWithdraw && notEbotNode && !autoPass && __status && isApprovingStatus
    if (!!currentSigner) {
      showApproveBtnCondition = showApproveBtnCondition && !!currentSigner
    }
    if (showApproveBtnCondition) {
      arr.push(handleAllowWithdraw())
    }
  }

  // 获取历史版本数据
  getHistoryVersions = async data => {
    const res = await getBillHistoryVersionList(data?.id, '')
    return res
  }

  // 获取两个版本间的数据diff
  getDiffs = async (type, curId, prevId) => {
    const privilegeId = ''
    const curVersion = await getBillHistoryVersionDetail(curId, privilegeId)
    const prevVersion = await getBillHistoryVersionDetail(prevId, privilegeId)
    const diffs = await getDiffsBetweenVersions(type, curVersion, prevVersion)
    return diffs
  }

  // 打开小组件
  handleViewDiff = async () => {
    const { invoiceRiskData, flowId, data } = this.props.state
    const { widgetConfig } = this.state
    const historyVersions = await this.getHistoryVersions({ id: flowId })
    let value = parseAsFormValue(data)
    app.open('@bill:BillVersionDiff', {
      versions: historyVersions?.items,
      getDiffs: this.getDiffs,
      riskData: getRiskReasonDataForVersionDiffModal(invoiceRiskData),
      dataSource: value,
      config: widgetConfig.config
    })
  }

  lazyGetActionMap = async (buttons) => {
    const { data: flow, from } = this.props.state
    if (!flow) {
      return null
    }
    let backlog = await this._getBacklogPromise
    if (!backlog) {
      backlog = {
        id: '',
        flowId: flow
      }
    }
    const isFromScanView = from === 'scan'
    const getPropsForActionMap = (button) => {
      const map = {
        [FlowAction.Modify]: {
          getTargetUrl: ({ id, formType } = {}, defaultUrl) => {
            return isFromScanView ? `/modify/${formType}/${id}/fromScanBill` : defaultUrl
          }
        },
        [FlowAction.AddAnnotation]: {
          inAddNoteMode: this.state.inAddNoteMode,
          onModeChange: (inAddNoteMode) => {
            this.setState({ inAddNoteMode })
            return {
              id: button.id,
              changeButton: true,
              name: inAddNoteMode ? i18n.get('退出批注模式') : i18n.get('添加批注'),
              showCurrentButton: inAddNoteMode,
              currencyCategory: 'primary'
            }
          }
        }
      }
      return map[button.action]
    }
    return buttons.reduce((acc, button) => {
      const actionType = button.action
      const Action = actionsMap[actionType]
      if (Action) {
        acc[actionType] = new Action({
          backlog,
          actionName: actionType,
          refreshData: () => {
            api.invokeService('@common:get:flow:detail:info', { id: flow.id })
          },
          propsForAction: getPropsForActionMap(button)
        })
      }
      return acc
    }, {})
  }

  buttons = (isShowReminder, isRetractNode, actions, flowState, forbidOwnerPrint) => {
    const messageSource = getV(this.props, 'params.messageSource', '')
    const state = getV(this.props, 'current_flow.state')
    let arr = []
    let fnButtonClick = _ => { }
    // 消息进来，source是workrecord时使用后台返回的action
    if (messageSource === 'workrecord') {
      fnButtonClick = this._buttonsClick
      const { me_info } = this.props
      arr = billActions(me_info, actions)
      if (!['reject', 'draft'].includes(flowState)) {
        arr.push(commentBtn())
      }
    } else {
      const { actionArr, buttonClick } = this.renderDetailActions(isShowReminder, isRetractNode, forbidOwnerPrint)
      arr = actionArr
      fnButtonClick = buttonClick
    }

    //加入添加批注按钮
    if (state !== 'draft' && state !== 'rejected') {
      arr.push(addNote())
    }

    return { actionArr: arr, buttonClick: fnButtonClick }
  }

  pushShareBtn = (btns, rightButtons) => {
    const urlParams = qs.parse(window.location.search.slice(1))
    if (urlParams?.hiddenShareBtn) {
      return
    }

    if (window.isDingtalk || window.isFeishu || window.isWxWork) {
      // 平铺的按钮大于等于四个时，收起一个
      if (rightButtons?.length >= 4) {
        btns.push(rightButtons.shift())
      }

      const shareBtn = {
        name: 'share',
        label: i18n.get('转发'),
        weight: 150
      }
      btns.push(shareBtn)
    }
  }

  renderActionPanel = (isShowReminder, isRetractNode, actions, flowState, forbidOwnerPrint = false, data) => {
    const { current_flow, billAdditionalInfo, params } = this.props
    let { actionArr, buttonClick } = this.buttons(isShowReminder, isRetractNode, actions, flowState, forbidOwnerPrint)
    let rightButtons = []
    if (actionArr.length) {
      actionArr = sortBy(actionArr, item => item.weight)
      rightButtons = actionArr.splice(-4)
    }
    const { inAddNoteMode, widgetConfig } = this.state
    if (inAddNoteMode) {
      actionArr = []
      rightButtons = [cancelAddNote()]
    }
    if (flowState === 'receivingExcep') {
      actionArr = []
      rightButtons = [reminder(), copy(), comment()]
      if (!forbidOwnerPrint) {
        rightButtons.push(print())
      }
    }

    //判断打印按钮的隐藏显示
    rightButtons = isPrintShow({ selectData: data })
      ? rightButtons
      : rightButtons.filter(
        line => line.name !== 'print' && line.name !== 'printInvoice' && line.name !== 'printRemind'
      )

    if (widgetConfig.visible) {
      const otherConfig = widgetConfig.config?.otherConfig
      if (otherConfig?.showMobileShortcut) {
        if (rightButtons.length >= 4) {
          actionArr.push(rightButtons.shift())
        }
        const btn = billVersionBtn()
        btn.label = (
          <div style={{ display: 'block', lineHeight: 'normal' }}>
            <EkbIcon name="#danjuxinxi" style={{ fontSize: '24px', display: 'block', margin: '0 auto' }} />
            <div style={{ fontSize: '12px' }}>{i18n.get('辅助信息')}</div>
          </div>
        )
        rightButtons.unshift(btn)
      }
      if (actionArr?.length > 0) {
        actionArr.push(billVersionBtn())
      }
    }

    if (!forbidOwnerPrint && this.state.showPrintBtn) {
      actionArr.unshift(printInvoice())
    }

    // 钉钉环境添加分享按钮
    if (rightButtons.length < 3) {
      this.pushShareBtn(rightButtons)
    } else {
      this.pushShareBtn(actionArr, rightButtons)
    }

    const isCarbonCopy = params?.carbonCopyType === 'carbonCopy'
    // 抄送单据不通过配置按钮管理
    const useButtonConfig = billAdditionalInfo?.needConfigButton ? !isCarbonCopy : billAdditionalInfo?.needConfigButton

    console.log({
      current_flow,
      data,
    })

    return (
      <BillInfoButtons
        needConfigButton={useButtonConfig}
        flowId={current_flow?.flowId || current_flow?.id}
        scene={billAdditionalInfo?.scene}
        flowActionMap={this.fnGetActionMap()}
        getFlowActionMap={this.lazyGetActionMap}
      >
        <ActionsPanel
          leftButtons={actionArr}
          rightButtons={rightButtons}
          buttonAction={buttonClick}
          specId={data?.form?.specificationId}
        />
      </BillInfoButtons>
    )
  }
}

function commentBtn(buttonType = 'main') {
  const commentBtn = comment()
  commentBtn.buttonType = buttonType
  return commentBtn
}
