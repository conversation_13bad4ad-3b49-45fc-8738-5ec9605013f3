import React from 'react'
import {set} from 'lodash'

export function formatRiskWarningsV2(riskAll, details) {
  const { singleInvoiceRiskWarning = [], value, invoiceRiskExplainContent } = riskAll
  const result = formatRiskWarnings(value, details)
  if (result?.form?.details && singleInvoiceRiskWarning.length > 0) {
    const riskNameAndInvoiceRiskDataMap = {}
    const riskWarningReasonMap = {}
    singleInvoiceRiskWarning.forEach((item) => {
      const {
        invoiceId,
        invoiceMsg,
        riskWarning,
        pathValueId,
        riskWarningReason
      } = item
      riskWarningReason?.forEach(reasonItem => {
        const { invoiceNormDesc } = reasonItem
        riskWarningReasonMap[`${pathValueId}-${invoiceNormDesc}`] = reasonItem
      })
      riskWarning?.forEach?.(name => {
        const list = riskNameAndInvoiceRiskDataMap[name] ?? []
        const relatedFlows = item.relatedFlows?.[name] ?? []
        riskNameAndInvoiceRiskDataMap[name] = list.concat({
          invoiceMsg,
          invoiceId,
          pathValueId,
          invoiceRiskExplainContent: riskWarningReasonMap[`${pathValueId}-${name}`]?.invoiceRiskExplainContent || ( name === '票据连号' ? invoiceRiskExplainContent ?? '' : ''),
          relatedFlows
        })
      })
    })


    Object.keys(result.form.details).forEach(key => {
      const detail = result.form.details[key]
      const isInvoiceRiskEmpty = !detail.invoiceForm || detail.invoiceForm.length === 0;
      if (isInvoiceRiskEmpty) {
        return
      }
      for (const risk of detail.invoiceForm) {
        const { controlName, pathValueId } = risk
        const name = controlName.slice(0, -1)
        // switch (name) {
        //   case '发票疑似重复': {
        //     risk.messagesV2 = (riskNameAndInvoiceRiskDataMap[name] ?? []).filter(item => item.pathValueId === key)
        //     break
        //   }
        //   case '票据连号': {
        //     risk.messagesV2 = (riskNameAndInvoiceRiskDataMap[name] ?? []).filter(item => item.pathValueId === key)
        //     break
        //   }
        // }
        if (name == '发票疑似重复' || name == '票据连号' || riskWarningReasonMap[`${pathValueId}-${name}`]) {
          risk.messagesV2 = (riskNameAndInvoiceRiskDataMap[name] ?? []).filter(item => item.pathValueId === key)
        }
      }
    })
  }


  return result
}

export function formatRiskWarnings(value, details) {
  if (!value) return {}
  const { riskWarning } = value
  let flowRiskWarnings = {}
  let detailObj = {}
  let apportionsObj = {}
  set(flowRiskWarnings, ['form', 'trips'], {})
  riskWarning.map(item => {
    if (item.path === 'FORM') {
      exists(flowRiskWarnings.form, item)
    } else if (item.path === 'FEE_DETAIL') {
      parseFieldToFormat(detailObj, item)
    } else if (item.path === 'APPORTION') {
      parseApportionsToFormat(apportionsObj, item, details)
    } else if (item.path === 'TRIP_DETAIL') {
      parseFieldToFormat(flowRiskWarnings.form.trips, item)
    }
  })

  handleApportionsObj(detailObj, apportionsObj)

  handleDetailObj(detailObj, flowRiskWarnings)

  handleTripsObj(flowRiskWarnings)
  return flowRiskWarnings
}

export default formatRiskWarnings

function exists(key, item) {
  key[item.controlField] ? key[item.controlField].push(item) : (key[item.controlField] = [item])
}

function parseFieldToFormat(obj, item) {
  obj[item.pathValueId] ? exists(obj[item.pathValueId], item) : set(obj, [item.pathValueId, item.controlField], [item])
}

function parseApportionsToFormat(apportionsObj, item, details) {
  // let [pathValueId, idx] = item.pathValueId.split('$')
  const apportionIdMap = getApportionIdMap(details)
  let pathValueId, idx
  if (apportionIdMap[item.pathValueId]) {
    pathValueId = apportionIdMap[item.pathValueId].detailId
    idx = apportionIdMap[item.pathValueId].apportionId
    console.log("====== apportionIdMap", apportionIdMap);
    console.log("====== pathValueId", pathValueId);
    console.log("====== apportionId", idx);
  } else {
    pathValueId = item.pathValueId.split('$ID_')[0]
    idx = item.pathValueId.substring(pathValueId.length + 1)
  }
  apportionsObj[pathValueId]
    ? apportionsObj[pathValueId][idx]
      ? exists(apportionsObj[pathValueId][idx], item)
      : set(apportionsObj, [pathValueId, idx, item.controlField], [item])
    : set(apportionsObj, [pathValueId, idx, item.controlField], [item])
}

function handleApportionsObj(detailObj, apportionsObj) {
  for (let key in detailObj) {
    if (apportionsObj.hasOwnProperty(key)) {
      detailObj[key].apportions = apportionsObj[key]
      delete apportionsObj[key]
    }
  }

  if (Object.keys(apportionsObj).length) {
    for (let key in apportionsObj) {
      detailObj[key] = { apportions: apportionsObj[key] }
    }
  }
}

function handleDetailObj(detailObj, flowRiskWarnings) {
  if (Object.keys(detailObj).length) {
    flowRiskWarnings.form.details = detailObj
  }
}

function handleTripsObj(flowRiskWarnings) {
  if (!Object.keys(flowRiskWarnings.form.trips).length) {
    delete flowRiskWarnings.form.trips
  }
}

/**
 * 根据费用明细的值获取分摊风险path对应的分摊id和费用明细id
 */
export const getApportionIdMap = (details) => {
  if (!details?.length) return {}
  const map = {}
  details.forEach(detail => {
    const detailId = detail?.feeTypeForm?.detailId
    const apportions = detail?.feeTypeForm?.apportions
    if (!apportions?.length || !detailId) return
    apportions.forEach(apportion => {
      const apportionId = apportion?.apportionForm?.apportionId
      if (!apportionId) return
      map[`${detailId}$${apportionId}`] = { detailId, apportionId }
    })
  })
  return map
}