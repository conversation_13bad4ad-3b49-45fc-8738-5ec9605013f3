import React from 'react'
import { getBoolVariation } from '../../../lib/featbit'
import Markdown from '../../../elements/Markdown'

export const riskTypeMap: any = {
  costControl: i18n.get('费标'),
  budget: i18n.get('预算')
}

export const riskColorMap: any = {
  costControl: {
    warn: 'orange-bg',
    error: 'red-bg'
  },
  budget: {
    yellow: 'orange-bg',
    red: 'red-bg',
    blue: 'blue-bg'
  }
}

const costControlComponent = (risk: any) => {
  const riskDesc: string[] = risk.ruleDetail ? risk.ruleDetail.split('\n') : []
  return (
    <div className="cost-control-item">
      {risk.ruleDetail ? <div className="risk-rule-detail">{riskDesc.map(l => <div style={{ minHeight: 16 }}>{l}</div>)}</div> : null}
      <div className="risk-messages">
        {risk.messages?.map((msg: string, index: number) => (
          <div className="risk-messages-item" key={msg}>
            {risk.messages?.length === 1
              ? getBoolVariation('ao-46-bill-risk-markdown') ? <Markdown context={msg} type='risk' /> : msg
              : `${index + 1}、` + msg}
          </div>
        ))}
      </div>
    </div>
  )
}

const budgetComponent = (item: any) => {
  if (!item?.nodes?.length) return null
  return item.nodes.map((el: any, index: number) => {
    const isShowContent =
      el.budgetMoney !== null || el.userdMoney !== null || el.remainMoney !== null || el.budgetPercent !== null
    return (
      <div key={index} className="budget-risk-item risk-warning-item-content">
        <div className="budget-risk-item-name">
          <div>{el.nodeName}</div>
          <div>
            {i18n.get('超标{__k0}时，', { __k0: el.controlPercent })}
            {el.control}
          </div>
        </div>
        {isShowContent && (
          <div className="budget-risk-item-content">
            {el.budgetMoney !== null && (
              <div>
                <span className="budget-content-label">{i18n.get('预算金额') + '：'}</span>
                <span className="budget-content-value">
                  {item.symbol}
                  {el.budgetMoney}
                </span>
              </div>
            )}
            {el.userdMoney !== null && (
              <div>
                <span className="budget-content-label">{i18n.get('已用金额') + '：'}</span>
                <span className="budget-content-value">
                  {item.symbol}
                  {el.userdMoney}
                </span>
              </div>
            )}
            {el.remainMoney !== null && (
              <div>
                <span className="budget-content-label">{i18n.get('预算余额') + '：'}</span>
                <span className="budget-content-value">
                  {item.symbol}
                  {el.remainMoney}
                </span>
              </div>
            )}
            {el.budgetPercent !== null && (
              <div>
                <span className="budget-content-label">{i18n.get('预算执行比例') + '：'}</span>
                <span className="budget-content-value">{el.budgetPercent}</span>
              </div>
            )}
          </div>
        )}
      </div>
    )
  })
}

export const getRiskComponent = () => {
  return {
    costControl: costControlComponent,
    budget: budgetComponent
  }
}
