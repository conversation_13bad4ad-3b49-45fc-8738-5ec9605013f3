import { app } from '@ekuaibao/whispered'
const parseAsMeta = app.require('@lib/parser.parseAsMeta')
import { toJS } from 'mobx'
import { summarySelectType } from '../../../components/consts'

//获取默认Specification
export function getDefSpecificationDS(dataSource, specificationGroupsList) {
  if (!specificationGroupsList) return
  if (dataSource.form && dataSource.form.specificationId) {
    let specification = dataSource.form.specificationId
    return specification
  }
  return null
}

function updateFeeDetailOptional(specification = {}) {
  const receipt = specification?.configs.find(v => v.ability === 'receipt')
  const isReceipt = (receipt?.summarySelect == summarySelectType.repay && receipt?.feeDetailNotRequired) || false
  const feeType = specification?.components.find(v => v.field == 'details')
  // 单据为收款金额并且非必填项 重置 optional 字段 为可选 summarySelect == 2 表示为还款金额类型，还款金额并且非必填
  if (isReceipt && feeType) {
    feeType.optional = true
  }
  return specification
}

export default function fnParseSpecification(props, state, inReadOnly) {
  let {
    specification,
    specification_group,
    globalFields,
    value,
    isRejected,
    type,
    from,
    expenseLink,
    isModifyBill,
    limitFieldRequireds
  } = props
  if (state && state.specification && state.template) {
    if (state.value !== props.value) {
      state.value = props.value
      state.code = props.value.code
    }
    state.specification = updateFeeDetailOptional(toJS(state.specification))
    return state
  }

  let template

  if (!specification) {
    //说明必须的参数没有返回
    if (!specification_group || !specification_group.length || inReadOnly) {
      return { specification: null, template: null, value, code: value && value.code }
    }
    let specArr = []
    specification_group.forEach(group => {
      group.specifications.forEach(spec => {
        specArr.push(spec)
      })
    })
    specArr = specArr.filter(spe => spe.type === type)
    specification = specArr[0]
  }

  if (specification) {
    specification = updateFeeDetailOptional(toJS(specification))
    template = parseAsMeta(specification, globalFields)
  }

  // 申请事项补充申请时
  if (from === 'supplyReq' && expenseLink) {
    // TODO: 申请单的行程 费用明细 不需要关联
    value['u_行程规划'] = []
    value['details'] = []
    let linkRequisitionInfoComponent = template.find(v => v.name === 'linkRequisitionInfo')
    let legalEntityMultiCurrency = template.find(v => v.name === 'legalEntityMultiCurrency')
    if (linkRequisitionInfoComponent) {
      linkRequisitionInfoComponent.editable = true
    }
    if (legalEntityMultiCurrency) {
      legalEntityMultiCurrency.editable = true
    }
  }

  if (isModifyBill && limitFieldRequireds?.length) {
    template = modifyBillOfLimitField(limitFieldRequireds, template)
  }
  // 申请按明细赋值，移动bill中的延迟赋值，在初始化的时候赋值，解决自动计算带不出值的问题
  if(template.find(v => v.name === 'expenseLinks') && expenseLink){
    value['expenseLinks'] = [expenseLink]
  }

  // let expenseLink = api.getState()['@bill'].expenseLink
  // if (template && !isRejected) {
  //   template = fnChangeExpenseLinkField(template, expenseLink)
  // }
  return { specification, template, value, code: value && value.code }
}

export function modifyBillOfLimitField(limitFieldRequireds, template) {
  if (limitFieldRequireds?.length && template) {
    const limitRequiredFieldMap = limitFieldRequireds.reduce((res, field) => {
      res[field] = field
      return res
    }, {})
    template = template?.map(field => {
      if (limitRequiredFieldMap[field?.name]) {
        field.optional = false
      }
      return field
    })
  }
  return template
}

// export function fnChangeExpenseLinkField(template, expenseLink) {
//   return template.map(line => {
//     if (line.field === 'expenseLink' || line.field === 'expenseLinks') {
//       line.editable = !expenseLink
//     }
//     return line
//   })
// }

// export function getSpecifications(type) {
//   switch (type) {
//     case 'expense':
//       api.dataLoader('@common.specification_expense').load()
//       break
//     case 'loan':
//       api.dataLoader('@common.specification_loan').load()
//       break
//     case 'requisition':
//       api.dataLoader('@common.specification_requisition').load()
//       break
//   }
// }
