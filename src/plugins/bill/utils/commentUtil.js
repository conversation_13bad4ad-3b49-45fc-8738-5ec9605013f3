/**
 *  Created by <PERSON><PERSON> on 2018/3/5 下午3:28.
 */
import { isOPG } from '../../../hosting/Utils'
import { getUserDisplayName } from '../../../components/utils/fnDataLinkUtil'

export function getStaffDisplayName(staff) {
  let id = ''
  if (staff.code && staff.code.length) {
    id = staff.code
  } else {
    let suffixId = staff.id.split(':')[1]
    id = suffixId?.length > 10 ? suffixId.substring(0, 10) : suffixId
  }
  const name = getUserDisplayName(staff)
  return `[${name.trim()}${isOPG() ? '' : `:${id}`}]`
}

export function getMentions(comment, prefix = '@') {
  const regex = getRegExp(prefix)
  const entities = []
  let matchArr
  while ((matchArr = regex.exec(comment)) !== null) {
    entities.push(matchArr[0].trim())
  }
  return entities
}

export function getRegExp(prefix) {
  const prefixArray = Array.isArray(prefix) ? prefix : [prefix]
  let prefixToken = prefixArray.join('').replace(/(\$|\^)/g, '\\$1')
  if (prefixArray.length > 1) {
    prefixToken = `[${prefixToken}]`
  }
  return new RegExp(`${prefixToken}\\[[^\\[\\]]*\\]`, 'g')
}

export function insertFlg(str, flg, sn) {
  let tmp0 = str.substring(0, sn)
  let tmp1 = str.length > sn ? str.substring(sn, str.length) : ''
  return tmp0 + flg + tmp1
}

export function getCursorPosition(areaId) {
  let txtarea = document.getElementById(areaId)
  return txtarea.selectionStart
}
