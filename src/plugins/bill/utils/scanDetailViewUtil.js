export const scanDetailActions = {
  'freeflow.pay': { name: 'freeflow.pay', label: i18n.get('支付'), weight: 16 },
  'freeflow.agree': { name: 'freeflow.agree', label: i18n.get('同意'), weight: 15 },
  'freeflow.addnode': { name: 'freeflow.addnode', label: i18n.get('转交审批'), weight: 14 },
  'freeflow.reject': { name: 'freeflow.reject', label: i18n.get('驳回'), weight: 13 },
  'freeflow.editApproving': { name: 'freeflow.editApproving', label: i18n.get('修改'), weight: 12 },
  'freeflow.retract': { name: 'freeflow.retract', label: i18n.get('撤回'), weight: 11 },
  'freeflow.remind': { name: 'freeflow.remind', label: i18n.get('打印提醒'), weight: 10 },
  'freeflow.print': { name: 'freeflow.print', label: i18n.get('打印单据'), weight: 9 },
  'freeflow.printInvoice': { name: 'freeflow.printInvoice', label: i18n.get('打印单据和发票'), weight: 9 },
  'freeflow.comment': { name: 'freeflow.comment', label: i18n.get('评论'), weight: 8 },
  'freeflow.send': { name: 'freeflow.send', label: i18n.get('添加寄送信息'), weight: 7 },
  'freeflow.skip.send': { name: 'freeflow.skip.send', label: i18n.get('跳过寄送'), weight: 6 },
  'freeflow.receive': { name: 'freeflow.receive', label: i18n.get('确认收单'), weight: 5 },
  'freeflow.submit': { name: 'freeflow.submit', label: i18n.get('提交'), weight: 4 },
  'freeflow.edit': { name: 'freeflow.edit', label: i18n.get('保存'), weight: 3 },
  'freeflow.delete': { name: 'freeflow.delete', label: i18n.get('删除'), weight: 2 },
  'freeflow.urge': { name: 'freeflow.urge', label: i18n.get('催办'), weight: 1 },
  'freeflow.copy': { name: 'freeflow.copy', label: i18n.get('复制'), weight: 0.9 }
}
