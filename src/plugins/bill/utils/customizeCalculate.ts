/**************************************************
新建单据：currentEditField[{"type": "master_","values": [],"operate":"addBill"}]
单据字段变更：currentEditField[{"type": "master_","values": ["title"],","operate":"modifyBill""}]
单据删除明细：currentEditField[{"type": "detail_","values": [],"operate":"deleteFeeType"}]
单据导入/申请单导入明细：currentEditField[{"type": "detail_","values": [1,2],"operate":"exportFeeType"}]
添加明细：currentEditField[{"type": "detail_","values": [],"operate":"addFeeType"}]
编辑明细：currentEditField[{"type": "detail_","values": ["title"],"operate":"modifyFeeType"}]
单据：草稿、驳回态，修改模式 重新开始编辑时  手动计算的 不可以重新赋值， 自动计算要重新赋值
明细：保存后再次编辑 手动计算的 不可以重新赋值， 自动计算要重新赋值

 **************************************************/
import { app, app as api } from '@ekuaibao/whispered'
import { get, xor, cloneDeep, isEqual } from 'lodash'
import { isObject, isNull, isString } from '@ekuaibao/helpers'
import { parseShowValue2SaveValue } from './formatUtil'
import { showLoading, toast, hideLoading } from '../../../lib/util'
import { standardValueMoney, standardMoneyByForeign } from '../../../components/utils/fnInitalValue'
import { checkStaffDataRange } from '../../../components/utils/fnCheckStaffDataRange'

/**
 * isCalculateWrite 为true的情况下会计算手动填写中的自动计算，false不计算
 * **/
const __queues: any = []
let __timerId: any

/**
 * @param fn 回调函数
 */
export async function callCustomizeCalByQueue(fn: () => Promise<boolean>) {
  __queues.push(fn)
  const sendRequst = () => {
    if (!__timerId) {
      __timerId = setInterval(async () => {
        if (__queues.length) {
          const firstFn = __queues.shift()
          firstFn()
        } else {
          clearInterval(__timerId)
          __timerId = null
        }
      }, 200)
    }
  }
  sendRequst()
}

function deleteUselessKey(formData: any = {}) {
  const form: any = {}
  Object.keys(formData).forEach((key: string) => {
    if (formData[key] !== undefined && !isNull(formData[key])) {
      form[key] = formData[key]
    }
  })
  return form
}

/**
 * @method async getCustomizeCalResultOnField
 * @param {baseDataPropertiesMap} Object
 * @param {bus} messageCenter
 * @param {specification} Object specification
 * @param {formData} Object fromValue
 * @param {sourceType} String  master_||detail_||trip_
 * @param {submitterId} String  submitterId
 * @return undefined
 */
export async function getCustomizeCalResultOnField(
  baseDataPropertiesMap: any,
  bus: any,
  specification: any,
  formData: any,
  sourceType: any,
  billContent?: any,
  updateLocalStorage?: any,
  template?: any,
  checkDefaultValue?: boolean,
  calculateSpecificationID?: string,
  state?: string,
  changeValues?: any,
  changedFields?: any,
  onlyQueryFields?: boolean,
  filterDataLinkFields?: boolean
) {
  let billData: any = parseShowValue2SaveValue(deleteUselessKey(formData))
  switch (sourceType) {
    case 'master_':
      billData.source = 'master_'
      break
    case 'detail_':
      billContent.feeTypeForm = deleteUselessKey(billContent.feeTypeForm)
      formData.details = [billContent]
      billData = parseShowValue2SaveValue(formData)
      billData.specificationId = specification.id
      billData.source = 'detail_'
      break
    case 'trip_':
      billContent.tripTypeForm = deleteUselessKey(billContent.tripForm)
      formData.trips = [billContent]
      billData = parseShowValue2SaveValue(formData)
      billData.specificationId = specification.id
      billData.source = 'trip_'
      break
    default:
      throw new TypeError('sourceType is invalid')
  }
  addArgumentsForCost(billData, specification)
  const params: any = getParams(formData, billData, sourceType, billContent, checkDefaultValue)
  const currentEditField = changedFields ? changedFields : getChangedFields(billData.source, changeValues)
  const results = await api
    .invokeService('@bill:get:customizeCalculationresult', {
      form: { ...params.billData, ...params.formData, state, currentEditField, filterDataLinkFields }
    })
    .then((action: any) => {
      const { value } = action.payload
      const items = value?.onFieldValues
      if (!items) {
        return toast.error(action.payload.msg)
      }
      const hasError = items.filter((v: { errorMsg: any }) => v.errorMsg)
      if (hasError.length > 0) {
        throw { msg: hasError[0].errorMsg }
      }
      return action.payload.value
    })
    .catch((error: any) => {
      return toast.error(error.msg || error.errorMessage)
    })

  if (!results) {
    return
  }
  //分摊中联查 赋值逻辑在分摊模块处理
  if (currentEditField?.length && currentEditField[0].type === 'apportion_') {
    return results
  }
  const { resultValue, attrValue } = await formatCustomizeCalculateResult(
    bus,
    results?.onFieldValues,
    baseDataPropertiesMap,
    template,
    checkDefaultValue,
    calculateSpecificationID,
    onlyQueryFields
  )
  if (Object.keys(attrValue).length) {
    bus.emit('update:calculate:template', attrValue)
  }
  if (Object.keys(resultValue).length) {
    bus.setFieldsValue({ ...resultValue }).then((_: any) => {
      updateLocalStorage && updateLocalStorage()
    })
  }
  return results
}

function getParams(formData: any, billData: any, sourceType: any, billContent: any, checkDefaultValue: boolean) {
  const params: any = { submitterId: formData?.submitterId?.id, isCalculateWrite: checkDefaultValue || false }
  if (sourceType === 'master_') {
    const { details, trips, ...others } = billData
    params.billData = billData
    params.formData = others
  }
  if (sourceType === 'detail_') {
    params.formData = billContent
    params.billData = billData
  }
  if (sourceType === 'trip_') {
    params.billData = billData
    params.formData = billContent
  }
  return params
}

async function formatCustomizeCalculateResult(
  bus: any,
  results: any,
  baseDataPropertiesMap: any,
  components: any,
  checkDefaultValue: boolean,
  calculateSpecificationID: any,
  onlyQueryFields: boolean
) {
  const formValue = (await bus.getValue()) || {} //  当前表单值
  const resultValue: any = {}
  const attrValue: any = {}
  let changeDetailFields: any = []
  results?.forEach?.((element: any) => {
    const {
      onField,
      result,
      error,
      dataFrom,
      loc,
      resultType = 'VALUE',
      attribute,
      currencyType,
      numCode,
      value,
      specificationId,
      flag,
      idx,
      allMatchList,
      apportionIdx,
      isCanViewAllDataWithResultBlank,
      viewFullData = false
    } = element
    // if(specificationId !== calculateSpecificationID) return //用来区别是更新单据中的字段还是更新费用明细的字段
    const r = value === '' || value === 'null' ? undefined : value
    const globalField = baseDataPropertiesMap[onField]
    const type = get(globalField, 'dataType.type')
    const entity = get(globalField, 'dataType.entity') || get(globalField, 'dataType.elemType.entity')
    const component = components && components.find((v: any) => v.name === onField)
    if (resultType === 'VALUE') {
      // 自定义档案|| 业务对象  选项从结果中获取
      if (
        type === 'ref' &&
        isString(entity) &&
        (entity.startsWith('basedata.Dimension') || entity.includes('datalink.DataLinkEntity')) &&
        dataFrom !== 'apportions'
      ) {
        attrValue[onField] = {
          ...attrValue[onField],
          allMatchList: viewFullData ? undefined : allMatchList,
          isCanViewAllDataWithResultBlank,
          viewFullData
        }
      }
      // 修改单据字段需要赋值明细/或者分摊时
      if (specificationId !== calculateSpecificationID && (dataFrom === 'details' || dataFrom === 'apportions')) {
        const { details = [] } = formValue
        const currentDetail = details.find((v: any) => {
          return v.specificationId?.id === specificationId && v.idx === idx
        })
        if (!currentDetail) return
        const { feeTypeForm } = currentDetail
        // 单据上联查影响到分摊时 = 编辑单据
        if (dataFrom === 'apportions' && apportionIdx > -1) {
          // 草稿驳回态单据、以保存的明细 重新编辑时，不再覆盖可手动编辑的字段
          if (onlyQueryFields) return
          if (feeTypeForm.apportions && feeTypeForm.apportions[apportionIdx]) {
            const oldValue = feeTypeForm.apportions[apportionIdx]?.apportionForm[onField]
            // 原字段不在 可选范围内时重置
            const isExist = allMatchList && allMatchList.items?.find((i: any) => i.id === oldValue?.id)
            if (isExist) return
            feeTypeForm.apportions[apportionIdx].apportionForm[onField] = r && isString(r) ? JSON.parse(r) : null
            changeDetailFields.push('apportions')
            resultValue.details = details
          }
        } else {
          const feeTypeComponent = currentDetail.specificationId.components.find((v: any) => onField === v.field)
          // 草稿驳回态单据、以保存的明细 重新编辑时，不再覆盖可手动编辑的字段
          if (onlyQueryFields && feeTypeComponent.editable) return
          const defaultValue = get(feeTypeComponent, 'defaultValue.type')
          if (
            feeTypeComponent &&
            !checkValueChange(
              feeTypeForm[onField],
              r,
              type,
              currentDetail.specificationId.components,
              onField,
              checkDefaultValue,
              entity,
              currencyType
            )
          ) {
            if (defaultValue === 'customizeQuery') {
              feeTypeForm[onField] = formatValue(type, entity, r, feeTypeComponent, feeTypeForm[onField])
              changeDetailFields.push(onField)
            }
          }
          resultValue.details = details
        }
      } else if (dataFrom === 'apportions' && apportionIdx > -1) {
        if (onlyQueryFields) return
        // 修改费用类型影响到分摊时 = 编辑费用明细
        if (formValue.apportions && formValue.apportions[apportionIdx]) {
          const oldValue = formValue.apportions[apportionIdx]?.apportionForm[onField]
          //原字段不在 可选范围内时重置
          const isExist = allMatchList && allMatchList.items?.find((i: any) => i.id === oldValue?.id)
          if (isExist) return
          let v = r && isString(r) ? JSON.parse(r) : {}
          v.allMatchList = allMatchList
          formValue.apportions[apportionIdx].apportionForm[onField] = v
          resultValue.apportions = formValue.apportions
          setTimeout(() => {
            if (bus.has('feeType:update:apportions')) {
              bus.invoke('feeType:update:apportions', formValue.apportions)
            }
          }, 500)
        }
      } else if (
        component &&
        !checkValueChange(formValue[onField], r, type, components, onField, checkDefaultValue, entity, currencyType) &&
        !error
      ) {
        // 草稿驳回态单据、以保存的明细 重新编辑时，不再覆盖可手动编辑的字段
        if (onlyQueryFields && component.editable) return
        resultValue[onField] = formatValue(type, entity, r, component, formValue[onField], { currencyType, numCode })
      }
    }
    if (specificationId === calculateSpecificationID && dataFrom !== 'apportions') {
      attrValue[onField] = {
        ...attrValue[onField],
        customizeCalculateTip: flag ? i18n.get('数据联查出多个结果，默认自动填写第一个') : flag,
        isCanViewAllDataWithResultBlank
      }
    }
  })
  if (changeDetailFields.length) {
    const changeDetails = cloneDeep(formValue.details)
    changeDetails.currentEditField = [
      { type: 'detail_', values: Array.from(new Set(changeDetailFields)), operate: 'saveFeeType' }
    ]
    setTimeout(() => bus.invoke('detail:value:change', changeDetails), 500)
    bus.setFieldsValue({ details: formValue.details })
  }
  return { resultValue, attrValue }
}

function getChangedFields(source: any, changeValues: any) {
  let values = [] //后端根据变化的值 来判断哪些字段需要重新赋值
  Object.keys(changeValues).forEach(key => {
    values.push(key)
  })
  return [{ type: source, values, operate: source === 'master_' ? 'editBill' : 'editFeeType' }]
}

function formatSetValue(attribute: any, result: any, component: any, field: any) {
  const type = get(field, 'dataType.elemType.type') || get(field, 'dataType.type')
  const res = {}
  let typeArr = ['text', 'ref', 'boolean']
  if (typeArr.indexOf(type) > -1 && result !== undefined) {
    return { ...res, [attribute]: result === 'true' ? true : false, ['hasAttrHide']: true }
  }
  return res
}

function formatAttrValue(attribute: any, result: any, component: any, field: any) {
  const type = get(field, 'dataType.elemType.type') || get(field, 'dataType.type')
  const res = {}
  let typeArr = ['attachment', 'text', 'ref']
  if (typeArr.indexOf(type) > -1 && result && attribute) {
    return { ...res, [attribute]: result === 'true' ? false : true }
  }
  return res
}

export function formatValue(
  type: string,
  entity: string,
  value: string | undefined,
  component: any,
  formValue?: any,
  resultCurrencyInfo?: any
) {
  if (type === 'money') {
    if (isObject(formValue)) {
      const defaultRate = 1
      const { foreignScale, scale, rate = defaultRate } = formValue
      if (isString(value)) {
        if (resultCurrencyInfo?.currencyType === 'FOREIGN') {
          return standardMoneyByForeign(value, resultCurrencyInfo?.numCode, rate)
        } else {
          if (formValue.hasOwnProperty('foreign')) {
            formValue.foreign = new Big(value).div(rate || defaultRate).toFixed(foreignScale || scale)
          }
          formValue.standard = value
        }
      }
      return { ...formValue }
    }
    if (isString(value)) {
      const dimensionCurrency = api.getState()['@bill'].dimensionCurrencyInfo
      if (resultCurrencyInfo?.currencyType === 'FOREIGN') {
        return standardMoneyByForeign(value, resultCurrencyInfo?.numCode)
      }
      return standardValueMoney(value, dimensionCurrency?.currency)
    }
  } else if (type === 'date' && value) {
    return parseFloat(value)
  } else if (type === 'dateRange' && isString(value)) {
    return JSON.parse(value)
  } else if ((type === 'switcher' || type === 'boolean') && value) {
    return value === 'true'
  } else if (entity === 'basedata.city') {
    if (!value || value === '[]') {
      return ''
    }
    const vv = value.replace(/中国\/\s*/g, '')
    return !component.multiple && component.editable ? JSON.stringify([JSON.parse(vv)[0]]) : vv
  } else if (
    isString(value) &&
    isString(entity) &&
    (entity === 'organization.Staff' || entity === 'organization.Department' || entity.startsWith('basedata.Dimension'))
  ) {
    if (!!value && entity === 'organization.Staff') {
      const staff = JSON.parse(value)
      return checkStaffDataRange(component, staff)
    }
    return value ? JSON.parse(value) : undefined
  } else if (entity && entity.includes('datalink.DataLinkEntity') && isString(value)) {
    const dataLinkObj = JSON.parse(value)
    return dataLinkObj && dataLinkObj.id
  }
  return value
}

function checkValueChange(
  prevValue: any,
  nextValue: any,
  type: any,
  template: any,
  key: any,
  checkDefaultValue: boolean,
  entity?: string,
  currencyType?: string
) {
  const field = template.find((v: any) => {
    return key === v.name
  })
  const defaultValue = get(field, 'defaultValue.type')
  if (field && field.editable === true && defaultValue === 'formula') {
    return !checkDefaultValue || Boolean(prevValue)
  }
  if (type === 'text' || type === 'textarea') {
    return prevValue === nextValue
  } else if (type === 'dateRange') {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return next.start === prev.start && next.end === prev.end
  } else if (type === 'boolean') {
    return String(prevValue) === String(nextValue)
  } else if (type === 'number' || type === 'money' || type === 'date') {
    let prev = isObject(prevValue) && type === 'money' ? parseFloat(prevValue.standard) : parseFloat(prevValue)
    let next = isObject(nextValue) && type === 'money' ? parseFloat(nextValue.standard) : parseFloat(nextValue)
    if (type === 'money' && currencyType === 'FOREIGN') {
      prev = isObject(prevValue) && type === 'money' ? parseFloat(prevValue.foreign) : parseFloat(prevValue)
      next = isObject(nextValue) ? parseFloat(nextValue.foreign) : parseFloat(nextValue)
    }
    return (isNaN(prev) && isNaN(next)) || prev === next
  } else if (entity && entity === 'basedata.city') {
    const next = nextValue ? JSON.parse(nextValue) : []
    const prev = prevValue ? JSON.parse(prevValue) : []
    const nextIds = next.map((v: any) => v.key)
    const prevIds = prev.map((v: any) => v.key)
    return !xor(nextIds, prevIds).length
  } else if (type === 'list' && entity === 'organization.Staff') {
    const next = nextValue ? JSON.parse(nextValue) : []
    const prev = prevValue ? prevValue : []
    const nextIds = next.map((v: any) => v.id)
    const prevIds = prev
    return !xor(nextIds, prevIds).length
  } else if ((type === 'ref' && entity === 'organization.Staff') || entity === 'organization.Department') {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return prev.id === next.id
  } else if (type === 'ref' && isString(entity) && entity.startsWith('basedata.Dimension')) {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return prev.id === next.id
  } else if (entity && entity.includes('datalink.DataLinkEntity')) {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return prev.id === next.id
  } else {
    return prevValue === nextValue
  }
}

function checkAttrChange(prevValue: any, nextValue: any, component: any, field: any) {
  const type = get(field, 'dataType.elemType.type') || get(field, 'dataType.type')
  let typeArr = ['attachment', 'text', 'ref']
  if (typeArr.indexOf(type) > -1 && prevValue !== undefined) {
    return String(!prevValue) === String(nextValue)
  }
  return prevValue === nextValue
}

function addArgumentsForCost(billData: any, specification: any) {
  if (!specification) return
  const canPay = specification.configs.find((v: any) => v.ability === 'pay')
  const canWrittenOff = specification.configs.find((v: any) => v.ability === 'writtenOff')
  const canExpense = specification.configs.find((v: any) => v.ability === 'expense')
  const canRequisition = specification.configs.find(
    (v: any) => v.ability === 'requisition' && v.applyContentRule.applyContentRule === 'auto'
  )
  const canReceipt = specification.configs.find((v: any) => v.ability === 'receipt')
  //后台需要匹配口径
  if (canPay) {
    billData.payMoney = billData.payMoney || standardValueMoney(0)
  }
  if (canWrittenOff) {
    billData.writtenOffMoney = billData.writtenOffMoney || standardValueMoney(0)
  }
  if (canExpense) {
    billData.expenseMoney = billData.expenseMoney || standardValueMoney(0)
  }
  if (canRequisition) {
    billData.requisitionMoney = billData.requisitionMoney || standardValueMoney(0)
  }
  if (canReceipt) {
    billData.receiptMoney = billData.receiptMoney || standardValueMoney(0)
  }
}

export function getCustomizeCalParams4Import(billData: any, feeTypeForm: any, billSpecification: any) {
  if (feeTypeForm.feeTypeForm && !feeTypeForm.feeTypeForm.amount) {
    feeTypeForm.feeTypeForm.amount = standardValueMoney(0)
  }
  billData.details = [feeTypeForm]
  const billValue: any = parseShowValue2SaveValue(billData)
  billValue.specificationId = billSpecification.id
  billValue.source = 'detail_'
  addArgumentsForCost(billValue, billSpecification)
  return { submitterId: billValue.submitterId, formData: feeTypeForm, billData: billValue, isCalculateWrite: true }
}

export async function checkCustomizeSubmitValue(
  formData: any,
  bus: any,
  specificationId: string,
  state: string = 'new'
) {
  if (!formData.details || !formData.submitterId) return
  const value = parseShowValue2SaveValue(formData)
  value.specificationId = specificationId
  const params = {
    billData: { ...value, source: 'master_' },
    formData: value,
    submitterId: value.submitterId,
    isCalculateWrite: false
  }
  showLoading()
  const results = await api
    .invokeService('@bill:get:customizeCalculationresult', { form: { ...params.billData, ...params.formData, state } })
    .then((action: any) => {
      const { value } = action.payload
      const items = value?.onFieldValues
      if (!items) {
        return toast.error(action.payloadmsg)
      }
      const hasError = items.filter((v: { errorMsg: any }) => v.errorMsg)
      if (hasError.length > 0) {
        throw { msg: hasError[0].errorMsg }
      }
      return value
    })
    .catch((error: any) => {
      return toast.error(error.msg || error.errorMessage)
    })
  hideLoading()
  if (results) {
    const baseDataPropertiesMap = api.getState()['@common'].baseDataProperties.baseDataPropertiesMap
    const { resultValue, attrValue } = await formatCustomizeCalculateResult(
      bus,
      results?.onFieldValues,
      baseDataPropertiesMap,
      undefined,
      false,
      specificationId,
      true
    )
    return { resultValue, attrValue }
  }
  return undefined
}

export function checkChangedFields(newV: any, oldV: any) {
  if (!newV) return
  const newValue = cloneDeep(newV?.feeTypeForm)
  const oldValue = cloneDeep(oldV?.feeTypeForm)
  newValue.feeTypeId = get(newV, 'feeTypeId.id')
  if (oldValue) {
    oldValue.feeTypeId = get(oldV, 'feeTypeId.id')
    let changedFields: any = []
    Object.keys({ ...newValue, ...oldValue }).forEach(field => {
      JSON.stringify(newValue[field]) != JSON.stringify(oldValue[field]) && changedFields.push(field)
    })
    return changedFields
  } else {
    return Object.keys(newValue)
  }
}

export function checkValueOrIdEqual(value: any, other: any) {
  if (typeof value === 'object' && value?.id) {
    return isEqual(value, other) || value.id === other?.id
  }
  return value === other || isEqual(value, other)
}
