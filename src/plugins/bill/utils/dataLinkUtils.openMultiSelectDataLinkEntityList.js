/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/29 下午12:08.
 */
import { app as api } from '@ekuaibao/whispered'
import get from 'lodash/get'

export function openMultiSelectDataLinkEntityList(data, template, props, flowId, formType, formData) {
  let { entityInfo = {}, value, filterId ,expenseIds, otherFilters} = data
  let { bus, getRuleDataLinkFeetype, specification } = props
  const isString = typeof specification.originalId === 'string'
  const name = get(entityInfo,'name','')
  const type = get(entityInfo,'platformId.type','')
  const isPrivateCar = type === 'PRIVATE_CAR'
  return api
    .open('@bill:MultiSelectDataLinkModal', {
      value: entityInfo,
      id: value && value.id,
      filterId,
      flowId,
      title: isPrivateCar ? i18n.get('行车记录') : name || '行程订单',
      bus,
      expenseIds,
      getRuleDataLinkFeetype,
      specificationId: isString ? specification.originalId : specification.originalId.id,
      formType,
      formData,
      otherFilters
    })
    .then(result => {
      return result
    })
}

export default openMultiSelectDataLinkEntityList
