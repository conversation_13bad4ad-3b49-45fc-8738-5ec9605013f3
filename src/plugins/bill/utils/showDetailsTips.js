import { app } from '@ekuaibao/whispered'
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/8/22 下午7:39.
 */
import { app as api } from '@ekuaibao/whispered'
// import { fpOpenDetail, fnOpenInvoiceCardList } from '../../../elements/puppet/ThirdCard/fnOpenDetails'
const { fpOpenDetail, fnOpenInvoiceCardList } = app.require('@elements/puppet/ThirdCard/fnOpenDetails')
// import { fnPreviewAttachments } from '../../../components/utils/fnAttachment'
const { fnPreviewAttachments } = app.require('@components/utils/fnAttachment')
// import { PopupInvoiceUnify } from '../../../elements/puppet/invoice/PopupInvoiceUnify'
const { PopupInvoiceUnify } = app.require('@elements/puppet/invoice/PopupInvoiceUnify')

const TIPS_TYPE = {
  fp: i18n.get('发票详情'),
  didi: i18n.get('滴滴打车'),
  ekbmall: i18n.get('易快报自营'),
  attachment: i18n.get('附件')
}

export function showDetailsTips(data, submitterId) {
  let { tipsLine, dataSource } = data
  let { feeTypeForm } = dataSource
  let ordersData = feeTypeForm.ordersData
  if (tipsLine.type === 'fp') {
    fpOpenDetail({ data: ordersData[0] }, submitterId)
  } else if (tipsLine.type === 'newfp') {
    fnShowNewFp(tipsLine, submitterId)
  } else if (tipsLine.type === 'attachment') {
    let value = tipsLine.data
    let index = 0
    fnPreviewAttachments({ value, index })
  } else {
    api.open('@feetype:ImportCardsModal', { ordersData, name: TIPS_TYPE[tipsLine.type], submitterId })
  }
}

function fnShowNewFp(tipsLine, submitterId) {
  let {
    data: { attachments, invoices, type, invoiceCorporationId }
  } = tipsLine
  if (type === 'unify') {
    PopupInvoiceUnify(invoiceCorporationId)
  } else if ((attachments && attachments.length > 0) || (invoices && invoices.length > 0)) {
    attachments = attachments ? attachments.map(line => parseAsAttactmentsShowValue(line)) : []
    const data = attachments.concat(invoices || [])
    if (
      data &&
      data.length &&
      data.length === 1 &&
      data[0].invoiceId &&
      data[0].invoiceId.entityId === i18n.get('system_发票主体')
    ) {
      const { invoiceId, itemIds } = data[0]
      const value = { platform: 'fp', type: 'invoice', data: { master: invoiceId, details: itemIds } }
      fpOpenDetail(value, submitterId)
    } else {
      fnOpenInvoiceCardList(data, invoices, submitterId)
    }
  }
}

function parseAsAttactmentsShowValue(value) {
  if (!value) return
  let { name, status, response, fileName, key, fileId } = value
  name = name || fileName
  status = status || 'done'
  response = response || {
    key,
    hash: String(Math.random()),
    ['x:originalname']: name,
    fileId
  }
  return { name, status, response, type: 'invoicePhoto' }
}
