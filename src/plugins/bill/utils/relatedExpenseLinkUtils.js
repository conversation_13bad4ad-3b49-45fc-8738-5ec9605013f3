import { app } from '@ekuaibao/whispered'
import { get, groupBy } from 'lodash'
import { MoneyMath } from '@ekuaibao/money-math'
const { related } = app.require('@components/utils/Related')

export function fnLinkDetailEntitiesValue(detail, map = {}) {
  const { detailId, linkDetailEntities } = detail.feeTypeForm
  if (!linkDetailEntities) return detail
  const dataList = linkDetailEntities.find(item => item.dataList)
  if (dataList) return detail
  const entities = groupBy(linkDetailEntities, 'linkDetailEntityId.linkId')
  const list = Object.keys(entities).map(flowId => {
    const dataList = entities[flowId].map(item => {
      const { linkDetailEntityId, amount } = item
      const { id, unwrittenOffAmount } = linkDetailEntityId
      return {
        ...linkDetailEntityId,
        unwrittenOffAmount: new MoneyMath(unwrittenOffAmount).add(map[id] ?? 0).value,
        modifyValue: amount,
        _tempConsumId: detailId,
      }
    })
    return { flowId, dataList }
  })
  detail.feeTypeForm.linkDetailEntities = list
  return detail
}

export function initRelatedMap(detail) {
  const { detailId, linkDetailEntities } = detail.feeTypeForm
  if (!linkDetailEntities) return
  let dataList0 = []
  let detailIds = ''
  linkDetailEntities?.forEach(line => {
    const dataList = line.dataList ?? []
    const itemList = dataList.map(item => {
      const { id: relateId, _tempConsumId, modifyValue, unwrittenOffAmount } = item
      detailIds = _tempConsumId
      return { consumeAmount: modifyValue || unwrittenOffAmount, relateId }
    })
    dataList0 = dataList0.concat(itemList)
  })
  const id = detailId || detailIds
  related.setRelatedMap({ id: id, value: dataList0 })
}

export function getDetailsRelateMoney(details) {
  const map = {}
  details?.forEach(line => {
    const { linkDetailEntities } = line.feeTypeForm
    linkDetailEntities?.forEach(link => {
      if (!line.dataList) {
        const { amount } = link
        const id = get(link, 'linkDetailEntityId.id')
        if (id) {
          map[id] = new MoneyMath(map[id]).add(amount).value
        }
      } else {
        link.dataList?.forEach(item => {
          const { id, modifyValue, unwrittenOffAmount } = item
          const amount = modifyValue || unwrittenOffAmount
          map[id] = map[id] ? new MoneyMath(map[id]).add(amount).value : amount
        })
      }
    })
  })
  return map
}
