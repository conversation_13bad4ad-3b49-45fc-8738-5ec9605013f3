import { app, app as api } from '@ekuaibao/whispered'
import { Fetch, Resource } from '@ekuaibao/fetch'
import { cloneDeep, get, isString, isBoolean, isArray } from 'lodash'
import { isObject } from '@ekuaibao/money-math/esm/util'
import { getV, isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { Dialog } from '@hose/eui-mobile'
import { toast } from '../../../lib/util'
import moment from 'moment'
import { IDENTIFY_INVOICE_TYPE } from '../../../lib/enums'
import { InvoiceEntityType } from './types'
import { INVOICE_TYPE } from '@ekuaibao/lib/lib/enums'
import {
  batchGetSpecificationHiddenFieldsByIds,
  getDetailFLowRelation,
  getSpecificationHiddenFieldsById
} from '../bill.action'
import { getAllAutoCalResultForBillDiff } from './autoCalResult'
import { fnHideFieldsNote } from '../../../components/utils/fnHideFields'
import qs from 'qs'
import { clearDetailsReceivingAmount, getBillReceivingCurrency } from '../../../plugins/feetype/parts/feeTypeInfoHelper'

const feeTypesVisibility = new Resource('/api/flow/v2/flows/feeType/visibility')
const specifications = new Resource('/api/form/v2/specifications')
import { getBoolVariation } from '../../../lib/featbit'
import { getIfCheckInvoicesMoney } from '../../../lib/featbit/utils'
import { getBackLog_OLD } from '../../approve/approve.action'

// 明细权限获取
export function getFeeTypeVisibleList(param) {
  return feeTypesVisibility.GET('', param)
}

// 删除单据上的批注
export function deleteNoteById(params) {
  return Fetch.DELETE(`/api/credit/v2/notes/$${params.noteId}`, null, { body: { reason: params.reason } }).then(() => {
    setTimeout(() => api.invokeService('@bill:get:bill:notes', { flowId: params.flowId }))
  })
}

export function showConfirmToEdit() {
  return new Promise((resolve, reject) => {
    Dialog.show({
      iconType: 'warn',
      title: i18n.get('此单据有未保存的草稿，是否继续编辑？'),
      closeOnAction: true,
      primarySecondaryActions: [
        {
          key: 'success',
          text: i18n.get('继续编辑'),
          category: 'primary'
        },
        {
          key: 'cancel',
          text: i18n.get('取消'),
          category: 'secondary'
        }
      ],
      onAction: action => {
        resolve(action.key)
      }
    })
  })
}

export function showConfirmToCreate() {
  return new Promise((resolve, reject) => {
    Dialog.show({
      iconType: 'warn',
      title: i18n.get('你上一次编辑的单据还未完成，是否继续编辑？'),
      closeOnAction: true,
      primarySecondaryActions: [
        {
          key: 'success',
          text: i18n.get('继续编辑'),
          category: 'primary'
        },
        {
          key: 'cancel',
          text: i18n.get('新建单据'),
          category: 'secondary'
        }
      ],
      onAction: action => {
        resolve(action.key)
      }
    })
  })
}

export function showModalForFollowWeChat() {
  const KA_LOCAL_CONFIG = api.getState()['@common'].powers.KA_LOCAL_CONFIG
  if (KA_LOCAL_CONFIG) {
    return Fetch.GET('/api/v1/organization/corporations/select').then(resp => {
      const { value: { enableWechatBind = true } = {} } = resp
      if (!enableWechatBind) {
        return null
      }
      return doShowModalForFollowWeChat()
    })
  } else {
    return doShowModalForFollowWeChat()
  }
}

// form表单上面后台返回的一些数据需要在单据保存的时候重新塞回去
export function updateFormData(dataSource, formValue) {
  if (dataSource && formValue) {
    const outerCode = get(dataSource, 'form.outerCode')
    if (outerCode) {
      formValue.outerCode = outerCode
    }
  }
}

// 弹出绑定窗口
export function doShowModalForFollowWeChat() {
  const disablePlatform = ['HUAWEI', 'FEISHU']
  // 判断是否应该弹出绑定窗口
  return Fetch.GET('/api/weixin/v1/qrcode', { tagId: 104 }).then(res => {
    if (res.isBinding) return null
    const userInfo = api.getState()['@common'].me_info.staff
    let isHaveLocalStorage = localStorage.getItem('isBindWeChat:' + userInfo.id)
    // 民生银行测试企业要求隐藏所有展示易快报相关功能
    if (
      !isHaveLocalStorage &&
      !disablePlatform.includes(window.__PLANTFORM__) &&
      Fetch.ekbCorpId !== 'DZgaDKv4Yk8400'
      // && res?.qrCode
    ) {
      // 判断该用户是否首次登陆
      api.open('@bill:FollowWeChatModal', { qrCode: 'data:image/jpg;base64,' + res.qrCode })
    }
  })
}

export function formatNewTemplateValue(value = {}, template = [], oldTemplate) {
  const v = cloneDeep(value)
  const isHongShanTest = isHongShanTestingEnterprise(Fetch.ekbCorpId)
  const writeList = ['expenseLink', 'expenseLinks', 'expenseDate']
  const fieldMap = template?.reduce((result, field) => {
    result[field.field] = field
    return result
  }, {})
  Object.keys(v).forEach(key => {
    const field = fieldMap[key]
    const type = get(field, 'dataType.type', '')
    if (field?.type === 'dataLinkEdits' && key !== 'u_行程规划') {
      // 如果是业务对象写入或修改，则在切换模板的时候从修改切换到写入或者从写入切换到修改需要清空数据
      const value = v[key]
      const { behaviour } = field
      if (
        value?.length &&
        ((value[0].dataLinkId && behaviour !== 'UPDATE') || (!value[0].dataLinkId && behaviour === 'UPDATE'))
      ) {
        delete v[key]
      }
    }

    if (field?.type === 'splitCalculation') {
      delete v[key]
    }

    if (key === 'expenseLink' || key === 'expenseLinks') {
      fnClearExpenseLink(template, v, key, oldTemplate)
    }

    // 切换模板时如果是固定值且有值，把之前的值清除了
    if (
      !!oldTemplate &&
      v[key] &&
      !field?.editable &&
      field?.defaultValue?.type === 'constant' &&
      field?.defaultValue?.value
    ) {
      delete v[key]
    }

    // TODO 天阳(bnB3nHI6Fb3qzw)的切换模板时如果是固定值且有值，把之前的值清除了,不用判断是否是可编辑的
    if (Fetch.ekbCorpId === 'bnB3nHI6Fb3qzw') {
      if (v[key] && field?.defaultValue?.type === 'constant' && field?.defaultValue?.value) {
        delete v[key]
      }
    }

    if (
      !field?.editable &&
      // 单据中的字段为系统计算-无时，不清空字段
      field?.defaultValue?.type !== 'none' &&
      writeList.indexOf(key) < 0 &&
      key !== 'invoiceForm' &&
      type !== 'text' &&
      type !== 'ref'
    ) {
      if (type === 'date' && ['firstSubmit.date', 'submit.date'].includes(field?.defaultValue?.value)) {
        console.log('首次送审日期,数据不删除,还把数据保留', v, key)
      } else {
        delete v[key]
      }
    }

    // 固定值在费用类型切换时，清除当前值，分摊数据不清空
    if (field?.editable === false && writeList.indexOf(key) < 0 && key !== 'invoiceForm' && key !== 'apportions') {
      // 法人实体 不清空
      if (oldTemplate && field?.editable === false && type === 'ref') {
        return
      }
      // 单据模板中的字段为系统计算-无时，不清空字段
      if (oldTemplate && field?.defaultValue?.type === 'none') {
        return
      }
      if (isHongShanTest && field?.defaultValue?.type === 'constant') {
        if (v[key]?.id === field?.defaultValue?.value?.id) {
          return
        }
      }
      if (type === 'date' && ['firstSubmit.date', 'submit.date'].includes(field?.defaultValue?.value)) {
        console.log('首次送审日期,数据不删除,还把数据保留', v, key)
        return
      }
      v[key] = undefined
    }
  })
  const invoiceFormField = template.find(el => el.name === 'invoiceForm')
  if (invoiceFormField && !v[invoiceFormField.name]) {
    v[invoiceFormField.name] = invoiceFormField.editable
      ? { type: invoiceFormField.invoiceType?.defaultInvoiceType || 'noWrite' }
      : { type: invoiceFormField.defaultValue?.value || 'noWrite' }
  }
  return v
}

function fnClearExpenseLink(template, v, key, oldTemplate) {
  const expenseLink = template?.find(el => ['expenseLink', 'expenseLinks'].includes(el.field))
  const expenseLinkFlag = key !== get(expenseLink, 'field')
  const oldExpenseLink = oldTemplate?.components?.find(el => ['expenseLink', 'expenseLinks'].includes(el.field))
  const expenseLinksFlag = get(oldExpenseLink, 'allowMultipleRequests') !== get(expenseLink, 'allowMultipleRequests')

  if (expenseLinkFlag || (expenseLinksFlag && v[key] && v[key].length > 1)) {
    const { related } = app.require('@components/utils/Related')
    v.details?.forEach(line => {
      if (line.feeTypeForm.linkDetailEntities) {
        delete line.feeTypeForm.linkDetailEntities
      }
    })
    delete v[key]
    related.clearRelateMap()
  }
}

export function formatFeetypeValuesForInvoice(value = {}, template = []) {
  const invoices = get(value, 'invoiceForm.invoices', [])
  const attachments = get(value, 'invoiceForm.attachments', [])
  const field = template.find(f => f.field === 'invoiceForm') || {}
  if (value && value.apportions) {
    const apportionsField = template.find(item => item.field === 'apportions')
    if (apportionsField) {
      let specificationId = value.apportions[0] && value.apportions[0].specificationId.originalId
      let isSame = apportionsField.specificationIds.find(oo => oo === specificationId)
      if (!isSame) {
        delete value['apportions']
      }
    }
  }
  const refs = template.filter(f => get(f, 'dataType.type', '') === 'ref' && !f.editable)
  refs.forEach(ref => delete value[ref.field])
  //如果是滴滴统一开票的话切换消费记录值不变
  let { ordersData } = value

  if (ordersData && ordersData.find(item => item.platform === 'DIDI' && item.form.useCarType !== 'TAXI')) {
    if (
      value &&
      value.invoiceForm.type === 'unify' &&
      value.invoiceForm.invoiceCorporationId &&
      value.invoiceForm.invoiceCorporationId.channel === 'DIDI'
    ) {
      if (!field.editable) {
        return value
      } else {
        const hasUnify = field.invoiceType.unify && field.invoiceType.unify.choose
        if (!hasUnify) {
          delete value['invoiceForm']
          return value
        } else {
          const unifyList = api.getState()['@invoice-form'].unifyList || []
          const didiUnfiy = unifyList.find(line => line.active && line.channel === 'DIDI')
          const invoiceCorpIds = get(field, 'invoiceType.unify.invoiceCorporation')
          const limit = get(field, 'invoiceType.unify.limit')
          if (didiUnfiy && (!limit || invoiceCorpIds.indexOf(didiUnfiy.id) >= 0)) {
            return value
          } else {
            value['invoiceForm'] = {
              type: 'unify',
              invoiceCorporationId:
                invoiceCorpIds.length === 0 ? unifyList[0] : unifyList.find(item => item.id === invoiceCorpIds[0])
            }
            return value
          }
        }
      }
    } else {
      delete value['invoiceForm']
      return value
    }
  }

  if (invoices.length === 0 && attachments.length === 0) {
    delete value['invoiceForm']
    return value
  }

  if (!get(field, 'editable') && get(field, 'defaultValue.value') === 'noWrite') {
    // 无需添加
    return value
  }
  if (!get(field, 'editable') || !get(field, 'invoiceType.exist')) {
    delete value['invoiceForm']
  }
  return value
}

export function canChangeTemplateFn(dataSource) {
  return get(dataSource, 'plan.submitNode.nextId', 'SUBMIT') === 'SUBMIT'
}

export function parseValue({ newTemplate, value }) {
  return new Promise((resolve, reject) => {
    if (!newTemplate || !value) reject()
    resolve(value)
  })
}

/**
 * @desc 切换模板对关联申请的权限控制
 * @param {*} value
 * @param {*} newT
 * @returns
 */
export const formatExpenseLinkInfo = (value, newT) => {
  const { expenseLink, expenseLinks } = value
  const newRequisitionList = (newT?.configs || []).find(it => it.ability === 'apply')?.requisitionList?.ids || []
  if (expenseLink) {
    const expenseLinkTemId = expenseLink.specificationId?.originalId
    if (checkExpenseLinksDelete(newRequisitionList, expenseLinkTemId)) {
      delete value.expenseLink
    }
  }
  if (expenseLinks) {
    const expenseLinkTemId = expenseLinks[0]?.specificationId?.originalId
    if (checkExpenseLinksDelete(newRequisitionList, expenseLinkTemId)) {
      delete value.expenseLinks
    }
  }
  return value
}

const checkExpenseLinksDelete = (newRequisitionList, expenseLinkTemId) => {
  return newRequisitionList.length && !newRequisitionList.includes(expenseLinkTemId)
}

export async function checkValue({ oldTemplate, newTemplate, value }) {
  let v = await parseValue({ newTemplate, value })
    v = await checkTempKeepCurrencyEqual({ oldTemplate, newTemplate, value: v })
    v = await isReceivingAmountEquel({ oldTemplate, newTemplate, value })
  return v
}


export function checkTempKeepCurrencyEqual({ oldTemplate, newTemplate, value }) {
  return new Promise(async (resolve, reject) => {
    const OTMustAllFeeTypeCurrencyEqual = getMustAllFeeTypeCurrencyEqual(oldTemplate)
    const NTMustAllFeeTypeCurrencyEqual = getMustAllFeeTypeCurrencyEqual(newTemplate)
    if (OTMustAllFeeTypeCurrencyEqual === NTMustAllFeeTypeCurrencyEqual) return resolve(value)
    const details = getV(value, 'details', [])
    if (!details.length || isCurrencyEquel(details)) return resolve(value)
    if (NTMustAllFeeTypeCurrencyEqual) {
      Dialog.confirm({
        title: i18n.get('即将移除所有费用明细'),
        content: i18n.get(
          '新模板要求所有费用币种一致。当前单据存在不同币种的费用。切换到新的单据模板将会导致所有费用被移除。'
        ),
        cancelText: i18n.get('取消'),
        onCancel: () => reject(),
        confirmText: i18n.get('移除'),
        onConfirm: () => {
          delete value.details
          resolve(value)
        }
      })
    } else {
      resolve(value)
    }
  })
}

async function isReceivingAmountEquel({ oldTemplate, newTemplate, value }){
  let isChange = false
  const { allowSelectionReceivingCurrency: oldAllow,currencyRange: oldCurrencyRange } = oldTemplate?.configs?.find(v => v.ability === 'pay') || {}
  const { allowSelectionReceivingCurrency: newAllow, currencyRange: newCurrencyRange } = newTemplate?.configs?.find(v => v.ability === 'pay') || {}
  const receivingCurrency = getBillReceivingCurrency(value)
  const currencyEquel = arraysStringAreEqual(oldCurrencyRange, newCurrencyRange)

  return new Promise(async (resolve, reject) => {
    // 没有收款币种的话清除receivingAmount属性
    if ((newAllow && !currencyEquel && value?.details?.length) || (oldAllow && !newAllow && value?.details?.length)) {
      isChange = true // 范围变更
    }
    if(!newAllow && receivingCurrency){
      delete value.receivingCurrency
      delete value.payeeId?.receivingCurrency
    }
    if (isChange) {
      Dialog.confirm({
        title: i18n.get('提示'),
        content: i18n.get('新模板的收款币种配置不一致，切换到新的单据模板收款金额会清空，更新后请检查费用明细。'),
        cancelText: i18n.get('取消'),
        onCancel: () => reject(),
        confirmText: i18n.get('我知道了'),
        onConfirm: () => {
          value.details = clearDetailsReceivingAmount(value.details)
          resolve(value)
        }
      })
    } else {
      resolve(value)
    }
  })
}

function arraysStringAreEqual(arr1, arr2) {
  if (isArray(arr1) && isArray(arr2)) {
    if (arr1.length !== arr2.length) return false
    return arr1.every(item => arr2.includes(item))
  }
 return false
}

function isCurrencyEquel(details) {
  let result = true
  let beforeCode = ''
  for (let i = 0; i < details.length; i++) {
    const detail = details[i]
    const foreignNumCode = getV(detail, 'feeTypeForm.amount.foreignNumCode', '')
    if (i !== 0) {
      result = foreignNumCode === beforeCode
    }
    if (!result) {
      break
    }
    beforeCode = foreignNumCode
  }
  return result
}

function getMustAllFeeTypeCurrencyEqual(template) {
  const components = getV(template, 'components', [])
  const detailComponent = components.find(c => c.type === 'details')
  return getBoolVariation('fkrd-5393-mustAllFeeTypeCurrencyEqual-true') || getV(detailComponent, 'mustAllFeeTypeCurrencyEqual', false)
}

export function getValidateErrorByShow(components = [], errKeys = []) {
  return components.filter(v => !v.hide && errKeys.includes(v.field))
}

export function showSensitiveContent(dataSource, userId) {
  const owner = get(dataSource, 'ownerId')
  const submitter = get(dataSource, 'form.submitterId')

  const ownerId = typeof owner === 'object' ? owner.id : owner
  if (ownerId === userId) return true

  const submitterId = typeof submitter === 'object' ? submitter.id : submitter
  if (submitterId === userId) return true

  const nodes = get(dataSource, 'plan.nodes') || []
  let show = false
  for (let node of nodes) {
    const { counterSigners, approverId } = node
    if (approverId && approverId.id === userId) {
      show = true
      break
    }
    if (counterSigners && counterSigners.length > 0) {
      const signer = counterSigners.find(item => item.signerId.id === userId)
      if (signer) {
        show = true
        break
      }
    }
  }
  return show
}

export const STATE_LIST = ['paid', 'archived', 'rejected'] //这些状态下不显示敏感信息

export function getInvoiceIdsByFeeTypeForm(feeTypeForm = {}) {
  const invoices = getV(feeTypeForm, 'invoiceForm.invoices', [])
  return invoices.map(line => {
    const detailIds = line.itemIds ? line.itemIds.map(item => item.id) : []
    return { masterId: get(line, 'invoiceId.id'), detailIds }
  })
}

// 校验借款包是否存在，是否可以被单据关联
export function checkLoanPackage(writtenOff, submitterId) {
  if (!writtenOff || !writtenOff.length) return Promise.resolve()
  let loanIds = writtenOff.map(item => {
    return item.loanInfoId
  })
  return new Promise((resolve, reject) => {
    api.invokeService('@bill:check:loan:package:exist', loanIds, submitterId).then(res => {
      const { items } = res
      if (items && items.length) {
        let str = ''
        Object.keys(items[0]).forEach(a => {
          str += items[0][a] + i18n.get('；')
        })
        str = str.substring(0, str.length - 1)
        // toast.error(str)
        reject(str)
      } else {
        resolve()
      }
    })
  })
}

// 多收款人埋点
export function mutiPayTrack(data) {
  const { staff = {} } = api.getState()['@common'].me_info
  const payPlanMode = get(data, 'form.payPlanMode')
  const payPlan = get(data, 'form.payPlan', [])
  const payMoney = get(data, 'form.payMoney.standard')
  if (payPlanMode) {
    let amount = ''
    payPlan.forEach(el => {
      //@i18n-ignore
      amount += get(el, 'dataLinkForm.E_system_支付计划_支付金额.standard', 0) + '/'
    })
    //神策埋点
    api.track('PaymentPlan', {
      staffId: staff.id,
      PlanCount: payPlan.length,
      TotalAmount: payMoney,
      PlanAmount: amount
    })
  } else {
    const details = get(data, 'form.details', [])
    let payByMutiDetails
    if (details.length > 0) {
      payByMutiDetails = !!details[0].feeTypeForm.feeDetailPayeeId
      if (payByMutiDetails) {
        let amount = ''
        details.forEach(el => {
          amount += get(el, 'feeTypeForm.amount.standard', 0) + '/'
        })
        //神策埋点
        api.track('PaymentDetail', {
          staffId: staff.id,
          DetailCount: details.length,
          TotalAmount: payMoney,
          DetailAmount: amount
        })
      }
    }
  }
}

export function fnFilterFeetypes(feeTypes = []) {
  let feeIds = []
  let getChildren = ids => {
    ids.forEach(item => {
      let id = typeof item === 'string' ? item : item.id
      feeIds.push(id)
      if (item.children && item.children.length) {
        getChildren(item.children)
      }
    })
  }
  getChildren(feeTypes)
  return feeIds
}

export function logInfo(info) {
  const isAssistPlatform = Fetch.isAssistPlatform
  if (isAssistPlatform) {
    Fetch.POST('/api/assistPlatform/log/save', null, { body: { content: info } })
  }
}

// 酬金明细
export function checkIsRemuneration(specificationId) {
  const remunerationSetting = api.getState()['@home'].remunerationSetting
  if (specificationId?.split(':')?.length) {
    const originalId = specificationId?.split(':')[0]
    return remunerationSetting && remunerationSetting.specificationId === originalId
  }
  return false
}

export function fixRemunerationSpecification(specification) {
  const components = specification?.components
  let detailsConfig = components && components.find(c => c.field === 'details')
  if (detailsConfig?.hide) {
    detailsConfig.hide = false
  }
}

export function mergeBillFormValue(oldFormValue, newFormValue, whiteListMap) {
  if (oldFormValue) {
    Object.keys(oldFormValue)?.forEach(key => {
      if (key === 'payeeId' && newFormValue[key]?.receivingCurrency) {
        oldFormValue[key] = newFormValue[key]
      }
      if ((!oldFormValue[key] || key === 'invoiceForm') && !whiteListMap[key]) {
        oldFormValue[key] = newFormValue[key]
      }
    })
  }
  return oldFormValue
}

export function mergeValue(value, formValue, template, isModifyBill) {
  const temp = {}
  Object.keys(value).forEach(key => {
    const fKey = formValue?.[key]
    const vKey = value?.[key]
    const currentTemplate = template.find(item => item?.field === key)
    // formValue档案依赖返回数据字符串形式的value不正确
    if (currentTemplate?.dependence?.length && ((isString(fKey) && isObject(vKey)) || (isString(fKey) && !vKey))) {
      temp[key] = value?.[key]
    } else {
      if (isModifyBill) {
        temp[key] = value?.[key] ?? formValue?.[key]
        if (key === 'payeeId' && formValue[key]?.receivingCurrency) {
          temp[key].receivingCurrency = formValue[key].receivingCurrency
        }
      } else {
        temp[key] = formValue?.[key] ?? value?.[key]
      }
    }
  })

  formValue &&
    Object.keys(formValue).forEach(key => {
      const fKey = formValue?.[key]
      const vKey = value?.[key]
      const currentTemplate = template.find(item => item?.field === key)
      if (currentTemplate?.dependence?.length && ((isString(fKey) && isObject(vKey)) || (isString(fKey) && !vKey))) {
        temp[key] = value?.[key]
      } else if (isBoolean(fKey)) {
        temp[key] = formValue?.[key]
      } else {
        if (isModifyBill) {
          temp[key] = value?.[key] ?? formValue?.[key]
        } else {
          temp[key] = formValue?.[key] ?? value?.[key]
        }
      }
    })

  return temp
}

export const formatDate = (value, format) => {
  if (!value) {
    return value
  }
  return moment(value).format(format)
}

// 费用类型的差异
export const getDiffsBetweenFeeTypes = (curDetails, prevDetails) => {
  const curFeeDetailMap = curDetails.reduce(
    (result, item) =>
      Object.assign(result, item?.feeTypeForm?.detailId ? { [item?.feeTypeForm?.detailId]: item } : undefined),
    {}
  )
  const prevFeeDetailMap = prevDetails.reduce(
    (result, item) =>
      Object.assign(result, item?.feeTypeForm?.detailId ? { [item?.feeTypeForm?.detailId]: item } : undefined),
    {}
  )
  const curRes = curDetails.map((item, index) => {
    const id = item?.feeTypeForm?.detailId
    const prev = prevFeeDetailMap[id]
    // 如果之前不存在的记录，那就是新增的
    if (!prev) {
      return {
        type: 'add',
        name: item?.feeTypeId?.fullname,
        time: item?.feeTypeId?.createTime,
        amount: item?.feeTypeForm?.amount,
        subText: [formatDate(item?.feeTypeForm?.feeDate, 'YYYY年MM月DD日')],
        text: []
      }
    } else {
      // 假如价格相同，则视为没有变更
      if (prev?.feeTypeForm?.amount.standard == item?.feeTypeForm?.amount.standard) {
        return null
      }
      return {
        type: 'change',
        name: item?.feeTypeId?.fullname,
        time: item?.feeTypeId?.createTime,
        amount: item?.feeTypeForm?.amount,
        subText: [formatDate(item?.feeTypeForm?.feeDate, 'YYYY年MM月DD日')],
        text: [],
        before: {
          name: prev?.feeTypeId?.fullname,
          time: prev?.feeTypeId?.createTime,
          amount: prev?.feeTypeForm?.amount,
          subText: [formatDate(prev?.feeTypeForm?.feeDate, 'YYYY年MM月DD日')],
          text: []
        }
      }
    }
  })

  const prevRes = prevDetails.map(item => {
    const id = item?.feeTypeForm?.detailId
    const cur = curFeeDetailMap[id]
    // 如果是之前存在现在不存在的记录，那就是删除的
    if (!cur) {
      return {
        type: 'deled',
        name: item?.feeTypeId?.fullname,
        time: item?.feeTypeId?.createTime,
        amount: item?.feeTypeForm?.amount,
        subText: [formatDate(item?.feeTypeForm?.feeDate, 'YYYY年MM月DD日')],
        text: []
      }
    } else {
      return null
    }
  })
  return [...curRes, ...prevRes].filter(Boolean)
}

const formatInvoice = item => {
  const fileKey = item?.key
  if (fileKey) {
    return {
      name: item.fileName,
      time: null,
      thumbUrl: item.fileId?.thumbUrl || item.fileId?.url,
      url: item.fileId?.url || item.fileId?.thumbUrl
    }
  }
  const result = formatInvoiceInfoData(item)
  return {
    ...result,
    amountLabel: '税价合计：',
    subText: [result.date, result.code && '发票号码：' + result.code, '发票类型：' + result.typeName]
  }
}

/**
 * 转换发票数据
 * @param item
 * @returns {import('./types').InvoiceSimpleData}
 */
export const formatInvoiceInfoData = item => {
  const entityId = item?.invoiceId?.entityId
  const value = item?.invoiceId
  const mapItem = {
    [InvoiceEntityType.增值税发票]: {
      name: 'E_system_发票主体_销售方名称',
      date: 'E_system_发票主体_发票日期',
      code: 'E_system_发票主体_发票号码',
      type: 'E_system_发票主体_发票类别',
      amount: 'E_system_发票主体_价税合计'
    },
    [InvoiceEntityType.出租车票]: {
      name: `出租车发票(${value?.form?.E_system_出租车票_发票所在地})`,
      date: 'E_system_出租车票_上车时间',
      code: 'E_system_出租车票_发票号码',
      type: '出租车票',
      amount: 'E_system_出租车票_金额'
    },
    [InvoiceEntityType.过路费发票]: {
      name: `过路费发票(${value?.form?.E_system_过路费发票_入口} - ${value?.form?.E_system_过路费发票_出口})`,
      date: 'E_system_过路费发票_时间',
      code: 'E_system_过路费发票_发票号码',
      type: '过路费发票',
      amount: 'E_system_过路费发票_金额'
    },
    [InvoiceEntityType.定额发票]: {
      name: '定额发票',
      code: 'E_system_定额发票_号码',
      type: '定额发票',
      amount: 'E_system_定额发票_金额'
    },
    [InvoiceEntityType.铁路客票]: {
      name: `${value?.form?.E_system_火车票_上车车站}--${value?.form?.E_system_火车票_下车车站}`,
      code: 'E_system_火车票_号码', // 没有发票代码
      type: '铁路客票',
      date: 'E_system_火车票_乘车时间',
      amount: 'E_system_火车票_金额'
    },
    [InvoiceEntityType.航空运输电子客票行程单]: {
      name: `${value?.form?.E_system_航空运输电子客票行程单_出发站}--${value?.form?.E_system_航空运输电子客票行程单_到达站}`,
      code: 'E_system_航空运输电子客票行程单_电子客票号码', // 没有发票代码
      type: '机票行程单',
      date: 'E_system_航空运输电子客票行程单_乘机时间',
      amount: 'E_system_航空运输电子客票行程单_金额'
    },
    [InvoiceEntityType.客运汽车发票]: {
      name: `客运汽车发票（${value?.form?.E_system_客运汽车发票_出发车站}--${value?.form?.E_system_客运汽车发票_达到车站}）`,
      code: 'E_system_客运汽车发票_发票号码',
      type: '客运汽车票',
      date: 'E_system_客运汽车发票_时间',
      amount: 'E_system_客运汽车发票_金额'
    },
    [InvoiceEntityType.其他发票]: {
      name: '其他票据',
      date: 'E_system_其他_日期',
      type: '其他票据',
      amount: 'E_system_其他_金额'
    },
    [InvoiceEntityType.医疗发票]: {
      name: '医疗发票',
      date: 'E_system_医疗发票_开票日期',
      type: '医疗发票',
      code: 'E_system_医疗发票_票据号码',
      amount: 'E_system_医疗发票_金额合计'
    },
    [InvoiceEntityType.财政票据]: {
      name: '财政票据',
      date: 'E_system_非税收入类票据_开票日期',
      type: '财政票据',
      code: 'E_system_非税收入类票据_票据号码',
      amount: 'E_system_非税收入类票据_金额合计'
    },
    [InvoiceEntityType.消费小票]: {
      name: value?.form?.E_system_消费小票_店名 || '无法识别店名',
      type: '消费小票',
      date: 'E_system_消费小票_时间',
      amount: 'E_system_消费小票_金额'
    },
    [InvoiceEntityType.机打发票]: {
      name: 'E_system_机打发票_销售方名称',
      code: 'E_system_机打发票_发票号码',
      type: '机打发票',
      date: 'E_system_机打发票_时间',
      amount: 'E_system_机打发票_金额'
    }
  }
  const oo = mapItem[entityId]
  const invoiceType = value?.form?.[oo.type] || oo.type
  let invoiceTypeText = INVOICE_TYPE()?.[invoiceType] ?? IDENTIFY_INVOICE_TYPE?.()?.[invoiceType] ?? invoiceType

  if (invoiceTypeText === 'E_system_发票主体_发票类别') {
    invoiceTypeText = i18n.get('增值税发票')
  }
  return {
    name: value?.form?.[oo.name] || oo.name,
    time: value?.createTime,
    entityId,
    type: invoiceType,
    typeName: invoiceTypeText,
    code: value?.form?.[oo.code],
    amount: value?.form?.[oo.amount],
    date: oo.date ? formatDate(value?.form?.[oo.date], 'YYYY年MM月DD日') : undefined
  }
}

export const getDiffsBetweenInvoices = (curDetails, prevDetails) => {
  let curInvoices = []
  let preInvoices = []
  curDetails.map(item => {
    const invoices = getV(item, 'feeTypeForm.invoiceForm.invoices', [])
    const attachments = getV(item, 'feeTypeForm.invoiceForm.attachments', [])
    curInvoices = curInvoices.concat(invoices).concat(attachments)
  })
  prevDetails.map(item => {
    const invoices = getV(item, 'feeTypeForm.invoiceForm.invoices', [])
    const attachments = getV(item, 'feeTypeForm.invoiceForm.attachments', [])
    preInvoices = preInvoices.concat(invoices).concat(attachments)
  })
  console.log(curInvoices, preInvoices)
  const curRes = curInvoices.map((item, index) => {
    const fileKey = item?.key // 发票照片
    // 发票id + 更新时间 作为唯一
    const id = item?.invoiceId?.id
    const updateTime = item?.invoiceId?.updateTime
    const entityId = item?.invoiceId?.entityId
    const prev = preInvoices.find(item => {
      if (fileKey) {
        return item?.key == fileKey
      } else {
        return item?.invoiceId?.id == id && item?.invoiceId?.updateTime == updateTime
      }
    })
    // 如果是之前不存在的则是新增的
    if (!prev) {
      return {
        ...formatInvoice(item),
        type: 'add'
      }
    } else {
      // 假如价格相同，则视为没有变更
      if (
        prev?.invoiceId?.form?.['E_' + entityId + '_发票号码'] == item?.invoiceId?.form?.['E_' + entityId + '_发票号码']
      ) {
        return null
      }
      return {
        ...formatInvoice(item),
        before: formatInvoice(prev),
        type: 'change'
      }
    }
  })
  const prevRes = preInvoices.map((item, index) => {
    const fileKey = item?.key // 发票照片
    // 发票id + 更新时间 作为唯一
    const id = item?.invoiceId?.id
    const updateTime = item?.invoiceId?.updateTime
    const cur = curInvoices.find(item => {
      if (fileKey) {
        return item?.key == fileKey
      } else {
        return item?.invoiceId?.id == id && item?.invoiceId?.updateTime == updateTime
      }
    })
    // 如果现在不存在，则是删除的
    if (!cur) {
      return {
        ...formatInvoice(item),
        type: 'deled'
      }
    } else {
      return null
    }
  })
  return [...curRes, ...prevRes].filter(Boolean)
}

export const getDiffsBetweenVersions = (type, curVersion, prevVersion) => {
  const curDetails = curVersion.value.form.details.reverse()
  const prevDetails = prevVersion.value.form.details
  switch (type) {
    case 'documentType':
      return getDiffsBetweenFeeTypes(curDetails, prevDetails)
    case 'invoice':
      return getDiffsBetweenInvoices(curDetails, prevDetails)
  }
}

/**
 * 判断单据是否使用了快速报销
 */
export const checkQuickExpends = async flowId => {
  if (flowId) {
    // 查询单据明细是否是快速报销明细
    getDetailFLowRelation(flowId).then(async res => {
      const filterObj = api.getState()['@home'].expenseSpecAfterFiltered
      const extendType = res?.value === 'QUICK_EXPENSE' ? 'QuickExpense' : 'Other'
      await api.invokeService('@home:save:specification:after:filtered', { ...filterObj, extendType })
    })
  }
}

/**
 * 根据发票使用规则和单据风险判断提交送审时是否需要填写风险原因
 * @param id 单据id
 * @returns {Promise<T_RiskWarningContentItem[]>}
 */
export const getNeedSubmitRiskReasonList = async flowId => {
  const result = await Promise.all([
    api.invokeService('@invoice:get:invoice:rule:list'),
    api.invokeService('@common:get:riskwarningById', { flowId, reasonModify: true })
  ])
  const [invoiceRuleList, riskWarning] = result
  const needSubmitRiskReasonRuleList = invoiceRuleList?.items?.filter(
    item => item.control === 'ALLOW_SUBMIT_SHOW_RISK_EXPLAIN'
  )
  const needSubmitRiskReasonRuleMap = {}
  needSubmitRiskReasonRuleList.forEach(item => {
    needSubmitRiskReasonRuleMap[item.id] = item
  })
  const invoiceNormIdMap = {}
  const needSubmitRiskReasonList = riskWarning?.singleInvoiceRiskWarning?.reduce((result, curRiskWarning) => {
    const { invoiceId, invoiceMsg, relatedFlows, riskWarningReason = [] } = curRiskWarning
    const needSubmitRiskReasonListFilter = riskWarningReason.filter(
      reasonItem => !!needSubmitRiskReasonRuleMap[reasonItem.invoiceNormId]
    )
    needSubmitRiskReasonListFilter.forEach(item => {
      const { invoiceNormId, invoiceNormDesc, invoiceRiskExplainId, invoiceRiskExplainContent } = item
      if (invoiceNormIdMap[invoiceNormId] !== undefined) {
        result[invoiceNormIdMap[invoiceNormId]]?.riskWarningContent.push({
          invoiceNum: invoiceId,
          invoiceMsg,
          invoiceRiskExplainContent,
          relatedFlows: relatedFlows ? relatedFlows[invoiceNormDesc] : []
        })
      } else {
        invoiceNormIdMap[invoiceNormId] = result.length
        result.push({
          invoiceNormId,
          invoiceNormDesc,
          riskWarningContent: [
            {
              invoiceNum: invoiceId,
              invoiceMsg,
              invoiceRiskExplainId,
              invoiceRiskExplainContent,
              relatedFlows: relatedFlows ? relatedFlows[invoiceNormDesc] : []
            }
          ]
        })
      }
    })
    return result
  }, [])
  return needSubmitRiskReasonList
}

/**
 * 为 BillVersionDiffModal 展示发票风险组装数据
 * @params 通过 @common:get:riskwarningById 获取的原始的风险数据
 * @returns 组装好的备份数据
 */
export const getRiskReasonDataForVersionDiffModal = riskData => {
  if (!riskData || !riskData.value) return riskData
  const riskDataClone = cloneDeep(riskData)
  const { singleInvoiceRiskWarning = [], value } = riskDataClone
  const riskReasonArr = []
  singleInvoiceRiskWarning?.forEach(riskWarningItem => {
    const { invoiceMsg, riskWarningReason } = riskWarningItem
    if (riskWarningReason?.length) {
      riskWarningReason.forEach(reasonItem => {
        const { invoiceNormDesc, invoiceRiskExplainContent } = reasonItem
        if (invoiceRiskExplainContent) {
          riskReasonArr.push({
            invoiceMsg,
            invoiceNormDesc,
            invoiceRiskExplainContent
          })
        }
      })
    }
  })
  value?.riskWarning?.forEach(riskWarningItem => {
    const { controlName, messages = [] } = riskWarningItem
    riskReasonArr
      .filter(item => `${item.invoiceNormDesc}：` === controlName)
      .forEach(item => {
        let index = messages.findIndex(message => item.invoiceMsg === message)
        if (index !== -1) {
          messages.splice(index + 1, 0, i18n.get('原因：{_k0}', { _k0: item.invoiceRiskExplainContent }))
        }
      })
  })
  return riskDataClone
}

// 检查分摊其它配置
export function fnCheckApportionOtherConfig(apportions) {
  if (apportions?.length) {
    const configs = apportions[0]?.specificationId?.configs
    const otherConfig = configs?.find(item => item.ability === 'apportionOtherConfig')
    const { apportionDetailsAtLeastTwo, apportionSingleItemNotEqualOne } = otherConfig || {}
    if (apportionDetailsAtLeastTwo && apportions.length < 2) {
      return i18n.get('分摊明细数量至少2条')
    } else if (apportionSingleItemNotEqualOne) {
      const hasOne = apportions.some(v => {
        const { apportionPercent } = v.apportionForm
        return apportionPercent == 100
      })
      if (hasOne) {
        return i18n.get('单条分摊比例不可等于100%')
      }
    }
  }
}

// 找到隐藏分摊的明细
export async function getApportionUnVisibleCount(billData, selectedData) {
  if (!selectedData?.length) return false
  // 复用小组件getAllAutoCalResultForBillDiff方法，查询整张单据的自动计算结果
  const data = await getAllAutoCalResultForBillDiff({ ...billData, details: selectedData })
  const apportionItem = data?.items?.filter(item => !!item.detailId && item.onField === 'apportions' && item.attribute)
  const result = selectedData.map(detail => {
    const apportionsField = detail?.specificationId?.components?.find(comp => comp.field === 'apportions') || {}
    // 没有配置隐藏
    if (!apportionsField.hide) {
      return
    }
    const hideAttr = apportionItem.find(
      item => item.detailId === detail?.feeTypeForm?.detailId && item.attribute === 'hide'
    )
    // 配置隐藏，但是隐藏公式 false时
    if (hideAttr && hideAttr.result === 'false') {
      return
    }
    // 配置隐藏，但是白名单
    if (apportionsField.hideVisibility && fnHideFieldsNote(apportionsField.hideVisibility)) {
      return
    }
    // 始终开启分摊时 不隐藏
    if (apportionsField.open) {
      const openItem = apportionItem.find(
        item => item.detailId === detail?.feeTypeForm?.detailId && item.attribute === 'open'
      )
      if (!openItem || openItem.result === 'true') {
        return
      }
    }
    return detail
  })
  return result.filter(v => v).length
}
export const fnShareBillInfoFEISHU = async flowId => {
  const params = {
    flowId,
    sharingPlatform: 'FEI_SHU' // 分享平台 DT,APP,KdCloud,QYWX
  }
  const shareResult = await Fetch.POST('/api/flow/v1/sharing/getForwardingUrl', {}, { body: params })
  if (!shareResult?.value) {
    showMessage.success(i18n.get('链接失效'))
  }

  api?.sdk?.share({
    title: shareResult.value?.title || i18n.get('合思费控报销'),
    url: shareResult.value?.url
  })
}

/**
 * 转发企业微信
 * @param {*} flowId
 */
export const fnShareBillInfoQW = async flowId => {
  const params = {
    flowId,
    sharingPlatform: 'QY_WX' //分享平台，默认钉钉，可以不传 DT,APP,KdCloud,QYWX
  }
  const shareResult = await Fetch.POST('/api/flow/v1/sharing/getForwardingUrl', {}, { body: params })
  if (!shareResult?.value) {
    showMessage.success(i18n.get('链接失效'))
  }
  api?.sdk?.onMenuShareAppMessage({
    title: shareResult.value?.title || i18n.get('合思费控报销'), // i18n.get("合思费控报销"), // 分享标题
    link: shareResult.value?.url // 分享链接；在微信上分享时，该链接的域名必须与企业某个应用的可信域名一致
  })
}

/**
 * 单据信息支持分享到钉钉个人对话框或钉钉群
 */
export const fnShareBillInfoDT = flowId => {
  api?.sdk?.pickConversation({
    corpId: Fetch.corpId, // 钉钉中的企业id（ding******）
    isConfirm: true, // 选择会话时是否需要弹出确认弹窗
    onSuccess: async res => {
      const params = {
        flowId,
        sharingPlatform: 'DT', //分享平台，默认钉钉，可以不传 DT,APP,KdCloud,QYWX
        conversationId: res?.cid, //会话id
        deviceType: 'MOBILE' // 设备类型： 默认DESKTOP-桌面端，MOBILE-移动端
      }
      const shareResult = await Fetch.POST('/api/flow/v1/sharing', {}, { body: params })
      if (shareResult?.value) {
        toast.success(i18n.get('已分享到钉钉会话'))
      }
    },
    onFail: err => console.log(err)
  })
}

export const fnShareBillInfo = flowId => {
  const params = qs.parse(location.search.slice(1))
  if (!!params?.sdkName?.length) {
    api?.sdk?.share({
      flowId
    })
    return
  }
  if (window.isDingtalk) {
    fnShareBillInfoDT(flowId)
  } else if (window.isWxWork) {
    fnShareBillInfoQW(flowId)
  } else if (window.isFeishu) {
    fnShareBillInfoFEISHU(flowId)
  }
}

export function updateDetailIndex(bill) {
  const { details } = bill
  if (details && details.length) {
    details.forEach((v, idx) => {
      v.idx = idx
    })
  }
  return bill
}

export function checkCSCMoney(feeTypeForm, item, sumUseBalance) {
  const standard = get(feeTypeForm, `${item.field}.standard`, '')
  const standardScale = get(feeTypeForm, `${item.field}.standardScale`)
  const standardNumber = Number(standard)
  const sumUseBalanceNumber = Number(sumUseBalance?.toFixed(standardScale))
  // 公务卡金额范围：
  // 手动计算字段：正数：0.01<= 金额 <=公务卡订单可用金额  负数：公务卡订单可用金额<= 金额 <=-0.01, 0: 公务卡订单可用金额总和为0（正加负）
  // 自动计算字段：金额 ===公务卡订单可用金额
  if (item?.editable) {
    const zeroRangeErr = standardNumber === 0 && sumUseBalanceNumber !== 0
    const positiveRangeErr = standardNumber > 0 && !(standardNumber >= 0.01 && standardNumber <= sumUseBalanceNumber)
    const negativeRangeErr = standardNumber < 0 && !(standardNumber <= -0.01 && standardNumber >= sumUseBalanceNumber)
    return !standard || zeroRangeErr || positiveRangeErr || negativeRangeErr
  } else {
    return !standard || standardNumber !== sumUseBalanceNumber
  }
}

export function filterCSCFields(components) {
  const fields = components.filter(item => {
    const defaultValueType = get(item, 'defaultValue.type')
    if (
      defaultValueType === 'officialCardMoney' ||
      defaultValueType === 'officialCardSettlement' ||
      item.field === 'amount'
    ) {
      return item
    }
  })
  return fields
}

/**
 * 根据originalId获取模板
 */
const getSpecifications = params => {
  return specifications.GET('/[ids]', params)
}

/**
 * 根据originalId检查模板是否可用
 * @param specificationOriginalId
 * @returns {boolean}
 */
export const checkSpecificationActive = async specificationOriginalId => {
  const originalId = specificationOriginalId?.id || specificationOriginalId
  const res = await getSpecifications({ ids: [originalId] })
  const specActive = get(res, 'items[0].active', true)
  if (!specActive) {
    Dialog.alert({
      iconType: 'warn',
      bodyStyle: { textAlign: 'center' },
      title: i18n.get('复制失败'),
      content: i18n.get('该单据所用的单据模板已停用，请启用后再试'),
      confirmText: i18n.get('知道了')
    })
    return false
  }
  return true
}

// 获取模版上对当前人员隐藏的字段
export const getSpecificationHiddenFields = async specification => {
  if (!specification?.id) {
    return []
  }
  try {
    const result = await getSpecificationHiddenFieldsById(specification.id)
    return result?.items ?? []
  } catch (e) {
    return []
  }
}

export const getBatchSpecificationHiddenFields = async ids => {
  if (!ids.length) {
    return {}
  }
  try {
    const data = await batchGetSpecificationHiddenFieldsByIds(ids)
    const result = {}
    data?.items.forEach(item => {
      result[item.specificationId] = item.fieldsHideResult
    })
    return result
  } catch (e) {
    return {}
  }
}

export const checkSpecificationSumField = (template = []) => {
  if (!template?.length) {
    return false
  }
  return !!template.find(item => item.defaultValue?.type === 'sum')
}

// 获取单据模版名称：英文还是中文
export const getSpecificationName = specification => {
  if (!specification) return ''
  return i18n.currentLocale === 'en-US' && specification.enName
    ? specification.enName
    : specification?.fullname || specification.name
}

// 获取费用名称：英文还是中文
export const getFeetypeName = feeType => {
  if (!feeType) return ''
  if (i18n.currentLocale === 'en-US') {
    return feeType?.enFullName || feeType?.enName || feeType?.fullname || feeType?.name
  } else {
    return feeType?.fullname || feeType?.name
  }
}

function getInvoicesMoney(invoices) {
  const moneyMap = new Map()
  /**
   * 取发票总金额字段
   * 增值税发票：价税合计
   * 非增值税发票：金额
   * 海外发票：金额
   * 非税收票：金额合计
   */
  invoices.forEach(invoice => {
    // invoiceId 是修改后的invoiceId对象数据
    const _invoice = isObject(invoice?.invoiceId) ? invoice.invoiceId : invoice.originalData
    invoice = invoice?.valueObj || _invoice
    const master = invoice?.master || invoice
    const amount =
      master?.form[`E_${master.entityId}_价税合计`] ||
      master?.form[`E_${master.entityId}_金额`] ||
      master?.form[`E_${master.entityId}_发票金额`] ||
      master?.form[`E_${master.entityId}_金额合计`]
    if (amount?.foreignNumCode) {
      moneyMap.set(amount.foreignNumCode, (moneyMap.get(amount.foreignNumCode) || 0) + Number(amount.foreign))
    } else if (amount) {
      moneyMap.set(amount.standardNumCode, (moneyMap.get(amount.standardNumCode) || 0) + Number(amount.standard))
    }
  })
  return moneyMap
}

export function validateFeeTypeOnlyCurrency(feeTypeForm) {
  const { invoiceForm } = feeTypeForm
  let isOnlyOne = true
  if (!(api.getState()['@common'].powers.OverseasInvoice && getBoolVariation('only-one-hide-local-currrency'))) {
    return isOnlyOne
  }

  if (!invoiceForm?.invoices?.length) {
    return isOnlyOne
  }

  const invoicesMap = getInvoicesMoney(invoiceForm.invoices)
  // 如果Map的大小大于1，说明有不同的币种
  return invoicesMap.size <= 1
}

// 校验 费用金额<=发票总金额
export function validateInvoicesMoneyLessThanAmount(feeTypeForm) {
  const { invoiceForm, amount } = feeTypeForm
  if (!api.getState('@common').powers.ValidateFeeAmountLessThanInvoices) {
    return true
  }
  if (!getIfCheckInvoicesMoney()) {
    return true
  }
  if (!invoiceForm?.invoices?.length) {
    return true
  }

  const invoicesMap = getInvoicesMoney(invoiceForm.invoices)

  // 如果币种不唯一，则不进行校验
  if (invoicesMap.size > 1) {
    return true
  }

  // 如果币种唯一，则进行同币种校验
  if (amount?.foreignNumCode) {
    if (!invoicesMap.has(amount.foreignNumCode)) return true
    return Number(invoicesMap.get(amount.foreignNumCode).toFixed(amount.foreignScale)) >= Number(amount.foreign)
  } else if (amount?.standardNumCode) {
    if (!invoicesMap.has(amount.standardNumCode)) return true
    return Number(invoicesMap.get(amount.standardNumCode).toFixed(amount.standardScale)) >= Number(amount.standard)
  } else {
    return true
  }
}

export const getScenesLabel = (type, menuList) => {
  const typeObj = menuList.find(item => item.type === type)
  if (!typeObj) return
  const { label, enLabel } = typeObj
  if (i18n.currentLocale === 'en-US') {
    return enLabel || (typeObj.type === 'all' ? 'All' : label)
  }
  return label
}

export const getBacklogByFlowId = async (flowId) => {
  const backlogIdObj = await api.invokeService('@bill:get:current:backLog', flowId)
  if (backlogIdObj && backlogIdObj.id) {
    const res = await api.dispatch(getBackLog_OLD(backlogIdObj.id, flowId))
    return res.value
  }
  return null
}
