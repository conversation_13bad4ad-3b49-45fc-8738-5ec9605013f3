/**************************************
 * Created By LinK On 2022/9/14 18:54.
 **************************************/
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import { Resource } from '@ekuaibao/fetch'

const dimensions = new Resource('/api/v1/basedata/dimensions')
const commonDimension = new Resource('/api/v1/basedata/commonDimensionItem')
const currencyAction = new Resource('/api/v2/currency')
const loanInfo = new Resource('/api/v1/loan/loanInfo')
export const getDimensionConfig = (id: string) => {
  return dimensions.GET(`/[${id}]`)
}

export const getCommonDimensionConfig = (params: any) => {
  return commonDimension.GET('', params)
}

export const getCommonDimension = (params: any) => {
  return commonDimension.POST('', null, params)
}

// 获取前一版本币种汇率
export const getEffectiveCurrencyInfo = (originalId: string, time: number) => {
  return currencyAction.GET('/effectiveCurrencyInfo/$originalId/$time', { originalId, time })
}

export function getLoanCurrencyField(id: string) {
  return loanInfo.GET('/timecaliber', { id })
}
