/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/29 下午5:27.
 */
/**
 * sourceField 作为key
 * @param map
 * @param fields
 */
export function sourceToTarget(map, fields) {
  fields.forEach(line => {
    if (line.fields && line.fields.length > 0) {
      map[line.sourceField] = {}
      sourceToTarget(map[line.sourceField], line.fields)
    } else if (map[line.sourceField]) {
      //存在一个字段要映射到多个字段上面
      let array = []
      const data = map[line.sourceField]
      if (Array.isArray(data)) {
        array = data
        handleField(array, line.targetField)
      } else {
        handleField(array, data)
        handleField(array, line.targetField)
      }
      map[line.sourceField] = array
    } else {
      map[line.sourceField] = line.targetField
    }
  })
}

/**
 * targetField作为key
 * @param map
 * @param fields
 */

export function targetToSource(map, fields) {
  fields.forEach(line => {
    if (line.fields && line.fields.length > 0) {
      map[line.targetField] = {}
      targetToSource(map[line.targetField], line.fields)
    } else {
      map[line.targetField] = line.sourceField
    }
  })
}

function handleField(array, field) {
  if (!!~dataRangeFields.indexOf(field)) {
    array.push({ field: field, type: 'dateRange' })
  } else {
    array.push({ field: field })
  }
}

const dataRangeFields = ['feeDatePeriod', 'datePeriod']
