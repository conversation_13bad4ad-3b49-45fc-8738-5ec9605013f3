/**
 *  Created by pw on 2021/3/11 下午12:57.
 */

import { app } from '@ekuaibao/whispered'

interface TrackBillProps {
  startTime: number
  endTime: number
}

export function trackBillReviewTime(params: TrackBillProps) {
  // trackBill('checkflowTime', '查看单据详情时长统计', 'checkFlowTime', params)
}

export function trackLoadBillFlowTime(params: TrackBillProps) {
  trackBill('loadflowTime', '系统打开单据详情时长', 'loadflowTime', params)
}

function trackBill(key: string, actionName: string, eventName: string, params: TrackBillProps) {
  const { startTime, endTime } = params
  const me_info = app.getState()['@common'].me_info
  const corpId = me_info?.staff?.corporationId?.id ? me_info?.staff?.corporationId.id : me_info?.staff?.corporationId
  const staffId = me_info?.staff?.id
  const instance: any = app
  if (instance.track) {
    const decice = window.isIOS ? 'iOS' : 'android'
    instance.track(key, {
      actionName,
      corpId,
      [eventName]: endTime - startTime,
      decice,
      staffId
    })
  }
}
