import { checkBillBudgetOccupy } from '../bill.action'

interface BudgetNodeIF {
  budgetId: string
  nodeId: string
  name: string
  isRollCalc: boolean
  periodTime: string
  budgetMoney: number
  confirmedMoney: number
  occupiedMoney: number
  budgetMoneyRoll: number
  confirmedMoneyRoll: number
  occupiedMoneyRoll: number
  isCustom: boolean
  overControllerRate: number
  control: string
  isCanView: boolean
  freeze: boolean
}

export async function checkBudgetOccupy(param: any): Promise<boolean> {
  const { form, formType, flowId } = param
  const result = await checkBillBudgetOccupy({ form, formType, flowId }).catch(err => {
    console.log(err)
    return true
  })
  return result
}
