import { Dialog } from '@hose/eui-mobile'
import { app as api } from '@ekuaibao/whispered'

// 修改币种类型时，设置所有币种
export function handlerCurrencyMoneySelectChange(parms, type) {
  const bus = this.props.bus || this.bus
  const { foreignCurrency, allCurrencyRates } = parms
  let pageName = ''
  if (type === 'billInfo') {
    pageName = i18n.get('单据')
  } else if (type === 'feeDetail') {
    pageName = i18n.get('明细')
  } else if (type === 'editInvoice') {
    pageName = i18n.get('发票')
  }
  Dialog.confirm({
    title: i18n.get(`提示`),
    content: i18n.get(`您是否切换本{_k1}所有原币币种为{_k2}`, { _k1: pageName, _k2: foreignCurrency?.name }),

    onConfirm: () => {
      bus.emit('set:default:currency', {
        foreignCurrency,
        rates: allCurrencyRates,
        isSetDefault: true
      })
    }
  })
}
// 全局币种数据
const getGlobalCurrency = data => {
  const standardCurrency = data?.standardCurrency ?? api.getState()['@common'].standardCurrency ?? {}
  const allCurrencyRates = data?.allCurrencyRates ?? api.getState()['@common'].allCurrencyRates?.slice() ?? []
  return {
    standardCurrency,
    allCurrencyRates
  }
}
function filterCurrencyList(rates, currency) {
  return rates.find(i => i.isDefault && i.originalId === currency.numCode)
}
// 新建单据及手动设置默认币种
export function setDefaultCurrencyMoney(data, isDimention) {
  const { bus, dimensionCurrencyInfo } = this.props
  const { standardCurrency, allCurrencyRates } = getGlobalCurrency(data)
  let filterDefaultCurrency = filterCurrencyList(allCurrencyRates, standardCurrency)
  // 费用明细中是否选择法人实体多币种
  if (dimensionCurrencyInfo) {
    filterDefaultCurrency = dimensionCurrencyInfo.rates?.find(_ => _.isDefault)
  }
  filterDefaultCurrency = fnSetCurrencyBySpec({ props: this.props, data, filterDefaultCurrency, isDimention })
  if (!!filterDefaultCurrency) {
    fnSetDefaultCurrency(bus, data, filterDefaultCurrency, allCurrencyRates, true)
  }
}

// 修改默认币种
const fnSetDefaultCurrency = (bus, data, filterDefaultCurrency, allCurrencyRates, isSetDefault = true) => {
  const currencyData = {
    foreignCurrency: filterDefaultCurrency ?? '',
    rates: allCurrencyRates,
    isSetDefault
  }
  if (data) {
    bus.emit('set:default:currency', currencyData)
  } else {
    bus.emit('first:set:default:currency', currencyData)
  }
}
// 监听法人实体多币种切换
export function handleDimensionCurrencyChange({ currency, rates }) {
  setDefaultCurrencyMoney.call(
    this,
    {
      standardCurrency: currency,
      allCurrencyRates: rates
    },
    true
  )
}

// 单据模板配置默认币种
const fnSetCurrencyBySpec = ({ props, data, filterDefaultCurrency, isDimention }) => {
  const { dimensionCurrencyInfo } = props
  const billSpecification = getSpecInfo(props)
  const { standardCurrency, allCurrencyRates } = getGlobalCurrency(data)
  const { isConfigureDefaultCurrency = false, currencyInfoId = '' } = getCurrencyBySpec(billSpecification.configs)
  // 单据模板配置了默认币种（if条件分开写提高代码可读性）
  if (!!isConfigureDefaultCurrency && isBill(props)) {
    // 设置了法人实体多币种，和与单据模板配置的默认币种相同
    if (!isDimention) {
      filterDefaultCurrency = getCurrencyById(currencyInfoId, allCurrencyRates)
    } else if (hasDimention(isDimention, dimensionCurrencyInfo) && isGlobalCurrency(standardCurrency)) {
      filterDefaultCurrency = getCurrencyById(currencyInfoId, allCurrencyRates)
    }
  } else if (!!isConfigureDefaultCurrency && !isBill(props)) {
    if (!!dimensionCurrencyInfo && isGlobalCurrency(dimensionCurrencyInfo?.currency)) {
      filterDefaultCurrency = getCurrencyById(currencyInfoId, allCurrencyRates)
    } else if (!!!dimensionCurrencyInfo) {
      filterDefaultCurrency = getCurrencyById(currencyInfoId, allCurrencyRates)
    }
  }
  return filterDefaultCurrency
}
// 获取默认币种
const getCurrencyBySpec = config => filterConfigByKey(config, 'publicExpansion')

// 过滤单据模板配置项
const filterConfigByKey = (config = [], key, keyword = 'ability') => config.find(i => i[keyword] === key) ?? {}

// 兼容老数据

const getCurrencyById = (currencyInfoId, allCurrencyRates) => allCurrencyRates.find(i => i.id === currencyInfoId)
// 获取单据模板数据

const getSpecInfo = props => {
  const { specification, billSpecification } = props
  return specification ?? billSpecification
}
// 判断法人实体本位币是否是全局本位币
const isGlobalCurrency = currency => api.getState()['@common'].standardCurrency?.numCode === currency?.numCode

// 判断是否使用法人实体多币种
const hasDimention = (isDimention, dimensionCurrencyInfo) => !!isDimention || !!dimensionCurrencyInfo

// 判断是单据还是费类
const isBill = props => !!props.specification
