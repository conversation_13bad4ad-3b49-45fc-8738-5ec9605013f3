/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/8/21 下午7:30.
 */
import get from 'lodash/get'
import { isObject } from '@ekuaibao/helpers'
import { isTicketReview } from './BillInfoEditableUtils'
import { app as api } from '@ekuaibao/whispered'

export function getImportValueWithValidate(form, specification) {
  handleDetailAttachmentsParas(form.details)
  handleTripsAttachmentsParas(form.trips)
  getAttachmentsBySpecification(form, specification)
  handleOrdersDataParas(form.details)
  handleMoney(form, specification)
  handleInvoiceForm(form.details, specification)
}

export function handleCountersigonNode(nodes = []) {
  nodes?.forEach(line => {
    if (line.type === 'countersign') {
      let { counterSigners, counterSignersCandidate } = line
      deleteCountersSignerId(counterSigners)
      deleteCountersSignerId(counterSignersCandidate)
    }
  })
}

export function handleMoney(form, specification) {
  let components = (specification && specification.components) || []
  components?.forEach(line => {
    if (line.type === 'money') {
      let key = line.field
      let standardNumCode = get(form[key], 'standardNumCode.numCode')
      if (standardNumCode) form[key] = { ...form[key], standardNumCode }
    }
  })
}

function deleteCountersSignerId(counterSigners) {
  counterSigners &&
    counterSigners.forEach(line => {
      line.signerId = line.signerId.id
    })
}

function handleInvoiceForm(details, specification = {}) {
  const isTicketReviewConfig = isTicketReview(specification, true)
  details &&
    details.forEach(line => {
      let {
        feeTypeForm: { invoiceForm }
      } = line
      if (!invoiceForm) return
      let { invoices, attachments, invoiceCorporationId } = invoiceForm
      if (invoices && invoices.length > 0) {
        invoiceForm.invoices = invoices
          .map(line => {
            const itemIds = line.itemIds && line.itemIds.filter(line => !!line.id).map(line => line.id)
            const invoiceId = isObject(line.invoiceId) ? line.invoiceId.id : line.invoiceId
            const data = {
              invoiceId,
              itemIds,
              taxRate: line.taxRate,
              taxAmount: line.taxAmount
            }
            if (isTicketReviewConfig) {
              data.approveAmount = line.approveAmount
              data.comment = line.comment
            }
            return data
          })
          .filter(oo => !!oo && !!oo.invoiceId)
      }
      if (attachments) {
        invoiceForm.attachments = attachments
          .filter(line => !!line)
          .map(line => {
            return { key: line.key, fileName: line.fileName, fileId: get(line, 'fileId.id', '') }
          })
      }
      if (invoiceForm.type === 'unify') {
        invoiceForm.invoiceCorporationId = isObject(invoiceCorporationId) ? invoiceCorporationId.id : invoiceCorporationId || ''
      }
    })
}

function getAttachmentsBySpecification(form, specification) {
  let components = (specification && specification.components) || []
  let undeal = true
  components?.forEach(line => {
    if (line.type === 'attachments' || line.type === 'aiAttachments') {
      let attach = form[line.field]
      handleAttachmentsParas(attach)
      undeal = false
    } else if (line.type === 'money') {
      try {
        const key = line.field
        const listValue = form[key]
        if (form[key]?.standard === undefined || form[key]?.standard === '') {
          delete form[key]
        } else if (
          listValue &&
          Object.hasOwnProperty?.call?.(listValue, 'foreign') &&
          Object.hasOwnProperty?.call?.(listValue, 'foreignNumCode')
        ) {
          const keys = Object.keys(listValue)
          if (keys.length < 14) {
            const allCurrencyRates = api.getState()['@common']?.allCurrencyRates ?? []
            const rate = allCurrencyRates.find?.(rate => rate?.numCode === listValue?.foreignNumCode)
            if (rate) {
              listValue.foreignStrCode = rate?.strCode
              listValue.foreignSymbol = rate?.symbol
              listValue.foreignUnit = rate?.unit
              listValue.foreignScale = rate?.scale
            }
          }
        }
      } catch (error) {
        console.log(error)
      }
    }
  })
  const attachments = get(form, 'attachments', [])
  if (undeal && attachments.length > 0) {
    handleAttachmentsParas(attachments)
  }
}

function handleDetailAttachmentsParas(details = []) {
  details?.forEach(line => {
    let { feeTypeForm } = line
    delete feeTypeForm.ordersData
    let specificationId = typeof line.specificationId === 'object' ? line.specificationId : line.template
    getAttachmentsBySpecification(line.feeTypeForm, specificationId)
  })
}

function handleAttachmentsParas(attachments = []) {
  attachments &&
    attachments?.forEach(line => {
      if (typeof line.fileId === 'object') {
        let { fileId } = line
        let keyString = fileId.key
        let id = fileId.id
        delete line.fileId
        line.key = keyString
        line.fileId = id
      }
    })
}

function handleOrdersDataParas(details = []) {
  details?.forEach(line => delete line.feeTypeForm.ordersData)
}

function handleTripsAttachmentsParas(trips) {
  if (!trips) return
  trips?.forEach(line => {
    const specificationId = typeof line.specificationId === 'object' ? line.specificationId : line.template
    getAttachmentsBySpecification(line.tripForm, specificationId)
  })
}
