import { app } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { MoneyMath } from '@ekuaibao/money-math'
const { standardValueMoney } = app.require<any>('@components/utils/fnInitalValue')

export function multipleMoney(value: any) {
  return invoiceMoney(value)
}

export function mergeMoney(value: any[]) {
  if (!value || !value.length) {
    return { amount: standardValueMoney('0.00') }
  }
  const moneys = value.map((line: any) => {
    return invoiceMoney(line)
  })
  const { amount, noTaxAmount, taxAmount } = totalMoney(moneys)
  const firstTaxRate = moneys.length ? moneys[0].taxRate : undefined
  const flag = moneys.every((money: any) => money.taxRate === firstTaxRate)
  const taxRate = flag ? firstTaxRate : undefined
  return { amount, noTaxAmount, taxAmount, taxRate }
}

function initMoney(money: any) {
  if (!money || typeof money !== 'object' || !money.hasOwnProperty('standard')) {
    return standardValueMoney('0.00')
  }
  return money
}

function totalMoney(moneys: Array<{ amount: number; noTaxAmount: string; taxAmount: number }>) {
  let totalAmount = standardValueMoney('0.00')
  let totalNoTaxAmount = standardValueMoney('0.00')
  let totalTaxAmount = standardValueMoney('0.00')
  moneys.forEach(money => {
    totalAmount = new MoneyMath(money.amount).add(totalAmount).value
    totalNoTaxAmount = new MoneyMath(money.noTaxAmount).add(totalNoTaxAmount).value
    totalTaxAmount = new MoneyMath(money.taxAmount).add(totalTaxAmount).value
  })
  return { amount: totalAmount, noTaxAmount: totalNoTaxAmount, taxAmount: totalTaxAmount }
}

function invoiceMoney(line: any) {
  const key = line.invoiceId.entityId
  const moneyPath =
    key === i18n.get('system_发票主体')
      ? i18n.get(`form.E_{__k0}_价税合计`, { __k0: key })
      : i18n.get(`form.E_{__k0}_金额`, { __k0: key })
  const path = key === i18n.get('system_发票主体') ? `form.E_${key}_` : `form.E_`
  const amount = initMoney(get(line.invoiceId, moneyPath))
  const noTaxAmount =
    key === 'system_发票主体' // @i18n-ignore
      ? initMoney(get(line.invoiceId, i18n.get(`{__k0}发票金额`, { __k0: path })))
      : initMoney(get(line.invoiceId, i18n.get(`{__k0}不计税金额`, { __k0: path })))
  const taxAmount = initMoney(get(line.invoiceId, i18n.get(`{__k0}税额`, { __k0: path })))
  const rate =
    key === 'system_发票主体' // @i18n-ignore
      ? get(line, 'itemIds[0].form.E_system_发票明细_税率') // @i18n-ignore
      : get(line.invoiceId, i18n.get(`{__k0}税率`, { __k0: path }))
  const taxRate = rate ? (!!~rate.indexOf('%') ? rate.replace('%', '') : rate) : undefined
  return { amount, noTaxAmount, taxAmount, taxRate }
}

interface MoneyInterface {
  standard: string
  standardStrCode: number
  standardNumCode: number
  standardSymbol: string
  standardUnit: string
  standardScale: number
}
