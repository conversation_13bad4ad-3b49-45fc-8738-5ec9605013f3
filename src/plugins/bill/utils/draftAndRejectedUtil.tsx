/**
 *  Created by gym on 2018/12/20 下午6:37.
 *
 *  draft和rejected有大量重复代码,做一下合并
 */

// @ts-ignore
import { getDefSpecificationDS } from './parseSpecification'
// @ts-ignore
import { formatRiskNotice } from './formatRiskData'
// @ts-ignore
import { app as api } from '@ekuaibao/whispered'
import cloneDeep from 'lodash/cloneDeep'
import get from 'lodash/get'
import { formatRiskWarningsV2 } from './formatRiskData.formatRiskWarnings'
import { isObject, isUndefined } from '@ekuaibao/helpers'
import { Fetch } from '@ekuaibao/fetch'
// 解决单点登录企业不重新拉去易快报资源问题
const reloadSpecificationWhiteList = ['zuY3K7aYWy3Gvf']

interface baseData {
  bus: any
  id: string
  type: string
  setState: Function
  globalFieldsMap: string
  specification_group: string
  isModifyBill: any
  RiskPromptOptimization?: any
  isForbid?: boolean
}

interface Args {
  external: string
  form: any
  bus: any
  newTemplate: string
  setState: Function
  template: string
  globalFieldsMap: string
  isModifyBill: any
  RiskPromptOptimization?: any
  isForbid?: boolean
}

export function publicInitiFlow(args: baseData) {
  const {
    id,
    type,
    globalFieldsMap,
    specification_group,
    setState,
    bus,
    isModifyBill = false,
    RiskPromptOptimization,
    isForbid = false
  } = args

  return Promise.all([
    api.invokeService('@common:get:flow:detail:info', { id, type }),
    api.invokeService(
      '@common:get:riskwarningById',
      isModifyBill ? { flowId: id } : { flowId: id, level: 'OutOfLimitReject' }
    )
  ]).then(result => {
    let data = result[0]
    const { payeeId, receivingCurrency } = data?.value?.form
    if (receivingCurrency && payeeId) {
      payeeId.receivingCurrency = receivingCurrency
    }
    const alterFlag = data?.value?.form?.alterFlag

    if (!isUndefined(alterFlag)) {
      api?.logger?.info('查看alterFlag的值', {
        alterFlag: alterFlag,
        flowId: data?.value?.id
      })
    }

    const riskAll = result[1]
    let risks = riskAll?.value
    return api.invokeService('@home:get:specification:with:version').then(async (resSpecificationGroup: any) => {
      const specificationGroups = specification_group?.length
        ? specification_group
        : resSpecificationGroup?.specification_group
      let currentSpecification = getDefSpecificationDS(data.value, specificationGroups)
      if (reloadSpecificationWhiteList.includes(Fetch.ekbCorpId)) {
        if (currentSpecification?.active) {
          const lastestSP = await api.dataLoader('@common.getSpecificationByOriginalId').reload({
            id: isObject(currentSpecification.originalId)
              ? currentSpecification.originalId?.id
              : currentSpecification.originalId
          })
          if (lastestSP?.mustUpdateTemplate && lastestSP?.id !== currentSpecification.id) {
            await api.invokeService('@home:get:specification:with:version:reload')
          }
        }
      }
      const {
        form: { specificationId, submitterId, details }
      } = data.value
      const components = specificationId.components
      let flowRiskList = formatRiskWarningsV2(cloneDeep(riskAll), details)
      let noticeList = formatRiskNotice({
        riskData: flowRiskList,
        components,
        globalFieldsMap,
        bus: bus,
        submitter: submitterId,
        isBudgetClick: false,
        RiskPromptOptimization,
        isForbid
      })
      noticeList && (noticeList.originalRisk = result[1])
      setState({ noticeList, flowRiskList })
      return currentSpecification
    })
  })
}

export async function publicInitFlowFromApplyReq(args: any) {
  const { id, type, specification_group } = args
  const data = await api.invokeService('@common:get:flow:detail:info', { id, type })
  await api.invokeService('@home:get:specification:with:version')
  return getDefSpecificationDS(data.value, specification_group)
}

export function fnDelegatorList(userInfo: any, delegators: any[] = [], specification: any) {
  let delegatorList = []
  if (specification) {
    // const id = specification.id?.split(':')[0]
    const id = get(specification, 'originalId.id', '') || get(specification, 'originalId', '')
    delegatorList = delegators.filter(de => {
      return (
        de.delegateType === specification.type &&
        (!de.specIds || de.specIds.length === 0 || de.specIds.indexOf(id) > -1)
      )
    })
  }
  let tags: { [key: string]: any } = {}
  let delegatorObjectList: any[] = []
  if (delegatorList.length) {
    delegatorList.forEach((o: any) => {
      delegatorObjectList[o.id] = o
    })
  }
  if (!userInfo) {
    return tags
  }
  delegatorObjectList[userInfo.id] = userInfo
  tags.submitterId = { delegatorObjectList, userInfo }
  return tags
}
export function resetRiskNotice(args: Args) {
  const { external, form, newTemplate, setState, template, globalFieldsMap, bus, RiskPromptOptimization, isForbid = false } = args
  let submitterId = get(form, 'submitterId')
  let components = get(form, 'specificationId.components')
  newTemplate && setState({ template: newTemplate })
  let temps = newTemplate ? newTemplate : template ? template : components
  let noticeList = formatRiskNotice({
    riskData: external,
    components: temps,
    globalFieldsMap,
    bus: bus,
    submitter: submitterId,
    RiskPromptOptimization,
    isForbid
  })
  setState({ noticeList })
}
