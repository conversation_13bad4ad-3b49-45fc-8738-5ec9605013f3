import { app as api } from '@ekuaibao/whispered'
import moment from 'moment'
import { targetToSource } from './fieldsMergeUtils'
import { get, clone, isNumber } from 'lodash'
import { mergeMoney, multipleMoney } from './OCRUtils'
import { checkPriority } from './CheckFieldPriority'
import { getAutoCalParams4Import, formatValue, formatAttrValue, checkAttrChange } from './autoCalResult'
import { getAssignmentRuleById, getCalculateResult } from '../bill.action'
import InvoiceMappingValue, { standardValueMoney } from '../../../lib/InvoiceMappingValue'
import { updateInvoiceDeduction } from '../../../lib/invoice/InvoicePriceTaxSeparated'
import { isArray } from '@ekuaibao/helpers'
import { toast } from '../../../lib/util'
const moneyStr2StandardMoneyValue = api.require('@lib/parser.moneyStr2StandardMoneyValue')
const invoiceOptions = api.require('@invoice-form/utils/config.invoiceOptions')
const getDefaultValue = api.require('@components/utils/getInitialValue')
import { specificationKeyMap } from '../../feetype/parts/FeetypeInfoEditable'

async function invoiceConvertConsume(result) {
  const invoiceMappingValue = new InvoiceMappingValue()
  const { isMerge } = result
  if (isMerge) {
    const { feetypes = [], orders } = result
    let feeType = feetypes.length ? feetypes[0] : {}
    await updateInvoiceDeduction(feetypes, orders, isMerge)
    return await invoiceMappingValue.invoice2Detail({ invoices: orders, feeType })
  }

  const { orders, feetypes = [] } = result
  await updateInvoiceDeduction(feetypes, orders, isMerge)
  const promises = orders.map(async (invoice, index) => {
    const feeType = feetypes[index]
    return await invoiceMappingValue.invoice2Detail({ invoices: [invoice], feeType })
  })
  return Promise.all(promises)
}

function consumeDetails(feeType, feeTypeForm) {
  const expenseSpecification = get(feeType, 'expenseSpecification')
  let consume = {
    feeTypeId: feeType,
    feeTypeForm: feeTypeForm,
    specificationId: expenseSpecification
  }

  return Promise.resolve(consume)
}

function didiConvertConsume(data, others) {
  let consumeData = []
  const { feetype, orders } = data

  const promiseArray = orders.map(order => {
    const { form } = order
    const item = {
      amount: form.actualPrice,
      feeDate: moment().valueOf(),
      order,
      feetype
    }

    let feeTypeForm = {}
    const { expenseSpecification } = feetype
    const components = expenseSpecification && expenseSpecification.components

    components.forEach(v => {
      const mappingRelation = api.getState()['@third-import'].mappingRelation
      const mapping = mappingRelation.filter(m => m.to === v.field)

      mapping.forEach(m => {
        if (form[m.from]) {
          feeTypeForm[m.to] = form[m.from]
          v.editable = m.editable
        }
      })
    })
    item.feeDate = feeTypeForm['feeDate']

    return formatDetails(item, feeTypeForm, others).then(consume => {
      consumeData.push(consume)
    })
  })

  return Promise.all(promiseArray).then(() => consumeData)
}

function EkbFlightConvertConsume(data) {
  const { feetype, orders } = data
  let consumeData = []
  const promiseArray = orders.map(order => {
    const orderItem = order.data
    let feeDate = setFeeDate(feetype, orderItem.data.routes[0].depart_time)
    const item = {
      amount: orderItem.actual_price,
      feeDate,
      order,
      feetype
    }
    return formatDetails(item).then(consume => {
      consumeData.push(consume)
    })
  })

  return Promise.all(promiseArray).then(() => consumeData)
}

function officialCardConvertConsume(data) {
  let consumeData = []
  const { feetype, orders } = data
  const promiseArray = orders.map(order => {
    const orderItem = order.data
    const dateTime = moment(new Date(`${orderItem.tranDate} ${orderItem.tranTime}`)).format('YYYY/MM/DD HH:mm')
    const feeDate = setFeeDate(feetype, dateTime)
    const item = {
      amount: orderItem.useBalance,
      feeDate,
      order,
      feetype
    }
    return formatDetails(item).then(consume => {
      consumeData.push(consume)
    })
  })

  return Promise.all(promiseArray).then(() => {
    return consumeData
  })
}

function formatDetails(item, feeTypeForm = {}, others) {
  let { amount, feeDate, order, feetype } = item
  const { data = {} } = order
  const { expenseSpecification } = feetype
  const components = expenseSpecification && expenseSpecification.components

  feeTypeForm = formatFeeTypeForm(components, feeDate, feeTypeForm, data, others, order)

  components.forEach(el => {
    if (get(el, 'defaultValue.type') === 'officialCardMoney') {
      feeTypeForm[el.field] = moneyStr2StandardMoneyValue(amount)
    }
  })

  let consume = {
    feeTypeId: feetype,
    feeTypeForm: {
      ...feeTypeForm,
      amount: moneyStr2StandardMoneyValue(amount),
      orders: [order.id],
      ordersData: [order]
    },
    specificationId: expenseSpecification
  }

  return Promise.resolve(consume)
}

export function formatInitialValue(form, baseDataPropertiesMap, lastChoice, submitterId) {
  return new Promise(resolve => {
    const { feeTypeForm, specificationId } = form
    const { components } = specificationId
    const defaultForm = {}
    const promiseList = components.map(c => {
      const field = baseDataPropertiesMap[c.field]
      return getDefaultValue(c, field, lastChoice, submitterId).then(data => {
        if (data || data === false) {
          defaultForm[c.field] = data
        }
      })
    })
    Promise.all(promiseList).then(_ => {
      form.feeTypeForm = { ...defaultForm, ...feeTypeForm }
      resolve(form)
    })
  })
}
//type单据类型
export function getAutoCalResult(form, billData, billSpecification, baseDataPropertiesMap) {
  let { feeTypeForm, feeTypeId, specificationId } = form || {}
  return new Promise(resolve => {
    let data = {}
    data.feeTypeForm = clone(feeTypeForm)
    data.feeTypeId = (feeTypeId && feeTypeId.id) || ''
    data.specificationId = specificationId && specificationId.id
    const params = getAutoCalParams4Import(billData, data, billSpecification)
    api
      .invokeService('@bill:get:calculationresult', params)
      .then(action => {
        if (action.error) {
          getCalculateRecordLink(params.billData, form, specificationId, resolve)
          return
        }
        const r = {}
        const results = action.payload.items
        results &&
          results.forEach(element => {
            const { onField, result, resultType, attribute, numCode, currencyType, amount } = element
            const detailComponents = get(form, 'specificationId.components', [])
            const _result = result === '' || result === 'null' ? undefined : result
            let component = specificationId.components.find(v => v.field === onField)
            const specificationComponent = specificationId.components.find(v => v.field === onField)
            if (resultType === 'VALUE') {
              const type = get(baseDataPropertiesMap[onField], 'dataType.type')
              const entity = get(baseDataPropertiesMap[onField], 'dataType.entity')
              if (component.editable && component.defaultValue && component.defaultValue.type === 'formula') {
                //组件为可编辑状态
                r[onField] = !!result ? formatValue(type, entity, result, component) : feeTypeForm[onField]
              } else {
                // 配置了按币种自定义计算规则的字段，需要参数resultCurrencyInfo
                const resultCurrencyInfo = get(component, 'defaultValue.customRuleId')
                  ? { currencyType, numCode, amount }
                  : null
                r[onField] = formatValue(type, entity, result, component, null, resultCurrencyInfo)
              }
            } else if (resultType === 'SPECIFICATION_ATTRIBUTE') {
              const isApparitionsOpenChange = attribute === 'open' ? String(component[attribute]) !== _result : false
              if (
                !checkAttrChange(component[attribute], _result, component, baseDataPropertiesMap[onField]) ||
                isApparitionsOpenChange
              ) {
                const attrObj = formatAttrValue(attribute, _result, detailComponents, baseDataPropertiesMap[onField])
                component = { ...component, ...attrObj }
                switch (attribute) {
                  case 'open': {
                    if (!specificationComponent) {
                      break
                    }
                    specificationComponent.open = _result === 'true'
                    break
                  }
                }
              }
              const type =
                get(baseDataPropertiesMap[onField], 'dataType.elemType.type') ||
                get(baseDataPropertiesMap[onField], 'dataType.type')
              if (attribute === 'optional') {
                let isOptional = false
                if (type === 'attachment') {
                  isOptional =
                    component[attribute] === false &&
                    (!feeTypeForm?.[onField] ||
                      !(Array.isArray(feeTypeForm?.[onField]) && feeTypeForm?.[onField].length))
                } else {
                  const onFieldValue = feeTypeForm?.[onField] || r[onField]
                  isOptional = component?.[attribute] == false && !onFieldValue
                }
                if (isOptional) {
                  form.errorMsg = {
                    completed: i18n.get('该明细填写不完整'),
                    message: i18n.get('该明细填写不完整'),
                    isCheckCalAttr: true
                  }
                }
              }
            }
          })
        const { components } = specificationId
        const linkageAssignmentField = components.find(
          element =>
            element.type === 'dataLink' &&
            element.defaultValue &&
            element.defaultValue.type === 'formula' &&
            element.isLinkageAssignment
        )
        if (linkageAssignmentField && r[linkageAssignmentField.field]) {
          formatDataLinkDataForAssignment(r[linkageAssignmentField.field], components, linkageAssignmentField).then(
            dataLinkFormValue => {
              form.feeTypeForm = { ...feeTypeForm, ...dataLinkFormValue, ...r }
              getCalculateRecordLink(params.billData, form, specificationId, resolve)
            }
          )
        } else {
          form.feeTypeForm = { ...feeTypeForm, ...r }
          getCalculateRecordLink(params.billData, form, specificationId, resolve)
        }
      })
      .catch(error => {
        getCalculateRecordLink(params.billData, form, specificationId, resolve)
        return toast.error(error.msg || error.errorMessage)
      })
  })
}

function getCalculateRecordLink(billData, form, specificationId, resolve) {
  const components = specificationId?.components || []
  const dependence = components.find(item => item?.dependence?.length)
  if (dependence) {
    api
      .dispatch(getCalculateResult(billData))
      .then(res => {
        const { feeTypeForm } = form
        const newFeeTypeForm = res?.items?.[0]?.feeTypeForm
        delete newFeeTypeForm.linkDetailEntities //这个字段不会自动计算，用之前的就可以了，这个接口返回的结构有问题
        if (newFeeTypeForm) {
          form.feeTypeForm = { ...feeTypeForm, ...newFeeTypeForm }
        }
        resolve(form)
      })
      .catch(error => {
        resolve(form)
        return toast.error(error.msg || error.errorMessage)
      })
  } else {
    resolve(form)
  }
}

// 初始化feeTypeForm
function formatFeeTypeForm(components, feeDate, feeTypeForm = {}, data = {}, others, order) {
  if (typeof feeDate === 'object') {
    feeTypeForm['feeDatePeriod'] = feeDate.feeDatePeriod
  } else {
    feeTypeForm['feeDate'] = data.feeDate ? data.feeDate : feeDate
  }

  components.forEach(v => {
    // 处理映射字段默认值
    if (v.field === 'invoice') {
      feeTypeForm[v.field] = '1'
    }

    if (v.field === 'invoiceForm') {
      invoiceFromDefault(v, feeTypeForm, others, order)
    }

    if (v.type === 'switcher') {
      feeTypeForm[v.field] = v.defaultValue && v.defaultValue.value
    }

    if (v.type === 'money' && feeTypeForm[v.field]) {
      feeTypeForm[v.field] = moneyStr2StandardMoneyValue(feeTypeForm[v.field])
    }

    if (v.field === 'invoiceType') {
      let invoiceType = data.invtype || data.invoiceType
      feeTypeForm.invoiceType = setInvoiceType(invoiceType)
    }

    if (v.field === 'noTaxAmount') {
      const noTaxAmount = Number(data.invamt) || data.invamt || data.noTaxAmount || feeTypeForm.noTaxAmount
      feeTypeForm.noTaxAmount = moneyStr2StandardMoneyValue(noTaxAmount)
    }

    if (v.field === 'taxAmount') {
      const taxAmount = Number(data.invtaxamt) || data.invtaxamt || data.taxAmount || feeTypeForm.taxAmount
      feeTypeForm.taxAmount = moneyStr2StandardMoneyValue(taxAmount)
    }
    if (v.field === 'taxRate') {
      //税率
      if (data && data.itemdata) {
        const itemdata = JSON.parse(data.itemdata)
        if (itemdata && itemdata.length === 1) {
          feeTypeForm.taxRate = itemdata[0].taxRate
        }
      }
      if (data.taxRate) {
        feeTypeForm.taxRate = data.taxRate
      }
    }
  })
  return feeTypeForm
}

/**
 * 导入滴滴订单，若授权范围为：1丨2丨3 时，执行以下逻辑：
 *
 * 发票形式字段不可编辑的时候：
 *   非出租车订单：有统一开票且有DIDI开票方则：【发票形式】值默认为【统一开票】、【开票方】为“source"为"DIDI"的开票方
 *              有统一开票但没有DIDI开票方则：【发票形式】值默认为【统一开票】、【开票方】为空
 *              没有统一开票则：无需填写
 *   出租车订单： 无需填写
 *
 * 发票形式字段可编辑的时候：
 *   非出租车订单：有统一开票且有DIDI开票方则：【发票形式】值默认为【统一开票】、【开票方】为“source"为"DIDI"的开票方
 *              有统一开票但没有DIDI开票方则：【发票形式】值默认为【统一开票】、【开票方】为空
 *              没有统一开票则：【发票形式】的默认值取第一个
 *   出租车订单：【发票形式】的默认值取第一个（如果【发票形式】的第一个值是【统一开票】，则【开票方】默认选择第一个值）
 */
function invoiceFromDefault(v, feeTypeForm, others, order = {}) {
  const didiAuth = api.getState()['@didierp'].didiAuth
  const { platform, form = {} } = order
  const { useCarType } = form

  //发票形式列表
  const fnInvoiceType = invoiceOptions(v.invoiceType)
  //判断是否有统一开票
  const hasUnify = v.invoiceType.unify && ((v.editable && v.invoiceType.unify.choose) || !v.editable)

  const didi = platform === 'DIDI' && useCarType !== 'TAXI' && didiAuth
  const unifyList = api.getState()['@invoice-form'].unifyList || []
  const invoiceCorpIds = get(v, 'invoiceType.unify.invoiceCorporation')
  if (didi && hasUnify) {
    const didiUnfiy = unifyList.find(line => line.active && line.channel === 'DIDI')
    const limit = get(v, 'invoiceType.unify.limit')
    if (didiUnfiy && (!v.editable || !limit || invoiceCorpIds.indexOf(didiUnfiy.id) >= 0)) {
      feeTypeForm[v.field] = { type: 'unify', invoiceCorporationId: didiUnfiy }
    } else {
      feeTypeForm[v.field] = { type: 'unify', invoiceCorporationId: '' }
    }
  } else {
    if (!v.editable) {
      if (order) feeTypeForm[v.field] = { type: 'noWrite' }
    } else {
      let arr = fnInvoiceType || []
      const defaultInvoiceType = get(v, 'invoiceType.defaultInvoiceType', '')
      let type = defaultInvoiceType || (arr.length > 0 && arr[0].type)
      if (type) {
        if (type === 'unify') {
          feeTypeForm[v.field] = {
            type,
            invoiceCorporationId:
              invoiceCorpIds.length === 0 ? unifyList[0] : unifyList.find(item => item.id === invoiceCorpIds[0])
          }
        } else {
          feeTypeForm[v.field] = { type }
        }
      }
    }
  }
}

// 通用格式化时间
function setFeeDate(feetype, value, type) {
  let isreq = type === 'requisition'
  let components = isreq ? feetype.requisitionSpecification.components : feetype.expenseSpecification.components
  for (let i = 0; i < components.length; i++) {
    let item = components[i]
    let field = item.field
    if ((field && field === 'feeDatePeriod') || field === 'feeDate') {
      value = value && value.toString()
      let time = moment(value, 'YYYY-MM-DD HH:mm:ss').format('x') * 1
      if (item.type === 'dateRange') {
        return setValueRange(time)
      } else {
        return setValue(time)
      }
    }
  }
}

function setValueRange(time) {
  let start = time || Date.now()
  let end = time || Date.now()
  return {
    feeDatePeriod: {
      start: start,
      end: end
    }
  }
}

function setValue(time) {
  return time || Date.now()
}

export function setInvoiceType(type) {
  switch (type) {
    case 'DIGITAL_NORMAL':
    case 'PAPER_ROLL':
    case 'PAPER_FEE':
    case 'PAPER_NORMAL':
      return 'VATOrdinaryInvoice'
    case 'DIGITAL_SPECIAL':
      return 'VATElectronicSpecialInvoice'
    case 'PAPER_SPECIAL':
      return 'VATSpecialInvoice'
    case 'FULL_DIGITAl_PAPER':
      return 'FullDigitalSpecialPaper'
    case 'FULL_DIGITAl_PAPER_NORMAL':
      return 'FullDigitalSpecialPaperNormal'
    case 'PAPER_CAR':
      return 'MotorInvoice'
    default:
      return ''
  }
}

export function FormatData(order, type = 'expense') {
  if (!order) return

  if (order.type === 'invoice' || order.type === 'invoiceOCR') {
    return invoiceConvertConsume(order)
  }

  if (order.type === 'didi') {
    return didiConvertConsume(order.data, {
      invoiceForm: order.invoiceForm
    })
  }

  if (order.type === 'ekb') {
    return EkbFlightConvertConsume(order.data)
  }

  if (order.type === 'officialCard') {
    return officialCardConvertConsume(order.data)
  }

  if (order.type === 'dataLink') {
    return dataLinkConvertConsume(order, type)
  }

  if (order.type === 'requisitionDetail' || order.type === 'applyDetail') {
    return order.details
  }
  // if (order.type === 'invoiceOCR') {
  //   return OCRConverConsume(order)
  // }
}

function dataLinkConvertConsume(data, type) {
  const { feetype, dataLinkList, dataLinkEntity, linkDataLinkEntity } = data
  //赋值规则相同的数据互联字段, //多个的情况取第一个
  let assignmentRuleMap = {}
  let ids = dataLinkList.filter(item => item.entityId !== dataLinkEntity.id).map(o => o.entityId)
  if (!!linkDataLinkEntity) {
    //对照关联
    const linkIds = dataLinkList.filter(
      item => item.linkDataLinkEntity && item.linkDataLinkEntity.dataLink.entityId !== linkDataLinkEntity.id
    )
    ids = ids.concat(linkIds)
  }
  if (ids.length > 0) {
    return api.dispatch(getAssignmentRuleById([...new Set(ids)])).then(result => {
      const list = result.items
      list.forEach(item => {
        assignmentRuleMap[item.sourceEntityId] = item
      })
      return fnUpdateData(feetype, assignmentRuleMap, dataLinkList, type, dataLinkEntity)
    })
  } else {
    return fnUpdateData(feetype, assignmentRuleMap, dataLinkList, type, dataLinkEntity)
  }
}

function fnUpdateData(feetype, assignmentRuleMap, dataLinkList, type, dataLinkEntity) {
  let consumeData = []
  const promiseArray = dataLinkList.map(dataLink => {
    const { feeType: dataLinkFeeType, linkDataLinkEntity: linkDataLink } = dataLink
    let { id, entityId, useCount, totalCount, ownerId, visibility, active } = dataLink
    const form = dataLink.form || dataLink
    const linkValue = linkDataLink?.dataLink
    return consumeDataLinkDetails(
      dataLinkEntity,
      assignmentRuleMap,
      dataLinkFeeType ? dataLinkFeeType : feetype,
      entityId,
      linkValue,
      type,
      {
        ...form,
        id,
        ownerId,
        visibility,
        useCount,
        totalCount,
        active
      }
    ).then(consume => {
      consumeData.push(consume)
    })
  })
  return Promise.all(promiseArray).then(() => consumeData)
}

function getLinkageAssignmentValue(components, datalinkComponent, assignmentRuleMap, feeTypeForm, dataLinkId, form) {
  const feeTypeFomValue = { ...feeTypeForm }
  let map = {}
  if (datalinkComponent) {
    //只对相同赋值规则的第一个赋值
    feeTypeFomValue[datalinkComponent.field] = {
      id: form.id,
      form: { ...form }
    }
    //启用了联动赋值
    if (datalinkComponent.isLinkageAssignment) {
      targetToSource(map, (assignmentRuleMap[dataLinkId] || datalinkComponent.assignmentRule).fields)
      for (let key in map) {
        const value = form[map[key]]
        if (value?.location) {
          map[key] = value?.name || value.address
        } else {
          map[key] = value
        }
      }
      //对模板的字段赋值
      const { visible: multiplePayeesMode } = api.getState()['@bill'].payeeComponentData || {}
      map = filterFeeTypeFormByComponet(map, components, multiplePayeesMode)
    }
  }
  return { ...feeTypeFomValue, ...map }
}

function consumeDataLinkDetails(dataLinkEntity, assignmentRuleMap, feetype, dataLinkId, linkValue, type, form) {
  const specification = feetype[specificationKeyMap[type]]
  const components = specification.components
  const amountField = components.find(item => item.field === 'amount')
  //赋值规则相同的数据互联字段, //多个的情况取第一个
  const datalinkComponent = components.find(item => {
    return item.type === 'dataLink' && item.referenceData && item.referenceData.id === dataLinkEntity.id
  })

  let feeTypeForm = {}
  if (datalinkComponent) {
    feeTypeForm = getLinkageAssignmentValue(
      components,
      datalinkComponent,
      assignmentRuleMap,
      feeTypeForm,
      dataLinkId,
      form
    )
  }
  if (linkValue) {
    const entityId = linkValue.entityId
    const linkDatalinkComponent = components.find(item => {
      return item.type === 'dataLink' && item.referenceData && item.referenceData.id === entityId
    })
    feeTypeForm = getLinkageAssignmentValue(
      components,
      linkDatalinkComponent,
      assignmentRuleMap,
      feeTypeForm,
      entityId,
      linkValue
    )
  }
  feeTypeForm = checkFeeTypeForm(feeTypeForm)
  if (
    amountField &&
    amountField.optional === true &&
    amountField.hide === true &&
    (!feeTypeForm.amount || !feeTypeForm.amount.standard)
  ) {
    //在费用类型上面的金额字段隐藏并且选填的情况下，金额字段没有赋值的话会导致直接导入保存失败，因为金额字段会没有值
    feeTypeForm.amount = standardValueMoney(0)
  }
  let consume = {
    feeTypeId: feetype,
    feeTypeForm: feeTypeForm,
    specificationId: specification
  }

  return Promise.resolve(consume)
}

function checkFeeTypeForm(feeTypeForm = {}, components = []) {
  if (feeTypeForm.taxRate && !isNumber(feeTypeForm.taxRate * 1)) {
    feeTypeForm.taxRate = ''
  }
  return feeTypeForm
}

function filterFeeTypeFormByComponet(feeTypeForm, components, multiplePayeesMode = false) {
  let values = {}
  const keys = Object.keys(feeTypeForm)
  keys.forEach(key => {
    let field = components.find(c => c.field === key)
    if (field && checkPriority(field) && (!field.dependence || (field.dependence && !field.dependence.length))) {
      values[key] = feeTypeForm[key]
    }
  })
  if (multiplePayeesMode && feeTypeForm.feeDetailPayeeId) {
    values.feeDetailPayeeId = feeTypeForm.feeDetailPayeeId
  }
  return values
}

function OCRConverConsume(result) {
  const { isMerge } = result
  if (isMerge) {
    return OCRMergeConsume(result)
  } else {
    return OCRMultipleConsume(result)
  }
}

function OCRMultipleConsume(result) {
  const { orders, feetypes = [] } = result
  let consumeData = []
  const promiseArray = orders.map((order, index) => {
    const feeType = feetypes[index]
    const components = get(feeType, 'expenseSpecification.components')
    const haveTaxAmount = components.find(
      line =>
        line.field === 'taxAmount' && (line.defaultValue.type === 'none' || line.defaultValue.type === 'invoiceSum')
    )
    const haveNoTaxAmount = components.find(
      line =>
        line.field === 'noTaxAmount' && (line.defaultValue.type === 'none' || line.defaultValue.type === 'invoiceSum')
    )
    const haveTaxRate = components.find(line => line.field === 'taxRate')
    let feeDate = setFeeDate(feeType)
    let { amount, noTaxAmount, taxAmount, taxRate } = multipleMoney(order)
    let feeTypeForm = { amount }
    if (haveTaxAmount) {
      feeTypeForm.taxAmount = taxAmount
    }
    if (haveTaxRate) {
      feeTypeForm.taxRate = taxRate
    }
    if (haveNoTaxAmount) {
      feeTypeForm.noTaxAmount = noTaxAmount
    }
    feeTypeForm = formatFeeTypeForm(components, feeDate, feeTypeForm)
    if (feeTypeForm.hasOwnProperty('invoiceForm')) {
      let invoiceForm = {
        invoices: [order],
        type: 'exist'
      }
      feeTypeForm = { ...feeTypeForm, invoiceForm }
    }
    return consumeDetails(feeType, feeTypeForm).then(consume => {
      consumeData.push(consume)
    })
  })
  return Promise.all(promiseArray).then(() => consumeData)
}

function OCRMergeConsume(result) {
  const { orders, feetypes = [] } = result
  let { amount, noTaxAmount, taxAmount, taxRate } = mergeMoney(orders)
  let feeTypeForm = { amount }
  const feeType = feetypes.length ? feetypes[0] : {}
  const components = get(feeType, 'expenseSpecification.components')
  const haveTaxAmount = components.find(
    line => line.field === 'taxAmount' && (line.defaultValue.type === 'none' || line.defaultValue.type === 'invoiceSum')
  )
  const haveNoTaxAmount = components.find(
    line =>
      line.field === 'noTaxAmount' && (line.defaultValue.type === 'none' || line.defaultValue.type === 'invoiceSum')
  )
  const haveTaxRate = components.find(line => line.field === 'taxRate')
  if (haveTaxAmount) {
    feeTypeForm.taxAmount = taxAmount
  }
  if (haveTaxRate) {
    feeTypeForm.taxRate = taxRate
  }
  if (haveNoTaxAmount) {
    feeTypeForm.noTaxAmount = noTaxAmount
  }
  let feeDate = setFeeDate(feeType)
  feeTypeForm = formatFeeTypeForm(components, feeDate, feeTypeForm)
  if (feeTypeForm.hasOwnProperty('invoiceForm')) {
    let invoiceForm = {
      invoices: orders,
      type: 'exist'
    }
    feeTypeForm = { ...feeTypeForm, invoiceForm }
  }
  return consumeDetails(feeType, feeTypeForm).then(consume => {
    return consume
  })
}

export async function getObjById(details) {
  const types = ['select', 'dataLink']
  const list = []
  details.map(line => {
    const { feeTypeForm, feeTypeId } = line
    const components = feeTypeId?.expenseSpecification?.components || feeTypeId?.requisitionSpecificationId?.components || []

    components.forEach(item => {

      const type = item.type
      if (types.indexOf(type) >= 0 && feeTypeForm[item.field]) {
        const value = feeTypeForm[item.field]
        isArray(value)
          ? value.forEach(line => {
              list.push({ ref: item.field, id: line })
            })
          : list.push({ ref: item.field, id: value })
      }
    })
  })
  const result = await api.invokeService('@bill:get:metaby:ids', { value: list })
  const map = {}
  result &&
    result.items.forEach(line => {
      map[line.id] = line
    })
  return formatDetailsById(details, map)
}

export function formatDetailsById(details, map) {
  return details.map(line => {
    const { feeTypeId } = line

    const feeTypeForm = { linkDetailEntities: get(line, 'feeTypeForm.linkDetailEntities') }
    const components = feeTypeId?.expenseSpecification?.components || feeTypeId?.requisitionSpecificationId?.components || []
    components.forEach(item => {
      const key = item.field
      if (key) {
        const value = line.feeTypeForm[key]
        let val = value
        if (map[value]) {
          val = isArray(value)
            ? value.map(v => {
                return map[v]
              })
            : map[value]
        }
        if (val) {
          feeTypeForm[key] = val
        }
      }
    })
    return { ...line, feeTypeForm }
  })
}

export function formatDataLinkDataForAssignment(id, template, field) {
  const { assignmentRule } = field
  return api.invokeService('@bill:get:datalink:template:byId', { entityId: id?.id ?? id, type: 'CARD' }).then(res => {
    const data = res.value.data
    return fnUpdateDataLinkData(data, assignmentRule, template)
  })
}

function fnUpdateDataLinkData(data, assignmentRule, template) {
  const { fields = [] } = assignmentRule
  const valueMap = {}
  fields.forEach(item => {
    template.forEach(v => {
      if (item.targetField === v.field) {
        const value = data.dataLink[item.sourceField]
        if (!!value) {
          if (v.type === 'number') {
            valueMap[v.field] = `${value * 1}`
          } else if (v.type === 'dataLink') {
            valueMap[v.field] = { ...value, id: get(value, 'data.dataLink.id') }
          } else {
            valueMap[v.field] = value
          }
        }
      }
    })
  })
  for (const key in valueMap) {
    if (typeof valueMap[key] === 'undefined') {
      delete valueMap[key]
    }
  }
  return valueMap
}
