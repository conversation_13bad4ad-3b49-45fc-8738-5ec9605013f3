import { formatValue } from './autoCalResult'
import { toast } from '../../../lib/util'
import { Resource } from '@ekuaibao/fetch'
import { cloneDeep, get, xor } from 'lodash'
import { isNull, isObject, isString } from '@ekuaibao/helpers'
import { parseShowValue2SaveValue } from '../../../lib/formatDetail'
import { app as api } from '@ekuaibao/whispered'

/**
 * 关联需求: KA-PRD-202111- 补助自动计算规则 2.0
 * https://hose2019.feishu.cn/docs/doccnxiAKVOlaSPkkyPERtcGdfD
 */

function transFormToParam(data: any) {
  const formData = cloneDeep(data)
  formData.submitterId = formData.submitterId?.id ?? formData.submitterId
  formData.travelers = formData?.travelers?.map(v => v?.id ?? v) ?? []
  formData.details = formData.details.map(v => ({
    ...v,
    specificationId: v.specificationId?.id,
    feeTypeId: v.feeTypeId.id
  }))
  formData.specificationId = formData?.specificationId?.id ?? formData.specificationId

  return formData
}

const AutoCalc2 = new Resource('/api/flow/v1/autoRule')
type AutoCalculate2ResultItem = {
  detailId: string
  feeTypeId: string
  fieldName: string
  fieldValue: number
}
function deleteUselessKey(formData: any = {}) {
  const form: any = {}
  Object.keys(formData).forEach((key: string) => {
    if (formData[key] !== undefined && !isNull(formData[key])) {
      form[key] = formData[key]
    }
  })
  return form
}

function getParams(formData: any, billData: any, sourceType: any, billContent: any) {
  const params: any = { submitterId: formData.submitterId.id, isCalculateWrite: false }
  if (sourceType === 'master_') {
    const { details, trips, ...others } = billData
    params.billData = billData
    params.formData = others
  }
  if (sourceType === 'detail_') {
    params.formData = billContent
    params.billData = billData
  }
  if (sourceType === 'trip_') {
    params.billData = billData
    params.formData = billContent
  }
  return params
}

function checkValueChange(
  prevValue: any,
  nextValue: any,
  type: any,
  checkDefaultValue: any,
  component: any,
  entity: any,
  currencyType: any
) {
  const defaultValue = get(component, 'defaultValue.type')
  if (component && component.editable === true && defaultValue === 'formula') {
    return !checkDefaultValue || Boolean(prevValue)
  }

  if (type === 'dateRange') {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return next.start === prev.start && next.end === prev.end
  } else if (type === 'boolean') {
    return String(prevValue) === String(nextValue)
  } else if (type === 'number' || type === 'money' || type === 'date') {
    let prev = isObject(prevValue) && type === 'money' ? parseFloat(prevValue.standard) : parseFloat(prevValue)
    let next = isObject(nextValue) && type === 'money' ? parseFloat(nextValue.standard) : parseFloat(nextValue)
    if (type === 'money' && currencyType === 'FOREIGN') {
      prev = isObject(prevValue) && type === 'money' ? parseFloat(prevValue.foreign) : parseFloat(prevValue)
      next = isObject(nextValue) ? parseFloat(nextValue.foreign) : parseFloat(nextValue)
    }
    return (isNaN(prev) && isNaN(next)) || prev === next
  } else if (entity === 'basedata.city') {
    const next = nextValue ? JSON.parse(nextValue) : []
    const prev = prevValue ? JSON.parse(prevValue) : []
    const nextIds = next.map(v => v.key)
    const prevIds = prev.map(v => v.key)
    return !xor(nextIds, prevIds).length
  } else if (type === 'list' && entity === 'organization.Staff') {
    const next = nextValue ? JSON.parse(nextValue) : []
    const prev = prevValue ? prevValue : []
    const nextIds = next.map(v => v.id)
    const prevIds = prev.map(v => v.id)
    return !xor(nextIds, prevIds).length
  } else if ((type === 'ref' && entity === 'organization.Staff') || entity === 'organization.Department') {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return prev.id === next.id
  } else if (type === 'ref' && isString(entity) && entity.startsWith('basedata.Dimension')) {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return prev.id === next.id
  } else if (entity && entity.includes('datalink.DataLinkEntity')) {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return prev.id === next.id
  } else {
    return prevValue === nextValue
  }
}

export class AutoCalculate2 {
  /**
   * 获取系统计算值的 value ，并非自动计算 value，独立方法出来以便自动计算之后执行系统计算
   * @param formData
   * @param billSpec
   * @param specification
   * @param baseDataProperties
   * @param billContent
   * @param baseDataPropertiesMap
   * @param template
   */
  static async getAutoCal1ResultValue(
    billContent: any,
    billSpec: any,
    specification: any,
    baseDataProperties: any,
    baseDataPropertiesMap: any,
    template: any,
    formData: any
  ) {
    let billData: any = parseShowValue2SaveValue(deleteUselessKey(formData))
    billContent.feeTypeForm = deleteUselessKey(billContent.feeTypeForm)
    formData.details = [billContent]
    billData = parseShowValue2SaveValue(formData)
    billData.specificationId = billSpec.id
    billData.source = 'detail_'

    const params: any = getParams(formData, billData, 'detail_', billContent)
    let results: any
    try {
      const { payload: response } = await api.invokeService('@bill:get:calculationresult', params)
      const { items } = response
      const hasError = items.filter(v => v.errorMsg)
      if (hasError.length > 0) {
        throw { msg: hasError[0].errorMsg }
      }
      results = response.items
    } catch (err) {
      if (err.msg) {
        toast.fail(err.msg)
        // showMessage.error(err.msg)
      }
      return
    }

    return this.formatAutoCalculate1Result(formData, results, baseDataPropertiesMap, false, template)
  }

  /**
   * 转换系统计算值的 value 的格式 ，并非自动计算 value 的内容，独立方法出来以便自动计算之后执行系统计算
   * @param formValue
   * @param results
   * @param baseDataPropertiesMap
   * @param checkDefaultValue
   * @param template
   * @private
   */
  private static formatAutoCalculate1Result(
    formValue: any,
    results: any,
    baseDataPropertiesMap: any,
    checkDefaultValue: boolean,
    template: any
  ) {
    const resultValue: any = {}
    results &&
      results.forEach((element: any) => {
        const { onField, result, dataFrom, loc, resultType, attribute, currencyType, numCode } = element
        const r = result === '' || result === 'null' ? undefined : result // 为了匹配老的自动计算接口奇奇怪怪的返回值
        let { type, entity } = get(baseDataPropertiesMap[onField], 'dataType') || {}
        if (!entity && type === 'list') {
          entity = get(baseDataPropertiesMap[onField], 'dataType.elemType.entity')
        }
        const component = template.find((v: any) => {
          return onField === v.name
        })
        if (resultType === 'VALUE') {
          if (dataFrom === 'details') {
            const { details = [] } = formValue
            const currentDetail = details[loc]
            const { specificationId, feeTypeForm } = currentDetail

            const feeTypeComponent = specificationId.components.find((v: any) => {
              return onField === v.field
            })
            const defaultValue = get(feeTypeComponent, 'defaultValue.type')
            const originalValue = typeof feeTypeForm[onField] === 'object' ? feeTypeForm[onField]?.id : feeTypeForm[onField]
            feeTypeForm[onField] =
              feeTypeComponent.editable && defaultValue === 'formula'
                ? feeTypeForm[onField]
                : formatValue(type, entity, r, feeTypeComponent, feeTypeForm[onField])
            const changeOnFieldValue = typeof feeTypeForm[onField] === 'object' ? feeTypeForm[onField]?.id : feeTypeForm[onField]
            if (
              feeTypeComponent.type === 'dataLink' &&
              feeTypeComponent.isLinkageAssignment &&
              !feeTypeComponent.editable &&
              originalValue !== changeOnFieldValue
            ) {
              feeTypeForm[onField] = originalValue
              currentDetail.errorMsg = {}
              const label = feeTypeComponent?.label || feeTypeComponent?.labelCopy
              currentDetail.errorMsg['needUpdate'] = i18n.get(`{__k0}的值发生变化,请重新保存该条明细`, {
                __k0: label
              })
              currentDetail.needUpdate = true
            }
            resultValue.details = details
          } else if (
            component &&
            !checkValueChange(formValue[onField], r, type, checkDefaultValue, component, entity, currencyType)
          ) {
            resultValue[onField] = formatValue(type, entity, r, component, formValue[onField], {
              currencyType,
              numCode
            })
          }
        }
      })
    return resultValue
  }

  static async calc(form: any): Promise<AutoCalculate2ResultItem[]> {
    const { details } = form
    const { items: result = [] }: { items: AutoCalculate2ResultItem[] } = await AutoCalc2.POST(`/cal`, {
      ...parseShowValue2SaveValue(form),
      specificationId: form.specificationId.id
    })

    const resultFeeTypeAndFieldNameList = result.map(item => {
      const targetDetail = details.find((v: any) => item.detailId === v.feeTypeForm.detailId)
      return {
        ...item,
        detail: targetDetail
      }
    })

    return resultFeeTypeAndFieldNameList.filter(resultItem => {
      const { detail, fieldName } = resultItem

      const targetField = detail.specificationId.components.find((component: any) => component.field === fieldName)
      if (!targetField) {
        return false
      }
      switch (targetField.defaultValue?.type ?? '') {
        case 'constant':
        case 'none':
          return true
        default:
          return false
      }
    })
    // return AutoCalc2.POST('/cal', form)
  }
}
