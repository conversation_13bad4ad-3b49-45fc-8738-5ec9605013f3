import { app } from '@ekuaibao/whispered'
import React from 'react'
import moment from 'moment'
import { cloneDeep, get } from 'lodash'
import { getContentByLocale } from '@ekuaibao/lib/lib/i18n-helpers'

import PersonnelList from '../elements/datalink/datalink-detail-modal/elements/PersonnelList'
import { formatLinkText, renderRedirectCode } from '../../../components/dynamic/inner/dataLinkUtil'
import { getStaffShowByConfig } from '../../../components/utils/fnDataLinkUtil'

export function formatEntityList(fields, entityList) {
  const { formatEntityItem } = app.require('@components/utils/fnDataLinkUtil')
  let cEntityList = entityList.slice()
  cEntityList.forEach(line => {
    formatEntityItem(line, fields)
  })
  return cEntityList
}

export function getNameCodeSearchKey(entityList = []) {
  if (entityList.length === 0) {
    return
  }

  const { getStrLastWord } = app.require('@components/utils/fnDataLinkUtil')

  let form = entityList[0].form
  let nameKey = '',
    codeKey = '',
    formKey = '',
    toKey = ''
  for (let key in form) {
    if (getStrLastWord(key, '_') === 'name') {
      nameKey = key
    } else if (getStrLastWord(key, '_') === 'code') {
      codeKey = key
      // @i18n-ignore
    } else if (getStrLastWord(key, '_') === '出发地') {
      formKey = key
      // @i18n-ignore
    } else if (getStrLastWord(key, '_') === '目的地') {
      toKey = key
    }
  }
  return { nameKey, codeKey, formKey, toKey }
}

export function filterDataLinkUseCount(map, dataLinkList) {
  if (!map || Object.keys(map).length === 0) {
    return dataLinkList
  }
  return dataLinkList.filter(line => {
    let newLine = line
    if (line.dataLink) {
      newLine = line.dataLink
    }
    const flowCount = newLine.flowCount || 0
    let count = map[newLine.id] || 0
    let availableCount = newLine.totalCount - newLine.useCount + flowCount - count
    newLine.useCount = newLine.totalCount - availableCount
    return availableCount > 0
  })
}

function generateList(value) {
  const { staff = [], role = [], department = [] } = cloneDeep(value)
  staff.forEach(item => (item.type = 'staff'))
  role.forEach(item => (item.type = 'role'))
  department.forEach(item => (item.type = 'department'))
  return [...staff, ...role, ...department]
}

export function formatFieldShowByType(field, value, planned, entity, isOwner = false, handleEditParticipant) {
  if (field.name && field.name.indexOf('visibility') > -1) {
    if (!value) {
      return '-'
    }
    if (value.fullVisible) {
      return i18n.get('全员可见')
    }
    const arr = generateList(value)
    if (arr.length === 0) {
      return '-'
    }
    return (
      <>
        <PersonnelList dataSource={arr} maxShowNum={2} title={i18n.get('参与人')} />
        {isOwner && (
          <a
            className="edit-participant"
            onClick={() => {
              handleEditParticipant?.()
            }}
          >
            编辑
          </a>
        )}
      </>
    )
  }

  if (field.name && (field.name === 'active' || field.name === 'dataLink.active')) {
    return value ? i18n.get('启用中') : i18n.get('已停用')
  }

  let showWarnning = +value > 100
  const plannedValue = get(planned, field.field)
  if (plannedValue) {
    const { fnShouldShowWarning } = app.require('@components/utils/fnDataLinkUtil')
    showWarnning = fnShouldShowWarning(value, plannedValue)
  }

  if (/percentage$/i.test(field.field || field.name) && showWarnning) {
    return (
      <span style={{ color: 'rgb(245, 34, 45)' }}>
        {value}
        {field.unit || ''}
      </span>
    )
  }
  let formatStr = 'YYYY/MM/DD'
  switch (field.type) {
    case 'date':
      formatStr = field.withTime ? 'YYYY/MM/DD HH:mm' : 'YYYY/MM/DD '
      return value ? formatDate(value, formatStr) : '-'
    case 'Currency':
      return value ? `${value.name}(${value.strCode})` : '-'
    case 'dateRange':
      if (!value) {
        return '-'
      }
      let { start, end } = value
      formatStr = field.withTime ? 'YYYY/MM/DD HH:mm' : 'YYYY/MM/DD '
      return formatDate(start, formatStr) + '~' + formatDate(end, formatStr)
    case 'switcher':
      return value === undefined ? '-' : value ? i18n.get('是') : i18n.get('否')
    case 'dimension':
      return getContentByLocale(value, 'name') || '-'
    case 'list':
      const { elemType, label, labelCopy } = field
      if (get(elemType, 'type') === 'ref' && get(elemType, 'entity') === 'organization.Staff') {
        return <PersonnelList dataSource={value} maxShowNum={2} title={labelCopy || label} />
      } else if (get(elemType, 'type') === 'attachment') {
        return value
      } else {
        return i18n.get('无法解析')
      }
    case 'city':
      return value
        ? JSON.parse(value)
          .map(o => o.label)
          .join(',')
        : '-'
    case 'Staff':
      return value ? getStaffShowByConfig(value) : '-'
    case 'text': //行程里面的原始单据type是text类型但是返回的是一个单据对象
      const dom = renderRedirectCode(field.field, entity, value)
      if (dom) {
        return dom
      }
      const textFieldValue = value ? (typeof value === 'object' ? get(value, 'form.title', '-') : value) : '-'
      const { domValue } = formatLinkText(textFieldValue, field.name)
      return domValue
    default:
      return value ? value + (field.unit || '') : '-'
  }
}

export function formatData(data, value) {
  const { fields } = value
  const { getStrLastWord } = app.require('@components/utils/fnDataLinkUtil')
  const list = formatEntityListAll(fields, data.form, false)
  const fromValue = list.find(item => getStrLastWord(item.name, '_') === '出发地') // @i18n-ignore
  const to = list.find(item => getStrLastWord(item.name, '_') === '目的地') // @i18n-ignore
  const date = list.find(item => getStrLastWord(item.name, '_') === '行驶日期') // @i18n-ignore
  const money = list.find(item => !!~getStrLastWord(item.name, '_').search('补助金额')) // @i18n-ignore
  const actualMiles = list.find(item => !!~getStrLastWord(item.name, '_').search('实际里程')) || {} // @i18n-ignore
  const cause = list.find(item => !!~getStrLastWord(item.name, '_').search('事由')) || {} // @i18n-ignore
  const wayPoints = list.find(item => !!~getStrLastWord(item.name, '_').search('途经地')) || {} // @i18n-ignore
  const isAuto = list.find(item => !!~getStrLastWord(item.name, '_').search('记录方式'))?.content === '自动打点' || false


  return {
    orderType: 'car',
    form: {
      departureName: fromValue?.content?.name,
      departure: fromValue?.content?.address,
      destinationName: to?.content?.name,
      destination: to?.content?.address,
      departureTime: date.content,
      actualPrice: money.content.standard,
      actualMiles: actualMiles.content || 0,
      cause: cause.content || '',
      wayPoints: wayPoints?.content || [],
      isAuto: isAuto
    }
  }
}

export function formatEntityListAll(fields, value, format = true) {
  if (!fields || !value) {
    return []
  }
  const { thousandBitSeparator } = app.require('@components/utils/fnThousandBitSeparator')
  let cEntityList = []
  fields.forEach(v => {
    let formatStr = value[v.name]
    let item = {}
    item.label = v.label
    item.name = v.name
    if (format) {
      switch (v.type) {
        case 'money':
          item.content = formatStr.standardSymbol + thousandBitSeparator(formatStr.standard)
          break
        case 'date':
          item.content = formatStr
            ? v.withTime
              ? moment(formatStr).format('YYYY-MM-DD HH:mm')
              : moment(formatStr).format('YYYY-MM-DD')
            : '-'
          break
        case 'dateRange':
          item.content = formatStr ? fnGetDateRangeFormate(v.withTime, formatStr.start, formatStr.end) : '-'
          break
        case 'switcher':
          item.content = formatStr ? i18n.get('是') : i18n.get('否')
          break
        case 'duration':
          let m = Math.ceil((formatStr % 3600000) / 60000)
          let h = Math.floor(formatStr / 3600000)
          if (h > 0) {
            item.content = i18n.get(`{__k0}时{__k1}分`, { __k0: h, __k1: m })
          } else {
            item.content = i18n.get(`{__k0}分`, { __k0: m })
          }
          break
        default:
          item.content = formatStr ? formatStr : ''
      }
    } else {
      item.content = formatStr ? formatStr : ''
    }
    cEntityList.push(item)
  })
  return cEntityList
}

export function fnGetDateRangeFormate(withTime, start, end) {
  return withTime
    ? moment(Number(start)).format('YYYY/MM/DD HH:mm') + '~' + moment(Number(end)).format('YYYY/MM/DD HH:mm')
    : moment(Number(start)).format('YYYY/MM/DD') + '~' + moment(Number(end)).format('YYYY/MM/DD')
}

function formatDate(value, formatStr) {
  return moment(value).format(formatStr)
}

/**
 * 生成一个用不重复的ID,将生成 rfmipbs8ag0kgkcogc 类似的ID
 */
export function genNonDuplicateID(randomLength = 6) {
  return (
    'app' +
    Number(
      Math.random()
        .toString()
        .substr(3, randomLength) + Date.now()
    ).toString(36)
  )
}

export * from './dataLinkUtils.openDataLinkEntityList'

export * from './dataLinkUtils.openMultiSelectDataLinkEntityList'

function filterDataLink(components) {
  return components.filter(line => line.type === 'dataLink')
}

export function handleHeaderFields(template, values, map) {
  const dataLinkFields = filterDataLink(template)
  dataLinkFields.forEach(line => {
    if (values[line.field]) {
      map[values[line.field].id] = 1
    }
  })
}

export function isEntity(value) {
  if (__DEV__ && value && Array.isArray(value.data)) {
    value.data = value.data[0]
  }
  return value && value.data && value.data.dataLink && value.data.dataLink.id && value.template
}

export function getId(value) {
  return value ? (isEntity(value) ? value.data.dataLink.id : typeof value === 'string' ? value : value.id) : void 0
}