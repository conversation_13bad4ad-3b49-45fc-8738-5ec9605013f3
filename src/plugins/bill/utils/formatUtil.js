/**************************************************
 * Created by nany<PERSON>ingfeng on 04/08/2017 14:49.
 **************************************************/
import sortBy from 'lodash/sortBy'
import get from 'lodash/get'
import { app as api } from '@ekuaibao/whispered'
import { Dialog } from '@hose/eui-mobile'
import { getV } from '../../../lib/help'
import moment from 'moment'
import {
  isPlainObject,
  isPlainObjectList,
  isDataLinkEdits,
  parseEmptyText,
  getPureValue,
  parseShowValue2SaveValue,
  getPureDetailsValue,
  getPureTripsValue,
  filterParamByComponent
} from '../../../lib/formatDetail'
import { uuid } from '@ekuaibao/helpers'
import { nanoid } from 'nanoid'
import { getBoolVariation } from '../../../lib/featbit'
export {
  isPlainObject,
  isPlainObjectList,
  isDataLinkEdits,
  parseEmptyText,
  getPureValue,
  parseShowValue2SaveValue,
  getPureDetailsValue,
  getPureTripsValue,
  filterParamByComponent
}

export function getFlowPlanConfigId(specification) {
  let { configs } = specification
  let flow = configs.find(line => line.ability === 'flow')
  return flow.flowPlanConfigId
}

//消费明细按照时间排序。规则是按照消费日期升序，如果没有消费日期，则取起止日期的开始日期。如果前两者均没有，则按照添加顺序排在列表底部
export function sortFeeTypeForm(details = [], orderByType = 'INPUT') {
  if (orderByType === 'INPUT') {
    if (details && details.length > 1) {
      return sortBy(details, [
        el => {
          return el.feeTypeForm?.feeDate || (el.feeTypeForm?.feeDatePeriod && el.feeTypeForm.feeDatePeriod?.start)
        }
      ])
    }
  }
  return details
}

//提交或者修改单据的时候,删除data.form.details.feeTypeForm.thirdPartyOrders
export function delThirdOrders(detailsArr, alterFlag) {
  const datalinkIds = []
  if (detailsArr && detailsArr.length > 0) {
    detailsArr.forEach(el => {
      el.feeTypeForm.thirdPartyOrders && delete el.feeTypeForm.thirdPartyOrders
      //变更单据，保存和提交时  明细的行程当有id 重复时删除
      if (el?.feeTypeForm?.['u_行程规划']?.length > 0 && alterFlag && Number(alterFlag) && Number(alterFlag) >= 1) {
        const arr = el?.feeTypeForm?.['u_行程规划']
        arr?.forEach(u => {
          if (u?.dataLinkId) {
            if (datalinkIds.includes(u.dataLinkId)) {
              u.dataLinkId = ''
            } else {
              datalinkIds.push(u.dataLinkId)
            }
          }
        })
      }
    })
  }
  return detailsArr
}

export function formatInvoiceToDetail({ invoiceData, relationshipMap, originalIeDatas }) {
  let item = {}
  for (let key in invoiceData) {
    const values = relationshipMap[key]
    if (Array.isArray(values)) {
      values.forEach(v => {
        if (v.type === 'dateRange') {
          item[v.field] = { start: invoiceData[key], end: invoiceData[key] }
        } else {
          item[v.field] = invoiceData[key]
        }
      })
    } else {
      item[relationshipMap[key]] = invoiceData[key]
    }
  }
  if (originalIeDatas) {
    const { E_system_发票主体_发票日期, E_system_发票主体_发票类别 } = originalIeDatas
    if (E_system_发票主体_发票日期) {
      item['feeDate'] = E_system_发票主体_发票日期
    }
    if (E_system_发票主体_发票类别) {
      item['invoiceType'] = E_system_发票主体_发票类别
    }
  }
  return item
}

export function confirmCopy(dataSource) {
  return new Promise((resolve, reject) => {
    const formType = get(dataSource, 'form.specificationId.type', '')
    if (formType === 'expense') {
      Dialog.confirm({
        title: i18n.get('温馨提示'),
        content: i18n.get(
          '复制生成的新报销单，将不会携带原消费记录中的卡片信息，如：发票、滴滴、易快报自营等第三方卡片信息。如有疑问请找小易咨询'
        ),
        onCancel: () => reject(),
        onConfirm: () => resolve()
      })
    } else {
      resolve()
    }
  })
}

//删除form中的数据互联相关字段
function deleteKeysForCopy(value, specification, keys = []) {
  const { components } = specification
  components.forEach(component => {
    if (isNeedDel(component)) keys.push(component.field)
  })
  keys.forEach(delkey => {
    delete value[delkey]
  })
  return value
}

function isNeedDel(component) {
  const { type, defaultValue, editable } = component
  const value = get(defaultValue, 'value', '')
  const isOfficialCardSettlement = defaultValue?.type === 'officialCardSettlement'
  return (
    type === 'dataLink' ||
    (value === 'submit.requisition' && !editable) ||
    type === 'dataLinkEdits' ||
    isOfficialCardSettlement
  )
}

//复制单据时的过滤方法
export async function formatFormForCopy(form) {
  const deleteKeys = [
    'expenseLink',
    'expenseLinks',
    'linkRequisitionInfo',
    'writtenOffMoney',
    'expenseDate',
    'submitDate',
    'requisitionDate',
    'loanDate',
    'repaymentDate'
  ]
  let { specificationId: specification, travelPlanning = [] } = form

  // 在复制行程数据中删除行程中原有id
  form.travelPlanning = travelPlanning.map(item => {
    item.travelId = null
    return item
  })
  delete form.code
  // 更新支付计划的id
  if (form.payPlan && form.payPlan.length) {
    form.payPlan = form.payPlan.map(el => {
      el.dataLinkId = uuid(14)
      if (!form?.paymentPlanByApportion && el.dataLinkForm?.E_system_支付计划_legalEntity) {
        delete el.dataLinkForm.E_system_支付计划_legalEntity
      }
      return el
    })
  }
  //过滤form中的实例类型的值
  form = deleteKeysForCopy(form, specification, deleteKeys)
  form = await checkSelectValue(form)
  let allCurrency = []
  if (form?.legalEntityMultiCurrency && form?.legalEntityMultiCurrency?.form?.baseCurrencyId) {
    const { baseCurrencyId } = form?.legalEntityMultiCurrency?.form
    const { items: rates = [] } = await api.invokeService('@common:get:currency:rates:by:id', baseCurrencyId)
    allCurrency = rates
  } else {
    allCurrency = await api.dataLoader('@common.allCurrencyRates').reload()
  }
  const formInvoiceType = get(form, 'invoiceForm.type')
  if (formInvoiceType === 'exist') {
    //通过导入发票生成的明细
    const components = get(specification, 'components', [])
    const invoiceFormField = components.find(el => el.field === 'invoiceForm')
    const { editable, defaultValue } = invoiceFormField
    if (!editable) {
      form.invoiceForm = { type: defaultValue.value }
    } else {
      form.invoiceForm = { type: formInvoiceType }
    }
    const taxs = components.filter(line => get(line, 'defaultValue.type') === 'invoiceSum').map(line => line.field)
    taxs.forEach(key => {
      delete form[key]
    })
  }
  //过滤明细中的实例类型的值，删除明细id
  const detailKey = form.details ? 'details' : form.trips ? 'trips' : undefined

  if (detailKey) {
    let arr = []
    const formKey = detailKey ? (detailKey === 'details' ? 'feeTypeForm' : 'tripForm') : undefined

    form[detailKey].forEach(el => {
      const { specificationId } = el
      if (detailKey === 'details') {
        el.feeTypeForm.detailId = nanoid()
        // 复制时重新生成分摊id
        const apportions = getV(el, 'feeTypeForm.apportions', [])
        if (apportions.length) {
          apportions.forEach(el => {
            el.apportionForm['apportionId'] = 'ID_' + uuid(11)
          })
        }

        const travelPlanning = getV(el, 'feeTypeForm.travelPlanning', [])
        if (travelPlanning.length) {
          travelPlanning.forEach(el => {
            el.travelId = null
          })
        }

        // 复制时重新生成摊销id
        const amortizes = getV(el, 'feeTypeForm.amortizes', [])
        if (amortizes.length) {
          amortizes.forEach(el => {
            el.apportionForm['amortizeId'] = 'ID_' + uuid(11)
          })
        }
        const invoiceType = get(el[formKey], 'invoiceForm.type')
        //以下信息，不在复制单据时带入草稿

        const { components } = specificationId
        const dateFields = components.filter(el => el.type === 'date')
        dateFields.forEach(field => {
          const { defaultValue, withTime } = field
          if (defaultValue && defaultValue.value === 'submit.date') {
            el.feeTypeForm[field.field] = withTime
              ? moment(moment().format('YYYY/MM/DD HH:mm')).valueOf()
              : moment(moment().format('YYYY/MM/DD')).valueOf()
          }
        })

        if (el.feeTypeForm.linkDetailEntities) {
          // 通过导入关联明细生成的费用明细上的关联明细
          delete el.feeTypeForm.linkDetailEntities
        }
        if (invoiceType === 'exist') {
          //通过导入发票生成的明细
          const components = get(specificationId, 'components', [])
          const invoiceFormField = components.find(el => el.field === 'invoiceForm')
          const { editable, defaultValue } = invoiceFormField
          if (!editable) {
            el[formKey].invoiceForm = { type: defaultValue.value }
          } else {
            el[formKey].invoiceForm = { type: invoiceType }
          }
          const taxs = components
            .filter(line => get(line, 'defaultValue.type') === 'invoiceSum')
            .map(line => line.field)
          taxs.forEach(key => {
            delete el.feeTypeForm[key]
          })
        }
        if (
          el[formKey].thirdPartyOrders || //通过导入中航易购生成的明细
          el[formKey].ordersData //通过导入易快报自营业务生成的明细（企业消费，公务卡等）
        ) {
          delete el[formKey].thirdPartyOrders
          delete el[formKey].ordersData
          delete el[formKey].orders
        }
        //为费用明细中的预置日期字段重新赋值
        if (el[formKey].feeDate) {
          el[formKey].feeDate = new Date().getTime()
        }
      } else {
        delete el.tripForm.tripId
      }
      el[formKey] = deleteKeysForCopy(el[formKey], specificationId)
      arr.push(formatFormAmount(el, allCurrency, 'feeTypeForm'))
    })
    form[detailKey] = arr
  }
  return formatFormAmount(form, allCurrency)
}

/**
 * 对数据中的金额字段格式化，过滤出所有金额字段
 * @param data 需要处理的数据
 * @param allCurrency: 系统中所有已配置的外币币种
 * @param valuePath: 对应的数据路径，默认传进来的data就是要处理的数据
 * @returns {*}
 */
function formatFormAmount(data, allCurrency, valuePath = '') {
  const components = getV(data, 'specificationId.components', [])
  if (!components.length) return data
  const moneyComponents = components.filter(item => {
    const type = getV(item, 'type', '')
    return type === 'money'
  })
  if (!moneyComponents.length) return data
  let value = !!valuePath ? data[valuePath] : data
  moneyComponents.forEach(item => {
    const field = getV(item, 'field', '')
    if (value) {
      value[field] = formatAmount(value[field], allCurrency)
    }
  })
  !!valuePath ? (data[valuePath] = value) : (data = value)
  return data
}

/**
 * 格式化金额：复制过来的金额中的税率和系统配置的外币的税率不一样的时候，按照系统配置的税率重新计算此金额中对应的本位币金额
 * @param amount 需要格式化的币种
 * @param allCurrency 系统中所有已配置的外币币种
 * @returns {*}
 */
function formatAmount(amount, allCurrency) {
  const foreignStrCode = getV(amount, 'foreignStrCode', '')
  const rate = getV(amount, 'rate', '')
  if (!foreignStrCode || !rate) return amount
  if (!allCurrency || !allCurrency.length) return amount
  const currency = allCurrency.find(item => item.strCode === foreignStrCode)
  const currencyRate = getV(currency, 'rate', 1)
  if (rate === currencyRate) return amount
  const foreign = getV(amount, 'foreign', 0)
  const standardScale = getV(amount, 'standardScale', 2)
  const result = { ...amount }
  result.rate = currencyRate
  result.sysRate = currencyRate
  result.standard = new Big(foreign).times(currencyRate).toFixed(Number(standardScale))
  return result
}

const blackList = ['invoiceType', 'submitterId', 'expenseLink', 'expenseLinks']

function filterItem(item) {
  return item.type === 'select' && !blackList.includes(item.field) && item.selectRange === 'leaf'
}

async function checkSelectValue(value) {
  const components = getV(value, 'specificationId.components', [])
  if (!components.length) return value
  const selectAndLeafComponents = components.filter(filterItem)
  const needCheckValueKeys = []
  selectAndLeafComponents.forEach(item => {
    const { field } = item
    if (Object.keys(value).includes(field)) {
      needCheckValueKeys.push(field)
    }
  })
  if (!!needCheckValueKeys.length) {
    const params = []
    needCheckValueKeys.forEach(key => {
      const dimension = value[key]
      const id = getV(dimension, 'id') || dimension
      params.push({
        key,
        id
      })
    })
    const result = await api.invokeService(
      '@bill:check:Dimensions:isLeaf',
      params.map(item => item.id)
    )
    const notLeaf = result.filter(item => !item.isLeaf)
    if (notLeaf.length) {
      const ids = notLeaf.map(item => item.id)
      const needDelKey = params.filter(item => ids.includes(item.id)).map(item => item.key)
      needDelKey.forEach(delkey => {
        delete value[delkey]
      })
    }
  }
  return value
}

export const fnFormatMoneyValue = ({ data, specification }) => {
  if (!data || !specification) {
    return data
  }
  if (!getBoolVariation('cyxq-76627')) {
    return data
  }
  fnCheckComponentMoneyValue(specification, data.form || data)
  const details = data?.details || data?.form?.details || []
  if (details.length) {
    details.forEach(item => {
      const { feeTypeForm, specificationId } = item
      if (feeTypeForm && specificationId) {
        fnCheckComponentMoneyValue(specificationId, feeTypeForm)
      }
    })
  }
  return data
}

const fnCheckComponentMoneyValue = (specification, form) => {
  const { components } = specification
  const moneyFields = components.filter(c => c.type === 'money')
  if (moneyFields.length) {
    moneyFields.forEach(component => {
      const { field } = component
      const value = form[field]
      if (value) {
        form[field] = fnCheckMoneyValue(value)
      }
    })
  }
}

export const fnCheckMoneyValue = moneyValue => {
  if (!moneyValue) {
    return moneyValue
  }
  const { foreignNumCode } = moneyValue
  if (foreignNumCode === undefined) {
    delete moneyValue.rate
  }
  return moneyValue
}
