/**
 *  Created by gym on 2018/11/12 下午2:35.
 */
import get from 'lodash/get'

export function resetDetailsExternalsData(args) {
  const { apportionId, detailId, field, external, bus, handleRiskNoticeChange } = args
  const details = get(external, 'form.details')
  if (!details) {
    return
  }
  if (apportionId) {
    //修改分摊页面风险
    Object.keys(details[detailId]).forEach(key => {
      delete details[detailId][key][apportionId]
      !Object.keys(details[detailId][key]).length && delete details[detailId][key]
      handleDeleteRiskNotices(details, detailId, external)
    })
  } else {
    //修改详情页面风险
    if (field) {
      delete details[detailId][field]
      handleDeleteRiskNotices(details, detailId, external)
    } else {
      delete external.form.details[detailId]
      isDeleteFatherObj(details, external)
    }
  }
  risksPublicFunc({ external, bus, handleRiskNoticeChange })
}

function handleDeleteRiskNotices(details, detailId, external) {
  !Object.keys(details[detailId]).length && delete details[detailId]
  isDeleteFatherObj(details, external)
}

function isDeleteFatherObj(details, external) {
  !Object.keys(details).length && delete external.form.details
  !Object.values(external.form).length && delete external.form
}

export function resetFormExternalsData(args, external, bus, handleRiskNoticeChange) {
  const { fieldName } = args
  if (external && external.form && external.form[fieldName]) {
    delete external.form[fieldName]
    !Object.values(external.form).length && delete external.form
    risksPublicFunc({ external, bus, handleRiskNoticeChange })
  }
}

export function resetTripsExternalsData(tripId, external, bus, handleRiskNoticeChange) {
  let trips = get(external, 'form.trips')
  if (trips && trips[tripId]) {
    delete external.form.trips[tripId]
    !Object.keys(external.form.trips).length && delete external.form.trips
    !Object.keys(external.form).length && delete external.form
    risksPublicFunc({ external, bus, handleRiskNoticeChange })
  }
}

export function deleteDetailItemExternalsData(args) {
  //删除detail整项
  const { detailIdList, external, bus, handleRiskNoticeChange } = args
  let details = get(external, 'form.details')
  if (!details || !detailIdList.length) return
  detailIdList.forEach(item => {
    if (details[item] && Object.keys(details[item]).length) {
      delete details[item]
      isDeleteFatherObj(details, external)
      risksPublicFunc({ external, bus, handleRiskNoticeChange })
    }
  })
}

function risksPublicFunc(props) {
  const { bus, handleRiskNoticeChange, external } = props
  external && bus.setFieldsExternalsData?.({ ...external.form })
  handleRiskNoticeChange?.(external)
}

export function isTicketReview(specification) {
  const configs = get(specification, 'configs', [])
  const config = configs.find(oo => ['expense', 'reimbursement'].includes(oo.ability) && oo.isTicketReview)
  return config ? config : false
}
