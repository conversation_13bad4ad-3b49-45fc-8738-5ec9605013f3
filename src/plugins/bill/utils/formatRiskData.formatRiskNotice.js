import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { cloneDeep, uniqBy, flatten, get} from 'lodash'
import { global_blackList } from '../../../lib/util'
import { isErrorItem } from '../../../elements/puppet/RiskNotice/ai-audit-result/utils'

const riskTypeMap = () => ({
  budget: i18n.get('「预算」'),
  costControl: i18n.get('「费用标准」'),
  dataLinkLedger: i18n.get('「台账标准」'),
  invoice: i18n.get('「发票」')
})

export function formatRiskNotice(data) {
  const { riskData ,RiskPromptOptimization,isFeeDetail=false, isForbid=false } = data
  let noticeList = []
  if (!riskData || !Object.keys(riskData).length) return noticeList
  let noticeData = riskData.form ? riskData.form : riskData
  return fnPublicRisks({ ...data, noticeList, riskData: noticeData, RiskPromptOptimization,isFeeDetail, isForbid })
}

export default formatRiskNotice

function fnPublicRisks(args) {
  const { noticeList, riskData, components, globalFieldsMap,RiskPromptOptimization,isFeeDetail, isForbid } = args
  const fields = components.map(c => c.field)
  const extraFields = Object.keys(riskData).filter(o => !~fields.indexOf(o))
  controlFieldIsNull({ riskData, noticeList, ...args }) //报错字段为空
  controlFieldNoFindTemp({ noticeList, riskData, globalFieldsMap, extraFields }) //报错字段在模板上找不到字段承载
  controlFieldInTemp({ noticeList, riskData, fields, extraFields,RiskPromptOptimization,isFeeDetail,isForbid}) //字段上报错
  return noticeList
}

function controlFieldIsNull(args) {
  const { riskData, noticeList, submitter, bus, isBudgetClick = true } = args
  Object.keys(riskData).forEach(key => {
    if (key === '') {
      riskData[key].forEach(vv => {
        let text = vv.messages[0]
        // @i18n-ignore
        if(IS_SZJL && (text === '有可核销借款但未核销' || text === 'Have a write-off of the loan but have not written off') && (vv.type === 'loan')){
          text = i18n.get('有可核销的借款或预付款尚未核销')
        }
        if (vv.type === 'budget') {
          noticeList.push(
            isBudgetClick
              ? _newNotice(text, () => {
                  bus.emit('element:click:message:panel', 'budget')
                })
              : _newNotice(text)
          )
        } else if (vv.type === 'loan') {
          noticeList.push(
            _newNotice(`${submitter.name} ${text}`, () => {
              bus.emit('element:click:message:panel', 'loan')
            })
          )
        } else if (vv.type === 'flowQuery') {
          noticeList.push({ content: text, type: vv.type })
        } else {
          noticeList.push({ content: text })
        }
      })
    }
  })
}

function _newNotice(content, onClick) {
  return onClick ? { content: content, onClick: onClick } : { content: content }
}

function controlFieldNoFindTemp(args) {
  const { noticeList, riskData, globalFieldsMap, extraFields } = args
  extraFields.forEach(item => {
    if (!!~global_blackList.indexOf(item)) {
      let str = {}
      let oo = globalFieldsMap[item]
      str.label = oo.label
      str.value = riskData[item]
      noticeList.push({ content: riskNoticeNofindTemp(str) })
    }
  })
}

function creatList(data) {
  const list = []
  const loop = function(obj) {
    Object.values(obj).forEach(v => {
      Array.isArray(v) ? list.push(v) : loop(v)
    })
  }
  Object.values(data).forEach(v => {
    Array.isArray(v) ? list.push(v) : loop(v)
  })
  return list
}

function getRiskTypeInTemp({ riskData = {}, extraFields = [],RiskPromptOptimization,isFeeDetail }) {
  // 以后新添加在字段上展示的风险类型，首先在riskTypeMap注入，然后在词条中补充
  const cRiskData = cloneDeep(riskData)
  extraFields.forEach(v => {
    delete cRiskData[v]
  })
  const riskTypeInTempObj = {}
  const riskInTempList = creatList(cRiskData)
  uniqBy(flatten(riskInTempList), 'type').forEach(v => {
    riskTypeInTempObj[v.type] = riskTypeMap()[v.type]
  })
  Object.keys(riskTypeMap()).forEach(v => {
    !riskTypeInTempObj[v] && (riskTypeInTempObj[v] = '')
  })

  // 风险提示优化
  if(RiskPromptOptimization){
    let budgetNum =0
    let costControlNum =0
    let dataLinkLedgerNum =0
    let invoiceNum =0
    // let noticeData = riskData.form ? riskData.form : riskData
    // Object.keys(noticeData).forEach(key => {
    //   if(key === ''){
    //     riskInTempList.push(noticeData[key])
    //   }
    // })
    riskInTempList && riskInTempList?.forEach((v,idx) => {
      const field = get(v[0], 'controlField')
      //field为invoiceForm，都是invoice风险，但是field为amount,就存在几种情况了……禁声
      if(field === 'amount' && v.length>1){
        v.forEach((el,idx) =>{
          const type = get(el, 'type')
          switch (type) {
            case 'budget':
              budgetNum++
              break
            case 'costControl':
              costControlNum++
              break
            case 'dataLinkLedger':
              dataLinkLedgerNum++
              break
            case 'invoice':
              !isFeeDetail && invoiceNum++
              break
          }
        })
      }else{
        const type = get(v[0], 'type') || get(v,'type')
        if(type==='invoice' && isFeeDetail){
          if(v.length>1){
            v.forEach((el,idx)=>{
              invoiceNum++
            })
          }else{
            invoiceNum++
          }
        }
        switch (type) {
          case 'budget':
            budgetNum++
            break
          case 'costControl':
            costControlNum++
            break
          case 'dataLinkLedger':
            dataLinkLedgerNum++
            break
          case 'invoice':
            !isFeeDetail && invoiceNum++
            break
        }
      }
    })
    if(budgetNum>0){
      const newTxt = riskTypeInTempObj.budget?.split('」')[0]
      riskTypeInTempObj.budget = newTxt + i18n.get(`${budgetNum}处」`)
    }
    if(costControlNum>0){
      const newTxt = riskTypeInTempObj.costControl?.split('」')[0]
      riskTypeInTempObj.costControl = newTxt + i18n.get(`${costControlNum}处」`)
    }
    if(dataLinkLedgerNum>0){
      const newTxt = riskTypeInTempObj.dataLinkLedger?.split('」')[0]
      riskTypeInTempObj.dataLinkLedger = newTxt + i18n.get(`${dataLinkLedgerNum}处」`)
    }
    if(invoiceNum>0){
      const newTxt = riskTypeInTempObj.invoice?.split('」')[0]
      riskTypeInTempObj.invoice = newTxt + i18n.get(`${invoiceNum}处」`)
    }
  }
  return riskTypeInTempObj
}

function controlFieldInTemp(args) {
  const { noticeList, riskData, fields, extraFields,RiskPromptOptimization,isFeeDetail,isForbid } = args
  const riskTypeInTempObj = getRiskTypeInTemp({ riskData, extraFields, RiskPromptOptimization,isFeeDetail})
  const isExistFields = Object.keys(riskData).filter(o => !!~fields.indexOf(o))
  let fieldList = []
  if (isExistFields.length) {
    getRiskFieldNum(fieldList, riskData)
    const isRiskError = isForbid && fnIsRiskError(riskData)
    let count = isFeeDetail ? flatten(fieldList).length : fieldList.length - extraFields.length
    let text = noticeList.length
      ? i18n.get(!RiskPromptOptimization ? '还有{__k0}处风险已标注在字段上':'还有label风险已标注在字段上', { __k0: count, ...riskTypeInTempObj })
      : i18n.get(!RiskPromptOptimization ? `单据有{__k0}处风险已标注在字段上，${isRiskError ? '请修改后再提交' : '请注意'}`:(isFeeDetail ?'该明细存在风险label已标注在字段上，请注意':'单据存在风险label已标注在字段上，请注意'), { __k0: count, ...riskTypeInTempObj })
    noticeList.push({ content: text, count })
  }
}

function getRiskFieldNum(fieldList, riskData) {
  Object.keys(riskData).forEach(key => {
    if (Array.isArray(riskData[key])) {
      fieldList.push(riskData[key])
    } else {
      getRiskFieldNum(fieldList, riskData[key])
    }
  })
  return fieldList
}

function riskNoticeNofindTemp(item) {
  return (
    <div>
      {i18n.get(`「{__k0}」 超标`, { __k0: item.label })}
      <a style={{ color: 'var(--brand-base)', marginLeft: 12 }} onClick={handleRiskClick.bind(this, item.value)}>
        {i18n.get('查看详情')}
      </a>
    </div>
  )
}

function handleRiskClick(riskList, e) {
  e.stopPropagation()
  e.preventDefault()
  api.open('@bill:FeetypeRiskModal', { riskList })
}

export function fnIsRiskError(external) {
  if (!external) return false
  let list = []
  list = Array.isArray(external) ? external : flatten(creatList(external))
  return list?.some(v => isErrorItem(v))
}