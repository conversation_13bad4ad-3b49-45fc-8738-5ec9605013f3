import { app } from '@ekuaibao/whispered'
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/29 下午12:08.
 */
import { app as api } from '@ekuaibao/whispered'
import { targetToSource } from './fieldsMergeUtils'
import { get, groupBy, isObject } from 'lodash'

import { checkPriority } from './CheckFieldPriority'
import { getBoolVariation, useDepartmentVisible } from '../../../lib/featbit'

import { getAssignmentRuleById, getPermissionFieldComponents } from '../bill.action'
import { checkStaffDataRange } from '../../../components/utils/fnCheckStaffDataRange'
import { useNewAutomaticAssignment } from '../../../components/utils/fnAutoDependence'

export function mergeData(fieldMap, values, deleteKeynoValue) {
  let map = {}
  targetToSource(map, fieldMap.fields)
  let mapToValue = map => {
    Object.keys(map).forEach(key => {
      if (map[key] instanceof Object) {
        mapToValue(map[key])
      } else {
        let form = values.dataLink || values.form
        const value = form[map[key]]
        if (value?.location) {
          map[key] = value?.name || value.address
        } else {
          map[key] = value
        }
      }
    })
  }
  mapToValue(map)
  if (deleteKeynoValue) {
    Object.keys(map).forEach(key => {
      const value = map[key]
      if (!value) {
        delete map[key]
      }
    })
  }
  return map
}

export function openDataLinkEntityList({
  data,
  template,
  props = {},
  flowId,
  index,
  isDetail,
  inApportion,
  entityInfo,
  saveValue,
  sourcePage = ''
}) {
  let { field, value, multi = false } = data
  let params = {}
  if (isDetail) {
    if (['recordExpends', 'eCardExpense'].includes(sourcePage)) {
      params = {
        dataMap: {},
        bus: props.bus,
        submitterId: props.submitterId,
        value: field,
        id: multi ? value && value.map(item => item.id) : value && value.id,
        multi,
        flowId,
        isDetail,
        template,
        formValue: saveValue
      }
      return openDataLinkModal(params, template, props, data)
    }
    return api.invoke('get:bills:value').then(result => {
      let map = handleDataLinkUseCount(result, index)
      params = {
        dataMap: map,
        bus: props.bus,
        submitterId: props.submitterId,
        value: field,
        id: multi ? value && value.map(item => item.id) : value && value.id,
        multi,
        flowId,
        isDetail,
        template,
        formValue: saveValue
      }
      return openDataLinkModal(params, template, props, data)
    })
  } else if (inApportion) {
    params = {
      value: { referenceData: entityInfo.referenceData, filterId: entityInfo.filterId },
      formValue: { form: {} }
    }
    return openDataLinkModal(params, template, props, data)
  } else {
    if (['recordExpends', 'eCardExpense'].includes(sourcePage)) {
      params = {
        value: field,
        id: multi ? value && value.map(item => item.id) : value && value.id,
        multi,
        flowId,
        isDetail,
        template,
        formValue: saveValue,
        submitterId: props.submitterId
      }
      return openDataLinkModal(params, template, props, data)
    }
    return api.invoke('get:bills:value', true).then(result => {
      params = {
        value: field,
        id: multi ? value && value.map(item => item.id) : value && value.id,
        flowId,
        multi,
        isDetail: false,
        template,
        formValue: result && result.saveValue,
        submitterId: props.submitterId
      }
      return openDataLinkModal(params, template, props, data)
    })
  }
}

export default openDataLinkEntityList

export function openDataLinkModal(params, template, props, data) {
  let { field, multi = false, dependenceParams, deleteKeynoValue } = data
  let { feeTypeSpecificationId } = props || {}
  return api.open('@bill:DataLinkModal', { ...params, dependenceParams, feeTypeSpecificationId }).then(result => {
    if (multi) {
      return result
    }
    const { assignmentRule, isLinkageAssignment, referenceData } = field
    if (!assignmentRule || !isLinkageAssignment) {
      return result
    }
    updateFormValueByDataLink({ result, field, template, props, deleteKeynoValue })
    return result
  })
}

export function updateFormValueByDataLink(params) {
  const { result, field, template, props, deleteKeynoValue } = params
  const { assignmentRule, isLinkageAssignment, referenceData } = field
  if (!assignmentRule || !isLinkageAssignment) {
    return
  }
  if (result.dataLink && referenceData.id !== result.dataLink.entityId) {
    //TODO 获取业务对象类型的赋值规则
    api.dispatch(getAssignmentRuleById([result.dataLink.entityId])).then(res => {
      const list = res.items
      updateData(list[0] || assignmentRule, result, props, template)
    })
  } else {
    updateData(assignmentRule, result, props, template, deleteKeynoValue)
  }
}

export function updateData(assignmentRule, result, props, template, deleteKeynoValue) {
  let values = mergeData(assignmentRule, result, deleteKeynoValue)
  let keys = template.map(line => line.name)
  let hasMoneyFields = false
  const { dimensionValueVisible } = app.require('@components/utils/fnDimension')
  props.bus.getFieldsValue('payeeId').then(async data => {
    const depFields = []
    const staffFields = []
    for (let key in values) {
      let temp = template.find(item => item.name === key)
      if ((keys.indexOf(key) >= 0 || typeof values[key] === 'undefined') && !checkPriority(temp)) {
        delete values[key]
      }
      if (temp && temp.type) {
        if (temp.type === 'dataLink') {
          let dataLinkValu = values[temp.name]
          // fix bug：XSG-12499
          // fix 业务对象关联带出相关业务对象的数据填充到表单
          const id = get(dataLinkValu, 'data.dataLink.id')
          if (id && temp.isLinkageAssignment && temp.assignmentRule && dataLinkValu.data) {
            api
              .invokeService('@bill:get:datalink:template:byId', {
                entityId: id,
                type: 'CARD'
              })
              .then(res => {
                if (res.value && res.value.data) {
                  updateFormValueByDataLink({ result: res.value.data, field: temp, template, props })
                }
              })
          }
          values[temp.name] = { ...dataLinkValu, id }
        } else if (temp.type.startsWith('ref:basedata.Dimension')) {
          let value = values[temp.name]
          if (value) {
            value.shouldCheckVisible = true //用于依赖性校验
            if (!dimensionValueVisible(value, props.submitter || props.submitterId)) {
              delete values[temp.name]
            }
          }
        } else if (temp.type === 'money') {
          hasMoneyFields = true
        } else if (temp.type === 'payeeInfo') {
          if (data?.payeeId && data?.payeeId?.multiplePayeesMode) {
            //收款信息为多收款人时不赋值
            values[temp.name] = data.payeeId
          }
        } else if (temp.type === 'number') {
          const scale = get(temp, 'dataType.scale')
          let number = values[temp.name]
          if (number === null || number === undefined) {
            delete values[key]
          } else if (number && scale !== undefined) {
            number = Number(number).toFixed(scale)
            values[temp.name] = number
          }
        } else if (temp.type === 'ref:organization.Staff' || temp.type === 'list:ref:organization.Staff') {
          staffFields.push(temp)
          const staff = values[temp.name]
          const rangeValue = checkStaffDataRange(temp, staff)
          if (Array.isArray(rangeValue) && !!rangeValue.length) {
            values[temp.name] = rangeValue
          } else if (!!rangeValue) {
            values[temp.name] = rangeValue
          } else {
            delete values[temp.name]
          }
        } else if (temp.type === 'ref:organization.Department') {
          depFields.push(temp)
        }
      }
    }
    await checkAssignmentRuleValue({ values, depFields, staffFields, bus: props.bus })
    props.bus.setFieldsValue({ ...values })
    if (props.type === 'loan' && values.loanMoney) {
      props.bus && props.bus.emit('loanMoney:changed', values.loanMoney)
    }
    hasMoneyFields && props.bus && props.bus.invoke('element:datalink:money:value:changed')
  })
}

export async function checkAssignmentRuleValue({ values = {}, depFields = [], staffFields = [], bus }) {
  if (useDepartmentVisible()) {
    for (let i = 0; i < depFields.length; i++) {
      const v = depFields[i];
      const isCanSelect = await bus.invoke(`department:check:${v.name}:value`, values[v.name]);
      if (!isCanSelect) {
        delete values[v.name];
      }
    }
    const [configVisibilityStaffs, contactConfig] = await Promise.all([
      api.invokeService('@common:get:organization:staffIds:visibility'),
      api.dataLoader('@common.organizationConfig').load()
    ])
    if (!configVisibilityStaffs.value.fullVisible && contactConfig?.filterScope !== 'UNLIMITED') {
      const visibleStaffIdsSet = new Set(configVisibilityStaffs.value.staffIds)
      for (let i = 0; i < staffFields.length; i++) {
        const v = staffFields[i];
        if (Array.isArray(values[v.name])) {
          const newValue = values[v.name].filter(staff => visibleStaffIdsSet.has(staff.id))
          newValue.length ? (values[v.name] = newValue) : delete values[v.name]
        } else if (isObject(values[v.name]) && !visibleStaffIdsSet.has(values[v.name].id)) {
          delete values[v.name]
        }
      }
    }
  }
}

export function filterDataLink(components = []) {
  return components.filter(line => line.type === 'dataLink')
}

export function handleHeaderFields(template, values, map) {
  const dataLinkFields = filterDataLink(template)
  dataLinkFields.forEach(line => {
    if (values[line.field]) {
      map[values[line.field].id] = 1
    }
  })
}

export function handleDetailsField(details, detailsMapPath, index) {
  let detailsMap = {},
    arr = []
  let detaisUseCount = details.map((line, idx) => {
    if (idx === index) {
      return false
    }
    const detailMap = {}
    let fieldsValue = get(line, detailsMapPath['fieldsValue'])
    let components = get(line, detailsMapPath['components'])
    let fields = filterDataLink(components)
    fields.forEach(line => {
      if (fieldsValue[line.field]) {
        detailMap[fieldsValue[line.field].id] = { id: fieldsValue[line.field].id, count: 1 }
      }
    })
    return detailMap
  })
  detaisUseCount.map(line => {
    const keys = Object.keys(line)
    keys.forEach(key => {
      arr.push(line[key])
    })
  })
  detailsMap = groupBy(arr, 'id')
  for (let key in detailsMap) {
    detailsMap[key] = detailsMap[key].length
  }
  return detailsMap
}

export function handleDataLinkUseCount(result, index) {
  let { detailsMapPath } = result
  let template = get(result, 'template')
  let map = {}
  handleHeaderFields(template, result.values, map)
  const details = get(result, detailsMapPath['details'])
  if (!details) {
    return map
  }
  let detailsMap = handleDetailsField(details, detailsMapPath, index)
  for (let key in map) {
    if (detailsMap[key]) {
      detailsMap[key] += 1
    } else {
      detailsMap[key] = map[key]
    }
  }
  return detailsMap
}

// 过滤出可以执行自动赋值操作的业务对象字段
export function filterAutoAssignDataLinkField(components = []) {
  return components.filter(line => line.type === 'dataLink' && line?.filterId)
}

// 过滤出当前模板业务对象字段需要唯一值自动赋值字段
export function filterDataLinkFields(components = [], fields = []) {
  const rangeOnlyOneAutomaticAssignment = useNewAutomaticAssignment() ? 'rangeOnlyOneAutomaticAssignment' : 'autoFillByFilterDataOnlyOne'
  if (fields.length) {
    // 存在依赖字段变更需更新的业务对象
    let dataLinkFields = []
    fields.forEach(field => {
      // filterId 筛选业务对象数据
      let obj = components.find(v => v?.field === field && v?.type === 'dataLink' && v?.[rangeOnlyOneAutomaticAssignment])
      obj = components.find(v => v?.field === field && v?.type === 'dataLink' && v?.filterId)
      if (obj) {
        dataLinkFields.push(obj)
      }
    })
    return dataLinkFields
  } else {
    return filterAutoAssignDataLinkField(components)
  }
}

// 根据单据模板或者费类id获取业务对象依赖字段
export async function getDataLinkPermissionFieldComponent(id) {
  if (!id) return
  const res = await getPermissionFieldComponents(id)
  return res?.items ?? []
}

// 获取业务对象依赖字段
export function getDependenceFieldOfDataLink(field = [], currentFields = []) {
  // 遍历当前change 的值取深层次依赖的值
  return field.find(v => currentFields.includes(v?.dependentField && v.dependentField.split('.')[0]))
}

//业务对象筛选数据结果只有一条数据时自动赋值处理
export async function handleAutoAssignOfOneResultOfDataLink({ dataLinkFields, currentValue, limit = 10, filterBySearch = false }) {
  return Promise.all(
    dataLinkFields.map(async line => {
      const { referenceData, allMatchList, dependence, filterBy } = line
      //业务对象存在依赖筛选，历史代码已经在DataLinkSelect中实现
      if (dependence && dependence.length) return
      if (filterBySearch && !filterBy) return // 二次查找针对filterBy属性查找

      const platformType = get(referenceData, 'platformId.type')
      const isTravelOrder = platformType === 'TRAVEL_MANAGEMENT' && referenceData.type === 'ORDER'
      const filterId = line?.filterId || ''
      let params = {
        entityId: typeof referenceData === 'object' ? get(referenceData, 'id') : referenceData,
        type: 'LIST',
        start: 0,
        count: limit || 10,
        form: currentValue && currentValue.form,
        params: isTravelOrder ? { type: 'order' } : currentValue && currentValue.params,
        submitterId: currentValue.submitterId,
        filterBy: filterBySearch && filterBy ? filterBy : '(active==true)',
        filterId
      }
      if (typeof allMatchList !== 'undefined') {
        //业务对象联查
        params.allMatchList = allMatchList
      }
      // 调用业务对象筛选数据接口
      const res = await api.invokeService('@mine:post:serach:dataLink', params)

      return res?.items?.data ?? []
    })
  )
}
