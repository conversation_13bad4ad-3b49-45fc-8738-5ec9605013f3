export function formatTemplate(params: {
  specification: any
  flowForm: any
  flowId: any
  mallField: string[]
  defaultFields: any
}) {
  const { specification, flowForm, flowId, mallField = [], defaultFields = {} } = params
  const components = specification?.components || []
  const defaultField = [
    'submitterId',
    'title',
    'travelers',
    'requisitionDate',
    // 'requisitionMoney',
    'u_订单总额',
    'u_企业支付金额',
    'u_订单号',
    'expenseLink'
    // 'u_订单类型',
  ]
  const needField = ['submitterId', 'expenseLinks']
  const filterField = mallField?.length > 0 ? mallField : defaultField
  let filterComponents = components?.filter((it: any) => {
    if (needField.includes(it?.field)) return true
    return !filterField?.includes(it?.field)
  })
  const flowValue: any = {}
  if (flowId) {
    // 如果申请单里面有数据,对有数据的字段进行赋值 value,并设置 editable: false
    filterComponents = filterComponents?.map((it: any) => {
      if (it?.field === 'expenseLinks') {
        flowValue[it.field] = flowId ? [flowId] : []
        return { ...it, editable: false }
      }
      if (flowForm[it?.field] && it?.field !== 'details') {
        flowValue[it.field] = flowForm[it?.field]
        return { ...it, editable: false }
      }
      return it
    })
  }
  const fieldsArr = Object.keys(defaultFields)
  if (fieldsArr.length > 0) {
    // 商城传过来的 对组件赋值
    filterComponents = filterComponents?.map((it: any) => {
      if ((it.field === 'u_合思商旅订单类型') || fieldsArr.includes(it?.field) && it?.field !== 'details') {
        flowValue[it.field] = defaultFields[it?.field]
        if (it.field === 'u_合思商旅订单类型' && defaultFields['u_订单类型']) {
          flowValue[it.field] = defaultFields['u_订单类型']
        }
        return { ...it, editable: false }
      }
      return it
    })
  }
  filterComponents = filterComponents?.map((it: any) => {
    if (needField.includes(it?.field)) {
      return { ...it, editable: false, hide: true }
    }
    return it
  })
  const newSpecification = { ...specification, components: filterComponents }
  return {
    newSpecification,
    flowValue
  }
}
