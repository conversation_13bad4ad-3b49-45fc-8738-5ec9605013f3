const __queues: Array<() => Promise<boolean>> = []
let __timerId: NodeJS.Timeout | null = null

/**
 * @param fn 回调函数
 */
export async function callFnByQueue(fn: () => Promise<boolean>): Promise<void> {
  return new Promise((resolve, reject) => {
    const queueFn = async () => await fn()

    __queues.push(queueFn)

    if (!__timerId) {
      __timerId = setInterval(() => {
        if (__queues.length) {
          const firstFn = __queues.shift()
          if (firstFn) {
            firstFn().catch(error => console.error('Error while processing function in queue:', error))
          }
        } else {
          clearInterval(__timerId)
          __timerId = null
          resolve()
        }
      }, 100)
    }
  })
}

type QueueFunction<T> = (arg: T) => Promise<boolean>

interface QueueItem<T> {
  fn: QueueFunction<T>
  arg: T
  resolve: () => void
  reject: (error: any) => void
}

const __queueNews: Array<QueueItem<any>> = []
let __isProcessing = false

/**
 * 按队列顺序执行函数，确保每个任务完全完成后再执行下一个
 * @param arg 传递给回调函数的参数
 * @param fn 回调函数，接收一个参数并返回Promise<boolean>
 * @param waitForStateUpdate 是否等待状态更新完成，默认为true
 */
export async function callFnByQueueNew<T>(
  arg: T,
  fn: QueueFunction<T>,
  waitForStateUpdate: boolean = true
): Promise<void> {
  return new Promise((resolve, reject) => {
    const queueItem: QueueItem<T> = {
      fn: async (arg: T) => {
        const result = await fn(arg)

        // 如果需要等待状态更新，添加延迟确保setState完成
        if (waitForStateUpdate) {
          await new Promise(resolveDelay => {
            setTimeout(resolveDelay, 50)
          })
        }

        return result
      },
      arg,
      resolve,
      reject
    }

    __queueNews.push(queueItem)

    if (!__isProcessing) {
      processNextTask()
    }
  })
}

/**
 * 处理队列中的下一个任务
 */
async function processNextTask(): Promise<void> {
  if (__isProcessing || __queueNews.length === 0) {
    return
  }

  __isProcessing = true

  const item = __queueNews.shift()
  if (!item) {
    __isProcessing = false
    return
  }

  try {
    const result = await item.fn(item.arg)

    // 验证返回值，确保任务真正完成
    if (result === true || result === undefined) {
      item.resolve()
    } else {
      item.reject(new Error('Task returned false, indicating failure'))
    }
  } catch (error) {
    item.reject(error)
  } finally {
    __isProcessing = false

    // 继续处理下一个任务
    if (__queueNews.length > 0) {
      setTimeout(() => {
        processNextTask()
      }, 100)
    }
  }
}
