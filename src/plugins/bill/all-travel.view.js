/*
 * @Author: 樊超
 * @Date: 2021-07-28 14:56:21
 * @LastEditTime: 2021-08-06 14:17:45
 * @LastEditors: Please set LastEditors
 * @Description: 全部行程列表
 * @FilePath: /applet/src/plugins/bill/all-travel.view.js
 */
import React, { PureComponent } from 'react'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import { AllTravelLayer } from './layers/AllTravelLayer'

@EnhanceTitleHook(i18n.get('全部行程消费信息'))
export default class AllTravel extends PureComponent {
  render() {
    const { submitterName, submitterId, requisitionId } = this.props.params || {};
    return <AllTravelLayer submitterId={submitterId} submitterName={submitterName} requisitionId={requisitionId} />
  }
}
