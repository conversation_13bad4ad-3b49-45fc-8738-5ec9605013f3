/**************************************************
 * Created by nanyuantingfeng on 17/07/2017 14:50.
 **************************************************/
import { app as api } from '@ekuaibao/whispered'
import * as actions from './bill.action'
import { hideLoading, showLoading } from '../../lib/util'
import { getDimensionConfig, getCommonDimensionConfig, getCommonDimension } from './utils/billFetchUtil'

import loadable from '@loadable/component'
import { getBatchSpecificationHiddenFields } from './utils/billUtils'
import { updateBillAppendInfo } from './bill.action'

const dispatchHelpers = (name, thenFn = d => d) => async (...theArgs) => {
  const actions = await import('./bill.action')
  const fn = actions[name]
  return api.dispatch(fn(...theArgs)).then(thenFn)
}

const noDispatchHelpers = (name, thenFn = d => d) => async (...theArgs) => {
  const actions = await import('./bill.action')
  const fn = actions[name]
  return fn(...theArgs).then(thenFn)
}

export default [
  {
    id: '@bill',
    reducer: () => require('./bill.reducer').default,
    'get:invoice:reason': async params => {
      return api.dispatch(actions.getInvoiceReason(params))
    },
    'save:invoice:reason:batch': async params => {
      return actions.saveInvoiceReasonBatch(params)
    },
    'get:invoice:idAndReason': async params => {
      return api.dispatch(actions.getInvoiceIdAndReason(params))
    },
    'save:invoice:reason': async params => {
      return api.dispatch(actions.saveInvoiceReason(params))
    },

    'get:submitter:loan:list': async param => {
      // const actions = await import('./bill.action')
      return new Promise((resolve, reject) => {
        actions
          .listLoanPackageBySubmitter({
            id: param.id,
            state: param.status,
            flowId: param.flowId,
            filters: param.filters,
            method: param.method,
            start: 0,
            count: 9999
          })
          .then(data => {
            resolve(data)
          })
      })
    },
    'get:bill:loan:list': async param => {
      // const actions = await import('./bill.action')
      return new Promise((resolve, reject) => {
        actions.getLoanListByBill(param).then(data => {
          resolve(data)
        })
      })
    },

    'get:favProject': async id => {
      // const actions = await import('./bill.action')
      return new Promise((resolve, reject) => {
        api.dispatch(
          actions.getFavProjects(id, (_, action) => {
            action.error ? reject(action.payload) : resolve(action.payload)
          })
        )
      })
    },

    'get:requisition:byExpense': async id => {
      // const actions = await import('./bill.action')
      return new Promise((resolve, reject) => {
        api.dispatch(
          actions.getApplyByExpense(id, (_, action) => {
            action.error ? reject(action.payload) : resolve(action.payload)
          })
        )
      })
    },

    'get:getRepaymentList': async () => {
      return api.dispatchO(actions.getRepaymentList())
    },

    'get:requisition:byExpenseOrder': async id => {
      return new Promise((resolve, reject) => {
        api.dispatch(
          actions.getApplyByExpenseWithOrder(id, (_, action) => {
            action.error ? reject(action.payload) : resolve(action.payload)
          })
        )
      }).finally(() => {
        hideLoading()
      })
    },

    'get:requisition:getApplyEventById': async id => {
      return new Promise((resolve, reject) => {
        api.dispatch(
          actions.getApplyEventById(id, (_, action) => {
            action.error ? reject(action.payload) : resolve(action.payload)
          })
        )
      })
    },

    'get:owner:loan:list': async param => {
      // const actions = await import('./bill.action')
      return new Promise((resolve, reject) => {
        api.dispatch(
          actions.listLoanPackage({ state: param, start: 0, count: 9999 }, (_, action) => {
            action.error ? reject(action.payload) : resolve(action.payload)
          })
        )
      })
    },

    'get:calculationresult': async params => {
      // const actions = await import('./bill.action')
      return api.dispatchO(actions.getBatchAutoCalResult(params))
    },

    //获取系统计算业务对象联查赋值
    'get:customizeCalculationresult': async params => {
      return api.dispatchO(actions.getCustomizeCalResult(params))
    },
    'check:loan:package:exist': async (loanIds, submitterId) => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.checkLoanPackageExist(loanIds, submitterId))
    },

    'set:payee:component:visibility': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.setPayeeComponentVisibility(params))
    },

    'comment:bill': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.commentFlow(params))
    },

    'get:dimension': async id => {
      // const actions = await import('./bill.action')
      let creator = () => api.dispatch(actions.getDimensionItemsById([id]))
      const cache = api.require('@lib/cache')
      return cache.invokeCache(`dimension` + id, creator)
    },
    'get:department': async id => {
      const deptMap = api.getState()['@common'].departmentsVisibility.mapData || {}
      const dept = deptMap[id]
      if (dept) {
        const { id, code, name, form } = dept
        return Promise.resolve({ id, code, name, form })
      }

      // const actions = await import('./bill.action')
      return api.dispatch(actions.getDepartmentById(id)).then(res => res.value)
    },
    'get:enumitem': async id => {
      // const actions = await import('./bill.action')
      let creator = () => api.dispatch(actions.getEnumItemById([id]))
      const cache = api.require('@lib/cache')
      return cache.invokeCache(`enum` + id, creator)
    },
    'get:enum:by:ids': async ids => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getEnumItemById(ids))
    },
    'get:entity:details': async id => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getEntityDetailsById(id))
    },
    'get:entity:by:platform:name': async name => {
      const actions = await import('./bill.action')
      return api.dispatch(actions.getEntityByPlatformName(name))
    },
    getAutoAssociateDetail: async (param, data) => {
      const { getAutoAssociateDetail } = await import('./bill.action')
      return getAutoAssociateDetail(param, data)
    },

    'save:current:flow': async flow => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.saveCurrentFlow(flow))
    },

    'save:history:currency:rates': async flow => {
      return api.dispatch(actions.saveHistoryCurrencyRates(flow))
    },

    'save:current:payees': async payees => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.saveCurrentPayees(payees))
    },

    'save:copied:value': async value => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.saveCopiedValue(value))
    },

    'get:entity:list': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getEntityValueListById(params))
    },
    'get:entity:list:v2': async params => {
      return api.dispatch(actions.getEntityValueListV2(params))
    },
    'get:entity:details:list': async params => {
      return api.dispatch(actions.getEntityDetailsList(params))
    },
    'get:travelManagementConfig': async params => {
      const actions = await import('./bill.action')
      return api.dispatch(actions.getTravelManagementConfig(params))
    },
    'get:checkReturnCity': async params => {
      const actions = await import('./bill.action')
      return api.dispatch(actions.checkReturnCity(params))
    },
    'pull:TravelOrder': async params => {
      const actions = await import('./bill.action')
      return api.dispatch(actions.pullTravelOrder(params))
    },
    'get:getFlowDetailInfo': async params => {
      const actions = await import('./bill.action')
      return api.dispatch(actions.getFlowDetailInfo(params))
    },
    'get:getFlowDetailInfoLite': async params => {
      const actions = await import('./bill.action')
      return api.dispatch(actions.getFlowDetailInfoLite(params))
    },
    'get:getFlowInfo:tripPlatform': async params => {
      const actions = await import('./bill.action')
      return api.dispatch(actions.getFlowInfoTripPlatform(params))
    },
    'get:field:mapping': async id => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getFieldMappingByRuleId(id))
    },
    'get:travel:management': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getTravelManagement(params))
    },
    'get:travel:management:detail': params => {
      return actions.getTravelManagementDetail(params)
    },
    'set:sync:trip'(flowId) {
      return api.dispatch(actions.setSyncTripManually(flowId))
    },
    'get:sync:trip:result'(flowId) {
      return api.dispatch(actions.getSyncTripResult(flowId))
    },
    'get:sync:trip:valid'(params) {
      return api.dispatch(actions.getSyncTripValid(params))
    },
    'isEbot:shift': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getIsEbotShift(params))
    },

    'get:VPhoto:data': async id => {
      // const actions = await import('./bill.action')
      return actions.getVphotoDetail(id)
    },

    'verify:VPhoto:orders': async value => {
      // const actions = await import('./bill.action')
      return actions.verifyVPhotoOrders(value)
    },

    'get:invoice:state': async data => {
      let { masterId, submitterId } = data
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getInvoiceStateById(masterId, submitterId))
    },

    'get:feetypeImportRule:byId': async (id, type) => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getFeetypeImportRuleById(id, type)).then(res => {
        if (window.IS_ICBC) {
          const { items } = res
          res.items = items.filter(el => el.id !== 'invoiceWeChat')
        }
        return res
      })
    },

    'get:flow:submit:check:state': async flowId => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getFlowSubmitCheckState(flowId))
    },

    'get:current:backLog': async flowId => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getCurrentBackLog(flowId))
    },

    'get:ledger:planned': async id => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getLedgerPlanedById(id))
    },

    'get:ledger:relation:list': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getLedgerRelationList(params))
    },
    'get:ledger:relation:entity:list': async params => {
      // const actions = await import('./bill.action')
      return actions.getLedgerRelationEntityList(params)
    },

    'get:flow:risk:info': params => actions.getFlowRiskInfo(params),

    'get:datalink:template:byId': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getDatalinkTemplateById(params))
    },
    'get:datalink:template:byIds': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getDatalinkTemplateByIds(params))
    },
    'get:dependenceList': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getDependenceList(params))
    },
    'get:loanpackage:by:flowId': async id => {
      // const actions = await import('./bill.action')
      return new Promise((resolve, reject) => {
        api
          .dispatch(actions.getLoanPackageByFlowId({ id }))
          .then(res => {
            resolve(res)
          })
          .catch(err => reject(err))
      })
    },
    'get:calculaterfield': async param => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getCalculateField(param.specificationId, param.submitterId))
    },
    'check:Supplement:identity': async params => {
      // const actions = await import('./bill.action')
      return actions.checkSupplementIdentity(params)
    },
    'check:YeeGo:Order': async params => {
      // const actions = await import('./bill.action')
      return actions.checkYeeGoOrder(params)
    },
    'check:Dimensions:isLeaf': async ids => {
      // const actions = await import('./bill.action')
      const result = await actions.checkDimensionIsLeaf(ids)
      return result
    },
    'get:specification:by:ids': async ids => {
      // const actions = await import('./bill.action')
      const result = await api.dispatch(actions.getSpecificationsById(ids))
      return result.items
    },
    'get:ExpenseLink:details': async params => {
      // const actions = await import('./bill.action')
      const result = await api.dispatch(actions.getExpenseLinkDetails(params))
      return result.items
    },
    'get:ExpenseLink:list': async ids => {
      // const actions = await import('./bill.action')
      const result = await actions.getExpenseLinkList(ids)
      return result.items
    },
    'save:expenseLink:record:details': async params => {
      // const actions = await import('./bill.action')
      const result = await api.dispatch(actions.saveExpenseLinkRecordDetails(params))
      return result
    },
    'save:feeType:visible:list': async param => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.saveFeeTypeVisibleList(param))
    },
    'get:active:credit:rules:group': async params => {
      return api.dispatch(actions.getActiveCreditRulesWithGroup(params))
    },
    'get:bill:notes': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getBillNotes(params))
    },
    'add:bill:note': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.addBillNote(params))
    },
    'change:add:note:mode': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.changeAddNoteMode(params))
    },
    'search:note:form:history': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.searchNoteFormHistory(params))
    },
    'change:status:of:notes': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.changeStatusOfShowBillNotesInHistory(params))
    },
    'get:my:credit:point': async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getMyCreditPoint(params))
    },
    'get:loan:detail:logs': async (flowId, relateFlowId) => {
      return api.dispatch(actions.getloanpackageDetailLogs(flowId, relateFlowId))
    },
    'get:invoice:corporation': dispatchHelpers('getInvoiceCorporation'),
    setRequisitionInfo: async (...params) => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.setRequisitionInfo(...params))
    },
    getCurrentRequisitionInfo: async (...params) => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.getCurrentRequisitionInfo(...params))
    },
    setValidateError: async params => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.setValidateError(params))
    },
    getCalculateField: async (...params) => {
      // const actions = await import('./bill.action')
      return api.dispatchO(actions.getCalculateField(...params))
    },

    async dispatchAction(actionName, ...params) {
      // const actions = await import('./bill.action')
      return api.dispatch(actions[actionName](...params))
    },
    'get:flows:by:ids': async ids => {
      // const actions = await import('./bill.action')
      return actions.getFlowByIds(ids)
    },
    'get:metaby:ids': async ids => {
      // const actions = await import('./bill.action')
      return actions.getMetaByIds(ids)
    },
    'save:written:off:summary': async writtenOffSummaryForMutiCurrency => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.saveWrittenOffSummary(writtenOffSummaryForMutiCurrency))
    },
    'clear:written:off:summary': async ids => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.clearWrittenOffSummary())
    },

    'save:cross:written:off:summary': async writtenOffSummaryForCrossCurrency => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.saveCrossWrittenOffSummary(writtenOffSummaryForCrossCurrency))
    },
    'clear:cross:written:off:summary': async ids => {
      // const actions = await import('./bill.action')
      return api.dispatch(actions.clearCrossWrittenOffSummary())
    },

    'get:getTravelBackInfo': async (id, submitterId) => {
      return api.dispatch(actions.getTravelBackInfo(id, submitterId))
    },
    'get:getTravelBackInfoV3': async code => {
      const params = {
        page: 0,
        size: 20,
        flowDetailFlowCode: code
      }
      return api.dispatch(actions.getTravelBackInfoV3(params))
    },
    'update:dimension:currency': async params => {
      return api.dispatch(actions.updateDimentionCurrency(params))
    },
    'get:payee:config:check': async params => {
      return api.dispatch(actions.payeeConfigCheck())
    },
    'get:datalink:dependence:list': async params => {
      return actions.getDependenceDatalinkList(params)
    },
    'record:invoice:riskWarning': async (id, params) => {
      return actions.recordInvoiceRiskWarning(id, params)
    },
    'get:original:order:no': async params => {
      return actions.getOriginalOrderNo(params)
    },
    'get:details:fieldsGroupData': async params => {
      return actions.getFieldsGroupData(params)
    },
    'set:print:preview:url': async url => {
      return api.dispatch(actions.setPrintPreviewUrl(url))
    },
    'get:loan:isCancel:share': params => {
      return actions.shareIsCancel(params)
    },
    'get:check:nulllify:rule': async params => {
      return api.dispatch(actions.checkNulllifyRule(params))
    },
    'get:check:nulllify:flowId': async flowId => {
      return api.dispatch(actions.checkNulllifyFlowId(flowId))
    },

    // 'get:all:orders': async params => {
    //   return api.dispatch(actions.getAllOrders(params))
    // },
    'get:getFeeTypeChange': async id => {
      return api.dispatch(actions.getFeeTypeChange(id))
    },
    'get:micro:order:list': async () => {
      return actions.getMicroOrderList()
    },
    'get:default:payee': async params => {
      return api.dispatch(actions.getDefaultPayee(params))
    },
    'get:delegate:config': async id => {
      return api.dispatch(actions.getDelegateConfig(id))
    },
    'set:submitter:data': async data => {
      return api.dispatch(actions.setSubmitterData(data))
    },
    'get:calculate:corpId:whitelist': async () => {
      return api.dispatch(actions.getCalculateCorpIdWhiteList())
    },
    'get:flow:link:list': async id => {
      return new Promise((resolve, reject) => {
        api.dispatch(
          actions.getFlowLinkList(id, (_, action) => {
            action.error ? reject(action.payload) : resolve(action.payload)
          })
        )
      })
    },
    'get:dimension:config': async params => {
      return getDimensionConfig(params)
    },
    'get:common:dimension:config': async params => {
      return getCommonDimensionConfig(params)
    },
    'get:common:dimension': async params => {
      return getCommonDimension(params)
    },
    'get:approve:detail': noDispatchHelpers('getApproveDetail'),
    'get:travel:date:range': noDispatchHelpers('getTravelDateRange'),
    'get:baiwang:previewUrl': noDispatchHelpers('getBaiwangPreviewUrl'),
    'get:repayment:account': noDispatchHelpers('getRepaymentAccountList'),
    'get:show:print:invoice': noDispatchHelpers('judgeShowPrintInvoice'),
    'add:or:cancel:collect'(id, enable) {
      return api.dispatch(actions.addOrCancelCollect(id, enable))
    },
    'get:flow:by:code': actions.getFlowInfoByCode,
    'get:batch:specification:hiddenFields': async specificationIds => {
      return getBatchSpecificationHiddenFields(specificationIds)
    },
    'set:my:bill:from:entry': dispatchHelpers('setMyBillsEntry'),
    'get:flow:buttons': noDispatchHelpers('getFlowButtons'),
    'update:flow:append:info': data => {
      api.dispatch(actions.updateBillAppendInfo(data))
    },
    'check:purchase:travel': noDispatchHelpers('checkTravelTime'),
    'update:currencyRatesList': dispatchHelpers('updateBillCurrencyRates'),
    'create:feishu:chat': noDispatchHelpers('createFeishuChat'),
    'get:feishu:chat:url': noDispatchHelpers('getFeishuChatUrl'),
    'get:feishu:chat:status': noDispatchHelpers('getFeishuChatStatus'),
    'get:department:visible:data': noDispatchHelpers('getDepartmentVisibleData'),
    'post:order:ai:plan:travel': noDispatchHelpers('orderAIPlanTravel'),
    'get:Text:form:audio': noDispatchHelpers('getTextFormAudio'),
    'get:mall:trip:config': noDispatchHelpers('getMallTripConfig'),
  },
  {
    point: '@@layers',
    prefix: '@bill',
    onload: () => require('./layers').default
  },
  {
    point: '@@components',
    namespace: '@bill',
    onload: () => require('./components').default
  },
  {
    path: '/bill/:type',
    ref: '/',
    exact: true,
    onload: () => import('./bill-draft.view')
  },
  {
    path: '/copy/bill/:type/:isCopyBill',
    ref: '/',
    exact: true,
    onload: () => import('./bill-draft.view')
  },
  {
    path: '/bill/localStorage/:localStorage',
    ref: '/',
    exact: true,
    onload: () => import('./bill-draft.view')
  },
  {
    path: '/bill/:type/:id',
    ref: '/',
    exact: true,
    onload: () => import('./bill-draft.view')
  },
  {
    path: '/bill/:type/from/:from',
    ref: '/',
    exact: true,
    onload: () => import('./bill-draft.view')
  },
  {
    path: '/billScan',
    ref: '/',
    exact: true,
    onload: () => import('./bill-scan-detail.view')
  },
  {
    path: '/message/bill/:type/:isFromMessage',
    ref: '/',
    exact: true,
    onload: () => import('./bill-draft.view')
  },
  {
    path: '/delegator/:type/:delegatorId',
    ref: '/',
    onload: () => import('./bill-draft.view')
  },
  {
    path: '/rejected/:type/:id',
    ref: '/',
    exact: true,
    onload: () => import('./bill-rejected.view')
  },
  {
    path: '/rejected/:type/:id/:canEditExpress',
    ref: '/',
    exact: true,
    onload: () => import('./bill-rejected.view')
  },
  {
    path: '/requisitionToDetail/:type/:id',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/detail/:id',
    ref: '/',
    exact: true,
    onload: () => import(/* webpackChunkName: "bill-detail-view" */ './bill-detail.view')
  },
  {
    path: '/requisitionTabDetail/:type/:id',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/paiddetail/:id/:isAlwaysPrint/:sourcePage',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/billDetail/:id/:sourcePage',
    ref: '/',
    exact: true,
    dependencies: ['@common', '@auth-check', '@home5'],
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/detail/:id/:carbonCopyReadType/:carbonCopyType/:carbonCopyId',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/detail/:id/:carbonCopyReadType/:carbonCopyType/:carbonCopyId/:sourcePage',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/detail/:id/:sourcePage',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/detail/:id/:budgetId/:nodeId',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/billdetail/:id/:budgetId/:nodeId/:fromNodeId',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/message/detail/:messageSource/:id',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/version/detail/:id/:flowVersionedId',
    ref: '/',
    exact: true,
    onload: () => import('./bill-versions.view')
  },
  {
    path: '/modify/:type/:id',
    ref: '/',
    exact: true,
    onload: () => import('./bill-modify.view')
  },
  {
    path: '/modify/:type/:id/:entry',
    ref: '/',
    exact: true,
    onload: () => import('./bill-modify.view')
  },
  {
    path: '/comment',
    ref: '/',
    exact: true,
    onload: () => import('./elements/bill-comment.view')
  },
  {
    path: '/homeToDetail/:id/:canEditExpress',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/homeToDetail/:id/:sourcePage/:canEditExpress',
    ref: '/',
    exact: true,
    onload: () => import('./bill-detail.view')
  },
  {
    path: '/tripToDetail/:id',
    ref: '/',
    exact: true,
    onload: () => import('./elements/datalink/TripDetail')
  },
  {
    path: '/tripDetailNew/:id',
    ref: '/',
    exact: true,
    onload: () => import('./elements/datalink/tripDetailNew')
  },
  {
    path: '/thirdpartNewBill/:specificationOriginalId',
    ref: '/',
    exact: true,
    onload: () => import('./thirdpart-new-bill.view')
  },
  {
    path: '/thirdpartNewBill/:specificationOriginalId/:openFrom',
    ref: '/',
    exact: true,
    onload: () => import('./bill-draft.view')
    // onload: () => import('./thirdpart-new-bill.view')
  },
  {
    path: '/bill/requisition/success/:id',
    ref: '/',
    exact: true,
    onload: () => import('./elements/bill-requisition-success')
  },
  {
    path: '/all-travel/:submitterId/:requisitionId/:submitterName',
    ref: '/',
    exact: true,
    onload: () => import('./all-travel.view')
  },
  {
    path: '/bill/isOpenAssociation/:type/:isOpenAssociation',
    ref: '/',
    exact: true,
    onload: () => import('./bill-draft.view')
  },
  {
    resource: '@bill',
    value: {
      ['bill-content/bill-item']: loadable(() => import('./bill-content/bill-item')),
      ['bill-content/bill-item.isUrgent']: require('./bill-content/bill-item.isUrgent').default,
      ['utils/formatRiskData.formatBillData']: require('./utils/formatRiskData.formatBillData').default,
      ['utils/formatRiskData.formatRiskNotice']: require('./utils/formatRiskData.formatRiskNotice').default,
      ['utils/formatRiskData.formatRiskWarnings']: require('./utils/formatRiskData.formatRiskWarnings').default,
      ['utils/formatRiskData.formatTripRiskNotice']: require('./utils/formatRiskData.formatTripRiskNotice').default,
      ['utils/dataLinkUtils.openDataLinkEntityList']: () => import('./utils/dataLinkUtils.openDataLinkEntityList'),
      ['utils/dataLinkUtils.openMultiSelectDataLinkEntityList']: () =>
        import('./utils/dataLinkUtils.openMultiSelectDataLinkEntityList'),
      ['utils/autoCalResult']: require('./utils/autoCalResult'),
      ['utils/customizeCalculate']: require('./utils/customizeCalculate'),
      ['utils/billUtils']: require('./utils/billUtils'),
      ['utils/formatUtil']: require('./utils/formatUtil'),
      ['elements/datalink/datalink-detail-modal/types']: require('./elements/datalink/datalink-detail-modal/types'),
      ['elements/EKBFormItem']: loadable(() => import('./elements/EKBFormItem')),
      ['utils/importFormatData']: () => import('./utils/importFormatData'),
      ['utils/showDetailsTips']: () => import('./utils/showDetailsTips'),
      ['utils/OCRUtils']: require('./utils/OCRUtils'),
      ['bill-content/bill-empty']: loadable(() => import('./bill-content/bill-empty')),
      ['bill-content/bill-content']: loadable(() => import('./bill-content/bill-content')),
      ['elements/datalink/ListTitle']: loadable(() => import('./elements/datalink/ListTitle')),
      ['elements/datalink/dataLinkUtil']: require('./elements/datalink/dataLinkUtil'),
      ['panels/UrgentView']: loadable(() => import('./panels/UrgentView')),
      ['panels/LogsCardView']: loadable(() => import('./panels/LogsCardView')),
      ['bill-content/BillListWrapper']: loadable(() => import('./bill-content/BillListWrapper')),
      ['bill-content/bill-list']: loadable(() => import('./bill-content/bill-list')),
      ['elements/datalink/TripView']: loadable(() => import('./elements/datalink/TripView')),
      ['utils/parseSpecification']: require('./utils/parseSpecification'),
      ['bill.action']: require('./utils/parseSpecification'),
      ['EKBDropDown']: loadable(() => import('./elements/EKBDropDown')),
      ['elements/NoPermissionViewBillDetail']: loadable(() => import('./elements/NoPermissionViewBillDetail'))
    }
  }
]
