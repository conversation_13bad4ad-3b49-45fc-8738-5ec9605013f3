/**************************************************
 * Created by nanyuantingfeng on 11/07/2017 12:04.
 **************************************************/
import { Reducer } from '@ekuaibao/store'
import key from './key'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { toast, alert, hideLoading, printAlert } from '../../lib/util'

const reducer = new Reducer(key.ID, {
  specification_expense: [],
  specification_loan: [],
  specification_requisition: [],
  requisitionInfoCurLen: 0,
  requisitionInfoFilter: [],
  requisitionInfoCur: [],
  requisitionInfo: [],
  visibleSpecId: [],
  expenseLink: undefined, //从申请单详情页发起的报销
  expressNums: [],
  billCopiedValue: undefined, //复制单据时创建的单据信息
  payeeComponentData: {}, // 表达报销单的费用明细中是否需要有收款信息
  expressBudgetChildData: {},
  currentPayeesForBillInfoReadOnly: [], // 单据只读状态时，存储收款信息，避免在刷新页面时丢失
  loanInfo: {},
  expenseLinkDetails: null, //从随手记页发起的报销
  feeTypeVisibleObjForModify: {}, //在审批中修改时过滤明权权限
  activeCreditRuleWithGroups: [], // 可用的信用批注选项
  billNotes: [], // 当前单据可见批注
  addNoteMode: false, //单据是否进入批注模式
  detailReadable: false, // 单据上的信用批注分数是否可见
  authorRemovable: false, // 允许信用批注的添加人删除该批注
  creditNoteAvailable: false, // 当前用户是否可以添加信用批注
  billNotesInHistory: [], //在单据历史中的批注快照
  showBillNotesInHistory: false, //用户是否在浏览单据历史
  myCreditPoint: {}, //个人信用分数据
  writtenOffSummaryForMutiCurrency: null, //多币种核销
  travelBackInfo: [], // 申请单行程是否可撤回
  dimensionCurrencyInfo: null, // 法人实体多币种
  loanLogs: [], //审核中logs,
  trips_info: null,
  printPreviewUrl: '', // 当前单据的打印预览链接
  generationRule: [],
  // allOrders: null
  feeChangeInfo: {}, // 费用变更
  submitterData: undefined,
  skipCheck: null,
  flowLinkInfo: [], //单据查询
  myBillsOpenEntry: '',
  billsRateList: null, // 单据上最新的汇率列表
  billAdditionalInfo: {} // 单据附加信息
})

reducer.handle(key.SET_SYNCTRIPMANUALLY, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
  }
  return state
})

reducer.handle(key.GET_SYNCTRIP_RESULT, (state, action) => {
  return state
})

reducer.handle(key.GET_SYNC_VALID, (state, action) => {
  return state
})

//票据连号风险需要填写原因
reducer.handle(key.GET_INVOICE_REASON, (state, action) => {
  return state
})

const updateRequisition = () => {
  const currentRequisitionId = api.getState()['@requisition'].currentRequisitionId
  if (currentRequisitionId) {
    setTimeout(_ => api.invokeService('@requisition:get:requisition', currentRequisitionId), 300)
  }
}

reducer.handle(key.GET_SPECIFICATION_LIST, (state, action) => {
  let { specificationType, payload } = action
  let items = payload.items

  return { ...state, [`specification_${specificationType}`]: items }
})
reducer.handle(key.EDIT_FLOW, (state, action) => {
  hideLoading()
  if (action.error) {
    alert(action.payload.msg)
    return state
  }
  let data = action.payload || {}
  let { value, type } = data
  if (!value && type !== 0) {
    updateRequisition()
  }
  toast.success(i18n.get('保存成功'))
})

reducer.handle(key.SET_PRINT_PREVIEW_URL, (state, action) => {
  return { ...state, printPreviewUrl: action.url }
})

reducer.handle(key.SAVE_AND_SUBMIT, (state, action) => {
  return state
})
reducer.handle(key.GET_FLOW, (state, action) => {
  return state
})
reducer.handle(key.GET_FLOW_LITE, (state, action) => {
  return state
})
reducer.handle(key.GET_FLOW_TRIPPLATFORM, (state, action) => {
  return state
})

reducer.handle(key.POST_PRINTJOB, (state, action) => {
  //打印确认弹窗
  const { flowId, callback } = action
  printAlert(flowId, callback)
  return state
})

reducer.handle(key.SUBMIT_FLOW, (state, action) => {
  if (action.error) {
    return state
  }

  let data = action.payload || {}
  let { value, type } = data
  if (action.isBillCopiedValue) {
    // 提交成功后清除单据复制值，解决费用中没有detailId的问题
    setTimeout(_ => api.invokeService('@bill:save:copied:value'), 0)
  }

  if (!value && type !== 0 && !data?.flow?.form?.linkRequisitionInfo) {
    updateRequisition()
  }
})

reducer.handle(key.GET_LOAN_LOGS, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  let loanLogs = action.payload.value
  return { ...state, loanLogs }
})

reducer.handle(key.DELETE_FLOW, (state, action) => {
  hideLoading()
  if (action.error) {
    alert(action.payload.msg)
    return state
  }
  let data = action.payload || {}
  let { value, type } = data
  if (!value && type !== 0) {
    updateRequisition()
  }
  toast.success(i18n.get('单据已删除至回收站'))
})

reducer.handle(key.RETRACT_FLOW, (state, action) => {
  hideLoading()
  if (action.error) {
    alert(action.payload.msg)
    return state
  }
  toast.success(i18n.get('撤回成功'))
  api.go(-1)
})

reducer.handle(key.WITHDRAWN_FLOW, (state, action) => {
  hideLoading()
  if (action?.payload?.code === '1') {
    toast.fail(action?.payload?.message)
    return state
  }
  toast.success(action?.payload?.message)
  api.go(-1)
})

reducer.handle(key.COMMENT_FLOW, (state, action) => {
  hideLoading()
  if (action.error) {
    alert(action.payload.msg)
    return state
  }
  toast.success(i18n.get('评论成功'))
  return state
})

reducer.handle(key.FAV_PROJECTS, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  let data = action.payload.items
  return { ...state, favProjects: data } // ?
})
reducer.handle(key.GET_QR_INVOICE_DATA, (state, action) => {
  hideLoading()
  if (action.error) {
    alert(action.payload.msg)
    return state
  }
  return { ...state }
})

reducer.handle(key.COMPUTED_FLOW_PLAN, (state, action) => {
  hideLoading()
  if (action.error) {
    alert(action.payload.msg)
    return state
  }
  return state
})

reducer.handle(key.GET_APPLY_BY_EXPENSE, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  let { requisitionInfoCurLen = 0 } = state
  const { data, filterStr } = action.payload
  let requisitionInfo = data?.items || []
  const requisitionInfoFilter = !filterStr
    ? requisitionInfo
    : requisitionInfo.filter(el => el.code.includes(filterStr) || el.name.includes(filterStr))
  requisitionInfoCurLen += 10
  let requisitionInfoCur = requisitionInfoFilter.slice(0, requisitionInfoCurLen)
  return { ...state, requisitionInfo, requisitionInfoFilter, requisitionInfoCur, requisitionInfoCurLen }
})

reducer.handle(key.GET_APPLY_BY_EXPENSE_FILTER, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  const { filterStr = '' } = action?.payload || {}
  let { requisitionInfo = [] } = state
  const requisitionInfoFilter = !filterStr
    ? requisitionInfo
    : requisitionInfo.filter(el => el.code.includes(filterStr) || el.name.includes(filterStr))
  const requisitionInfoCurLen = 10
  let requisitionInfoCur = requisitionInfoFilter.slice(0, requisitionInfoCurLen)
  return { ...state, requisitionInfoFilter, requisitionInfoCur, requisitionInfoCurLen }
})

reducer.handle(key.LOAD_MORE_APPLY_BY_EXPENSE, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  const { filterStr = '' } = action?.payload || {}
  let { requisitionInfoCurLen = 0, requisitionInfo = [] } = state
  const requisitionInfoFilter = !filterStr
    ? requisitionInfo
    : requisitionInfo.filter(el => el.code.includes(filterStr) || el.name.includes(filterStr))
  requisitionInfoCurLen += 10
  let requisitionInfoCur = requisitionInfoFilter.slice(0, requisitionInfoCurLen)
  return { ...state, requisitionInfoFilter, requisitionInfoCur, requisitionInfoCurLen }
})

reducer.handle(key.GET_APPLY_EVENT, (state, action) => {
  const { value = {} } = action.payload
  if (value?.id) {
    const { requisitionInfo = [] } = state
    requisitionInfo.some(v => {
      if (v.id === value.id) {
        Object.assign(v, { ...value })
        return true
      }
      return false
    })
    return {
      ...state,
      requisitionInfo
    }
  }
})

reducer.handle(key.GET_EXPENSE_LINK, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  return { ...state }
})

reducer.handle(key.GET_CURRENT_REQUISITION_INFO, (state, action) => {
  let { visibleSpecId = [] } = action
  return { ...state, visibleSpecId }
})
reducer.handle(key.GET_ENTITY_BY_PLATFORM_NAME, (state, action) => {
  return { ...state }
})
reducer.handle(key.GET_TREVELMANAGEMENT_CONFIG, (state, action) => {
  return { ...state }
})
reducer.handle(key.CHECK_RETURN_CITY, (state, action) => {
  return { ...state }
})
reducer.handle(key.PULL_TRAVEL_ORDER, (state, action) => {
  return { ...state }
})

reducer.handle(key.SET_REQUISITION_INFO, (state, action) => {
  let { expenseLink = undefined } = action
  let visibleSpecId = action.visibleIds || []
  return { ...state, expenseLink, visibleSpecId }
})

reducer.handle(key.BILL_REMIND, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  toast.success(i18n.get('催办成功'))
  return { ...state }
})
reducer.handle(key.GET_FEETYPE_IMPORT_RULE_BY_ID, (state, action) => {
  return { ...state }
})
reducer.handle(key.GET_BATCH_AUTO_CAL_RESULT)((state, action) => {
  return state
})
reducer.handle(key.GET_CUSTOMIZE_CAL_RESULT)((state, action) => {
  return state
})
reducer.handle(key.GET_INVOICE_STATE_BYID)((state, action) => {
  return state
})
reducer.handle(key.GET_DIMENSION_BY_ID)((state, action) => {
  return state
})
reducer.handle(key.GET_DEPARTMENT_BY_ID)((state, action) => {
  return state
})
reducer.handle(key.GET_ENUMITEM_BY_ID)((state, action) => {
  return state
})
reducer.handle(key.GET_CUSTOMTRAVELTYPE)((state, action) => {
  return state
})
reducer.handle(key.GET_ENTITY_VALUE_BY_ID)((state, action) => {
  let entityDetail = action.payload.value
  return { ...state, entityDetail }
})
reducer.handle(key.GET_ENTITY_FORM_BY_ID)((state, action) => {
  const dataLinkEntity = action.payload?.items || []
  return { ...state, dataLinkEntity }
})
reducer.handle(key.GET_OBJECT_FORM_BY_ID)((state, action) => {
  return state
})
reducer.handle(key.GET_ENTITY_VALUE_LIST_V2)((state, action) => {
  return state
})
reducer.handle(key.GET_ENTITY_VALUE_LIST_BY_ID)((state, action) => {
  let entityListData = action.payload
  return { ...state, entityListData }
})
reducer.handle(key.GET_ENTITY_LIST_BY_PLATFORM)((state, action) => {
  return state
})
reducer.handle(key.GET_FIELD_MAPPING_BY_ID)((state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  let fieldMap = action.payload.value
  return { ...state, fieldMap }
})

reducer.handle(key.SAVE_CURRENT_FLOW)((state, action) => {
  return { ...state, originFlowForHistory: action.payload }
})

reducer.handle(key.SAVE_CURRENT_PAYEES)((state, action) => {
  return { ...state, currentPayeesForBillInfoReadOnly: action.payload }
})

reducer.handle(key.GET_EXPRESS_NUMS)((state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  return { ...state, expressNums: action.payload.items }
})

reducer.handle(key.UPDATE_EXPRESS_NUMS)((state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  let { expressNums } = state
  const newExpressNums = action.payload.items
  const { expressId } = action
  const express = newExpressNums.find(expressInfo => expressInfo.express.id === expressId)
  // unshift 无法在ie中正常运作，所以用了concat实现
  if (express) expressNums = [express].concat(expressNums)
  return { ...state, expressNums }
})

reducer.handle(key.GET_EXPRESS)((state, action) => {
  const expressNums = state.expressNums.map(el => {
    if (el.express.id === action.expressId) {
      if (action.payload.msg) {
        //仅用于 后台直接返回错误 没有返回值的时候 默认显示用
        el.express = { ...el.express, ...action.payload.value, msg: action.payload.msg }
      } else {
        el.express = { ...el.express, ...action.payload.value }
      }
      el.isLoading = true
    }
    return el
  })

  return { ...state, expressNums }
})

reducer.handle(key.GET_VISIBLE_DATALINK_FEETYPE)((state, action) => {
  return { ...state, visibleFeetype: action.payload.items }
})

reducer.handle(key.GET_FLOW_SUBMIT_CHECK_STATE)((state, action) => {
  return state
})
reducer.handle(key.GET_CURRENT_BACKLOG)((state, action) => {
  return state
})

reducer.handle(key.GET_ISEBOT_SHIFT)((state, action) => {
  return state
})

reducer.handle(key.SAVE_COPIED_VALUE)((state, action) => {
  return { ...state, billCopiedValue: action.payload }
})

reducer.handle(key.GET_LEDGER_PLANED_BYID)((state, action) => {
  return state
})

reducer.handle(key.GET_LEDGER_RELATION_LIST)((state, action) => {
  return state
})

reducer.handle(key.SET_PAYEE_COMPONENT_VISIBILITY)((state, action) => {
  return { ...state, payeeComponentData: action.payload }
})

reducer.handle(key.GET_DATALLINK_TEMPLATE_BYID)((state, action) => {
  return state
})
reducer.handle(key.GET_DATALLINK_TEMPLATE_BYIDS)((state, action) => {
  return state
})

reducer.handle(key.GET_RECORDLINK)((state, action) => {
  return state
})

reducer.handle(key.GET_EXPRESS_BUDGET_CHILD_LIST)((state, action) => {
  return { ...state, expressBudgetChildData: action.payload }
})
reducer.handle(key.GET_LOAN_PACKAGE_BY_FLOWID)((state, action) => {
  return { ...state, loanInfo: action.payload }
})

reducer.handle(key.GET_CALCULATE_FIELD)((state, action) => {
  return state
})

reducer.handle(key.GET_ASSIGNMENT_RULE_BY_ID)((state, action) => {
  return state
})

reducer.handle(key.CHECK_LOAN_PACKAGE_EXIST)((state, action) => {
  return state
})

reducer.handle(key.VALIDATE_ERROR)((state, action) => {
  const validateError = { ...state.validateError, ...action.payload }
  return { ...state, validateError }
})

reducer.handle(key.GET_SPECIFICATION_BY_IDS)((state, action) => {
  return { ...state }
})
reducer.handle(key.GET_EXPENSE_LINK_DETAILS)((state, action) => {
  return { ...state }
})

reducer.handle(key.SAVE_EXPENSE_LINK_RECORD_DETAILS)((state, action) => {
  const { expenseLinkDetails = null } = action
  return { ...state, expenseLinkDetails: expenseLinkDetails.details }
})

reducer.handle(key.SAVE_FEETYPE_VISIBLE_LIST)((state, action) => {
  const { feeTypeVisibleObjForModify } = action
  return { ...state, feeTypeVisibleObjForModify }
})

reducer.handle(key.GET_ACTIVE_CREDIT_RULES_GROUP)((state, action) => {
  const creditNoteAvailable = get(action, 'payload.value.creditNoteAvailable', false)
  const activeCreditRuleWithGroups = get(action, 'payload.value.ruleWithGroups', [])
  return { ...state, activeCreditRuleWithGroups, creditNoteAvailable }
})
reducer.handle(key.GET_BILL_NOTES)((state, action) => {
  const billNotes = get(action, 'payload.value.items', [])
  const detailReadable = get(action, 'payload.value.detailReadable', false)
  const authorRemovable = get(action, 'payload.value.authorRemovable', false)
  return { ...state, billNotes, detailReadable, authorRemovable }
})

reducer.handle(key.POST_ADD_NOTE)((state, action) => {
  setTimeout(() => api.invokeService('@bill:get:bill:notes', { flowId: action.flowId }))
  toast.success(i18n.get('添加成功'))
  return { ...state }
})

reducer.handle(key.CHANGE_ADD_NOTE_MODE)((state, action) => {
  const { addNoteMode } = action
  return { ...state, addNoteMode }
})

reducer.handle(key.SEARCH_NOTE_FORM_HISTORY)((state, action) => {
  const billNotesInHistory = get(action, 'payload.value.items', [])
  return { ...state, billNotesInHistory, showBillNotesInHistory: true }
})

reducer.handle(key.CHANGE_STATUS_OF_SHOWBILLNOTESINHISTORY)((state, action) => {
  const { showBillNotesInHistory } = action
  return { ...state, showBillNotesInHistory }
})

reducer.handle(key.GET_MY_CREDIT_POINT)((state, action) => {
  const myCreditPoint = get(action, 'payload.value', {})
  const { submitterId } = action
  return { ...state, myCreditPoint: { ...myCreditPoint, submitterId } }
})

reducer.handle(key.SAVE_WRITTENOFF_SUMMARY)((state, action) => {
  return { ...state, writtenOffSummaryForMutiCurrency: action.writtenOffSummaryForMutiCurrency }
})

reducer.handle(key.CLEAR_WRITTENOFF_SUMMARY)((state, action) => {
  return { ...state, writtenOffSummaryForMutiCurrency: null }
})

reducer.handle(key.SAVE_CROSS_WRITTENOFF_SUMMARY)((state, action) => {
  return { ...state, writtenOffSummaryForCrossCurrency: action.writtenOffSummaryForCrossCurrency }
})

reducer.handle(key.CLEAR_CROSS_WRITTENOFF_SUMMARY)((state, action) => {
  return { ...state, writtenOffSummaryForCrossCurrency: null }
})

reducer.handle(key.GET_TRAVEL_BACK_INFO)((state, action) => {
  return {
    ...state,
    travelBackInfo: action.payload || []
  }
})

reducer.handle(key.GET_TRAVEL_BACK_INFO_V3)((state, action) => {
  return {
    ...state,
    travelBackInfoV3: action.payload || []
  }
})

reducer.handle(key.GET_FIELDS_GROUP_DATA)((state, action) => {
  return {
    ...state,
    fieldsGroupData: action.payload?.value || []
  }
})

reducer.handle(key.GET_PAYEE_CONFIG_CHECK)((state, action) => {
  const payeeConfigCheck = action.payload.value
  return {
    ...state,
    payeeConfigCheck
  }
})

reducer.handle(key.UPDATE_DIMENTION_CURRECY)((state, action) => {
  return { ...state, dimensionCurrencyInfo: action.dimensionCurrencyInfo }
})

reducer.handle(key.SAVE_TRIPS_INFO)((state, action) => {
  return { ...state, trips_info: action.payload }
})

// reducer.handle(key.GET_ALL_ORDERS)((state, action) => {
//   return { ...state, allOrders:action.payload }
// })

reducer.handle(key.AUTO_GENERATION_FEE_DETAIL_RULES)((state, action) => {
  let generationRule = action.payload?.value?.generationRule || []
  return { ...state, generationRule }
})

reducer.handle(key.AUTO_GENERATION_FEE_DETAIL)((state, action) => {
  return { ...state }
})

reducer.handle(key.SEARCH_EXTENSION_CENTER_CONFIG)((state, action) => {
  return { ...state }
})
reducer.handle(key.GET_FEE_TYPE_CHANGE)((state, action) => {
  const result = action?.payload?.items || []
  const feeChangeInfo = {}
  result.forEach((item, index) => {
    const key = item.feeTypeId
    feeChangeInfo[`${key}${index}`] = item
  })
  return {
    ...state,
    feeChangeInfo
  }
})

reducer.handle(key.GET_DEFAULT_PAYEE)((state, action) => {
  return { ...state }
})

reducer.handle(key.GET_DELEGATE_CONFIG)((state, action) => {
  return { ...state }
})

reducer.handle(key.SET_SUBMITTER_DATA)((state, action) => {
  return { ...state, submitterData: action.payload }
})

reducer.handle(key.GET_CAlCULATE_CORP_ID_WHITELIST)((state, action) => {
  return { ...state, skipCheck: action.payload.value }
})

reducer.handle(key.GET_FLOW_LINK_LIST, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  let flowLinkInfo = action.payload.items
  return { ...state, flowLinkInfo }
})

reducer.handle(key.CALCULATE_RECORD_LINK, (state, action) => {
  return { ...state }
})

reducer.handle(key.NULLIFY_FLOW, (state, action) => {
  hideLoading()
  if (action.error) {
    alert(action.payload.msg)
    return state
  }
  toast.success(i18n.get('作废成功'))
})
reducer.handle(key.GET_NULLIFY_RULE_BYSPECID)((state, action) => {
  return state
})
reducer.handle(key.GET_NULLIFY_RULE_BY_FLOWID)((state, action) => {
  return state
})
reducer.handle(key.GET_INVOICE_CORPORATION)((state, action) => {
  let corporationList = action.payload
  return { ...state, corporationList }
})
reducer.handle(key.SAVE_HISTORY_CURRECY_RATES)((state, action) => {
  return { ...state, historyCurrencyInfo: action.historyCurrencyInfo }
})

reducer.handle(key.ADDORCANCELCOLLECT, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
  }
  return state
})

reducer.handle(key.GET_MY_BILLS_ENTRY)((state, action) => {
  return { ...state, myBillsOpenEntry: action.myBillsOpenEntry }
})

reducer.handle(key.UPDATE_BILL_ADDEND_INFO, (state, action) => {
  return { ...state, billAdditionalInfo: action.billAdditionalInfo }
})

reducer.handle(key.SET_CURRENCY_RATES)((state, action) => {
  return { ...state, billsRateList: action.payload.data }
})

export default reducer
