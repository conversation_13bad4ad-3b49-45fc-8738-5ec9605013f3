import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import BillDetail from './bill-detail.view'
import { EnhanceConnect } from '@ekuaibao/store'
import { Checkbox } from 'antd-mobile'
import { Dialog } from '@hose/eui-mobile'
import { scanDetailActions } from './utils/scanDetailViewUtil'
const { thousandBitSeparator } = app.require('@components/utils/fnThousandBitSeparator')
import { toast, showLoading, hideLoading, printRemindAlert } from '../../lib/util'
import { billRemind, retractFlow } from './bill.action'
const parseAsFormValue = app.require('@lib/parser.parseAsFormValue')
import { confirmCopy, formatFormForCopy } from './utils/formatUtil'
import cloneDeep from 'lodash/cloneDeep'
import { checkFlowAllRequiredFields } from '../approve/service'
import { FlowAction } from './panels/FlowAction'
const CheckboxItem = Checkbox.CheckboxItem

@EnhanceConnect(state => ({
  userInfo: state['@common'].me_info,
  current_flow: state['@common'].current_flow,
  scan_current_flow: state['@common'].scan_data.flow,
  scan_current_backLogId: state['@common'].scan_data.backLogId,
  standardCurrency: state['@common'].standardCurrency,
  billActions: state['@common'].billActions
}))
export default class BillModify extends PureComponent {
  constructor(props) {
    super(props)
    const btns = this.initBtn(props.billActions)
    this.state = {
      btns,
      lastTime: 0,
      carbonCopyType: ''
    }
  }

  componentWillReceiveProps(np) {
    if (np.current_flow !== this.props.current_flow) {
      if (this.state.btns.length === 2) {
        return this.setState({ carbonCopyType: 'carbonCopy' })
      }
    }
  }

  initBtn = billActions => {
    let buttons = billActions[this.props.userInfo.staff.id]
    buttons = buttons.map(btn => {
      let button = scanDetailActions[btn.action]
      if (btn.main) button.weight = 99
      return button
    })
    return buttons
  }

  handleRefresh = () => {
    const { id: flowId } = this.props.current_flow
    api.invokeService('@common:get:flow:detail:info', { id: flowId })
  }

  handlePrint = async () => {
    let select = {}
    select[this.props.current_flow.id] = this.props.current_flow
    const data = await api.invokeService('@approve:dealPrintData', [this.props.current_flow.id], select)
    api.invokeService('@approve:doPrint', data, null, this.handleRefresh, '0')
  }

  handlePrintAndInvoice = async () => {
    let select = {}
    select[this.props.current_flow.id] = this.props.current_flow
    const data = await api.invokeService('@approve:dealPrintData', [this.props.current_flow.id], select)
    api.invokeService('@approve:doPrint', data, null, this.handleRefresh, '1')
  }

  handleReminder = () => {
    let { lastTime } = this.state
    let newTime = new Date().valueOf()
    if (newTime - lastTime > 60000) {
      //60秒内只能执行一次催办功能
      let { id: flowId, plan } = this.props.current_flow
      let { taskId } = plan
      api.dispatch(billRemind(flowId, taskId))
      this.setState({ lastTime: newTime })
    } else {
      toast.error(i18n.get('操作频繁'))
    }
  }

  handleRetract = () => {
    let { id: flowId } = this.props.current_flow
    Dialog.confirm({
      title: i18n.get('撤回单据'),
      content: <div style={{ textAlign: 'left' }}>{i18n.get('撤回后若重新提交，将从第一位审批人开始审批。')}</div>,
      cancelText: i18n.get('取消'),
      confirmText: i18n.get('撤回'),
      onConfirm: () => api.dispatch(retractFlow(flowId))
    })
  }

  //确认 归档
  handleConfirm = () => {
    let { id: flowId } = this.props.current_flow
    api.invokeService('@approve:do:confirm', [flowId], { name: 'freeflow.archive' }).then(_ => {
      api.go(-1)
    })
  }

  handleCommentBill = () => {
    let { id: flowId } = this.props.current_flow
    api.open('@bill:BillComment').then(params => {
      api.invokeService('@bill:comment:bill', { id: flowId, params }).then(_ => {
        this.handleRefresh()
      })
    })
  }

  handleShift = async transferType => {
    const { current_flow, scan_current_backLogId } = this.props
    const isPaying = current_flow.state.toLocaleUpperCase() === 'PAYING'
    const { value: rejectedAddNode } = await api.invokeService('@common:get:toggle:switch:by:name', {
      key: 'tg_fix_rejected_addNode'
    })
    api
      .open(!transferType && rejectedAddNode ? '@basic:RejectedAddNodeModal' : '@basic:PlanShift', {
        isPaying,
        flowDatas: [current_flow],
        isSignShift: !transferType
      })
      .then(data => {
        data.name = 'freeflow.addnode'
        api.invokeService('@approve:dispatchAction', 'doAction', { id: scan_current_backLogId }, data, _ => api.go(-1))
      })
  }

  handleReject = () => {
    const { current_flow, scan_current_backLogId } = this.props
    const backlog = { flowId: current_flow, id: scan_current_backLogId }
    api
      .open('@basic:PlanReject', {
        data: { backlog }
      })
      .then(data => {
        const { resubmitMethod } = data
        data = { ...data, name: 'freeflow.reject', resubmitMethod } //TODO 未处理其他方式
        api.invokeService('@approve:dispatchAction', 'doAction', { id: scan_current_backLogId }, data, _ => api.go(-1))
      })
  }

  handlePrintRemind() {
    const { id } = this.props.current_flow
    printRemindAlert(() => {
      api
        .invokeService('@approve:dispatchAction', 'printRemindAction', [id])
        .then(res => {
          const { errors } = res
          if (errors.length > 0) {
            const err = errors[0]
            return Dialog.alert({
              title: i18n.get('操作失败'),
              content: err.message,
              onConfirm: () => api.go(-1)
            })
          }
          toast.success(i18n.get('提醒成功'))
        })
        .then(() => this.handleRefresh())
    })
  }

  handleModifyFlow() {
    const { id, formType } = this.props.current_flow
    api.go(`/modify/${formType}/${id}/fromScanBill`)
  }

  handleAddExpressInfo = () => {
    const { current_flow, scan_current_backLogId } = this.props
    api.open('@basic:ExpressAdd', {
      backlog: { flowId: current_flow, id: scan_current_backLogId },
      label: i18n.get('添加寄送信息')
    })
  }

  handleSkipExpress = () => {
    const { current_flow, scan_current_backLogId } = this.props
    api.open('@basic:ExpressAdd', {
      backlog: { flowId: current_flow, id: scan_current_backLogId },
      label: i18n.get('跳过寄送'),
      type: 'skipExpress'
    })
  }

  handleReceiveExpress = () => {
    const { current_flow, scan_current_backLogId } = this.props
    const backlog = { flowId: current_flow, id: scan_current_backLogId }
    api.open('@basic:PlanResolve', { data: { backlog } }).then(value => {
      showLoading()
      api.invokeService('@approve:receiveExpresses', { backlogIds: [backlog.id], ...value }).then(data => {
        hideLoading()
        const list = (data.value && data.value.errors) || []
        const errorList = list.filter(item => item.resultCode !== 'OK')
        if (errorList.length) {
          return Dialog.alert({
            title: i18n.get('操作失败'),
            content: data.value.errors[0].message
          })
        }
        api.go(-1)
      })
    })
  }

  handleZeroPay = () => {
    const { current_flow } = this.props
    let params = { flowIds: [current_flow.id] }
    let autograph = this.props.userInfo && this.props.userInfo.staff && this.props.userInfo.staff.autograph
    if (this.state.signChecked && autograph) {
      params.autographImageId = autograph.key
    }

    api.invokeService('@approve:dispatchAction', 'zeroPay', params).then(_ => {
      api.go(-1)
    })
  }

  handlePay = async () => {
    const { userInfo, standardCurrency, current_flow, scan_current_backLogId } = this.props
    const backlog = { flowId: current_flow, id: scan_current_backLogId }
    const allFieldCheckIn = await checkFlowAllRequiredFields([current_flow])
    if (!allFieldCheckIn) return

    if (backlog.flowId.form.payMoney.standard * 1 === 0) {
      const { symbol = '', scale = 2 } = standardCurrency
      const zeroMoney = thousandBitSeparator(new Big(0).toFixed(scale))
      return Dialog.confirm({
        content: (
          <div>
            <span style={{ color: '#54595b', fontSize: 15, fontWeight: 500 }}>
              {i18n.get('支付金额：{__k0}{__k1}', { __k0: symbol, __k1: zeroMoney })}
            </span>
            <br />
            <span style={{ color: '#b1b9bd', fontSize: 14, marginTop: 5 }}>{i18n.get('无需支付，确定即完成')}</span>
            <br />
            {userInfo?.staff?.autograph && (
              <div style={{ display: 'inline-block' }}>
                <CheckboxItem checked={this.state.signChecked} onChange={this.handleSignChange.bind(this)}>
                  <span>{i18n.get('使用签名影像')}</span>
                </CheckboxItem>
              </div>
            )}
          </div>
        ),
        onConfirm: this.handleZeroPay
      })
    }
    return api
      .open('@basic:PlanPay', {
        data: {
          backlog: [backlog],
          onPay: data => api.invokeService('@approve:dispatchAction', 'payBackLog', data),
          onConfirm: data => api.invokeService('@approve:dispatchAction', 'confirmPayment', data)
        }
      })
      .then(_ => {
        api.go(-1)
      })
  }

  handleAgree = () => {
    const { current_flow, scan_current_backLogId } = this.props
    const backlog = { flowId: current_flow, id: scan_current_backLogId }
    api.open('@basic:PlanResolve', { data: { backlog } }).then(data => {
      data = { ...data, name: 'freeflow.agree' }
      api.invokeService('@approve:dispatchAction', 'doAction', { id: scan_current_backLogId }, data, () => {
        api.go(-1)
      })
    })
  }

  handleCopyClick = async () => {
    const { current_flow } = this.props
    const flow = cloneDeep(current_flow)
    let value = parseAsFormValue(flow)
    value = await formatFormForCopy(value)
    const isCopyBill = 'isCopyBill'
    const { specificationId } = value
    api.invokeService('@home:save:specification', specificationId).then(_ => {
      confirmCopy(flow).then(_ => {
        api
          .invokeService('@bill:save:copied:value', value)
          .then(api.go(`/copy/bill/${specificationId.type}/${isCopyBill}`))
      })
    })
  }

  fnGetActionMap = () => {
    return {
      [FlowAction.Pay]: { onClick: () => this.handlePay() },
      [FlowAction.Agree]: { onClick: () => this.handleAgree() },
      [FlowAction.AddNode]: { onClick: () => this.handleShift('SHIFT_NODE') },
      [FlowAction.Reject]: { onClick: () => this.handleReject() },
      [FlowAction.Modify]: { onClick: () => this.handleModifyFlow() },
      [FlowAction.Retract]: { onClick: () => this.handleRetract() },
      [FlowAction.PrintRemind]: { onClick: () => this.handlePrintRemind() },
      [FlowAction.PrintInvoice]: { onClick: () => this.handlePrintAndInvoice() },
      [FlowAction.Comment]: { onClick: () => this.handleCommentBill() },
      [FlowAction.Send]: { onClick: () => this.handleAddExpressInfo() },
      [FlowAction.SkipSend]: { onClick: () => this.handleSkipExpress() },
      [FlowAction.Receive]: { onClick: () => this.handleReceiveExpress() },
      [FlowAction.Urge]: { onClick: () => this.handleReminder() },
      [FlowAction.Copy]: { onClick: () => this.handleCopyClick() },
      [FlowAction.AddSignNode]: { onClick: () => this.handleShift() },
      signShift: { onClick: () => this.handleShift() }
    }
  }

  _buttonsClick = params => {
    const { name = '' } = params
    const actionMap = this.fnGetActionMap()
    const action = actionMap[name]
    if (action) {
      action.onClick()
    }
  }

  render() {
    const { scan_current_flow } = this.props
    const { btns, carbonCopyType } = this.state
    return (
      <BillDetail
        billActions={btns.filter(el => el)}
        params={{ id: scan_current_flow.id, canEditExpress: true, carbonCopyType }}
        buttonsClickInScan={this._buttonsClick}
        flowActionMap={this.fnGetActionMap()}
        from='scan'
      />
    )
  }
}
