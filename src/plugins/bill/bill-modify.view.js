import React, { PureComponent } from 'react'
import BillDraft from './bill-draft.view'
import { app as api } from '@ekuaibao/whispered'
import clone from 'lodash/clone'
import isEqual from 'lodash/isEqual'
import { EnhanceConnect } from '@ekuaibao/store'

@EnhanceConnect(state => ({
  me_info: state['@common'].me_info,
  feeTypeVisibleObjForModify: state['@bill'].feeTypeVisibleObjForModify
}))
export default class BillModify extends PureComponent {
  componentWillMount() {
    this._$UUID = String(Math.random())
    api.backControl.create(this._$UUID, () => {
      this.backHookCallback()
    })
  }

  componentWillUnmount() {
    api.backControl.remove(this._$UUID)
  }

  backHookCallback = () => {
    const { params } = this.props
    this.bus?.getValue?.().then(newValue => {
      if (newValue?.details?.length === 0) {
        newValue.details = undefined
      }
      const oldValue = this.formatOldValueStr(newValue)
      if (!isEqual(oldValue, newValue)) {
        localStorage.removeItem(params.id)
        this.showModifyModal()
      } else {
        api.backControl.remove(this._$UUID)
        api.backControl.invoke()
      }
    })
  }

  formatOldValueStr = newValue => {
    let oldValue = api.getState('@common.current_flow')
    let cloneOldValue = clone(oldValue)
    let oldFormValue = cloneOldValue?.form || {}
    let compareObj = {}
    for (let key in newValue) {
      compareObj[key] = oldFormValue[key]
    }
    return compareObj
  }

  showModifyModal = () => {
    const { me_info } = this.props
    api.open('@bill:ModifyModal', { staff: me_info?.staff }).then(result => {
      let { isCancel, editReason } = result
      if (isCancel) {
        api.backControl.remove(this._$UUID)
        api.backControl.invoke()
      } else {
        this.backModifySave(editReason)
      }
    })
  }

  handleBackModifySave = fn => {
    this.backModifySave = fn
  }

  handleCompareModify = bus => {
    this.bus = bus
  }

  render() {
    return (
      <BillDraft
        {...this.props}
        isModifyBill={true}
        compareModify={this.handleCompareModify}
        backModifySave={this.handleBackModifySave}
      />
    )
  }
}
