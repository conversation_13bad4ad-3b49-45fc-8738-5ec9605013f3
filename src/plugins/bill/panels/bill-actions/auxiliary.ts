import { app } from "@ekuaibao/whispered"
import { BillAdditionalMessageApi } from '@ekuaibao/ekuaibao_types'
import { getBillHistoryVersionDetail, getBillHistoryVersionList } from "../../bill.action"
import { BillActionBase, BillActionBaseProps } from "./base-action"
import { getRiskReasonDataForVersionDiffModal, getDiffsBetweenVersions} from "../../utils/billUtils"
import { get } from "lodash"

export class AuxiliaryInformationAction extends BillActionBase {
  label = i18n.get('辅助信息')
  _initDataPromise: Promise<any> = null

  parepare = async () => {
    const { backlog } = this
    const [invoiceRiskData, widgetConfig] = await Promise.all([
      app.invokeService('@common:get:riskwarningById', { flowId: backlog.flowId.id }),
      BillAdditionalMessageApi.fetchUserConfig(get(backlog, 'flowId.form.specificationId.originalId.id'))
    ])
    return {
      invoiceRiskData,
      widgetConfig,
    }
  }

  onAction = async () => {
    await this._initDataPromise
    this._initDataPromise = null
    return this.handleViewDiff()
  }

  handleViewDiff = async () => {
    const { backlog } = this
    const { invoiceRiskData, widgetConfig } = await this.parepare()
    const historyVersions = await getBillHistoryVersionList(backlog?.flowId?.id, '')
    return app.open('@bill:BillVersionDiff', {
      versions: historyVersions?.items,
      getDiffs: this.getDiffs,
      riskData: getRiskReasonDataForVersionDiffModal(invoiceRiskData),
      dataSource: backlog?.flowId?.form,
      config: widgetConfig
    })
  }

  getDiffs = async (type: string, curId: any, prevId: any) => {
    const privilegeId = ''
    const curVersion = await getBillHistoryVersionDetail(curId, privilegeId)
    const prevVersion = await getBillHistoryVersionDetail(prevId, privilegeId)
    const diffs = await getDiffsBetweenVersions(type, curVersion, prevVersion)
    return diffs
  }
}