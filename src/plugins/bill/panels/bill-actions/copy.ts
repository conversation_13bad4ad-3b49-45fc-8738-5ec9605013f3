import { app } from "@ekuaibao/whispered"
import { BillActionBase, BillActionBaseProps } from "./base-action"
import { cloneDeep, get } from "lodash"
import { checkSpecificationActive, checkIsRemuneration, fixRemunerationSpecification, logInfo } from "../../utils/billUtils"
import { formatFormForCopy, confirmCopy } from "../../utils/formatUtil"

const parseAsFormValue = app.require('@lib/parser.parseAsFormValue')

export class CopyAction extends BillActionBase {
  label = i18n.get('复制')

  onAction = () => {
    return this.handleCopy()
  }

  handleCopy = async () => {
    const { backlog } = this

    // 获取单据数据 - backlog.flowId 包含表单数据
    const dataSource = backlog.flowId

    // 单据使用的模板被停用时，拦截复制动作
    const specOriginalId = get(dataSource, 'form.specificationId.originalId')
    const specActive = await checkSpecificationActive(specOriginalId)
    if (!specActive) return

    const flow = cloneDeep(dataSource)

    delete flow.form.systemGeneration
    let value = (parseAsFormValue as any)(flow)
    value = await formatFormForCopy(value)
    const isCopyBill = 'isCopyBill'
    const { specificationId } = value
    const isRemuneration = checkIsRemuneration(specificationId?.id || specificationId)
    isRemuneration && fixRemunerationSpecification(specificationId)

   return app.invokeService('@home:save:specification', specificationId).then((_: any) => {
     return confirmCopy(flow).then((_: any) => {
        return app.invokeService('@bill:save:copied:value', value).then(() => {
          app.go(`/copy/bill/${specificationId.type}/${isCopyBill}`)
          logInfo(`复制${dataSource.form.title}单据`)
        })
      })
    })
  }
}