import { app as api } from "@ekuaibao/whispered"
import { BillActionBase, BillActionBaseProps } from "./base-action"
import { action } from "./constant"

export class MarkedReadAction extends BillActionBase<BillActionBaseProps> {
  action = action.commnet
  label = i18n.get('标为已读')

  onAction = async () => {
    return this.handleMarkedRead()
  }

  handleMarkedRead = () => {
    const { id } = this.backlog.flowId
    return api.invokeService('@approve:marked:read', { type: '', ids: [id] }).then((_: never) => {
      api.go(-1)
    })
  }
}