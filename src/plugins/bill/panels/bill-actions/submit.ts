import { get } from "lodash";
import { BillActionBase, BillActionBaseProps } from "./base-action";
import { setFormMultiplePayeesMode } from "../../util/billUtils";
import { ButtonProps, message as EUIMessage } from "@hose/eui";

export class SubmitAction extends BillActionBase<BillActionBaseProps> {
  label = i18n.get('提交送审')

  constructor(props: BillActionBaseProps, buttonProps: ButtonProps) {
    super(props, buttonProps)
  }

  onAction = async () => {
    this.handleSubmit()
  }

  handleSubmit = async () => {
    const { backlog, bus } = this.props
    try {
      const formValue = await bus.getValueWithValidate()
      setFormMultiplePayeesMode(formValue, get(backlog, 'flowId'))
      this.buttonProps.loading = true
      this.buttonProps.disabled = true
      const res = await bus.invoke('submit:bill:click', formValue)
      !!res && this.reloadData()
    } catch (e) {
      if (e && (e['details'] || e['trips'])) {
        let { errors = [] } = e['details'] || e['trips']
        let { message } = errors[0]
        EUIMessage.error(message)
      }
    } finally {
      //校验失败
      this.buttonProps.loading = false
      this.buttonProps.disabled = false
    }
  }
}