import React from "react";
import { Dialog } from "@hose/eui-mobile";
import { app as api } from "@ekuaibao/whispered";
import { BillActionBase } from "./base-action";
import * as actions from "../../bill.action";
import { logInfo } from "../../utils/billUtils";

export class RetractAction extends BillActionBase {
  label = i18n.get('撤回')

  onAction = async () => {
    return this.handleRetract()
  }

  handleRetract = () => {
    const { flowId: flow } = this.backlog
    Dialog.confirm({
      title: i18n.get('撤回单据'),
      content: <div style={{ textAlign: 'left' }}>{i18n.get('撤回后若重新提交，将从第一位审批人开始审批。')}</div>,
      cancelText: i18n.get('取消'),
      confirmText: i18n.get('撤回'),
      onConfirm: async () => {
        logInfo(`撤回${flow.form.title}单据`)
        await api.dispatch(actions.retractFlow(flow.id))
      }
    })
  }
}