import { app as api } from '@ekuaibao/whispered'
import { Dialog, Questionnaire } from '@hose/eui-mobile'
import questionnaireConfig from '../../../../lib/questionnaireConfig'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { toast } from '../../../../lib/util'
import { getBoolVariation } from '../../../../lib/featbit'
import qs from 'qs'

/**
 * 初始化问卷调查
 * @param specification 单据规格
 */
export const initSurvey = (specification: any) => {
  // 问卷调查逻辑
  const billTypeMaps: any = billTypeMap()
  const type = billTypeMaps[specification?.type || 'expense']
  const staff = api.getState()['@common'].me_info?.staff || {}

  Questionnaire.initSurvey({
    sid: questionnaireConfig?.approve?.sid,
    channelId: questionnaireConfig?.approve?.channelId,
    externalUserId: staff.userId,
    externalCompanyId: staff.corporationId?.id,
    parameters: {
      name: specification.name,
      type
    }
  })
}

/**
 * 获取驳回节点数据
 * @param taskId 当前任务ID
 * @param nodes 节点数组
 */
export const getRejectData = (taskId: string, nodes: any[]) => {
  let spcSetting = false,
    spcResubmitMethod,
    id,
    rejectTo,
    rejectToNode,
    rejectToNodeIndex

  for (var i = 0; i < nodes.length; i++) {
    if (nodes[i].id === taskId) {
      rejectToNode = nodes[i]
      rejectToNodeIndex = i
      break
    }
  }

  // 在nodes里面找到数据后， 在查看 rejectSetting 数据
  if (rejectToNode.hasOwnProperty('rejectSetting') && rejectToNode.rejectSetting?.rejectMethod?.methods?.length > 0) {
    //存在设置了单个节点的驳回配置，弹窗提示，走单个节点的驳回配置
    spcSetting = true
    rejectTo = rejectToNode?.rejectSetting?.rejectTo

    // 如果 rejectSetting 中 rejectTo 有值， 根据 rejectTo 值到整个nodes里面找 configNodeId 相同数据 （type不能为ebot）， 找到之后获取id
    if (rejectTo === null) {
      id = null
    }

    if (!id && id !== null) {
      for (var i = 0; i < nodes.length; i++) {
        if (rejectTo === nodes[i].configNodeId && nodes[i].type != 'ebot' && nodes[i].type != 'invoicingApplication') {
          id = nodes[i].id
          break
        }
      }
    }

    // 根据 rejectTo 没有找到数据， 则根据当前node索引往上找最近 非 ebot数据， 找到后返回id， 没有到话 返回null
    if (!id && id !== null) {
      for (var i = rejectToNodeIndex - 1; i >= 0; i--) {
        if (nodes[i].type != 'ebot' && nodes[i].type != 'invoicingApplication') {
          id = nodes[i].id
          break
        }
      }
    }

    if (!id && id !== null) {
      id = null
    }

    spcResubmitMethod = rejectToNode?.rejectSetting?.rejectMethod?.methods[0]
    return { spcResubmitMethod, id, spcSetting }
  }
}

interface RunAfterApproveOrRejectProps {
  fromThirdParty: any
  isApproveNext: boolean
  onApprovePermissionSuccess: () => void
  onApproveNext: () => void
  initSurvey: () => void
}
/**
 * 同意、驳回之后的操作
*/
export const runAfterApproveOrReject = ({
  fromThirdParty,
  isApproveNext,
  onApprovePermissionSuccess,
  onApproveNext,
  initSurvey
}: RunAfterApproveOrRejectProps) => {
  if (fromThirdParty) {
    window.location.reload()
  } else if (location.pathname?.includes('approvePermission')) {
    setTimeout(() => {
      if (window.isPC) {
        Dialog.alert({
          content: i18n.get('审批成功, 点击确认关闭本窗口'),
          confirmText: i18n.get('确认'),
          onConfirm: () => {
            window.opener = null
            window.open('', '_self')
            window.close()
          }
        })
      } else {
        onApprovePermissionSuccess?.()
        toast.success(i18n.get('审批成功'))
      }
    }, 1.8)
  } else {
    if (isApproveNext && onApproveNext) {
      onApproveNext()
    } else {
      const urlParams = qs.parse(window.location.search.slice(1))
      if(getBoolVariation('cyxq-75554')&& urlParams?.messagePurpose === 'sharing' && window.isDingtalk) {
        api.invokeService('@layout:close:window')
      }
      api.go(-1)
    }
    initSurvey()
  }
}