import { app as api } from '@ekuaibao/whispered'
import { BillActionBase, BillActionBaseProps } from './base-action'
import { action } from './constant'

export class ModifyAction extends BillActionBase<BillActionBaseProps> {
  action = action.modify
  label = i18n.get('修改')

  onAction = async () => {
    const {
      flowId: { id, formType },
      ownerId,
    } = this.backlog
    const { getTargetUrl } = this.propsForAction
    let url = `/modify/${formType}/${id}/${ownerId}?source=Modify`
    if (getTargetUrl) {
      url = getTargetUrl({id, formType, ownerId}, url)
    }
    return api.go(url)
  }
}