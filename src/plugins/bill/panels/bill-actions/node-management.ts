import { app as api } from '@ekuaibao/whispered'
import { BillActionBase } from './base-action'
import { action } from './constant'
import { hangUpAction } from '../../../approve/approveFetchUtil'
import { logInfo } from '../../utils/billUtils'
import { toast } from '../../../../lib/util'
import { handleShift as _handleShift } from './node-management.utils'

export class ShiftApproveAction extends BillActionBase {
  action = action.addnode
  label = i18n.get('转交审批')

  onAction = async () => {
    await this.handleShift('SHIFT_NODE')
  }

  handleShift = async (transferType?: string) => {
    const continuousApproval = api.getState('@approve').continuousApproval
    const { params, backlog, onApproveNext } = this
    const { sourcePage } = params || {}
    return _handleShift({
      backlog,
      transferType,
      continuousApproval,
      sourcePage,
      onApproveNext,
    })
  }
}

export class AddNodeApproveAction extends BillActionBase {
  action = action.addSignNode
  label = i18n.get('加签审批')

  onAction = async () => {
    await this.handleShift()
  }

  handleShift = async (transferType?: string) => {
    const continuousApproval = api.getState('@approve').continuousApproval
    const { params, backlog, onApproveNext } = this
    const { sourcePage } = params || {}
    return _handleShift({
      backlog,
      transferType,
      continuousApproval,
      sourcePage,
      onApproveNext,
    })
  }
}

export class SuspendAction extends BillActionBase {
  action = action.hangUp
  label = i18n.get('暂挂审批')

  onAction = async () => {
    const {
      id,
      flowId: { form }
    } = this.backlog
    return hangUpAction([id]).then(_ => {
      logInfo(`暂挂审批${form.title}单据`)
      toast.success(i18n.get('操作成功'))
      this.refreshData()
    })
  }
}
