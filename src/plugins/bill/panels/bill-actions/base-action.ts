import { app } from '@ekuaibao/whispered'
import { Backlog } from '../types'

export interface BillActionBaseProps {
  backlog: Backlog
  actionName: string
  refreshData: () => void
  postAction: (action: string) => void
  preAction: (action: string) => Promise<void>
  params?: {
    sourcePage: string,
    fromThirdParty: string
  }
  bus: MessageEvent
  propsForAction?: Record<string, any>
  onApproveNext?: () => void
}

export interface BillActionBaseOptions {
  continuousApproval: boolean
}

export class BillActionBase<T extends BillActionBaseProps = BillActionBaseProps> {
  backlog: BillActionBaseProps['backlog']
  refreshData: BillActionBaseProps['refreshData']
  actionName: BillActionBaseProps['actionName']
  params?: BillActionBaseProps['params']
  propsForAction?: BillActionBaseProps['propsForAction']
  onApproveNext?: () => void

  constructor({
    backlog,
    refreshData,
    actionName,
    params,
    propsForAction,
    onApproveNext,
  }: T) {
    this.backlog = backlog;
    this.refreshData = refreshData
    this.actionName = actionName
    this.params = params
    this.propsForAction = propsForAction || {}
    this.onApproveNext = onApproveNext
  }

  onAction: () => Promise<any>

  onClick = async () => {
    return this.onAction()
  }
}
