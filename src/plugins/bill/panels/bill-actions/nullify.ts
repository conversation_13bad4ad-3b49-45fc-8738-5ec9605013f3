import { app as api } from '@ekuaibao/whispered'
import { BillActionBase } from './base-action'
import { action } from './constant'
import * as actions from '../../../approve/approve.action'
import { get } from 'lodash'

export class NullifyAction extends BillActionBase {
  action = action.nullify
  label = i18n.get('作废')

  onAction = async () => {
    const { flowId, id } = this.backlog
    const nodes = get(flowId, 'plan.nodes', [])
    const taskId = get(flowId, 'plan.taskId', '')
    const planNode = nodes.find(v => v.id === taskId) || {}
    const lockInvoiceWhenNullify = planNode?.config?.lockInvoiceWhenNullify
    return api.open('@basic:NullifyFlowModal', { lockInvoiceWhenNullify }).then((result: any) => {
      const data = {
        resubmitMethod: 'FROM_START',
        comment: '',
        name: 'freeflow.nullify',
        params: { nullifyType: result.checkedValue ? 'lockInvoice' : 'releaseInvoice' }
      }
      return api.dispatch(actions.setFlowNullify({ id: id, data })).then(() => {
        api.go(-1)
      })
    })
  }
}
