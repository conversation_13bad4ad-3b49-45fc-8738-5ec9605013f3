import React from "react"
import { get } from "lodash"
import { BillActionBase } from "./base-action"
import { Fetch } from "@ekuaibao/fetch"
import { Dialog, Input, InputRef } from "@hose/eui-mobile"
import { toast } from "../../../../lib/util"

export class ExportAction extends BillActionBase {
  label = i18n.get('导出全部附件')

  onAction = () => {
    return this.export()
  }

  export = async () => {
    const hasAttachment = this.checkAttachment()
    if (!hasAttachment) {
      toast.info(i18n.get('无可导出附件或附件是钉盘上传'))
      return
    }

    const ref = React.createRef<InputRef>()
    const flowId = this.backlog.flowId.id

    Dialog.confirm({
      title: i18n.get('导出全部附件'),
      content: (
        <>
          <div style={{ marginBottom: '20px' }}>
            {i18n.get('进程将在后台处理，请输入任务名称并在「导出管理」查看任务结果。')}
          </div>
          <Input
            ref={ref}
            placeholder={i18n.get('请输入任务名称')}
            onlyShowClearWhenFocus={false}
            clearable
            border
          />
        </>
      ),
      confirmText: i18n.get('导出'),
      onConfirm: async () => {
        const taskName = ref.current?.nativeElement?.value
        if (!taskName || taskName.length > 12) {
          toast.info(i18n.get('任务名不能为空或者不超过12个字符'))
          throw new Error()
        }
        Fetch.GET(`/api/v2/easyexcel/annex/export/async?taskName=${taskName}&flowId=${flowId}`)
        toast.info(i18n.get('附件异步导出中,请稍后到导出管理中查看!'), 3000)
      }
    })
  }

  checkAttachment = () => {
    const backlog = this.backlog
    const form = get(backlog, 'flowId.form', {})
    const formComponents = get(backlog, 'flowId.form.specificationId.components', [])
    const hasAttachment = this.checkAttachmentFn(form, formComponents)
    return hasAttachment || this.checkDetailsAttachment()
  }

  checkAttachmentFn = (form: any, components: any[]) => {
    const attachmentsArr = components.filter(it => it?.type === 'attachments')
    let hasAttachment = false
    if (attachmentsArr.length > 0) {
      for (let i = 0; i < attachmentsArr.length; i++) {
        const el = attachmentsArr[i]
        const attachments = form?.[el?.field]
        if (attachments?.length > 0) {
          for (let j = 0; j < attachments.length; j++) {
            const item = attachments[j]
            // 必须要有非钉盘的文件才能导出,都是钉盘文件不到处
            if (!item?.key?.startsWith('DP:{')) {
              hasAttachment = true
              break
            }
          }
          if (hasAttachment) {
            break
          }
        }
      }
    }
    return hasAttachment
  }

  checkDetailsAttachment = () => {
    const backlog = this.backlog
    const details = get(backlog, 'flowId.form.details', [])
    let hasAttachment = false
    for (let i = 0; i < details.length; i++) {
      const el = details[i]
      const form = get(el, 'feeTypeForm', {})
      const components = get(el, 'specificationId.components', [])
      const hasAttachment2 = this.checkAttachmentFn(form, components)
      if (hasAttachment2) {
        hasAttachment = true
        break
      }
    }
    return hasAttachment
  }
}