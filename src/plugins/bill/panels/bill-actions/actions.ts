import { FlowAction } from "../FlowAction";
import { ApproveAction } from "./approve";
import { RejectAction } from "./reject";
import { PrintAction, PrintInvoiceAction, PrintedAction, PrintRemindAction, PrintDocAction } from "./print";
import { StartSignatureAction, ViewSignatureAction } from "./signature";
import {
  AddExpressAction,
  SkipExpressAction,
  ReceiveAction,
  ReceiveExceptionAction,
  CancelReceiveExceptionAction,
} from "./send-receive";
import { ModifyAction } from "./modify";
import {
  AddNodeApproveAction,
  ShiftApproveAction,
  SuspendAction,
} from "./node-management";
import { NullifyAction } from "./nullify";
import { PayAction } from "./pay";
// import { WithdrawPayAction } from "./pay.withdraw";
// import { AdminSkipNodeAction } from "./admin";
// import { ActivateAction } from "./activate";
import { SaveAction } from "./save";
// import { SubmitAction } from "./submit";
import { ExportAction } from "./export";
import { AlterAction } from "./alter";
import { CopyAction } from "./copy";
import { RetractAction } from "./retract";
import { WithdrawAction } from "./withdraw";
// import { CancelAction } from "./cancel";
// import { DeleteAction } from "./delete";
import { UrgeAction } from "./urge";
import { MarkedReadAction } from "./cc";
import { CommentAction } from "./comment";
import { ShareAction } from "./share";
import { AddNoteAction } from "./note";
import { AuxiliaryInformationAction } from "./auxiliary";

export const actionsMap = {
  // [FlowAction.Save]: SaveAction,
  // [FlowAction.Submit]: SubmitAction,
  [FlowAction.Comment]: CommentAction,
  // 审批
  [FlowAction.Agree]: ApproveAction,
  [FlowAction.Reject]: RejectAction,
  [FlowAction.Withdraw]: WithdrawAction,
  // 支付
  [FlowAction.Pay]: PayAction,
  // 打印
  [FlowAction.Print]: PrintAction,
  [FlowAction.PrintInvoice]: PrintInvoiceAction,
  [FlowAction.PrintDoc]: PrintDocAction,
  [FlowAction.Printed]: PrintedAction,
  [FlowAction.PrintRemind]: PrintRemindAction,
  // 签署
  [FlowAction.StartSignature]: StartSignatureAction,
  [FlowAction.ViewSignature]: ViewSignatureAction,
  // [FlowAction.Activate]: ActivateAction,
  // 寄送
  [FlowAction.Send]: AddExpressAction,
  [FlowAction.SkipSend]: SkipExpressAction,
  [FlowAction.JumpExpress]: SkipExpressAction,
  // 收单
  [FlowAction.Receive]: ReceiveAction,
  [FlowAction.ReceiveException]: ReceiveExceptionAction,
  [FlowAction.CancelReceiveException]: CancelReceiveExceptionAction,

  [FlowAction.Nullify]: NullifyAction,
  [FlowAction.Modify]: ModifyAction,

  [FlowAction.ShiftNode]: ShiftApproveAction,
  [FlowAction.ShiftApprove]: ShiftApproveAction,
  [FlowAction.AddNode]: AddNodeApproveAction,
  [FlowAction.Suspend]: SuspendAction,
  [FlowAction.Urge]: UrgeAction,
  [FlowAction.MarkedRead]: MarkedReadAction,
  // [FlowAction.AdminSkipNode]: AdminSkipNodeAction,

  [FlowAction.Alter]: AlterAction,
  [FlowAction.Copy]: CopyAction,
  [FlowAction.Retract]: RetractAction,
  // [FlowAction.Cancel]: CancelAction,
  // [FlowAction.Delete]: DeleteAction,

  [FlowAction.ExportAllAttachment]: ExportAction,
  [FlowAction.Share]: ShareAction,

  [FlowAction.AddAnnotation]: AddNoteAction,
  [FlowAction.AuxiliaryInformation]: AuxiliaryInformationAction,
} as const;
