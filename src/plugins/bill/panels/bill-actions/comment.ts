import { app as api } from '@ekuaibao/whispered'
import { BillActionBase, BillActionBaseProps } from './base-action'
import { action } from './constant'
import { logInfo } from '../../utils/billUtils'

export class CommentAction extends BillActionBase<BillActionBaseProps & {noCheckPermissions: boolean, reload: () => void}> {
  action = action.commnet
  label = i18n.get('评论')

  onAction = async () => {
    return this.handleComment()
  }

  handleComment = () => {
    const id = this.backlog.flowId.id
    return api.open('@bill:BillComment').then(params => {
      return api.invokeService('@bill:comment:bill', { id, params }).then((_: never) => {
        logInfo(`评论${this.backlog.flowId.form.title}单据`)
        this.refreshData()
      })
    })
  }
}
