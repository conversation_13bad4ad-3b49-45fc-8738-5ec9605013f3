import { app as api } from "@ekuaibao/whispered";
import { Dialog } from "@hose/eui-mobile";
import { BillActionBase } from "./base-action";
import * as actions from "../../bill.action";
import { get } from "lodash";

export class WithdrawAction extends BillActionBase {
  label = i18n.get('撤回审批')

  onAction = async () => {
    return this.handleWithdraw()
  }

  getRejectNode = () => {
    const { backlog } = this
    const nodes = get(backlog, 'flowId.plan.nodes', [])
    const currentId = get(backlog, 'flowId.plan.taskId', '')
    const index = nodes.findIndex((_node: any) => _node.id === currentId)
    const prevNode = index > 0 ? nodes[index - 1] : {}
    return {
      nextId: currentId,
      rejectTo: prevNode.id,
    }
  }


  handleWithdraw = () => {
    const { flowId } = this.backlog
    const { nextId, rejectTo } = this.getRejectNode()
    return Dialog.confirm({
      title: i18n.get('提示'),
      content: i18n.get('确认发起撤回吗?'),
      onConfirm: () => api.dispatch(actions.approveWithdraw(flowId.id, rejectTo, nextId))
    })
  }
}