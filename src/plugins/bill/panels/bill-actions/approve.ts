import { BillActionBase, BillActionBaseProps } from './base-action'
import { getApproverRepeatConfig } from '../../../bill/bill.action'
import { action } from './constant'
import { Dialog } from '@hose/eui-mobile'
import { checkFlowAllRequiredFields } from '../../../approve/service'
import qs from 'qs'
import { app as api } from '@ekuaibao/whispered'
import { hideLoading, showLoading, toast } from '../../../../lib/util'
import { logInfo } from '../../utils/billUtils'
import * as actions from '../../../approve/approve.action'
import { runAfterApproveOrReject, initSurvey } from './actions.utils'

export class ApproveAction extends BillActionBase {

  action = action.agree
  label = i18n.get('同意')
  _approverRepeatStatus = false
  _approverRepeatMessage = ''

  constructor(props: BillActionBaseProps) {
    super(props)
  }

  _init = async () => {
    const { flowId } = this.backlog
    const res = await getApproverRepeatConfig(flowId.id)
    if (res?.value) {
      const { isRepeated, message } = res.value
      this._approverRepeatStatus = isRepeated
      this._approverRepeatMessage = message
    }
  }

  onAction = async () => {
    await this._init()
    return this.onAgree()
  }

  // 逻辑来源于 approve-approving.js
  onAgree = async () => {
    const { params, backlog, propsForAction, onApproveNext } = this
    const { onApprovePermissionSuccess } = propsForAction || {}
    const { sourcePage, fromThirdParty: defaultFromThirdParty } = params || {}
    const continuousApproval = api.getState('@approve').continuousApproval;
    // 如果第三方地址栏携带backLastPage=true，则表示从第三方页面返回
    const urlParams = qs.parse(window.location.search.slice(1))
    let fromThirdParty = defaultFromThirdParty
    if (urlParams?.backLastPage === 'true') {
      fromThirdParty = window.isPC ? defaultFromThirdParty : undefined
    }

    const allFieldCheckIn = await checkFlowAllRequiredFields([backlog.flowId])
    if (!allFieldCheckIn) return

    if (this._approverRepeatStatus) {
      return Dialog.alert({ title: i18n.get('温馨提示'), content: this._approverRepeatMessage })
    }

    // 获取风险警告信息
    const riskwarning = api.getState()['@common'].riskwarning

    try {
      // 打开审批弹窗
      const data: any = await api.open('@basic:PlanResolve', {
        data: { backlog },
        riskWarningCount: riskwarning?.moneyCount,
        isBudgetExcited: !!riskwarning ? riskwarning.riskWarning : [],
        isShowRiskNotice: true,
        selectData: [backlog]
      })

      const resultData = { ...(data || {}), name: 'freeflow.agree' }

      showLoading()
      const isApproveNext = continuousApproval && sourcePage === 'approving'
      const result = await api.invokeService('@bill:get:current:backLog', String(backlog.flowId.id))
      const id = isApproveNext ? backlog.id : result.id || backlog.id

      // 执行同意操作
      await api.dispatch(actions.doAction({ id }, resultData))

      // 记录日志
      logInfo(`同意${backlog.flowId.form.title}单据`)

      // 临时授权接口增加【审批完成后跳转的地址】参数，仅pageType=form/backlogDetail时有效。
      const urlState = qs.parse(window.location.search.slice(1))
      if (urlState && ['form', 'backlogDetail'].includes(String(urlState.pageType)) && urlState.approvalUrl) {
        hideLoading()
        return api.invokeService('@layout:open:link', String(urlState.approvalUrl))
      }

      hideLoading()

      runAfterApproveOrReject({
        fromThirdParty,
        isApproveNext,
        onApproveNext,
        onApprovePermissionSuccess,
        initSurvey: () => this._initSurvey()
      })

    } catch (err) {
      console.log(err)
      hideLoading()
    }
  }

  _initSurvey = () => {
    // 问卷调查逻辑
    const specificationId = this.backlog?.flowId?.form?.specificationId || {}
    initSurvey(specificationId)
  }
}
