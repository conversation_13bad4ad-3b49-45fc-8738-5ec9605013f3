import { app } from "@ekuaibao/whispered"
import { get } from "lodash"
import { BillActionBaseProps } from "./base-action"

export interface PrintActionProps extends BillActionBaseProps {
  privilegeId: string
  showAllFeeType: boolean
  handlePrintCallback: () => void
  reload: () => Promise<void>
}

export enum PrintType {
  Print = '0',
  PrintInvoice = '1',
}

export const handlePrint = ({
  backlog,
  privilegeId,
  showAllFeeType,
  handlePrintCallback,
  reload,
  printType,
}: PrintActionProps & { printType: PrintType }) => {
  const { doPrint } = app.require<any>('@audit/service-print')
  let dataSource = backlog.flowId
  let obj = app.invokeService('@share:get:print:param', dataSource)
  let data = [obj]
  // @ts-ignore
  data.privilegeId = privilegeId
  // @ts-ignore
  data.showAllFeeType = showAllFeeType
  doPrint(
    data,
    !!privilegeId,
    () => {
      reload &&
        reload().then(() => {
          handlePrintCallback && handlePrintCallback()
        })
      app.invokeService('@common:insert:assist:record', {
        title: '打印完成' + get(dataSource, 'form.title') + '单据'
      })
      app.close()
    },
    false,
    printType
  )
}