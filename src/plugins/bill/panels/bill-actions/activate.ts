import { message, Modal } from "@hose/eui"
import { BillActionBase } from "./base-action"
import { app } from "@ekuaibao/whispered"

export class ActivateAction extends BillActionBase {
  label = i18n.get('激活')

  onAction = () => {
    this.handleActiveFlow()
  }

  handleActiveFlow = () => {
    const { backlog, bus } = this.props
    app.close()
    Modal.confirm({
      title: i18n.get('确认激活单据吗？'),
      content: i18n.get('此单据将会重新置于制单人的【我的单据】中，制单人可以重新提交单据。'),
      onOk: () => {
        app
          .invokeService('@bills:activate:flow', { name: 'freeflow.activate', id: backlog?.flowId?.id })
          .then(() => {
            message.success(i18n.get('激活成功'))
            bus.reload()
          })
          .catch((err: Error) => {
            message.error(err)
            bus.reload()
          })
      },
      okText: i18n.get('确定'),
      cancelText: i18n.get('取消')
    })
  }

}