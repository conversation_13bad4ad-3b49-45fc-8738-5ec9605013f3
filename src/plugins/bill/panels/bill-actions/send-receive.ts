import { app as api } from '@ekuaibao/whispered'
import { BillActionBase } from './base-action'
import { action } from './constant'
import {
  hideLoading,
  printCancelReceiveExceptionAlert,
  printReceiveExceptionAlert,
  showLoading
} from '../../../../lib/util'
import { Dialog } from '@hose/eui-mobile'
import {
  cancelReceiveExceptionExpresses,
  receiveExceptionExpresses,
  receiveExpresses
} from '../../../approve/approveFetchUtil'

export class AddExpressAction extends BillActionBase {
  action = action.addExpress
  label = i18n.get('添加寄送信息')

  onAction = () => {
    return api.open('@basic:ExpressAdd', { backlog: this.backlog, label: i18n.get('添加寄送信息') })
  }
}

export class SkipExpressAction extends BillActionBase {
  action = action.jumpExpress
  label = i18n.get('跳过寄送')

  onAction = () => {
    return api.open('@basic:ExpressAdd', { backlog: this.backlog, label: i18n.get('跳过寄送'), type: 'skipExpress' })
  }
}

export class ReceiveAction extends BillActionBase {
  action = action.receiveExpress
  label = i18n.get('确认收单')

  onAction = async () => {
    const backlog = this.backlog
    return api.open('@basic:PlanResolve', { data: { backlog } }).then((value: any) => {
      showLoading()
      return receiveExpresses({ backlogIds: [backlog.id], ...value }).then((data: any) => {
        hideLoading()
        const list = (data.value && data.value.errors) || []
        const errorList = list.filter((item: any) => item.resultCode !== 'OK')
        if (errorList.length) {
          Dialog.alert({ title: i18n.get('操作失败'), content: data.value.errors[0].message })
          return
        }
        api.go(-1)
      })
    })
  }
}

export class ReceiveExceptionAction extends BillActionBase {
  action = action.receiveExceptionExpress
  label = i18n.get('收单异常')

  onAction = () => {
    const backlog = this.backlog
    return new Promise((resolve, reject) => {
      printReceiveExceptionAlert(() => {
        showLoading()
        receiveExceptionExpresses({ backlogIds: [backlog.id], comment: '收单异常' })
          .then((data: any) => {
            hideLoading()
            const list = (data.value && data.value.errors) || []
            const errorList = list.filter((item: any) => item.resultCode !== 'OK')
            if (errorList.length) {
              Dialog.alert({ title: i18n.get('操作失败'), content: data.value.errors[0].message })
              reject('收单异常操作失败')
              return
            }
            api.go(-1)
            resolve(true)
          })
          .catch(reject)
      })
    })
  }
}

export class CancelReceiveExceptionAction extends BillActionBase {
  action = action.cancelReceiveExceptionExpress
  label = i18n.get('取消收单异常')
  onAction = () => {
    const backlog = this.backlog
    return new Promise((resolve, reject) => {
      printCancelReceiveExceptionAlert(() => {
        showLoading()
        cancelReceiveExceptionExpresses({ backlogIds: [backlog.id], comment: '取消收单异常' })
          .then((data: any) => {
            hideLoading()
            const list = (data.value && data.value.errors) || []
            const errorList = list.filter((item: any) => item.resultCode !== 'OK')
            if (errorList.length) {
              Dialog.alert({ title: i18n.get('操作失败'), content: data.value.errors[0].message })
              reject('取消收单异常操作失败')
              return
            }
            api.go(-1)
            resolve(true)
          })
          .catch(reject)
      })
    })
  }
}
