import { app as api } from "@ekuaibao/whispered"
import { checkFlowAllRequiredFields } from "../../../approve/service"
import * as actions from '../../../approve/approve.action'
import { Backlog } from "../types"

export const handleShift = async ({
  backlog,
  transferType,
  continuousApproval,
  sourcePage,
  onApproveNext,
}: {
  backlog: Backlog,
  transferType?: string,
  continuousApproval: boolean,
  sourcePage: string,
  onApproveNext: () => void,
}) => {
  const isPaying = backlog.state === 'PAYING'
  const { value: rejectedAddNode } = await api.invokeService('@common:get:toggle:switch:by:name', {
    key: 'tg_fix_rejected_addNode'
  })
  const data = await api.open<{type: string}>(!transferType && rejectedAddNode ? '@basic:RejectedAddNodeModal' : '@basic:PlanShift', {
    isPaying,
    flowDatas: [backlog],
    isSignShift: !transferType
  })
  // 后加签需要校验表单是否有必填字段
  if (data.type == 'AFT_ADD_NODE') {
    const allFieldCheckIn = await checkFlowAllRequiredFields([backlog.flowId])
    if (!allFieldCheckIn) return
  }
  await api.dispatch(actions.doAction({ id: backlog.id }, { ...data, name: 'freeflow.addnode' }))
  if (continuousApproval && sourcePage === 'approving' && !isPaying && onApproveNext) {
    onApproveNext()
  } else {
    api.go(-1)
  }
}