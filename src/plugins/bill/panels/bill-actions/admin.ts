import { ILayerProps } from "@ekuaibao/enhance-layer-manager"
import { BillA<PERSON>B<PERSON>, BillActionBaseProps } from "./base-action"
import { app } from "@ekuaibao/whispered"

class AdminNodeAction extends BillActionBase<BillActionBaseProps & {layer: ILayerProps['layer'], privilegeId: string}> {

  handleAdminOperateNode = async (type: 'adminAddNode' | 'adminSkipNode') => {
    const { privilegeId, backlog, layer } = this.props
    const result = await app.invokeService('@order-manage:adminOperateNode', {
      line: backlog.flowId,
      privilegeId,
      type
    })
    result && layer?.emitOk()
  }
}

export class AdminAddNodeAction extends AdminNodeAction {
  label = i18n.get('加签')

  onAction = () => {
    return this.handleAdminOperateNode('adminAddNode')
  }
}

export class AdminSkipNodeAction extends AdminNodeAction {
  label = i18n.get('跳过')

  onAction = () => {
    return this.handleAdminOperateNode('adminSkipNode')
  }
}