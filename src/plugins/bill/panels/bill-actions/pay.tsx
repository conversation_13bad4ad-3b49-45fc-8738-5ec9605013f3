import React from 'react'
import { action } from './constant'
import { BillActionBase, BillActionBaseProps } from './base-action'
import { app as api } from '@ekuaibao/whispered'
import { Dialog, Checkbox } from '@hose/eui-mobile'
import { checkFlowAllRequiredFields } from '../../../approve/service'
import { logInfo } from '../../utils/billUtils'
import * as actions from '../../../approve/approve.action'
import { getApproveFlowConfig } from '../../../approve/approve.action'
import qs from 'qs'
import { thousandBitSeparator } from '../../../../components/utils/fnThousandBitSeparator'
import { get } from 'lodash'

export interface PayActionProps extends BillActionBaseProps {
  onZeroPayment?: () => void // 零元支付回调
}

export class PayAction extends BillActionBase<PayActionProps> {
  action = action.pay
  label = i18n.get('支付')
  main = true

  _isAutographTips = false
  _signChecked = true

  _userAutograph = api.getState()['@common'].me_info?.staff?.autograph
  _batchFlow_config: any

  _dialogHandler: any

  onAction = async () => {
    return this.onPay()
  }

  // 逻辑来源于 approve-approving.js 的 _handleAction 方法
  onPay = async () => {
    const { params, backlog } = this
    const { fromThirdParty: defaultFromThirdParty } = params || {}
    // 如果第三方地址栏携带backLastPage=true，则表示从第三方页面返回
    const urlParams = qs.parse(window.location.search.slice(1))
    let fromThirdParty = defaultFromThirdParty
    if (urlParams?.backLastPage === 'true') {
      fromThirdParty = window.isPC ? defaultFromThirdParty : undefined
    }

    const allFieldCheckIn = await checkFlowAllRequiredFields([backlog.flowId])
    if (!allFieldCheckIn) return

    // 检查支付金额
    if (backlog.flowId.form.payMoney.standard * 1 === 0) {
      // 零元支付逻辑
      api.dispatch(getApproveFlowConfig(backlog.flowId.id)).then(() => {
        this._batchFlow_config = api.getState()['@approve'].batchFlow_config
        logInfo(`支付${backlog.flowId.form.title}单据`)
        this.onConfirmZeroPayment()
      })
    } else {
      // 非零元支付逻辑
      try {
        await api.open('@basic:PlanPay', {
          data: {
            backlog: [backlog],
            onPay: (data: any) => api.dispatch(actions.payBackLog(data)),
            onConfirm: (data: any) => {
              logInfo(`支付了单据${backlog.flowId.form.title}`)
              return api.dispatch(actions.confirmPayment(data))
            }
          }
        })

        // 支付成功后的跳转逻辑
        if (fromThirdParty) {
          window.location.reload()
        } else {
          api.go(-1)
        }
      } catch (err) {
        console.log(err)
        // 用户取消支付或其他错误，不需要特殊处理
      }
    }
  }

  _zeroPayDialogOptions = () => {
    const stardardCurrency = api.getState()['@common'].stardardCurrency || {}
    const { symbol = '', scale = 2 } = stardardCurrency
    const zeroMoney = thousandBitSeparator(new Big(0).toFixed(scale))
    const _handleCancelZeroPay = this._handleCancelZeroPay.bind(this)
    const _handleZeroPay = this._handleZeroPay.bind(this)
    return {
      content: <div>
        <span style={{ color: '#54595b', fontSize: 15, fontWeight: 500 }}>
            {i18n.get('支付金额：{__k0}{__k1}', { __k0: symbol, __k1: zeroMoney })}
          </span>
          <br />
          <span style={{ color: '#b1b9bd', fontSize: 14, marginTop: 5 }}>{i18n.get('无需支付，确定即完成')}</span>
          <br />
          {this._isAutographTips && (
            <span style={{ color: '#FC3842', fontSize: 12, marginTop: 4 }}>
              {i18n.get('您还没有上传您的签名影像，请到我的-我的签名中进行维护')}
            </span>
          )}
          {this._userAutograph && (
            <div style={{ display: 'inline-block' }}>
              <Checkbox
                checked={this._signChecked}
                disabled={this._batchFlow_config?.mustBeUsedSignature}
                onChange={this._handleSignChange}
              >
                <span>{i18n.get('使用签名影像')}</span>
              </Checkbox>
            </div>
          )}
      </div>,
      closeOnAction: false,
      actions: [
        [
          { text: i18n.get('取消'), key: 'cancel', onClick: _handleCancelZeroPay  },
          { text: i18n.get('确定'), key: 'confirm', onClick: _handleZeroPay }
        ]
      ]
    }
  }

  onConfirmZeroPayment = () => {
    this._dialogHandler = Dialog.show(this._zeroPayDialogOptions())
  }

  _handleCancelZeroPay = () => {
    this._isAutographTips = false
    this._signChecked = true
    this._dialogHandler?.close()
  }

  _handleSignChange = (checked: boolean) => {
    this._signChecked = checked
  }

  _handleZeroPay = () => {
    const { backlog } = this
    let params = { flowIds: [backlog.flowId.id] } as { flowIds: string[], autographImageId?: string }
    const mustBeUsedSignature = get(this._batchFlow_config, 'mustBeUsedSignature')
    if (mustBeUsedSignature && !this._userAutograph) {
      if (this._isAutographTips === false) {
        this._isAutographTips = true
        this._dialogHandler?.close()
        this._dialogHandler = Dialog.show(this._zeroPayDialogOptions())
      }
    } else {
      if (this._signChecked && this._userAutograph) {
        params.autographImageId = this._userAutograph.key
      }
      api.dispatch(actions.zeroPay(params)).then((_: never) => {
        api.go(-1)
      }).finally(() => {
        this._dialogHandler?.close()
      })
    }
  }
}
