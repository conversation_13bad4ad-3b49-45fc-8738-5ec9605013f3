import { BillActionBase } from './base-action'
import { app as api } from '@ekuaibao/whispered'

/**
 * 申请事项变更
 */
export class AlterAction extends BillActionBase {
  label = i18n.get('变更')

  onAction = async () => {
    return this.handleAlter()
  }

  handleAlter = async () => {
    const { flowId: flow } = this.backlog
    const { formType } = flow
    const requisitionObj = flow.form?.specificationId?.configs?.find((i: any) => i.ability === 'requisition') || {}
    const mustRequire = !requisitionObj.optionalComment
    return api.open('@bill:BillChangeModal', { flowId: flow.id, formType, mustRequire })
  }
}
