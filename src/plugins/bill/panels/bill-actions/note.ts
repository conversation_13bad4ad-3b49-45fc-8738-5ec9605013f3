import { app as api } from '@ekuaibao/whispered'
import { BillActionBase } from './base-action'
import { action } from './constant'

export class AddNoteAction extends BillActionBase {
  action = action.addnode

  // label = i18n.get('添加批注')
  _inAddNoteMode = false


  constructor(props: any) {
    super(props)
    const { inAddNoteMode } = this.propsForAction
    this._inAddNoteMode = inAddNoteMode
    // this.label = this._inAddNoteMode ? i18n.get('退出批注模式') : i18n.get('添加批注')
  }

  onAction = async () => {
    const { onModeChange } = this.propsForAction
    const nextValue = !this._inAddNoteMode
    this._inAddNoteMode = nextValue
    api.invokeService('@bill:change:add:note:mode', nextValue)
    return onModeChange?.(nextValue)
  }
}