import React from 'react'
import { app } from '@ekuaibao/whispered'
import { Dialog } from '@hose/eui-mobile'
import { BillActionBase } from './base-action'
import { logInfo } from '../../utils/billUtils'
import { toast } from '../../../../lib/util'
import * as actions from '../../bill.action'
import { get } from 'lodash'

export class UrgeAction extends BillActionBase {
  label = i18n.get('催办')
  lastTime = 0

  onAction = async () => {
    return this.handleUrge()
  }

  handleUrge = async () => {
    const { backlog } = this

    // 获取审批人信息
    const approveMember = this.getApproveMember()

    return new Promise((resolve, reject) => {
      Dialog.confirm({
        title: i18n.get('发送催办消息'),
        content: (
          <div style={{ textAlign: 'left' }}>
            <span>{i18n.get('系统将发送一条消息提醒')} </span>
            <span style={{ fontWeight: 'bold' }}>{approveMember} </span>
            <span>{i18n.get('审批。若长时间未审批，建议通过其他方式联系审批人。')}</span>
          </div>
        ),
        confirmText: i18n.get('发送'),
        onConfirm: async () => {
          try {
            const { flowId } = backlog
            const { form, plan } = flowId

            logInfo(`催办${form.title}单据`)

            const newTime = new Date().valueOf()
            if (newTime - this.lastTime > 60000) {
              // 60秒内只能执行一次催办功能
              const { taskId } = plan
              await app.dispatch(actions.billRemind(flowId.id, taskId))
              this.lastTime = newTime
              resolve(true)
            } else {
              toast.error(i18n.get('操作频繁'))
              reject(new Error('操作频繁'))
            }
          } catch (error) {
            reject(error)
          }
        },
        onCancel: () => {
          reject(new Error('用户取消'))
        }
      })
    })
  }

  getApproveMember = () => {
    const { backlog } = this
    const { flowId } = backlog
    const { plan } = flowId
    const { taskId, nodes } = plan

    const currentNode = nodes.find((node: any) => node.id === taskId)

    if (currentNode.type === 'countersign') {
      const counterSigners = get(currentNode, 'counterSigners', [])
      const approvingSigners = counterSigners
        .filter((item: any) => item.state === 'APPROVING' || item.state === null)
        .map((item: any) => item.signerId.name)
      return i18n.get(`{__k0}等{__k1}人`, {
        __k0: approvingSigners.slice(0, 10).join(),
        __k1: approvingSigners.length
      })
    } else if (currentNode.type === 'ebot') {
      return 'Ebot'
    } else if (currentNode.type === 'invoicingApplication') {
      return i18n.get('开票申请')
    } else {
      return currentNode.approverId ? currentNode.approverId.name : i18n.get('未选择')
    }
  }
}