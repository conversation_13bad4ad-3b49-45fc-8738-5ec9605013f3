import { BillActionBase, BillActionBaseProps } from './base-action'
import { action } from './constant'
import { app as api } from '@ekuaibao/whispered'
import { Dialog } from '@hose/eui-mobile'
import { checkFlowAllRequiredFields } from '../../../approve/service'
import { hideLoading, showLoading } from '../../../../lib/util'
import { logInfo } from '../../utils/billUtils'
import * as actions from '../../../approve/approve.action'
import { initSurvey, getRejectData, runAfterApproveOrReject } from './actions.utils'
import qs from 'qs'

export class RejectAction extends BillActionBase<BillActionBaseProps> {
  action = action.reject
  label = i18n.get('驳回')

  onAction = async () => {
    return this.onReject()
  }

  // 逻辑来源于 approve-approving.js 的 _handleAction 方法
  onReject = async () => {
    const { params, backlog, propsForAction, onApproveNext } = this
    const { onApprovePermissionSuccess } = propsForAction || {}
    const { sourcePage, fromThirdParty: defaultFromThirdParty } = params || {}
    const continuousApproval = api.getState('@approve').continuousApproval;
    // 如果第三方地址栏携带backLastPage=true，则表示从第三方页面返回
    const urlParams = qs.parse(window.location.search.slice(1))
    let fromThirdParty = defaultFromThirdParty
    if (urlParams?.backLastPage === 'true') {
      fromThirdParty = window.isPC ? defaultFromThirdParty : undefined
    }

    const allFieldCheckIn = await checkFlowAllRequiredFields([backlog.flowId])
    if (!allFieldCheckIn) return

    // 获取风险警告信息
    const riskwarning = api.getState()['@common'].riskwarning

    //判断是否存在设置了单个节点的驳回配置，如果设置了，弹窗提示，走单个节点的驳回配置
    let nodes = backlog?.flowId?.plan?.nodes || []
    let taskId = backlog?.flowId?.plan?.taskId
    const rejectData = getRejectData(taskId, nodes) || {} as any

    try {
      // 打开驳回弹窗
      const data: any = await api.open('@basic:PlanReject', {
        data: { backlog },
        riskWarningCount: riskwarning?.moneyCount,
        isBudgetExcited: [], // reject 时不处理 isBudgetExcited
        isShowRiskNotice: true,
        spcSetting: rejectData?.spcSetting,
        spcResubmitMethod: rejectData?.spcResubmitMethod,
        spcRejectTo: rejectData?.id,
        selectData: [backlog]
      })

      const resultData = { ...(data || {}), name: 'freeflow.reject' }

      showLoading()
      const isApproveNext = continuousApproval && sourcePage === 'approving'
      const result = await api.invokeService('@bill:get:current:backLog', String(backlog.flowId.id))
      const id = isApproveNext ? backlog.id : result.id || backlog.id

      // 执行驳回操作 - 注意：驳回使用特殊的 spAgreeBackLog 方法
      const subData = [{ id, ...resultData, name: 'freeflow.reject' }]
      const res = await api.dispatch(actions.spAgreeBackLog({ data: subData }))
      if (res?.success === 0 && res?.errors?.length) {
        Dialog.alert({
          title: i18n.get('操作失败'),
          content: res.errors[0].message || i18n.get('驳回失败'),
          confirmText: i18n.get('确定')
        })
        hideLoading()
        return
      }

      // 记录日志
      logInfo(`驳回${backlog.flowId.form.title}单据`)

      // 临时授权接口增加【审批完成后跳转的地址】参数，仅pageType=form/backlogDetail时有效。
      const urlState = qs.parse(window.location.search.slice(1))
      if (urlState && ['form', 'backlogDetail'].includes(String(urlState.pageType)) && urlState.approvalUrl) {
        hideLoading()
        return api.invokeService('@layout:open:link', String(urlState.approvalUrl))
      }

      hideLoading()

      runAfterApproveOrReject({
        fromThirdParty,
        isApproveNext,
        onApproveNext,
        onApprovePermissionSuccess,
        initSurvey: () => this._initSurvey()
      })
    } catch (err) {
      console.log(err)
      hideLoading()
    }
  }

  _initSurvey = () => {
    const specificationId = this.backlog?.flowId?.form?.specificationId || {}
    initSurvey(specificationId)
  }
}