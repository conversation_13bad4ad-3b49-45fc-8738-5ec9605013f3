import { app as api } from '@ekuaibao/whispered'
import { Dialog } from '@hose/eui-mobile'
import { BillActionBase } from './base-action'
import { action } from './constant'
import { dealPrintData, doPrint } from '../../../approve/service'
import { printRemindAlert, toast } from '../../../../lib/util'
import { logInfo } from '../../utils/billUtils'
import * as actions from '../../../approve/approve.action'
import { printReceiveAction } from '../../../approve/approveFetchUtil'

export class PrintRemindAction extends BillActionBase {
  action = action.printRemind
  label = i18n.get('打印提醒')

  onAction = async  () => {
    const { id, form } = this.backlog.flowId
    return new Promise((resolve, reject) => {
      printRemindAlert(() => {
        api
          .dispatch(actions.printRemindAction([id]))
          .then((res: any) => {
            const { errors } = res
            if (errors.length > 0) {
              const err = errors[0]
              Dialog.alert({
                title: i18n.get('操作失败'),
                content: err.message,
                confirmText: i18n.get('确定'),
                onConfirm: () => {
                  api.go(-1)
                }
              })
              reject(err)
              return
            }
            logInfo(`提醒打印${form.title}单据`)
            toast.success(i18n.get('提醒成功'))
          })
          .then((_: any) => {
            this.refreshData()
            resolve(true)
          }).catch(reject)
      })
    })
  }
}

export class PrintAction extends BillActionBase {
  action = action.print
  label = i18n.get('打印单据')

  onAction = async () => {
    return new Promise((resolve, reject) => {
      if (this.backlog && this.backlog.flowId) {
        const backlogId = this.backlog.id
        let data = dealPrintData([backlogId], {
          [backlogId]: this.backlog
        })
        doPrint(data, null, () => {
          this.refreshData()
          resolve(true)
        }, '0')
      } else {
        reject('flowId is empty')
      }
    })
  }
}

export class PrintInvoiceAction extends BillActionBase {
  action = action.print
  label = i18n.get('打印单据和发票')

  onAction = async () => {
    return new Promise((resolve, reject) => {
      if (this.backlog && this.backlog.flowId) {
        const backlogId = this.backlog.id
        let data = dealPrintData([backlogId], {
          [backlogId]: this.backlog
        })
        doPrint(data, null, () => {
          this.refreshData()
          resolve(true)
        }, '1')
      } else {
        reject('flowId is empty')
      }
    })
  }
}

/**
 * 仅打印单据面单
 */
export class PrintDocAction extends BillActionBase {
  action = action.print
  label = i18n.get('打印单据')

  onAction = async () => {
    return new Promise((resolve, reject) => {
      if (this.backlog && this.backlog.flowId) {
        const backlogId = this.backlog.id
        let data = dealPrintData([backlogId], {
          [backlogId]: this.backlog
        })
        doPrint(data, null, () => {
          this.refreshData()
          resolve(true)
        }, '0', false)
      } else {
        reject('flowId is empty')
      }
    })
  }
}

export class PrintedAction extends BillActionBase {
  action = action.receivePrint
  label = i18n.get('收到打印')

  onAction = async () => {
    const { id, form } = this.backlog.flowId
    return printReceiveAction([id]).then(_ => {
      logInfo(`收到打印${form.title}单据`)
      toast.success(i18n.get('操作成功'))
      this.refreshData()
    })
  }
}
