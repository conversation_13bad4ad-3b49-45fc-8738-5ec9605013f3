export enum FlowAction {
  Save = 'freeflow.edit', // 保存草稿
  Submit = 'freeflow.submit', // 提交送审
  Comment = 'freeflow.comment', // 评论
  Withdraw = 'freeflow.withdraw', // 撤回审批
  // 审批
  Agree = 'freeflow.agree', // 审批同意
  Reject = 'freeflow.reject', // 驳回
  // 支付
  Pay = 'freeflow.pay', // 支付
  // 打印
  Print = 'freeflow.print', // 打印单据
  PrintInvoice = 'freeflow.printInvoice', // 打印单据和发票
  PrintDoc = 'freeflow.printDoc', // 打印文档
  Printed = 'freeflow.printed', // 已打印
  PrintRemind = 'freeflow.remind', // 打印提醒
  // 签署
  StartSignature = 'freeflow.start.signature', // 发起签署
  ViewSignature = 'freeflow.view.signature', // 查看签署
  Activate = 'freeflow.activate', // 激活
  // 寄送
  Send = 'freeflow.send', // 添加寄送
  SkipSend = 'freeflow.skip.send', // 跳过寄送
  // 收单
  Receive = 'freeflow.receive', // 收单
  ReceiveException = 'freeflow.receiveExcep', // 收单异常
  CancelReceiveException = 'freeflow.cancelReceiveExcep', // 取消收单异常

  Nullify = 'freeflow.nullify', // 作废
  Modify = 'freeflow.editApproving', // 修改

  ShiftNode = 'freeflow.shiftnode', // 转交审批
  AddNode = 'freeflow.addnode', // 加签审批
  AddSignNode = 'freeflow.addSignNode', // 加签审批
  Suspend = 'freeflow.suspend', // 暂挂审批
  Back = 'freeflow.back', // 回退审批
  JumpExpress = 'freeflow.jumpExpress', // 跳转寄送信息
  ShiftApprove = 'freeflow.shiftApprove', // 转交审批
  Urge = 'freeflow.urge', // 催办
  MarkedRead = 'freeflow.read', // 标为已读
  AdminSkipNode = 'freeflow.admin.skipnode', // 管理员跳过
  Alter = 'freeflow.alter', // 变更
  Copy = 'freeflow.copy', // 复制
  Retract = 'freeflow.retract', // 撤回
  Cancel = 'freeflow.cancel', // 取消
  Delete = 'freeflow.delete', // 删除
  ExportAllAttachment = 'freeflow.export', // 导出所有附件
  Share = 'freeflow.share', // 分享
  MarkRead = 'freeflow.markRead', // 标记为已读
  AuxiliaryInformation = 'freeflow.auxiliary',
  AddAnnotation = 'freeflow.addAnnotation' // 添加批注
}
