import React, { Fragment, PureComponent } from 'react'
import classNames from 'classnames'
import styles from './BillInfoReadonly.module.less'
import { MoneyMath } from '@ekuaibao/money-math'
import EkbPopup from '@ekuaibao/popup-mobile'
import { EnhanceConnect } from '@ekuaibao/store'
import { Dynamic } from '@ekuaibao/template'
import { app as api, app, UIContainer as Container } from '@ekuaibao/whispered'
import { List } from 'antd-mobile'
import { cloneDeep, debounce, get, once } from 'lodash'
import createDOMForm from 'rc-form/lib/createDOMForm'
import { parseShowValue2SaveValue } from '../utils/formatUtil'
import { getDetailCalculateMoney, getNodeValueByPath, isNeedPayComponent, toast } from '../../../lib/util'
import { getAutoCalFields } from '../bill.action'
import CreditPointLabel from '../elements/CreditPointLabel'
import { getAutoCalResultOnField } from '../utils/autoCalResult'
import { isTicketReview } from '../utils/BillInfoEditableUtils'
import { formatRiskWarnings } from '../utils/formatRiskData.formatRiskWarnings'
import {
  checkIsRemuneration,
  getFeeTypeVisibleList,
  logInfo,
  checkQuickExpends,
  getSpecificationHiddenFields
} from '../utils/billUtils'
import parseSpecification from '../utils/parseSpecification'
import { fnLinkDetailEntitiesValue } from '../utils/relatedExpenseLinkUtils'
import { showDetailsTips } from '../utils/showDetailsTips'
import { fnHideFieldsNote, fnFlowHideFields } from '../../../components/utils/fnHideFields'
import { getInvoiceDisableInfo, getInvoiceMarkInfo } from '../../invoice-form/utils/invoiceDisableUtils'
import { Resource } from '@ekuaibao/fetch'
const CodeLabel = app.require('@elements/puppet/CodeLabel')
const DisplayConfig = app.require('@elements/puppet/DisplayConfig')
const readonly = app.require('@components/dynamic/index.readonly')
const filterResults = app.require('@lib/parser.filterResults')
const presetFormulaValue = app.require('@lib/parser.presetFormulaValue')
const parseAsReadOnlyFormTemplate = app.require('@lib/parser.parseAsReadOnlyFormTemplate')
const parseSpecificationAsSelectDataSource = app.require('@lib/parser.parseSpecificationAsSelectDataSource')
const { getPayeeInfoByIds } = app.require('@detail/history-fetchUtil')
const { fnPreviewAttachments } = app.require('@components/utils/fnAttachment')
const MoneySummaryReadOnly = app.require('@elements/puppet/MoneySummary.readonly')
const ManualRepayment = app.require('@elements/puppet/repayment/ManualRepayment')
import { getV } from '@ekuaibao/lib/lib/help'
import { syncTravel } from '../../requisition/requisition.action'
import { GET } from '@ekuaibao/fetch'
import CalculateBillState from '../components/CalculateBillState/CalculateBillState'
import { fnFlowShowFields } from '../../../components/utils/fnShowFields'
import { __unsafe__getFileIdFromFileKey } from '../../../components/utils/fnAttachment'
import {
  endOpenFlowPerformanceStatistics,
  leaveFlowPerformanceStatistics,
  startLoadFlowPerformanceStatistics,
  reportBillPagePaintDuration,
  flowDetailsStatistics
} from '../../../lib/flowPerformanceStatistics'
import { Button } from '@hose/eui-mobile'
import { OutlinedDataLog } from '@hose/eui-icons'
import { SubmitterLoanTipView } from '../../../elements/puppet/WrittenOff/submitterLoanWrittenView'
import { switchDocmentRenewEnable } from '../../../lib/featbit/utils'

function create(T) {
  return createDOMForm({
    onValuesChange(props, changedValues) {
      setTimeout(() => {
        //先走的  onValuesChange
        //再运行  setFieldValue
        props.bus.emit('dynamic:value:changed', changedValues)
      }, 0)
    }
  })(T)
}

@EnhanceConnect(
  (state, props) => {
    const { type } = props
    const specificationList = parseSpecificationAsSelectDataSource(state['@bill'][`specification_${type}`] || [])
    return {
      specificationList,
      me_info: state['@common'].me_info,
      globalFields: state['@common'].baseDataProperties.data,
      feeTypeMap: state['@common'].feetypes.map,
      feetypes: state['@common'].feetypes.data,
      tripTypes: state['@common'].tripTypes.list,
      departmentTree: state['@common'].departments.data,
      noRootPathMap: state['@common'].departments.noRootPathMap,
      requisition: state['@requisition'].requisitionList,
      specification_group: state['@home'].specificationWithVersion.specification_group,
      newFilterFeeTypes: state['@common'].newFilterFeeTypes.items,
      standardCurrency: state['@common'].standardCurrency,
      showBillNotesInHistory: state['@bill'].showBillNotesInHistory,
      remunerationSetting: state['@home'].remunerationSetting,
      disableInvoicePower: state['@common'].powers.KA_DISABLE_INVOICE,
      KA_REEXCHANGE_PROCESSING: state['@common'].powers.KA_REEXCHANGE_PROCESSING,
      baseDataPropertiesMap: state['@common'].baseDataProperties.baseDataPropertiesMap
    }
  },
  {
    getAutoCalFields
  }
)
export default class BillInfoReadonly extends PureComponent {
  static defaultProps = {
    errorMessages: []
  }


  constructor(props, ...args) {
    super(props, ...args)
    const { specificationId } = props.value
    startLoadFlowPerformanceStatistics()
    const { template, ...others } = parseSpecification({ ...props, specification: specificationId }, null, true)
    const value = this.formatLinkDetailEntities(props.value)
    this.state = {
      hiddenFields: [],
      isRefresh: false,
      template: [],
      templateAll: [],
      ...others,
      calFields: {},
      riskInfo: {},
      showAllFeeType: false,
      value: value,
      allowViewLoanList: false
    }
    if (props.disableInvoicePower) {
      getInvoiceDisableInfo({ ...props, isReadOnlyPage: true }).then(disableInfo => {
        this.setState({ disableInfo })
      })
      getInvoiceMarkInfo({ ...props, isReadOnlyPage: true }).then(markInfo => {
        this.setState({ markInfo })
      })
    }

    this.fixInvoicePhotoFileIdMissProblem()
  }

  /**
   * XSG-32526
   * 修复这个工单专用配置，只有 2023/07/29-2023/07/31 的 SaaS 单据才需要修复
   * 由于这个工单造成了发票照片 file id 丢失，预览失效，所以需要通过 key 去还原 file id 来预览发票
   * @returns {Promise<void>}
   */
  async fixInvoicePhotoFileIdMissProblem() {
    const { value } = this.state
    const { details } = value ?? {}
    if (!details) {
      return
    }
    /**
     * 发票照片中，所有缺失发票的 fileId 的照片
     */
    const allInvoiceFile = details
      .reduce((r, detail) => {
        return r.concat(detail.feeTypeForm.invoiceForm?.attachments ?? [])
      }, [])
      .filter(v => !v?.fileId)

    const isPhotoFileIdMiss = allInvoiceFile.length > 0

    if (!isPhotoFileIdMiss) {
      return
    }

    const result = await __unsafe__getFileIdFromFileKey(allInvoiceFile.map(v => v.key))
    const map =
      result.items?.reduce((r, v) => {
        r[v.key] = v
        return r
      }, {}) ?? {}

    allInvoiceFile.forEach(file => {
      file.fileId = map[file.key]
    })

    this.setState({
      value: { ...value }
    })
  }

  formatLinkDetailEntities = cValue => {
    const details = get(cValue, 'details')
    if (details && details.length) {
      cValue.details = details.map(line => {
        return fnLinkDetailEntitiesValue(line)
      })
    }
    return cValue
  }

  componentDidMount() {
    api.dataLoader('@common.nodesAIAgentMap').load({
      currentMap: api.getState()['@common']?.nodesAIAgentMap || {},
      nodes: this.props.plan?.nodes || [],
      logs: this.props.logs || []
    })
    api.invokeService('@requisition:get:requisition:list')
    api.dataLoader('@common.specificationVersionConfig').load()
    const { flowId, showBillNotesInHistory, type, external, bus, value, sourcePage, billState } = this.props
    checkQuickExpends(flowId)
    this.judgeAllowViewLoanList()
    this.getHiddenFields(value?.specificationId)
    if (!showBillNotesInHistory && flowId) {
      api.invokeService('@bill:get:active:credit:rules:group', { flowId })
      api.invokeService('@bill:get:bill:notes', { flowId })
    }
    if (flowId && value.details?.length > 0) {
      api.invokeService('@bill:get:getFeeTypeChange', flowId)
    }
    if (flowId && window.__PLANTFORM__ === 'DING_TALK') {
      GET('/api/flow/v2/flows/process/task', {
        flowId
      })
    }
    this.fnCheckFeeTypeVisible()
    this.fnInitPayeeInfo()
    logInfo(`查看${get(value, 'title', '无标题')}单据`)
    external && bus.setFieldsExternalsData?.({ ...external.form })
    this.loadFlowRiskInfo()
    bus.watch('element:details:add:trips:click', this.handleShowTripDetail)
    // 申请单同步行程到第三方平台
    if (type === 'requisition') {
      syncTravel({ id: value?.id })
    }
    //添加单据查询组件费用明细的查看权限
    if (sourcePage === 'flowLinks') return this.setState({ showAllFeeType: true })
    flowDetailsStatistics({
      formType: type,
      state: billState || 'new',
      sourcePage: sourcePage
    })
    reportBillPagePaintDuration()
  }

  componentWillUnmount() {
    let { bus, flowId, value } = this.props
    bus.un('element:details:line:click', this.handleLineClick)
    bus.un('element:attachments:line:click', this.handleAttachment)
    bus.un('element:details:tips:click', this.handleTipsClick)
    bus.un('update:calculate:detail:template', this.updateCalDetailTemplate)
    window.detailShowType = undefined
    api.invokeService('@bill:set:payee:component:visibility') //清空相关数据
    EkbPopup.hide()
    bus.un('element:details:add:trips:click', this.handleShowTripDetail)
    leaveFlowPerformanceStatistics({ flowId, specification: value?.specificationId })
  }

  componentWillMount() {
    let { bus, ownerId = {} } = this.props
    const staffId = ownerId.id
    bus.watch('element:details:line:click', this.handleLineClick)
    bus.watch('element:attachments:line:click', this.handleAttachment)
    bus.watch('element:details:tips:click', this.handleTipsClick)
    api.invokeService('@common:get:payerInfo', { staffId })
    api.dataLoader('@common.departments').load()
    api.invokeService('@common:get:feeTypes')
    this.handleAutoCalFields()
    api.invokeService('@home:get:remuneration:setting')
    bus.on('update:calculate:detail:template', this.updateCalDetailTemplate)
  }

  loadFlowRiskInfo = async () => {
    const { value = {}, specification } = this.state
    const { bus, billState, type } = this.props
    const form = value
    const components = get(specification, 'components') || []
    const detailCmp = components.find(el => el.field === 'details')
    if (
      detailCmp?.realtimeCalculateBudget &&
      form?.details?.length > 0 &&
      ['approving', 'paying'].includes(billState)
    ) {
      this.tempId = new Date().getTime()
      api
        .invokeService('@bill:get:flow:risk:info', {
          formType: type,
          form: parseShowValue2SaveValue(form),
          state: billState,
          version: this.tempId
        })
        .then(riskData => {
          if (this.tempId.toString() === riskData.flowId) {
            const riskInfo = formatRiskWarnings(riskData, form?.details)?.form?.details
            if (riskInfo) {
              this.setState({ riskInfo })
            }
          }
        })
    }
  }

  fnCheckFeeTypeVisible = () => {
    const { type, flowId, me_info, showAllFeeType, sourcePage } = this.props
    if (type === 'permit') {
      return this.setState({ showAllFeeType: true })
    }
    const billTypeList = [
      'requisition',
      'expense',
      'reconciliation',
      'settlement',
      'receipt',
      'corpPayment',
      'reimbursement'
    ]
    if (!billTypeList.includes(type)) return null
    //单据查询组件费用明细的查看权限sourcePage === 'flowLinks'
    if (showAllFeeType || sourcePage === 'flowLinks') return this.setState({ showAllFeeType: true })
    const staffId = get(me_info, 'staff.id')
    getFeeTypeVisibleList({ flowId, staffId }).then(res => {
      const { state, data } = res
      if (state === 200) {
        const { viewAll } = data
        const feeTypes = data.feeTypes || []
        const apportionVisibleList = []
        const feeTypeVisibleList = feeTypes.map(el => {
          if (el.apportionIds && el.apportionIds.length > 0) {
            apportionVisibleList.push(...el.apportionIds)
          }
          return el.feeTypeId
        })
        const param = {
          flowId,
          showAllFeeType: viewAll,
          feeTypeVisibleList,
          apportionVisibleList
        }
        api.invokeService('@bill:save:feeType:visible:list', param)
        this.setState(param, this.forceUpdate)
      }
    })
  }

  fnSetPayeeInfoForPayPlanMode = value => {
    let { payPlan, details } = value
    if (!payPlan) return

    const payeeIds = payPlan.map(el => el.dataLinkForm.E_system_支付计划_收款信息)
    getPayeeInfoByIds(payeeIds).then(result => {
      const payees = result?.items ?? []
      payPlan = payPlan.map(el => {
        const payeeInfo = payees.find(payer => payer.id === el.dataLinkForm.E_system_支付计划_收款信息)
        el.dataLinkForm.E_system_支付计划_收款信息 = payeeInfo
        return el
      })
      details = details.map(detail => {
        const feeDetailPayeeId = detail?.specificationId?.components?.find(cp => cp?.field === 'feeDetailPayeeId')
        if (!feeDetailPayeeId) {
          delete detail.feeTypeForm.feeDetailPayeeId
        }
        return detail
      })
      this.setState({ value: { ...value, payPlan, details } })
    })
  }

  fnSetPayeeInfoForPayPlanByDetailMode = (value, payeeComponent) => {
    let { payPlan, details, paymentPlanByApportion } = value
    let payeeIds = []
    const legalEntityObj = {} // 收款法人实体数据
    if (payPlan) {
      payeeIds = payPlan.map(el => el.dataLinkForm.E_system_支付计划_收款信息)
    } else {
      details.forEach(detail => {
        if (detail.feeTypeForm.feeDetailPayeeId) {
          payeeIds.push(detail.feeTypeForm.feeDetailPayeeId)
        }
      })
    }
    getPayeeInfoByIds(payeeIds).then(result => {
      const payees = result?.items ?? []
      api.invokeService('@bill:save:current:payees', payees)
      api.invokeService('@bill:set:payee:component:visibility', { visible: true, payeeComponent })
      const legalEntityFormValue = getV(value, '法人实体')
      details = details.map(detail => {
        const feeDetailPayee = detail.specificationId.components.find(cp => cp.field === 'feeDetailPayeeId')
        if (!feeDetailPayee) {
          detail.specificationId.components.push(payeeComponent)
        }
        const payeeId = detail.feeTypeForm.feeDetailPayeeId
        const payeeInfo = payees.find(payer => payer.id === payeeId)
        detail.feeTypeForm.feeDetailPayeeId = payeeId?.id ? payeeId : payeeInfo
        // 按分摊明细生成支付计划时，收集分摊中法人实体，赋值到支付计划中
        if (paymentPlanByApportion) {
          const apportions = getV(detail, 'feeTypeForm.apportions')
          if (apportions) {
            apportions?.forEach(el => {
              const legalEntity = getV(el, 'apportionForm.法人实体')
              if (legalEntity) {
                legalEntityObj[legalEntity.id] = legalEntity
              }
            })
          }
        }
        return detail
      })
      payPlan = payPlan?.map(el => {
        const payeeInfo = payees.find(payer => payer.id === el.dataLinkForm.E_system_支付计划_收款信息)
        el.dataLinkForm.E_system_支付计划_收款信息 = payeeInfo
        // 按分摊明细生成支付计划时，将收集的法人实体数据，赋值到支付计划中
        if (paymentPlanByApportion && el.dataLinkForm.E_system_支付计划_legalEntity) {
          if (legalEntityObj[el.dataLinkForm.E_system_支付计划_legalEntity]) {
            el.dataLinkForm.legalEntity = legalEntityObj[el.dataLinkForm.E_system_支付计划_legalEntity]
          } else if (
            legalEntityFormValue &&
            typeof el.dataLinkForm.E_system_支付计划_legalEntity === 'string' &&
            el.dataLinkForm.E_system_支付计划_legalEntity === legalEntityFormValue?.id
          ) {
            // 勾选【包含分摊明细】，但支付计划中没有法人实体，在只读单据回显时，取单据上的法人实体
            el.dataLinkForm.legalEntity = legalEntityFormValue
          }
        }
        return el
      })
      this.setState({ value: { ...value, payPlan, details } })
    })
  }

  fnInitPayeeInfo = () => {
    const { specification, value } = this.state
    const { type, components, configs } = specification ?? {}
    if (type !== 'expense') return null
    const payConfig = configs?.find(el => el.ability === 'pay')
    const { allowMultiplePayees = false } = payConfig ?? {}
    const { multiplePayeesMode, payPlanMode } = value ?? {}
    if (allowMultiplePayees && multiplePayeesMode) {
      if (payPlanMode) {
        this.fnSetPayeeInfoForPayPlanMode(value)
      } else {
        const payeeIdComponent = components.find(el => el.field === 'payeeId') || {}
        const payeeComponent = { ...payeeIdComponent, field: 'feeDetailPayeeId', manualAdd: true }
        this.fnSetPayeeInfoForPayPlanByDetailMode(value, payeeComponent)
      }
    }
  }
  //审批人禁用后更新状态
  handleDisableInvoiceByApprover = (id, isMark) => {
    let { disableInfo, markInfo } = this.state
    if (isMark) {
      // 标记发票
      markInfo[id].mark = true
      this.setState({ markInfo })
    } else {
      // 禁用发票
      disableInfo[id].disable = true
      this.setState({ disableInfo })
    }
  }
  handleAutoCalFields = () => {
    const {
      value: { submitterId, specificationId },
      getAutoCalFields
    } = this.props
    if (!submitterId || !specificationId) return
    getAutoCalFields({
      submitterId: submitterId.id,
      specificationId: specificationId.id
    }).then(action => {
      if (action.error) return
      const autoRules = action.payload.items || []
      this.setState({ calFields: autoRules[0] })
      this.isFirstAutoCalFinished = false
      this.updateAutoCalResult(true)
      //隐藏必填支持公式在详情中，在知道哪些字段有计算公式后，去修改template，先不让其显示出来
      this.dataLogic(autoRules[0])
    })
  }

  dataLogic = autoCalFields => {
    const { flowRulePerformLogs, plan } = this.props
    const { specificationId } = this.props.value
    let { template } = parseSpecification({ ...this.props, specification: specificationId }, null, true)
    if (template) {
      template = parseAsReadOnlyFormTemplate(template)
      template = presetFormulaValue(filterResults(flowRulePerformLogs), template)
    }
    const _template = []
    const { currentNodeShowFieldMap } = fnFlowShowFields(plan)
    template.forEach(item => {
      if (currentNodeShowFieldMap[item.name]) {
        _template.push({ ...item, currentNodeShowField: true })
      } else if (autoCalFields?.onFields?.includes(item.name)) {
        const attrs = item?.configs || null || undefined
        if (attrs?.length) {
          let index = attrs.findIndex(el => el.ability === 'caculate' && el.property === 'hide')
          if (index === -1) {
            _template.push(item)
          }
        } else {
          _template.push(item)
        }
      } else {
        _template.push(item)
      }
    })
    _template.unshift({ name: 'specificationId', type: 'text', field: 'specificationId', label: i18n.get('模板名称') })
    //是否设置了审批流字段隐藏
    const flowHiddenFields = fnFlowHideFields(plan)
    const finalTemplate = _template.filter(v => !flowHiddenFields.includes(v?.name))
    this.setState({
      template: finalTemplate,
      templateAll: template
    })
  }

  isFirstAutoCalFinished = false
  updateAutoCalResult = debounce(async (checkDefaultValue = false) => {
    const { bus, baseDataPropertiesMap, state = {}, value } = this.props
    const { specification, template } = this.state
    try {
      let formValue = cloneDeep(value)
      formValue.specificationId = specification && specification.id
      if (state.flowId) {
        formValue.flowId = state.flowId
      }
      bus.emit('bill:loading:change', true)
      const needUpdateDefaultValue = checkDefaultValue
      const updateAutoCalResultAttribute = true
      await getAutoCalResultOnField(
        baseDataPropertiesMap,
        bus,
        specification,
        formValue,
        'master_',
        null,
        undefined,
        template,
        needUpdateDefaultValue,
        updateAutoCalResultAttribute
      )
      if (checkDefaultValue) {
        this.isFirstAutoCalFinished = true
      }
      bus.emit('bill:loading:change', false)
    } catch (e) { }
  }, 400)

  updateCalDetailTemplate = value => {
    const calValue = value
    let { templateAll: template } = this.state
    const { plan } = this.props
    const { currentNodeShowFieldMap, isShowFileds } = fnFlowShowFields(plan)
    const fields = Object.keys(value)
    fields?.forEach(field => {
      if (currentNodeShowFieldMap[field]) {
        return
      }
      let index = template.findIndex(el => el.field === field)
      if (index !== -1) {
        let attrs = calValue[field]
        let autoHide = attrs?.attributeHide
        const _hideVisibility = template[index]?.hideVisibility
        let hasStaffs = true
        const { departments, roles, staffs } = _hideVisibility || { departments: [], roles: [], staffs: [] }
        if (!departments?.length && !roles?.length && !staffs?.length) {
          hasStaffs = false
        }
        if ((autoHide && !hasStaffs) || (autoHide && !fnHideFieldsNote(_hideVisibility))) {
          template.splice(index, 1)
        } else {
          template[index] = { ...template[index], ...calValue[field] }
        }
      }
    })
    template.unshift({ name: 'specificationId', type: 'text', field: 'specificationId', label: i18n.get('模板名称') })
    if (isShowFileds) {
      template = template.map(field => {
        if (currentNodeShowFieldMap[field.name]) {
          return { ...field, currentNodeShowField: true }
        }
        return field
      })
    }
    //是否设置了审批流字段隐藏
    const flowHiddenFields = fnFlowHideFields(plan)
    const finalTemplate = template.filter(v => !flowHiddenFields.includes(v?.name))
    this.setState({ template: [...finalTemplate] })
  }

  componentWillReceiveProps(nextProps) {
    let { bus, external } = nextProps
    const curSpecificationList = getNodeValueByPath(this.props, 'specificationList', [])
    const nextSpecificationList = getNodeValueByPath(nextProps, 'specificationList', [])
    const curSpecificationId = getNodeValueByPath(this.props, 'value.specificationId', {})
    const nextSpecificationId = getNodeValueByPath(nextProps, 'value.specificationId', {})
    if (
      (curSpecificationList.length !== nextSpecificationList.length &&
        curSpecificationList !== nextSpecificationList) ||
      this.props.globalFields !== nextProps.globalFields ||
      curSpecificationId.id !== nextSpecificationId.id
    ) {
      const props = { ...nextProps, specification: nextSpecificationId }
      let { specification, template } = parseSpecification(props, null, true)
      if (template) {
        template = parseAsReadOnlyFormTemplate(template)
      }
      template.unshift({ name: 'specificationId', type: 'text', field: 'specificationId', label: i18n.get('模板名称') })
      this.getHiddenFields(specification)
      this.setState({
        ...parseSpecification(props, null, true),
        template
      })
    }
    if (this.props.external !== nextProps.external) {
      external && bus.setFieldsExternalsData?.({ ...external.form })
    }
    if (this.props.value !== nextProps.value) {
      const value = this.formatLinkDetailEntities(nextProps.value)
      this.setState({ value })
    }
  }

  getHiddenFields = async specification => {
    const hiddenFields = await getSpecificationHiddenFields(specification)
    this.setState({ hiddenFields })
  }

  handleTipsClick = data => {
    const {
      value: { submitterId }
    } = this.props
    showDetailsTips(data, submitterId.id)
  }

  handleLineClick = (line, idx, ds, submitterId, type, isHideWait, external, externals, invoiceRiskData, riskInfo) => {
    const {
      flowRulePerformLogs,
      flowId,
      bus,
      ownerId,
      isEditTaxRate,
      billState,
      value,
      remunerationData,
      isModifyBill,
      plan
    } = this.props
    const { specification, disableInfo, markInfo } = this.state
    const canEditNote = billState !== 'draft' && billState !== 'rejected'
    const risks = filterResults(flowRulePerformLogs, 'details')
    const { feeTypeId, feeTypeForm, specificationId, apportionVisibleList, showAllFeeType } = line
    const billType = type
    const ticketReview = isTicketReview(get(value, 'specificationId'))
    const specificationCurrent = get(value, 'specificationId')
    const isRemuneration = checkIsRemuneration(specificationCurrent.id || specificationCurrent)
    if (isRemuneration) {
      return app.open('@bill:Remuneration', {
        isModifyBill: isModifyBill,
        type: 'readonly',
        flow: remunerationData,
        bus: bus
      })
    }
    return api.open('@feetype:FeeTypeInfoModal', {
      risks,
      ds,
      isEdit: false,
      value: feeTypeForm,
      feetype: feeTypeId,
      template: specificationId,
      idx,
      submitterId,
      billType,
      flowId,
      billBus: bus,
      isHideWait,
      external,
      externals,
      riskInfo,
      ownerId,
      isEditTaxRate,
      showAllFeeType,
      apportionVisibleList,
      canEditNote,
      isTicketReview: ticketReview,
      invoiceRiskData: invoiceRiskData,
      billSpecification: specification,
      formAllData: value,
      disableInfo,
      markInfo,
      plan,
      onDisableInvoiceByApprover: this.handleDisableInvoiceByApprover,
      notShowModalIfAllInvoiceSuccess: true
    })
  }

  handleAttachment = (value, index) => {
    fnPreviewAttachments({ value, index })
  }

  handleSubmitterLoan = () => {
    const { carbonCopyType, type, flowId, value } = this.props
    if (carbonCopyType !== 'carbonCopy' && type !== 'requisition') {
      api
        .dataLoader('@common.submitter_loans')
        .reload({
          submitterId: value.submitterId.id,
          flowId,
          state: 'REPAID',
          start: 0,
          count: 2999
        })
        .then(items => {
          if (items && items.length) {
            let { submitterId } = this.state.value
            api.go('/submitter/' + submitterId.id + '/' + flowId)
          } else {
            toast.info(i18n.get('无可核销借款！'))
          }
        })
        .catch(e => {
          toast.info(i18n.get('无可核销借款！'))
        })
    }
  }

  fnRefreshFilterTripTypes = (tripTypes, specification, bus) => {
    let newTripTypes = cloneDeep(tripTypes)
    return bus.getFieldsValue().then(values => {
      let currentTrips = values['trips'] || []
      let tripList = newTripTypes.filter(trip => trip.active)
      const { configs = [] } = specification
      const tripTypeConfig = configs.filter(config => config.tripType && config.tripType.isAll === false)
      if (tripTypeConfig.length > 0) {
        tripList = tripList.filter(trip => tripTypeConfig[0].tripType.ids.includes(trip.id))
      }
      currentTrips.forEach(trip => {
        if (!tripList.includes(trip.tripTypeId.id)) {
          let ban = newTripTypes.find(t => t.id === trip.tripTypeId.id)
          if (!ban.active) {
            ban.name = ban.name + i18n.get('（已停用）')
            tripList.unshift(ban)
          }
        }
      })
      return api.invokeService('@common:getFilterTripTypes', { filterTripTypes: tripList })
    })
  }

  handleShowTripDetail = (external, fn, update, canEdit, currentTrips) => {
    let { bus, tripTypes, value = {} } = this.props
    let { flowId, template, specification } = this.state
    const originTrips = cloneDeep(getV(value, 'trips', []))
    const trips = canEdit ? originTrips : [currentTrips]
    return bus.getValue().then(currentValue => {
      if (flowId) {
        currentValue.flowId = flowId
      }
      const { submitterId } = currentValue
      return this.fnRefreshFilterTripTypes(tripTypes, specification, bus).then(tripList => {
        if (tripList.filterTripTypes.length === 0) {
          return toast.error(i18n.get('无可用行程类型'))
        }
        return api.open('@trip-type:TripTypeEditModal', {
          flowId,
          fn,
          bus,
          shouldUpdate: update,
          external,
          submitterId,
          billTemplate: template,
          billData: currentValue,
          originTrips,
          specification,
          canEdit,
          trips
        })
      })
    })
  }

  refresh = () => {
    this.setState(prev => ({
      isRefresh: !prev.isRefresh
    }))
  }

  reportPerformanceTime = once(() => {
    const { flowId, value } = this.props
    endOpenFlowPerformanceStatistics({ flowId, specification: value?.specificationId })
  })

  judgeAllowViewLoanList = async () => {
    const configRule = new Resource('/api/v2/loan/config')

    const userInfo = await api.getState()['@common'].me_info
    const loanConfig = await configRule.GET('')

    const hasLoanManage = userInfo?.permissions?.includes("LOAN_MANAGE");
    const restrictAccessItem = loanConfig?.items?.find(item => item?.type === "RESTRICT_ACCESS")?.forbid

    if (userInfo?.staff?.id === this.props.value?.submitterId?.id) { // 自己看自己的单据
      this.setState({ allowViewLoanList: true })
    } else if (userInfo?.staff?.id === this.props.ownerId?.id) { // 委托人查看单据
      this.setState({ allowViewLoanList: true })
    } else {
      this.setState({ allowViewLoanList: !restrictAccessItem || hasLoanManage });
    }
  }

  render() {
    let {
      bus,
      value: { submitterId, writtenOffMoney, partialPayState },
      flowId,
      writtenOff,
      tags,
      type,
      plan,
      showLoan,
      errorMessages,
      globalFields,
      noRootPathMap,
      isHideWait,
      ownerId = {},
      standardCurrency,
      billState,
      invoiceRiskData,
      KA_REEXCHANGE_PROCESSING,
      auto,
      arrangeLayout = [],
      renderRiskNotice,
      renderLogsCardView,
      renderFlowNodeView,
      renderTripOrderAction,
      skipCheck,
      sourcePage
    } = this.props
    let {
      hiddenFields,
      specification,
      template,
      value,
      calFields,
      showAllFeeType,
      feeTypeVisibleList,
      apportionVisibleList,
      riskInfo,
      isRefresh,
      allowViewLoanList
    } = this.state

    if (!specification || !template.length || !value) {
      return null
    }

    this.reportPerformanceTime()
    tags = { ...tags, details: { filterFeeTypeIds: [] }, isOpenAssociation: { tag: value, plan: plan } }

    const { details, multiplePayeesMode, payPlanMode, payeePayPlan, payPlan, payeeId } = value ?? {}

    // 核销金额要减去公司支付部分
    let writtenOffAmount = '0.00'
    if (details) {
      const { companyPayMoney, payDetail } = getDetailCalculateMoney(details, type)
      writtenOffAmount = new MoneyMath(payDetail.value).minus(companyPayMoney).value
    }
    const canEditNote = billState !== 'draft' && billState !== 'rejected'
    const isNeedWrittenoff = api.invokeService('@writtenoff:isneedwrittenoff', specification)
    const isReceiptTemplate = specification?.type === 'receipt'
    const alterFlag = value.alterFlag >= '1'

    let noPayInfo = false // 收款信息是否显示‘无’ true显示无
    // 多收款人根据是否有支付计划判断 noPayInfo值
    if (multiplePayeesMode && !payPlan?.length) {
      noPayInfo = true
    }
    // 单收款人根据是否有payeeId判断 noPayInfo值
    if (!multiplePayeesMode && !payeeId) {
      noPayInfo = true
    }
    const loanManualRepayment =
      (this.props.data && this.props.data.loanManualRepayment && this.props.data.loanManualRepayment.records) ||
      (this.props.loanManualRepayment && this.props.loanManualRepayment.records) ||
      []
    const replayAbility = specification.configs.filter(item => {
      return item.ability === 'allowRepayment'
    })
    const isAllowedRepayment = (replayAbility.length && replayAbility[0].isAllowedRepayment) || false
    const isAllowedRepaymentForeign = (replayAbility.length && replayAbility[0].isAllowedRepaymentForeign) || false
    const isForbiddenSubmit = (replayAbility.length && replayAbility[0].isForbiddenSubmit) || 'Y'

    return (
      <div id="bill_info_readOnly" className={classNames(styles.bill_info_readOnly_wrap, { refresh: isRefresh })}>
        {arrangeLayout && arrangeLayout.length > 0 ? (
          arrangeLayout.map(arrangeLayoutItem => {
            switch (arrangeLayoutItem.key) {
              case 'risks':
                return renderRiskNotice ? renderRiskNotice() : <div key="RiskNoticeDiv" />
              case 'status':
                return renderLogsCardView ? renderLogsCardView() : <div key="LogsCardViewDiv" />
              case 'travel_order':
                return renderTripOrderAction ? renderTripOrderAction() : <div key="TripOrderActionDiv" />
              case 'details':
                return (
                  <List className={styles.list_body} key="DetailList">
                    <CalculateBillState type={type} billState={billState} skipCheck={skipCheck} flowId={flowId} />
                    <Dynamic
                      {...this.props}
                      hiddenFields={hiddenFields}
                      billSpecification={specification}
                      errorMessages={errorMessages}
                      bus={bus}
                      riskInfo={riskInfo}
                      elements={readonly}
                      create={create}
                      template={template}
                      value={value}
                      noRootPathMap={noRootPathMap}
                      calFields={calFields}
                      submitterId={submitterId}
                      globalFields={globalFields}
                      tags={tags}
                      isHideWait={isHideWait}
                      multiplePayeesMode={multiplePayeesMode}
                      payPlanMode={payPlanMode}
                      payeePayPlan={payeePayPlan}
                      showAllFeeType={showAllFeeType}
                      feeTypeVisibleList={feeTypeVisibleList}
                      apportionVisibleList={apportionVisibleList}
                      canEditNote={canEditNote}
                      invoiceRiskData={invoiceRiskData}
                      noPayInfo={noPayInfo}
                      notShowModalIfAllInvoiceSuccess={true}
                      isReadyOnly={true}
                      receivingCurrency={value.receivingCurrency}
                      dataSource={this.props.value}
                      businessType={'FLOW'}
                    />
                  </List>
                )
              case 'payPlan':
                return (
                  <Container
                    key="BillPayPlanReadonly"
                    name="@bill:BillPayPlanReadonly"
                    bus={bus}
                    value={value}
                    billState={billState}
                    payPlans={payPlan}
                    multiplePayeesMode={multiplePayeesMode}
                    inPartialPayState={!!partialPayState}
                    KA_REEXCHANGE_PROCESSING={KA_REEXCHANGE_PROCESSING}
                  />
                )
              case 'writtenOff':
                if (!isNeedWrittenoff) return <div key="WrittenOffDiv" />
                return !isAllowedRepayment ? (
                  <Container
                    key="WrittenOffWrapper"
                    name="@writtenoff:WrittenOffWrapper"
                    style={{ marginTop: 20 }}
                    value={{ writtenOff, writtenOffAmount, value }}
                    writtenOffMoney={writtenOffMoney}
                    bus={bus}
                    isEdit={false}
                    specification={specification}
                    standardCurrency={standardCurrency}
                    flowId={flowId}
                    isReceiptTemplate={isReceiptTemplate}
                  />
                ) : (
                  <Fragment key="WrittenOffWrapper-ManualRepayment">
                    <Container
                      key="WrittenOffWrapper"
                      name="@writtenoff:WrittenOffWrapper"
                      style={{ marginTop: 20 }}
                      value={{ writtenOff, writtenOffAmount, value }}
                      writtenOffMoney={writtenOffMoney}
                      bus={bus}
                      isEdit={false}
                      specification={specification}
                      standardCurrency={standardCurrency}
                      flowId={flowId}
                      isReceiptTemplate={isReceiptTemplate}
                    />
                    <ManualRepayment
                      key="ManualRepayment"
                      specification={specification}
                      bus={bus}
                      eidtable={false}
                      flowId={flowId}
                      isAllowedRepaymentForeign={isAllowedRepaymentForeign}
                      defaultLoanManualRepayment={loanManualRepayment}
                      isForbiddenSubmit={isForbiddenSubmit == 'Y' ? true : false}
                      value={{
                        writtenOff,
                        writtenOffAmount
                      }}
                    />
                  </Fragment>
                )
              case 'flow':
                return renderFlowNodeView()
              default:
                return <div key="ArrangeLayoutItemDiv" />
            }
          })
        ) : (
          <>
            <List className={styles.list_body}>
              <Dynamic
                {...this.props}
                billSpecification={specification}
                errorMessages={errorMessages}
                bus={bus}
                elements={readonly}
                create={create}
                template={template}
                value={value}
                noRootPathMap={noRootPathMap}
                calFields={calFields}
                submitterId={submitterId}
                globalFields={globalFields}
                tags={tags}
                isHideWait={isHideWait}
                multiplePayeesMode={multiplePayeesMode}
                payPlanMode={payPlanMode}
                payeePayPlan={payeePayPlan}
                showAllFeeType={showAllFeeType}
                feeTypeVisibleList={feeTypeVisibleList}
                apportionVisibleList={apportionVisibleList}
                canEditNote={canEditNote}
                invoiceRiskData={invoiceRiskData}
                noPayInfo={noPayInfo}
                notShowModalIfAllInvoiceSuccess={true}
                receivingCurrency={value.receivingCurrency}
                dataSource={this.props.value}
                businessType={'FLOW'}
              />
            </List>
            {isNeedWrittenoff && (
              <Container
                name="@writtenoff:WrittenOffWrapper"
                style={{ marginTop: 20 }}
                value={{ writtenOff, writtenOffAmount, value }}
                writtenOffMoney={writtenOffMoney}
                bus={bus}
                isEdit={false}
                specification={specification}
                standardCurrency={standardCurrency}
                flowId={flowId}
                isReceiptTemplate={isReceiptTemplate}
              />
            )}
            {isAllowedRepayment && (
              <ManualRepayment
                specification={specification}
                bus={bus}
                eidtable={false}
                flowId={flowId}
                isAllowedRepaymentForeign={isAllowedRepaymentForeign}
                defaultLoanManualRepayment={loanManualRepayment}
                isForbiddenSubmit={isForbiddenSubmit == 'Y' ? true : false}
                value={{
                  writtenOff,
                  writtenOffAmount
                }}
              />
            )}
            {renderTripOrderAction ? renderTripOrderAction() : <div />}
            <Container
              name="@bill:BillPayPlanReadonly"
              bus={bus}
              value={value}
              billState={billState}
              payPlans={payPlan}
              multiplePayeesMode={multiplePayeesMode}
              inPartialPayState={!!partialPayState}
              KA_REEXCHANGE_PROCESSING={KA_REEXCHANGE_PROCESSING}
            />
          </>
        )}
        <MoneySummaryReadOnly
          specification={specification}
          value={value}
          writtenOff={writtenOff}
          type={type}
          isReceiptTemplate={isReceiptTemplate}
          renderAdditionalInfo={() => <SubmitterLoanTipView
            isNeedWrittenoff={isNeedWrittenoff}
            allowViewLoanList={allowViewLoanList}
            showLoan={showLoan}
            type={type}
            specification={specification}
            submitter={value.submitterId}
            sourcePage={sourcePage}
            handleSubmitterLoan={this.handleSubmitterLoan}
            className={styles.submitter_loan_tip_new}
          />}
        />
        {!switchDocmentRenewEnable() && <SubmitterLoanTipView
          isNeedWrittenoff={isNeedWrittenoff}
          allowViewLoanList={allowViewLoanList}
          showLoan={showLoan}
          type={type}
          specification={specification}
          submitter={value.submitterId}
          sourcePage={sourcePage}
          handleSubmitterLoan={this.handleSubmitterLoan}
          className={styles.submitter_loan_tip}
        />}
        <CodeLabel
          prefix={i18n.get('单号#')}
          code={value?.code}
          auto={auto || value?.auto || value?.subsidyGeneration === 'surplus'}
          alterFlag={alterFlag}
        />
        <DisplayConfig specification={specification} refresh={this.refresh} />
        <CreditPointLabel submitter={submitterId} ownerId={ownerId} canEditNote={canEditNote} flowId={flowId} />
        <div style={{ height: 80 }}></div>
      </div>
    )
  }
}
