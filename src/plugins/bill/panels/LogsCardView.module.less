@import "../../../styles/layout.less";

@keyframes shake {
  from,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}

.logs-card {
  width: 100%;
  padding: 10px 0px;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  :global {
    .time {
      width: 80px;
      display: inline-block;
      text-align: right;
    }
    .time-1 {
      display: inline-block;
      text-align: right;
    }
    .wrapper {
      width: 100%;
      padding: 12px 40px;
      font-size: 26px;
      position: relative;
      .line-1 {
        display: flex;
        justify-content: space-between;
        margin-bottom: 3px;
        .date {
          color: #9E9E9E;
          font-size: 24px;
        }
        .circle-icon {
          width: 12px;
          height: 12px;
          border-radius: 12px;
          background-color: #FFAE63;
          display: inline-block;
          margin-right: 10px;
        }
        .label {
          font-size: 28px;
        }
      }
      .line-2 {
        margin-left: 22px;
        .txt {
          color: #696D6D;
          font-size: 24px;
        }
        .date {
          color: #9E9E9E;
          font-size: 24px;
        }
        .action {
          color: #959898;
          font-size: 24px;
        }
      }
      .comment {
        border-radius: 2px;
        background-color: #F8F9F9;
        line-height: 34px;
        margin: 20px 22px 0 22px;
        color: #6C6C6C;
        padding: 20px;
        font-size: 28px;
      }
      .tag {
        margin-left: 22px;
        border-radius: 0.08rem;
        background-color: #F8F9F9;
        border: solid 0.02rem #FFFFFF;
        margin-bottom: 10px;
      }
    }
    .log-count-msg {
      align-self: center;
      height: 80px;
      display: flex;
      align-items: center;
      font-size: 24px;
      line-height: 1.5;
      text-align: right;
      color: #959898;
      cursor: pointer;
    }
  }
}

.right {
  flex-shrink: 0;
  position: absolute;
  right: 34px;
  top: 60px;
  :global {
    .risk-high {
      animation: shake 1.5s ease-in-out;
      animation-iteration-count: 2;
    }
  }
}



