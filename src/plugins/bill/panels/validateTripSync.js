import { app as api } from '@ekuaibao/whispered'
import { toast } from '../../../lib/util'
import { get } from 'lodash'

// 同步行程回调
const handleSynchronous = async (flowId, action) => {
  api.invokeService('@bill:set:sync:trip', { flowId, action })
}

const getTripSyncStatus = async props => {
  const { state } = props
  const flowId = get(state, 'flowId')
  if (!flowId) return true
  let response = await api.invokeService('@bill:get:sync:trip:result', { flowId })
  const { result, described } = response?.value
  // if (result === 'SUCCESS' && described) toast.success(described)
  // else if (described) toast.info(described)
  return result
}

const delTripSyncValid = async objectId => {
  let params = {
    action: 'FLOW', //FLOW=单据； TRIP=行程
    objectId // 单据ID/行程ID
  }
  let res = await api.invokeService('@bill:get:sync:trip:valid', params)
  const { result, failReason } = res.value
  return { result, failReason }
}

const getTravelManagementConfig = originalId => {
  return new Promise(resolve => {
    api
      .invokeService('@bill:get:travelManagementConfig', { type: 'draftSyncTrip' })
      .then(result => {
        const { contextDetail } = result?.value
        if (contextDetail?.specificationIds.includes(originalId) && contextDetail?.enable) {
          resolve(true)
        } else {
          resolve(false)
        }
      })
      .catch(err => {
        resolve(false)
      })
  })
}

const validTripSync = async props => {
  const { state } = props
  const id = get(state, 'flowId')
  let response = await delTripSyncValid(id)
  const { result: status, failReason } = response
  if (status === 'FAILED') {
    if (failReason) toast.error(failReason)
    return false
  }
  return true
}

const enableDraftConfig = async props => {
  const { specification_current } = props
  const originalId =
    typeof get(specification_current, 'originalId') === 'string'
      ? get(specification_current, 'originalId')
      : get(specification_current, 'originalId.id')
  const components = get(specification_current, 'components')
  const billFormType = get(specification_current, 'type')
  const enableDataLink = components?.filter(
    item => (item?.type === 'dataLinkEdits' && item?.referenceData?.type === 'TRIP') || item?.type === 'travel'
  )
  let result_config = await getTravelManagementConfig(originalId)
  if (result_config && enableDataLink?.length && billFormType === 'requisition') return true
  else return false
}

export { validTripSync, handleSynchronous, enableDraftConfig, getTripSyncStatus }
