/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/7.
 */
import React, { PureComponent } from 'react'
import { Tag } from '@hose/eui-mobile';
import { FilledGeneralBuzz } from '@hose/eui-icons';
import './UrgentView.less'
export default class UrgentView extends PureComponent {
  constructor() {
    super()
    this.state = {}
  }

  componentWillMount() {}

  componentWillUnmount() {}

  render() {
    let { urgentReason, state } = this.props
    const isPaid = state === 'paid' || state === 'archived'
    return (
      <div className={'urgent'}>
        <Tag color={ !isPaid ? 'danger' : 'neu'}><FilledGeneralBuzz />{i18n.get('加急')}</Tag>
        <span className="mr-16">{urgentReason}</span>
      </div>
    )
  }
}
