import { app } from '@ekuaibao/whispered'
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/7.
 */
import React, { PureComponent } from 'react'
import styles from './LogsCardView.module.less'
import Log_Comment_Msg from '../images/logs-card-msg.svg'
const { flowStateMap, fnMapLogs } = app.require('@elements/approve-log/card-log-item')
import { checkRiskLevel, calculTimeBySecond, calculTimeByMinute } from '../../../lib/util'
const AuthRiskTip = app.require('@elements/authRiskTip')
import { get } from 'lodash'
import { toast } from '../../../lib/util'
import { EnhanceConnect } from '@ekuaibao/store'
import { flowStateMap as logMap } from '../../../elements/approve-log/history-log-item'
import BillsVersion from './bills-version'
import { isAIAgentNode, getAIAgentObj } from '../../../elements/ai-agent-utils'

const stateMap = () => ({
  draft: {
    color: 'var(--warning-base)',
    icon: 'state-draft',
    text: i18n.get('待提交'),
    textColor: '#3A3F3F'
  },
  approving: {
    color: 'var(--info-base)',
    icon: 'state-approval',
    text: i18n.get('审批中'),
    textColor: '#3A3F3F'
  },
  sending: {
    color: 'var(--info-base)',
    icon: 'state-approval',
    text: i18n.get('待寄送'),
    textColor: '#3A3F3F'
  },
  receiving: {
    color: 'var(--info-base)',
    icon: 'state-approval',
    text: i18n.get('待收单'),
    textColor: '#3A3F3F'
  },
  receivingExcep: {
    color: 'var(--info-base)',
    icon: 'state-approval',
    text: i18n.get('收单异常'),
    textColor: '#3A3F3F'
  },
  pending: {
    color: 'var(--info-base)',
    icon: 'state-approval',
    text: i18n.get('提交中'),
    textColor: '#3A3F3F'
  },
  paying: {
    color: 'var(--info-base)',
    icon: 'state-approval',
    text: i18n.get('待支付'),
    textColor: '#3A3F3F'
  },
  back: {
    color: 'var(--info-base)',
    icon: 'state-approval',
    text: i18n.get('待支付'),
    textColor: '#3A3F3F'
  },
  paid: {
    color: 'var(--success-base)',
    icon: 'state-paid',
    text: i18n.get('已完成'),
    textColor: '#bbbdbd'
  },
  archived: {
    color: 'var(--success-base)',
    icon: 'state-paid',
    text: i18n.get('已完成'),
    textColor: '#bbbdbd'
  },
  rejected: {
    color: 'var(--danger-base)',
    icon: 'state-rejected',
    text: i18n.get('被驳回'),
    textColor: 'var(--danger-base)'
  },
  nullify: {
    color: 'var(--danger-base)',
    icon: 'state-rejected',
    text: i18n.get('已作废'),
    textColor: 'var(--danger-base)'
  }
})

@EnhanceConnect(state => ({
  dynamicChannelMap: state['@common'].dynamicChannelMap,
  staffDisplayConfigField: state['@common'].organizationConfig?.staffDisplayConfig?.[1] || '',
  nodesAIAgentMap: state['@common'].nodesAIAgentMap || {}
}))
export default class LogsCardView extends PureComponent {
  constructor() {
    super()
    this.state = { time: undefined, overTip: undefined }
  }

  componentWillMount() {
    const { staffDisplayConfigField } = this.props
    this.countDown()
    logMap.staffDisplayConfigField = staffDisplayConfigField
  }

  componentWillUnmount() {
    clearInterval(this.id)
  }

  countDown() {
    let { dataSource = {}, isFromApproving } = this.props
    let { state, formType, restTime, countDownDuration, autoApproveType } = dataSource
    if (formType === 'permit') {
      if (restTime && state === 'approving' && restTime.duration * 1 > 0) {
        this.countDown4Permit(restTime.duration, autoApproveType)
        return
      }
      return
    }
    if (isFromApproving && countDownDuration) {
      this.countDown4Other(countDownDuration.duration, autoApproveType)
    }
  }

  countDown4Permit(duration, autoApproveType) {
    duration = this.setTimeState(duration, true, autoApproveType)
    this.id = setInterval(() => {
      duration = this.setTimeState(duration, true, autoApproveType)
    }, 1000)
  }

  countDown4Other(duration, autoApproveType) {
    duration = this.setTimeState(duration, false, autoApproveType)
    this.id = setInterval(() => {
      duration--
      duration = this.setTimeState(duration, false, autoApproveType)
    }, 1000 * 60)
  }

  setTimeState(duration, isPermit = true, autoApproveType) {
    if (duration > 0) {
      let time = isPermit ? calculTimeBySecond(duration) : calculTimeByMinute(duration)
      let riskLevel = isPermit && checkRiskLevel(duration)
      this.setState({ time, riskLevel })
    } else {
      const tips = autoApproveType
        ? autoApproveType === 'AGREE'
          ? i18n.get('超时即将同意')
          : i18n.get('超时即将驳回')
        : i18n.get('超时即将驳回')
      this.setState({ time: undefined, riskLevel: '', overTip: tips })
      this.id && clearInterval(this.id)
    }
    return duration
  }

  renderCountDown4Permit() {
    let { time } = this.state
    let color = 'color-gray-8'
    if (time && time.m * 1 <= 10) {
      color = time.m * 1 > 5 ? 'color-orange' : 'color-red-6'
    }
    return (
      time &&
      time.m * 1 < 100 && (
        <div className={`${color} fs-12 fw-n`}>
          <span>{i18n.get('剩余')}</span>
          <span className="time">
            {time.m}:{time.s}
          </span>
        </div>
      )
    )
  }

  renderCountDown4Other() {
    const { dataSource = {} } = this.props
    let { time } = this.state
    const { autoApproveType } = dataSource
    if (!time) return <div />
    const tips = autoApproveType
      ? autoApproveType === 'AGREE'
        ? i18n.get('自动同意')
        : i18n.get('自动驳回')
      : i18n.get('自动驳回')
    return (
      <div className="color-gray-8 fs-12 fw-n">
        <span>{i18n.get('剩余')}</span>
        <span className="time-1">
          {time.d * 1 > 0 && time.d + i18n.get('天')}
          {time.h * 1 > 0 && time.h + i18n.get('小时')}
          {time.m + i18n.get('分')}
          {tips}
        </span>
      </div>
    )
  }

  render() {
    let { dataSource = {}, onLogMsgClick, userInfo, dynamicChannelMap = {} } = this.props
    let { logs = [] } = dataSource
    let { riskLevel, overTip } = this.state
    logs = logs.filter(el => el.action !== 'freeflow.print')
    let lastLog = logs[logs.length - 1]
    if (!lastLog) return <div />
    const formatLogs = fnMapLogs(logs).filter(line => line.action !== 'freeflow.carbonCopy')
    const commentCount = formatLogs.filter(e => {
      return e.attributes && (e.attributes.comment?.length > 0 || e.attributes.delOperatorId)
    }).length
    lastLog = formatLogs[formatLogs.length - 1] //把抄送的过滤掉，为了支付中时候有抄送，显示有问题
    let action = lastLog.action.replace('freeflow.', '')
    let state = stateMap()[dataSource.state]
    let label = state.text
    if (
      dataSource.state === 'paying' &&
      (action === 'paying' || action === 'pay.partial.paying' || action === 'pay.partial.success')
    ) {
      label = i18n.get('支付中')
    }
    if (lastLog && lastLog.attributes && lastLog.attributes.isEbotNode) {
      lastLog.operatorId = { name: 'EBot' }
    }
    if (get(lastLog, 'attributes.isInvoiceApplicationNode')) {
      lastLog.operatorId = { name: i18n.get('开票申请') }
    }
    if (get(lastLog, 'attributes.isRecalNode')) {
      lastLog.operatorId = { name: i18n.get('重算节点') }
    }

    if(isAIAgentNode(lastLog)){
      const {agent} = getAIAgentObj(lastLog, this.props.nodesAIAgentMap)
      lastLog.operatorId = { name: agent?.name }
    }

    let isPermit = dataSource.formType === 'permit'
    return (
      <div className={styles['logs-card']}>
        <div className="wrapper">
          <div className="line-1">
            <div className="label">
              <span className="circle-icon" style={{ background: state.color }} />
              <span style={{ color: state.textColor }}>{label}</span>
            </div>
            {!isPermit ? this.renderCountDown4Other() : this.renderCountDown4Permit()}
            {overTip && <div className="color-gray-8 fs-12 fw-n">{overTip}</div>}
          </div>
          {flowStateMap[action] &&
            flowStateMap[action].render({ item: lastLog, userInfo, flow: dataSource, dynamicChannelMap })}
          <div
            className={styles.right}
            onClick={() => toast.info(i18n.get('机票卖的快且价格变化快，越早授权，出票成功率越高'), 3000)}
          >
            <AuthRiskTip risk={riskLevel} />
          </div>
          <BillsVersion logs={dataSource?.logs || []} id={dataSource?.form?.id} />
        </div>
        {!!commentCount && (
          <div className="log-count-msg" onClick={onLogMsgClick}>
            <img className="mr-5" src={Log_Comment_Msg} />
            {i18n.get('查看{__k0}条评论', { __k0: commentCount })}
          </div>
        )}
      </div>
    )
  }
}
