/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-08-12 22:22:01
 * @Description  : 
 * 描述功能/使用范围/注意事项
 */
import React, { FC } from 'react'
import { app } from '@ekuaibao/whispered'
const { ModifyCreateType, fnMapLogs } = app.require<any>('@elements/approve-log/history-log-item')
import styles from './bills-version.module.less'
import { HistoryApi } from '../../../plugins/bill/history/api'

const BillsVersion: FC<{ logs: any[]; id: string }> = ({ logs, id }) => {
  const versionConfig = app.getState('@common.specificationVersionConfig')
  if (versionConfig && versionConfig.hideVersion) {
    return null
  }
  const versionLogs = fnMapLogs(logs)?.filter(
    (item: any) =>
      item?.action === 'freeflow.modify' ||
      (item?.action === 'freeflow.submit' &&
        item?.modifyFlowLog?.find((oo: any) => oo?.operatorState === ModifyCreateType))
  )
  if (!versionLogs?.length) return null
  const preVersion = versionLogs[versionLogs.length - 1]
  const { modifyFlowLog } = preVersion
  const current = modifyFlowLog?.find((item: any) => item?.operatorState === ModifyCreateType) || preVersion
  const handleHistoryVersionClick = async () => {
    const isHistoryVersion = await HistoryApi.getFlowVersionModifyState(current.flowVersionedId)
    if(isHistoryVersion?.value){
      app.go('/version/detail/' + id + '/' + current.flowVersionedId)
    }else{
      app.open('@bill:BillHistoryModal',{
        flowVersionedId:current.flowVersionedId
      })
    }
  }
  return (
    <div className={styles['version-wrapper']} onClick={handleHistoryVersionClick}>
      {i18n.get('查看修改内容')}
    </div>
  )
}

export default BillsVersion
