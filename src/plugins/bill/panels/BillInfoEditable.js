import styles from './BillInfoEditable.module.less'
import React, { PureComponent } from 'react'
import classNames from 'classnames'
import { MoneyMath } from '@ekuaibao/money-math'
import Popup from '@ekuaibao/popup-mobile'
import { EnhanceConnect } from '@ekuaibao/store'
import { Dynamic } from '@ekuaibao/template'
import { app as api, app, UIContainer as Container } from '@ekuaibao/whispered'
import { Dialog } from '@hose/eui-mobile'
import { QuerySelect } from 'ekbc-query-builder'
import { cloneDeep, debounce, flatten, get, isArray, uniq, result, isEqual, once } from 'lodash'
import moment from 'moment'
import createDOMForm from 'rc-form/lib/createDOMForm'
import { getV } from '../../../lib/help'
import { addMapJsApi } from '../../../lib/mapjsapi'
import { nanoid } from 'nanoid'
import {
  getBillKey,
  getDetailCalculateMoney,
  getNodeValueByPath,
  toast,
  localStorageSet,
  showLoading,
  hideLoading,
  noSpecificationAlert,
  getUrlParamString
} from '../../../lib/util'
import {
  getBatchAutoCalResult,
  getCalculateField,
  getCurrentRequisitionInfo,
  getExpenseLink,
  getRuleDataLinkFeetype,
  setRequisitionInfo,
  setValidateError,
  getDetailFLowRelation,
  getAutoGenerationFeeDetail,
  getAutoGenerationFeeDetailRules
} from '../bill.action'
import BillPayPlan from '../components/payPlan/BillPayPlan'
import CreditPointLabel from '../elements/CreditPointLabel'
import TAXI from '../images/taxi.svg'
import ELEME from '../images/eleme.svg'
import actions from '../../common/common.action'
import PLATFORM_PNG from '../images/platform-default-icon.png'
import { getAutoCalResultOnField } from '../utils/autoCalResult'
import { formatRiskWarnings } from '../utils/formatRiskData.formatRiskWarnings'
import {
  deleteDetailItemExternalsData,
  isTicketReview,
  resetDetailsExternalsData,
  resetFormExternalsData,
  resetTripsExternalsData
} from '../utils/BillInfoEditableUtils'
import {
  getSpecificationHiddenFields,
  canChangeTemplateFn,
  checkIsRemuneration,
  checkValue,
  showConfirmToEdit,
  updateDetailIndex,
  fixRemunerationSpecification,
  fnFilterFeetypes,
  formatNewTemplateValue,
  logInfo,
  mergeBillFormValue,
  checkQuickExpends,
  mergeValue,
  formatExpenseLinkInfo,
  getValidateErrorByShow,
  checkSpecificationSumField
} from '../utils/billUtils'
import { getCustomizeCalResultOnField, callCustomizeCalByQueue, checkValueOrIdEqual } from '../utils/customizeCalculate'
import {
  getDependenceFieldOfDataLink,
  openDataLinkEntityList,
  openMultiSelectDataLinkEntityList
} from '../utils/dataLinkUtils'
import {
  handleAutoAssignOfOneResultOfDataLink,
  getDataLinkPermissionFieldComponent,
  filterDataLinkFields
} from '../utils/dataLinkUtils.openDataLinkEntityList'
import {
  handleDimensionCurrencyChange,
  handlerCurrencyMoneySelectChange,
  setDefaultCurrencyMoney
} from '../utils/defaultCurrency.js'
import { getImportValueWithValidate } from '../utils/formatSubmitData'
import { parseShowValue2SaveValue } from '../utils/formatUtil'
import { FormatData, formatInitialValue, getAutoCalResult } from '../utils/importFormatData'
import parseSpecification from '../utils/parseSpecification'
import { fnLinkDetailEntitiesValue, getDetailsRelateMoney, initRelatedMap } from '../utils/relatedExpenseLinkUtils'
import { showDetailsTips } from '../utils/showDetailsTips'
import { AutoCalculate2 } from '../utils/autoCalculate2'
import { getInvoiceDisableInfo, getInvoiceMarkInfo } from '../../invoice-form/utils/invoiceDisableUtils'
import { getOfficialCardSetting, getPayments } from '../../feetype/feetype.action'
import { getEffectiveCurrencyInfo } from '../utils/billFetchUtil'
import * as ExpenseLinkUtil from '../../../components/utils/fnExpenseLinkUtil'
import { callFnByQueueNew } from '../utils/callFbByQueue'
const ManualRepayment = app.require('@elements/puppet/repayment/ManualRepayment')
const getValue = app.require('@lib/parser.getValue')
const parseAsMeta = app.require('@lib/parser.parseAsMeta')
const filterResults = app.require('@lib/parser.filterResults')
const isNeedUpdateResult = app.require('@lib/parser.isNeedUpdateResult')
const isNeedUpdateCustomizeResult = app.require('@lib/parser.isNeedUpdateCustomizeResult')
const CodeLabel = app.require('@elements/puppet/CodeLabel')
const DisplayConfig = app.require('@elements/puppet/DisplayConfig')
const SelectSpecification = app.require('@elements/puppet/SelectSpecification')
const MoneySummaryEditable = app.require('@elements/puppet/MoneySummary.editable')
const { getRemunerationLedgerProcess, confirmRemuneration } = app.require('@elements/puppet/Remuneration/actions')
const editable = app.require('@components/dynamic/index.editable')
const { related } = app.require('@components/utils/Related')
const { addResizeEvent } = app.require('@components/utils/fnNumberkeyboard')
const { standardValueMoney, constantValue } = app.require('@components/utils/fnInitalValue')
const { fnPreviewAttachments } = app.require('@components/utils/fnAttachment')
const dataLinkIconList = { PRIVATE_CAR: TAXI, ELEM: ELEME }
import { openRecord } from '../../record-expends/importRecordExpends'
import { openQuickExpense } from '../../quick-expense/importRecordExpends'
import { Fetch } from '@ekuaibao/fetch'
import { clearBillCache } from '../../../lib/checkBillCache'
import { fnApportionDetails } from '../../quick-expense/util/WithNoteGroupUtil'
import {
  endOpenFlowPerformanceStatistics,
  leaveFlowPerformanceStatistics,
  startLoadFlowPerformanceStatistics,
  reportBillPagePaintDuration,
  flowDetailsStatistics
} from '../../../lib/flowPerformanceStatistics'
import SkeletonComponent from './SkeletonModal'
import { getAllowSelectionReceivingCurrency } from '../../feetype/parts/feeTypeInfoHelper'
import { getBoolVariation, getVariation, useDepartmentVisible, enableOtherInvoiceByDimension } from "../../../lib/featbit";
import { useNewAutomaticAssignment } from '../../../components/utils/fnAutoDependence'
import { AIFillFormTracker, FillMode } from '../../../lib/aiFillFormTracker'

function create(T) {
  //屏蔽一些由setState，setFieldsValue,onChange 引发的不必要的dynamicChange，eg：setState({value})后 changedValues 是整个表单会引发不必要的 自动/计算/联动赋值
  let TEMPLATE_REF
  const FORMCOMPONENT = createDOMForm({
    onValuesChange(props, changedValues) {
      const filteredChangedValues = cloneDeep(changedValues)
      const { form } = TEMPLATE_REF?.props ?? {}
      const preValues = form?.getFieldsValue && form?.getFieldsValue()
      Object.keys(filteredChangedValues)?.forEach(field => {
        if (checkValueOrIdEqual(filteredChangedValues[field], preValues?.[field])) {
          delete filteredChangedValues[field]
        }
      })
      setTimeout(() => {
        props.bus.emit('dynamic:billinfo:value:changed', changedValues, filteredChangedValues)
      }, 0)
    }
  })(T)

  return React.forwardRef((props, ref) => {
    return <FORMCOMPONENT {...props} ref={ref} wrappedComponentRef={inst => (TEMPLATE_REF = inst)} />
  })
}

function getFilterFeetypeList(params) {
  window.FROM_SUISHOUJI = false
  return api.invokeService('@common:get:feeTypes:available', params).then(resp => resp.items)
}

const FLAG = { cancelLimit: false }
let lastLegalEntityChangedAlertTime = 0;

@EnhanceConnect(
  (state, props) => {
    return {
      globalFields: state['@common'].baseDataProperties.data,
      baseDataPropertiesMap: state['@common'].baseDataProperties.baseDataPropertiesMap,
      feetypes: state['@common'].feetypes.data,
      tripTypes: state['@common'].tripTypes.list,
      filterFeeTypes: state['@common'].filterFeeTypes,
      feeTypeMap: state['@common'].feetypes.map,
      importData: state['@invoice'].importData,
      corpConsume: state['@common'].corpConsume,
      isHaveOfficialCardPower: state['@common'].isHaveOfficialCardPower,
      expenseLink: state['@bill'].expenseLink,
      platFromList: state['@third-import'].platFromList,
      mappingRelation: state['@third-import'].mappingRelation,
      standardCurrency: state['@common'].standardCurrency,
      lastChoice: state['@common'].lastChoice.choiceValue,
      YEEGO: state['@common'].powers.YEEGO,
      Business: state['@common'].powers.Business,
      requisitionList: state['@requisition'].requisitionList,
      specification: state['@home'].specification_current,
      specification_group: state['@home'].specificationWithVersion.specification_group,
      MYCARBUSINESS: state['@common'].powers.MYCARBUSINESS, ///私车公用
      billCopiedValue: state['@bill'].billCopiedValue, //单据复制
      defaultPayee: state['@common'].defaultPayee,
      userInfo: state['@common'].me_info,
      newFilterFeeTypes: state['@common'].newFilterFeeTypes.items,
      isFilterStaffAndDept: state['@common'].organizationConfig.allowPermissionsEffect,
      isVisibilityStaffs:
        !!state['@common'].organizationConfig.visibilityConfigs?.length &&
        !state['@common'].visibilityStaffOptions.fullVisible,
      validateError: state['@bill'].validateError,
      remunerationSetting: state['@home'].remunerationSetting,
      dimensionCurrencyInfo: state['@bill'].dimensionCurrencyInfo,
      canUseDefaultCurrency: state['@common'].powers.powerCodeMap?.indexOf('162005') >= 0, //默认币种设置
      KA_NUMBER_FIELD_COUNT_RULE: state['@common'].powers.KA_NUMBER_FIELD_COUNT_RULE,
      KA_NUMBER_FIELD_COUNT_RULE_2: state['@common'].powers?.powerCodeMap?.includes?.('170023') ?? false, //补助自动计算 2.0
      customizeQueryPower: state['@common'].powers.customizeQuery,
      AutoGenerateFeeDetail: state['@common'].powers.AUTO_GENERATE_FEE_DETAIL,
      disableInvoicePower: state['@common'].powers.KA_DISABLE_INVOICE,
      generationRule: state['@bill'].generationRule,
      civilServiceCard: state['@common'].powers.CivilServiceCard,
      expenseSpecAfterFiltered: state['@home'].expenseSpecAfterFiltered,
      allCurrencyRates: state['@home'].allCurrencyRates,
      current_flow: state['@common'].current_flow,
      externalStaffPower: state['@common'].powers.ExternalAuth,
      externalStaffMap: state['@common'].externalStaffMap
    }
  },
  {
    getExpenseLink,
    getCurrentRequisitionInfo,
    setRequisitionInfo,
    getBatchAutoCalResult,
    getCalculateField,
    getRuleDataLinkFeetype,
    setValidateError
  },
  '@bill/current/page/state'
)
export default class BillInfoEditable extends PureComponent {
  static defaultProps = {
    type: 'expense'
  }

  //用于标记自动计算的顺序
  constructor(props, ...args) {
    super(props, ...args)
    this.copyDetials = get(props.value, 'details', [])
    this.valuation = {}
    this.importList = []
    this.calculateResults = [] //调用自定计算后的结果
    this.isInitAutoAssignOfOneResultOfDataLink = true // 是否是第一次业务对象自动赋值
    let menuValue = []
    if (props.YEEGO) {
      menuValue.push('ekbMall', 'flight')
    } else if (props.Business) {
      menuValue.push('ekbMall', 'ekb')
    } else {
      menuValue.push('invoice', 'fapiao')
    }
    startLoadFlowPerformanceStatistics()
    let feeTypeVisibleObjForModify
    let showAllFeeType = true
    //props?.sourcePage !== 'flowLinks'开启单据查询组件费用明细的查看权限
    if (props.isModifyBill && props?.sourcePage !== 'flowLinks') {
      const { flowId } = this.props.state
      const flowIdForFeeTypeModify = get(props, 'feeTypeVisibleObjForModify.flowId')
      showAllFeeType = get(props, 'feeTypeVisibleObjForModify.showAllFeeType')
      if (flowId && flowId !== flowIdForFeeTypeModify) showAllFeeType = true
      feeTypeVisibleObjForModify = showAllFeeType ? undefined : props.feeTypeVisibleObjForModify
    }
    related.setFlowType(props.type)
    this.state = {
      ...parseSpecification(props),
      hiddenFields: [],
      errorMessages: [],
      calFields: {},
      customizeQueryRule: [],
      thirdImportMenuValue: menuValue,
      isContinue: false,
      showAllFeeType,
      riskInfo: {},
      feeTypeVisibleObjForModify,
      applicationListDetails: [],
      canRenderMap: { lastChoice: !!props?.lastChoice?.length },
      generationRule: [],
      isRefresh: false,
      loanManualRepayment: [],
      dependenceFieldOfDataLink: [], // 业务对象字段依赖字段集
      billFeeForceValidation: false
    }
    this.shouldSave = true
    this.initPayeeInfo = true
    this.editedDetailList = []
    this.__CURRENT_IS_CHANGED = false
    this.countRuleCheckFields = props.KA_NUMBER_FIELD_COUNT_RULE
      ? get(props.value, 'specificationId.components', get(props, 'specification.components', []))
        .filter(item => item.countRuleCheck)
        .map(item => ({
          fieldKey: item.field,
          countRule: item.countRule
        }))
      : []
    //props.value.map
    if (props.disableInvoicePower) {
      getInvoiceDisableInfo(props).then(disableInfo => {
        this.setState({ disableInfo })
      })
      getInvoiceMarkInfo(props).then(markInfo => {
        this.setState({ markInfo })
      })
    }
    this.timer = new Date().getTime()
  }

  componentWillMount() {
    try {
      const { state, type, specification } = this.props
      const current_flow = state?.data
      const trackParams = {
        form_id: current_flow?.id || this.props.newFormId,
        form_template_id: specification?.id,
        form_template_name: specification?.name,
        form_type: type,
        form_create_time: current_flow?.createTime || Date.now(),
        fill_mode: current_flow ? FillMode.EDITDRAFT : FillMode.CREATE,
      }
      console.log('AIFillFormTracker.trackAIFillFormStart', this.props, trackParams)
      AIFillFormTracker.trackAIFillFormStart(trackParams)
    } catch (error) {
      console.error('AIFillFormTracker.trackAIFillFormStart error', error)
    }
    this.handleLastChoice(this.props.submitter)
    api.invokeService('@invoice-form:unify-invoice-list')
    if (!Object.keys(this.props.standardCurrency || {}).length) {
      api.dispatch(actions.getStandardCurrency())
    }
    if (!this.props.allCurrencyRates) {
      api.dispatch(actions.getEnterpriseAllCurrencyRate())
    }
  }

  setTimeField = async specification => {
    const currencyConfig = await api.dataLoader('@common.currencyConfig').load()
    const originalId =
      typeof specification?.originalId === 'object' ? specification?.originalId?.id : specification?.originalId
    const active = get(currencyConfig, 'timeCaliber.active', false)
    const rules = getV(currencyConfig, 'timeCaliber.rules', [])
    let timeField
    if (active) {
      timeField = rules.find(el => el.specificationIds.includes(originalId))?.timeField
    }
    if (!timeField && (specification?.type === 'loan' || specification?.type === 'requisition')) {
      timeField = `${specification?.type}Date`
    }
    this.setState({ timeField })
    return timeField
  }

  componentDidMount() {
    let {
      bus,
      platFromList,
      mappingRelation,
      isModifyBill,
      external,
      MYCARBUSINESS,
      showDialg = true,
      value,
      submitter,
      type,
      state,
      expenseLink,
      customizeQueryPower,
      civilServiceCard,
      AutoGenerateFeeDetail
    } = this.props
    checkQuickExpends(state?.flowId)
    const loanManualRepayment =
      (state.data && state.data.loanManualRepayment && state.data.loanManualRepayment.records) || []
    this.setState({ loanManualRepayment })

    this.setTimeField(this.state.specification)

    const { specification = {} } = this.state
    logInfo(`查看${get(value, 'title', '无标题')}单据`)
    this.getHiddenFields(specification)

    const billState = state.data?.state
    const billBack = localStorage.getItem('bill-detail-back')
    if (billBack === 'true') {
      localStorage.removeItem('bill-detail-back')
    }

    this.updateImportList(specification.originalId)

    const { flowId } = this.props.state
    related.setFlowId(flowId)

    if (civilServiceCard) {
      getOfficialCardSetting().then(res => {
        const referenceByDetailsEnable = getV(res, 'value.referenceDataType.referenceByDetailsEnable')
        const feeTypeIds = getV(res, 'value.referenceDataType.referenceByDetails.feeTypeIds', [])
        const supportCivilServiceCardFeeTypeIds = referenceByDetailsEnable
          ? feeTypeIds.length > 0
            ? feeTypeIds
            : 'supportAllFeeTypeIds'
          : undefined
        if (supportCivilServiceCardFeeTypeIds) {
          this.setState({ supportCivilServiceCardFeeTypeIds })
        }
        const settlementId = get(res, 'value.settlementId')
        if (settlementId) {
          getPayments().then(v => {
            const payments = get(v, 'items', [])
            if (payments.find(el => el.id === settlementId)) {
              this.setState({ settlementId: payments.find(el => el.id === settlementId) })
            } else {
              toast.error(i18n.get('未匹配到结算方式，请联系管理员'))
            }
          })
        }
      })
    }

    customizeQueryPower
      ? this.updateAutoCalFields(undefined, false, !!billState, { needReload: true })
      : this.updateAutoCalFields()

    if (flowId) {
      api.invokeService('@bill:get:bill:notes', { flowId })
      type === 'requisition' && api.invokeService('@bill:get:getTravelBackInfo', flowId, submitter?.id)
      type === 'requisition' && api.invokeService('@bill:get:getTravelBackInfoV3', state.data?.form?.code)
      state.data?.form?.details?.length > 0 && api.invokeService('@bill:get:getFeeTypeChange', flowId)
      IS_ZJZY && localStorageSet('bill_code', state.data?.form?.code || '')
    }

    if (IS_ZJZY && !flowId) localStorageSet('bill_code', '')

    if (!platFromList.length) {
      api.invokeService('@third-import:get:platFormList')
    }
    if (!mappingRelation.length) {
      api.invokeService('@third-import:get:mappingRelation')
    }

    api.dataLoader('@common.payeeConfig').reload()
    api.dataLoader('@common.payerInfo').load()
    //天阳(bnB3nHI6Fb3qzw)的不调用了下面的接口
    if (Fetch.ekbCorpId !== 'bnB3nHI6Fb3qzw') {
      api.dataLoader('@common.staffsVisibility').load(submitter && submitter.id)
    }
    api.dataLoader('@common.departmentsVisibility').load(submitter && submitter.id)
    api.dataLoader('@common.isHaveOfficialCardPower').load()
    api.dataLoader('@common.specificationVersionConfig').load()
    api.invokeService('@common:getDefaultPayee')
    api.invokeService('@requisition:get:requisition:list:orderBy')
    api.invokeService('@bill:set:submitter:data', submitter)
    bus.watch('element:ref:select:department', this.handleSelectDepartment)
    bus.watch('element:ref:select:payee', this.handleSelectPayee)
    bus.watch('element:ref:select:staff', this.handleSelectStaff)
    bus.watch('element:ref:select:staffs', this.handleSelectStaffs)
    bus.watch('element:details:import:click', this.handleImportClick)
    bus.watch('element:details:import:quick-expense', this.handleImportQuickExpense)
    bus.watch('element:details:import:backclick', this.handleImportBackClick)
    bus.watch('element:details:add:click', this.handleAddClick)
    bus.watch('element:details:copy:click', this.handleCopyClick)
    bus.watch('element:details:add:trips:click', this.handleAddTripsClick)
    bus.watch('element:details:line:click', this.handleLineClick)
    bus.watch('element:details:tips:click', this.handleTipsClick)
    bus.watch('detail:value:change', this.handleDetailValueChange)
    bus.watch('element:ref:select:property', this.handleSelectProperty)
    bus.watch('element:attachments:line:click', this.handleAttachment)
    bus.watch('element:requisition:line:click', this.handleSelectRequisition)
    bus.watch('element:expenseLink:changed', this.handleExpenseLinkChange)
    bus.watch('element:ref:select:staff:changed', this.handleSubmitterChange)
    bus.watch('element:select:city:click', this.handleSelectCity)
    bus.watch('element:dataLink:select:property', this.handleDataLinkSelect)
    bus.watch('element:dataLink:multiselect:property', this.handleMultiDataLinkSelect)
    bus.watch('open:associated:dataLink:modal', this.handleOpenAssociatedDataLinkModal)
    bus.watch('element:expenselink:import:trip:order', this.handleImportTripOrder)
    bus.watch('element:delete:detail:external', this.handleFormDeleteDetaiExternal)
    bus.watch('element:money:value:changed:external', this.handleFormExternalChange)
    bus.watch('element:trips:value:changed:external', this.handleTripsExternalChange)
    bus.watch('dynamic:billinfo:value:changed', this.handleDynamicValueChange)
    bus.watch('element:details:import:data:format', this.handleImportAutoCal)
    bus.watch('bill:edit:form:changed:should:save', this.fnChangeFormShouldSave)
    bus.watch('clear:realted:application:details', this.clearRelatedApplication)
    bus.watch('import:expense:link:details', this.handleImportApplyDetail)
    bus.watch('get:expense:link:details', this.handleGetApplyDetails)
    bus.watch('update:select:expenselink', this.initRelatedApplication)
    bus.watch('update:modify:related', this.updateModifyRelated)
    bus.watch('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
    api.watch('get:bills:value', this.handleGetBillsValue)
    api.watch('get:bills:bus', this.handleGetBillsBus)
    api.watch('bill:invoice:disable:change', this.updateInvoiceState)
    api.on('update:editedDetailList', this.handleUpdateList)
    bus.on('element:field:changed:external', this.handleFormExternalChange)
    bus.on('update:calculate:template', this.updateCalTemplate)
    bus.on('dimension:currency:change', this._handleDimensionCurrencyChange)
    bus.on('on:dependence:change', this.handleDependenceChange)
    bus.on('dateRange:changed:value', this.handleDataRangeChange)
    bus.watch('update:flow:risk:info', this.loadFlowRiskInfo)
    bus.watch('handleBillInfoModifySave', this.handleBillInfoModifySaveCallBack)
    bus.on('HAB:value:changed', this.handleHABValueChange)
    bus.on('update:payee:value', this.fnInitPayeeInfo)
    bus.watch('get:specification:hiddenFields', this.getSpecHiddenFields)

    const timeout = Number(getVariation('cyxq-71060-bill-edit-timeout', 2000))
    setTimeout(async () => {
      const currentValue = await bus?.getValue?.()
      const sourcePage = getUrlParamString(location.search, 'source') || ''

      this.getVisibleFeeTypes(currentValue, specification.id).then(async () => {
        this.updateFilterFeeTypeIds(this.props)
        this.initRelatedApplication()
        const cValue = this.formatLinkDetailEntities(currentValue)
        const template = this.state.template || []
        const expenseLinkComponent = template.find(v => v.name === 'expenseLink' || v.name === 'expenseLinks')
        const aValue = await bus?.getValue?.()
        const whiteListMap = { details: 'details' }
        if (expenseLinkComponent) {
          whiteListMap[expenseLinkComponent.field] = expenseLinkComponent.field
        }
        const formValue = mergeBillFormValue(cValue, aValue, whiteListMap)
        // 合并完整数据
        const fValue = mergeValue(value, formValue, template, isModifyBill)
        this.setState({ value: { ...fValue } }, () => {
          if (fValue?.multiplePayeesMode) {
            this.fnInitPayeeInfo()
          }
        })
        if (['new', 'draft', 'rejected'].includes(billState)) {
          this.fnCheckDelegater(value.submitterId, specification)
        }
      })
      external && bus.setFieldsExternalsData?.({ ...external.form })
      this.loadFlowRiskInfo()

      //自动生成费用明细
      if (AutoGenerateFeeDetail && currentValue) {
        this.getAutoGenerationFeeRules(specification, currentValue, flowId)
      }
      // 审批中立即修改
      sourcePage == 'ModifyNow' && this.initValidateByModify()
      // 获取业务对象依赖字段列表
      this.initDataLinkPermissionFields()
      //业务对象筛选数据唯一值自动赋值
      this._initAutoAssignOfOneResultOfDataLink({ template: this.state.template, value: currentValue })
    }, timeout)

    addResizeEvent()
    window.addEventListener('click', this.onResize)
    // external && bus.setFieldsExternalsData?.({ ...external.form })

    if (MYCARBUSINESS) {
      //如果开启私车公用的话提前加载地图对象
      setTimeout(addMapJsApi, 0)
    }

    const ls = localStorage.getItem(flowId)
    const param = isModifyBill ? { flowId } : undefined
    const collectionAccountInquiry = getBoolVariation('collection-account-inquiry')
    const count = collectionAccountInquiry ? 100 : 2999
    api
      .invokeService('@common:list:payees:action', {
        ...param,
        start: 0,
        count,
        filter: '(asPayee==true&&active==true)'
      })
      .then(result => {
        const payees = result?.items || []
        const billValue = this.props.value
        if (this.props.isModifyBill) {
          clearBillCache()
        }
        if (ls && ls !== 'undefined' && !this.props.isModifyBill) {
          const lsValue = updateDetailIndex(JSON.parse(ls))
          if (showDialg && !billBack) {
            showConfirmToEdit().then(key => {
              switch (key) {
                case 'success':
                  const value = this.formatLinkDetailEntities(lsValue)
                  const { specificationId } = value
                  api.invokeService('@home:save:specification', specificationId).then(() => {
                    if (specificationId.id !== specification.id) {
                      this.setState({ value, isContinue: true }, () =>
                        this.handleSpecificationChange(specificationId, value)
                      )
                    } else {
                      this.setState({ value, isContinue: true }, () => this.fnInitPayeeInfo())
                    }
                  })
                  break
                case 'cancel':
                  localStorage.removeItem(flowId)
                  !billValue && related.clearRelateMap()
                  this.removeRecordExpenses(flowId)
                  this.fnInitDetailValue(payees, billValue, () => this.fnInitPayeeInfo())
                  break
                default:
                  break
              }
            })
          } else {
            const value = lsValue
            const { specificationId } = value
            if (specificationId.id !== specification.id) {
              this.setState({ value, isContinue: true }, () => this.handleSpecificationChange(specificationId, value))
            } else {
              this.setState({ value, isContinue: true }, () => this.fnInitPayeeInfo())
            }
          }
        } else {
          this.fnInitDetailValue(payees, billValue, () => this.fnInitPayeeInfo())
        }
      })
    if (value?.details?.length > 0) {
      this.handleDetailValueChange(value.details)
    }
    flowDetailsStatistics({
      formType: type,
      state: billState || 'new'
    })
    reportBillPagePaintDuration()
  }
  // 审批中修改保存事件回调
  handleBillInfoModifySaveCallBack = () => {
    this.setState({
      billFeeForceValidation: true
    })
  }

  getSpecHiddenFields = () => {
    return this.state.hiddenFields || []
  }

  getHiddenFields = async specification => {
    const hiddenFields = await getSpecificationHiddenFields(specification)
    this.setState({ hiddenFields })
  }

  loadFlowRiskInfo = async newDetails => {
    const { value = {}, specification } = this.state
    const { bus, state, type } = this.props
    let form = value
    const formValue = await bus?.getValue?.()
    form = { ...form, ...formValue }
    if (newDetails) {
      form = { ...form, details: newDetails }
    }
    if (!form.specificationId) {
      form.specificationId = specification
    }
    const billState = state.data?.state
    const components = get(specification, 'components') || []
    const detailCmp = components.find(el => el.field === 'details')
    if (
      detailCmp?.realtimeCalculateBudget &&
      !!form?.details?.length &&
      ['draft', 'rejected', 'modify', 'new', undefined].includes(billState)
    ) {
      form.details.forEach(detail => {
        if (detail?.feeTypeForm && !detail.feeTypeForm.detailId) {
          detail.feeTypeForm.detailId = nanoid()
        }
      })
      this.tempId = new Date().getTime()
      api
        .invokeService('@bill:get:flow:risk:info', {
          formType: type,
          form: parseShowValue2SaveValue(form),
          state: billState || 'new',
          version: this.tempId
        })
        .then(riskData => {
          if (this.tempId.toString() === riskData.flowId) {
            const riskInfo = formatRiskWarnings(riskData, form?.details)?.form?.details
            if (riskInfo) {
              this.setState({ riskInfo })
            }
          }
        })
      return Promise.resolve(form.details)
    }
    return Promise.resolve(newDetails)
  }

  // 审批中修改自动定位错误字段
  initValidateByModify = debounce(() => {
    const { bus, isModifyBill } = this.props
    const { specification } = this.state

    if (isModifyBill && bus) {
      bus.getValueWithValidate(1).catch(e => {
        const components = specification.components
        const errors = getValidateErrorByShow(components, Object.keys(e))
        if (!errors.length) {
          setValidateError({ bill: Object.keys(e) })
        }
        return Promise.reject(e)
      })
    }
  }, 1000)

  // 获取业务对象依赖关系
  initDataLinkPermissionFields = async () => {
    let { specification } = this.state
    const items = await getDataLinkPermissionFieldComponent(specification?.id)
    this.setState({
      dependenceFieldOfDataLink: items
    })
  }

  //业务对象字段唯一值时，自动赋值逻辑
  initAutoAssignOfOneResultOfDataLink = debounce(async ({ template = [], value = {}, fields = [] }) => {
    const { bus, state, isModifyBill } = this.props
    const billState = state?.data?.state
    const editBill = (billState && ['draft', 'rejected'].includes(billState)) || isModifyBill

    let curTime = new Date().getTime()
    this.timer = curTime
    const dataLinkFields = filterDataLinkFields(template, fields)
    if (!dataLinkFields.length) return
    const limit = 10
    const currentValue = cloneDeep(value)
    currentValue.submitterId = getV(currentValue, 'submitterId.id')
    currentValue.form = { ...currentValue }

    try {
      showLoading()
      const res = await handleAutoAssignOfOneResultOfDataLink({ dataLinkFields, currentValue, limit })
      if (this.timer === curTime) {
        await this.processAutoAssignResults(res, dataLinkFields, currentValue, limit)
      }
      hideLoading()
      this.isInitAutoAssignOfOneResultOfDataLink = false
    } catch (e) {
      console.log(e)
      hideLoading()
    }
  }, 300)

  processAutoAssignResults = async (res, dataLinkFields, currentValue, limit) => {
    const { bus, state, isModifyBill } = this.props
    const billState = state?.data?.state
    const editBill = (billState && ['draft', 'rejected'].includes(billState)) || isModifyBill
    const checkValue = [] // 需要检查的值
    const rangeOnlyOneAutomaticAssignment = useNewAutomaticAssignment() ? 'rangeOnlyOneAutomaticAssignment' : 'autoFillByFilterDataOnlyOne'
    dataLinkFields.forEach((line, idx) => {
      const temp = res[idx]
      const { id } = currentValue[line.field] || {}
      const isPreviousIdPresent = (temp || []).map(v => v?.dataLink?.id).includes(id) // 上一次存在的id是否在这次结果中
      if (temp && temp.length === 1 && line[rangeOnlyOneAutomaticAssignment]) {
        // 唯一值自动赋值
        bus.emit('on:autoAssignDataLink', { id: temp[0]?.dataLink?.id, field: line })
      }
      if (id && !isPreviousIdPresent && temp.length >= limit) {
        // 因返回数据存在翻页，这里记录id，后面统一找
        checkValue.push({ field: line.field, id: id })
        line.filterBy = `(id.in("${id}")) && (active==true)`
      } else if (temp && temp.length !== 1 && !isPreviousIdPresent || !line[rangeOnlyOneAutomaticAssignment] && !isPreviousIdPresent) {
        // 非编辑单据返回多条直接清空，编辑单据只有在筛选依赖条件变更后更新对应赋值结果（多条清空）
        if (!editBill || (editBill && !this.isInitAutoAssignOfOneResultOfDataLink)) {
          bus.emit('clear:autoAssignDataLink', { field: line })
        }
      }
    })

    // 确定数据删除
    if (checkValue.length) {
      const res2 = await handleAutoAssignOfOneResultOfDataLink({
        dataLinkFields,
        currentValue,
        filterBySearch: true
      })

      dataLinkFields.forEach((line, idx) => {
        const temp = res2[idx]
        const { id } = data[line.field] || {}
        const isPreviousIdPresent = (temp || []).map(v => v?.dataLink?.id).includes(id) // 上一次存在的id是否在这次结果中

        if (!isPreviousIdPresent) {
          if (!isEditBill || (isEditBill && !this.isInitAutoAssignOfOneResultOfDataLink)) {
            bus.emit('clear:autoAssignDataLink', { field: line })
          }
        }
      })
    }
  }

  /**
   * 不使用防抖直接使用 业务对象字段唯一值时，自动赋值逻辑
   * 由于和 dynamic value change 调用 initAutoAssignOfOneResultOfDataLink 出现冲突
   * 初始化不使用上面防抖逻辑处理，直接进行调用并且赋值
   * @param template
   * @param value
   * @param fields
   * @returns {Promise<void>}
   * @private
   */
  _initAutoAssignOfOneResultOfDataLink = async ({ template = [], value = {}, fields = [] }) => {
    const { bus, state, isModifyBill } = this.props
    const billState = state?.data?.state
    const editBill = (billState && ['draft', 'rejected'].includes(billState)) || isModifyBill
    const limit = 10
    const dataLinkFields = filterDataLinkFields(template, fields)
    if (!dataLinkFields.length) return

    const currentValue = cloneDeep(value)
    currentValue.submitterId = getV(currentValue, 'submitterId.id')
    currentValue.form = { ...currentValue }

    try {
      showLoading()
      const res = await handleAutoAssignOfOneResultOfDataLink({ dataLinkFields, currentValue, limit })
      if (getBoolVariation('cyxq-77011')) {
        await this.processAutoAssignResults(res, dataLinkFields, currentValue, limit)
      } else {
        await this.processAutoAssignResults(res, dataLinkFields, currentValue)
      }
      hideLoading()
      this.isInitAutoAssignOfOneResultOfDataLink = false
    } catch (e) {
      hideLoading()
    }
  }

  //获取当前模板里自动生成费用明细的监听字段
  getAutoGenerationFeeRules = async (specification, currentValue, flowId) => {
    const { bus } = this.props
    currentValue = await bus.getValue()
    let specId = specification?.id
    api.dispatch(getAutoGenerationFeeDetailRules({ specId })).then(res => {
      let generationRule = res?.value?.generationRule || []
      let generationField = []
      generationRule.forEach(val => {
        generationField.push(val.field)
      })
      let formKey = Object.keys(currentValue)
      let isAutoField = false
      for (var i = 0; i < generationField.length; i++) {
        for (var j = 0; j < formKey.length; j++) {
          if (generationField[i] === formKey[j]) {
            isAutoField = true
            break
          }
        }
      }
      if (currentValue && generationField?.length && isAutoField) {
        //当前字段存在在自动生成费用明细的监听字段里
        let formDetails = cloneDeep(currentValue?.details) || []
        let subData = {
          form: { ...currentValue, specificationId: specification?.id },
          billType: specification?.type,
          currentEditField: '',
          startIdx: formDetails.length
        }
        //拿到这个字段对应的自动生成费用明细
        api.dispatch(getAutoGenerationFeeDetail(subData)).then(async res => {
          let autoDetails = res?.items
          if (!flowId) {
            //判断只有新增的情况，才去渲染全量的自动费用明细
            if (autoDetails && autoDetails.length > 0) {
              let cur = await bus.getValue()
              formDetails = cloneDeep(cur?.details)
              if (!formDetails || (formDetails && formDetails.length === 0)) {
                autoDetails = this.ResetAutoGenerationFeeIdx(autoDetails)
                bus.emit('detail:value:change', autoDetails)
                bus.setFieldsValue({ details: autoDetails })
              } else {
                //如果表单已经存在明细
                let detailsArr = []
                autoDetails.forEach(i => {
                  let id = i?.feeTypeId?.id
                  if (detailsArr.indexOf(id) < 0) {
                    detailsArr.push(id)
                  }
                })
                formDetails = formDetails.filter(v => {
                  return !v?.feeTypeForm?.isAutoDetail || detailsArr.indexOf(v?.feeTypeId?.id) < 0
                })
                autoDetails = this.ResetAutoGenerationFeeIdx(formDetails.concat(autoDetails))
                bus.emit('detail:value:change', autoDetails)
                bus.setFieldsValue({ details: autoDetails })
              }
            } else {
              let cur = await bus.getValue()
              formDetails = cloneDeep(cur?.details)
              formDetails = this.ResetAutoGenerationFeeIdx(formDetails)
              bus.emit('detail:value:change', formDetails)
              bus.setFieldsValue({ details: formDetails })
            }
          }
        })
      }
      this.setState({ generationField, generationRule })
    })
  }

  //重置明细的idx
  ResetAutoGenerationFeeIdx(details) {
    details.forEach((v, index) => {
      v.idx = index
    })
    return details
  }

  removeRecordExpenses = flowId => {
    if (flowId == void 0) return
    localStorage.removeItem(`${flowId}_move_record`)
  }

  clearRelatedApplication = () => {
    this.setState({ applicationListDetails: [] })
  }

  initRelatedApplication = async () => {
    this.map = getDetailsRelateMoney(this.copyDetials)
    return await this.initApplicationDetails(this.map)
  }

  formatLinkDetailEntities = value => {
    related.clear_relatedMap()
    if (value?.details) {
      value.details = value.details.map(line => {
        const value = fnLinkDetailEntitiesValue(line, this.map)
        initRelatedMap(value)
        return value
      })
    }
    return value
  }

  initApplicationDetails = async map => {
    const ids = related.expenseLinkIds
    const result = await api.invokeService('@feetype:get:requisitioninfo:details', { ids })
    const items = result?.items ?? []
    const details = items.map(item => {
      const dataList = item.dataList.map(line => {
        const money = map[line.id] ?? 0
        line.unwrittenOffAmount = new MoneyMath(line.unwrittenOffAmount).add(money).value
        return line
      })
      return { ...item, dataList }
    })
    this.setState({ applicationListDetails: details })
    return details
  }

  fnInitDetailValue = (payees, value, fn) => {
    const { multiplePayeesMode, details } = value
    if (multiplePayeesMode && details) {
      value.details = details.map(detail => {
        const payeeId = detail.feeTypeForm.feeDetailPayeeId
        const payeeInfo = payees.find(payer => payer.id === payeeId)
        detail.feeTypeForm.feeDetailPayeeId = payeeId?.id ? payeeId : payeeInfo
        return detail
      })
      this.setState({ value }, fn)
    } else {
      fn?.()
    }
  }

  // 解决问题：多收款人单据在未做修改时存储草稿
  fnChangeFormShouldSave = ShouldSave => {
    const { bus } = this.props
    this.initPayeeInfo = ShouldSave
    this.shouldSave = true
    bus.un('bill:edit:form:changed:should:save')
  }

  // fromLS: 从localStorage中取来的值，是否参与判断
  fnInitPayeeInfo = (fromLS, specChange = false) => {
    const { bus, billCopiedValue } = this.props
    const { specification, value } = this.state
    const { type, components, configs } = specification ?? {}
    if (type !== 'expense') return null
    if (!specChange) this.shouldSave = false
    if (!components.find(el => el.type === 'payeeInfo')) this.shouldSave = true

    const multiplePayeesModeForCopy = get(billCopiedValue, 'multiplePayeesMode')
    const multiplePayeesModeForLS = get(value, 'payeeId.multiplePayeesMode') || fromLS
    const multiplePayeesMode = get(value, 'multiplePayeesMode') || multiplePayeesModeForCopy
    const { allowMultiplePayees = false } = configs?.find(el => el.ability === 'pay') ?? {}
    let switcherCheck =
      (multiplePayeesModeForCopy || multiplePayeesModeForLS || multiplePayeesMode) && allowMultiplePayees
    if (specChange) switcherCheck = false

    // 复制单据和使用草稿时，需要改值
    let _payPlanMode = false
    let _payeePayPlan = false
    let _paymentPlanByApportion = false
    let _payPlans
    if (multiplePayeesModeForCopy) {
      // 复制单据时
      const { payPlanMode, payeePayPlan, payPlan, paymentPlanByApportion } = billCopiedValue
      _payPlanMode = payPlanMode
      _payeePayPlan = payeePayPlan
      _payPlans = payPlan
      _paymentPlanByApportion = paymentPlanByApportion
    } else if (switcherCheck) {
      _payPlanMode = get(value, 'payeeId.payPlanMode') ?? get(value, 'payPlanMode')
      _payeePayPlan = get(value, 'payeeId.payeePayPlan') ?? get(value, 'payeePayPlan')
      const _payPlanList = value?.payeeId?.payPlanList || []
      _payPlans = _payPlanList?.length ? _payPlanList : get(value, 'payPlan', [])
      _paymentPlanByApportion = get(value, 'payeeId.paymentPlanByApportion') ?? get(value, 'paymentPlanByApportion')
      if (_payPlanMode) {
        const { code, details } = value
        if (code) {
          // 按金额收款时，删除后端返回的feeDetailPayeeId字段,如果是费用模板上添加的就不删除
          value.details = details?.map(detail => {
            const feeDetailPayeeId = detail?.specificationId?.components?.find(cp => cp?.field === 'feeDetailPayeeId')
            if (!feeDetailPayeeId) {
              delete detail.feeTypeForm.feeDetailPayeeId
            }
            return detail
          })
          this.setState({ value })
        }
      }
    }

    const payeeIdComponent = components.find(el => el.field === 'payeeId') || {}
    const payeeComponent = { ...payeeIdComponent, field: 'feeDetailPayeeId', manualAdd: true }
    api.invokeService('@bill:set:payee:component:visibility', { visible: switcherCheck, payeeComponent }).then(() => {
      bus.emit('payPlan:payeeInfo:switcher', {
        switcherCheck,
        payPlanMode: _payPlanMode,
        payeePayPlan: _payeePayPlan,
        payPlans: _payPlans,
        paymentPlanByApportion: _paymentPlanByApportion
      })
    })
  }

  updateImportList = async specificationId => {
    const { type = '' } = this.props
    const id = specificationId?.id ?? specificationId
    if (!id) return
    const result = await api.invokeService('@bill:get:feetypeImportRule:byId', id, type)
    this.importList = result?.items ?? []
  }

  componentWillReceiveProps(nextProps) {
    let { flowId } = this.props.state
    const ls = localStorage.getItem(flowId)
    const { bus } = this.props
    if (this.props.value !== nextProps.value) {
      this.shouldSave = false
      //通过setState给form表单赋值的情况不存localStorage
    }
    if (this.props.globalFields !== nextProps.globalFields || (!ls && this.props.value !== nextProps.value)) {
      this.updateFilterFeeTypeIds(nextProps)
    }
    if (this.props.importData !== nextProps.importData) {
      let importData = nextProps.importData
      if (importData && importData.importInvoiceResult.length > 0) {
        let order = { type: 'invoice', data: importData.importInvoiceResult }
        this.resolve(order)
      }
    }

    if (this.props.external !== nextProps.external) {
      nextProps.external && bus.setFieldsExternalsData?.({ ...nextProps.external.form })
    }
    this.fnGetBillTitle()
  }

  componentWillUnmount() {
    let { bus, setRequisitionInfo, setValidateError } = this.props
    const { flowId } = this.props.state
    const { specification = {} } = this.state

    this.setState = _ => null
    this.forceUpdate = _ => null
    setRequisitionInfo()
    bus.un('HAB:value:changed', this.handleHABValueChange)
    bus.un('element:ref:select:department', this.handleSelectDepartment)
    bus.un('element:ref:select:payee', this.handleSelectPayee)
    bus.un('element:ref:select:staff', this.handleSelectStaff)
    bus.un('element:ref:select:staffs', this.handleSelectStaffs)

    bus.un('element:details:import:click', this.handleImportClick)
    bus.un('element:details:import:quick-expense', this.handleImportQuickExpense)
    bus.un('element:details:import:backclick', this.handleImportBackClick)
    bus.un('element:details:add:click', this.handleAddClick)
    bus.un('element:details:copy:click', this.handleCopyClick)
    bus.un('element:details:add:trips:click', this.handleAddTripsClick)
    bus.un('element:details:line:click', this.handleLineClick)
    bus.un('detail:value:change', this.handleDetailValueChange)

    bus.un('element:ref:select:property', this.handleSelectProperty)
    bus.un('element:attachments:line:click', this.handleAttachment)

    bus.un('element:requisition:line:click', this.handleSelectRequisition)
    bus.un('element:expenseLink:changed', this.handleExpenseLinkChange)

    bus.un('element:details:tips:click', this.handleTipsClick)

    bus.un('element:ref:select:staff:changed', this.handleSubmitterChange)
    bus.un('element:select:city:click', this.handleSelectCity)
    bus.un('dynamic:billinfo:value:changed', this.handleDynamicValueChange)
    bus.un('element:dataLink:select:property', this.handleDataLinkSelect)
    bus.un('element:dataLink:multiselect:property', this.handleMultiDataLinkSelect)
    bus.un('open:associated:dataLink:modal', this.handleOpenAssociatedDataLinkModal)
    bus.un('element:expenselink:import:trip:order', this.handleImportTripOrder)
    bus.un('element:details:import:data:format', this.handleImportAutoCal)
    bus.un('element:field:changed:external', this.handleFormExternalChange)
    bus.un('element:delete:detail:external', this.handleFormDeleteDetaiExternal)
    bus.un('element:money:value:changed:external', this.handleFormExternalChange)
    bus.un('element:trips:value:changed:external', this.handleTripsExternalChange)
    bus.un('bill:edit:form:changed:should:save', this.fnChangeFormShouldSave)
    api.un('update:editedDetailList', this.handleUpdateList)
    api.un('get:bills:value', this.handleGetBillsValue)
    api.un('get:bills:bus', this.handleGetBillsBus)
    api.un('bill:invoice:disable:change', this.updateInvoiceState)
    bus.un('on:dependence:change', this.handleDependenceChange)
    bus.un('clear:realted:application:details', this.clearRelatedApplication)
    bus.un('import:expense:link:details', this.handleImportApplyDetail)
    bus.un('get:expense:link:details', this.handleGetApplyDetails)
    bus.un('update:select:expenselink', this.initRelatedApplication)
    bus.un('update:modify:related', this.updateModifyRelated)
    bus.un('update:calculate:template', this.updateCalTemplate)
    bus.un('dimension:currency:change', this._handleDimensionCurrencyChange)
    bus.un('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
    bus.un('dateRange:changed:value', this.handleDataRangeChange)
    bus.un('update:flow:risk:info', this.loadFlowRiskInfo)
    bus.un('update:payee:value', this.fnInitPayeeInfo)
    bus.un('get:specification:hiddenFields', this.getSpecHiddenFields)

    window.removeEventListener('click', this.onResize)
    setValidateError({ bill: [] })
    api.invokeService('@bill:save:copied:value') //清空在复制单据时用到的数据
    api.invokeService('@bill:set:payee:component:visibility') //清空相关数据
    api.invokeService('@bill:update:dimension:currency', null) //清空法人实体多币种信息
    api.invokeService('@requisition:save:current:requisition:id') // 清空申请事项信息
    this.clearHistoryCurrencyRates()
    Popup.hide()
    leaveFlowPerformanceStatistics({ flowId, specification })
  }

  // 清空时间字段对应的汇率历史版本
  clearHistoryCurrencyRates = () => {
    return api.invokeService('@bill:save:history:currency:rates')
  }

  updateModifyRelated = value => {
    this.copyDetials = get(value, 'details', [])
    let cValue = cloneDeep(value)
    this.initRelatedApplication()
    cValue = this.formatLinkDetailEntities(cValue)
    return cValue
  }

  handleDependenceChange = async params => {
    const { bus } = this.props
    const { template, specification } = this.state
    const field = template.find(v => v.name === 'details') || {}
    const { dependence } = field
    if (dependence && !!dependence.length && !!dependence?.find(v => v.dependenceId === params.key) && !params.isLock) {
      const currentValue = (await bus.getValue()) || {}
      currentValue[params.key] = params.id
      FLAG.cancelLimit = false
      return this.getVisibleFeeTypes(currentValue, specification.id).then(filterList => {
        this.setState({ filterFeeTypeIds: fnFilterFeetypes(filterList) })
      })
    }
  }

  handleDataRangeChange = async date => {
    if (this.props.KA_NUMBER_FIELD_COUNT_RULE_2) {
      // charge 自动计算，按计算半日规则计算 需日期范围变更后触发自动计算
      const billData = cloneDeep(await this.props.bus.getValue())
      this.startAutoCalc2({ ...billData, ...date })
    }
  }

  handleUpdateList = detailId => {
    if (this.editedDetailList.indexOf(detailId) < 0) {
      this.editedDetailList.push(detailId)
    }
  }

  updateCalTemplate = value => {
    const { bus, external } = this.props
    const { template } = this.state
    const clearValidFields = []
    const fields = Object.keys(value)
    fields.forEach(field => {
      let index = template.findIndex(el => el.field === field)
      if (index !== -1) {
        template[index] = { ...template[index], ...value[field] }
        if (value[field]['optional']) {
          clearValidFields.push(field)
        }
      }
    })
    this.setState({ template: [...template] }, () => {
      if (clearValidFields && clearValidFields.length) {
        bus.getValueWithValidate(clearValidFields)
        external && bus.setFieldsExternalsData?.({ ...external.form })
      }
    })
  }

  onResize = e => {
    if (!!~window.navigator.userAgent.indexOf('Android')) {
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        const myEvent = new Event('resize')
        window.dispatchEvent(myEvent)
      }
    }
  }

  handleGetBillsBus = () => {
    let { bus } = this.props
    return bus
  }

  handleGetBillsValue = isFormatValue => {
    const { bus, type } = this.props
    const { template, specification } = this.state
    if (type === 'requisition') {
      return this.handleGetTripsBillsValue(bus, template, isFormatValue)
    }
    return bus
      .getValue()
      .then(values => {
        values.specificationId = specification
        return isFormatValue ? this.formatFilterData(values, specification, bus) : { values }
      })
      .then(result => {
        const { values, saveValue } = result
        const detailsMapPath = {
          details: 'values.details',
          components: 'specificationId.components',
          fieldsValue: 'feeTypeForm'
        }
        return { values, saveValue, template, detailsMapPath }
      })
  }

  formatFilterData = (values, specification, bus) => {
    let datas = cloneDeep(values)
    getImportValueWithValidate(datas, specification)
    datas.specificationId = specification.id
    datas = parseShowValue2SaveValue(datas)
    const multiplePayeesMode = get(datas, 'payeeId.multiplePayeesMode')
    if (multiplePayeesMode) {
      delete datas.payeeId
      datas.multiplePayeesMode = true
    }
    if (specification.type === 'loan' || specification.type === 'requisition' || specification.type === 'custom') {
      return new Promise((resolve, reject) => {
        resolve({ values, saveValue: { form: datas } })
      })
    }
    const isNeedWrittenoff = api.invokeService('@writtenoff:isneedwrittenoff', specification)
    if (isNeedWrittenoff) {
      return bus.invoke('get:writtenOff:value').then(loanWrittenOff => {
        return { values, saveValue: { form: datas, params: { loanWrittenOff } } }
      })
    }
    return { values, saveValue: { form: datas } }
  }

  handleGetTripsBillsValue = (bus, template, isFormatValue) => {
    let { specification } = this.state
    return bus
      .getValue()
      .then(values => {
        let cloneValues = cloneDeep(values)
        cloneValues.specificationId = specification

        return api.invoke('get:trips:details:value').then(
          result => {
            cloneValues.trips = result
            return isFormatValue ? this.formatFilterData(cloneValues, specification, bus) : { values: cloneValues }
          },
          _ => {
            return isFormatValue ? this.formatFilterData(values, specification, bus) : { values: cloneValues }
          }
        )
      })
      .then(result => {
        let detailsMapPath = {
          details: 'values.trips',
          components: 'tripTypeId.specification.components',
          fieldsValue: 'tripForm'
        }
        return { values: result.values, template, detailsMapPath, saveValue: result.saveValue }
      })
  }

  handleDataLinkSelect = data => {
    let { template } = this.state
    let { flowId } = this.props.state
    return openDataLinkEntityList({ data, template, props: this.props, flowId, source: 'bill' })
  }

  handleMultiDataLinkSelect = data => {
    let { template, specification } = this.state
    let { type, bus } = this.props
    let { flowId } = this.props.state
    return bus.getValue().then(currentValue => {
      const formData = parseShowValue2SaveValue({ ...currentValue, specificationId: specification.id })
      return openMultiSelectDataLinkEntityList(data, template, this.props, flowId, type, formData)
    })
  }
  handleOpenAssociatedDataLinkModal = data => {
    //打开对照关联对话框
    const { bus, getRuleDataLinkFeetype, specification, type: formType } = this.props
    const { flowId } = this.props.state
    return bus.getValue().then(currentValue => {
      const formData = parseShowValue2SaveValue({ ...currentValue, specificationId: specification.id })
      let { entityInfo = {}, value, expenseIds, otherFilters, filterId, linkDataLinkEntity, linkFilterId } = data
      const isString = typeof specification.originalId === 'string'
      const name = get(entityInfo, 'name', '')
      const type = get(entityInfo, 'platformId.type', '')
      const isPrivateCar = type === 'PRIVATE_CAR'
      return api
        .open('@bill:SelectAssociatedDataLinkStep1Modal', {
          value: entityInfo,
          id: value && value.id,
          filterId: filterId,
          linkDataLinkEntity,
          linkFilterId,
          flowId,
          title: `选择${isPrivateCar ? i18n.get('行车记录') : name || '行程订单'}`,
          bus,
          expenseIds,
          getRuleDataLinkFeetype,
          specificationId: isString ? specification.originalId : specification.originalId.id,
          formType,
          formData,
          otherFilters
        })
        .then(result => {
          return result
        })
    })
  }

  handleLastChoice(submitter) {
    const { canRenderMap } = this.state
    const fnUpdateLastChoiceState = lastChoice => {
      if (!canRenderMap?.lastChoice) {
        canRenderMap.lastChoice = lastChoice
        this.setState({ canRenderMap })
      }
    }
    try {
      if (submitter) {
        return api.invokeService('@common:get:lastChoice', submitter.id).then(res => {
          fnUpdateLastChoiceState(true)
          return res
        })
      }
      getValue(this.props).then(async res => {
        if (!res) return res
        const { submitterId } = res
        const s = submitter || submitterId
        await api.invokeService('@common:get:lastChoice', s.id)
        fnUpdateLastChoiceState(true)
      })
    } catch (e) {
      fnUpdateLastChoiceState(true)
    }
  }

  // 根据时间获取对应时期的本位币所对应的汇率
  handleChangeTimeField = async standardCurrencyNumCode => {
    const { bus } = this.props
    const { timeField } = this.state
    if (!timeField) return
    const data = await bus.getFieldsValue()
    const time = data[timeField]
    if (time) {
      let numCode = standardCurrencyNumCode
      if (!numCode) {
        const dimensionCurrency = api.getState()['@bill']?.dimensionCurrencyInfo
        const standardCurrency = dimensionCurrency?.currency
          ? dimensionCurrency?.currency
          : api.getState()['@common']?.standardCurrency
        numCode = standardCurrency?.numCode
      }
      const historyCurrencyInfo = await getEffectiveCurrencyInfo(numCode, time)
      const historyCurrency = historyCurrencyInfo?.items
      await api.invokeService('@bill:save:history:currency:rates', { currency: numCode, rates: historyCurrency })
      bus.emit('set:history:currency:rate', historyCurrency)
      if (data?.details?.length) {
        const details = cloneDeep(data.details)
        let shouldRefreshDetails = false
        details.forEach(el => {
          const feeTypeComponents = el?.specificationId?.components || []
          const moneyFieldKeys = []
          feeTypeComponents.forEach(el => {
            if (el.type === 'money' && !el?.editable) {
              moneyFieldKeys.push(el.field)
            }
          })
          moneyFieldKeys.forEach(key => {
            const moneyFieldValue = el?.feeTypeForm?.[key]
            if (moneyFieldValue && (moneyFieldValue.rate || moneyFieldValue.budgetRate)) {
              let historyCurrencyItem, budgetCurrencyItem
              historyCurrency.forEach(el => {
                if (!historyCurrencyItem && el.numCode === moneyFieldValue.foreignNumCode) historyCurrencyItem = el
                if (!budgetCurrencyItem && el.numCode === moneyFieldValue.budgetNumCode) budgetCurrencyItem = el
              })
              if (
                (historyCurrencyItem && historyCurrencyItem.rate !== moneyFieldValue.rate) ||
                (budgetCurrencyItem && budgetCurrencyItem.budgetRate !== moneyFieldValue.budgetRate)
              ) {
                el.shouldSaveFeetype = true
                shouldRefreshDetails = true
              }
            }
          })
        })
        if (shouldRefreshDetails) bus.setFieldsValue({ details })
      }
    }
  }

  checkDetailsRates = async () => {
    const { bus } = this.props
    const data = await bus.getFieldsValue()
    // 取汇率
    const dimensionCurrency = api.getState()['@bill']?.dimensionCurrencyInfo
    const allCurrencyRates = await api.dataLoader('@common.allCurrencyRates').reload()
    const rates = dimensionCurrency?.rates || allCurrencyRates || []

    bus.emit('set:history:currency:rate', rates)
    if (data?.details?.length) {
      const details = cloneDeep(data.details)
      let shouldRefreshDetails = false
      details.forEach(el => {
        const feeTypeComponents = el?.specificationId?.components || []
        const moneyFieldKeys = []
        feeTypeComponents.forEach(el => {
          if (el.type === 'money' && !el?.editable) {
            moneyFieldKeys.push(el.field)
          }
        })
        moneyFieldKeys.forEach(key => {
          const moneyFieldValue = el?.feeTypeForm?.[key]
          if (moneyFieldValue.rate || moneyFieldValue.budgetRate) {
            let historyCurrencyItem, budgetCurrencyItem
            rates.forEach(el => {
              if (!historyCurrencyItem && el.numCode === moneyFieldValue.foreignNumCode) historyCurrencyItem = el
              if (!budgetCurrencyItem && el.numCode === moneyFieldValue.budgetNumCode) budgetCurrencyItem = el
            })
            if (
              (historyCurrencyItem && historyCurrencyItem.rate !== moneyFieldValue.rate) ||
              (budgetCurrencyItem && budgetCurrencyItem.budgetRate !== moneyFieldValue.budgetRate)
            ) {
              el.shouldSaveFeetype = true
              shouldRefreshDetails = true
            }
          }
        })
      })
      if (shouldRefreshDetails) bus.setFieldsValue({ details })
    }
  }

  handleHABValueChange = value => {
    const { bus } = this.props
    bus.setFieldsValue(value)
  }

  handleDynamicValueChange = (changeValues, filteredChangedValues) => {
    const obj = isEqual(changeValues, {})
    let {
      autoCalFields,
      customizeQueryRule,
      specification,
      generationField,
      generationRule,
      dependenceFieldOfDataLink,
      timeField,
      template,
      autoCalOnFields
    } = this.state
    const { bus } = this.props
    let changeKeys = Object.keys(changeValues)

    if (timeField && changeKeys.includes(timeField)) this.handleChangeTimeField()

    //业务对象联查每次请求会更新监听的字段，应该先于系统计算执行，避免系统计算后未触发联查
    if (!filteredChangedValues) {
      filteredChangedValues = changeValues
    }
    const filteredChangeKeys = Object.keys(filteredChangedValues)
    const fn = () => {
      filteredChangeKeys.forEach(key => {
        if (isNeedUpdateCustomizeResult(key, customizeQueryRule)) {
          this.updateCustomizeCalResult(filteredChangedValues)
        }
      })
    }
    if (getBoolVariation('cyxq-75092')) {
      callFnByQueueNew({}, fn)
    } else {
      callCustomizeCalByQueue(fn)
    }

    changeKeys.forEach(key => {
      if (isNeedUpdateResult(key, autoCalFields, autoCalOnFields) && this.isFirstAutoCalFinished) {
        setTimeout(() => {
          this.updateAutoCalResult()
        }, 100)
      }
    })

    // 判断变更字段是否属于业务对象依赖字段,如果是执行业务对象唯一值自动赋值
    const currentDependenceField = getDependenceFieldOfDataLink(dependenceFieldOfDataLink, changeKeys)
    if (currentDependenceField) {
      setTimeout(() => {
        bus.getValue().then(value => {
          if (getBoolVariation('cyxq-75092')) {
            callFnByQueueNew({}, () =>
              this._initAutoAssignOfOneResultOfDataLink({
                template,
                value,
                fields: currentDependenceField?.fields
              })
            )
          } else {
            this.initAutoAssignOfOneResultOfDataLink({
              template,
              value,
              fields: currentDependenceField?.fields
            })
          }
        })
      }, 100)
    }

    if (!obj && this.shouldSave) {
      this.updateLocalStorage()
    }
    if (!this.initPayeeInfo) {
      this.shouldSave = true
    }
    if (Object.keys(changeValues) && Object.keys(changeValues)?.length === 1) {
      bus.getValue().then(currentValue => {
        bus.__CURRENT_IS_CHANGED = this.__CURRENT_IS_CHANGED
        this.__CURRENT_IS_CHANGED = true
        this.handleAutoGenerateDetails(changeValues, currentValue, specification, generationField, generationRule)
      })
    }
    bus?.has('bill:value:changed:forAIAttachment') && bus?.emit('bill:value:changed:forAIAttachment', changeValues)
    //监听单据查询组件,系统计算的时候走，对账单不走
    if (changeKeys.includes('flowLinks')) return
    if (!specification?.id?.includes('system:对账单') && specification?.components?.length > 0) {
      let isFlowLinks = specification?.components?.find(v => v?.field === 'flowLinks')
      if (isFlowLinks && !isFlowLinks?.editable) {
        bus.emit('flowLinkDynamicChange')
      }
    }

  }

  handleAutoGenerateDetails = (changeValues, formValue, specification, generationField, generationRule) => {
    const { bus } = this.props
    if (
      Object.keys(changeValues)[0] &&
      generationField?.length &&
      generationField.indexOf(Object.keys(changeValues)[0]) > -1 &&
      formValue
    ) {
      //当前字段存在在自动生成费用明细的监听字段里
      let formDetails = cloneDeep(formValue?.details) || []
      let subData = {
        form: { ...formValue, specificationId: specification?.id },
        billType: specification?.type,
        currentEditField: Object.keys(changeValues)[0],
        startIdx: formDetails.length
      }
      //拿到这个字段对应的自动生成费用明细
      api.dispatch(getAutoGenerationFeeDetail(subData)).then(async res => {
        let autoDetails = res?.items
        let subDetails = []
        if (autoDetails && autoDetails.length > 0) {
          //如果没有formDetails,直接插入生成的费用明细
          let cur = await bus.getValue()
          formDetails = cloneDeep(cur?.details)
          if (!formDetails || (formDetails && formDetails.length === 0)) {
            autoDetails = this.ResetAutoGenerationFeeIdx(autoDetails)
            bus.emit('detail:value:change', autoDetails)
            bus.setFieldsValue({ details: autoDetails })
          } else {
            //如果表单已经存在明细
            let detailsArr = []
            autoDetails.forEach(i => {
              let id = i?.feeTypeId?.id
              if (detailsArr.indexOf(id) < 0) {
                detailsArr.push(id)
              }
            })
            formDetails = formDetails.filter(v => {
              return !v?.feeTypeForm?.isAutoDetail || detailsArr.indexOf(v?.feeTypeId?.id) < 0
            })
            subDetails = this.ResetAutoGenerationFeeIdx(formDetails.concat(autoDetails))
            bus.emit('detail:value:change', subDetails)
            bus.setFieldsValue({ details: subDetails })
          }
        } else {
          let cur = await bus.getValue()
          formDetails = cloneDeep(cur?.details)
          generationRule.forEach(i => {
            if (i.field == Object.keys(changeValues)[0]) {
              formDetails = formDetails.filter(v => {
                return !v?.feeTypeForm?.isAutoDetail || v.feeTypeId.id != i.feeTypeId
              })
              subDetails = this.ResetAutoGenerationFeeIdx(formDetails.concat(autoDetails))
              bus.emit('detail:value:change', subDetails)
              bus.setFieldsValue({ details: subDetails })
            }
          })
        }
      })
    }
  }

  updateLocalStorage = debounce(() => {
    const { bus } = this.props
    const { specification } = this.state
    const { flowId = getBillKey() } = this.props.state
    bus?.getValue?.().then(result => {
      if (result) {
        result.specificationId = specification
        console.log('[ updateLocalStorage ] >', result)
        if (result?.details) {
          result.details = result.details?.filter?.(d => !d?.noCache)
        }
        localStorageSet(flowId, JSON.stringify(result))
      }
    })
  }, 1000)

  updateAutoCalFields = (submitter, isDelegatorChange, onlyQueryFields, params = { needReload: false }) => {
    const { getCalculateField, customizeQueryPower } = this.props
    const { specification } = this.state
    if (!specification) return

    getValue(this.props).then(res => {
      if (!res) return res
      const { submitterId } = res
      const s = submitter || submitterId
      this.submitter = s
      if (s) {
        if (customizeQueryPower) {
          this.isFirstCustomizeCalFinished = false
          //保存后再打开等表单赋值后再去查询联查监听字段，不然手动修改后可能会被重新查询覆盖
          const delayGetQueryField = onlyQueryFields ? 1000 : 0
          setTimeout(() => {
            this.updateCustomizeCalResult(
              undefined,
              isDelegatorChange ? [{ type: 'master_', values: ['submitterId'], operate: 'editBill' }] : undefined,
              true,
              onlyQueryFields,
              false,
              params
            )
          }, delayGetQueryField)
        }
        return getCalculateField(specification.id, s.id).then(action => {
          if (action.error) return
          const autoRules = action.payload.items || []
          const autoCalFields = (!!autoRules.length && autoRules[0].fields) || [] //参与计算的字段
          const autoCalOnFields = (!!autoRules.length && autoRules[0].onFields) || [] //被计算的字段
          this.isFirstAutoCalFinished = false
          this.updateAutoCalResult(true)
          this.setState({
            autoCalFields,
            autoCalOnFields,
            calFields: autoRules[0]
          })
          return autoCalFields
        })
      }
    })
  }

  isFirstAutoCalFinished = false
  updateAutoCalResult = debounce(async (checkDefaultValue = false) => {
    const { bus, baseDataPropertiesMap, state = {}, value, userInfo } = this.props
    const { specification, template } = this.state
    try {
      const formValue = await bus.getValue()
      formValue.specificationId = specification && specification.id
      if (state.flowId) {
        formValue.flowId = state.flowId
      }
      const code = getV(value, 'code')
      if (code) {
        formValue.code = code
      }
      if (specification?.type === 'permit') {
        formValue.submitterId = userInfo?.staff
      }
      bus.emit('bill:loading:change', true)
      const needUpdateDefaultValue = checkDefaultValue && !state.flowId
      console.log('[ getAutoCalResultOnField updateLocalStorage ] >')
      await getAutoCalResultOnField(
        baseDataPropertiesMap,
        bus,
        specification,
        formValue,
        'master_',
        null,
        this.updateLocalStorage,
        template,
        needUpdateDefaultValue
      ).finally(_ => {
        bus.emit('bill:loading:change', false)
      })
      if (checkDefaultValue) {
        this.isFirstAutoCalFinished = true
        // 新建单据设置默认币种
        this.props.canUseDefaultCurrency && !state?.data?.state && !state?.flowId && setDefaultCurrencyMoney.call(this)
      }
      bus.emit('bill:loading:change', false)
    } catch (e) { }
  }, 400)

  isFirstCustomizeCalFinished = false
  updateCustomizeCalResult = debounce(
    async (
      changeValues = {},
      changedFields,
      checkDefaultValue = false,
      onlyQueryFields,
      filterDataLinkFields = false,
      params = { needReload: false }
    ) => {
      const { bus, baseDataPropertiesMap, state = {}, value } = this.props
      const { specification, template } = this.state
      const billState = getV(state, 'data.state')
      try {
        const formValue = await bus?.getValue?.()
        if (!formValue) {
          setTimeout(() => {
            //本地缓存继续编辑时bug.getValue获取不到重新请求一次
            this.updateCustomizeCalResult(
              changeValues,
              changedFields,
              checkDefaultValue,
              onlyQueryFields,
              false,
              params
            )
          }, 200)
          return
        }
        formValue.specificationId = specification && specification.id
        if (state.flowId) {
          formValue.flowId = state.flowId
        }
        const code = getV(value, 'code')
        if (code) {
          formValue.code = code
        }
        bus.emit('bill:loading:change', true)
        const needUpdateDefaultValue = checkDefaultValue && !state.flowId
        const customizeQueryRule = await getCustomizeCalResultOnField(
          baseDataPropertiesMap,
          bus,
          specification,
          formValue,
          'master_',
          null,
          this.updateLocalStorage,
          template,
          needUpdateDefaultValue,
          formValue.specificationId,
          billState,
          changeValues,
          changedFields,
          onlyQueryFields,
          filterDataLinkFields
        ).finally(_ => {
          bus.emit('bill:loading:change', false)
        })
        customizeQueryRule &&
          this.setState({ customizeQueryRule: customizeQueryRule?.queryFields }, () => {
            if (params?.needReload) {
              this.updateCustomizeCalResult(changeValues, changedFields, checkDefaultValue, onlyQueryFields)
            }
          })
        if (checkDefaultValue) {
          this.isFirstCustomizeCalFinished = true
        }
        bus.emit('bill:loading:change', false)
      } catch (e) {
        console.log('[ updateCustomizeCalResult catch ] >', e)
        setTimeout(() => {
          //本地缓存继续编辑时bug.getValue获取不到重新请求一次
          this.updateCustomizeCalResult(changeValues, changedFields, checkDefaultValue, onlyQueryFields, false, params)
        }, 200)
      }
    },
    100
  )

  updateFilterFeeTypeIds = props => {
    let { specification: stateSpecification = {}, value } = this.state
    let { specification = {}, ...others } = parseSpecification(props, this.state)
    let filterFeeTypeIds = fnFilterFeetypes(props.newFilterFeeTypes)
    const whiteList = ['ID01iLmzUrRPVd', '8focGZAIaZKI00']
    if (whiteList.includes(Fetch.ekbCorpId)) {
      // props上的value和state上面的value不一致，之前代码使用的是props上面的value，会导致state上面的value丢失，目前现在针对这家企业客户做更改，如果没有问题就全量放开
      const { value: updateValue } = others
      others.value = { ...value, ...updateValue }
    }
    this.setState({ ...others, specification, filterFeeTypeIds })
    if (get(stateSpecification, 'id') !== get(specification, 'id')) {
      this.updateImportList(specification.originalId)
    }
  }

  fnGetBillTitle = async (submitterId) => {
    let { tags, state, submitter, current_flow } = this.props
    let { data } = state
    const currentSubmitterId = submitterId || submitter
    if (current_flow?.state === 'rejected') {
      api.invokeService('@layout:set:header:title', current_flow.form.specificationId.name)
      return
    }
    if (tags && tags.submitterId.userInfo) {
      if (data && data.state !== 'rejected') {
        return
      }
      let title =
        tags.submitterId.userInfo.id === currentSubmitterId?.id
          ? i18n.get('新建单据')
          : i18n.get('新建报销单：代{__k0}提交', { __k0: currentSubmitterId?.name })
      api.invokeService('@layout:set:header:title', title)
    } else {
      api.invokeService('@layout:set:header:title', i18n.get('新建单据'))
    }
  }

  handleSubmitterChange = submitterId => {
    this.fnGetBillTitle(submitterId)
    this.reloadStaffAndDeptment(submitterId && submitterId.id)
  }

  reloadStaffAndDeptment = id => {
    const { isFilterStaffAndDept } = this.props
    //配置了钉钉人员可见性时，切换委托人获取委托人的可见性数据,如果没有配置钉钉可见性则所有人看到的数据时相同的
    if (isFilterStaffAndDept) {
      api.invokeService('@common:get:visibility:staffs', id)
      api.invokeService('@common:list:visibility:departments', id)
    }
  }

  handleAttachment = (value, index) => {
    fnPreviewAttachments({ value, index })
  }

  countBy = (arr, value) => {
    return (arr || []).filter(item => item === value).length
  }

  // 获取附件字段明细中是否配置发票类型控制
  getAttachmentOfInvoiceType = () => {
    const { specification } = this.state
    const components = specification?.components ?? []
    const obj = components.find(item => item?.field === 'attachments' && item?.checkDetailInvoiceType)
    return Boolean(obj)
  }

  handleDetailValueChange = value => {
    let currentEditField = null
    if (value.currentEditField) {
      currentEditField = value.currentEditField
      delete value.currentEditField
    }
    // 数字字段计算规则赋值
    if (this.props.KA_NUMBER_FIELD_COUNT_RULE) {
      this.countRuleCheckFields.forEach(fieldRuleMap => {
        const { bus } = this.props
        const {
          fieldKey,
          countRule: { fieldValue, fieldType, operatorStr, isDistinct, countNum }
        } = fieldRuleMap
        let fieldsValues = value
          .map(valueItem => {
            // 如果是时间戳，转换成当天的起始时间戳
            if (fieldType && fieldType === 'date') {
              const value = valueItem.feeTypeForm[fieldValue]
              return value
                ? moment(value)
                  .startOf('day')
                  .unix()
                : undefined
            }
            return valueItem.feeTypeForm[fieldValue]
          })
          .filter(item => item !== undefined)
        fieldsValues = fieldsValues.filter(valueItem => {
          if (valueItem === undefined) {
            return false
          }
          switch (operatorStr) {
            case '=':
              return this.countBy(fieldsValues, valueItem) === countNum
            case '>':
              return this.countBy(fieldsValues, valueItem) > countNum
            case '>=':
              return this.countBy(fieldsValues, valueItem) >= countNum
            case '<':
              return this.countBy(fieldsValues, valueItem) < countNum
            case '<=':
              return this.countBy(fieldsValues, valueItem) <= countNum
          }
        })

        if (isDistinct) {
          fieldsValues = uniq(fieldsValues)
        }
        bus.setFieldsValue({
          [fieldKey]: fieldsValues.length + ''
        })
      })
    }

    if (this.props.KA_NUMBER_FIELD_COUNT_RULE_2) {
      if (!value || value.length === 0) return
      /**
       * 由于默认添加的费用明细不存在 ID
       * 当存在这个能力时，自动加上 uuid 。
       */
      value.forEach(detail => {
        detail.feeTypeForm.detailId = detail.feeTypeForm.detailId ?? nanoid()
      })
      this.startAutoCalc2(value)
    }

    this.detailsTemplateData = value
    this.valuation = {}
    value.forEach(d => {
      if (!d.feeTypeForm || !d.feeTypeForm.ordersData) return
      const { amount, ordersData } = d.feeTypeForm
      const { platform, transactRecordId } = ordersData?.[0] ?? {}
      if (platform === 'transact') {
        const valuationItem = this.valuation[transactRecordId]
        if (valuationItem) {
          valuationItem.used = new MoneyMath(valuationItem.used).add(amount).value
        } else {
          this.valuation[transactRecordId] = {
            total: amount,
            used: amount
          }
        }
      }
    })
    // 等 form 赋值到表单上
    this.props.customizeQueryPower &&
      setTimeout(() => {
        // 业务对象联动赋值
        this.isFirstCustomizeCalFinished = false
        currentEditField && this.updateCustomizeCalResult(undefined, currentEditField, true, '', true)
      }, 0)

    // 附件字段配置单据详情发票指定类型必填逻辑
    const hasAttachmentConfig = this.getAttachmentOfInvoiceType()
    if (hasAttachmentConfig) {
      setTimeout(() => {
        this.updateAutoCalResult()
      }, 0)
    }
    console.log('[ handleDetailValueChange updateLocalStorage ] >')
    this.updateLocalStorage()
  }

  // 红杉快速报销导入
  handleImportQuickExpense = async () => {
    const { bus, newFilterFeeTypes, type } = this.props
    const { specification } = this.state
    const currentValue = await bus.getValue()
    const isEBussCard = specification.configs?.find(item => item.isEBussCard)?.isEBussCard
    let submitter = currentValue.submitterId
    const details = getV(currentValue, 'details', [])
    const importedDetailIds = details.map(el => getV(el, 'feeTypeForm.detailId'))
    const filterFeeTypeIds = fnFilterFeetypes(newFilterFeeTypes)
    return openQuickExpense({
      recordType: type,
      filterFeeTypeIds,
      submitterId: submitter.id,
      isEBussCard,
      fromSourceType: 'createBill',
      importedDetailIds
    }).then(res => {
      return res.result
    })
  }

  // 第三方导入
  handleImportClick = async (orderImported, deleteOrders) => {
    let { platFromList, bus, newFilterFeeTypes, type, dataFromOrder } = this.props
    let { thirdImportMenuValue, specification } = this.state
    let detailsData = this.detailsTemplateData || []
    const currentValue = await bus.getValue()
    const filterList = await this.getVisibleFeeTypes(currentValue, specification.id)
    if (!FLAG.cancelLimit && !filterList?.length) {
      try {
        await this.handleNoFeeTypeModal()
      } catch (_) {
        return
      }
    }
    let submitter = currentValue.submitterId
    const specificationVersionId = specification.id
    const multiplePayeesMode =
      get(currentValue, 'payeeId.multiplePayeesMode') || get(currentValue, 'multiplePayeesMode')
    const isEBussCard = specification.configs?.find(item => item.isEBussCard)?.isEBussCard
    let importType = ''
    const callback = async () => {
      const types = await this.getImportMenus()
      const billType = { billType: type }
      const filterFeeTypeIds = fnFilterFeetypes(newFilterFeeTypes)
      if (!types.length) {
        Dialog.alert({ title: i18n.get('无可用导入方式'), content: i18n.get('无可用导入方式，请联系企业管理员。') })
        return
      }
      if (isEBussCard && types.find(item => item.list.find(ii => ii.type === 'recordExpends'))) {
        const fromSourceType = 'createBill'
        return openRecord(type, filterFeeTypeIds, submitter.id, isEBussCard, fromSourceType).then(res => {
          return res.result
        })
      }
      const hideGroupHeader = dataFromOrder?.showImportInPermit
      return api
        .invokeService('@third-import:click:import', {
          hideGroupHeader,
          platFromList,
          detailsData,
          valuation: this.valuation,
          types,
          thirdImportMenuValue,
          orderImported,
          deleteOrders,
          bus,
          submitter,
          specificationVersionId,
          billType,
          filterFeeTypeIds,
          isEBussCard
        })
        .then(result => {
          let { type, data } = result
          importType = type
          return type === 'invoice' ||
            type === 'dataLink' ||
            type === 'invoiceOCR' ||
            type === 'recordExpends' ||
            type === 'applyDetail'
            ? data
            : this.handleImportAutoCal(result)
        })
        .then(result => {
          return this.setupEnumValue(result)
        })
        .then(async result => {
          if (specification.type === 'expense' && importType === 'dataLink') {
            result = await fnApportionDetails(result, specification)
          }
          if (multiplePayeesMode) {
            const { payeeComponent } = api.getState()['@bill'].payeeComponentData
            result = result.map(detail => {
              if (!~detail.specificationId.components.findIndex(el => el.field === 'feeDetailPayeeId')) {
                detail.specificationId.components.push(payeeComponent)
              }
              return detail
            })
          }
          return this.setupSettlementValue(result)
        })
    }
    if (newFilterFeeTypes && newFilterFeeTypes.length) {
      return callback()
    } else {
      return this.getVisibleFeeTypes(currentValue, specification.id).then(filterList => {
        if (filterList && filterList.length) {
          this.setState({
            filterFeeTypeIds: fnFilterFeetypes(filterList)
          })
          return callback()
        } else {
          return this.handleNoFeeTypeModal()
        }
      })
    }
  }

  setupEnumValue(result) {
    if (result && result[0] && result[0].feeTypeForm.invoiceType) {
      return api
        .invokeService('@common:get:property:by:name', {
          name: 'basedata.Enum.InvoiceType'
        })
        .then(data => {
          return result.map(item => {
            let invoiceTypeId = get(item, 'feeTypeForm.invoiceType.id') || get(item, 'feeTypeForm.invoiceType')
            item.feeTypeForm.invoiceType = data.items.find(e => e.id === invoiceTypeId)
            return item
          })
        })
    }
    return result
  }

  async setupSettlementValue(result) {
    if (result && result[0] && result[0].feeTypeForm.settlement) {
      return api
        .invokeService('@common:get:property:by:name', {
          name: 'basedata.Settlement'
        })
        .then(data => {
          return result.map(item => {
            let id = get(item, 'feeTypeForm.settlement.id') || get(item, 'feeTypeForm.settlement')
            item.feeTypeForm.settlement = data.items.find(e => e.id === id)
            return item
          })
        })
    }
    let newResult = result
    if (result && result[0]) {
      // 获取merge，判断是否多条消费记录合并生成一条明细
      let merge = get(result[0], 'feeTypeForm.ordersData[0].merge')
      if (merge) {
        newResult = [result[0]]
        const feeTypeForm = result[0].feeTypeForm
        const keys = Object.keys(feeTypeForm)
        result.forEach((item, index) => {
          if (index) {
            keys.forEach(el => {
              if (feeTypeForm[el] !== item.feeTypeForm[el]) {
                const fieldDataType = Object.prototype.toString.call(item.feeTypeForm[el])
                if (fieldDataType.includes('Array')) {
                  feeTypeForm[el] = feeTypeForm[el].concat(item.feeTypeForm[el])
                }
                if (fieldDataType.includes('Object') && feeTypeForm[el].standard) {
                  feeTypeForm[el].standard = new Big(feeTypeForm[el].standard)
                    .plus(new Big(item.feeTypeForm[el].standard))
                    .toFixed(feeTypeForm[el]?.standardScale)
                }
              }
            })
          }
        })
        feeTypeForm.ordersData.forEach(item => {
          delete item['merge']
        })
        newResult[0].feeTypeForm = feeTypeForm
      }
    }
    const components = get(newResult, '[0].specificationId.components', [])
    const officialCardSettlementField = components.filter(
      item => get(item, 'defaultValue.type') === 'officialCardSettlement'
    )
    if (officialCardSettlementField?.length) {
      let { settlementId } = this.state
      // 若无settlementId则再次查询结算方式
      if (!settlementId) {
        settlementId = await this.handleGetSettlementId(components)
      }
      if (settlementId) {
        newResult.forEach(el => {
          if (JSON.stringify(el)?.includes('transact')) {
            officialCardSettlementField.forEach(v => {
              const defaultValueType = get(v, 'defaultValue.type')
              // 取公务卡结算方式
              if (defaultValueType === 'officialCardSettlement' && settlementId) {
                el.feeTypeForm[v.field] = settlementId
              }
            })
          }
        })
      }
    }
    return newResult
  }

  handleGetSettlementId = async components => {
    const officialCardSettlement = components.find(el => get(el, 'defaultValue.type') === 'officialCardSettlement')
    if (officialCardSettlement) {
      let errorMsg = '未设置结算方式，请联系管理员'
      let selectedId = ''
      await getOfficialCardSetting().then(async res => {
        const settlementId = get(res, 'value.settlementId')
        if (settlementId) {
          await getPayments().then(v => {
            const payments = get(v, 'items', [])
            if (payments.find(el => el.id === settlementId)) {
              selectedId = payments.find(el => el.id === settlementId)
            } else {
              errorMsg = i18n.get('找不到该结算方式')
            }
          })
        } else {
          errorMsg = i18n.get('未设置结算方式，请联系管理员')
        }
      })
      if (selectedId) {
        this.setState({ selectedId })
        return selectedId
      } else {
        toast.error(i18n.get(errorMsg))
        return ''
      }
    }
  }

  handleImportAutoCal = async result => {
    const { bus, baseDataPropertiesMap, lastChoice, disableInvoicePower } = this.props
    const { specification } = this.state
    const billData = await bus.getValue()
    const feeData = await FormatData(result, this.props.type)
    disableInvoicePower && this.checkInvoiceState(feeData)
    if (Array.isArray(feeData)) {
      return Promise.all(
        feeData.map(formData => {
          return formatInitialValue(formData, baseDataPropertiesMap, lastChoice, billData.submitterId).then(
            newFormData => {
              return getAutoCalResult(newFormData, billData, specification, baseDataPropertiesMap)
            }
          )
        })
      )
    }
    return getAutoCalResult(feeData, billData, specification, baseDataPropertiesMap)
  }
  //检查发票禁用状态，合并生成时：feeData 是 object；生成多条时：feeData 是 Arr
  checkInvoiceState = feeData => {
    const { disableInfo, markInfo } = this.state
    const details = Array.isArray(feeData) ? feeData : [{ ...feeData }]
    getInvoiceDisableInfo({ details }).then(newList => {
      this.setState({
        disableInfo: { ...newList, ...disableInfo }
      })
    })
    getInvoiceMarkInfo({ details }).then(newList => {
      this.setState({
        markInfo: { ...newList, ...markInfo }
      })
    })
  }
  //新增发票时更新禁用或标记列表
  updateInvoiceState = (disableList, markList) => {
    const { disableInfo, markInfo } = this.state
    this.setState({
      markInfo: { ...markList, ...markInfo },
      disableInfo: { ...disableList, ...disableInfo }
    })
  }

  handleImportTripOrder = async ({ entityInfo, expenseIds, otherFilters }) => {
    const { bus, newFilterFeeTypes } = this.props
    const { specification } = this.state
    const currentValue = await bus.getValue()
    const multiplePayeesMode =
      get(currentValue, 'payeeId.multiplePayeesMode') || get(currentValue, 'multiplePayeesMode')
    const callback = async () => {
      return this.handleMultiDataLinkSelect({ entityInfo, expenseIds, otherFilters })
        .then(result => {
          if (!result) return
          return result.consume
        })
        .then(result => {
          return this.setupEnumValue(result)
        })
        .then(result => {
          if (multiplePayeesMode) {
            const { payeeComponent } = api.getState()['@bill'].payeeComponentData
            result = result.map(detail => {
              if (!~detail.specificationId.components.findIndex(el => el.field === 'feeDetailPayeeId')) {
                detail.specificationId.components.push(payeeComponent)
              }
              return detail
            })
          }
          return this.setupSettlementValue(result)
        })
    }
    if (newFilterFeeTypes && newFilterFeeTypes.length) {
      return callback()
    } else {
      return this.getVisibleFeeTypes(currentValue, specification.id).then(filterList => {
        if (filterList && filterList.length) {
          this.setState({ filterFeeTypeIds: fnFilterFeetypes(filterList) })
          return callback()
        } else {
          return this.handleNoFeeTypeModal()
        }
      })
    }
  }

  async getImportMenus() {
    let { platFromList, dataFromOrder } = this.props
    const { showImportInPermit } = dataFromOrder || {}
    let types = await api.loadPoint('@bill:third:import')
    types = flatten(types)
    let typesMap = {}
    types.forEach(item => (typesMap[item.type] = item))
    let groups = []
    for (let group of this.importList) {
      const menus = []
      group?.list?.forEach(item => {
        if (item.id.indexOf('dataLink') === 0) {
          let dataLink = typesMap['dataLink']
          dataLink = { ...dataLink, ...item }
          const dataLinkType = get(item.dataLinkEntity.platformId, 'type')
          const icon = dataLinkIconList[dataLinkType]
          if (icon) {
            dataLink.icon = icon
          } else {
            dataLink.icon = get(item.dataLinkEntity.platformId, 'icon.fileId.thumbUrl') || PLATFORM_PNG
          }
          menus.push(dataLink)
        } else if (item.id === 'didi') {
          let didi = typesMap[item.id]
          didi.title = item.title
          didi.describe = item.describe
          didi.enTitle = item.enTitle
          didi.enDescribe = item.enDescribe
          let didiOtherInfo
          try {
            if (platFromList instanceof Object) {
              didiOtherInfo = platFromList.find(o => o.id === 'DIDI')
            } else {
              didiOtherInfo = JSON.parse(platFromList).find(o => o.id === 'DIDI')
            }
          } catch (e) {
            console.log('e-->>' + e)
          }
          if (didiOtherInfo) {
            let info = cloneDeep(didiOtherInfo)
            const { corporationBinding = [] } = didiOtherInfo
            const didiBinding = corporationBinding.find(o => o.platformId === 'DIDI')
            if (didiBinding && didiBinding.active) {
              didi.source = info
              didi.disabled = info.importMaint
              menus.push(didi)
            }
          }
        } else {
          let line = typesMap[item.id]
          if (line) {
            line.title = item.title
            line.describe = item.describe
            line.enTitle = item.enTitle
            line.enDescribe = item.enDescribe
            menus.push(line)
          }
        }
      })
      groups.push({ ...group, list: menus })
    }

    // 导入关联明细入口
    const { allowAdd } = related.specificationConfig
    if (allowAdd) {
      let { flowId } = this.props.state
      related.setFlowId(flowId)
      const line = {
        icon: 'EDico-ex-application',
        title: i18n.get('关联明细'),
        type: 'applyDetail',
        group: '',
        onClick: this.handleImportApplyDetail
      }
      const last = groups[groups?.length - 1]
      last.list.push(line)
      if (showImportInPermit) groups = [{ title: i18n.get('其他'), list: [line] }]
    }

    return groups
  }

  handleImportBackClick = () => {
    return this.clickBackPromise().then(result => {
      return FormatData(result)
    })
  }

  handleImportApplyDetail = async () => {
    const data = await this.initApplicationDetails(this.map)
    try {
      const { newFilterFeeTypes, dataFromOrder } = this.props
      const result = await api.open('@feetype:RelatedApplicationModal', {
        dataFromOrder,
        isImport: true,
        newFilterFeeTypes: newFilterFeeTypes || [],
        data: [],
        dataSource: data || []
      })
      return ExpenseLinkUtil.importApplyDetail(result, this.props.bus, dataFromOrder)
    } catch (e) {
      console.log(e)
      related.clear_relatedMap()
    }
  }
  handleGetApplyDetails = () => {
    const { applicationListDetails = [] } = this.state
    const ids = []
    applicationListDetails.forEach(item => {
      const { dataList = [] } = item
      dataList.forEach(o => {
        ids.push(o.detailId)
      })
    })
    return uniq(ids)
  }
  clickBackPromise() {
    const { promise, resolve } = window.PromiseDefer()
    this.resolve = resolve
    return promise
  }

  fnRefreshFilterTripTypes = (tripTypes, specification, bus) => {
    let newTripTypes = cloneDeep(tripTypes)
    return bus.getFieldsValue().then(values => {
      let currentTrips = values['trips'] || []
      let tripList = newTripTypes.filter(trip => trip.active)
      const { configs = [] } = specification
      const tripTypeConfig = configs.filter(config => config.tripType && config.tripType.isAll === false)
      if (tripTypeConfig.length > 0) {
        tripList = tripList.filter(trip => tripTypeConfig[0].tripType.ids.includes(trip.id))
      }
      currentTrips.forEach(trip => {
        if (!tripList.includes(trip.tripTypeId.id)) {
          let ban = newTripTypes.find(t => t.id === trip.tripTypeId.id)
          if (!ban.active) {
            ban.name = ban.name + i18n.get('（已停用）')
            tripList.unshift(ban)
          }
        }
      })
      return api.invokeService('@common:getFilterTripTypes', { filterTripTypes: tripList })
    })
  }

  //addtrip
  handleAddTripsClick = (external, fn, update, canEdit, currentTrips) => {
    let { bus, tripTypes, value = {} } = this.props
    let { template, specification } = this.state
    let { flowId } = this.props.state
    const originTrips = cloneDeep(getV(value, 'trips', []))
    const trips = canEdit ? originTrips : [currentTrips]
    return bus.getValue().then(currentValue => {
      if (flowId) {
        currentValue.flowId = flowId
      }
      let submitterId = currentValue.submitterId
      return this.fnRefreshFilterTripTypes(tripTypes, specification, bus).then(tripList => {
        if (tripList.filterTripTypes.length === 0) {
          return toast.error(i18n.get('无可用行程类型'))
        }
        return api.open('@trip-type:TripTypeEditModal', {
          flowId: this.props.state.flowId,
          fn,
          bus,
          shouldUpdate: update,
          external,
          submitterId,
          billTemplate: template,
          billData: currentValue,
          originTrips,
          specification,
          canEdit,
          trips
        })
      })
    })
  }

  getVisibleFeeTypes = async (value, specificationId) => {
    const formData = parseShowValue2SaveValue({ ...value, specificationId })
    const isCancelLimit = FLAG.cancelLimit
    return getFilterFeetypeList({ formData, isCancelLimit })
  }

  handleAddClick = async ({ selectCurrencyDisable, foreignNumCode, field, invoiceRiskData }) => {
    let {
      type = 'expense',
      bus,
      from,
      value = {},
      isModifyBill,
      flowAllowModifyFields,
      limitFieldRequireds,
      newFilterFeeTypes,
      dataFromOrder
    } = this.props
    const { companyRealPayFromOrder } = dataFromOrder || {}
    const billState = get(this.props, 'state.data.state')
    const currentFlowNode = this.getCurrentFlowNode()
    let {
      template,
      specification,
      disableInfo,
      supportCivilServiceCardFeeTypeIds,
      markInfo,
      applicationListDetails,
      timeField
    } = this.state
    let billType = type
    let { flowId } = this.props.state
    const currentValue = await bus.getValue()
    let submitterId = currentValue.submitterId
    const details = currentValue?.details
    if (flowId) {
      currentValue.flowId = flowId
    }
    const ticketReview = isTicketReview(specification)
    const callback = () => {
      return api.open('@feetype:SelectFeeTypeModal', { specification, needRecommend: true, submitterId }).then(feetype => {
        const detailInitValue = companyRealPayFromOrder ? { amount: companyRealPayFromOrder } : undefined
        return api.open('@feetype:FeeTypeInfoModal', {
          bus,
          value: detailInitValue,
          billType,
          feetype,
          flowId,
          submitterId,
          billTemplate: template,
          isModifyBill,
          flowAllowModifyFields,
          limitFieldRequireds,
          billSpecification: specification,
          formData: currentValue,
          selectCurrencyDisable,
          foreignNumCode,
          field,
          ds: value.details,
          isTicketReview: ticketReview,
          isNewCreate: true,
          isPermitForm: from === 'iframePermit',
          applicationListDetails,
          invoiceRiskData,
          billState,
          saveTempDisableInvoice: this.handleTempDisableInvoice,
          markInfo,
          disableInfo,
          supportCivilServiceCardFeeTypeIds,
          valuation: this.valuation,
          currentFlowNode,
          details,
          notShowModalIfAllInvoiceSuccess: true,
          dataFromOrder,
          timeField
        })
      })
    }
    if (newFilterFeeTypes && newFilterFeeTypes.length) {
      return callback()
    } else {
      return this.getVisibleFeeTypes(currentValue, specification.id).then(filterList => {
        if (filterList?.length) {
          this.setState({ filterFeeTypeIds: fnFilterFeetypes(filterList) })
          return callback()
        } else {
          return this.handleNoFeeTypeModal().then(filterList => {
            if (filterList?.length) {
              this.setState({ filterFeeTypeIds: fnFilterFeetypes(filterList) })
              return callback()
            }
          })
        }
      })
    }
  }

  handleCopyClick = ({ line, selectCurrencyDisable, foreignNumCode, field }) => {
    const { type = 'expense', bus, from, isModifyBill, flowAllowModifyFields, limitFieldRequireds } = this.props
    const {
      template,
      specification,
      applicationListDetails,
      disableInfo,
      supportCivilServiceCardFeeTypeIds,
      markInfo
    } = this.state
    //模版有冲销的时候费用明细可以填写负数
    const billType = type
    const { flowId } = this.props.state
    const { feeTypeId, feeTypeForm, specificationId } = line
    const currentFlowNode = this.getCurrentFlowNode()
    return bus.getValue().then(currentValue => {
      const submitterId = currentValue.submitterId
      if (flowId) {
        currentValue.flowId = flowId
      }
      return api.open('@feetype:FeeTypeInfoModal', {
        bus,
        billType,
        feetype: feeTypeId,
        flowId,
        submitterId,
        billTemplate: template,
        isModifyBill,
        flowAllowModifyFields,
        limitFieldRequireds,
        isPermitForm: from === 'iframePermit',
        billSpecification: specification,
        formData: currentValue,
        value: feeTypeForm,
        enterType: 'new',
        template: specificationId,
        selectCurrencyDisable,
        foreignNumCode,
        field,
        applicationListDetails,
        saveTempDisableInvoice: this.handleTempDisableInvoice,
        disableInfo,
        supportCivilServiceCardFeeTypeIds,
        valuation: this.valuation,
        markInfo,
        currentFlowNode,
        billState: get(this.props, 'state.data.state'),
        notShowModalIfAllInvoiceSuccess: true
      })
    })
  }

  handleNoFeeTypeModal = () => {
    return new Promise((resolve, reject) => {
      let { specification } = this.state
      let { name } = specification
      const allowCancelDependence = specification.components?.some(
        item => item.type === 'details' && item.allowCancelDependence
      )
      if (!FLAG.cancelLimit && allowCancelDependence) {
        Dialog.confirm({
          title: i18n.get('无法添加费用明细'),
          content: i18n.get('无可用的费用类型，单据已配置限制费用类型或取值规则，请联系企业管理员。', {
            __k0: name
          }),
          cancelText: i18n.get('确定'),
          onCancel: () => reject(),
          confirmText: i18n.get('取消限制'),
          onConfirm: async () => {
            FLAG.cancelLimit = true
            const currentValue = await this.props.bus.getValue()
            resolve(this.getVisibleFeeTypes(currentValue, specification.id))
          }
        })
      } else {
        Dialog.alert({
          title: i18n.get('无法添加费用明细'),
          content: i18n.get('无可用的费用类型，单据已配置限制费用类型或取值规则，请联系企业管理员。', {
            __k0: name
          }),
          confirmText: i18n.get('确定'),
          onConfirm: () => reject()
        })
      }
    })
  }

  handleTipsClick = data => {
    const {
      value: { submitterId }
    } = this.props
    showDetailsTips(data, submitterId.id)
  }

  handleExternalChange = (apportionId, detailId, field) => {
    let { external, bus, handleRiskNoticeChange } = this.props
    resetDetailsExternalsData({
      apportionId,
      detailId,
      field,
      external,
      bus,
      handleRiskNoticeChange
    })
  }

  handleFormExternalChange = args => {
    let { external, bus, handleRiskNoticeChange } = this.props
    resetFormExternalsData(args, external, bus, handleRiskNoticeChange)
  }

  handleTripsExternalChange = tripId => {
    let { external, bus, handleRiskNoticeChange } = this.props
    resetTripsExternalsData(tripId, external, bus, handleRiskNoticeChange)
  }

  handleFormDeleteDetaiExternal = detailIdList => {
    let { external, bus, handleRiskNoticeChange } = this.props
    deleteDetailItemExternalsData({ detailIdList, external, bus, handleRiskNoticeChange })
  }
  handleTempDisableInvoice = list => {
    let { disableInfo } = this.state
    Object.keys(list).forEach(id => {
      disableInfo[id].disable = true
    })
    this.setState({ disableInfo })
    api.emit('invoice:disable:list:change', list)
  }
  handleLineClick = ({
    line,
    idx,
    ds,
    isEdit,
    flowAllowModifyFields,
    isFrom,
    external,
    riskInfo,
    selectCurrencyDisable,
    foreignNumCode,
    field,
    showAllFeeType,
    cannotEditAmountField,
    apportionVisibleList,
    invoiceRiskData,
    cannotDelete
  }) => {
    let {
      type = 'expense',
      bus,
      value,
      from,
      isModifyBill,
      limitFieldRequireds,
      remunerationData,
      remunerationSetting: { formProperSetName },
      dataFromOrder
    } = this.props
    let {
      template,
      specification,
      applicationListDetails,
      disableInfo,
      supportCivilServiceCardFeeTypeIds,
      markInfo,
      timeField
    } = this.state
    let { flowId, data } = this.props.state
    let { feeTypeId, feeTypeForm, specificationId, enterType } = line
    let billType = type
    const ticketReview = isTicketReview(specification)
    const isRemuneration = checkIsRemuneration(specification.id || specification)
    const currentFlowNode = this.getCurrentFlowNode()
    if (isRemuneration) {
      // 处理历史数据有明细无批次号???
      // const hasBatchName= get(remunerationData, `form.${formProperSetName}`)
      // const hasDetails = get(remunerationData, 'form.details')
      const batchId = get(remunerationData, `form.${formProperSetName}.id`)
      // if(hasDetails && !hasBatchName){
      //   return toast.info(i18n.get('无酬金业务对象'))
      // }
      if (!batchId) {
        confirmRemuneration(bus, true)
        return false
      }
      return app.open('@bill:Remuneration', {
        isModifyBill: isModifyBill,
        type: 'editable',
        flow: remunerationData,
        bus: bus
      })
    }

    return bus.getValue().then(currentValue => {
      let submitterId = currentValue.submitterId
      let risks = filterResults(this.calculateResults, 'details')
      if (flowId) {
        currentValue.flowId = flowId
      }

      return api.open('@feetype:FeeTypeInfoModal', {
        idx,
        flowId,
        risks,
        isEdit,
        billType,
        feetype: feeTypeId,
        value: feeTypeForm,
        submitterId,
        ds,
        template: specificationId,
        billTemplate: template,
        isModifyBill,
        flowAllowModifyFields,
        limitFieldRequireds,
        enterType,
        isPermitForm: from === 'iframePermit',
        isFrom,
        external,
        riskInfo,
        shouldUpdate: this.getValue(feeTypeForm),
        detailId: feeTypeForm && feeTypeForm.detailId,
        handleExternalChangeDelete: this.handleExternalChange,
        billSpecification: specification,
        formData: currentValue,
        selectCurrencyDisable,
        foreignNumCode,
        field,
        showAllFeeType,
        cannotEditAmountField,
        apportionVisibleList,
        applicationListDetails,
        isTicketReview: ticketReview,
        originalValue: value,
        invoiceRiskData,
        billState: get(this.props, 'state.data.state'),
        cannotDelete,
        saveTempDisableInvoice: this.handleTempDisableInvoice,
        markInfo,
        disableInfo,
        supportCivilServiceCardFeeTypeIds,
        currentFlowNode,
        valuation: this.valuation,
        plan: data?.plan,
        dataFromOrder,
        notShowModalIfAllInvoiceSuccess: true,
        timeField
      })
    })
  }
  //获取当前审批节点，用来判断 无发票 是否适用
  getCurrentFlowNode = () => {
    const plan = get(this.props, 'state.data.plan')
    const { flowPlanConfigId, nodes, taskId } = plan ?? {}
    const node = nodes?.find(v => v.id === taskId)
    return node ? { flowPlanConfigId, nodeId: node?.configNodeId } : {}
  }
  getValue = feeTypeForm => {
    // 判断这个消费记录是否取过值了
    let id = feeTypeForm ? feeTypeForm.detailId : ''
    return id ? this.editedDetailList.indexOf(id) < 0 : false
  }

  handleSelectProperty = field => {
    let { bus, submitter } = this.props
    let name = field && field.dataType.entity
    let label = i18n.currentLocale === 'en-US' && field.enLable ? field.enLable : field.label || field.cnLabel
    let type = field && field.type
    let showBottom = field && field.optional
    let canSelectParent = 'all' === field.selectRange
    let { dependenceList, isDependence, multiple, selected, hideCode } = field
    const submitterId = get(this.submitter, 'id') || get(submitter, 'id')
    if (multiple) {
      name = get(field, 'dataType.elemType.entity')
    }
    let {
      isModifyBill,
      state: { flowId }
    } = this.props

    let param = isModifyBill ? { name, flowId } : { name }

    if (type === 'select_search' || type?.endsWith(':select_search')) {
      return api.open('@basic:SelectSearch', {
        label,
        type,
        showBottom,
        canSelectParent,
        field,
        bus,
        hasSearchBar: true,
        multiple,
        selected,
        hideCode,
      })
    } else {
      if (field?.allMatchList && !dependenceList?.length) {
        return api.open('@basic:SelectProperty', {
          submitterId,
          data: field.allMatchList,
          label,
          type,
          showBottom,
          canSelectParent,
          multiple,
          selected,
          hideCode,
        })
      }

      try {
        if (field?.dataType?.entity?.startsWith('basedata.Enum')) {
          return api.open('@basic:SelectEnumValue', {
            data: dependenceList,
            popupTitle: i18n.currentLocale === 'en-US' && field.enLable ? field.enLable : field.label || field.cnLabel,
            params: param
          })
        }
        showLoading()
        const promise = isDependence
          ? Promise.resolve({ items: dependenceList })
          : api.invokeService('@common:get:property:by:name', param)

        return promise.then(data => {
          hideLoading()
          return api.open('@basic:SelectProperty', {
            submitterId,
            data,
            label,
            type,
            showBottom,
            canSelectParent,
            multiple,
            selected,
            hideCode,
          })
        })
      } catch (_) {
        hideLoading()
      }
    }
  }

  handleSelectDepartment = ds => {
    if (useDepartmentVisible()) {
      return api.open('@basic:SelectDepartmentV3', ds)
    }
    const { submitter } = this.props
    const submitterId = get(this.submitter, 'id') || get(submitter, 'id')
    const canSelectParent = 'leaf' !== ds.selectRange
    return api.open('@basic:SelectDepartmentV2', {
      showBottom: ds.optional,
      canSelectParent,
      dataSource: ds.dataSource,
      isAsyn: ds.isAsyn,
      emptyText: ds.emptyText,
      isVisibilityDep: true,
      onlyBelongDepartment: ds.onlyBelongDepartment,
      submitter: this.submitter || submitter,
      submitterId: submitterId
    })
  }

  handleSelectPayee = (_, dependenceList, dependence, _isFeeDetailPayeeId, options) => {
    const { bus, state, isModifyBill } = this.props
    const { flowId } = state
    const { specification, template } = this.state
    const templateid = specification?.id
    const allowClear = template?.find(item => item.name === 'payeeId' && item.allowClear === true)?.allowClear || false
    // 修正点击收款信息页面重新刷新，在核销的情况支付金额显示不对的问题
    if (bus) {
      return bus.getValue().then(currentValue => {
        const selectedPayeeId = getV(currentValue, 'payeeId.id', '')
        const receivingCurrencyNumCode = getV(currentValue, 'payeeId.receivingCurrency', '')
        return api.open(
          getBoolVariation('new_version_of_payment_account') ? '@basic:SelectPayee' : '@basic:SelectPayeeOld',
          {
            allowClear,
            isModifyBill,
            flowId,
            selectedPayeeId,
            dependenceList,
            dependence,
            templateid,
            billSpecification: specification,
            filterRules: options?.filterRules,
            receivingCurrencyNumCode
          }
        )
      })
    }
    return api.open(
      getBoolVariation('new_version_of_payment_account') ? '@basic:SelectPayee' : '@basic:SelectPayeeOld',
      { isModifyBill, flowId, templateid, allowClear }
    )
  }

  handleSelectStaff = (whiteList, field) => {
    const { isVisibilityStaffs } = this.props
    return this.fnGetBillDate(field.valueRangeFilter).then(isByRule => {
      const { externalStaffMap } = this.props
      let isAllowExternalStaff = field.allowExternalStaff
      if (!isAllowExternalStaff) {
        isAllowExternalStaff = whiteList?.some(staffId => !!externalStaffMap[staffId]?.external)
      }
      return api
        .invokeService('@layout:select:staff', {
          whiteList,
          blacklist: [],
          showBottom: field.optional,
          dependence: field.dependence,
          isVisibilityStaffs,
          isByRule,
          isAllowExternalStaff,
          allowInteriorStaff: field.allowInteriorStaff ?? true
        })
        .then(data => {
          if (field.field === 'submitterId') {
            this.updateAutoCalFields(data, true, false)
          }
          this.handleLastChoice(data)
          return data
        })
    })
  }

  handleSelectStaffs = data => {
    const { isVisibilityStaffs } = this.props
    return this.fnGetBillDate(data.staffRangeRule).then(isByRule => {
      return api
        .invokeService('@layout:select:multiple:staff', { isVisibilityStaffs, ...data, isByRule })
        .then(data => {
          return data
        })
    })
  }
  fnGetBillDate = async ruleId => {
    const result = ruleId && ruleId !== 'false'
    if (!result) return result

    const { bus } = this.props
    const { specification } = this.state
    const billData = await bus.getValue().then(currentValue => {
      const formData = parseShowValue2SaveValue({ ...currentValue, specificationId: specification.id })
      return formData
    })
    return api.dataLoader('@common.staffRangeByRule').reload({ ...billData, ruleId })
  }
  fnGetDataSource = (applyList = []) => {
    let { value } = this.state
    const { dimensionCurrencyInfo } = this.props
    let data = []
    const dimentionCurrencyId = get(dimensionCurrencyInfo, 'dimension.id')
    const valueLegalEntityMultiCurrency = get(value, 'legalEntityMultiCurrency.id')
    const id = dimentionCurrencyId || valueLegalEntityMultiCurrency
    if (id) {
      data = applyList.filter(item => {
        const legalEntityMultiCurrency = get(item, 'flowId.form.legalEntityMultiCurrency')
        return id === legalEntityMultiCurrency
      })
    } else {
      data = applyList.filter(item => {
        const legalEntityMultiCurrency = get(item, 'flowId.form.legalEntityMultiCurrency')
        return !legalEntityMultiCurrency
      })
    }
    return data
  }

  handleSelectRequisition = param => {
    let { specification, value } = this.state
    let { submitterId = {} } = value
    const { userInfo, dimensionCurrencyInfo } = this.props
    submitterId = this.submitter || submitterId
    const legalEntityMultiCurrency = get(dimensionCurrencyInfo, 'dimension.id')
    const valueLegalEntityMultiCurrency = get(value, 'legalEntityMultiCurrency.id')
    //不想在多写一个一毛一样的函数 增加了判断是否为补充申请
    if (!param.linkRequisitionInfo) {
      //关联申请
      const queryParams = {
        id: specification.id,
        submitterId: submitterId.id,
        legalEntityMultiCurrency: legalEntityMultiCurrency || valueLegalEntityMultiCurrency,
        query: new QuerySelect().orderBy('flowId.form.submitDate', 'DESC').value()
      }
      // api.invokeService('@bill:get:requisition:byExpense', queryParams)
      api.invokeService('@bill:get:requisition:byExpenseOrder', queryParams)
      return api.open('@basic:SelectRequisition', { ...param, queryParams })
    } else {
      //补充申请,先根据当前的提交人id来获取补充申请列表-------应用场景委托申请情况下的补充申请，需要获取委托人的申请事项
      if (userInfo.staff.id === submitterId.id) {
        let { requisitionList = [] } = this.props
        const list = this.fnGetDataSource(requisitionList)
        api.invokeService('@requisition:get:requisition:data:source', list)
        return this.openSelectRequisitionLink(list, param)
      } else {
        // return api.invokeService('@requisition:get:requisition:list:by:submitterId', submitterId.id).then(value => {
        return api
          .invokeService('@requisition:get:requisition:list:by:submitterId:orderBy', submitterId.id)
          .then(value => {
            const requisitionList = (value && value.items) || []
            const list = this.fnGetDataSource(requisitionList)
            api.invokeService('@requisition:get:requisition:data:source', list)
            return this.openSelectRequisitionLink(list, param)
          })
      }
    }
  }

  openSelectRequisitionLink = (requisitionList, param) => {
    //筛选申请事项
    let requisitionArr = requisitionList.filter(v => {
      const requisitions = v.specification.configs.filter(v => v.ability === 'requisition')
      return requisitions[0].applyContentRule === param.applyContentRule
    })
    if (requisitionArr.length === 0) {
      return noSpecificationAlert(i18n.get('该申请单模版没有可用的申请事项，请联系系统管理员'))
    }
    param.requisitionList = requisitionArr
    param.updateRequisitionDataSource = this.updateRequisitionDataSource
    return api.open('@basic:SelectRequisitionLink', param)
  }

  updateRequisitionDataSource = (sortFieldName, sortType) => {
    const { value } = this.state
    const { userInfo } = this.props
    const { submitterId = {} } = value

    if (userInfo.staff.id === submitterId.id) {
      api.invokeService('@requisition:get:requisition:list:orderBy', '', sortFieldName, sortType).then(value => {
        const requisitionList = (value && value.items) || []
        const list = this.fnGetDataSource(requisitionList)
        api.invokeService('@requisition:get:requisition:data:source', list)
      })
    } else {
      api
        .invokeService(
          '@requisition:get:requisition:list:by:submitterId:orderBy',
          submitterId.id,
          sortFieldName,
          sortType
        )
        .then(value => {
          const requisitionList = (value && value.items) || []
          const list = this.fnGetDataSource(requisitionList)
          api.invokeService('@requisition:get:requisition:data:source', list)
        })
    }
  }

  handleExpenseLinkChange = id => {
    const { getExpenseLink, getCurrentRequisitionInfo } = this.props
    if (!id) return getCurrentRequisitionInfo([])
    getExpenseLink({ id }).then(action => {
      if (action.error) return
      const ids = action.payload?.items?.map(v => v.id) || []
      getCurrentRequisitionInfo(ids)
    })
  }

  handleSpecificationChange = async (specification, lsValue, changeTemplate) => {
    const { bus, value, globalFields, setValidateError, dimensionCurrencyInfo, expenseSpecAfterFiltered } = this.props
    const { specification: stateSpecification = {} } = this.state
    await this.clearHistoryCurrencyRates()

    if (stateSpecification.id !== specification.id) {
      this.updateImportList(specification.originalId)
      window.detailShowType = undefined
      setValidateError({ bill: [], detail: [], trip: [] })
      api.invokeService('@home:save:specification', specification)
    }
    const oldTmpIsRemuneration = checkIsRemuneration(stateSpecification.id)
    const isRemuneration = checkIsRemuneration(specification.id) //酬金申报明细必须显示，切换清空
    isRemuneration && fixRemunerationSpecification(specification)

    const template = parseAsMeta(specification, globalFields)
    const applyAbility = specification.configs.find(v => v.ability === 'apply')
    if (applyAbility?.canApply) {
      const valueLegalEntityMultiCurrency = value.legalEntityMultiCurrency?.id
      const legalEntityMultiCurrency = dimensionCurrencyInfo?.dimension?.id
      api.invokeService('@bill:get:requisition:byExpenseOrder', {
        id: specification.id,
        submitterId: value.submitterId.id,
        legalEntityMultiCurrency: valueLegalEntityMultiCurrency || legalEntityMultiCurrency,
        query: new QuerySelect().orderBy('createTime', 'DESC').value()
      })
    }

    // 红杉快速报销场景中单据模板变化时，清空明细
    if (expenseSpecAfterFiltered?.extendType === 'QuickExpense') {
      this.setState({ value: { ...value, details: [] } })
    }

    bus.emit('billSpecificationChanged', specification)
    this.getHiddenFields(specification)

    if (isRemuneration) {
      if (isRemuneration ^ oldTmpIsRemuneration) {
        delete value.details
        this.getRemunerationInitDetails({ value, specification, bus })
      }
      this.updateSpecificationValue(specification, value, template, false, changeTemplate)
    } else {
      if (lsValue) {
        if (isRemuneration ^ oldTmpIsRemuneration) {
          delete lsValue.details
        }
        this.updateSpecificationValue(specification, lsValue, template, true, changeTemplate)
      } else {
        bus.getValue().then(formValue => {
          if (isRemuneration ^ oldTmpIsRemuneration) {
            delete formValue.details
          }
          this.updateSpecificationValue(specification, formValue, template, false, changeTemplate)
          this.setState({ loanManualRepayment: [] })
        })
      }
    }
  }

  getRemunerationInitDetails = async params => {
    const { remunerationSetting } = this.props
    const { value, specification, bus } = params
    const { feeTypeId } = remunerationSetting
    let id = feeTypeId
    const { items } = await api.invokeService('@feetype:get:feetype:by:ids', id)
    const entityInfo = get(value, 'remunerationBatch')
    const batchId = get(entityInfo, 'id', entityInfo)
    const { flowId } = this.props.state
    if (flowId && entityInfo) {
      const val = await getRemunerationLedgerProcess({
        flowId: flowId,
        batchId: batchId
      })
      const { end, ledgerAmount } = val
      const details = this.generateDetailToForm({
        totalMoney: ledgerAmount,
        feeTypeId: items[0],
        specificationId: specification,
        isCalcEnd: end
      })
      bus.emit('detail:value:change', details)
      bus.setFieldsValue({ details })
    } else {
      const details = this.generateDetailToForm({
        totalMoney: standardValueMoney('0.00'),
        feeTypeId: items[0],
        specificationId: specification,
        isCalcEnd: true
      })
      bus.emit('detail:value:change', details)
      bus.setFieldsValue({ details })
    }
  }

  generateDetailToForm = params => {
    const { totalMoney, feeTypeId, specificationId, isCalcEnd } = params
    const fee = {
      feeTypeId: {
        ...feeTypeId,
        feeType: feeTypeId
      },
      feeTypeForm: {
        amount: !isCalcEnd ? setMoneytoZero(totalMoney) : totalMoney
      },
      specificationId,
      isRemuneration: true,
      idx: 0
    }
    return [fee]
  }

  setMoneytoZero = (totalMoney = { standard: 0 }) => {
    const obj = cloneDeep(totalMoney)
    obj.standard = 0
    return obj
  }

  updateSpecificationValue = (specification, value, template, fromLS = false, changeTemplate) => {
    const { bus, external, handleRiskNoticeChange, from, AutoGenerateFeeDetail } = this.props
    const { specification: stateSpecification = {} } = this.state
    checkValue({ oldTemplate: stateSpecification, newTemplate: specification, value }).then(formValue => {
      this.getVisibleFeeTypes(formValue, specification.id).then(filterList => {
        // 易商卡授权切换模版的时候清空授权
        if (formValue?.corporateExpenseCardForm?.id) {
          delete formValue.corporateExpenseCardForm
        }
        let filterFeeTypeIds = fnFilterFeetypes(filterList)
        if (formValue.linkRequisitionInfo) {
          //清空不同申请单类型的申请事项
          var oldRequisition = null
          //已保存单据的取值方式
          if (typeof get(formValue.linkRequisitionInfo, 'specificationId') === 'object') {
            oldRequisition = formValue.linkRequisitionInfo.specificationId.configs.filter(
              v => v.ability === 'requisition'
            )[0].applyContentRule
          }
          //新建单据的取值方式
          if (typeof get(formValue.linkRequisitionInfo, 'specification') === 'object') {
            oldRequisition = formValue.linkRequisitionInfo.specification.configs.filter(
              v => v.ability === 'requisition'
            )[0].applyContentRule
          }
          var newRequisition = get(
            get(specification, 'configs', []).filter(v => v.ability === 'requisition')[0],
            'applyContentRule'
          )
          if (oldRequisition !== newRequisition) {
            formValue.linkRequisitionInfo = null
          }
        }
        let value = formatNewTemplateValue(formValue, template, stateSpecification)
        if (changeTemplate) {
          value = formatExpenseLinkInfo(value, specification)
        }
        // 查询单据模板上所有固定值并重新赋值
        template.forEach(tmp => {
          if (constantValue(tmp)) {
            value[tmp.field] = constantValue(tmp)
          }
        })
        value.specificationId = specification
        ////切换模板的时候更新localStorage
        let { flowId = getBillKey() } = this.props.state
        const allowReceivingCurrency = getAllowSelectionReceivingCurrency(specification.configs, value)
        console.log(flowId, '[ flowId localStorage ls ] >', value)
        localStorageSet(flowId, JSON.stringify(value))

        //   // 关闭收款币种
        // if (!allowReceivingCurrency) {
        //   if(value?.payeeId?.receivingCurrency){
        //     delete value.payeeId.receivingCurrency
        //   }
        //   value?.details?.map(fee => {
        //     const receivingAmount = get(fee, 'feeTypeForm.receivingAmount', null)
        //     const feeTem = get(fee, 'specificationId.components', [])

        //     if (receivingAmount) {
        //       // 删除模板收款字段
        //       deleteReceivingTemplate(feeTem) // 删除模板
        //       delete fee.feeTypeForm.receivingAmount // 收款金额
        //     }
        //   })
        // }

        //按票审阅-》非按票审阅 清空核发金额
        if (!isTicketReview(specification)) {
          value.details?.map(fee => {
            const invoices = get(fee, 'feeTypeForm.invoiceForm.invoices', [])
            invoices?.map(item => {
              delete item.approveAmount
              delete item.comment
            })
          })
        }
        const oldIsEBussCard = stateSpecification.configs.find(item => item.isEBussCard)?.isEBussCard
        const newIsEBussCard = specification.configs.find(item => item.isEBussCard)?.isEBussCard
        if (oldIsEBussCard !== newIsEBussCard) {
          value?.details && bus.invoke('details:move:recordexpends', value.details)
          delete value.details
          bus.emit('template:change', undefined)
        }
        // 申请事项补充申请时,切换单据模板
        if (from === 'supplyReq') {
          let linkRequisitionInfoComponent = template.find(v => v.field === 'linkRequisitionInfo')
          let legalEntityMultiCurrency = template.find(v => v.field === 'legalEntityMultiCurrency')
          if (linkRequisitionInfoComponent) {
            linkRequisitionInfoComponent.editable = true
          }
          if (legalEntityMultiCurrency) {
            legalEntityMultiCurrency.editable = true
          }
        }
        const preSpecUseTimeField = !!this.state.timeField
        this.setState({ template: null }, async () => {
          const timeField = await this.setTimeField(specification)
          this.setState({ specification, template, value, filterFeeTypeIds, timeField }, () => {

            const travelPlanning = value.travelPlanning || []
            const hadScene = travelPlanning.filter(v => v.sceneList?.length)?.length
            const tripDataLink = value?.['u_行程规划']
            const hadTripDataLinkScene = tripDataLink?.filter(v => {
              const dataLinkForm = v?.dataLinkForm || {}
              const key = Object.keys(dataLinkForm).find(key => key?.includes('_场景'))
              return dataLinkForm[key]
            })?.length
            if (hadScene || hadTripDataLinkScene) {
              toast.info('申请单模板已切换，请重新填写行程中的场景字段')
            }
            this.fnInitPayeeInfo(fromLS, true)
            this.updateAutoCalFields(undefined, false, false)
            if (external) {
              bus.setFieldsExternalsData?.({ ...external.form })
              handleRiskNoticeChange(external, template)
            }
            // 检查新模版是否有汇总取值的字段
            const hasSumField = checkSpecificationSumField(template)
            if (hasSumField && !!value?.details?.length) {
              bus.emit('detail:value:change', value.details)
            }
            if (timeField) {
              this.handleChangeTimeField()
            } else if (preSpecUseTimeField) {
              this.checkDetailsRates()
            }
          })
        })
        api?.logger?.info?.('切换模板', {
          specificationId: specification?.id,
          specificationName: specification?.name,
          specificationOld: stateSpecification,
          specificationNew: template,
          flowId: flowId,
          formOld: formValue,
          formNew: value
        })
        this.fnCheckDelegater(value.submitterId, specification, true)
      })
    })
    //自动生成费用明细
    if (AutoGenerateFeeDetail) {
      setTimeout(() => {
        this.getAutoGenerationFeeRules(specification, null, '')
      }, 2000)
    }
    this._initAutoAssignOfOneResultOfDataLink({ template, value })
  }

  fnCheckDelegater = async (submitterId, currentSpecification, isChange = false) => {
    const { userInfo, bus, tags } = this.props
    const delegatorList = tags?.submitterId?.delegatorObjectList
    if (submitterId.id === userInfo.staff.id) return
    //非本账号提交人，切换模板就清空申请单选中的数据项
    if (isChange && submitterId.id !== userInfo.staff.id) {
      bus.setFieldsValue({ expenseLink: null, expenseLinks: null, linkRequisitionInfo: null })
    }
    if (!delegatorList[submitterId.id]) {
      toast.info(i18n.get('模板不支持当前委托关系，将变更提交人'))
      api.invokeService('@bill:set:submitter:data', userInfo)
      this.fnClearPayeeId(userInfo.staff.id)
      this.updateAutoCalFields(userInfo.staff, true, false)
      this.handleLastChoice(userInfo.staff)
      bus.emit('on:submitterId:change', userInfo.staff, submitterId)
    }
  }

  fnClearPayeeId = async submitterId => {
    const { bus, billSpecification } = this.props
    const result = await bus.getValue()
    const multiplePayeesMode = get(result, 'payeeId.multiplePayeesMode') || get(result, 'multiplePayeesMode')
    const formType = billSpecification?.type
    if (multiplePayeesMode) {
      let details = result?.details || []
      details = details.map(v => {
        if (v?.feeTypeForm?.feeDetailPayeeId) {
          v.feeTypeForm.feeDetailPayeeId = null
        }
        return v
      })
      bus.setFieldsValue({ details })
      bus.emit('clear:payPlan:value')
    } else {
      const res = await api.invokeService('@bill:get:default:payee', {
        formType,
        submitterId
      })
      bus.setFieldsValue({ payeeId: res?.value || undefined })
    }
  }

  handleSelectCity = data => {
    const { bus } = this.props
    return bus.getValue().then(value => {
      const travelers = value.travelers || []
      let travelerId = undefined
      if (travelers.length === 1) {
        travelerId = travelers[0]
      } else if (travelers.length === 0) {
        travelerId = value.submitterId.id
      }
      return api.open('@basic:CitySelector', {
        ...data,
        bus,
        travelerId,
        isMultipleTraveler: travelers.length > 1 ? true : false
      })
    })
  }

  // 监听法人实体多币种切换
  _handleDimensionCurrencyChange = (params, isCurrencyChange) => {
    const { canUseDefaultCurrency, state = {} } = this.props

    const { flowId } = this.state
    const { data = {} } = state
    canUseDefaultCurrency && !data?.state && !flowId && handleDimensionCurrencyChange.call(this, params)

    // 根据时间字段获取对应版本的汇率
    this.handleChangeTimeField(params?.currency?.numCode)
    this.showLegalEntityChangedModal(isCurrencyChange)
  }

  showLegalEntityChangedModal = async(isCurrencyChange)=>{
    const { details, legalEntityMultiCurrency } = await this.props.bus.getValue()
    // 币种不一致
    if(legalEntityMultiCurrency && details?.length && enableOtherInvoiceByDimension() && isCurrencyChange){      
      const isHasOtherInvoice = details.some(item =>
        item.feeTypeForm?.invoiceForm?.invoices?.some(invoice =>
          !['system_发票主体', 'system_海外票据'].includes(invoice?.master?.entityId)
        )
      )
    // 法人实体更新一次触发多次场景，2秒内只弹一次
    const now = Date.now(); 
    if(isHasOtherInvoice && now - lastLegalEntityChangedAlertTime > 2000){
        lastLegalEntityChangedAlertTime = now;
        Dialog.alert({
          content: i18n.get('法人实体已变更，请到费用明细中的发票编辑页面重新保存发票。')
        })
      }
    }
  }

  // 修改币种类型时，设置所有币种
  _handlerCurrencyMoneySelectChange = parms => {
    const { canUseDefaultCurrency, state = {} } = this.props
    const { flowId } = this.state
    const { data = {} } = state
    canUseDefaultCurrency && !data?.state && !flowId && handlerCurrencyMoneySelectChange.call(this, parms, 'billInfo')
  }

  refresh = () => {
    this.setState(prev => ({
      isRefresh: !prev.isRefresh
    }))
  }

  reportPerformanceTime = once(() => {
    const { flowId } = this.props.state
    const { specification = {} } = this.state
    endOpenFlowPerformanceStatistics({ flowId, specification })
  })

  render() {
    let {
      bus,
      tags,
      writtenOff,
      submitter,
      type,
      globalFields,
      standardCurrency,
      isModifyBill,
      flowAllowModifyFields,
      limitFieldRequireds,
      lastChoice,
      specification_current,
      notScroll = false,
      defaultPayee,
      validateError = {},
      invoiceRiskData,
      dataState = '',
      from,
      dataFromOrder,
      isCopyBill
    } = this.props
    let {
      specification = specification_current,
      hiddenFields,
      template,
      value,
      isContinue,
      errorMessages,
      calFields,
      customizeQueryRule,
      code,
      showAllFeeType,
      filterFeeTypeIds,
      feeTypeVisibleObjForModify,
      canRenderMap,
      isRefresh,
      riskInfo,
      loanManualRepayment,
      timeField
    } = this.state
    let { flowId, data } = this.props.state

    if (!specification || !template || !canRenderMap?.lastChoice) {
      return <SkeletonComponent showHead={true} bodyLength={2} length={3} />
    }
    this.reportPerformanceTime()
    const flowState = getNodeValueByPath(data, 'state', dataState)

    //此处很重要的,需要拿到 SpecificationId 在 ActionsPart
    bus.$extraGetSpecification = () => specification
    bus.$currentTemplate = () => template

    tags = {
      ...tags,
      details: {
        initValue: this.props.value?.details, // 为了滴滴订单去重使用，需要一份detail的初始值
        filterFeeTypeIds
      },
      trips: { isEdit: true }
    }

    // 修改单据不可以改联系人
    const submitterComponent = template.find(v => v.name === 'submitterId')
    if (submitterComponent && isModifyBill) {
      submitterComponent.editable = false
    }

    // 核销金额要减去公司支付部分
    let writtenOffAmount
    if (value?.details) {
      const { companyPayMoney, payDetail } = getDetailCalculateMoney(value.details, type)
      writtenOffAmount = new MoneyMath(payDetail.value).minus(companyPayMoney).value
    }

    const { submitterId = {}, payeeId } = value ?? {}
    const { multiplePayeesMode, payPlanMode, payeePayPlan } = payeeId || value
    const { state = 'new', logs, ownerId, form } = data || {}
    let canChangeTemplate
    if (specification?.type === 'contractWrite' && (state === 'draft' || state === 'rejected')) {
      canChangeTemplate = false
    } else {
      canChangeTemplate = state === 'draft' ? true : canChangeTemplateFn(data)
    }
    if (specification?.type === 'corpPayment') {
      canChangeTemplate = false
    }
    const canEditNote = state !== 'draft' && state !== 'rejected'
    const isNeedWrittenoff = api.invokeService('@writtenoff:isneedwrittenoff', specification)
    const isReceiptTemplate = specification?.type === 'receipt'
    const alterFlag = form?.alterFlag >= '1'

    const replayAbility = specification.configs.filter(item => {
      return item.ability === 'allowRepayment'
    })
    const isAllowedRepayment = (replayAbility.length && replayAbility[0].isAllowedRepayment) || false
    const isAllowedRepaymentForeign = (replayAbility.length && replayAbility[0].isAllowedRepaymentForeign) || false
    const isForbiddenSubmit = (replayAbility.length && replayAbility[0].isForbiddenSubmit) || 'Y'

    // 支付金额不为0时收款信息必填, 此时收款账户选择后可清除选项
    const pay = specification.configs.find(v => v.ability === 'pay')
    template.forEach(item => {
      if (item.name === 'payeeId' && pay && pay?.optionalPayeeByZero) {
        item.optionalPayeeByZero = true
        item.optional = true
        item.allowClear = true
      }
    })

    const { current_flow } = this.props
    const { taskId, nodes = [] } = current_flow?.plan || {}
    const currentNode = nodes.find(node => node.id === taskId)

    return (
      <div
        id="bill-info-editable"
        className={classNames(styles['bill-info-editable'], {
          'highlight-modifiable-field': currentNode?.config?.highlightModifiableField || false,
          'h-100-percent-c': notScroll,
          refresh: isRefresh
        })}
      >
        <div className={classNames({ 'inertial-rolling h-100-percent': !notScroll })}>
          {this.props.type !== 'permit' && (
            <SelectSpecification
              value={specification}
              disabled={isModifyBill}
              bus={bus}
              onChange={this.handleSpecificationChange}
              canChangeTemplate={canChangeTemplate}
              alterFlag={alterFlag}
            />
          )}
          <Dynamic
            className="bill-info-editable-form"
            bus={bus}
            hiddenFields={hiddenFields}
            validateError={validateError.bill}
            errorMessages={errorMessages}
            defaultPayeeInfo={defaultPayee}
            submitter={this.submitter || submitter}
            billSpecification={specification}
            elements={editable}
            create={create}
            template={template}
            value={value}
            calFields={calFields}
            customizeQueryRule={customizeQueryRule}
            standardCurrency={standardCurrency}
            globalFields={globalFields}
            tags={tags}
            logs={logs}
            riskInfo={riskInfo}
            isModifyBill={isModifyBill}
            flowAllowModifyFields={flowAllowModifyFields}
            limitFieldRequireds={limitFieldRequireds}
            flowId={flowId}
            flowState={flowState}
            lastChoice={lastChoice}
            writtenOff={writtenOff}
            multiplePayeesMode={multiplePayeesMode}
            payPlanMode={payPlanMode}
            payeePayPlan={payeePayPlan}
            filterFeeTypeIds={this.state.filterFeeTypeIds}
            showAllFeeType={showAllFeeType}
            feeTypeVisibleObjForModify={feeTypeVisibleObjForModify}
            originalValue={this.props.value}
            invoiceRiskData={invoiceRiskData}
            alterFlag={alterFlag}
            isPermitForm={from === 'iframePermit'}
            isCopyBill={isCopyBill}
            billState={state}
            plan={data?.plan}
            notShowModalIfAllInvoiceSuccess={true}
            dataFromOrder={dataFromOrder}
            billFeeForceValidation={this.state.billFeeForceValidation}
            timeField={timeField}
            receivingCurrency={form?.receivingCurrency}
            businessType={'FLOW'}
          />
          {(isNeedWrittenoff || isReceiptTemplate) && (
            <Container
              name="@writtenoff:WrittenOffWrapper"
              style={{ marginTop: 20 }}
              value={{ writtenOff, writtenOffAmount }}
              bus={bus}
              isEdit
              multiplePayeesMode={multiplePayeesMode}
              specification={specification}
              submitterId={submitterId}
              standardCurrency={standardCurrency}
              isModifyBill={isModifyBill}
              flowId={flowId}
              expenseLink={value.expenseLink || value.expenseLinks}
              details={value.details}
              isReceiptTemplate={isReceiptTemplate}
            />
          )}

          {isAllowedRepayment && (
            <ManualRepayment
              specification={specification}
              bus={bus}
              eidtable={isModifyBill ? false : true}
              flowId={flowId}
              isAllowedRepaymentForeign={isAllowedRepaymentForeign}
              defaultLoanManualRepayment={loanManualRepayment}
              isForbiddenSubmit={isForbiddenSubmit == 'Y' ? true : false}
              value={{ writtenOff, writtenOffAmount }}
            />
          )}

          <BillPayPlan bus={bus} value={value} billSpecification={specification} />

          <MoneySummaryEditable
            specification={specification}
            bus={bus}
            formType={type}
            isContinue={isContinue}
            value={value}
            writtenOffRecords={(isNeedWrittenoff && writtenOff && writtenOff.records) || []} //单据模板上允许使用核销才显示
            isReceiptTemplate={isReceiptTemplate}
          />

          <CodeLabel
            prefix={i18n.get('单号#')}
            code={code}
            auto={data?.form?.systemGeneration || data?.form?.subsidyGeneration === 'surplus'}
            alterFlag={alterFlag}
          />

          <DisplayConfig specification={specification} refresh={this.refresh} />

          {flowId && (
            <CreditPointLabel submitter={submitterId} ownerId={ownerId} canEditNote={canEditNote} flowId={flowId} />
          )}

          <div style={{ height: 80 }} />
        </div>
      </div>
    )
  }

  $startAutoCalc2RequestTime = Date.now()

  async startAutoCalc2(value) {
    let data = cloneDeep(value)
    await new Promise(r => setTimeout(r, 300))
    const requestTime = Date.now()
    this.$startAutoCalc2RequestTime = requestTime
    const { globalFields, baseDataPropertiesMap, bus } = this.props
    let {
      value: { submitterId, specificationId }
    } = this.state

    const formValue = cloneDeep(await bus.getValue?.())
    data = formValue?.details ?? data

    if (!specificationId) {
      specificationId = this.state.specification
    }

    const billData = {
      ...formValue,
      submitterId,
      specificationId
    }
    const systemCalc = async detail => {
      const { feeTypeForm, feeTypeId: feeType, specificationId: specification } = detail
      const _billData = cloneDeep(billData)
      const formData = {
        feeTypeForm,
        specificationId: specification.id,
        feeTypeId: feeType.id
      }
      _billData.details = [formData]

      const template = parseAsMeta(specification, globalFields)
      return AutoCalculate2.getAutoCal1ResultValue(
        formData,
        specificationId,
        specification,
        globalFields,
        baseDataPropertiesMap,
        template,
        _billData
      )
    }
    try {
      bus.emit('bill:loading:change', true)
      // 计算接口
      const calcResult = await AutoCalculate2.calc(billData)
      const effectDetailIdList = uniq(calcResult.map(item => item.detailId))
      if (effectDetailIdList.length === 0) return
        ; (data || []).forEach(detail => {
          const rules = calcResult.filter(v => v.detailId === detail.feeTypeForm.detailId)
          // 由于只有数字型的赋值，不需要考虑其他情况
          rules.forEach(({ fieldName, fieldValue }) => {
            detail.feeTypeForm[fieldName] = fieldValue.toString()
          })
        })

      // 重新系统计算
      const effectDetailList = billData.details.filter(v => effectDetailIdList.includes(v.feeTypeForm.detailId))

      const calc1ActionList = await Promise.all(
        effectDetailList.map(async detail => {
          const result = await systemCalc(detail)
          return {
            detail,
            result
          }
        })
      )
      const resultBillData = await bus.getValue()
      if (requestTime !== this.$startAutoCalc2RequestTime) return console.log('skip calc 2')
      // 赋值
      const calc1ActionMap = calc1ActionList.reduce(
        (result, { detail, result: v }) => ({ ...result, [detail.feeTypeForm.detailId]: v }),
        {}
      )
      resultBillData.details.forEach(detail => {
        const rules = calcResult.filter(v => v.detailId === detail.feeTypeForm.detailId)
        const calc1Result = calc1ActionMap[detail.feeTypeForm.detailId]
        // 由于只有数字型的赋值，不需要考虑其他情况
        rules.forEach(({ detailId, fieldName, fieldValue }) => {
          if (detail.feeTypeForm.detailId == detailId) {
            detail.feeTypeForm[fieldName] = fieldValue.toString()
          }
        })

        if (calc1Result) {
          // 系统计算赋值
          Object.keys(calc1Result).forEach(key => {
            detail.feeTypeForm[key] = calc1Result[key]
          })
        }
      })

      bus.setFieldsValue({
        details: resultBillData.details
      })
    } finally {
      bus.emit('bill:loading:change', false)
    }
  }
}
