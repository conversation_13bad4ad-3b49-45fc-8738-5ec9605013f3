import React from 'react'
import styles from './SkeletonModal.module.less'
import classNames from 'classnames'
import { SkeletonIconNav, SkeletonNormalList, SkeletonTextList } from '@hose/eui-mobile'

interface SkeletonProps {
  showHead?: boolean
  showNav?: boolean
  bodyLength?: number
  length?: number
}

const SkeletonComponent: React.FC<SkeletonProps> = ({ showHead = false, bodyLength = 2, length = 3, showNav=false }) => {
  return (
    <div className={classNames(styles['skeleton-modal'], 'scrollable', 'inertial-rolling', 'w-100p')}>
        <div className="head">
          {showHead && (<SkeletonNormalList showText showTitle animated/>)}
          {showNav && (
             <SkeletonIconNav twoLineIcon animated/>
          )}
        </div>
      {Array.from({ length: bodyLength }, (_, outerIndex) => (
        <div className="body" key={`body-${outerIndex}`}>
          {Array.from({ length: length }, (_, innerIndex) => (
            <SkeletonTextList
              style={{ paddingBottom: 16 }}
              key={`skeleton-${outerIndex}-${innerIndex}`}
              showText
              showTitle
              animated
            />
          ))}
        </div>
      ))}
    </div>
  )
}

export default SkeletonComponent
