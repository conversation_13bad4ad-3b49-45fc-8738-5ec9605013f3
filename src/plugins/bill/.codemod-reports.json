{"RES_ASYNC": ["elements/puppet/onceTip/OnceTip", "elements/ekbIcon", "elements/puppet/Money", "elements/listview/ListViewWrapper", "elements/ETabs", "elements/ActionsPanel", "elements/puppet/Approve/BillApprove", "elements/puppet/Express/BillExpress", "elements/puppet/RiskNotice", "elements/puppet/Remuneration", "elements/puppet/Remuneration/tab", "elements/puppet/Upload/UploadItem", "elements/puppet/Animation/WaitAnimation", "elements/puppet/<PERSON>ti<PERSON>taff/<PERSON>ti<PERSON>taff", "elements/puppet/Remuneration/Detail", "elements/ekbIcon", "elements/puppet/FakeInput", "ekb-components/base/EKBSearchBar", "elements/form/input-wrapper", "elements/puppet/CodeLabel", "elements/puppet/SelectSpecification", "elements/puppet/MoneySummary.editable", "elements/puppet/MoneySummary.readonly", "elements/authRiskTip", "ekb-components/base/EKBSearchBar", "elements/puppet/Money", "elements/ekbIcon"], "RES_SYNC": ["elements/puppet/Remuneration/actions", "components/utils/fnThousandBitSeparator", "components/dynamic/index.readonly.datalink.lite", "components/utils/validatorUtil", "components/utils/fnDataLinkUtil", "elements/enhance/enhance-form-create", "components/utils/Related", "elements/ActionsPanel.module.less", "components/utils/fnAttachment", "components/utils/fnNumberkeyboard", "elements/approve-log/card-log-item", "components/utils/fnInitalValue", "components/utils/fnDimension", "components/utils/getInitialValue", "elements/puppet/ThirdCard/fnOpenDetails", "elements/puppet/invoice/PopupInvoiceUnify", "components/dynamic/index.editable", "components/dynamic/index.readonly", "components/utils/Related"], "RES_IMG": ["images/exclamation-circle.svg", "images/avatar-null.svg", "images/avatar.svg", "images/change.svg", "images/save.svg", "images/avatar.svg"]}