<svg width="86" height="86" viewBox="0 0 86 86" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="43" cy="43" r="41.5" fill="#30B4C2" stroke="#C6E9ED" stroke-width="3"/>
<path d="M39.5 61.5L64.5 36.5L57 29L32 54L39.5 61.5Z" fill="white"/>
<g filter="url(#filter0_d)">
<path d="M47 54L31 38L23.5 45.5L39.5 61.5L47 54Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d" x="23.5" y="36" width="25.5" height="25.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dx="1" dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.439687 0 0 0 0 0.799354 0 0 0 0 0.8375 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
