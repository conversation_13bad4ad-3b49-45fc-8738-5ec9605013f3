<?xml version="1.0" encoding="UTF-8"?>
<svg width="56px" height="16px" viewBox="0 0 56 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>机票icon</title>
    <g id="4·数据展示" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="4·数据展示/Card-卡片/机票" transform="translate(-127.000000, -57.000000)" fill="#1D2B3D">
            <g id="机票icon" transform="translate(127.000000, 57.000000)">
                <g id="1·通用/2·Icon-图标/travel/take-off" transform="translate(20.000000, 0.000000)" fill-opacity="0.5">
                    <path d="M14.6374908,6.12633124 C14.6990842,6.40142066 14.6631551,6.65534671 14.5297015,6.88811546 C14.39625,7.12088422 14.1960727,7.27958724 13.9291676,7.36423061 C11.3832979,8.06253688 9.26861484,8.64444861 7.5850555,9.10998613 L4.10503282,10.0622164 L3.05794634,10.3796265 L1.33333333,7.3324896 L2.28802982,7.07856152 L3.58148958,8.09427382 L6.84593564,7.20552556 L4.13582948,2.3491511 L5.39849257,2 L9.92560173,6.3485183 L13.4364211,5.39628802 C13.7033261,5.31164465 13.9548299,5.34338566 14.1909402,5.49151105 C14.4270486,5.63963643 14.5758975,5.85124181 14.6374908,6.12633124 L14.6374908,6.12633124 Z M1.33333333,12 L14.6666667,12 L14.6666667,13.3333333 L1.33333333,13.3333333 L1.33333333,12 Z" id="Page-1"></path>
                </g>
                <circle id="椭圆形" fill-opacity="0.15" cx="44" cy="8" r="2"></circle>
                <circle id="椭圆形" fill-opacity="0.15" cx="2" cy="8" r="2"></circle>
                <circle id="椭圆形" fill-opacity="0.15" cx="54" cy="8" r="2"></circle>
                <circle id="椭圆形" fill-opacity="0.15" cx="12" cy="8" r="2"></circle>
            </g>
        </g>
    </g>
</svg>