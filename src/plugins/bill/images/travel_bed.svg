<?xml version="1.0" encoding="UTF-8"?>
<svg width="56px" height="16px" viewBox="0 0 56 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>酒店icon</title>
    <g id="4·数据展示" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="4·数据展示/Card-卡片/酒店" transform="translate(-127.000000, -57.000000)" fill="#1D2B3D">
            <g id="酒店icon" transform="translate(127.000000, 57.000000)">
                <g id="1·通用/2·Icon-图标/travel/bed" transform="translate(20.000000, 0.000000)" fill-opacity="0.5">
                    <path d="M14.6666667,13.3333333 L13.3333333,13.3333333 L13.3333333,10.6666667 L2.66633333,10.6666667 L2.66666667,13.3333333 L1.33333333,13.3333333 L1.33333333,8.66666667 L13.3333333,8.66666667 L13.3333333,2.66666667 L14.6666667,2.66666667 L14.6666667,13.3333333 Z M12,6 L12,7.33333333 L2.66666667,7.33333333 L2.66666667,6 L12,6 Z" id="形状结合"></path>
                </g>
                <circle id="椭圆形" fill-opacity="0.15" cx="44" cy="8" r="2"></circle>
                <circle id="椭圆形" fill-opacity="0.15" cx="2" cy="8" r="2"></circle>
                <circle id="椭圆形" fill-opacity="0.15" cx="54" cy="8" r="2"></circle>
                <circle id="椭圆形" fill-opacity="0.15" cx="12" cy="8" r="2"></circle>
            </g>
        </g>
    </g>
</svg>