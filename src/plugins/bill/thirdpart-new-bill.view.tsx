/**
 *  Created by pw on 2021/1/5 下午4:56.
 */
import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { getBillKey, showLoading, hideLoading, toast } from '../../lib/util'

interface PathParams {
  specificationOriginalId: string
}

interface Props {
  params: PathParams
}

export default class extends Component<Props> {
  constructor(props: Props) {
    super(props)
    showLoading(i18n.get('加载中...'))
  }

  componentDidMount() {
    api.invokeService('@common:list:departments')
    const { params } = this.props
    // 从第三方入口进入的要把缓存清除
    localStorage.removeItem(getBillKey())
    api.invokeService('@layout:change:header:title', i18n.get('新建单据'))
    api
      .dataLoader('@home.specificationWithVersion')
      .reload()
      .then((result: any) => {
        const { specification_map = {} } = result
        const { specificationOriginalId } = params
        const specification = specification_map[specificationOriginalId]
        if (specification) {
          api.invokeService('@home:save:specification', specification).then(() => {
            api
              .invokeService('@home:save:specification:after:filtered', {
                type: 'filterFormType',
                formType: specification.type,
                specifications: []
              })
              .then(() => {
                api.go(`/bill/${specification.type}/from/thirdpartnew`)
              })
          })
        } else {
          hideLoading()
          toast.error(i18n.get('您无此单据模板权限'))
        }
      })
  }

  componentWillUnmount() {
    hideLoading()
  }

  render() {
    return <div />
  }
}
