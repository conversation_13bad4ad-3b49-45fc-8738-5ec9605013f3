/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-08-26 11:49:24
 * @Description  : 单据主体
 * 描述功能/使用范围/注意事项
 */

import { EnhanceConnect} from '@ekuaibao/store'
import React, { Component, useState } from 'react'
import { Avatar, Space, Button } from '@hose/eui-mobile'
import { OutlinedGeneralTextfield, OutlinedGeneralLoading, IllustrationSmallLoadFailed, IllustrationSmallNoContent } from '@hose/eui-icons'
import { HistoryProps, HistoryDataProps, FieldProps, HistoryApiDataProps, StatusType } from './types'
import { HistoryApi } from './api'
import style from './index.module.less'
import Details from './element/details'
import moment from 'moment'
import FieldDetailsTable from './element/FieldDetails'
import { emptyString, renderComponentByType, OtherSpecialFields } from './element/components'
import MessageCenter from '@ekuaibao/messagecenter/esm/index'
import _flatten from 'lodash/flatten'

const List =(props)=>{
  const { data } = props
  return <>
        {
          data.map((v:FieldProps)=>
          {
            const otherFiled = OtherSpecialFields(v?.type)
            const dataLinkEdits = ['dataLinkEdits','payPlan'].includes(v.type)
            if(dataLinkEdits && !v.isMulDataLink){
              return <FieldDetailsTable data = {{...v,title:v.label}} isBack = {false}/>
            }else{
              return (
                <div className="list" key={v.field}>
                  <div className="title"><OutlinedGeneralTextfield className="mr-8" />{v.label}</div>
                  { otherFiled ? <div className="caption">{i18n.get('最新版本请在单据详情上查看')}</div>: 
                      <>
                        <div className="before">
                          <label>{i18n.get('修改前：')}</label>
                          <span className="des">
                              { renderComponentByType(v.oldValue,v) ?? <span className="empty">{emptyString(v,true)}</span> }
                          </span>
                      </div>
                      <div className="after">
                          <label>{i18n.get('修改后：')}</label>
                          <div className="des dis-f">
                              {
                                renderComponentByType(v.newValue,v) ?? <span className="empty">{emptyString(v,false)}</span>
                              }
                          </div>
                      </div>
                      </>
                  }
                </div>
              )
            }
          }
        )
    }
  </>
}


// 单据主体内容
const Main = ({ billBeforeDetails, total, detailsArray, bus, dataSource })=>{
    return (<div>
            <Space align="center" className="dis-f jc-sb">
               <div className="user-info-warp">
                    <Avatar size="mini" />
                    <span className="name text-nowrap-ellipsis">{dataSource?.modifyStaffId?.name}</span>
                    <span className="ml-4">修改了{total}处内容</span>
               </div>
               <div className="date">{moment(dataSource?.modifyDateTime).format('YYYY-MM-DD HH:mm')}</div>
            </Space>
            <List data={billBeforeDetails} />
            <Details data={detailsArray} bus={bus} />
        </div>
    )
}

interface HistoryState {
    loading:boolean,
    tryAgain:boolean
    detailsArray:FieldProps[],
    billBeforeDetails:FieldProps[],
    total:number
    showBillMain:boolean,
    otherFiledTable:any,
    dataSource:HistoryApiDataProps,
    emptyData:boolean
}

@EnhanceConnect()

  export default class History extends Component<HistoryProps, HistoryState> {
      static defaultProps = {
        flowVersionedId:[]
      }
      state: HistoryState = {
        loading:true,
        tryAgain:false,
        detailsArray:[],
        billBeforeDetails:[],
        total:0,
        showBillMain:true, //  单据主要信息 false则为其他字段表格信息
        otherFiledTable:null,
        dataSource:null,
        emptyData:false
    }
    bus = new MessageCenter()
    constructor(props) {
      super(props)
      this.getResult = this.getResult.bind(this)
      props.overrideGetResult(this.getResult)
    }

    componentDidMount(){
      this.getFetch()
      this.bus.on('history:show:field:table',this.showOtherFieldTable)
    }

    componentWillUnmount(){
      this.bus.un('history:show:field:table',this.showOtherFieldTable)
    }

    showOtherFieldTable = (data)=>{
      this.setState({
        showBillMain: data ? false : true,
        otherFiledTable:data
      })
    }

    getResult(){ }

    getFetch = ()=>{
      const { flowVersionedId } = this.props
      this.setState({
        loading:true
      },()=>{
        HistoryApi.get(flowVersionedId).then((data:HistoryDataProps)=>{
          if(data){
            this.setState({
              loading:false,
              tryAgain:data.dataSource.status !== StatusType.DONE,
              emptyData:data.dataSource.status === StatusType.DONE && !data.dataSource?.differences,
              dataSource:data.dataSource,
              total:_flatten(data?.detailsArray).length + data?.billBeforeDetails?.length,
              billBeforeDetails:data?.billBeforeDetails || [],
              detailsArray:data?.detailsArray || [],
            })
          }
        })
      })
    }
    
    render(){
        const { loading, tryAgain,billBeforeDetails,detailsArray, otherFiledTable , total, showBillMain, dataSource, emptyData} = this.state
        const Empty = ()=><div className="emptyTips center">
          <IllustrationSmallLoadFailed />
          <div className="placeholder">{i18n.get('无法加载，请重试')}</div>
          <Button category="secondary" className="mt-12" onClick={this.getFetch}>{i18n.get('重试')}</Button>
        </div>

        return <div className={style['history-record-warp']}>
                { loading && <Loading /> }
                { !loading && tryAgain && <Empty />} 
                { !loading && <EmptyView emptyData = {emptyData} />}
                { !loading && !emptyData && !tryAgain && showBillMain && <Main dataSource={dataSource} billBeforeDetails={billBeforeDetails} bus={this.bus} detailsArray={detailsArray} total={total} /> }
                { !showBillMain && <FieldDetailsTable data={otherFiledTable} bus={this.bus} />}
        </div>
    }
  }

  const Loading = ()=>{
  return (
    <div className="load center">
      <OutlinedGeneralLoading spin />
      <div className="placeholder">{i18n.get('加载中...')}</div>
    </div>
  )
}

interface EmptyViewProps {
  emptyData:boolean
}
const EmptyView: React.FC<EmptyViewProps> = props => {
  if (!props.emptyData) {
    return null
  }
 
  return (
    <div className="emptyTips center">
      <IllustrationSmallNoContent />
      <div className="placeholder">{ i18n.get('无修改内容') }</div>
    </div>
  )
}