/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-08-28 17:32:28
 * @Description  : 单据历史记录弹框（分摊列表）
 * 描述功能/使用范围/注意事项
 */
import React, {useCallback, useEffect,useState,useMemo} from 'react'
import { Button } from '@hose/eui-mobile'
import { OutlinedEditTableGroup } from '@hose/eui-icons'
import style from '../index.module.less'
import { actionEnum } from '../types'
import { LightTable } from '@ekuaibao/eui-isomorphic'
import { tableColumnsFormat, renderComponentByType, emptyString, isDeleteString, isAddString } from './components'

const FieldDetails = ({ data, bus, isBack = true }) => {
  const { title, isShowDiff = true } = data
  data = data?.details || [data] 
  // 使用useMemo来缓存columns和result的计算结果
  const { columnsData, result } = useMemo(() => tableColumnsFormat(data,false), [data])

  const columns = useMemo(() => columnsData.map((column, index) => ({
    title: column.label,
    dataIndex: column.field,
    key: index,
    width:120,
    render: (value, record) => renderColumn(value, record),
  })), [columnsData])

  const renderColumn = (value, record:any) => {
    const none = value?.contentAction === actionEnum.none || value?.action === actionEnum.none 
    const noUpdateDom = <span className="item">{i18n.get('<空>')}</span>

    if (!value) {
      if(record?.action === actionEnum.delete){
        return isDeleteString()
      }else if(record?.action === actionEnum.add){
        return isAddString()
      }
      return noUpdateDom
    }else if(none){
      return renderComponentByType(value.newValue, value,columnsData) ?? noUpdateDom
    } else {
      return (
        <div className={`dis-f fd-c ai-c`}>
          <span className="before">{renderComponentByType(value.oldValue, value,columnsData) ?? emptyString(value, true)}</span>
          <span className="after">{renderComponentByType(value.newValue, value,columnsData) ?? emptyString(value, false)}</span>
        </div>
      )
    }
  }

  const back = useCallback(() => {
    bus.emit('history:show:field:table', null)
  }, [bus])

  return (
    <div className={style['history-warp']}>
       <div className="list details active">
        <div className="title horizontal jc-sb">
          <div>
            <OutlinedEditTableGroup className="mr-8" />{title}
          </div>
          <div>
            { isBack && <Button category="text" size="mini" theme="highlight" onClick={back}>返回上一级</Button> }
          </div>
        </div>
        <LightTable fixedColumnCount={0} columns={columns} dataSource={result} />
      </div>
    </div>
  )
}

export default React.memo(FieldDetails)