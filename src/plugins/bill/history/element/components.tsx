/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-08-27 22:03:31
 * @Description  : 
 * 描述功能/使用范围/注意事项
 */
import React from 'react'
import Money from '../../../../elements/puppet/Money'
import { isArray } from '@ekuaibao/helpers'
import { actionEnum, FieldProps, ComponentType } from '../types'
import _compact from 'lodash/compact'
import TagsEllipsis from './TagsEllipsis'
import Big from 'big.js'
import { getStaffShowByConfig } from '../../../../components/utils/fnDataLinkUtil'

const ForeignMoney = ({value, containerWidth})=>{
  const { foreignStrCode, foreign, standardStrCode, standard, rate, scale = 2 } = value || {}
  const data = `${foreignStrCode || ''} ${new Big(foreign).toFixed(scale) || ''}（${standardStrCode || ''} ${new Big(standard).toFixed(scale) || ''}，${i18n.get('汇率')}${rate || ''}）`
  return (
      <TagsEllipsis items={[data]} containerWidth={containerWidth} /> 
    )
}

const MoneyRender = ({value, containerWidth})=>{
    value = value [0]
    if(value?.foreign){
      return <ForeignMoney value={value} containerWidth={containerWidth} />
    }else{
      return <span className="item"><Money value={value} /></span> 
    }
}

const Staff = ({value, containerWidth})=>{
  value = value?.map(item=>getStaffShowByConfig(item)) || []
  return <TagsEllipsis items={value} containerWidth={containerWidth} /> 
}

const Name = ({value, containerWidth})=>{
  value = value?.map(item=>{
    if(item.code){
      return `${item?.name}（${item?.code}）`
    }else{
      return `${item?.name}`
    }
  }) || []
  return <TagsEllipsis items={value} containerWidth={containerWidth} /> 
}

const Payee = ({value, containerWidth})=>{
  value = value?.map(item=>`${item?.bank} ${item?.cardNo} ${item?.name}`)|| []
  return <TagsEllipsis items={value} containerWidth={containerWidth} /> 
}

const Base = ({value, containerWidth})=>{
  return <TagsEllipsis items={value} containerWidth={containerWidth} /> 
}

export const Details = ({value, containerWidth})=>{
  value = value?.map(item=>`${item?.feeTypeName}(${item?.feeTypeCode})`)|| []
  return <TagsEllipsis items={value} containerWidth={containerWidth} /> 
}

const Attachments = ({value, containerWidth})=>{
  value = value?.map(item=>item?.fileName) || []
  return <TagsEllipsis items={value} containerWidth={containerWidth} />  
}

const WrittenOff = ({value, containerWidth})=>{
  value = value?.map(item=>`${item?.title} (${item?.code})`) || []
  return <TagsEllipsis items={value} containerWidth={containerWidth} />  
}

const Switcher = ({value})=>{
  value = value?.map(item=>Boolean(item) ? '开' :'关' ) || []
  return <span className="item" >{value[0] || ''}</span> 
}
const InvoiceForm =  ({value, containerWidth})=>{
  value = value?.map(item=>item?.invoiceMessage) || []
  return <TagsEllipsis items={value} containerWidth={containerWidth} />  
}

const City = ({ value, containerWidth })=>{
  value = JSON.parse(value) || []
  value = value?.map(item=>item?.label)
  return <TagsEllipsis items={value} containerWidth={containerWidth} />  
}

const DataLinkEdits = ({ value, containerWidth })=>{
  value = value?.map((item:any)=>`${item?.dataLinkName}（${item?.dataLinkCode}）`)
  return <TagsEllipsis items={value} containerWidth={containerWidth} />  
}

// 不同组件加载
export const OtherSpecialFields = (type:string)=>{
  return !(Object.values(ComponentType) as string[]).includes(type)
}

// 空字符判断 updateBefore: 修改前的数据
export const emptyString =(data:FieldProps,updateBefore:boolean) =>{
   const isAddString = '-'
    const emptyString = '<空>'
    const isAdd = data.action === actionEnum.add
    const isEdit = data.action === actionEnum.edit
    let value
    if(updateBefore){
      // 编辑中修改 空 新值  // 新增字段  - <空> 
      value =  isAdd ? isAddString : emptyString
    }else{
      value = (isAdd || isEdit) ? emptyString : isAddString
    }
    // 费用明细第一列特殊处理
    if(data.type === ComponentType.Details){
      value = [actionEnum.add,actionEnum.delete].includes(data.contentAction) ? isAddString : emptyString
    }
    return <span className={`item ${updateBefore && value === isAddString ? 'none' : '' }`} >{value}</span>
}

// 手动构造删除操作（后端不返回此数据）
export const isDeleteString = ()=>{
  return <div className="dis-f fd-c">
    <span className="before"><span className="item">{i18n.get('<空>')}</span></span>
    <span className="after"><span className="item">-</span></span>
</div>
}

// 手动构造add操作（后端不返回此数据）
export const isAddString = ()=>{
  return <div className="dis-f fd-c">
  <span className="before none"><span className="item">-</span></span>
  <span className="after"><span className="item">{i18n.get('<空>')}</span></span>
</div>
}

export const isDetailsUpdate =(data:FieldProps)=>{
  const fee =  data?.newValue && data?.newValue[0] || {}
  const oldFee = data?.oldValue &&  data?.oldValue[0] || {}
  if(data.action === actionEnum.edit && fee.feeTypeName === oldFee.feeTypeName){
    return <span className="fs-12 text-nowrap">{`${oldFee.feeTypeName}（${oldFee.feeTypeCode}）`}</span>
  }else{
    return null
  }
}

// 创建一个函数来根据类型渲染对应的组件
export const renderComponentByType = (value, data:FieldProps, columnsData = [] )=>{
  let ComponentToRender
  value = isArray(value) ? value : [value]
  let emptyString = !value?.length 
  if(ComponentType.Switcher !== data.type){
    // 开关值会过滤真假值
    emptyString = !value?.length || !_compact(value)?.length
  }
  const { clientWidth } = window.document.documentElement
  const containerWidth = columnsData?.length > 1 ? 80 : clientWidth - 150
  
  // 使用 switch 语句来确定要渲染的组件
  switch (data.type) {
    case ComponentType.Money:
        ComponentToRender = MoneyRender
        break
    case ComponentType.Number:
    case ComponentType.Text:
    case ComponentType.Date:
    case ComponentType.DateRange:
    case ComponentType.Textarea:
      ComponentToRender = Base
      break
    case ComponentType.Switcher:
      ComponentToRender = Switcher
      break
    case ComponentType.Requisition:
    case ComponentType.Datalink:
    case ComponentType.Dimension:
    case ComponentType.Department:
    case ComponentType.Enum:
    case ComponentType.Specification:
      ComponentToRender = Name
      break
    case ComponentType.Staff:
        ComponentToRender = Staff
        break
    case ComponentType.Details:
      ComponentToRender = Details
      break
    case ComponentType.Attachments:
      ComponentToRender = Attachments
      break
      case ComponentType.Payee:
      ComponentToRender = Payee
          break
    case ComponentType.City:
      ComponentToRender = City
      break
    case ComponentType.WrittenOff:
      ComponentToRender = WrittenOff
          break
    case ComponentType.InvoiceForm:
      ComponentToRender = InvoiceForm
        break
    case ComponentType.DataLinkEdits:
      ComponentToRender = DataLinkEdits
      break
    default:
      console.log('未知类型，无法渲染组件', data.type)
      return null
  }
  
  // 如果组件存在且值不为空，则渲染组件
  if (ComponentToRender && !emptyString) {
    return <ComponentToRender value={value} data={data} containerWidth={containerWidth} />
  }
  console.log('我是拿到null值的', data)
  return null
}

export const  tableColumnsFormat =(data:FieldProps[],isChildrenTable:boolean = false)=>{
  // 找出differences字段最长的对象
  const longestDifferences = data.reduce((prev:FieldProps, current) => {
      return (prev.differences || []).length > (current.differences || []).length ? {...prev,sort:0} : {...current,sort:0}
    }, {} as FieldProps)
    
   let resultArray =  [longestDifferences].concat(longestDifferences.differences || [])
       resultArray = isChildrenTable ? resultArray : resultArray.filter((_, index) => index !== 0)
   let flattenChildren = resultArray.map(({ differences, ...rest }) => rest)

     // 数据去重数据
     flattenChildren = Array.from(new Map(flattenChildren.map(item => [item.field, item])).values()).sort((a, b) => a.sort - b.sort)

    // 转换行数据并生成结果数组
    const result = data.map((item,index) => {
      const fieldObject:any = {}
      const { differences, ...rest } = item
      fieldObject[item.field] = rest
      if (differences && differences.length) {
        differences.forEach(diff => {
          const value = fieldObject[diff.field]
          const feeTypeId = (diff.newValue?.[0] || diff.oldValue?.[0]) || {}
          diff.isMulDataLink = (feeTypeId?.behaviour === 'REF' || feeTypeId?.behaviour === 'REF')
          if(value){
              // 多字段合并，通过details字段去存储多数组数据
              const newValue = isArray(value?.details) ? [...value.details,diff] : [value,diff] 
              fieldObject[diff.field] = {
                  ...value,
                  details:newValue
              }
          }else{
              fieldObject[diff.field] = diff
          }
        })
      }
      // 手动加上一个操作类型 手动构造删除操作（后端不返回此数据）
      return {...fieldObject,key:index,contentAction:rest.contentAction}
    })

  return {
      columnsData:flattenChildren,
      result:result
  }
}

// function calculateContainerWidth(columnsData) {
//   if (!columnsData || columnsData.length === 0) {
//     return defaultWidth;
//   }
//   return columnsData.length > 1 ? oneColumnWidth : tableWidth;
// }