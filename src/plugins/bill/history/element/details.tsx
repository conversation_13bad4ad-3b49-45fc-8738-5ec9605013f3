/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-08-28 14:48:25
 * @Description  : 单据历史记录弹框（费用明细列表）
 * 描述功能/使用范围/注意事项
 */
import React, { useState, useMemo, useCallback} from 'react'
import { Button } from '@hose/eui-mobile'
import { OutlinedEditTableGroup } from '@hose/eui-icons'
import { FieldProps, actionEnum} from '../types'
import { LightTable } from '@ekuaibao/eui-isomorphic'
import { renderComponentByType, emptyString, tableColumnsFormat, isDetailsUpdate, OtherSpecialFields } from './components'
import classNames from 'classnames'
interface Props {
  data:FieldProps[],
  style:React.CSSProperties
  bus:any
}

const DetailItem = React.memo(({ detail, index, renderColumns }) => {
  const {columns, result} = useMemo(() => renderColumns(detail), [detail, renderColumns])
  return (
    <div className={classNames('list details')} key={index}>
      <div className="title horizontal jc-sb">
        <div>
          <OutlinedEditTableGroup className="mr-8" />
          {i18n.get('费用明细')} - {detail[0]?.label?.split('(')[0]}
        </div>
        <div>
        </div>
      </div>
      <LightTable fixedColumnCount={0} columns={columns} dataSource={result} />
    </div>
  )
})

const Details = ({ data, bus }):JSX.Element => {
   // 展开分摊
  const showApportion = useCallback((data)=>{
    bus.emit('history:show:field:table',{title:data?.label, ...data})
  },[])

  const renderColumns = useCallback((Row) => {
    const { columnsData, result} = tableColumnsFormat(Row,true)

    const columns = columnsData.map((v,index)=>{
      return {
        title: v.field === 'details' ? '费用类型' :v.label,
        dataIndex: v.field,
        key: v.field+index,
        width:120,
        render:(v:FieldProps,record)=>{
          const isDelete = v?.action === actionEnum.delete
          const isAdd = v?.action === actionEnum.add
           // 优先使用内容的未修改状态 action 是字段上的操作 contentAction是实际内容操作
          const none = v?.contentAction === actionEnum.none || v?.action === actionEnum.none 
          const details = v?.field === 'details'
          const otherFiled = OtherSpecialFields(v?.type)
          const noUpdateDom = <span className="item">{'<空>'}</span>
          if(!v){
            return noUpdateDom
          }else if(otherFiled){
            const tips = i18n.get('最新版本请在单据详情上查看')
            return <div className="caption ml-10 mr-10">{tips}</div>
             // 关联业务对象当普通字段处理，业务对象写入type 是空这里不处理了
          }else if(['apportions'].includes(v.type)){
            if(isDelete){
              return <div className="dis-f fd-c">
                  <span className="placeholder">{i18n.get('无预览')}</span>
                  <span>-</span>
              </div>
            }else if (none) {
              return <span className="placeholder">{i18n.get('无预览')}</span>
            }else if(isAdd && !v?.differences){
              return <div className="dis-f fd-c">
                <span>-</span>
                <span className="placeholder">{i18n.get('无预览')}</span>
            </div>
            }
            return <div className="ws-nowrap fs-12">
                <Button category="text" size="mini" theme="highlight" onClick={()=>{showApportion(v)}}>{i18n.get('查看修改内容')}</Button>
            </div>
          }else if(details && isDetailsUpdate(v)){
            return isDetailsUpdate(v)
          }else if(none){
            return<span className="none-update">{renderComponentByType(v.newValue,v,columnsData) ?? noUpdateDom }</span>       
          }else{
            return <div className="dis-f fd-c ai-c">
                  <span className="before">{renderComponentByType(v.oldValue,v,columnsData) ?? emptyString(v, true)}</span>
                  <span className="after">{renderComponentByType(v.newValue,v,columnsData) ?? emptyString(v, false)}</span>
            </div>
          }
        }
      }
    })
    return { columns, result}
  }, [])

  return (
    <>
      {data.map((details, index) => (
        <DetailItem
          key={index}
          detail={details}
          index={index}
          renderColumns={renderColumns}
        />
      ))}
    </>
  )
}

export default React.memo(Details)