.history-record-warp{
    height: 100%;
    overflow-y: auto;
    padding-bottom: 50px;
    :global{
        .load{
            height: 100%;
            >span{
                font-size: 44px;
                color: var(--eui-primary-pri-500);
            }
        }
        .placeholder{
            color: var(--eui-text-placeholder);
        }
        .emptyTips{
            height: 100%;
            >span{
                font-size: 240px;
            }
        }

        .list{
            border: 1px solid var(--eui-line-divider-default);
            padding: 24px 32px;
            color: var(--eui-text-caption);
            border-radius: 16px;
            margin-top: 16px;
            .before,.after{
                display: flex;
                font-size: 24px;
                label{
                    width: 104px;
                }
                .des{
                    display: flex;
                    // width: 406px;
                }
            }
            .before {
                .item {
                    text-decoration: line-through;
                    font-size: 24px;
                    margin-right: 8px;
                    padding: 2px 8px;
                    border-radius: 8px;
                }
                .empty,.none{
                    text-decoration: none;
                    .item{
                        text-decoration: none;
                    }
                }
            }
            .none-update .item{
                font-size: 24px;
                display: flex;
            }
            .placeholder{
                color: var(--eui-text-placeholder);
                font-size: 24px;
            }
            .after .item{
                background-color: var(--eui-secondary-lime-50);
                margin-right: 8px;
                padding: 2px 8px;
                border-radius: 8px;
                font-size: 24px;
                text-wrap: nowrap;
                .value{
                    font-size: 24px;
                }
            }

            // 明细下的样式重写
            .ekuaibao-light-table{
                .before,.after{
                    max-width: 300px;
                    text-wrap: nowrap;
                }
                .border{
                    border-right: none;
                }
                .tags-ellipsis .item:only-child{
                    width: 100%;
                    display: inline-block;
                }
                .tags-ellipsis{
                    height:36px;
                }
            }
            .status{
                max-width: 300px;
                min-width: 60px;
            }

            .caption{
                font-size: 24px;
                color: var(--eui-text-caption);
            }

        }
        .item{
            .value{
                font-size: 24px;
            }
        }
        .title{
            text-align: left;
            font-size: 28px;
            color: var(--eui-text-title); 
            margin-bottom: 16px;
            font-weight: 500;
            height: 40px;
            >span{
                font-size: 28px;
                color: var(--eui-icon-n1);
            }
        }
        .user-info-warp{
            display: flex;
            align-items: center;
            color:var(--eui-text-title);
            font-size: 28px;
            .name{
                margin-left: 8px;
                max-width: 360px;
                display: inline-block;
                font-weight: 500;
            }
        }
        .date{
            color: var(--eui-text-caption);
            font-size: 28px;
        }

        .eui-button-mini{
            font-size: 24px;
        }
    }
}