/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-08-30 16:32:40
 * @Description  : 
 * 描述功能/使用范围/注意事项
 */
import { Resource } from '@ekuaibao/fetch'
const history =  new Resource('/api/flow/v2/flowVersioneds')
import _findLastIndex from 'lodash/findLastIndex'
import { FieldProps, ComponentType  } from '../types'

/**
 * 获取所有历史风险
 * @param data 
 * @returns 
 */
 const get = async (flowVersionedId:string) => {
  const errorStatus = {dataSource:{status:'TODO'}}
  return history.GET('/record/$flowVersionedId',{flowVersionedId}).then(data=>{
    if(data?.value){
      const differencesData:FieldProps[] = data.value.differences 
      if(!differencesData?.length){
        // 无数据修改
        return { dataSource: data.value}
      }
      // 分割数据为三部分
      const billBeforeDetails:FieldProps[] = []
      const detailsArray = []
      const detailsMap: Record<string, FieldProps[]> = {}
      const uniqueRecords:Record<string, FieldProps> = {}

      differencesData.forEach(item => {
         // 分组字段
        if(item.type !== ComponentType.Details){
          const fee = item.newValue?.[0] ?? {}
          const oldFee = item.oldValue?.[0] ?? {}
          item.isMulDataLink = (fee?.behaviour === 'REF' || oldFee?.behaviour === 'REF')
          if (!uniqueRecords[item.field]) {
            // 如果记录不存在，初始化记录并添加details字段
            uniqueRecords[item.field] = { ...item, details: [item]}
          } else {
            // 如果记录已存在，将当前项添加到details数组中
            uniqueRecords[item.field].details.push(item)
          }
        }
      })

      // 将对象转换回数组，并删除每个记录中的重复数据
      const result: FieldProps[] = Object.values(uniqueRecords).map((item:FieldProps) => {
        const { details, ...uniqueItem } = item
        return item.details.length > 1 ? item : uniqueItem
      }).concat(differencesData.filter(v=>v.field === ComponentType.Details)).sort((a, b) => a.sort - b.sort)
      
      result.forEach(item => {
        if (item.type === ComponentType.Details && (item.oldValue || item.newValue)) {
          // 根据feeTypeId 分组
          const feeTypeId = (item.oldValue?.[0]?.feeTypeId || item.newValue?.[0]?.feeTypeId) || ''
          if (!detailsMap[feeTypeId]) {
            detailsMap[feeTypeId] = [item]
          } else {
            detailsMap[feeTypeId].push(item)
          }
        } else {
          billBeforeDetails.push(item)
        }
      })

      // 将detailsMap转换为二维数组
      for (const key in detailsMap) {
        if(detailsMap[key]){
          detailsArray.push(detailsMap[key])
        }
      }
        return { dataSource: data.value, billBeforeDetails, detailsArray }
      }
      return errorStatus
    }).catch(()=>{
      return errorStatus
    })
  }

const getFlowVersionModifyState = (flowVersionedId:string)=>{
  return history.GET('/state/$flowVersionedId',{flowVersionedId})
}

export const HistoryApi = { get,getFlowVersionModifyState }

