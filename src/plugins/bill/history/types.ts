/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-08-26 21:17:40
 * @Description  : 
 * 描述功能/使用范围/注意事项
 */

// 后端数据返回
export interface FieldProps {
  field: string
  label: string
  cnLabel?: string
  enLabel?: string
  oldValue: any
  newValue: any
  sort: number
  type: string
  details?:FieldProps[] 
  differences: null | FieldProps[]  // 根据实际情况，这里可以是具体的类型或者any
  action: actionEnum,
  isMulDataLink:boolean, // 关联业务多选
  contentAction:actionEnum
}

export enum actionEnum{
  add = 'ADD',
  edit = 'EDIT',
  delete = 'DELETE',
  none = 'NONE' // 无修改
}

export enum StatusType{
  TODO = 'TODO',
  DONE = 'DONE',
  DOING = 'DOING'
}

export interface HistoryProps {
flowVersionedId:string
}

export interface HistoryApiDataProps {
  flowId:string,
  flowVersionId:string,
  status:StatusType,
  differences:FieldProps[],
  modifyDateTime:number,
  modifyStaffId:{
    name:string
  }
}

export interface HistoryDataProps {
  dataSource:HistoryApiDataProps,
  billBeforeDetails:FieldProps[],
  detailsArray:FieldProps[],
}

// 定义组件类型枚举
export enum ComponentType {
  Money = 'money',
  Textarea= 'textarea',
  Number = 'number',
  Staff = 'staff',
  Text = 'text',
  Date = 'date',
  Switcher = 'switcher',
  DateRange = 'dateRange',
  Enum = 'enum',
  Details = 'details',
  Attachments = 'attachment',
  Payee = 'payee',
  Datalink = 'datalink',
  DataLinkEdits = 'dataLinkEdits',
  Dimension = 'dimension',
  Department= 'department',
  City = 'city',
  WrittenOff = 'writtenOff',
  PayPlan = 'payPlan',
  InvoiceForm = 'invoice',
  Requisition = 'requisition',
  Specification = 'specification',
  Apportions = 'apportions'
}
