import { get } from 'lodash'

export default [
  {
    point: '@bill:third:import',
    onload: () => ({
      type: 'dataLink',
      async onClick(...args) {
        const groupType = get(args[3], 'dataLinkEntity.platformId.groupType', '')
        const type = get(args[3], 'dataLinkEntity.platformId.type', '')
        const linkId = get(args[3], 'linkId') //开启了对照关联

        const { fnOpenFeeType, fnAikeIsSavePhone, fnOpenAssociatedDataLinkModal } = await import('./utils')

        return fnAikeIsSavePhone({ groupType, type }).then(res => {
          if (res.isOpenNext) {
            if (linkId) {
              return fnOpenAssociatedDataLinkModal(...args).then(result => {
                return { data: result.consume, type: 'dataLink' }
              })
            }
            return fnOpenFeeType(...args).then(result => {
              return { data: result.consume, type: 'dataLink' }
            })
          }
        })
      }
    })
  },
  {
    resource: '@importDataLink',
    value: {
      ['utils']: () => import('./utils')
    }
  }
  // {
  //   point: '@bill:third:import',
  //   onload: () => ({
  //     type: 'tmcOrder',
  //     async onClick() {
  //       return api.open('@bill:NewOrderQuoteSelectList').then(result => {
  //         return fnOpenFeeType(...args).then(result => {
  //           return { data: result.consume, type: 'dataLink' }
  //         })
  //       })
  //     }
  //   })
  // }
]
