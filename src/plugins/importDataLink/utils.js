import { app as api } from '@ekuaibao/whispered'

export function fnOpenFeeType(a, b, { bus }, line) {
  const { promise, resolve } = window.PromiseDefer()
  bus
    .invoke('element:dataLink:multiselect:property', {
      entityInfo: line.dataLinkEntity,
      filterId: line.filterId
    })
    .then(data => {
      if (!data) return
      resolve(data)
    })
  return promise
}
export function fnOpenAssociatedDataLinkModal(a, b, { bus }, line) {
  const { promise, resolve } = window.PromiseDefer()
  bus
    .invoke('open:associated:dataLink:modal', {
      entityInfo: line.dataLinkEntity,
      filterId: line.filterId,
      linkDataLinkEntity: line.linkDataLinkEntity,
      linkFilterId: line.linkFilterId
    })
    .then(data => {
      if (!data) return
      resolve(data)
    })
  return promise
}

export function fnAikeIsSavePhone({ groupType, type }) {
  const { promise, resolve } = window.PromiseDefer()
  if (groupType === 'CRM') {
    api.invokeService('@common:is:need:phone', { type }).then(result => {
      if (result.value) {
        api.open('@basic:AikeVerificationModal', { type }).then(() => {
          resolve({ isOpenNext: false })
        })
      } else {
        resolve({ isOpenNext: true })
      }
    })
  } else {
    resolve({ isOpenNext: true })
  }
  return promise
}
