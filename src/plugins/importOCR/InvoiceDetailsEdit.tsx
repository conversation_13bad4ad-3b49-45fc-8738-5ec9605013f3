import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import { toast } from '../../lib/util'
import { app as api } from '@ekuaibao/whispered'
import styles from './InvoiceDetailsEdit.module.less'
import MessageCenter from '@ekuaibao/messagecenter'
// @ts-ignore
import SVG_STAMP from './images/fp-stamp.svg'
const Money = app.require('@elements/puppet/Money')
import { Dynamic } from '@ekuaibao/template'
const editable = app.require('@components/dynamic/index.editable')
// @ts-ignore
import createDOMForm from 'rc-form/lib/createDOMForm'
import { invoiceTemplate, invoiceMoney, invoiceJYM } from './configTemplate'
import moment from 'moment'
import get from 'lodash/get'
import { getFileregionUrl } from './listUtils'
import { canShowPreviewImage } from './utils'
import { NoticeBar } from '@hose/eui-mobile'

function create(T: any) {
  return createDOMForm({
    onValuesChange(props: any, changedValues: any) {}
  })(T)
}

interface State {
  isShowPrice: boolean
  template: any
  imageData: string
  message: string
  value: any
  isBlockChain: boolean
  regionUrl: string
  attachment: any
}

// @ts-ignore
@EnhanceTitleHook(i18n.get('发票修改'))
export default class InvoiceDetailsEdit extends PureComponent<any, State> {
  bus: any = new MessageCenter()
  data: any
  constructor(props: any) {
    super(props)
    const {
      dataSource: {
        master: { form }
      }
    } = props
    // @i18n-ignore
    const isBlockChain = form['E_system_发票主体_发票类别'] === 'BLOCK_CHAIN'
    // @i18n-ignore
    const message = i18n.get('请输入验证码')
    const template = [...invoiceTemplate.slice(), invoiceMoney(), invoiceJYM(isBlockChain)]
    this.state = {
      template,
      isShowPrice: true,
      imageData: '',
      message,
      value: form,
      isBlockChain,
      regionUrl: '',
      attachment: {}
    }
  }

  onChangeTemplate = (fpdm: string, isBlockChain: boolean) => {
    let cTemplate = invoiceTemplate.slice()
    const isFullDigital = this.props.dataSource?.master?.form?.E_system_发票主体_发票号码?.length === 20
    // isShowPrice: ture 增值税专用发票
    // isShowPrice: false 普通发票
    if (isBlockChain) {
      cTemplate = [...cTemplate, invoiceMoney(), invoiceJYM(isBlockChain)]
      this.setState({ template: cTemplate, isShowPrice: true })
    } else if (fpdm !== null) {
      api.invokeService('@invoice:get:invoice:inputType', { fpdm, fphm: '' }).then(res => {
        const { fplx, showType } = res?.value || {}
        let isShowPrice = false
        if (isFullDigital) {
          cTemplate.push(invoiceMoney(true))
          cTemplate.push(invoiceJYM(isBlockChain, true))
          cTemplate.forEach(el => {
            el.optional = true
          })
          isShowPrice = true
        } else if (showType === 'ALL') {
          cTemplate.push(invoiceMoney(true))
          cTemplate.push(invoiceJYM(isBlockChain, true))
          isShowPrice = true
        } else if (showType === 'AMOUNT') {
          cTemplate.push(invoiceMoney())
          isShowPrice = true
        } else {
          cTemplate.push(invoiceJYM(isBlockChain))
        }
        this.setState({ template: cTemplate, isShowPrice })
      })
    }
  }

  componentWillMount() {
    api
      .dataLoader('@common.payerInfo')
      .load()
      .then(() => {
        this.forceUpdate()
      })
  }

  componentDidMount() {
    const {
      dataSource: {
        master: { form }
      }
    } = this.props
    // @i18n-ignore
    const keys = { key: form['E_system_发票主体_图片'], regions: form['E_system_发票主体_识别范围'] }
    if (canShowPreviewImage(keys.key)) {
      getFileregionUrl(keys).then(result => {
        this.setState({
          regionUrl: get(result, 'value.regionUrl'),
          attachment: result.value
        })
      })
    }

    this.onChangeTemplate(form['E_system_发票主体_发票代码'], this.state.isBlockChain)
  }

  handleOnBlur = (value: any, field: any) => {
    const { isBlockChain } = this.state
    // @i18n-ignore
    if (field && field.name === 'E_system_发票主体_发票代码' && !isBlockChain) {
      this.bus.getValue().then((result: any) => {
        this.onChangeTemplate(value, isBlockChain)
        this.setState({ value: result })
      })
    }
  }

  handleOnCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOnSave = () => {
    const { isShowPrice, isBlockChain } = this.state
    const {
      dataSource: {
        master: { form }
      }
    } = this.props
    // @ts-ignore
    this.bus.getValueWithValidate(1).then((value: any) => {
      // @i18n-ignore
      if (!value['E_system_发票主体_发票金额'] && !value['E_system_发票主体_校验码']) {
        return toast.error(i18n.get('请输入金额或校验码'))
      }
      const date = moment(value['E_system_发票主体_发票日期']).format('YYYYMMDD')
      const fpImg = form?.E_system_发票主体_图片 // @i18n-ignore
      const region = form?.E_system_发票主体_识别范围 // @i18n-ignore

      let data: any = {
        source: 'OCR',
        fpdm: value['E_system_发票主体_发票代码'], // 发票代码 // @i18n-ignore
        fphm: value['E_system_发票主体_发票号码'], // 发票号码 // @i18n-ignore
        kprq: date, // 开票日期
        fpje: isBlockChain
          ? value['E_system_发票主体_发票金额'].standard
          : isShowPrice
          ? value['E_system_发票主体_发票金额'].standard
          : '', // 发票金额 // @i18n-ignore
        jym: isBlockChain ? value['E_system_发票主体_校验码'] : !isShowPrice ? value['E_system_发票主体_校验码'] : '', // @i18n-ignore
        isBlockChain: isBlockChain,
        region
      }
      // if (data.fpdm.length !== 10 && data.fpdm.length !== 12) {
      //   return util.alert(i18n.get('查验失败：[发票代码：不合法的长度]，请稍后再试!'))
      // }

      if (fpImg != void 0) {
        data = Object.assign(data, { key: fpImg })
      }

      api.invokeService('@invoice:get:inputInvoice:data', data).then((res: any) => {
        this.props.layer.emitOk(res)
      })
    })
  }

  handlePreviewImage = () => {
    const { regionUrl } = this.state
    api.invokeService('@layout:preview:file', regionUrl, false, '')
  }

  handlePreviewOriginImage = () => {
    const {
      attachment: { url }
    } = this.state
    api.invokeService('@layout:preview:file', url, false, '')
  }

  render() {
    const {
      dataSource: {
        master: { form, entityId }
      }
    } = this.props
    const { isBlockChain, regionUrl } = this.state
    const mom = moment(form['E_system_发票主体_发票日期']) || moment() // @i18n-ignore
    const date = mom.format(i18n.get('YYYY年MM月DD日'))
    const content = (
      <>
        <div>
          {i18n.get('- {__k0}，{__k1}，{__k2}和 {__k3}在【金额】字段处输入{__k4}', {
            __k0: i18n.get('电子发票（增值税专用发票）'),
            __k1: i18n.get('电子发票（普通发票）'),
            __k2: i18n.get('电子发票（航空运输电子客票行程单）'),
            __k3: i18n.get('电子发票（铁路电子客票）'),
            __k4: i18n.get('价税合计总金额')
          })}
        </div>
        <div>{i18n.get('- 其他增值税发票在【金额】字段处输入{__k0}', { __k0: i18n.get('不计税金额') })}</div>
      </>
    )
    return (
      <div className={styles['invoice-edit-wrapper']}>
        <div className="invoice-content">
          {entityId && entityId === 'system_发票主体' && <NoticeBar content={content} color="info" />}
          <div className="invoice-header">
            <div className="invoice-header-content">
              <div className="header">
                <div className="stamp_big">
                  <img src={SVG_STAMP} />
                </div>
                <div className="title">
                  {isBlockChain ? i18n.get('区块链电子发票') : i18n.get('增值税电子普通发票')}
                </div>
              </div>
              <div className="line" />
              <div className="line mt-1" />
              <div className="invoice-detials">
                <InvoiceItem name={i18n.get('购买方')} value={form[i18n.get('E_system_发票主体_购买方名称')]} />
                <InvoiceItem name={i18n.get('销售方')} value={form[i18n.get('E_system_发票主体_销售方名称')]} />
                <InvoiceItem name={i18n.get('开票时间')} value={date} />
                <div className="invoice-item">
                  <div>{i18n.get('价税合计')}</div>
                  <div>
                    <Money
                      currencySize={14}
                      valueSize={14}
                      color="#1d2b3d"
                      value={form[i18n.get('E_system_发票主体_价税合计')]}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="invoice-modify">
            {canShowPreviewImage(form?.['E_system_发票主体_图片']) ? (
              <div className="edit-header">
                <div className="image" onClick={this.handlePreviewImage}>
                  <img src={regionUrl} />
                </div>
                <div onClick={this.handlePreviewOriginImage}>查看原图</div>
              </div>
            ) : null}

            <div className="modify-content">
              <Dynamic
                value={this.state.value}
                bus={this.bus as any}
                elements={editable as any}
                create={create}
                template={this.state.template}
                onEditBlur={this.handleOnBlur}
              />
            </div>
          </div>
        </div>
        <div className="bottom-actions">
          <div className="delete" onClick={this.handleOnCancel}>
            {i18n.get('取消')}
          </div>
          <div className="save" onClick={this.handleOnSave}>
            {i18n.get('保存')}
          </div>
        </div>
      </div>
    )
  }
}

function InvoiceItem(props: { name: string; value: string }) {
  const { name, value } = props
  return (
    <div className="invoice-item">
      <div>{i18n.get(name)}</div>
      <div>{i18n.get(value)}</div>
    </div>
  )
}
