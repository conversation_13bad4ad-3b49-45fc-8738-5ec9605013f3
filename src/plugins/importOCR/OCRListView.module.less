@import '../../styles/ekb-colors';

:global {
  .debugger_top_fix.hidden {
    z-index: -1000;
  }
}
.tickets-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  background-color: #f5f5f5;
  :global {
    .am-list-line::after {
      height: 0 !important;
    }
    .am-list-body::before {
      height: 0 !important;
    }
    .am-list-item.am-list-item-active {
      background-color: transparent;
    }
    .group-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        color: rgba(29, 43, 61, 1);
        font-size: 32px;
        font-weight: 600;
      }
      .images {
        width: 116px;
        height: 116px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        img {
          border-radius: 8px;
          width: 80px;
          height: 80px;
        }
      }
      .xml-bg {
        background: var(--eui-primary-pri-50, #e8f1ff);
      }
      .images-default {
        margin: 18px 0 18px 18px;
        width: 80px;
        height: 80px;
        display: flex;
        background: var(--eui-function-danger-50, #ffece8);
        justify-content: center;
        align-items: center;
        border-radius: 8px;
      }
    }
    .tickets-content {
      height: 100%;
      overflow-y: auto;
      .invoice-item {
        margin-bottom: 32px;
        overflow-x: hidden;
        .checkbox-item {
          padding-left: 70px;
          .am-list-thumb {
            width: 24px !important;
            margin-right: 10px !important;
          }
        }
      }
    }
    .actions {
      height: 112px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px 0 2px;
      background-color: #ffffff;
      flex-shrink: 0;
      margin-bottom: 32px;
      .am-list-thumb {
        margin-right: 16px !important;
      }
      .invoice-select-all {
        font-size: 32px;
        color: rgba(29, 43, 61, 1);
      }
      .invoice-select-total {
        display: flex;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        align-items: center;
        font-size: 28px;
        margin-top: 4px;
        color: rgba(29, 43, 61, 1);
      }
      .continue {
        display: flex;
        align-items: center;
        color: @primary-6;
        .font {
          width: 144px;
          padding: 8px 12px;
          // height: 64px;
          background: rgba(29, 43, 61, 0.06);
          border-radius: 8px;
          font-size: 28px;
          text-align: center;
          color: rgba(29, 43, 61, 1);
          // line-height: 64px;
        }
        .ekb-files-uploader-wrapper {
          .input-wrapper {
            input {
              width: 100%;
            }
          }
        }
      }
    }
    .group-wrapper {
      margin-bottom: 32px;
      background-color: #ffffff;
      .rules {
        width: 618px;
        height: 1px;
        margin-left: 100px;
        background-color: #e8e8e8;
      }
    }
    .item-wrapper {
      .rules {
        height: 1px;
        background-color: #e8e8e8;
      }
      .am-list-thumb {
        .am-checkbox-inner {
          top: 0.54rem !important;
        }
      }
    }
    .set_other_icon {
      .am-list-thumb {
        label.am-checkbox-wrapper {
          span.am-checkbox {
            height: 50% !important;
          }
        }
      }
    }
    .bottom-actions {
      flex-shrink: 0;
      background-color: #ffffff;
      .select-info {
        display: flex;
        align-items: center;
        border-bottom: 2px solid #e8e8e8;
        padding: 16px 0;
        font-size: 28px;
      }
      .actions-btn {
        font-size: 32px;
        padding: 26px 16px 50px 16px;
        .multiple {
          flex: 1;
          color: @primary-6;
          text-align: center;
          width: 100%;
          height: 80px;
          border-radius: 8px;
          line-height: 80px;
        }
      }
      .actions-btn-import {
        display: flex;
        font-size: 32px;
        justify-content: space-between;
        padding: 20px 32px;
        .action-btn-import-button {
          margin-right: 20px;
          height: 88px;
          border-radius: 12px;
          text-align: center;
          font: var(--eui-font-head-r1);
          border: 1px solid var(--eui-primary-pri-500);
          background: var(--eui-bg-body);
          display: flex;
          justify-content: center;
          align-items: center;
          color: var(--eui-primary-pri-500);
          flex: 1;
          &:last-child {
            margin-right: 0;
          }

          &.disabled {
            color: var(--eui-text-disabled);
            border-color: var(--eui-line-border-component);
          }
        }
      }
    }
  }
}
