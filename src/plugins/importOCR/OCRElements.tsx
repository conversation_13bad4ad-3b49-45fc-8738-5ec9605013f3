import { app } from '@ekuaibao/whispered'
import React from 'react'
import styles from './OCRElements.module.less'
const Money = app.require<any>('@elements/puppet/Money')
const EKBIcon = app.require<any>('@elements/ekbIcon')
const SignView = app.require<any>('@elements/puppet/ThirdCard/SignView').default
const { InvoiceCheckerTag, InvoiceMetaileTag, InvoiceSignatureTag } = app.require<any>(
  '@elements/puppet/ThirdCard/SignView'
)

import moment from 'moment'
import { toast } from '../../lib/util'
import { INVOICE_TYPE } from '@ekuaibao/lib/lib/enums'
import { OutlinedDataOtherfile } from '@hose/eui-icons'
import { get } from 'lodash'
import { isInvoiceImage, getFormattedInvoiceTitle } from './utils'
import { getTitleByInvoiceMark } from '../../elements/puppet/ThirdCard/Ticket'
import { convertForeignToStandard } from '../../lib/InvoiceMappingValue'
import { OutlinedTipsInfo } from '@hose/eui-icons'
import { enableOtherInvoiceByDimension } from '../../lib/featbit/utils'

export const mapView: { [key: string]: any } = {
  system_火车票: TrainView,
  system_出租车票: TaxiView,
  system_发票主体: InvoiceView,
  system_医疗发票: MedicalView,
  system_非税收入类票据: NontaxView,
  system_过路费发票: LoadCostView,
  system_航空运输电子客票行程单: FlightView,
  system_客运汽车发票: PassengerTicketView,
  system_定额发票: QuotaInvoiceView,
  system_其他: OthersView,
  system_消费小票: ShoppingView,
  system_机打发票: MachineView,
  system_海外发票: OverseasInvoiceView,
  error: ErrorView
}
interface Color {
  bgColor: string
  fontColor: string
}
const colorType1: Color = {
  bgColor: 'rgba(24, 144, 255, 0.1)',
  fontColor: '#1890ff'
}
const colorType2: Color = {
  bgColor: 'rgba(211, 123, 69, 0.1)',
  fontColor: '#d37b45'
}
const colorType3: Color = {
  bgColor: ' rgba(29, 43, 61, 0.06)',
  fontColor: 'rgba(29, 43, 61, 0.5)'
}

const colorType4: Color = {
  bgColor: 'rgba(24,182,148,0.1)',
  fontColor: 'rgba(24,182,148,1)'
}

const colorType5: Color = {
  bgColor: 'rgba(97, 78, 216, 0.1)',
  fontColor: 'rgba(102, 90, 181, 1)'
}
const colorType6: Color = {
  bgColor: '#FFF3DB',
  fontColor: '#D25F00'
}

interface BaseProps {
  title: React.ReactNode
  info: React.ReactNode
  signTitle: string
  moneyValue: any
  bgColor: string
  fontColor: string
  onEdit?: Function
  message?: string
  messageColors?: string
  isCheckerInvoice?: boolean
  realTagClass?: string
  onRetryCheckerInvoiceClick: () => void
  status?: string
  master?: any
  bindValidateMessage?: string
  isOverseas: boolean
}
function BaseView(props: BaseProps) {
  const {
    title,
    info,
    signTitle,
    moneyValue,
    bgColor,
    fontColor,
    onEdit,
    message,
    bindValidateMessage,
    messageColors,
    isCheckerInvoice,
    realTagClass,
    onRetryCheckerInvoiceClick,
    status,
    master,
    isOverseas
  } = props
  const isNontax = master?.entityId === 'system_非税收入类票据'
  const fileName = get(master, 'form.E_system_发票主体_图片', '')
  const isOfd = fileName?.toLowerCase()?.endsWith('.ofd')
  const ticketType = get(master, 'form.E_system_发票主体_发票状态', '')
  const isCurrency = get(master, 'form.E_system_海外发票_currency', '')
  const money = moneyValue;
  const dimensionCurrencyInfo = app.getState()['@bill']?.dimensionCurrencyInfo
  const isHasDimensionCurrency = enableOtherInvoiceByDimension() && dimensionCurrencyInfo?.currency

  // 提取的编辑按钮渲染逻辑
  const renderEditButton = () => {
    // 判断是否应该显示编辑按钮
    const shouldShowEditButton = isNontax 
      ? !isCheckerInvoice  // 非税票据：不是验真发票时显示
      : status && status !== 'NO_RESULT'  // 其他票据：有状态且不是NO_RESULT时显示

    if (!shouldShowEditButton) {
      return null
    }
    // 和后端约定好的，有这两个字符代表不正常
    if(message?.includes('国税') || message?.includes('税务局系统')){
      return null
    }

    return (
      <div className="edit" onClick={() => onEdit?.()}>
        {i18n.get('编辑')}
        {isHasDimensionCurrency && <OutlinedTipsInfo className='ml-4' />}
      </div>
    )
  }

  const handleMessage = (message: string) => {
    let cur_message = message

    if (
      IS_ZJZY &&
      message?.indexOf('该发票在单据') != -1 &&
      message?.indexOf('中已经关联') != -1 &&
      message?.indexOf('再重新上传') === -1
    ) {
      const bill_code = window.localStorage.getItem('bill_code')
      const tipText = message + '，请先保存单据再上传'
      if (message?.indexOf(bill_code) !== -1) {
        cur_message = tipText
      }
    }
    return cur_message
  }

  return (
    <div className={styles['element-wrapper']}>
      <div className="dis-f jc-sb">
        <div className="left">
          <div className={'dis-f'}>
            <SignView title={signTitle} bgColor={bgColor} fontColor={fontColor} />
            {realTagClass && <InvoiceCheckerTag isCheckerInvoice={isCheckerInvoice} ticketType={ticketType} />}
          </div>
          <div className="title text-ellipsis">{React.isValidElement(title) ? title : <span>{title || i18n.get('暂未获取到开票方信息')}</span>}</div>
          <div className="info text-ellipsis">{React.isValidElement(info) ? info : <span>{info}</span>}</div>
          {!!message && (
            <div
              className=" message text-ellipsis"
              style={{ color: messageColors || '#fc3842' }}
              onClick={() => toast.info(handleMessage(message))}
            >
              {handleMessage(message)}
            </div>
          )}
          {!!bindValidateMessage && (
            <div
              className="message text-ellipsis"
              style={{ color: '#fc3842' }}
              onClick={() => toast.info(bindValidateMessage)}
            >
              {bindValidateMessage}
            </div>
          )}
        </div>
        <div className="right train">
          <Money
            value={money}
            currencySize={12}
            isShowSymbol={isOverseas ? isCurrency : true}
            isShowForeign={money?.foreign}
            onlyForeign={money?.foreign}
            valueSize={16}
            color="#1d2b3d"
          />
          {!isOfd && renderEditButton()}
          {!isCheckerInvoice && realTagClass && (
            <div
              className="edit"
              onClick={e => {
                if (e.cancelable) {
                  e.preventDefault && e.preventDefault()
                }
                e.stopPropagation()
                onRetryCheckerInvoiceClick && onRetryCheckerInvoiceClick()
              }}
            >
              {i18n.get('点击验真')}
            </div>
          )}
        </div>
      </div>
      <InvoiceMetaileTag master={master} />
      <InvoiceSignatureTag master={master} />
    </div>
  )
}

function TrainView(props: any) {
  const { master, ...others } = props
  const form = master.form
  let title = `${form[i18n.get('E_system_火车票_上车车站')]} - ${form[i18n.get('E_system_火车票_下车车站')]}`
  const date = getDate(form[i18n.get('E_system_火车票_乘车时间')])
  const info = `${date} ${form[i18n.get('E_system_火车票_车次')]} ${form[i18n.get('E_system_火车票_乘车人姓名')]}`

  if(!form[i18n.get('E_system_火车票_上车车站')] && !form[i18n.get('E_system_火车票_下车车站')]){
    title = getFormattedInvoiceTitle()
  }

  return (
    <BaseView
      title={<div>{title}</div>}
      info={info}
      signTitle={i18n.get('铁路客票')}
      master={master}
      moneyValue={form[i18n.get('E_system_火车票_金额')]}
      {...colorType1}
      {...others}
    />
  )
}
function TaxiView(props: any) {
  const { master, ...others } = props
  const form = master.form
  const date1 = getDate(form[i18n.get('E_system_出租车票_上车时间')])
  const date2 = getDate(form[i18n.get('E_system_出租车票_下车时间')], 'HH:mm')
  const distance = form[i18n.get('E_system_出租车票_里程')] ? `${form[i18n.get('E_system_出租车票_里程')]}km` : ''
  const info = `${date1}-${date2} ${distance}`
  const title = form[i18n.get('E_system_出租车票_发票所在地')]
    ? i18n.get(`出租车发票（{__k0}）`, { __k0: form[i18n.get('E_system_出租车票_发票所在地')] })
    : i18n.get('出租车发票')
  return (
    <BaseView
      title={getFormattedInvoiceTitle(title)}
      info={info}
      signTitle={i18n.get('出租车票')}
      moneyValue={form[i18n.get('E_system_出租车票_金额')]}
      {...colorType2}
      {...others}
    />
  )
}
function InvoiceView(props: any) {
  const { message, status, master, ...others } = props
  const form = master.form
  const date = getDate(form[i18n.get('E_system_发票主体_发票日期')], i18n.get('YYYY.MM.DD'))
  // @ts-ignore
  const invoiceType = INVOICE_TYPE()[form[i18n.get('E_system_发票主体_发票类别')]] || i18n.get('增值税发票')
  const colors = status === 'NO_VISIBLE' ? '#fa8c16' : '#fc3842'
  // @i18n-ignore
  const isCheckerInvoice = form['E_system_发票主体_验真']
  const realTagClass = isCheckerInvoice ? 'real-card' : 'no-real-card'
  const ErrorIcon = () => (
    <span onClick={() => toast.info(message)}>
      <EKBIcon name="#EDico-plaint-circle" style={{ color: colors, width: '20px', height: '24px' }} />
    </span>
  )
  const SucIcon = () => <EKBIcon name="#EDico-check-true" style={{ color: '#38b928', width: '20px', height: '24px' }} />

  return (
    <BaseView
      title={
        <div style={{ display: 'flex' }}>
          <div className="text-ellipsis invoice-title">{getTitleByInvoiceMark(form)}</div>
          {!message ? <SucIcon /> : <ErrorIcon />}
        </div>
      }
      message={message}
      messageColors={colors}
      info={date}
      signTitle={invoiceType}
      moneyValue={form[i18n.get('E_system_发票主体_价税合计')]}
      realTagClass={realTagClass}
      isCheckerInvoice={isCheckerInvoice}
      status={status}
      master={master}
      {...colorType2}
      {...others}
    />
  )
}
function LoadCostView(props: any) {
  const { master, ...others } = props
  const form = master.form
  let title = `${form[i18n.get('E_system_过路费发票_入口')]} - ${form[i18n.get('E_system_过路费发票_出口')]}`
  const date = getDate(form[i18n.get('E_system_过路费发票_时间')])
  if(!form[i18n.get('E_system_过路费发票_入口')] && !form[i18n.get('E_system_过路费发票_出口')]){
    title = getFormattedInvoiceTitle()
  }
  return (
    <BaseView
      title={title}
      info={date}
      signTitle={i18n.get('过路费发票')}
      moneyValue={form[i18n.get('E_system_过路费发票_金额')]}
      {...colorType2}
      {...others}
    />
  )
}

function FlightView(props: any) {
  const { master, ...others } = props
  const form = master.form
  let title = `${form[i18n.get('E_system_航空运输电子客票行程单_出发站')]} - ${
    form[i18n.get('E_system_航空运输电子客票行程单_到达站')]
  }`
  const date = getDate(form[i18n.get('E_system_航空运输电子客票行程单_乘机时间')])
  const info = `${date} ${form[i18n.get('E_system_航空运输电子客票行程单_航班号')]} ${
    form[i18n.get('E_system_航空运输电子客票行程单_乘机人姓名')]
  }`
  if(!form[i18n.get('E_system_航空运输电子客票行程单_出发站')] && !form[i18n.get('E_system_航空运输电子客票行程单_到达站')]){
    title = getFormattedInvoiceTitle()
  }
  return (
    <BaseView
      title={title}
      info={info}
      signTitle={i18n.get('机票行程单')}
      moneyValue={form[i18n.get('E_system_航空运输电子客票行程单_金额')]}
      {...colorType1}
      {...others}
    />
  )
}
function QuotaInvoiceView(props: any) {
  const { master, ...others } = props
  const form = master.form
  return (
    <BaseView
      title={i18n.get('定额发票')}
      info={''}
      signTitle={i18n.get('定额发票')}
      moneyValue={form[i18n.get('E_system_定额发票_金额')]}
      {...colorType2}
      {...others}
    />
  )
}
function PassengerTicketView(props: any) {
  const { master, ...others } = props
  const form = master.form
  let title = `${form[i18n.get('E_system_客运汽车发票_出发车站')]} - ${
    form[i18n.get('E_system_客运汽车发票_达到车站')]
  }`
  const date = getDate(form[i18n.get('E_system_客运汽车发票_时间')])
  const info = `${date} ${form[i18n.get('E_system_客运汽车发票_姓名')]}`
  if(!form[i18n.get('E_system_客运汽车发票_出发车站')] && !form[i18n.get('E_system_客运汽车发票_达到车站')]){
    title = getFormattedInvoiceTitle()
  }
  return (
    <BaseView
      title={title}
      info={info}
      signTitle={i18n.get('客运汽车票')}
      moneyValue={form[i18n.get('E_system_客运汽车发票_金额')]}
      {...colorType1}
      {...others}
    />
  )
}
function OthersView(props: any) {
  const { master, ...others } = props
  const form = master.form
  const date = getDate(form[i18n.get('E_system_其他_日期')], i18n.get('YYYY年MM月DD日'))
  return (
    <BaseView
      title={i18n.get('其他票据')}
      info={date}
      signTitle={i18n.get('其他票据')}
      moneyValue={form[i18n.get('E_system_其他_金额')]}
      {...colorType3}
      {...others}
    />
  )
}

function OverseasInvoiceView(props: any) {
  const { master, ...others } = props
  const form = master.form
  const type = form['E_system_海外发票_票据类型']
  const date = getDate(form[i18n.get('E_system_海外发票_日期')], i18n.get('YYYY年MM月DD日'))

  return (
    <BaseView
      title={getFormattedInvoiceTitle(form['E_system_海外发票_title'])}
      info={date}
      isOverseas={true}
      signTitle={type || i18n.get('海外票据')}
      master={master}
      moneyValue={form['E_system_海外发票_金额']}
      {...colorType6}
      {...others}
    />
  )
}
function ErrorView(props: any) {
  const { onDelete } = props
  return (
    <div className={styles['error-wrapper']}>
      <div className="title">{i18n.get('未知图片')}</div>
      <div className="actions">
        <div className="image">
          <img />
        </div>
        <div onClick={onDelete}>
          <EKBIcon name="#EDico-delete" className="delete" />
        </div>
      </div>
    </div>
  )
}

function ShoppingView(props: any) {
  const { master, ...others } = props
  const form = master.form
  const date = getDate(form['E_system_消费小票_时间']) // @i18n-ignore
  
  return (
    <BaseView
      title={getFormattedInvoiceTitle(form['E_system_消费小票_店名'])}
      info={date}
      signTitle={'消费小票'}
      moneyValue={form['E_system_消费小票_金额']} // @i18n-ignore
      {...colorType1}
      {...others}
    />
  )
}

function MedicalView(props: any) {
  const { master, ...others } = props
  const form = master.form
  const title = `${form['E_system_医疗发票_发票种类']}`
  const date = getDate(form['E_system_医疗发票_开票日期'], i18n.get('YYYY年MM月DD日')) // @i18n-ignore
  return (
    <BaseView
      title={getFormattedInvoiceTitle(title)}
      info={date}
      signTitle={i18n.get('医疗发票')}
      moneyValue={form['E_system_医疗发票_金额合计']} // @i18n-ignore
      master={master}
      {...colorType4}
      {...others}
    />
  )
}

function NontaxView(props: any) {
  const { message, status, master, ...others } = props
  const form = master.form
  const title = `${form['E_system_非税收入类票据_发票种类'] ||
    form['E_system_非税收入类票据_收款单位'] ||
    form['E_system_发票主体_销售方名称'] ||
    ''}`
  const date = getDate(form['E_system_非税收入类票据_开票日期'], i18n.get('YYYY年MM月DD日'))
  // @ts-ignore
  const invoiceType = i18n.get(form['E_system_非税收入类票据_发票类别']) || i18n.get('其他财政票据')
  // const showCheckerInvoiceButton = form['E_system_非税收入类票据_发票类别'] === '非税收入通用票据'
  const colors = status === 'NO_VISIBLE' ? '#fa8c16' : '#fc3842'

  return (
    <BaseView
      title={getFormattedInvoiceTitle(title)}
      message={message}
      messageColors={colors}
      info={date}
      // showCheckerInvoiceButton={showCheckerInvoiceButton}
      signTitle={invoiceType}
      moneyValue={form[i18n.get('E_system_非税收入类票据_金额合计')]}
      status={status}
      master={master}
      {...colorType5}
      {...others}
    />
  )
}

function MachineView(props: any) {
  const { master, ...others } = props
  const form = master.form
  const title = `${form['E_system_机打发票_销售方名称']}`
  const invoiceType = i18n.get(form['E_system_机打发票_发票种类']) || i18n.get('机打发票')
  const date = getDate(form['E_system_机打发票_时间']) // @i18n-ignore
  return (
    <BaseView
      title={getFormattedInvoiceTitle(title)}
      info={date}
      signTitle={invoiceType}
      moneyValue={form['E_system_机打发票_金额']} // @i18n-ignore
      master={master}
      {...colorType4}
      {...others}
    />
  )
}

function getDate(date?: any, format?: string) {
  const mom = date ? moment(date) : moment()
  const formatStr = format ? format : i18n.get('YYYY年MM月DD日 HH:mm')
  return mom.format(formatStr)
}

export function LoadingView(props: any) {
  const { fileId } = props
  return (
    <div className={styles['ocr-loading']}>
      <div className="dis-f ai-c">
        <EKBIcon name="#EDico-loading" className="warn-icon" />
        <span className="info">{i18n.get('智能识别中…')}</span>
      </div>
      <div className="images">
        {fileId && fileId.thumbUrl && isInvoiceImage({ fileName: fileId.key }) ? (
          <img src={fileId && fileId.thumbUrl} />
        ) : (
          <OutlinedDataOtherfile className="place-image" />
        )}
      </div>
    </div>
  )
}
