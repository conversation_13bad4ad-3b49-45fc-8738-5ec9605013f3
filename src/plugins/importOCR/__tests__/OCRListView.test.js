/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/11 下午5:48.
 */
import React from 'react'
import { configure, shallow } from 'enzyme'
import Adapter from 'enzyme-adapter-react-16'
configure({ adapter: new Adapter() })
import OCRListView from '../OCRListView'

describe(i18n.get(`测试<SmartInvoiceView />`), () => {
  it(i18n.get('1. 测试 SmartInvoiceView props'), () => {
    const warpper = shallow(<OCRListView />)
    expect(warpper.find('a').length).toBe(1)
  })
})
