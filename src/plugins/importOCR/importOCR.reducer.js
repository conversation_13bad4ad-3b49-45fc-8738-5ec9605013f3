import { Reducer } from '@ekuaibao/store'
import key, { ID } from './key'
import { toast } from '../../lib/util'

const reducer = new Reducer(ID, {})

reducer.handle(key.SAVE_INVOICE_EDIT_RESULT, (state, action) => {
  return { ...state }
})

reducer.handle(key.GET_OCR_RESULT, (state, action) => {
  if (action.error) {
    toast.error(action.payload.msg)
    return state
  }
  const data = action.payload
  return { ...state, ocrList: data, fileId: action.fileId }
})

export default reducer
