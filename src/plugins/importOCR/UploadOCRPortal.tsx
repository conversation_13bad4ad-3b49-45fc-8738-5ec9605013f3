import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
const UploadAttachment = api.require<any>('@invoice/invoiceAttachments')
import { toast, showLoading, hideLoading } from '../../lib/util'

interface Props {
  onlyPagesIniOS?: boolean // iOS系统中仅展示文稿
  type?: string
}

interface State {
  fields: any[]
}

export default class UploadOCRPortal extends PureComponent<Props, State> {
  node: Element
  attactmentRef: Element
  mResolve: Function
  constructor(props: Props) {
    super(props)
    this.state = { fields: [] }
  }

  get eventName() {
    const nameMap = {
      medicalInvoiceOCR: 'upload:smartphoto:medical:click',
      invoiceFileOCR: 'upload:ocr:pages:click',
      overseasInvoice: 'upload:ocr:overseasInvoice'
    }
    return nameMap[this.props?.type] ?? 'upload:smartphoto:click'
  }
  componentDidMount() {
    api.watch(this.eventName, this.handleUploadPhotoClick)
  }

  componentWillUnmount() {
    api.un(this.eventName, this.handleUploadPhotoClick)
  }

  handleUploadPhotoClick = () => {
    const { promise, resolve } = window.PromiseDefer()
    this.mResolve = resolve
    return promise
  }

  handleOnUploadDone = (list: any[] = []) => {
    hideLoading()
    this.mResolve && this.mResolve(list)
  }

  filterFiles = (files: any) => {
    const fileTypeREG = /^(.*)\.(jpg|jpeg|png|pdf|ofd|xml|zip)$/i
    const oo: any[] = []
    files = files.filter((fileData: any) => {
      const { file = {} } = fileData
      const { name = '' } = file
      if (fileTypeREG.test(name.toLowerCase())) {
        return true
      }
      oo.push(file)
      return false
    })
    if (!!oo.length) {
      toast.info(i18n.get('只支持图片类型'))
    }
    return files
  }

  handleStart = () => {
    showLoading(i18n.get('上传中...'))
  }

  render() {
    const { children, onlyPagesIniOS } = this.props
    const type = this.props?.type || ''
    // 私有化环境需要新的文件名，因为OCR切割服务文件名有特殊字符导致获取不到切割图片
    const needNewFileName = !!IS_STANDALONE
    let accept = onlyPagesIniOS
      ? 'application/pdf,.ofd,.xml,.zip'
      : type === 'medicalInvoiceOCR'
      ? 'image/png, image/jpeg, image/jpg,application/pdf'
      : 'image/png, image/jpeg, image/jpg,application/pdf,.ofd,.xml,.zip'

    if (window.isSeeyon && window.isAndroid) {
      accept = 'image/*,application/pdf,.xml'
    }

    return (
      <UploadAttachment
        type={type}
        onStart={this.handleStart}
        filterFiles={this.filterFiles}
        onUploadDone={this.handleOnUploadDone}
        accept={accept}
        multiple={true}
        needNewFileName={needNewFileName}
      >
        {children}
      </UploadAttachment>
    )
  }
}
