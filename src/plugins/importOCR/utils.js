import { app as api } from '@ekuaibao/whispered'
import { showLoading, hideLoading } from '../../lib/util'
import { TwoToneDataPdf, TwoToneDataCode } from '@hose/eui-icons'

function getEventName(type) {
  switch (type) {
    case 'medicalInvoiceOCR':
      return 'upload:smartphoto:medical:click'
    case 'invoiceFileOCR':
      return 'upload:ocr:pages:click'
    case 'overseasInvoice':
      return 'upload:ocr:overseasInvoice'
    default:
      return 'upload:smartphoto:click'
  }
}

export function openOCR(params) {
  const eventName = getEventName(params?.type)
  return api.invoke(eventName, params).then(fileList => {
    // 隐藏importEntry.js和ImportInvoiceType.js
    api.invokeService('@third-import:pophub:hide')
    showLoading()
    return api.open('@invoiceOCR:ImportOCRListView', { params, fileList }).then(result => {
      hideLoading()
      return { type: 'invoiceOCR', data: result }
    })
  })
}

export const IMG_REG = /^(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|tiff|png|raw|tga)$/i

export const isInvoiceImage = attachment => {
  const name = attachment?.fileName ?? attachment?.key
  return IMG_REG.test(name?.toLowerCase())
}

export const geShowInvoiceImg = attachment => {
  if (!attachment) {
    return <TwoToneDataPdf fontSize={20} />
  }
  if (attachment.fileName?.endsWith('.pdf') || attachment.fileName?.endsWith('.PDF')) {
    return <TwoToneDataPdf fontSize={20} />
  }
  if (attachment.fileName?.endsWith('.xml') || attachment.fileName?.endsWith('.XML')) {
    return <TwoToneDataCode fontSize={20} color="" />
  }
  return <TwoToneDataPdf fontSize={20} />
}

export function getShowViewName(fileName) {
  const DIGITAL_ORIGINAL_FILE = api.getState('@common').powers.DIGITAL_ORIGINAL_FILE //数电原文件获取
  if (!DIGITAL_ORIGINAL_FILE) {
    return i18n.get('查看文件')
  }
  if (!fileName) {
    return i18n.get('查看文件')
  }
  if (fileName?.endsWith('.pdf') || fileName?.endsWith('.PDF')) {
    return i18n.get('查看PDF')
  }
  return i18n.get('查看文件')
}
export const canShowPreviewImage = fileName => {
  if (!fileName) {
    return true
  }
  if (fileName.endsWith('.zip') || fileName.endsWith('.ZIP')) {
    return false
  }
  return true
}

// 是否是电子航空火车票（属于增值税票下）
export function isElectronicAirAndTrain(form) {
  return ['ELECTRONIC_AIRCRAFT_INVOICE', 'ELECTRONIC_TRAIN_INVOICE'].includes(form?.E_system_发票主体_发票类别)
}

export function calculateInvoiceTaxRate(invoices) {
  const { form } = invoices?.invoiceId || {}
  if (isElectronicAirAndTrain(form) && !invoices?.taxRate) {
    let taxRate = 9
    if (form[`E_system_发票主体_业务类型`] == i18n.get('退')) {
      taxRate = 6
    }
    return { ...invoices, taxRate }
  }
  return invoices
}

export function getFormattedInvoiceTitle(title) {
  return title || i18n.get('暂未获取到开票方信息')
}