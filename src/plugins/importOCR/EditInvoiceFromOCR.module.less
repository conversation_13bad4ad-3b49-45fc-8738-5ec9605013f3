@import '../../styles/ekb-colors';

.edit-invoice-from-ocr-wrapper {
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  height: 100%;
  :global {
    .edit-invoice-content {
      flex: 1;
      overflow-y: auto;
      .edit-header-title {
        margin-top: 16px;
        color: var(--eui-text-caption);
        font: var(--eui-font-body-r1);
        padding-top: 32px;
        padding-left: 32px;
        padding-bottom: 8px;
        background: #ffffff;
      }
      .edit-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
        padding: 0 32px 32px;
        background: #ffffff;
        margin-bottom: 16px;
        color: #1d2b3d;

        .image {
          width: 156px;
          border-radius: 14px;
          background: var(--eui-function-danger-50, #ffece8);
          height: 156px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .edit-content {
        background: #ffffff;
        margin-bottom: 80px;
        .image {
          width: 120px;
          height: 120px;
          margin-bottom: 48px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
          }
        }
      }
    }
    .bottom-actions {
      background: #ffffff;
      flex-shrink: 0;
      display: flex;
      font-size: 32px;
      padding: 20px 16px 20px 16px;
      .delete {
        flex: 1;
        text-align: center;
        color: @black-45;
        height: 84px;
        line-height: 84px;
      }
      .save {
        flex: 1;
        border-radius: 8px;
        background-color: @primary-6;
        text-align: center;
        color: #ffffff;
        height: 84px;
        line-height: 84px;
      }
    }
  }
}
