import { Resource } from '@ekuaibao/fetch'
import key from './key'

const saveOCRInvoice = new Resource('/api/v2/invoice/ocr')

export function saveOCRInvoiceEditable(params) {
  return {
    type: key.SAVE_INVOICE_EDIT_RESULT,
    payload: saveOCRInvoice.PUT('/$invoiceId/$entityId', params)
  }
}

const orcPathMap = {
  overseasInvoice: '/overseasInvoice'
}

export function getOCRResult(params, done) {
  const { fileId, type, ...others } = params
  const path = orcPathMap[type] ?? '/multiple_items'
  return {
    type: key.GET_OCR_RESULT,
    payload: saveOCRInvoice.POST(path, others),
    fileId: fileId,
    done
  }
}
