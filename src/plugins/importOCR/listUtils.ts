/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/1/2 下午3:27.
 */
import { app } from '@ekuaibao/whispered'
// import { mergeMoney } from '../bill/utils/index'

import { MoneyMath } from '@ekuaibao/money-math'
import { get, uniq, flatten, groupBy, xorBy } from 'lodash'
// import { fnCheckedImport } from '../invoice/import-list/fnvalidator'

import { Fetch } from '@ekuaibao/fetch'
import { standardValueMoney } from '../../lib/InvoiceMappingValue'
import { isElectronicAirAndTrain } from './utils'
import { getOriginalFileDownload } from '../invoice/invoice.action'

export function initData(dataSource: any[]) {
  const disableChecked: any[] = []
  const disableData: any[] = []
  dataSource.forEach(line => {
    if (!line.isLoading) {
      line.details &&
        line.details.forEach((item: any) => {
          if (!item.master.active) {
            let details = item.details.filter((oo: any) => !oo.active)
            disableChecked.push({ key: line.groupId, value: item.master.id, valueObj: { ...item, details } })
            // @i18n-ignore
          } else if (item.master.entityId === 'system_其他' && !item.ischeck) {
            let details = item.details.filter((oo: any) => !oo.active)
            disableChecked.push({ key: line.groupId, value: item.master.id, valueObj: { ...item, details } })
          } else if (item.master.entityId === 'system_发票主体' && item.status !== 'EDIT' && !item.ischeck) {
            disableData.push({ key: line.groupId, value: item.master.id, valueObj: item })
          } else if (!item.ischeck && item.status !== 'EDIT') {
            const details = item.details?.filter((oo: any) => !oo.active)
            disableChecked.push({ key: line.groupId, value: item.master.id, valueObj: { ...item, details } })
          } else if (
            item.details[0] &&
            item.details[0].entityId === 'system_发票明细' && // @i18n-ignore
            item.status !== 'EDIT' &&
            !item.ischeck
          ) {
            disableData.push({ key: line.groupId, value: item.master.id, valueObj: item })
          }
        })
    }
  })
  return { disableChecked, disableData }
}

export function checkListName(list: any[] = []) {
  const data = { show: false, title: '' }
  for (let i = 0; i < list.length; i++) {
    const item = list[i]
    if (item?.valueObj?.master?.entityId === 'system_航空运输电子客票行程单') {
      if (!item?.valueObj?.master?.form?.E_system_航空运输电子客票行程单_乘机人姓名) {
        const title = `${item?.valueObj?.master?.form[i18n.get('E_system_航空运输电子客票行程单_出发站')]} - ${
          item?.valueObj?.master?.form[i18n.get('E_system_航空运输电子客票行程单_到达站')]
        }`
        const tagName = i18n.get('机票行程单')
        const name = i18n.get('乘机人姓名')
        const empty = i18n.get('不能为空')
        data.show = true
        data.title = `${title},${tagName}${name}${Fetch.defaultLanguage === 'en-US' ? ' ' : ''}${empty}`
        break
      }
    }

    // if (item?.valueObj?.master?.entityId === 'system_火车票') {
    //   if (!item?.valueObj?.master?.form?.E_system_火车票_乘车人姓名) {
    //     const title = `${item?.valueObj?.master?.form[i18n.get('E_system_火车票_上车车站')]} - ${
    //       item?.valueObj?.master?.form[i18n.get('E_system_火车票_下车车站')]
    //     }`
    //     const tagName = i18n.get('火车票')
    //     const name = i18n.get('乘车人姓名')
    //     const empty = i18n.get('不能为空')
    //     data.show = true
    //     data.title = `${title},${tagName}${name}${Fetch.defaultLanguage === 'en-US' ? ' ' : ''}${empty}`
    //     break
    //   }
    // }
    // if (item?.valueObj?.master?.entityId === 'system_客运汽车发票') {
    //   if (!item?.valueObj?.master?.form?.E_system_客运汽车发票_姓名) {
    //     const title = `${item?.valueObj?.master?.form[i18n.get('E_system_客运汽车发票_出发车站')]} - ${
    //       item?.valueObj?.master?.form[i18n.get('E_system_客运汽车发票_达到车站')]
    //     }`
    //     const tagName = i18n.get('客运汽车票')
    //     const name = i18n.get('乘车人姓名')
    //     const empty = i18n.get('不能为空')
    //     data.show = true
    //     data.title = `${title},${tagName}${name}${Fetch.defaultLanguage === 'en-US' ? ' ' : ''}${empty}`
    //     break
    //   }
    // }
  }
  return data
}

export function getTotalMoney(result: any[]) {
  const res = result.map((line: any) => line.valueObj)
  const orders = parseDataToSave(res)

  const { mergeMoney } = app.require('@bill/utils/OCRUtils')
  return mergeMoney(orders)
}

export function checkedAllValues(dataSource: any[] = [], disableChecked: any[], disableData?: any[]) {
  const cDisableChecked = disableData ? disableChecked.concat(disableData) : disableChecked
  const checkedResult: any = []
  dataSource.forEach(line => {
    line.details &&
      line.details.forEach((item: any) => {
        // @i18n-ignore
        const invoiceEntity = get(item, 'master.entityId', '') === 'system_发票主体'
        let isAvailable = invoiceEntity && item.status === 'EDIT'
        if (get(item, 'master.entityId', '') === 'system_非税收入类票据') {
          isAvailable = !item.ischeck
        }
        if (!isAvailable) {
          let datas = cDisableChecked.find((oo: any) => oo.value === item.master.id)
          //发票(只有发票才有不可选择的状态)
          if (datas) {
            //length相等说明整张发票不可选
            if (datas.valueObj.details.length !== item.details.length) {
              //数组取差集
              const details = xorBy(datas.valueObj.details, item.details, 'id')
              const newItem = { ...item, details }
              checkedResult.push({
                key: line.groupId,
                value: item.master.id,
                valueObj: { ...newItem, fileId: line.fileId }
              })
            }
          } else {
            //非发票
            checkedResult.push({
              key: line.groupId,
              value: item.master.id,
              valueObj: { ...item, fileId: line.fileId }
            })
          }
        }
      })
  })
  return { checkedResult }
}
export function parseSingleSaveData(value: any) {
  let amountStr = i18n.get('E_system_发票明细_金额'),
    taxAmountStr = i18n.get('E_system_发票明细_税额')

  const itemIds: any = value.details || []
  const invoiceId = value.master
  const fileId = value.fileId || {}
  const taxRate = itemIds.length ? null : invoiceId.form['E_税率'] // @i18n-ignore
  const taxAmount = itemIds.length
    ? invoiceId.form[i18n.get('E_是否抵扣')] // @i18n-ignore
      ? getTotalTaxAmount(itemIds)
      : { ...itemIds[0].form[amountStr], standard: '0' }
    : invoiceId.form['E_税额']
  return { itemIds, invoiceId, fileId, taxRate, taxAmount }
}

export function parseDataToSave(orders: any) {
  return orders.map((line: any) => {
    return parseSingleSaveData(line)
  })
}

export function getDetailsCount(dataSource: any[]) {
  let count = 0
  dataSource.forEach(line => {
    count += line.count
  })
  return count
}

export function getInvoiceEditCount(dataSource: any[]) {
  const cDataSource = dataSource.slice()
  let count = 0
  cDataSource.forEach((line: any) => {
    if (!line.isLoading) {
      line.details.forEach((item: any) => {
        if (item.status === 'EDIT') {
          count += count + 1
        }
      })
    }
  })
  return count
}

//全选时，若是增值税发票且不能编辑，要把发票明细整理一份数据用于展开选中状态
export function getInvoiceCheckValue(checkedResult: any[] = [], keys: string) {
  let invoiceItemChecked: any[] = []
  const lineChencked = checkedResult.filter(oo => oo.value === keys)
  lineChencked.forEach((item: any) => {
    // @i18n-ignore
    if (get(item, 'valueObj.master.entityId', '') === 'system_发票主体' && item.valueObj.status !== 'EDIT') {
      item.valueObj.details.forEach((oo: any) => {
        invoiceItemChecked.push({ key: keys, value: oo.id, valueObj: oo })
      })
    }
  })
  return invoiceItemChecked
}

export function getAllInvoiceDetailsCount(checkedResult: any[] = []) {
  let amountStr = i18n.get('E_system_发票明细_金额'),
    taxAmountStr = i18n.get('E_system_发票明细_税额')

  let count = uniq(checkedResult.map(oo => oo.value)) || []
  let moneys = checkedResult
    .map((line: any) => {
      const key = get(line, 'valueObj.master.entityId', '')
      // @i18n-ignore
      if (get(line, 'valueObj.master.entityId', '') === 'system_发票主体') {
        if (isElectronicAirAndTrain(line?.valueObj?.master?.form)) {
          return Number(line.valueObj.master.form['E_system_发票主体_价税合计'].standard ?? 0)
        }
        return line.valueObj.details.map((vv: any) => {
          return Number(vv.form[amountStr].standard ?? 0) + Number(vv.form[taxAmountStr].standard ?? 0)
          // return Number(new MoneyMath(vv.form[amountStr]).add(vv.form[taxAmountStr]).fixedValue.standard || 0)
        })
      } else if (key === 'system_医疗发票' || key === 'system_非税收入类票据') {
        return Number(line.valueObj.master.form[`E_${key}_金额合计`].standard)
      } else {
        return Number(line.valueObj.master.form[`E_${key}_金额`].standard)
      }
    })
    .filter(line => !NaN)
  const money = flatten(moneys)
  const total = money.reduce((a, b) => a + b, 0)
  const dimensionCurrency = app.getState()['@bill']?.dimensionCurrencyInfo
  const totalMoney = standardValueMoney(total, dimensionCurrency?.currency)
  return { count: count.length, total: totalMoney }
}

export function getIsHalfInvoice(checkedResult: any[], dataSource: any[], isFilter?: boolean) {
  let isHalfInvoice = false
  // @i18n-ignore
  let checkInvoice = checkedResult.filter(oo => get(oo, 'valueObj.master.entityId', '') === 'system_发票主体') || []
  checkInvoice.forEach((item: any) => {
    dataSource.forEach((vv: any) => {
      if (item.key === vv.groupId) {
        let invoiceDetail = vv.details.find((oo: any) => get(oo, 'master.id', '') === item.value)
        let activeInvoiceDetail = isFilter
          ? invoiceDetail.details
          : invoiceDetail.details?.filter((oo: any) => oo.active)
        if (!!invoiceDetail && item.valueObj.details?.length !== activeInvoiceDetail.length) {
          isHalfInvoice = true
        }
      }
    })
  })
  return isHalfInvoice
}

//验证发票是否重复导入(消费明细中是否已经绑定了该发票，不可重复绑定，但是本页面的继续添加不验证是否已经上传了同一张图片发票)
export async function fnCheckinvoiceImport(ocrList: any) {
  const { fnCheckedImport } = app.require('@invoice/import-list/fnvalidator')

  let invoiceList = ocrList.items.filter((oo: any) => oo.master.active)
  const promises = invoiceList.map((item: any) => fnCheckedImport(item.details.length ? item.details : [item.master]))

  return Promise.all(promises).then(result => {
    result.forEach((isImported: boolean, index: number) => {
      if (isImported) {
        const item = invoiceList[index]
        if (item.master.entityId === 'system_发票主体') {
          item.master.active = false
          item.details.forEach((vv: any) => {
            vv.active = false
          })
          item.message = i18n.get('发票已导入，不可重复导入')
          if (IS_ZJZY) item.message = i18n.get('发票已导入，不可重复导入，请先保存费用明细再上传')
        } else {
          item.isCheck = false
          item.master.active = false
          item.message = i18n.get('票据已导入，不可重复导入')
        }
      }
    })
    return invoiceList
  })
}

//判断选定的发票明细个数是否等于发票的总明细数，以此来判断是否全选(disData,disableValues同理)
export function formatLineValue(checkedValues: any = [], disData: any = [], disableValues: any = [], line: any = []) {
  // @i18n-ignore
  let invoice = line.details.filter((oo: any) => oo.master.entityId === 'system_发票主体')
  let checkedValueList = checkedValues,
    disDataList = disData,
    disableValuesList = disableValues
  let checkedStr: any = {},
    disDataStr: any = {},
    disableValuesStr: any = {}
  if (checkedValues && checkedValues.length) {
    getValueStr(checkedValues, checkedStr)
  }
  if (disData && disData.length) {
    getValueStr(disData, disDataStr)
  }
  if (disableValues && disableValues.length) {
    getValueStr(disableValues, disableValuesStr)
  }
  if (invoice.length) {
    invoice.forEach((item: any) => {
      let masterId = item.master.id
      let details = item.details.filter((oo: any) => oo.active)
      //details的length相等 说明全部都选择了
      if (checkedStr[masterId] && checkedStr[masterId].length === details.length) {
        checkedValueList = checkedValues
      } else {
        checkedValueList = fnFilterValue(checkedValues, masterId) || []
      }
      if (disDataStr[masterId] && disDataStr[masterId].length === details.length) {
        disDataList = disData
      } else {
        disDataList = fnFilterValue(disData, masterId) || []
      }
      if (disableValuesStr[masterId] && disableValuesStr[masterId].length === item.details.length) {
        disableValuesList = disableValues
      } else {
        disableValuesList = fnFilterValue(disableValues, masterId) || []
      }
    })
  }
  return { checkedValueList, disDataList, disableValuesList }
}

function getValueStr(values: any, str: any) {
  values.map((oo: any) => (str[oo.value] = oo.valueObj.details))
}

function fnFilterValue(values: any, masterId: any) {
  return values.filter((vv: any) => vv.value !== masterId)
}

//控制顶部全选按钮
export function fnInvoiceSelectAll(
  dataSource: any = [],
  checkedResult: any = [],
  disableChecked: any = [],
  disableData: any = []
) {
  let isCheckedResult = checkedResult.length ? getIsHalfInvoice(checkedResult, dataSource) : false
  let isDisableChecked = disableChecked.length ? getIsHalfInvoice(disableChecked, dataSource, true) : false
  let isDisableData = disableData.length ? getIsHalfInvoice(disableData, dataSource) : false
  return { isCheckedResult, isDisableChecked, isDisableData }
}

//checkedResult和disableChecked可能重复，去重
export function fnFilterDisableChecked(checkedResult: any = [], disableChecked: any = []) {
  if (checkedResult.length && disableChecked.length) {
    let checkedResults = groupBy(checkedResult, 'value')
    let arr: any[] = []
    disableChecked.forEach((line: any) => {
      if (!checkedResults[line.value]) {
        arr.push(line)
      }
    })
    return arr
  } else {
    return disableChecked
  }
}

//将disableChecked和checkedResult数据合并
export function formatDisableValue(disableChecked: any[], checkedResult: any[]) {
  let checkedResultStr: any = groupBy(checkedResult, 'value')
  let cDisableChecked = disableChecked.slice()
  if (cDisableChecked.length) {
    cDisableChecked.forEach((dd: any) => {
      // @i18n-ignore
      if (get(dd, 'valueObj.master.entityId') === 'system_发票主体' && checkedResultStr[dd.value]) {
        //选中的是发票明细 且有的明细上次已经选择,将两次选择的合并
        dd.valueObj.details = dd.valueObj.details.concat(checkedResultStr[dd.value][0].valueObj.details)
      } else {
        cDisableChecked = cDisableChecked.concat(checkedResult)
      }
    })
  } else {
    cDisableChecked = cDisableChecked.concat(checkedResult)
  }
  return cDisableChecked
}

export function getFileregionUrl(keys: { key: string; regions: string }) {
  return Fetch.POST(
    '/api/v1/attachment/attachments/region',
    {},
    {
      body: keys
    }
  )
}

export function getTotalTaxAmount(itemList: any[]) {
  let amountStr = i18n.get('E_system_发票明细_金额'),
    taxAmountStr = i18n.get('E_system_发票明细_税额')

  let money = itemList.map((line: any) => {
    if (line.form[taxAmountStr]) {
      return Number(line.form[taxAmountStr].standard)
    } else {
      return 0
    }
  })
  let total = money.reduce((a, b) => a + b, 0).toFixed(2)
  return { ...itemList[0].form[amountStr], standard: total }
}

export function getInvoiceTotalMoney(itemList: any[]) {
  const amountStr = i18n.get('E_system_发票明细_金额')
  const taxAmountStr = i18n.get('E_system_发票明细_税额')

  const money = itemList.map((vv: any) => {
    if (isElectronicAirAndTrain(vv.form)) {
      return Number(vv.form['E_system_发票主体_价税合计']?.standard || 0)
    }
    return Number(new MoneyMath(vv.form[amountStr]).add(vv.form[taxAmountStr]).fixedValue.standard || 0)
  })
  return money.reduce((a, b) => a + b, 0).toFixed(2)
}

export function fnOriginalFileDownload(data: any[]){
    const DIGITAL_ORIGINAL_FILE = app.getState('@common').powers.DIGITAL_ORIGINAL_FILE
    if (!DIGITAL_ORIGINAL_FILE) return
    const digitalInvoices = data.filter(line => {
      const type = get(line, 'master.form.E_system_发票主体_发票类别', '')
      return [
        'FULL_DIGITAl_SPECIAL',
        'FULL_DIGITAl_NORMAL',
        'ELECTRONIC_TRAIN_INVOICE',
        'ELECTRONIC_AIRCRAFT_INVOICE'
      ].includes(type)
    })
    if (DIGITAL_ORIGINAL_FILE && digitalInvoices.length) {
      const invoiceIds = digitalInvoices.map(v => v?.master?.id)
      getOriginalFileDownload({ invoiceIds }).catch(() => {})
    }
}