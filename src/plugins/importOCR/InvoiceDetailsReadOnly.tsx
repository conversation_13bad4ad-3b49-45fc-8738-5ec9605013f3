import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceLayer } from '@ekuaibao/enhance-layer-manager-mobile'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import { app as api } from '@ekuaibao/whispered'
// import InvoiceDetailView from '../invoice/details/invoice-detail-view'
const InvoiceDetailView = app.require<any>("@invoice/details/invoice-detail-view")
import styles from './InvoiceDetailsReadOnly.module.less'
// import EKBIcon from '../../elements/ekbIcon'
const EKBIcon = app.require<any>('@elements/ekbIcon')

@EnhanceTitleHook(i18n.get('发票详情'))
export default class InvoiceDetailsReadOnly extends PureComponent<any> {
  componentWillMount() {
    api
      .dataLoader('@common.payerInfo')
      .load()
      .then(() => {
        this.forceUpdate()
      })
  }

  render() {
    const { isShowTitle = true } = this.props
    return (
      <div className={styles['invoice-details-wrapper']}>
        <div className="invoice-content-wrapper">
          {isShowTitle && (
            <div className="header-tips">
              <EKBIcon name="#EDico-plaint-circle" className="warning" />
              <div className="tips">{i18n.get('已验真，票据不可修改')}</div>
            </div>
          )}
          {isShowTitle && (
            <div className="invoice-img">
              <img />
            </div>
          )}
          <div className="invoice-content">
            <InvoiceDetailView {...this.props} />
          </div>
        </div>
        <div className="bottom" onClick={() => this.props.layer.emitCancel()}>
          {i18n.get('返  回')}
        </div>
      </div>
    )
  }
}
