/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/11 下午2:23.
 */
import info, { overseasInvoiceInfo, pagesInfo } from "./fnConfig";
import { openOCR } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { getOCRResult } from './importOCR.action'
import loadable from '@loadable/component'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { Fetch } from '@ekuaibao/fetch'

export default [
  {
    id: '@invoiceOCR',
    reducer: () => require('./importOCR.reducer').default,
    'open:ocr': param => {
      return openOCR(param)
    },
    'get:ocr:data': param => {
      return new Promise((resolve, reject) => {
        api.dispatch(
          getOCRResult(param, (state, action) => {
            action.error ? reject(action.payload) : resolve(state)
          })
        )
      })
    }
  },
  {
    point: '@bill:third:import',
    onload: app => {
      const powers = app.getState('@common').powers ?? {}
      const importArray = []
      if (powers.OCR || powers.OCRMetering) {
        importArray.push(info())
      }
      // if (app.getState('@common').powers.OCRMedical) {
      //   importArray.push(medicalInfo())
      // }
      if (isHongShanTestingEnterprise(Fetch.ekbCorpId) && window.isIOS) {
        importArray.push(pagesInfo())
      }
      if (powers.OverseasInvoice) {
        importArray.push(overseasInvoiceInfo())
      }
      return importArray
    }
  },
  {
    point: '@invoice-form:invoice:import',
    onload: app => {
      const powers = app.getState('@common').powers ?? {}
      const importArray = []
      if (powers.OCR || powers.OCRMetering) {
        importArray.push(info())
      }
      // if (app.getState('@common').powers.OCRMedical) {
      //   importArray.push(medicalInfo())
      // }
      if (isHongShanTestingEnterprise(Fetch.ekbCorpId) && window.isIOS) {
        importArray.push(pagesInfo())
      }
      if (powers.OverseasInvoice) {
        importArray.push(overseasInvoiceInfo())
      }
      return importArray
    }
  },
  {
    point: '@withNode:import',
    onload: app => {
      const powers = app.getState('@common').powers ?? {}
      const importArray = []
      if (powers.OCR || powers.OCRMetering) {
        importArray.push(info())
      }
      // if (app.getState('@common').powers.OCRMedical) {
      //   importArray.push(medicalInfo())
      // }
      if (isHongShanTestingEnterprise(Fetch.ekbCorpId) && window.isIOS) {
        importArray.push(pagesInfo())
      }
      if (powers.OverseasInvoice) {
        importArray.push(overseasInvoiceInfo())
      }
      return importArray
    }
  },
  {
    point: '@@layers',
    prefix: '@invoiceOCR',
    onload: () => require('./layers').default
  },
  {
    resource: '@invoiceOCR',
    value: {
      ['UploadOCRPortal']: loadable(() => import('./UploadOCRPortal')),
      ['listUtils']: require('./listUtils')
    }
  }
]
