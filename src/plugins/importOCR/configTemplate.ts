/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/1/22 上午10:03.
 */
export const invoiceTemplate = [
  {
    name: i18n.get('E_system_发票主体_发票代码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票代码'),
    label: i18n.get('发票代码'),
    optional: true,
    isvalidatorLev: 1
  },
  {
    name: i18n.get('E_system_发票主体_发票号码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票号码'),
    label: i18n.get('发票号码'),
    optional: false,
    isvalidatorLev: 1
  },
  {
    name: i18n.get('E_system_发票主体_发票日期'),
    type: 'date',
    editable: true,
    optionalDefaultValue: true,
    placeholder: i18n.get('请选择开票时间'),
    label: i18n.get('开票时间'),
    optional: false
  }
]

export const invoiceMoney = (optional: boolean = false) => ({
  name: i18n.get('E_system_发票主体_发票金额'),
  type: 'money',
  editable: true,
  placeholder: i18n.get('请输入金额'),
  label: i18n.get('金额'),
  optional
})

export const invoiceJYM = (isBlockChain: boolean, optional: boolean = false) => ({
  name: i18n.get('E_system_发票主体_校验码'),
  type: 'text',
  editable: true,
  placeholder: isBlockChain ? i18n.get('请输入校验码') : i18n.get('请输入校验码末6位'),
  label: i18n.get('校验码'),
  optional,
  isvalidatorLev: 1
})

export const train = [
  {
    name: i18n.get('E_system_火车票_乘车人姓名'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入乘车人姓名'),
    label: i18n.get('乘车人姓名'),
    optional: true
  },
  {
    name: i18n.get('E_system_火车票_车次'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入车次'),
    label: i18n.get('车次'),
    optional: true
  },
  {
    name: i18n.get('E_system_火车票_座位类型'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请选择席别'),
    label: i18n.get('席别'),
    optional: true
  },
  {
    name: i18n.get('E_system_火车票_乘车时间'),
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择乘车时间'),
    label: i18n.get('乘车时间'),
    withTime: true,
    optional: true
  },
  {
    name: i18n.get('E_system_火车票_上车车站'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请选择始发地'),
    label: i18n.get('始发地'),
    optional: true
  },
  {
    name: i18n.get('E_system_火车票_下车车站'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请选择目的地'),
    label: i18n.get('目的地'),
    optional: true
  },
  {
    name: i18n.get('E_system_火车票_金额'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入总金额'),
    label: i18n.get('总金额'),
    isIgnore: true,
    optional: true
  },
  {
    name: i18n.get('E_system_火车票_号码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入车票号'),
    label: i18n.get('车票号'),
    optional: true
  }
]

export const airplane = [
  {
    name: i18n.get('E_system_航空运输电子客票行程单_乘机人姓名'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入乘机人姓名'),
    label: i18n.get('乘机人姓名'),
    optional: false
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_电子客票号码'),
    type: 'text',
    placeholder: i18n.get('请输入电子客票号码'),
    label: i18n.get('电子客票号码'),
    editable: true,
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_航班号'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入航班号'),
    label: i18n.get('航班号'),
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_座位等级'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请选择席别'),
    label: i18n.get('席别'),
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_乘机时间'),
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择乘机时间'),
    label: i18n.get('乘机时间'),
    withTime: true,
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_填开日期'),
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择填开日期'),
    label: i18n.get('填开日期'),
    withTime: false,
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_出发站'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入出发站'),
    label: i18n.get('出发站'),
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_到达站'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入到达站'),
    label: i18n.get('到达站'),
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_金额'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入总金额'),
    label: i18n.get('总金额'),
    isIgnore: true,
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_票价'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入票价'),
    label: i18n.get('票价'),
    isIgnore: true,
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_税费'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入税费'),
    label: i18n.get('税费'),
    isIgnore: true,
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_燃油附加费'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入燃油附加费'),
    label: i18n.get('燃油附加费'),
    isIgnore: true,
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_保险费'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入保险费'),
    label: i18n.get('保险费'),
    isIgnore: true,
    optional: true
  },
  {
    name: i18n.get('E_system_航空运输电子客票行程单_民航发展基金'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入民航发展基金'),
    label: i18n.get('民航发展基金'),
    isIgnore: true,
    optional: true
  }
]

export const taxi = [
  {
    name: i18n.get('E_system_出租车票_发票代码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票代码'),
    label: i18n.get('发票代码'),
    optional: true
  },
  {
    name: i18n.get('E_system_出租车票_发票号码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票号码'),
    label: i18n.get('发票号码'),
    optional: true
  },
  {
    name: i18n.get('E_system_出租车票_车牌号码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入车牌号码'),
    label: i18n.get('车牌号码'),
    optional: true
  },
  {
    name: i18n.get('E_system_出租车票_上车时间'),
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择上车时间'),
    label: i18n.get('上车时间'),
    withTime: true,
    optional: true
  },
  {
    name: i18n.get('E_system_出租车票_下车时间'),
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择下车时间'),
    label: i18n.get('下车时间'),
    withTime: true,
    optional: true
  },
  {
    name: i18n.get('E_system_出租车票_里程'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入里程'),
    label: i18n.get('里程'),
    optional: true
  },
  {
    name: i18n.get('E_system_出租车票_金额'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入总金额'),
    label: i18n.get('总金额'),
    isIgnore: true,
    optional: true
  },
  {
    name: i18n.get('E_system_出租车票_发票所在地'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请选择所在地'),
    label: i18n.get('所在地'),
    optional: true
  }
]

export const bus = [
  {
    name: i18n.get('E_system_客运汽车发票_发票代码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票代码'),
    label: i18n.get('发票代码'),
    optional: true
  },
  {
    name: i18n.get('E_system_客运汽车发票_发票号码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票号码'),
    label: i18n.get('发票号码'),
    optional: true
  },
  {
    name: i18n.get('E_system_客运汽车发票_时间'),
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择乘车时间'),
    label: i18n.get('乘车时间'),
    withTime: true,
    optional: true
  },
  {
    name: i18n.get('E_system_客运汽车发票_出发车站'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入出发车站'),
    label: i18n.get('出发车站'),
    optional: true
  },
  {
    name: i18n.get('E_system_客运汽车发票_达到车站'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入到达车站'),
    label: i18n.get('到达车站'),
    optional: true
  },
  {
    name: i18n.get('E_system_客运汽车发票_金额'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入总金额'),
    label: i18n.get('总金额'),
    isIgnore: true,
    optional: true
  },
  {
    name: i18n.get('E_system_客运汽车发票_姓名'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入乘车人姓名'),
    label: i18n.get('乘车人姓名'),
    optional: true
  }
]

const Tolls = [
  {
    name: i18n.get('E_system_过路费发票_发票代码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票代码'),
    label: i18n.get('发票代码'),
    optional: true
  },
  {
    name: i18n.get('E_system_过路费发票_发票号码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票号码'),
    label: i18n.get('发票号码'),
    optional: true
  },
  {
    name: i18n.get('E_system_过路费发票_时间'),
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择时间'),
    label: i18n.get('时间'),
    withTime: true,
    optional: true
  },
  {
    name: i18n.get('E_system_过路费发票_入口'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入入口'),
    label: i18n.get('入口'),
    optional: true
  },
  {
    name: i18n.get('E_system_过路费发票_出口'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入出口'),
    label: i18n.get('出口'),
    optional: true
  },
  {
    name: i18n.get('E_system_过路费发票_金额'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入总金额'),
    label: i18n.get('总金额'),
    isIgnore: true,
    optional: true
  }
]

const Quota = [
  {
    name: i18n.get('E_system_定额发票_发票代码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票代码'),
    label: i18n.get('发票代码'),
    optional: true
  },
  {
    name: i18n.get('E_system_定额发票_号码'),
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入发票号码'),
    label: i18n.get('发票号码'),
    optional: true
  },
  {
    name: i18n.get('E_system_定额发票_金额'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入总金额'),
    label: i18n.get('总金额'),
    isIgnore: true,
    optional: true
  }
]

const others = [
  {
    name: i18n.get('E_system_其他_日期'),
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择日期'),
    label: i18n.get('日期'),
    optional: true
  },
  {
    name: i18n.get('E_system_其他_金额'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入总金额'),
    label: i18n.get('总金额'),
    isIgnore: true,
    optional: true
  }
]

const overseas = [
  {
    name: 'E_system_海外发票_title',
    type: 'text',
    editable: true,
    placeholder: i18n.get('请填写消费标题'),
    label: i18n.get('消费标题'),
    optional: false
  },
  {
    name: 'E_system_海外发票_日期',
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择日期'),
    label: i18n.get('日期'),
    optional: true
  },
  {
    name: 'E_system_海外发票_金额',
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入合计金额'),
    label: i18n.get('合计金额'),
    isIgnore: true,
    optional: true,
    hiddenRate: true,
    hiddenLocal: true
  },
  {
    name: i18n.get('E_system_海外发票_税额'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入税额'),
    label: i18n.get('税额'),
    isIgnore: true,
    optional: true,
    hiddenRate: true,
    hiddenLocal: true
  },
  {
    name: i18n.get('E_system_海外发票_不计税金额'),
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入不计税金额'),
    label: i18n.get('不计税金额'),
    isIgnore: true,
    optional: true,
    hiddenRate: true,
    hiddenLocal: true
  },
  {
    name: 'E_system_海外发票_税率',
    type: 'number',
    editable: true,
    placeholder: i18n.get('请填写税率'),
    label: i18n.get('税率'),
    dataType: { scale: 2, type: 'number', unit: '%' },
    optional: true
  },
  {
    name: 'E_system_海外发票_消费国家',
    type: 'text',
    editable: true,
    maxLength: 144,
    minLength: 0,
    placeholder: i18n.get('请填写消费国家'),
    label: i18n.get('消费国家'),
    optional: true
  },
  {
    name: 'E_system_海外发票_票据号码',
    type: 'text',
    editable: true,
    maxLength: 144,
    minLength: 0,
    placeholder: i18n.get('请填写票据号码'),
    label: i18n.get('票据号码'),
    optional: true
  },
  {
    name: 'E_system_海外发票_购买方名称',
    type: 'text',
    editable: true,
    maxLength: 144,
    minLength: 0,
    placeholder: i18n.get('请填写购买方名称'),
    label: i18n.get('购买方名称'),
    optional: true
  },
  {
    name: 'E_system_海外发票_购买方纳税人识别号',
    type: 'text',
    editable: true,
    maxLength: 144,
    minLength: 0,
    placeholder: i18n.get('请填写购买方纳税人识别号'),
    label: i18n.get('购买方纳税人识别号'),
    optional: true
  },
  {
    name: 'E_system_海外发票_销售方名称',
    type: 'text',
    maxLength: 144,
    minLength: 0,
    editable: true,
    placeholder: i18n.get('请填写销售方名称'),
    label: i18n.get('销售方名称'),
    optional: true
  },
  {
    name: 'E_system_海外发票_销售方纳税人识别号',
    type: 'text',
    editable: true,
    maxLength: 144,
    minLength: 0,
    placeholder: i18n.get('请填写销售方纳税人识别号'),
    label: i18n.get('销售方纳税人识别号'),
    optional: true
  }
]

const shopping = [
  {
    name: 'E_system_消费小票_店名', // @i18n-ignore
    type: 'text',
    editable: true,
    placeholder: i18n.get('请输入店名'),
    label: i18n.get('店名'),
    optional: false
  },
  {
    name: 'E_system_消费小票_金额', // @i18n-ignore
    type: 'money',
    editable: true,
    placeholder: i18n.get('请输入金额'),
    label: i18n.get('金额'),
    optional: false
  },
  {
    name: 'E_system_消费小票_时间', // @i18n-ignore
    type: 'date',
    editable: true,
    placeholder: i18n.get('请选择时间'),
    label: i18n.get('时间'),
    optional: true,
    withTime: true
  }
]

const medical: any[] = [
  {
    name: `E_system_医疗发票_票据代码`, // @i18n-ignore
    label: i18n.get('票据代码'),
    maxLength: 20,
    type: 'text',
    optional: false,
    editable: true
  },
  {
    name: `E_system_医疗发票_票据号码`, // @i18n-ignore
    label: i18n.get('票据号码'),
    type: 'text',
    optional: false,
    editable: true
  },
  {
    name: 'E_system_医疗发票_校验码',
    label: i18n.get('校验码'),
    type: 'text',
    optional: true,
    editable: true
  },
  {
    name: 'E_system_医疗发票_开票日期',
    label: i18n.get('开票日期'),
    type: 'date',
    optional: true,
    editable: true,
    withTime: false
  },
  {
    name: 'E_system_医疗发票_金额合计',
    label: i18n.get('金额合计'),
    type: 'money',
    optional: true,
    editable: true
  }
]

const nontax = [
  {
    name: `E_system_非税收入类票据_收款单位`, // @i18n-ignore
    label: i18n.get('收款单位'),
    type: 'text',
    optional: false,
    editable: true
  },
  {
    name: `E_system_非税收入类票据_票据代码`, // @i18n-ignore
    label: i18n.get('票据代码'),
    maxLength: 20,
    type: 'text',
    optional: false,
    editable: true
  },
  {
    name: `E_system_非税收入类票据_票据号码`, // @i18n-ignore
    label: i18n.get('票据号码'),
    type: 'text',
    optional: false,
    editable: true
  },
  {
    name: 'E_system_非税收入类票据_校验码',
    label: i18n.get('校验码'),
    type: 'text',
    optional: true,
    editable: true
  },
  {
    name: 'E_system_非税收入类票据_开票日期',
    label: i18n.get('开票日期'),
    type: 'date',
    optional: true,
    editable: true,
    withTime: false
  },
  {
    name: 'E_system_非税收入类票据_金额合计',
    label: i18n.get('金额合计'),
    type: 'money',
    optional: true,
    editable: true
  }
]

const machine = [
  {
    name: `E_system_机打发票_发票代码`, // @i18n-ignore
    label: i18n.get('发票代码'),
    maxLength: 20,
    type: 'text',
    optional: false,
    editable: true
  },
  {
    name: `E_system_机打发票_发票号码`, // @i18n-ignore
    label: i18n.get('发票号码'),
    type: 'text',
    optional: false,
    editable: true
  },
  {
    name: 'E_system_机打发票_时间',
    label: i18n.get('开票时间'),
    type: 'date',
    optional: true,
    editable: true,
    withTime: true
  },
  {
    name: 'E_system_机打发票_校验码',
    label: i18n.get('校验码'),
    type: 'text',
    optional: true,
    editable: true
  },
  {
    name: 'E_system_机打发票_金额',
    label: i18n.get('发票金额'),
    type: 'money',
    optional: true,
    editable: true
  },
  {
    name: 'E_system_机打发票_销售方名称',
    label: i18n.get('销售方名称'),
    type: 'text',
    optional: true,
    editable: true
  },
  {
    name: 'E_system_机打发票_销售方税号',
    label: i18n.get('销售方税号'),
    type: 'text',
    optional: true,
    editable: true
  },
  {
    name: 'E_system_机打发票_购买方名称',
    label: i18n.get('购买方名称'),
    type: 'text',
    optional: true,
    editable: true
  },
  {
    name: 'E_system_机打发票_购买方税号',
    label: i18n.get('购买方税号'),
    type: 'text',
    optional: true,
    editable: true
  }
]

export const OCRInvoiceMap: any = {
  system_火车票: train,
  system_航空运输电子客票行程单: airplane,
  system_出租车票: taxi,
  system_客运汽车发票: bus,
  system_过路费发票: Tolls,
  system_定额发票: Quota,
  system_其他: others,
  system_医疗发票: medical,
  system_非税收入类票据: nontax,
  system_消费小票: shopping,
  system_机打发票: machine,
  system_海外发票: overseas
}

export const OCRInvoiceTitleForCN: any = {
  system_火车票: i18n.get('铁路客票'),
  system_航空运输电子客票行程单: i18n.get('飞机票'),
  system_出租车票: i18n.get('出租车票'),
  system_客运汽车发票: i18n.get('客运汽车票'),
  system_过路费发票: i18n.get('过路费发票'),
  system_定额发票: i18n.get('定额发票'),
  system_其他: i18n.get('其他发票'),
  system_消费小票: i18n.get('消费小票'),
  system_机打发票: i18n.get('机打发票'),
  system_海外发票: i18n.get('海外发票')
}

export const OCRInvoiceTitleForEN: any = {
  system_火车票: 'Train Ticket',
  system_出租车票: 'Taxi Ticket',
  system_航空运输电子客票行程单: 'Air Ticket',
  system_客运汽车发票: 'Passenger Ticket',
  system_过路费发票: 'Invoice For Tolls',
  system_定额发票: 'Quota Invoice',
  system_其他: 'Other Invoice',
  system_消费小票: 'Shopping Invoice',
  system_机打发票: 'Machine Invoice',
  system_海外发票: 'Overseas Invoice'
}
