/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/11 下午2:58.
 */

export default [
  {
    key: 'ImportOCRListView',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../OCRListView')
  },
  {
    key: 'InvoiceDetailReadOnly',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../InvoiceDetailsReadOnly')
  },
  {
    key: 'InvoiceDetailEdit',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../InvoiceDetailsEdit')
  },
  {
    key: 'EditInvoiceFromOCR',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../EditInvoiceFromOCR')
  },
  {
    key: 'PreviewOCRImageModal',
    enhancer: 'layer',
    enhancerOptions: { className: 'debugger_top_fix' },
    getComponent: () => import('../PreviewOCRImageModal')
  }
]
