@import '../../styles/ekb-colors';
.invoice-edit-wrapper {
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  height: 100%;
  :global {
    .invoice-content {
      flex: 1;
      overflow-y: auto;
      .invoice-header {
        flex-shrink: 0;
        margin: 16px;
        padding-bottom: 16px;
        background: radial-gradient(transparent 0px, transparent 12px, white 8px, white);
        background-size: 40px 36px;
        background-position: -6px -6px;
        .invoice-header-content {
          padding: 0 34px 12px 30px;
          background-color: #fff;
          .header {
            width: 100%;
            display: flex;
            position: relative;
            height: 108px;
            .title {
              position: absolute;
              font-size: 32px;
              font-weight: 600;
              color: #d37b45;
              height: 24px;
              line-height: 24px;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
            .stamp_big {
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 120px;
              height: 64px;
              img {
                width: 100%;
                height: 100%;
              }
            }
          }
        }

        .line {
          border-bottom: solid 2px rgba(29, 43, 61, 0.09);
        }
        .invoice-detials {
          margin-top: 24px;
          .invoice-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            div:first-child {
              font-size: 28px;
              color: #c07f56;
              width: 120px;
              margin-right: 78px;
            }
            div:last-child {
              font-size: 28px;
              color: #1d2b3d;
            }
          }
        }
      }
      .invoice-modify {
        padding: 24px 0 0 32px;
        background: #ffffff;
        margin-bottom: 80px;
        .edit-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #1d2b3d;
          padding-right: 32px;
          .image {
            width: 120px;
            height: 120px;
            margin-bottom: 24px;
            img {
              width: 120px;
              height: 120px;
              border-radius: 8px;
            }
          }
        }
        .modify-content > div > div:last-child > div {
          border: none;
        }
      }
    }
    .bottom-actions {
      background: #ffffff;
      flex-shrink: 0;
      display: flex;
      font-size: 32px;
      padding: 8px 16px 76px 16px;
      .delete {
        flex: 1;
        text-align: center;
        color: @black-45;
        height: 84px;
        line-height: 84px;
      }
      .save {
        flex: 1;
        border-radius: 8px;
        background-color: @primary-6;
        text-align: center;
        color: #ffffff;
        height: 84px;
        line-height: 84px;
      }
    }
  }
}
