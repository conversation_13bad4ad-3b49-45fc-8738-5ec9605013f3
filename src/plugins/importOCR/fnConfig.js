/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/11 下午3:04.
 */
import { app } from '@ekuaibao/whispered'
// import UploadOCRPortal from './UploadOCRPortal'
import SVG_PHOTO from './images/photo.svg'
import SVG_MEDICAL from './images/medical.svg'
import SVG_ATTACHMENT from './images/attachment.svg'
//import { fnCheckCorporationInfo } from '../invoice/utils'

import { openOCR } from './utils'
// import { fnInvoicepathTrack } from '../invoice/utils/invoiceTrack'
import loadable from '@loadable/component'
import { OutlinedOtherOverseasBills } from '@hose/eui-icons'

export const info = () => ({
  icon: SVG_PHOTO,
  title: i18n.get('智能识票'),
  type: 'invoiceOCR',
  enterType: 'invoiceOCR',
  component: loadable(() => import('./UploadOCRPortal')),
  fnIsLoadComponent: () => document.getElementById('UploadAttachmentPortal'),
  weight: 10,
  onClick: function(...args) {
    let params = {}
    args.forEach(line => {
      params = { ...line, ...params }
    })

    const { fnInvoicepathTrack } = app.require('@invoice/utils/invoiceTrack')

    fnInvoicepathTrack(params.submitter, params.importWay, 'ocr')

    const { fnCheckCorporationInfo } = app.require('@invoice/utils/checkInvoice')

    return fnCheckCorporationInfo().then(result => {
      const { type } = result || {}
      if (type === 'suc') {
        return openOCR(params)
      }
    })
  }
})

// 海外票据识别
export const overseasInvoiceInfo = () => ({
  icon: <OutlinedOtherOverseasBills color="#8445d0" className="fs-20 mr-5" />,
  isIcon:true,
  title: i18n.get('海外票据识别'),
  type: 'overseasInvoice',
  enterType: 'overseasInvoice',
  component: loadable(() => import('./UploadOCRPortal')),
  fnIsLoadComponent: () => document.getElementById('UploadAttachmentPortal'),
  weight: 70,
  onClick: function(...args) {
    let params = {}
    args.forEach(line => {
      params = { ...line, ...params }
    })

    const { fnInvoicepathTrack } = app.require('@invoice/utils/invoiceTrack')

    fnInvoicepathTrack(params.submitter, params.importWay, 'ocr')

    const { fnCheckCorporationInfo } = app.require('@invoice/utils/checkInvoice')

    return fnCheckCorporationInfo().then(result => {
      const { type } = result || {}
      if (type === 'suc') {
        return openOCR(params)
      }
    })
  }
})

export const medicalInfo = () => ({
  icon: SVG_MEDICAL,
  title: i18n.get('医疗发票'),
  type: 'medicalInvoiceOCR',
  enterType: 'medicalInvoiceOCR',
  component: loadable(() => import('./UploadOCRPortal')),
  fnIsLoadComponent: () => document.getElementById('UploadAttachmentPortal'),
  weight: 10,
  onClick: function(...args) {
    let params = {}
    args.forEach(line => {
      params = { ...line, ...params, notShowModalIfAllInvoiceSuccess: false }
    })

    const { fnInvoicepathTrack } = app.require('@invoice/utils/invoiceTrack')

    fnInvoicepathTrack(params.submitter, params.importWay, 'ocr')

    const { fnCheckCorporationInfo } = app.require('@invoice/utils/checkInvoice')

    return fnCheckCorporationInfo().then(result => {
      const { type } = result || {}
      if (type === 'suc') {
        return openOCR(params)
      }
    })
  }
})

// 文稿导入
export const pagesInfo = () => ({
  icon: SVG_ATTACHMENT,
  title: i18n.get('智能文件识别'),
  type: 'invoiceFileOCR',
  enterType: 'invoiceFileOCR',
  component: loadable(() => import('./UploadOCRPortal')),
  fnIsLoadComponent: () => document.getElementById('UploadAttachmentPortal'),
  weight: 10,
  onClick: function(...args) {
    let params = {}
    args.forEach(line => {
      params = { ...line, ...params }
    })

    const { fnInvoicepathTrack } = app.require('@invoice/utils/invoiceTrack')

    fnInvoicepathTrack(params.submitter, params.importWay, 'ocr')

    const { fnCheckCorporationInfo } = app.require('@invoice/utils/checkInvoice')

    return fnCheckCorporationInfo().then(result => {
      const { type } = result || {}
      if (type === 'suc') {
        return openOCR(params)
      }
    })
  }
})

export default info
