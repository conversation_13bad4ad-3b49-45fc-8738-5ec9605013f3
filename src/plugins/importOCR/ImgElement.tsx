/**
 *  Created by gym on 2020-06-16 14:32.
 */
import React, { PureComponent } from 'react'
// import MarkupImage from '@ekuaibao/markup-image'

interface StateInterface {
  markupUrl: string
  markups: any[]
  orientation?: number
}

export default class ImageElementCard extends PureComponent<any, StateInterface> {
  constructor(props: any) {
    super(props)
    const { regions = [], attachment } = props
    const markups = regions.map((region: number[]) => {
      return { region }
    })
    this.state = { markupUrl: attachment && attachment.thumbUrl, markups }
  }

  handleImageLoaded = (url: string) => {
    this.setState({ markupUrl: url })
  }

  handlePreviewImage = () => {
    const { handleShowImg } = this.props
    const { markupUrl } = this.state
    handleShowImg && handleShowImg(markupUrl)
  }

  render() {
    // let { attachment } = this.props
    const { markups, markupUrl } = this.state
    // attachment = attachment || { url: '' }
    return (
      <div onClick={this.handlePreviewImage}>
        <img src={markupUrl} />
        {/*<MarkupImage url={attachment.url} markups={markups} onImageLoadFinshed={this.handleImageLoaded} />*/}
      </div>
    )
  }
}
