/**
 *  Created by gym on 2020/6/11 14:53.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceLayer } from '@ekuaibao/enhance-layer-manager-mobile'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'

// @ts-ignore
@EnhanceTitleHook(props => {
  return i18n.get('识别的图片')
})
export default class EditInvoiceFormOCR extends PureComponent<any, any> {
  render() {
    const { markupUrl } = this.props
    return (
      <div>
        <img src={markupUrl} alt="" style={{ width: '100%', height: '100%' }} />
      </div>
    )
  }
}
