/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/28 下午5:33.
 */
export const mockData = [
  {
    groupId: '1',
    count: 1,
    details: [
      { id: '11', type: 'train', money: 1, active: true },
      { id: '12', type: 'invoice', checkResult: true, money: 1, active: true },
      { id: '13', type: 'taxi', money: 1, active: true }
    ]
  },
  {
    groupId: '2',
    count: 2,
    details: [
      { id: '21', type: 'loadcost', money: 1, active: true },
      { id: '22', type: 'others', money: 1, active: true },
      { id: '23', type: 'train', money: 1, active: true }
    ]
  },
  {
    groupId: '3',
    count: 3,
    details: [
      { id: '31', type: 'error', checkResult: false, money: 1, disabled: true, active: false },
      { id: '32', type: 'loadcost', money: 1, active: true },
      { id: '34', type: 'train', money: 1, active: true }
    ]
  }
]

export const mockData1 = {
  items: [
    {
      master: {
        id: 'HvE8mx7Ll80k00:032001500111:45119118',
        code: '032001500111:45119118',
        form: {
          E_system_发票主体_code: '032001500111',
          E_system_发票主体_name: '45119118',
          E_system_发票主体_备注: '',
          E_system_发票主体_来源: 'UPLOAD',
          E_system_发票主体_税额: {
            standard: '2.58',
            standardUnit: i18n.get('元'),
            standardScale: '2',
            standardSymbol: '¥',
            standardNumCode: '156',
            standardStrCode: 'CNY'
          },
          E_system_发票主体_价税合计: {
            standard: '45.50',
            standardUnit: i18n.get('元'),
            standardScale: '2',
            standardSymbol: '¥',
            standardNumCode: '156',
            standardStrCode: 'CNY'
          },
          E_system_发票主体_发票代码: '032001500111',
          E_system_发票主体_发票号码: '45119118',
          E_system_发票主体_发票日期: 1530460800000,
          E_system_发票主体_发票类别: 'DIGITAL_NORMAL',
          E_system_发票主体_发票金额: {
            standard: '42.92',
            standardUnit: i18n.get('元'),
            standardScale: '2',
            standardSymbol: '¥',
            standardNumCode: '156',
            standardStrCode: 'CNY'
          },
          E_system_发票主体_购买方名称: i18n.get('南京合思国际旅游有限公司'),
          E_system_发票主体_销售方名称: i18n.get('南京肯德基有限公司'),
          E_system_发票主体_购买方纳税人识别号: '91320105MA1MULYY44',
          E_system_发票主体_销售方纳税人识别号: '9132010060891824XW'
        },
        name: '45119118',
        index: 0,
        active: true,
        source: 'WRITE',
        version: 1,
        entityId: i18n.get('system_发票主体'),
        masterId: null,
        useCount: 0,
        nameSpell: '45119118',
        createTime: 1548144357644,
        flowCounts: {},
        platformId: i18n.get('system_发票扩展'),
        totalCount: 1,
        updateTime: 1548144357644,
        visibility: {
          roles: null,
          staffs: null,
          departments: null,
          fullVisible: true,
          departmentsIncludeChildren: true
        },
        corporationId: 'HvE8mx7Ll80k00'
      },
      details: [
        {
          id: '-Xo8xQ0B-U4w00',
          code: '-Xo8xQ0B-U4w00',
          form: {
            E_system_发票明细_code: '-Xo8xQ0B-U4w00',
            E_system_发票明细_name: i18n.get('*畜禽产品*百年栗园1.35kg 30枚柴鸡蛋'),
            E_system_发票明细_单价: {
              standard: '31.80',
              standardUnit: i18n.get('元'),
              standardScale: '2',
              standardSymbol: '¥',
              standardNumCode: '156',
              standardStrCode: 'CNY'
            },
            E_system_发票明细_数量: '1',
            E_system_发票明细_税率: '0%',
            E_system_发票明细_税额: {
              standard: '0.00',
              standardUnit: i18n.get('元'),
              standardScale: '2',
              standardSymbol: '¥',
              standardNumCode: '156',
              standardStrCode: 'CNY'
            },
            E_system_发票明细_金额: {
              standard: '31.80',
              standardUnit: i18n.get('元'),
              standardScale: '2',
              standardSymbol: '¥',
              standardNumCode: '156',
              standardStrCode: 'CNY'
            }
          },
          name: i18n.get('*畜禽产品*百年栗园1.35kg 30枚柴鸡蛋'),
          index: 0,
          active: true,
          source: 'WRITE',
          version: 1,
          entityId: i18n.get('system_发票明细'),
          masterId: 'IU48mx7Ll80c00:011001800311:05686069',
          useCount: 0,
          nameSpell: '*CHUQINCHANPIN*BAINIANLIYUAN1.35kg 30MEICHAIJIDAN',
          createTime: 1547805644984,
          flowCounts: {},
          platformId: i18n.get('system_发票扩展'),
          totalCount: 1,
          updateTime: 1547805644984,
          visibility: {
            roles: null,
            staffs: null,
            departments: null,
            fullVisible: true,
            departmentsIncludeChildren: true
          },
          corporationId: 'IU48mx7Ll80c00'
        }
      ],
      ischeck: false,
      message: 'message',
      status: 'NO_RESULT'
    },
    {
      master: {
        id: 'HvEsdfdsfx7Ll80k00:032001500111:45119118',
        code: '032001500111:45119118',
        form: {},
        active: true,
        source: 'WRITE',
        version: 1,
        entityId: i18n.get('system_火车票'),
        masterId: null,
        useCount: 0,
        nameSpell: '45119118',
        createTime: 1548144357644,
        flowCounts: {},
        platformId: i18n.get('system_发票扩展'),
        totalCount: 1,
        updateTime: 1548144357644,
        visibility: {
          roles: null,
          staffs: null,
          departments: null,
          fullVisible: true,
          departmentsIncludeChildren: true
        },
        corporationId: 'HvE8mx7Ll80k00'
      },
      details: [],
      ischeck: false,
      message: 'message',
      status: 'NO_RESULT'
    }
  ],
  items1: [
    {
      master: {
        flowCounts: {},
        form: {
          E_system_发票主体_code: '011001800311',
          E_system_发票主体_name: '47824182',
          E_system_发票主体_图片: 'applet-ekb-web2-1548840530801-967-.jpg',
          E_system_发票主体_备注: i18n.get('门店编号：20199 北京新中关店'),
          E_system_发票主体_来源: 'OCR',
          E_system_发票主体_税额: {
            standard: '10.64',
            standardUnit: i18n.get('元'),
            standardScale: '2',
            standardSymbol: '¥',
            standardNumCode: '156',
            standardStrCode: 'CNY'
          },
          E_system_发票主体_价税合计: {
            standard: '188.00',
            standardUnit: i18n.get('元'),
            standardScale: '2',
            standardSymbol: '¥',
            standardNumCode: '156',
            standardStrCode: 'CNY'
          },
          E_system_发票主体_发票代码: '011001800311',
          E_system_发票主体_发票号码: '47824182',
          E_system_发票主体_发票日期: 1540224000000,
          E_system_发票主体_发票类别: 'DIGITAL_NORMAL',
          E_system_发票主体_发票金额: {
            standard: '177.36',
            standardUnit: i18n.get('元'),
            standardScale: '2',
            standardSymbol: '¥',
            standardNumCode: '156',
            standardStrCode: 'CNY'
          },
          E_system_发票主体_识别范围: [0, 0, 1440, 1080],
          E_system_票据来源_sourceEntityId: 'qgg8BJdAfQeI00',
          E_system_发票主体_购买方名称: i18n.get('北京合思信息技术有限公司'),
          E_system_发票主体_销售方名称: i18n.get('华联咖世家（北京）餐饮管理有限公司'),
          E_system_发票主体_购买方纳税人识别号: '91110108318283928K',
          E_system_发票主体_销售方纳税人识别号: '9111000067055263X2'
        },
        totalCount: 1,
        useCount: 0,
        entityId: i18n.get('system_发票主体'),
        platformId: i18n.get('system_发票扩展'),
        source: 'WRITE',
        masterId: null,
        index: 0,
        visibility: {
          fullVisible: true,
          staffs: null,
          roles: null,
          departments: null,
          departmentsIncludeChildren: true
        },
        id: 'IU48mx7Ll80c00:011001800311:47824182',
        version: 1,
        active: true,
        createTime: 1548750577845,
        updateTime: 1548750577845,
        name: '47824182',
        nameSpell: '47824182',
        code: '011001800311:47824182',
        corporationId: 'IU48mx7Ll80c00'
      },
      details: [
        {
          flowCounts: {},
          form: {
            E_system_发票明细_code: 'xAY8BmC__00o00',
            E_system_发票明细_name: i18n.get('*餐饮服务*餐费'),
            E_system_发票明细_单价: {
              standard: '177.36',
              standardUnit: i18n.get('元'),
              standardScale: '2',
              standardSymbol: '¥',
              standardNumCode: '156',
              standardStrCode: 'CNY'
            },
            E_system_发票明细_数量: '1',
            E_system_发票明细_税率: '6%',
            E_system_发票明细_税额: {
              standard: '10.64',
              standardUnit: i18n.get('元'),
              standardScale: '2',
              standardSymbol: '¥',
              standardNumCode: '156',
              standardStrCode: 'CNY'
            },
            E_system_发票明细_金额: {
              standard: '177.36',
              standardUnit: i18n.get('元'),
              standardScale: '2',
              standardSymbol: '¥',
              standardNumCode: '156',
              standardStrCode: 'CNY'
            }
          },
          totalCount: 1,
          useCount: 0,
          entityId: i18n.get('system_发票明细'),
          platformId: i18n.get('system_发票扩展'),
          source: 'WRITE',
          masterId: 'IU48mx7Ll80c00:011001800311:47824182',
          index: 0,
          visibility: {
            fullVisible: true,
            staffs: null,
            roles: null,
            departments: null,
            departmentsIncludeChildren: true
          },
          id: 'xAY8BmC__00o00',
          version: 1,
          active: true,
          createTime: 1548750577889,
          updateTime: 1548750577889,
          name: i18n.get('*餐饮服务*餐费'),
          nameSpell: '*CANYINFUWU*CANFEI',
          code: 'xAY8BmC__00o00',
          corporationId: 'IU48mx7Ll80c00'
        }
      ],
      message: i18n.get('购买信息与提交人的企业开票信息不符'),
      status: 'NO_VISIBLE',
      ischeck: true
    }
  ]
}

const testData = {
  itemIds: [
    {
      flowCounts: {},
      form: {
        E_system_发票明细_code: 'xAY8BmC__00o00',
        E_system_发票明细_name: i18n.get('*餐饮服务*餐费'),
        E_system_发票明细_单价: {
          standard: '177.36',
          standardUnit: i18n.get('元'),
          standardScale: '2',
          standardSymbol: '¥',
          standardNumCode: '156',
          standardStrCode: 'CNY'
        },
        E_system_发票明细_数量: '1',
        E_system_发票明细_税率: '6%',
        E_system_发票明细_税额: {
          standard: '10.64',
          standardUnit: i18n.get('元'),
          standardScale: '2',
          standardSymbol: '¥',
          standardNumCode: '156',
          standardStrCode: 'CNY'
        },
        E_system_发票明细_金额: {
          standard: '177.36',
          standardUnit: i18n.get('元'),
          standardScale: '2',
          standardSymbol: '¥',
          standardNumCode: '156',
          standardStrCode: 'CNY'
        }
      },
      totalCount: 1,
      useCount: 0,
      entityId: i18n.get('system_发票明细'),
      platformId: i18n.get('system_发票扩展'),
      source: 'WRITE',
      masterId: 'IU48mx7Ll80c00:011001800311:47824182',
      index: 0,
      visibility: {
        fullVisible: true,
        staffs: null,
        roles: null,
        departments: null,
        departmentsIncludeChildren: true
      },
      id: 'xAY8BmC__00o00',
      version: 1,
      active: true,
      createTime: 1548750577889,
      updateTime: 1548750577889,
      name: i18n.get('*餐饮服务*餐费'),
      nameSpell: '*CANYINFUWU*CANFEI',
      code: 'xAY8BmC__00o00',
      corporationId: 'IU48mx7Ll80c00'
    }
  ],
  invoiceId: {
    flowCounts: {},
    form: {
      E_system_发票主体_code: '011001800311',
      E_system_发票主体_name: '47824182',
      E_system_发票主体_图片: 'applet-ekb-web2-1548840817495-878-.jpg',
      E_system_发票主体_备注: i18n.get('门店编号：20199 北京新中关店'),
      E_system_发票主体_来源: 'OCR',
      E_system_发票主体_税额: {
        standard: '10.64',
        standardUnit: i18n.get('元'),
        standardScale: '2',
        standardSymbol: '¥',
        standardNumCode: '156',
        standardStrCode: 'CNY'
      },
      E_system_发票主体_价税合计: {
        standard: '188.00',
        standardUnit: i18n.get('元'),
        standardScale: '2',
        standardSymbol: '¥',
        standardNumCode: '156',
        standardStrCode: 'CNY'
      },
      E_system_发票主体_发票代码: '011001800311',
      E_system_发票主体_发票号码: '47824182',
      E_system_发票主体_发票日期: 1540224000000,
      E_system_发票主体_发票类别: 'DIGITAL_NORMAL',
      E_system_发票主体_发票金额: {
        standard: '177.36',
        standardUnit: i18n.get('元'),
        standardScale: '2',
        standardSymbol: '¥',
        standardNumCode: '156',
        standardStrCode: 'CNY'
      },
      E_system_发票主体_识别范围: [0, 0, 1440, 1080],
      E_system_票据来源_sourceEntityId: 'PF48BJdAfQfA00',
      E_system_发票主体_购买方名称: i18n.get('北京合思信息技术有限公司'),
      E_system_发票主体_销售方名称: i18n.get('华联咖世家（北京）餐饮管理有限公司'),
      E_system_发票主体_购买方纳税人识别号: '91110108318283928K',
      E_system_发票主体_销售方纳税人识别号: '9111000067055263X2'
    },
    totalCount: 1,
    useCount: 0,
    entityId: i18n.get('system_发票主体'),
    platformId: i18n.get('system_发票扩展'),
    source: 'WRITE',
    masterId: null,
    index: 0,
    visibility: {
      fullVisible: true,
      staffs: null,
      roles: null,
      departments: null,
      departmentsIncludeChildren: true
    },
    id: 'IU48mx7Ll80c00:011001800311:47824182',
    version: 1,
    active: true,
    createTime: 1548750577845,
    updateTime: 1548750577845,
    name: '47824182',
    nameSpell: '47824182',
    code: '011001800311:47824182',
    corporationId: 'IU48mx7Ll80c00'
  },
  fileId: {
    id: 'mTw8BJdAfQfw00',
    url:
      'http://7u2ps8.com2.z0.glb.qiniucdn.com/applet-ekb-web2-1548840817495-878-.jpg?e=1548927218&token=hky7l9UOxMaLClIe5GV51aPS6KMpYBW2zLVpzfxi:hWGcna9wPltXoM5JgkozVeJnstI=',
    thumbUrl:
      'http://7u2ps8.com2.z0.glb.qiniucdn.com/applet-ekb-web2-1548840817495-878-.jpg?imageView2/1/w/120/h/120&e=1548927218&token=hky7l9UOxMaLClIe5GV51aPS6KMpYBW2zLVpzfxi:C94_Y3IqA94kEA6ludhxOs3iPKQ=',
    key: 'applet-ekb-web2-1548840817495-878-.jpg'
  },
  isEdit: true
}

const aaa = {
  itemIds: [
    {
      flowCounts: {},
      form: {
        E_system_发票明细_code: 'xAY8BmC__00o00',
        E_system_发票明细_name: i18n.get('*餐饮服务*餐费'),
        E_system_发票明细_单价: {
          standard: '177.36',
          standardUnit: i18n.get('元'),
          standardScale: '2',
          standardSymbol: '¥',
          standardNumCode: '156',
          standardStrCode: 'CNY'
        },
        E_system_发票明细_数量: '1',
        E_system_发票明细_税率: '6%',
        E_system_发票明细_税额: {
          standard: '10.64',
          standardUnit: i18n.get('元'),
          standardScale: '2',
          standardSymbol: '¥',
          standardNumCode: '156',
          standardStrCode: 'CNY'
        },
        E_system_发票明细_金额: {
          standard: '177.36',
          standardUnit: i18n.get('元'),
          standardScale: '2',
          standardSymbol: '¥',
          standardNumCode: '156',
          standardStrCode: 'CNY'
        }
      },
      totalCount: 1,
      useCount: 0,
      entityId: i18n.get('system_发票明细'),
      platformId: i18n.get('system_发票扩展'),
      source: 'WRITE',
      masterId: 'IU48mx7Ll80c00:011001800311:47824182',
      index: 0,
      visibility: {
        fullVisible: true,
        staffs: null,
        roles: null,
        departments: null,
        departmentsIncludeChildren: true
      },
      id: 'xAY8BmC__00o00',
      version: 1,
      active: true,
      createTime: 1548750577889,
      updateTime: 1548750577889,
      name: i18n.get('*餐饮服务*餐费'),
      nameSpell: '*CANYINFUWU*CANFEI',
      code: 'xAY8BmC__00o00',
      corporationId: 'IU48mx7Ll80c00'
    }
  ],
  invoiceId: {
    flowCounts: {},
    form: {
      E_system_发票主体_code: '011001800311',
      E_system_发票主体_name: '47824182',
      E_system_发票主体_图片: 'applet-ekb-web2-1548842243142-832-.jpg',
      E_system_发票主体_备注: i18n.get('门店编号：20199 北京新中关店'),
      E_system_发票主体_来源: 'OCR',
      E_system_发票主体_税额: {
        standard: '10.64',
        standardUnit: i18n.get('元'),
        standardScale: '2',
        standardSymbol: '¥',
        standardNumCode: '156',
        standardStrCode: 'CNY'
      },
      E_system_发票主体_价税合计: {
        standard: '188.00',
        standardUnit: i18n.get('元'),
        standardScale: '2',
        standardSymbol: '¥',
        standardNumCode: '156',
        standardStrCode: 'CNY'
      },
      E_system_发票主体_发票代码: '011001800311',
      E_system_发票主体_发票号码: '47824182',
      E_system_发票主体_发票日期: 1540224000000,
      E_system_发票主体_发票类别: 'DIGITAL_NORMAL',
      E_system_发票主体_发票金额: {
        standard: '177.36',
        standardUnit: i18n.get('元'),
        standardScale: '2',
        standardSymbol: '¥',
        standardNumCode: '156',
        standardStrCode: 'CNY'
      },
      E_system_发票主体_识别范围: [0, 0, 1440, 1080],
      E_system_票据来源_sourceEntityId: 'pDU8BJdAfQqQ00',
      E_system_发票主体_购买方名称: i18n.get('北京合思信息技术有限公司'),
      E_system_发票主体_销售方名称: i18n.get('华联咖世家（北京）餐饮管理有限公司'),
      E_system_发票主体_购买方纳税人识别号: '91110108318283928K',
      E_system_发票主体_销售方纳税人识别号: '9111000067055263X2'
    },
    totalCount: 1,
    useCount: 0,
    entityId: i18n.get('system_发票主体'),
    platformId: i18n.get('system_发票扩展'),
    source: 'WRITE',
    masterId: null,
    index: 0,
    visibility: {
      fullVisible: true,
      staffs: null,
      roles: null,
      departments: null,
      departmentsIncludeChildren: true
    },
    id: 'IU48mx7Ll80c00:011001800311:47824182',
    version: 1,
    active: true,
    createTime: 1548750577845,
    updateTime: 1548750577845,
    name: '47824182',
    nameSpell: '47824182',
    code: '011001800311:47824182',
    corporationId: 'IU48mx7Ll80c00'
  },
  fileId: {
    id: 'I4g8BJdAfQqM00',
    url:
      'http://7u2ps8.com2.z0.glb.qiniucdn.com/applet-ekb-web2-1548842243142-832-.jpg?e=1548928644&token=hky7l9UOxMaLClIe5GV51aPS6KMpYBW2zLVpzfxi:P6pOIb0AOilrTsTwgjaGpK6JS-Q=',
    thumbUrl:
      'http://7u2ps8.com2.z0.glb.qiniucdn.com/applet-ekb-web2-1548842243142-832-.jpg?imageView2/1/w/120/h/120&e=1548928644&token=hky7l9UOxMaLClIe5GV51aPS6KMpYBW2zLVpzfxi:kwG9x0xYhwcD_uYn4ekUXJC5NnY=',
    key: 'applet-ekb-web2-1548842243142-832-.jpg'
  },
  others: {
    message: i18n.get('购买信息与提交人的企业开票信息不符'),
    status: 'NO_VISIBLE',
    ischeck: true
  },
  isEdit: true
}

const bbbb = {
  items: [
    {
      master: {
        flowCounts: {},
        form: {
          E_system_发票主体_name: '',
          E_system_发票主体_code: '',
          E_system_发票主体_发票代码: '',
          E_system_发票主体_发票号码: '4267600',
          E_system_发票主体_发票日期: '20180829',
          E_system_发票主体_发票金额: '0.55',
          E_system_发票主体_校验码: '249206',
          E_system_发票主体_识别范围: [0, 914, 3666, 2976]
        },
        totalCount: 1,
        useCount: 0,
        entityId: i18n.get('system_发票主体'),
        platformId: i18n.get('system_发票扩展'),
        source: 'WRITE',
        masterId: null,
        index: 0,
        visibility: {
          fullVisible: true,
          staffs: null,
          roles: null,
          departments: null,
          departmentsIncludeChildren: true
        },
        id: 'IU48mx7Ll80c00::4267600',
        version: 1,
        active: true,
        createTime: 1548896447577,
        updateTime: 1548896447577,
        name: 'IU48mx7Ll80c00::4267600',
        nameSpell: 'IU48mx7Ll80c00::4267600',
        code: 'IU48mx7Ll80c00::4267600',
        corporationId: 'IU48mx7Ll80c00'
      },
      details: [],
      message: i18n.get('国税系统返回错误：发票代码 不能为空!'),
      status: 'EDIT',
      ischeck: false
    },
    {
      master: {
        flowCounts: {},
        form: {
          E_system_出租车票_发票代码: '',
          E_system_出租车票_发票号码: '',
          E_system_出租车票_上车时间: 1548864000000,
          E_system_出租车票_下车时间: 1548864000000,
          E_system_出租车票_里程: '49.0',
          E_system_出租车票_金额: {
            standard: '0.00',
            standardStrCode: 'CNY',
            standardNumCode: '156',
            standardSymbol: '¥',
            standardUnit: i18n.get('元'),
            standardScale: '2'
          },
          E_system_出租车票_发票所在地: '',
          E_system_出租车票_消费类型: i18n.get('交通'),
          E_system_发票主体_来源: 'OCR',
          E_system_票据来源_sourceEntityId: 'DLE8BMWNAIvo00',
          E_system_发票主体_识别范围: [3247, 1164, 3968, 2976],
          E_system_发票主体_图片: 'applet-ekb-web2-1548896434842-597-.jpg'
        },
        totalCount: 1,
        useCount: 0,
        entityId: i18n.get('system_出租车票'),
        platformId: i18n.get('system_发票扩展'),
        source: 'WRITE',
        masterId: null,
        index: 0,
        visibility: {
          fullVisible: true,
          staffs: null,
          roles: null,
          departments: null,
          departmentsIncludeChildren: true
        },
        id: 'ZMY8BMWNAIvw00',
        version: 1,
        active: true,
        createTime: 1548896447584,
        updateTime: 1548896447584,
        name: 'ZMY8BMWNAIvw00',
        nameSpell: 'ZMY8BMWNAIvw00',
        code: 'ZMY8BMWNAIvw00',
        corporationId: 'IU48mx7Ll80c00'
      },
      details: [],
      message: null,
      status: 'SUCCESS',
      ischeck: true
    }
  ]
}
