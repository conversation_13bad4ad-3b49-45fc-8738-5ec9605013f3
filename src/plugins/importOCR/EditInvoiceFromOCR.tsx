import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import { app as api } from '@ekuaibao/whispered'
import styles from './EditInvoiceFromOCR.module.less'
import MessageCenter from '@ekuaibao/messagecenter'
import { Dynamic } from '@ekuaibao/template'
const editable = app.require('@components/dynamic/index.editable')
import createDOMForm from 'rc-form/lib/createDOMForm'
import { OCRInvoiceMap } from './configTemplate'
import { OCRInvoiceTitleForCN } from './configTemplate'
import { OCRInvoiceTitleForEN } from './configTemplate'
import * as actions from './importOCR.action'
import { get } from 'lodash'
import { getFileregionUrl } from './listUtils'
import { showLoading, hideLoading, toast } from '../../lib/util'
import { canShowPreviewImage } from './utils'
import { Image, Button, ImageViewer } from '@hose/eui-mobile'
import { handlerCurrencyMoneySelectChange } from '../bill/utils/defaultCurrency'
import { TwoToneDataPdf } from '@hose/eui-icons'
import { enableOtherInvoiceByDimension } from '../../lib/featbit/utils'

function create(T: any) {
  return createDOMForm({
    onValuesChange(props: any, changedValues: any) {}
  })(T)
}

// @ts-ignore
@EnhanceTitleHook(props => {
  const type = get(props, 'dataSource.master.entityId')
  return i18n.get('发票编辑', { titleForCN: OCRInvoiceTitleForCN[type], titleForEN: OCRInvoiceTitleForEN[type] })
})
export default class EditInvoiceFormOCR extends PureComponent<any, any> {
  constructor(props: any) {
    super(props)
    this.state = {
      regionUrl: '',
      attachment: {},
      imageViewerVisible: false
    }
  }

  bus: any = new MessageCenter()

  componentWillMount() {
    api
      .dataLoader('@common.payerInfo')
      .load()
      .then(() => {
        this.forceUpdate()
      })
    this.bus.watch('element:ref:select:property', this.handleSelectProperty)
    this.bus.watch('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
  }

  componentDidMount() {
    const {
      dataSource: {
        master: { form }
      }
    } = this.props
    // @i18n-ignore
    const keys = { key: form['E_system_发票主体_图片'], regions: form['E_system_发票主体_识别范围'] }
    if (canShowPreviewImage(keys.key)) {
      getFileregionUrl(keys).then(result => {
        this.setState({
          regionUrl: result?.value?.regionUrl ?? result?.value.thumbUrl,
          attachment: result.value,
          url: result?.value?.url
        })
      })
    }
    const dimensionCurrency = api.getState()['@bill']?.dimensionCurrencyInfo
    if(dimensionCurrency?.currency && enableOtherInvoiceByDimension()){
      toast.info(i18n.get('存在法人实体信息，请核对金额，重新保存发票信息'))
    }
  }

  componentWillUnmount() {
    this.bus.un('element:ref:select:property', this.handleSelectProperty)
    this.bus.un('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
  }

  // 修改币种类型时，设置所有币种
  _handlerCurrencyMoneySelectChange = (parms: any) => {
    handlerCurrencyMoneySelectChange.call(this, parms, 'editInvoice')
  }

  handleSelectProperty = (field: any) => {
    const name = 'basedata.Enum.TrainSeatType'
    const label = field && field.label
    const type = field && field.type
    const showBottom = field && field.optional
    const canSelectParent = 'all' === field.selectRange
    const { flowId } = this.props

    if (type === 'select_search') {
      return api.open('@basic:SelectSearch', { label, type, showBottom, canSelectParent, field })
    } else {
      return api.invokeService('@common:get:property:by:name', { name, flowId }).then((data: any) => {
        return api.open('@basic:SelectProperty', { data, label, type, showBottom, canSelectParent })
      })
    }
  }

  handleOnCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOnSave = () => {
    const invoiceId = get(this.props, 'dataSource.master.id')
    const entityId = get(this.props, 'dataSource.master.entityId')
    this.bus.getValueWithValidate(1).then((value: any) => {
      Object.keys(value).forEach(item => {
        if (value[item] === undefined) {
          value[item] = ''
        }
      })
      showLoading()
      const data = get(this.props, 'dataSource.master.form')
      const master = { ...data, ...value }
      const params = { ...master, invoiceId, entityId }
      if (entityId === 'system_海外发票') {
        const money = value['E_system_海外发票_金额']
        const originCurrency = data['E_system_海外发票_currency']
        const currency = money?.foreignNumCode ?? money?.standardNumCode ?? originCurrency
        params['E_system_海外发票_currency'] = currency
      }
      api
        .dispatch(actions.saveOCRInvoiceEditable(params))
        .then((res: any) => {
          const form = get(res, 'value.form') || {}
          hideLoading()
          this.props.layer.emitOk(form)
        })
        .catch((err: any) => {
          console.log(err)
          hideLoading()
        })
    })
  }

  handlePreviewImage = () => {
    const { regionUrl } = this.state
    api.open('@invoiceOCR:PreviewOCRImageModal', { markupUrl: regionUrl })
  }

  handlePreviewOriginImage = () => {
    this.setState({ imageViewerVisible: true })
  }

  handlePreviewPDF = () => {
    const { url } = this.state
    const isApp = window.__PLANTFORM__ === 'APP'
    if (isApp) {
      api.invokeService('@layout:open:link', url, null, i18n.get('预览PDF'))
    } else {
      api.invokeService('@layout:open:link', url)
    }
  }
  fnFormatValue = (invoiceInfo: any) => {
    const { form } = invoiceInfo.master
    if (invoiceInfo.master?.entityId === 'system_非税收入类票据') {
      form['E_system_非税收入类票据_收款单位'] = !!form['E_system_非税收入类票据_收款单位']?.length
        ? form['E_system_非税收入类票据_收款单位']
        : form['E_system_非税收入类票据_发票种类']
    }
    return form
  }

  render() {
    const { dataSource, isForeignCurrencyEdit= false } = this.props
    const {
      master: { form, entityId  }
    } = dataSource
    const {
      regionUrl,
      attachment: { url },
      imageViewerVisible
    } = this.state
    const type = get(this.props, 'dataSource.master.entityId')
    const template = OCRInvoiceMap[type]
    const hasPdf = (form?.['E_system_发票主体_图片'] || '').toLowerCase().endsWith('.pdf')
    const isCurrencyEdit = !['system_海外发票','system_其他','system_发票主体'].includes(entityId) && isForeignCurrencyEdit
    const dimensionCurrencyInfo = app.getState()['@bill']?.dimensionCurrencyInfo
    const isHasDimensionCurrency = enableOtherInvoiceByDimension() && dimensionCurrencyInfo?.currency
    
    return (
      <div className={styles['edit-invoice-from-ocr-wrapper']}>
        <div className="edit-invoice-content">
          {canShowPreviewImage(form?.['E_system_发票主体_图片']) ? (
            <>
              <div className="edit-header-title">{i18n.get('票据')}</div>
              <div className="edit-header">
                {hasPdf ? (
                  <div className="image" onClick={this.handlePreviewPDF}>
                    <TwoToneDataPdf fontSize={39} />
                  </div>
                ) : (
                  <>
                    <ImageViewer
                      image={url}
                      visible={imageViewerVisible}
                      onClose={() => {
                        this.setState({ imageViewerVisible: false })
                      }}
                    />
                    <Image width={78} height={78} src={regionUrl} onClick={this.handlePreviewOriginImage} />
                  </>
                )}
                <Button
                  category="text"
                  theme="highlight"
                  onClick={hasPdf ? this.handlePreviewPDF : this.handlePreviewOriginImage}
                >
                  {hasPdf ? '查看原文件' : '查看原图'}
                </Button>
              </div>
            </>
          ) : null}

          <div className="edit-content">
            <Dynamic
              value={this.fnFormatValue(dataSource)}
              bus={this.bus as any}
              elements={editable as any}
              create={create}
              isForeignCurrencyEdit={isCurrencyEdit}
              template={template}
            />
          </div>
        </div>
        <div className="bottom-actions">
          {!isHasDimensionCurrency && (
            <div className="delete" onClick={this.handleOnCancel}>
              {i18n.get('取消')}
            </div>
          )}
          <div className="save" onClick={this.handleOnSave}>
            {i18n.get('保存')}
          </div>
        </div>
      </div>
    )
  }
}
