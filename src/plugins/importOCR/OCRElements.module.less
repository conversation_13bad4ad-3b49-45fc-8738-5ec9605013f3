.element-wrapper {
  display: flex;
  padding: 20px 0;
  justify-content: space-between;
  flex-direction: column;
  :global {
    .left {
      max-width: 390px;
      .title {
        color: #1d2b3d;
        font-weight: 500;
        font-size: 32px;
        max-width: 450px;
        margin: 16px 0;
      }
      .info {
        font-size: 28px;
        color: rgba(29, 43, 61, 0.5);
        margin-bottom: 8px;
        max-width: 400px;
      }
      .message {
        font-size: 28px;
        margin-top: 16px;
      }
    }
    .icon-real {
      width: 80px;
      height: 80px;
      color: var(--brand-base);
    }
    .right {
      min-width: 30px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .edit {
        margin-top: 16px;
        font-size: 28px;
        color: var(--brand-base);
      }
    }
    .invoice-title {
      display: inline-block;
      max-width: 352px;
      margin-right: 8px;
    }
  }
}
.error-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  :global {
    .title {
      color: rgba(29, 43, 61, 0.3);
      font-size: 32px;
    }
    .actions {
      .image {
        width: 120px;
        height: 120px;
        img {
          border-radius: 8px;
          width: 100%;
          height: 100%;
        }
      }
      .delete {
        width: 32px;
        height: 48px;
        margin-left: 32px;
        color: rgba(29, 43, 61, 0.75);
      }
    }
  }
}
.ocr-loading {
  height: 168px;
  display: flex;
  align-items: center;
  background: #ffffff;
  justify-content: space-between;
  padding: 0 32px;

  :global {
    .warn-icon:local {
      color: #008dff;
      width: 36px;
      height: 36px;
      animation: spinnerFour 1s linear infinite;
    }
    @keyframes spinnerFour {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
    .images {
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      img {
        border-radius: 8px;
        width: 100%;
        height: 100%;
      }
      .place-image {
        width: 80px;
        height: 80px;
        color: var(--eui-primary-pri-300);
        svg {
          width: 100%;
          height: 100%;
        }
      }
    }
    .info {
      font-size: 32px;
      color: #1d2b3d;
      margin-left: 20px;
    }
  }
}
