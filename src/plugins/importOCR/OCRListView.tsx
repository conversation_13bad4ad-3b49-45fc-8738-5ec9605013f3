import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import styles from './OCRListView.module.less'
import classNames from 'classnames'
import { Checkbox } from 'antd-mobile'
const CheckboxItem = Checkbox.CheckboxItem
import { <PERSON><PERSON>, Dialog } from '@hose/eui-mobile'
import { EnhanceLayer } from '@ekuaibao/enhance-layer-manager-mobile'
import { toast, hideLoading } from '../../lib/util'
import { mapView, LoadingView } from './OCRElements'
import { uuid } from '@ekuaibao/helpers'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import {
  checkedAllValues,
  parseDataToSave,
  getInvoiceCheckValue,
  getAllInvoiceDetailsCount,
  getIsHalfInvoice,
  checkListName,
  fnCheckinvoiceImport,
  formatLineValue,
  fnInvoiceSelectAll,
  fnFilterDisableChecked,
  initData,
  formatDisableValue,
  fnOriginalFileDownload
} from './listUtils'
import { cloneDeep, get, isArray, isObject, remove, set } from 'lodash'
import UploadOCRPortal from './UploadOCRPortal'
import { Fetch } from '@ekuaibao/fetch'
import { isElectronicAirAndTrain } from './utils'
import { convertForeignToBaseCurrency, getBillAllCurrencyRatesInfo } from '../../components/utils/fnCurrencyObj'
import { validateFeeTypeOnlyCurrency } from '../bill/utils/billUtils'
import { getFeetypeInvoiceIds } from '../invoice/import-list/fnvalidator'
import { otherInvoiceByDimensionMoneyFormat } from '../../components/utils/fnCurrencyObj'
import { NoticeBar } from '@hose/eui-mobile'
import { enableOtherInvoiceByDimension } from '../../lib/featbit/utils'

interface Props {
  layer: any
  [key: string]: any
}

interface State {
  checkedResult: any[]
  dataSource: any[]
  disableData: any[]
  disableChecked: any[]
}

function openFeetype({ isMerge, invoiceData, specificationVersionId, submitter }: any) {
  return api.open('@invoice:InvoiceFeeTypeRecommend', { isMerge, invoiceData, specificationVersionId, submitter })
}

import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import ImageElementCard from './ImgElement'
import { geShowInvoiceImg, isInvoiceImage } from './utils'
import { getOriginalFileDownload } from '../invoice/invoice.action'
import { OutlinedTipsAdd } from '@hose/eui-icons'
const EkbCheckBoxGroup = app.require<any>('@invoice/puppet/EkbCheckBoxGroup')
const EKBCheckboxItem = EkbCheckBoxGroup.EkbGroup
const Money = app.require<any>('@elements/puppet/Money')
const ItemTitleView = app.require<any>('@invoice/import-list/InvoiceItemView.ItemTitleView')
const ItemContentView = app.require<any>('@invoice/import-list/InvoiceItemView.ItemContentView')
const { fnConinvoiceTrack, fnCreatecostsTrack, fnCreatecostTrack } = app.require<any>('@invoice/utils/invoiceTrack')
const { checkedAllValueGroup, getAllDataChildren, getCheckedValue } = app.require<any>(
  '@invoice/import-list/invoiceListUtils'
)

@EnhanceLayer({ className: 'debugger_top_fix' })
@EnhanceTitleHook(i18n.get('发票列表'))
export default class OCRListView extends PureComponent<Props, State> {
  importResult: any[] = []
  _$UUID: string
  invoiceRepeatRule: any
  continueCount: number = 0
  verifyInvoiceResult: any[] = []
  invoiceCopyMap: { [key: string]: any } = {} //发票列表 已识别未导入的状态-去重使用

  constructor(props: Props) {
    super(props)
    const fileL = this.initFetchData(props.fileList)
    this.state = {
      dataSource: fileL || [], // 数据源
      disableChecked: [], // 已经导入的
      disableData: [], // 不可用的数据如金额为负数的
      checkedResult: [] // //选中的要导入的
    }
    if (props.params?.notShowModalIfAllInvoiceSuccess && isHongShanTestingEnterprise(Fetch.ekbCorpId)) {
      api.invokeService('@layout:change:header:title', i18n.get('消费明细'))
      props.layer.override({ className: 'debugger_top_fix hidden' })
    } else {
      api.invokeService('@layout:change:header:title', i18n.get('发票列表'))
      props.layer.override({ className: 'debugger_top_fix' })
    }
  }
  componentWillMount() {
    this._$UUID = String(Math.random())
    api.backControl.create(this._$UUID, () => {
      this.backHookCallback()
    })
    this.handleLayerDOM()
  }
  backHookCallback = () => {
    api.backControl.remove(this._$UUID)
    api.backControl.invoke()
    if (this.props.params?.enterType !== 'invoiceFrom') {
      this.props.layer.emitOk(this.importResult)
    }
  }

  handleLayerDOM = () => {
    // 隐藏一个多余的 "ekb-layer-wrapper debugger_top_fix" 元素
    const layers = document.querySelectorAll('body .ekb-layer-wrapper.debugger_top_fix')
    Array.prototype.forEach.call(layers, (layer: any) => {
      const subContent = layer.querySelectorAll('.ekb-layer-content-wrapper div')
      if (subContent && subContent.length === 0) {
        layer.setAttribute('class', `${layer.getAttribute('class')} hidden`)
      }
    })
  }

  componentDidMount() {
    this.handleLayerDOM()
    this.fetchData(this.state.dataSource, () => {
      hideLoading()
      if (this.props.params?.notShowModalIfAllInvoiceSuccess && isHongShanTestingEnterprise(Fetch.ekbCorpId)) {
        const { dataSource = [] } = this.state
        const isNotAllSuccess = dataSource.some(dataSourceItem => {
          const { details = [] } = dataSourceItem
          return details.some((detailItem: any) => detailItem.status && detailItem.status !== 'SUCCESS')
        })
        // 如果全部验证通过，则自动走全选并确定流程,不显示弹窗
        if (!isNotAllSuccess) {
          // 直接全选并返回
          this.handleCheckedAll(true, () => {
            this.handleBindConsume()
          })
        }
      }
      api.invokeService('@layout:change:header:title', i18n.get('发票列表'))
      this.props.layer.override({ className: 'debugger_top_fix' })
    })
  }
  // 置灰已关联的明细数据
  disableLinkedItems = async (dataSource:any) => {
    const result:any = await getFeetypeInvoiceIds()
    if (!result?.error && isArray(dataSource)) {
      dataSource.forEach(line => {
        const detailsList = line?.details?.[0]?.details;
        if (isArray(detailsList)) {
          detailsList?.forEach(item => {
            if ((result || [])?.indexOf(item?.id) >= 0) {
              item.active = false;
              line.details[0].master.active = false;
            }
          })
        }
      })
    }
    return dataSource
  }
  componentWillUnmount() {
    api.backControl.remove(this._$UUID)
  }
  initFetchData = (fileList: any[]) => {
    return fileList.map((line: any) => {
      const fileId = get(line, 'response.fileId', {})
      return { ...line, isLoading: true, fileId }
    })
  }
  fetchData = (dataSource: any[], cb?: Function) => {
    const { params } = this.props
    const staffId = params.submitter && params.submitter.id
    let cDataSource = dataSource.slice()
    dataSource.forEach((line: any) => {
      if (line.isLoading) {
        const { fileId, hash } = line.response
        fileId?.key &&
          fileId?.url &&
          api
            .invokeService('@invoiceOCR:get:ocr:data', {
              fileName: fileId.key,
              fileUrl: fileId.url,
              fileId: fileId,
              isMedical: params?.type === 'medicalInvoiceOCR',
              type: params?.type,
              staffId,
              hash,
              appId: params.appId,
              specificationId: params.specificationId
            })
            .then(async (result: any) => {
              let { ocrList, fileId } = result
              ocrList = await this.handleFilterInvoicesRepeatByRule(ocrList)
              ocrList = this.handleResetInvoiceOnly(ocrList)
              ocrList = params?.type === 'overseasInvoice' ? this.handleOverseasInvoice(ocrList) : ocrList
              fnCheckinvoiceImport(ocrList).then(async(res) => {
                cDataSource = cDataSource.map((data: any) => {
                  if (get(data, 'response.fileId.id', '') === fileId.id && data.isLoading) {
                    return this.initDataSource(ocrList, fileId)
                  } else {
                    return data
                  }
                })
                cDataSource = await this.disableLinkedItems(cDataSource)
                let { disableChecked } = this.state
                let disableCheckedKey = disableChecked.map(function(item) {
                  return item.key
                })
                const { disableChecked: newdisableChecked, disableData } = initData(cDataSource)
                newdisableChecked.forEach(line => {
                  if (!disableCheckedKey.includes(line.key)) {
                    disableChecked.push(line)
                  }
                })
                this.setState({ dataSource: cDataSource, disableChecked, disableData }, () => {
                  cb && cb()
                })
              })
            })
      }
    })
  }

  handleOverseasInvoice = (invoiceListData: { items: any[] }) => {
    invoiceListData?.items?.forEach(v => {
      const { form } = v.master
      const fieldsToUpdate = ['E_system_海外发票_不计税金额', 'E_system_海外发票_税额', 'E_system_海外发票_金额']
      fieldsToUpdate.forEach(field => {
        if (form[field] && isObject(form[field])) {
          const { currentCurrency, rates } = getBillAllCurrencyRatesInfo(null, true)
          const value = convertForeignToBaseCurrency(form[field], currentCurrency, rates)
          form[field] = value
        }
      })
    })
    return invoiceListData
  }

  handleFilterInvoicesRepeatByRule = async (invoiceListData: { items: any[] }) => {
    const validRepeatInvoiceEntityIds = ['system_火车票', 'system_出租车票', 'system_机打发票', 'system_定额发票']
    const validRepeatTypes = ['INVOICE_TRAIN', 'INVOICE_TAXI', 'INVOICE_MACHINE_PRINT', 'INVOICE_QUOTA']
    const currentAllInvoices = invoiceListData?.items?.filter(item =>
      validRepeatInvoiceEntityIds.includes(get(item, 'master.entityId'))
    )
    if (!currentAllInvoices?.length) {
      return invoiceListData
    }
    if (this.invoiceRepeatRule === undefined) {
      const ruleList = await api.invokeService('@invoice:get:invoice:rule:list')
      const validInvoiceRepeatRule = ruleList.items?.find(
        (rule: any) => rule.normType === 'INVOICE_REPEAT_VERIFICATION'
      )
      const hasRepeatRule = validRepeatTypes.find(type => validInvoiceRepeatRule.invoiceType?.includes(type))
      if (hasRepeatRule) {
        this.invoiceRepeatRule = validInvoiceRepeatRule
      }
    }
    if (!this.invoiceRepeatRule) {
      return invoiceListData
    }
    const getTrainValue = (el: any) => {
      const form = el?.form || el?.invoiceId?.form
      return (
        get(form, 'E_system_火车票_车次') +
        get(form, 'E_system_火车票_乘车人姓名') +
        get(form, 'E_system_火车票_乘车时间') +
        get(form, 'E_system_火车票_号码') +
        get(form, 'E_system_火车票_序列码')
      )
    }
    const getBaseInfoValue = (el: any, entityId: string) => {
      const form = el?.form || el?.invoiceId?.form
      return get(form, `E_${entityId}_发票代码`) + get(form, `E_${entityId}_发票号码`)
    }

    const getQuotaValue = (el: any, entityId: string) => {
      const form = el?.form || el?.invoiceId?.form
      return get(form, `E_${entityId}_发票代码`) + get(form, `E_${entityId}_号码`)
    }

    const switchRuleAndEntityIdMap: Record<
      string,
      { rule: string; entityId: string; compareValueFunction: (item: any, entityId?: string) => void }
    > = {}
    const hasTrainRepeatRule = this.invoiceRepeatRule.invoiceType?.includes('INVOICE_TRAIN')
    const hasTaxiRepeatRule = this.invoiceRepeatRule.invoiceType?.includes('INVOICE_TAXI')
    const hasMachineRepeatRule = this.invoiceRepeatRule.invoiceType?.includes('INVOICE_MACHINE_PRINT')
    const hasQuotaRepeatRule = this.invoiceRepeatRule.invoiceType?.includes('INVOICE_QUOTA')

    if (hasTrainRepeatRule) {
      switchRuleAndEntityIdMap['system_火车票'] = {
        rule: 'INVOICE_TRAIN',
        entityId: 'system_火车票',
        compareValueFunction: getTrainValue
      }
    }
    if (hasTaxiRepeatRule) {
      switchRuleAndEntityIdMap['system_出租车票'] = {
        rule: 'INVOICE_TAXI',
        entityId: 'system_出租车票',
        compareValueFunction: getBaseInfoValue
      }
    }
    if (hasMachineRepeatRule) {
      switchRuleAndEntityIdMap['system_机打发票'] = {
        rule: 'INVOICE_MACHINE_PRINT',
        entityId: 'system_机打发票',
        compareValueFunction: getBaseInfoValue
      }
    }
    if (hasQuotaRepeatRule) {
      switchRuleAndEntityIdMap['system_定额发票'] = {
        rule: 'INVOICE_QUOTA',
        entityId: 'system_定额发票',
        compareValueFunction: getQuotaValue
      }
    }

    const promise = api.has('get:bills:value') ? api.invoke('get:bills:value') : Promise.resolve({})
    return promise.then(result => {
      let billTrainInvoices: any[] = Object.values(this.invoiceCopyMap).map(el => el.master)
      const billsInvoiceList: any[] = get(result, 'values.details')
      if (billsInvoiceList) {
        billsInvoiceList.forEach(item => {
          const invoicesData: any[] = get(item, 'feeTypeForm.invoiceForm.invoices', [])
          const filterInvoices: any[] = []
          invoicesData.forEach(invoice => {
            if (!!switchRuleAndEntityIdMap[get(invoice, 'invoiceId.entityId')]) {
              filterInvoices.push(invoice)
            }
          })
          if (filterInvoices.length) {
            billTrainInvoices = billTrainInvoices.concat(filterInvoices)
          }
        })
      }
      invoiceListData = this.fnFilterRepeatInvoices(invoiceListData, billTrainInvoices, switchRuleAndEntityIdMap)
      return invoiceListData
    })
  }

  fnFilterRepeatInvoices = (
    invoiceListData: { items: any[] },
    billTrainInvoices: any[],
    switchRuleAndEntityIdMap: any = {}
  ) => {
    if (invoiceListData.items.length) {
      let hasRepeat = false
      invoiceListData.items = invoiceListData.items
        .map(item => {
          const entityId = item?.master?.entityId || item?.invoiceId?.entityId
          const rule = switchRuleAndEntityIdMap[entityId]
          if (!!rule && entityId === rule.entityId) {
            const compareValueFunction = rule.compareValueFunction
            const isRepeat = !!billTrainInvoices.find(invoice => {
              return compareValueFunction(item.master, entityId) === compareValueFunction(invoice, entityId)
            })
            if (isRepeat) {
              hasRepeat = true
              return undefined
            }
            billTrainInvoices.push(item)
            return item
          }
          return item
        })
        .filter(el => !!el)
      if (hasRepeat) {
        toast.info(i18n.get('已过滤掉重复的发票'))
      }
    }
    return invoiceListData
  }

  handleResetInvoiceOnly = (invoiceListData: { items: any[] }) => {
    let { items = [] } = invoiceListData
    items = items
      .map(item => {
        const id = get(item, 'master.id', '')
        if (Object.keys(this.invoiceCopyMap).includes(id)) {
          toast.info(i18n.get('已过滤掉重复的发票'))
          return undefined
        } else {
          this.invoiceCopyMap[item.master.id] = item
        }
        return item
      })
      .filter(oo => !!oo)
    invoiceListData.items = items
    return invoiceListData
  }
  initDataSource = (dataSource: any, fileId: any) => {
    return { groupId: uuid(), details: dataSource.items, count: dataSource.items.length, isLoading: false, fileId }
  }

  handleAsShow(line: any) {
    const { checkedResult, disableData, disableChecked } = this.state
    let checkedValues: any = checkedAllValueGroup(checkedResult)
    checkedValues = checkedValues ? checkedValues[line.groupId] : []

    let disData: any = checkedAllValueGroup(disableData)
    disData = disData ? disData[line.groupId] : []

    let disableValues: any = checkedAllValueGroup(disableChecked)
    disableValues = disableValues ? disableValues[line.groupId] : []
    return { checkedValues, disData, disableValues }
  }

  handleInvoiceAsShow = (line: any, id: string) => {
    const { disableData, disableChecked, checkedValues } = line
    let invoiceDisableData = getInvoiceCheckValue(disableData, id)
    let InvoiceDisableChecked = getInvoiceCheckValue(disableChecked, id)
    let invoiceCheckedValues = getInvoiceCheckValue(checkedValues, id)
    return { invoiceDisableData, InvoiceDisableChecked, invoiceCheckedValues }
  }

  handleCheckedAll = (checked: boolean, cb?: Function) => {
    const { dataSource, disableChecked, disableData } = this.state
    let checkedResultList = []
    if (checked) {
      let { checkedResult } = checkedAllValues(dataSource, disableChecked, disableData)
      checkedResultList = checkedResult
    }
    this.setState({ checkedResult: checkedResultList }, () => {
      cb && cb()
    })
  }

  get eventName() {
    const nameMap = {
      medicalInvoiceOCR: 'upload:smartphoto:medical:click',
      invoiceFileOCR: 'upload:ocr:pages:click',
      overseasInvoice: 'upload:ocr:overseasInvoice'
    }
    return nameMap[this.props?.params?.type] ?? 'upload:smartphoto:click'
  }

  handleContinue = () => {
    const {
      params: { submitter }
    } = this.props
    api.invoke(this.eventName).then((fileList: any) => {
      const list = this.initFetchData(fileList)
      const { dataSource } = this.state
      let cDataSource = dataSource.slice()
      cDataSource = cDataSource.concat(list)
      this.setState({ dataSource: cDataSource }, () => {
        this.fetchData(cDataSource)
      })
      //神策埋点
      this.continueCount = this.continueCount + 1
      fnConinvoiceTrack(submitter, this.continueCount, 'ocr')
    })
  }

  handleOnCheckedValue = (name: string, values: any[]) => {
    this.handleCheckSelectedValue(name, values)
  }

  handleCheckSelectedValue = (name: string, values: any[]) => {
    const cValue = this.filterDisableData(values)
    const { checkedResult } = this.state
    let cCheckedResult = checkedResult.slice()
    cCheckedResult = getCheckedValue(cCheckedResult, cValue, name)
    this.setState({ checkedResult: cCheckedResult })
  }

  //发票按照明细拆分
  handleInvoiceOnCheckedValue = (
    groupId: string,
    parentIndex: number,
    index: number,
    checkedValues: any[],
    name: string,
    e: any
  ) => {
    e.stopPropagation()
    let { dataSource = [], checkedResult } = this.state
    const key = dataSource[parentIndex].groupId //与checkedValues中key一样，都是groupId
    //整张发票取消时
    if (checkedValues && !checkedValues.length) {
      let cCheckedResult = checkedResult.filter(vv => vv.key === key).filter(oo => oo.value !== name)
      this.handleCheckSelectedValue(groupId, cCheckedResult)
    } else {
      let checkedResultList = checkedResult.slice()
      let selectedItem = dataSource[parentIndex].details[index]
      let cCheckedResult = {
        key: key,
        value: selectedItem.master.id,
        valueObj: { ...selectedItem, details: checkedValues.map(oo => oo.valueObj) }
      }
      //选择发票明细时，是重复的
      checkedResultList = checkedResultList
        .filter(vv => vv.key === key)
        .filter(oo => oo.value !== selectedItem.master.id)
      checkedResultList.push(cCheckedResult)
      this.handleCheckSelectedValue(groupId, checkedResultList)
    }
  }

  filterDisableData = (values: any[]) => {
    return values.filter((line: any) => {
      const item = line.valueObj
      // @i18n-ignore
      let isAvailable = item.master.entityId === 'system_发票主体' && item.status === 'EDIT'
      if (
        item.master.entityId === 'system_非税收入类票据' ||
        ['ELECTRONIC_AIRCRAFT_INVOICE', 'ELECTRONIC_TRAIN_INVOICE'].includes(
          item.master.form?.E_system_发票主体_发票类别
        )
      ) {
        isAvailable = !item.ischeck
      }
      return !isAvailable
    })
  }

  handleOnDelete = (value: any, groupId: string) => {
    const { dataSource, disableData } = this.state
    const cDataSource = dataSource.slice()
    const cDisableData = disableData.slice()
    cDataSource.forEach(line => {
      if (line.groupId === groupId) {
        remove(line.details, function(n: { id: string }) {
          return n.id === value.id
        })
      }
    })
    remove(cDisableData, function(n: { value: string }) {
      return n.value === value.id
    })
    this.setState({ dataSource: cDataSource, disableData: cDisableData })
  }

  handleOnEdit = (item: any, parentIndex: number, childrenIndex: number, fileId: any) => {
    if (item.master.entityId === 'system_发票主体') {
      // @i18n-ignore
      api.open('@invoiceOCR:InvoiceDetailEdit', { dataSource: item, fileId }).then((result: any) => {
        this.fnUpdateAfterSave({ result, parentIndex, childrenIndex }, true)
      })
    } else {
      const {invoice, isForeignCurrencyEdit = false, isShowToast} = otherInvoiceByDimensionMoneyFormat(cloneDeep(item))
      setTimeout(() => {
        api.open('@invoiceOCR:EditInvoiceFromOCR', { dataSource: invoice, fileId, isForeignCurrencyEdit }).then((result: any) => {
          this.fnUpdateAfterSave({ result, parentIndex, childrenIndex }, false)
        })
      }, isShowToast ? 2000 : 0);
     
    }
  }

  fnUpdateAfterSave = (params: any, isFp: boolean) => {
    const { result, parentIndex, childrenIndex } = params
    const { dataSource } = this.state
    const cDataSouce = dataSource.slice()
    const editObj = cDataSouce[parentIndex].details[childrenIndex]
    const form = editObj.master.form
    let oldForm = undefined
    if (isFp) {
      oldForm = editObj.master
      editObj.master = result.master
      editObj.details = result.details
      editObj.message = result.message
      editObj.ischeck = true
      editObj.status = result.status
    } else {
      oldForm = cloneDeep(form)
      editObj.master.form = { ...form, ...result }
    }
    api?.logger?.info('在发票导入时编辑发票', {
      sceneName: '发票',
      operateType: '编辑',
      entityId: result?.entityId,
      invoiceId: result?.invoiceId,
      oldForm,
      newForm: result
    })
    this.setState({ dataSource: cDataSouce })
  }

  handleConsume = (isMerge: boolean, invoiceNum?: number) => {
    const {
      params: { submitter }
    } = this.props
    const { checkedResult, dataSource } = this.state
    if (checkedResult.length === 0) {
      toast.info(i18n.get('请选择要导入的发票明细'))
      return
    }
    const { show, title } = checkListName(checkedResult)
    if (show) {
      toast.error(title)
      return
    }

    const batch = (value: any, checkedResult: any[]) => {
      const flag = this.handleImportFinished(value, checkedResult)
      if (flag) {
        this.props.layer.emitOk(this.importResult)
      }
    }
    const fn = isMerge ? this.handleOpenFeetypeDetailsModal : batch
    //神策埋点
    isMerge ? fnCreatecostTrack(submitter, invoiceNum) : fnCreatecostsTrack(submitter)
    this.handleImport(fn, checkedResult, isMerge)
  }

  handleOpenFeetypeDetailsModal = (consume: any, checkedResult: any) => {
    const value = { ...consume, enterType: 'invoiceImport' }
    if (api.has('get:bills:bus')) {
      api.invoke('get:bills:bus').then((bus: any) => {
        bus
          ?.invoke('element:details:line:click', {
            line: value,
            idx: 0,
            ds: [],
            isEdit: true,
            flowAllowModifyFields: undefined,
            isForm: 'importDetails'
          })
          .then((result: any) => {
            if (result !== -1) {
              const flag = this.handleImportFinished(result, checkedResult)
              if (flag) {
                this.props.layer.emitOk(this.importResult)
              } else {
                toast.success(i18n.get('导入成功'))
              }
            }
          })
      })
    } else {
      // 随手记页面
      api.invoke('record:import:details', consume).then((result: any) => {
        if (result !== -1) {
          const flag = this.handleImportFinished(result, checkedResult)
          if (flag) {
            this.props.layer.emitOk(this.importResult)
          } else {
            toast.success(i18n.get('导入成功'))
          }
        }
      })
    }
  }

  handleMergeConsume = (len: number) => {
    const { checkedResult } = this.state
    if (!validateFeeTypeOnlyCurrency({ invoiceForm: { invoices: checkedResult } })) {
      toast.error(i18n.get('一个费用明细中只能存在一种消费币种（原币或本位币），请检查发票信息'))
      return
    }
    this.handleConsume(true, len)
  }

  getVerifyParams = async () => {
    const { checkedResult } = this.state
    let invoiceIds: string[] = []
    checkedResult?.forEach(v => {
      if (v?.valueObj?.master?.id) {
        invoiceIds.push(v.valueObj.master.id)
      }
      // 发票明细id
      let detailIds = []
      if (v?.valueObj?.details?.length) {
        detailIds = v.valueObj.details.map(o => o?.id)
        invoiceIds = invoiceIds.concat(detailIds)
      }
    })

    const param = {
      formSpecId: '',
      feeTypeSpecId: '',
      invoiceIds
    }
    if (api.has('get:current:feeType:value')) {
      const res: any = await api.invoke('get:current:feeType:value')
      param.formSpecId = res?.formSpecId ?? ''
      param.feeTypeSpecId = res?.feeTypeSpecId ?? ''
    }
    return [param]
  }

  formatVerifyResult = () => {
    const { dataSource } = this.state
    const target: any = {}
    this.verifyInvoiceResult.forEach(i => {
      if (i?.invoiceId && i?.bindValidateMessage?.length) {
        target[i.invoiceId] = i.bindValidateMessage.join()
      }
    })

    return dataSource.map(i => {
      i.details = i.details.map((o: any) => {
        if (o?.master?.id && target[o.master.id]) {
          o.bindValidateMessage = target[o.master.id]
        } else if (o?.bindValidateMessage) {
          o.bindValidateMessage = ''
        }
        return o
      })
      return i
    })
  }

  handleDisplayVerifyResult = () => {
    const cData = this.formatVerifyResult()
    if (cData && cData.length) {
      this.setState({
        dataSource: cData
      })
    }
  }

  handleVerifyInvoice = async () => {
    try {
      const param = await this.getVerifyParams()
      const res = await Fetch.POST('/api/v2/invoice/bindValidate', {}, { body: param })
      if (res.items && res.items.length) {
        this.verifyInvoiceResult = res.items
        Dialog.alert({
          title: i18n.get('禁止提交'),
          content: i18n.get('存在与所选费用类型不能关联的发票，请重新选择其他费用类型或上传其他发票再提交'),
          confirmText: i18n.get('我知道了'),
          onConfirm: this.handleDisplayVerifyResult
        })
        return true
      } else {
        return false
      }
    } catch (e) {
      console.log(e)
      return true
    }
  }

  handleBindConsume = async () => {
    const { checkedResult, dataSource } = this.state
    if (checkedResult.length === 0) {
      toast.info(i18n.get('请选择要导入的发票明细'))
      return
    }

    let checkedResultCopy = checkedResult
    if (api.has('get:feetype:form:value')) {
      const feeTypeValue = await api.invoke('get:feetype:form:value')
      // 海外发票入口校验绑定逻辑，明细上发票一起校验
      if (checkedResult.find(v => v?.valueObj?.master?.entityId === 'system_海外发票')) {
        checkedResultCopy = [...checkedResult, ...(feeTypeValue?.invoiceForm?.invoices || [])]
      }
    }

    if (!validateFeeTypeOnlyCurrency({ invoiceForm: { invoices: checkedResultCopy } })) {
      toast.error(i18n.get('一个费用明细中只能存在一种消费币种（原币或本位币），请检查发票信息'))
      return
    }
    const { show, title } = checkListName(checkedResult)
    if (show) {
      toast.error(title)
      return
    }
    const verifyResult = await this.handleVerifyInvoice()
    if (!!verifyResult) {
      return
    }
    const res = checkedResult.map((line: any) => line.valueObj)
    fnOriginalFileDownload(res)
    const orders = parseDataToSave(res)
    this.props.layer.emitOk(orders)
  }

  handleImport = (fn: Function, checkedResult: any[], isMerge: boolean) => {
    const result = checkedResult.map((line: any) => line.valueObj)
    fnOriginalFileDownload(result)
    const orders = parseDataToSave(result)
    const specificationVersionId = get(this.props, 'params.specificationVersionId')
    const submitter = get(this.props, 'params.submitter')
    openFeetype({ isMerge, invoiceData: orders, specificationVersionId, submitter }).then((feetypes: any) => {
      const val = { orders, type: 'invoiceOCR', feetypes, isMerge }
      if (api.has('get:bills:bus')) {
        api.invoke('get:bills:bus').then((bus: any) => {
          bus?.invoke('element:details:import:data:format', val).then((consume: any) => {
            fn && fn(consume, checkedResult)
            // TODO: 单据上导入发票的埋点
            const { billSpecification, flowId, code } = this?.props
            const { feeTypeId, feeTypeForm } = consume
            const invoiceIds = orders?.map((it: any) => {
              return it?.invoiceId?.id || it?.invoiceId
            })
            api?.logger?.info(`单据上导入发票`, {
              specificationId: billSpecification?.id,
              specificationName: billSpecification?.name,
              flowId,
              code,
              sceneName: '发票',
              feeTypeId: feeTypeId?.id,
              feeTypeName: feeTypeId?.name,
              operateType: '导入',
              invoiceIds: invoiceIds,
              resultForm: feeTypeForm
            })
          })
        })
      } else {
        // 随手记导入发票
        api?.invoke('record:expends:details:import:data:format', val).then((consume: any) => {
          fn && fn(consume, checkedResult)
          // TODO: 随手记导入发票的埋点
          const { billSpecification, flowId, code } = this?.props
          const { feeTypeId, feeTypeForm } = consume
          const invoiceIds = orders?.map((it: any) => {
            return it?.invoiceId?.id || it?.invoiceId
          })
          api?.logger?.info(`随手记导入发票`, {
            specificationId: billSpecification?.id,
            specificationName: billSpecification?.name,
            flowId,
            code,
            sceneName: '发票',
            feeTypeId: feeTypeId?.id,
            feeTypeName: feeTypeId?.name,
            operateType: '导入',
            invoiceIds: invoiceIds,
            resultForm: feeTypeForm
          })
        })
      }
    })
  }

  handleImportFinished = (result: any, checkedResult: any[]) => {
    const { dataSource, disableChecked, disableData } = this.state
    const surplus = this.handleCheckedToDisable(checkedResult)
    let cDisableChecked = formatDisableValue(disableChecked, checkedResult)
    const cDisableData = disableData.slice()
    let cDataSource: any[] = []
    let isHalfInvoice = getIsHalfInvoice(cDisableChecked, dataSource, true)
    //isHalfInvoice为true说明只选了一张发票且是半张的情况(选有部分明细的发票不允许在选择其他的发票了,上面有拦截)
    if (isHalfInvoice) {
      cDataSource = dataSource.slice()
      //将选中的发票明细在dataSource中对应的项发票明细和主体的active改为false
      cDataSource.forEach(line => {
        const cDisableCheckedList = cDisableChecked.filter(item => line.groupId === item.key)
        cDisableCheckedList.forEach((vv: any) => {
          let lineDetails = line.details.find((oo: any) => oo.master.id === vv.value)
          if (lineDetails) {
            let ids = vv.valueObj.details.map((oo: any) => oo.id)
            lineDetails.details.forEach((detailLine: any) => {
              if (!!~ids.indexOf(detailLine.id)) {
                detailLine.active = false
                lineDetails.master.active = false
              }
            })
          }
        })
      })
    } else {
      dataSource.forEach(line => {
        const cDisableCheckedList = cDisableChecked.filter(item => line.groupId === item.key)
        const len0 = cDisableCheckedList.length
        const len1 = cDisableData.filter(itm => line.groupId === itm.key).length
        const details = get(line, 'details', []) || []
        if (details.length !== len1 + len0) {
          cDataSource.push(line)
        } else {
          remove(cDisableChecked, function(n) {
            return n.key === line.groupId
          })
          remove(disableData, function(n) {
            return n.key === line.groupId
          })
        }
      })
    }

    const children = getAllDataChildren(cDataSource)
    this.setState({
      disableChecked: cDisableChecked,
      checkedResult: surplus,
      disableData: cDisableData,
      dataSource: cDataSource
    })
    this.importResult = this.importResult.concat(result)
    return children.length === cDisableChecked.length + disableData.length
  }

  handleCheckedToDisable(checkedFinished: any) {
    const { checkedResult } = this.state
    const ids = checkedFinished.map((line: any) => line.key + '_' + line.value)
    return checkedResult.filter(line => ids.indexOf(line.key + '_' + line.value) < 0)
  }

  renderActions = () => {
    const { dataSource, checkedResult, disableChecked, disableData } = this.state
    const { params } = this.props
    const children = getAllDataChildren(dataSource)
    const disableCheckedList = fnFilterDisableChecked(checkedResult, disableChecked)
    const { isCheckedResult, isDisableChecked, isDisableData } = fnInvoiceSelectAll(
      dataSource,
      checkedResult,
      disableCheckedList,
      disableData
    )
    const len = checkedResult.length + disableCheckedList.length + disableData.length
    const disLen = disableCheckedList.length + disableData.length
    const isSecteAll = isCheckedResult || isDisableChecked || isDisableData
    const selectedAll = children.length === len && !isSecteAll
    const disabledAll = children.length === disLen && !(isDisableChecked || isDisableData)
    let { count, total } = getAllInvoiceDetailsCount(checkedResult)
    const fnClick = () => {
      if (disabledAll) {
        return
      }
      this.handleCheckedAll(!selectedAll)
    }
    return (
      <div className="actions">
        <CheckboxItem onClick={fnClick} checked={selectedAll} disabled={disabledAll}>
          <span className="invoice-select-all">{i18n.get('全选')}</span>
        </CheckboxItem>
        <div className="invoice-select-total">
          {i18n.get('已选{__k0}张,共', { __k0: count })}
          <Money currencySize={14} valueSize={14} value={total} />
        </div>
        <div className="continue" onClick={this.handleContinue}>
          <UploadOCRPortal type={params?.type}>
            <Button category={'text'} theme={'highlight'} icon={<OutlinedTipsAdd />}>
              {i18n.get('继续添加')}
            </Button>
          </UploadOCRPortal>
        </div>
      </div>
    )
  }

  handleShowImg = (url: string) => {
    api.open('@invoiceOCR:PreviewOCRImageModal', { markupUrl: url })
  }

  renderTitle(line: any) {
    const { count, fileId } = line
    const name = get(line, 'fileId.key', '')
    const isXml = name.toLowerCase().endsWith('xml')
    const imgSrc = geShowInvoiceImg(fileId)
    const regions = line.details.map((oo: any) => get(oo, 'master.form.E_system_发票主体_识别范围', []))
    return (
      <div className="group-title">
        <div className="title">{i18n.get(`共识别 {__k0} 张发票`, { __k0: count })}</div>
        {isInvoiceImage(fileId) ? (
          <div className="images">
            <ImageElementCard
              regions={regions}
              attachment={fileId}
              currentRegion={[]}
              currentOrientation={0}
              handleShowImg={this.handleShowImg}
            />
          </div>
        ) : (
          <div className={`images-default ${isXml ? 'xml-bg' : ''}`}>{imgSrc}</div>
        )}
      </div>
    )
  }

  handleFoldClick = (line: any, parentIndex: number, index: number, fileId: any) => {
    const { dataSource } = this.state
    //status === 'EDIT' ocr识别错误才会返回该类型 此时可以编辑
    if (line.status === 'EDIT') {
      this.handleOnEdit(line, parentIndex, index, fileId)
    } else {
      let cDataSource = dataSource.slice()
      cDataSource[parentIndex].details[index].isFold = !line.isFold
      this.setState({ dataSource: cDataSource })
    }
  }

  handleRetryCheckerInvoiceClick = (line: any, parentIndex: number, index: number, fileId: any) => {
    const { dataSource } = this.state
    let cDataSource = dataSource.slice()
    const invoiceId = line.master.id
    api.invokeService('@invoice:retry:checker:invoice', invoiceId).then((data: any) => {
      cDataSource[parentIndex].details[index] = { ...data.value, isFold: line.isFold }
      const { disableChecked, disableData } = initData(cDataSource)
      this.setState({ dataSource: cDataSource, disableChecked, disableData })
    })
  }

  renderInvoiceTitle(line: any, parentIndex: number, index: number, fileId: any) {
    return (
      <ItemTitleView
        value={line.master}
        details={line.details}
        isFold={line.isFold}
        message={line.message}
        bindValidateMessage={line.bindValidateMessage}
        status={line.status}
        onItemClick={this.handleFoldClick.bind(this, line, parentIndex, index, fileId)}
        onRetryCheckerInvoiceClick={this.handleRetryCheckerInvoiceClick.bind(this, line, parentIndex, index, fileId)}
      />
    )
  }

  renderInvoiceCheckBoxItem(itemData: any[], isFold: boolean, fileId: any) {
    return itemData.map((item, index) => {
      return (
        <EKBCheckboxItem
          className={'checkbox-item'}
          value={item.id}
          key={index}
          valueObj={{ ...item, fileId }}
          style={isFold ? {} : { display: 'none' }}
        >
          <ItemContentView value={item} />
        </EKBCheckboxItem>
      )
    })
  }

  renderCheckBoxItem(props: any) {
    const { itemData, groupId, parentIndex, fileId } = props
    const len = itemData.length
    return itemData.map((item: any, index: number) => {
      const { id, form, entityId: type } = item.master
      const Component = mapView[type]
      const isLastItem = index === len - 1
      // @i18n-ignore
      let isAvailable = type === 'system_发票主体' && item.status === 'EDIT'
      // @i18n-ignore
      const isInvoiceEdit =
        type === 'system_发票主体' &&
        item.status !== 'EDIT' &&
        item.status !== 'UNEXAMINED' &&
        !isElectronicAirAndTrain(form)
      if (type === 'system_非税收入类票据') {
        isAvailable = !item.ischeck
      }

      if (isInvoiceEdit) {
        const { invoiceDisableData, InvoiceDisableChecked, invoiceCheckedValues } = this.handleInvoiceAsShow(props, id)
        return (
          <div className="invoice-item" key={id} valueObj={{ ...item, fileId }} value={id}>
            <EkbCheckBoxGroup
              isShowLine={true}
              renderTitle={this.renderInvoiceTitle.bind(this, item, parentIndex, index, fileId)}
              name={item.master.id}
              childrenCount={item.details.length}
              disableData={invoiceDisableData}
              disableChecked={InvoiceDisableChecked}
              checkedValues={invoiceCheckedValues}
              onCheckedValues={this.handleInvoiceOnCheckedValue.bind(this, groupId, parentIndex, index)}
            >
              {this.renderInvoiceCheckBoxItem(item.details, item && item.isFold, fileId)}
            </EkbCheckBoxGroup>
          </div>
        )
      }
      return (
        <EKBCheckboxItem
          key={id}
          className={`item-wrapper ${!isInvoiceEdit ? 'set_other_icon' : ''}`}
          valueObj={{ ...item, fileId }}
          value={id}
          isShow={!isLastItem}
          isAvailable={!isAvailable}
        >
          <Component
            {...item}
            onDelete={this.handleOnDelete.bind(this, item, groupId)}
            onEdit={this.handleOnEdit.bind(this, item, parentIndex, index, fileId)}
            onRetryCheckerInvoiceClick={this.handleRetryCheckerInvoiceClick.bind(
              this,
              item,
              parentIndex,
              index,
              fileId
            )}
          />
        </EKBCheckboxItem>
      )
    })
  }

  renderBottomActions = () => {
    const { checkedResult, dataSource } = this.state
    const len = checkedResult.length
    const { params, isDetail } = this.props
    const enterType = params && params.enterType
    // 研究所私有化需求
    const isShouldChangeButtonPlace = window.PLATFORMINFO.name === 'zhongdian'

    return (
      <div className="bottom-actions">
        {enterType === 'invoiceFrom' ? (
          <div className="actions-btn">
            <div className="multiple" onClick={this.handleBindConsume}>
              {isDetail ? i18n.get('与该消费绑定') : i18n.get('与该单据绑定')}
            </div>
          </div>
        ) : (
          <div className="actions-btn-import">
            {isShouldChangeButtonPlace && (
              <>
                <div
                  className={classNames('action-btn-import-button', { disabled: len === 0 })}
                  onClick={len > 0 ? this.handleMergeConsume.bind(this, len) : () => {}}
                >
                  {i18n.get('合并生成消费')}
                </div>
                <div
                  className={classNames('action-btn-import-button', 'primary', { disabled: len === 0 })}
                  onClick={() => this.handleConsume(false)}
                >
                  {i18n.get('生成{__k0}条消费', { __k0: len })}
                </div>
              </>
            )}
            {!isShouldChangeButtonPlace && (
              <>
                <div
                  className={classNames('action-btn-import-button', { disabled: len === 0 })}
                  onClick={() => this.handleConsume(false)}
                >
                  {i18n.get('生成{__k0}条消费', { __k0: len })}
                </div>
                <div
                  className={classNames('action-btn-import-button', 'primary', { disabled: len === 0 })}
                  onClick={len > 0 ? this.handleMergeConsume.bind(this, len) : () => {}}
                >
                  {i18n.get('合并生成消费')}
                </div>
              </>
            )}
          </div>
        )}
      </div>
    )
  }

  render() {
    const { dataSource } = this.state
    const {
      params: { submitter }
    } = this.props
    const dimensionCurrencyInfo = app.getState()['@bill']?.dimensionCurrencyInfo
    const isHasDimensionCurrency = enableOtherInvoiceByDimension() && dimensionCurrencyInfo?.currency
    return (
      <div className={styles['tickets-wrapper']}>
        {isHasDimensionCurrency && <NoticeBar content={i18n.get('为确保多币种金额正确请检查信息')} color="info" />}
        {this.renderActions()}
        <div className="tickets-content">
          <div className="inertial-rolling">
            {dataSource.map((line, index) => {
              if (line.isLoading) {
                return <LoadingView key={index} {...line} />
              }
              const { checkedValues, disData, disableValues } = this.handleAsShow(line)
              const { checkedValueList, disDataList, disableValuesList } = formatLineValue(
                checkedValues,
                disData,
                disableValues,
                line
              )
              return line.count && line.count > 0 ? (
                <div key={index} className="group-wrapper">
                  <EkbCheckBoxGroup
                    isShowLine={true}
                    renderTitle={this.renderTitle.bind(this, line)}
                    key={index}
                    name={line.groupId}
                    childrenCount={line.details.length}
                    disableData={disDataList || []}
                    disableChecked={disableValuesList || []}
                    checkedValues={checkedValueList || []}
                    disableGroupData={disData}
                    disableGroupChecked={disableValues}
                    checkedGroupValues={checkedValues}
                    submitter={submitter}
                    onCheckedValues={this.handleOnCheckedValue.bind(this, line.groupId)}
                  >
                    {this.renderCheckBoxItem({
                      itemData: line.details,
                      groupId: line.groupId,
                      parentIndex: index,
                      fileId: line.fileId,
                      disableData: disData || [],
                      disableChecked: disableValues || [],
                      checkedValues: checkedValues || []
                    })}
                  </EkbCheckBoxGroup>
                </div>
              ) : (
                ''
              )
            })}
          </div>
        </div>
        {this.renderBottomActions()}
      </div>
    )
  }
}
