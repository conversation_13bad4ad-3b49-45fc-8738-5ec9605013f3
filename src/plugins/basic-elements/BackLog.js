/**************************************************
 * Created by nanyuanting<PERSON> on 22/09/2016 17:33.
 **************************************************/
const Flow = require('./Flow')

class BackLog {
  constructor(line) {
    this.updateUseNewLine(line)
  }

  updateUseNewLine(line) {
    this.id = line.id
    this.ownerId = line.ownerId
    this.flowId = line.flowId
    this.logId = line.logId
    this.state = line.state
    this.updateTime = line.updateTime

    this.flow = new Flow(line.flowId)
    return this
  }
}

export default BackLog
