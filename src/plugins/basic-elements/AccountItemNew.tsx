/**************************************
 * Created By LinK On 2019/12/12 18:38.
 **************************************/

import React, { useState, useMemo } from 'react'
import { app as api } from '@ekuaibao/whispered'
import styles from './AccountItemNew.module.less'
const EKBIcon = api.require('@elements/ekbIcon') as any
import { getPayeeAccountBg, getPayeeTextColor } from '../../lib/enums'
import _compact from 'lodash/compact'
import { Space, Tag } from '@hose/eui-mobile'
import {
  FilledGeneralCollect,
  OutlinedGeneralMore,
  OutlinedGeneralGroup,
  OutlinedGeneralMember,
  OutlinedTipsDone
} from '@hose/eui-icons'
const BANK_DISABLE_BG = require('./images/bank_disable_bg.svg')
const BANK_BG = require('./images/bank_bank_bg.svg')
const BANK_CORPORATE_BG = require('./images/bank_corporate_bg.svg')
const BANK_OTHER_BG = require('./images/bank_other_bg.svg')
const BANK_WECHAT_BG = require('./images/bank_wechat_bg.svg')

interface AccountProps {
  accountName: string
  accountNo: string
  active: true
  bank: string
  bankLinkNo?: string
  branch: string
  branchId: any
  cardNo: string
  certificateNo: string
  certificateType: string
  favoriteStatus: boolean
  city: string
  code: string
  corporationId: string
  createTime: number
  icon: string
  id: string
  isDefault: boolean
  logs: any[]
  name: string
  nameSpell: string
  owner: string
  province: string
  sort: string
  staffId?: any
  type: string
  unionBank: string
  unionIcon: string
  updateTime: number
  version: number
  visibility: any
  remark: any
  filterActive: boolean
  customFields?: []
}

interface Props {
  isDefault: boolean
  selected?: boolean
  account: AccountProps
  payeeConfig?: any
  fnHandleLineClick: (e: any, account: AccountProps) => void
  fnHandleSelectType: (account: AccountProps) => void
  globalFields: []
  style: any
}
const PERSONAL = 'PERSONAL'

export default function(props: Props) {
  const {
    account,
    fnHandleSelectType,
    fnHandleLineClick,
    isDefault,
    selected,
    payeeConfig,
    globalFields,
    style = {}
  } = props
  const { accountName, accountNo, sort, favoriteStatus } = account
  const me_info = api.getState()['@common'].me_info
  const owner = account.owner === 'CORPORATION' ? i18n.get('企业') : account.staffId && account.staffId.name
  // const isShared = account.staffId && account.staffId.id !== me_info.staff.id
  const isShared = account.visibility && account.visibility.fullVisible
  const icon = !!account.unionBank ? account.unionIcon : account.icon
  const bankName =
    sort === 'ALIPAY'
      ? i18n.get('支付宝(办公)')
      : account?.branchId?.name || account?.branch || account.unionBank || account.bank
  const type = account.type === PERSONAL ? i18n.get('个人账户') : i18n.get('对公账户')
  const payeeSetConfig =
    account.type === 'PUBLIC' ? payeeConfig?.publicAccountConfig : payeeConfig.personalAccountConfig

  const handleSelectType = (e: any) => {
    e.preventDefault()
    e.stopPropagation()
    fnHandleSelectType(account)
  }
  const remarkDisplay =
    account.type === 'PERSONAL'
      ? payeeConfig?.personalAccountConfig?.remarkDisplay
      : payeeConfig?.publicAccountConfig?.remarkDisplay
  const remark = account?.remark || ''
  const showRemark = remark && remarkDisplay

  const getBgImg = (sort: string) => {
    if (!account.filterActive) {
      return BANK_DISABLE_BG
    } else if (payeeSetConfig?.publicBackgroundCls) {
      return BANK_CORPORATE_BG
    }
    switch (sort) {
      case 'WEIXIN':
        return BANK_WECHAT_BG
      case 'BANK':
        return BANK_BG
      default:
        return BANK_OTHER_BG
    }
  }

  return (
    <div
      style={selected ? { ...style, border: '1px solid var(--eui-primary-pri-500, #2555FF)' } : style}
      className={styles['account-item-wrapper']}
      onClick={(e: any) => fnHandleLineClick(e, account)}
    >
      <div style={{ position: 'relative' }}>
        <div
          className="account-item-top"
          style={{ background: getPayeeAccountBg(sort, !account.filterActive, payeeSetConfig?.publicBackgroundCls) }}
        >
          <img src={getBgImg(sort)} className="account-item-top-bg"></img>
          <div className="account-item-top-title">
            <div
              className="account-item-top-title-left"
              style={{ color: getPayeeTextColor(sort, !account.filterActive, payeeSetConfig?.publicBackgroundCls) }}
            >
              {accountName}
            </div>
            <Space style={{ '--gap': '12px' }}>
              {favoriteStatus && <FilledGeneralCollect color="var(--eui-function-warning-400)" fontSize={16} />}
              {sort !== 'VIRTUALCARD' && account.filterActive && (
                <div onClick={handleSelectType}>
                  <OutlinedGeneralMore color="var(--eui-icon-n2)" fontSize={16} />
                </div>
              )}
            </Space>
          </div>
          <div
            className="account-item-top-number"
            style={{ color: getPayeeTextColor(sort, !account.filterActive, payeeSetConfig?.publicBackgroundCls) }}
          >
            {formatCardNo(accountNo)}
          </div>
          <div className="account-item-top-tag">
            <Space style={{ '--gap': '4px' }}>
              <Tag fill="outline">
                <span>
                  {account.type === PERSONAL ? (
                    <OutlinedGeneralMember fontSize={12} style={{ marginRight: 2 }} />
                  ) : (
                    <OutlinedGeneralGroup style={{ marginRight: 2 }} fontSize={12} />
                  )}
                  {type}
                </span>
              </Tag>
              {isShared && <Tag fill="outline">{i18n.get('共享')}</Tag>}
              {isDefault && <Tag color="pri">{i18n.get('默认')}</Tag>}
            </Space>
            {showRemark && <div className="account-item-top-remark">{`${i18n.get('备注：')}${remark}`}</div>}
          </div>
        </div>
        <div className="account-item-bottom">
          <img className="account-item-bottom-icon" src={`${icon}?type=new`} />
          <span className="account-item-bottom-bankName">{bankName}</span>
          <span>{`${i18n.get('所有者：')}${owner}`}</span>
        </div>
        {selected && <div className="account-item-mask" />}
        {selected && <OutlinedTipsDone className="mask-icon" color="white" fontSize={10.8} />}
      </div>
      {/* <DisplayMore account={account} globalFields={globalFields} payeeSetConfig={payeeSetConfig} /> */}
    </div>
  )
}

// 账户组件展示更多
const DisplayMore = (props: any) => {
  const { account, globalFields, payeeSetConfig } = props
  const [displayArrow, setDisplayArrow] = useState(false)

  const customFilesData = useMemo(() => {
    const showCardCustomFields = payeeSetConfig?.showCardCustomFields || []
    return _compact(
      showCardCustomFields.map((item: any) => {
        if (account.customFields && account.customFields[item]) {
          return {
            label: globalFields.filter((v: any) => v.name === item)[0]?.label || '',
            value: (account.customFields && account.customFields[item]) ?? ''
          }
        } else {
          return null
        }
      })
    )
  }, [])

  const handleOpen = (e: any) => {
    e && e.stopPropagation && e.stopPropagation()
    setDisplayArrow(!displayArrow)
  }

  return customFilesData.length ? (
    <div className={displayArrow ? 'account-shadow  open' : 'account-shadow'} onClick={handleOpen}>
      {customFilesData.map((item: any, index) => {
        return (
          <div className="other-files" key={item.value}>
            <span>
              {item.label}：{item.value}
            </span>
            {index === 0 && customFilesData.length !== 1 ? (
              <span>
                <EKBIcon className={'toggle'} name="#EDico-up-default" />
              </span>
            ) : null}
          </div>
        )
      })}
    </div>
  ) : (
    <></>
  )
}

const formatCardNo = (cardNo: string) => {
  if (/[^0-9]/.test(cardNo)) {
    return cardNo
  }
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1 ')
}
