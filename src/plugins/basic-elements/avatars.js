import './avatar.less'
import React, { Component } from 'react'
import { app } from '@ekuaibao/whispered'
const SVG_AVATAR_NULL = app.require('@images/avatar-null.svg')

export default class Avatar extends Component {
  renderClassName(imgList, isStyleChange) {
    if (imgList.length < 4) {
      return isStyleChange
        ? `avatar-square avatar-wrap avatar-wrap-${imgList.length}`
        : `avatar-wrap avatar-wrap-${imgList.length}`
    }
    return isStyleChange ? 'avatar-square avatar-wrap avatar-wrap-4' : 'avatar-wrap avatar-wrap-4'
  }

  renderImg(url, idx) {
    return (
      <div key={idx} className="avatar-img">
        <img src={url} />
      </div>
    )
  }

  render() {
    let { imgList, isStyleChange, style } = this.props
    imgList = Array.isArray(imgList) ? imgList : [imgList || SVG_AVATAR_NULL]
    return (
      <div className={this.renderClassName.call(this, imgList, isStyleChange)} style={style}>
        {imgList.map((el, idx) => this.renderImg(el, idx))}
      </div>
    )
  }
}
