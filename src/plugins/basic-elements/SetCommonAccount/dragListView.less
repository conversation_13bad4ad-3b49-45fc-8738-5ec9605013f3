@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.set-common-account-sortable-item{
    list-style: none;
    height: 136px;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: @color-white-1;
    z-index: 1000;
    .set-common-account-sortable-item-action-wrap{
        flex-shrink: 0;
        width: 112px;
        height: 48px;
        text-align: center;
        .icon {
          width: 48px;
          height: 48px;
          color: @color-error-2;
        }
    }
    .set-common-account-sortable-item-content-wrap{
        flex: 1;
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-bottom: 2px solid @color-line-2;
        min-width: 0;
        .set-common-account-sortable-item-content-left{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            min-width: 0;
            padding-right: @space-6;
            max-width: 100%;
            span {
                width: 100%;
                user-select: none;
                color: @color-black-1;
                .font-size-3;
                .font-weight-3;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
        .set-common-account-sortable-item-content-right{
            height: 100%;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;
            .common-account-icon-wrap {
                height: 100%;
                display: flex;
                align-items: center;
                padding: 0 @space-6;
                margin-left: @space-2;
            }
            .icon {
                width: 42px;
                .icon-size-5;
                color: @color-black-4;
            }
        }
    }
    
}