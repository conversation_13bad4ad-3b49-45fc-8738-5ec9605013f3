/**************************************
 * Created By LinK On 2019/12/16 15:21.
 **************************************/

import React from 'react'

interface Props {
  children: React.ReactNode
  label?: string
  require: boolean
  labelDisabled: boolean
}

export default function AddPayeeItem(props: Props) {
  const { label, children, require, labelDisabled } = props
  const labelStyle = labelDisabled ? { color: 'gray' } : {}

  return (
    <div className="addPayeeItem_wrapper">
      {label && (
        <div>
          {require && <span style={{ color: '#fc3842', fontSize: 14, marginRight: 4 }}>*</span>}
          <span style={labelStyle}>{label}</span>
        </div>
      )}
      {children}
    </div>
  )
}
