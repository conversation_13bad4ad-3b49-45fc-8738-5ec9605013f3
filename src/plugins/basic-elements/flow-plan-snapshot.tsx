import React from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './enhance-title-hook'
import { app } from '@ekuaibao/whispered'
import FLowPlanEditable from './flow-plan-editable'
import { getV } from '@ekuaibao/lib/lib/help'

interface PlanSnapshotProps {
  params: any
}

interface PlanSnapshotState {
  flow?: any
}

@EnhanceTitleHook(i18n.get('审批流快照'))
export default class PlanSnapshot extends React.PureComponent<PlanSnapshotProps, PlanSnapshotState> {
  componentDidMount(): void {
    const {
      params: { id, type, planVersionId }
    } = this.props
    Promise.all([
      app.invokeService('@common:get:flow:detail:info', { id, type }),
      app.invokeService('@detail:getVersionFlowPlanById', { planId: planVersionId })
    ]).then(result => {
      const [flow, planData] = result
      const plan = getV(planData, 'flowPlan')
      this.setState({ flow: { ...flow.value, plan } })
    })
  }

  render() {
    const flow = getV(this, 'state.flow')
    if (!flow) {
      return <></>
    }
    return <FLowPlanEditable flow={flow} isReadOnly={true} />
  }
}
