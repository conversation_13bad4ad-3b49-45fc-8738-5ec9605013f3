@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import "../../styles/theme.less";

.account-wrapper-blue {
  background: linear-gradient(101deg, rgba(55, 142, 208, 1) 0%, rgba(55, 100, 191, 1) 100%) !important;
}

.mod-green-bg {
  background: linear-gradient(101deg,#14C9C9 8%, #3599BB 100%) !important;
}

.account-wrapper-green {
  background: linear-gradient(100deg,#34cb4c 8%, #22ac38 93%) !important;
}

.account-wrapper-gray {
  background: linear-gradient(101deg, rgba(165, 169, 170, 1) 0%, rgba(194, 198, 198, 1) 100%) !important;
}

.account-wrapper-selected {
  :global {
    .account-selected-mask {
      display: flex !important;
      opacity: 1 !important;
      background: @color-black-3;
      border-radius: @radius-3;
    }
  }
}

.account-wrapper {
  position: relative;
  margin: @space-4;
  padding: @space-5 @space-6;
  border-radius: @radius-3;
  background: linear-gradient(101deg, rgba(253, 117, 102, 1) 0%, rgba(252, 82, 98, 1) 100%);
  :global {
    .account-absolute-class {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      overflow: hidden;
    }
    .account-selected-mask {
      .account-absolute-class;
      z-index: 1;
      display: none;
      align-items: center;
      justify-content: center;
      opacity: 0;
      .account-selected-icon {
        width: 148px;
        height: 148px;
        color: @color-white-2;
      }
    }
    .account-icon-wrap {
      .account-absolute-class;
      .account-bg-icon {
        position: absolute;
        right: 10%;
        top: 40px;
        height: 200px;
        width: 36%;
        transform: rotate(-15deg);
        color: rgba(255, 255, 255, 0.15);
      }
    }
    .account-header {
      position: relative;
      display: flex;
      flex-direction: row;
      .font-size-2;
      height: 40px;
      line-height: 40px;
      color: @color-white-1;
      .account-header-left {
        display: flex;
        flex-direction: row;
        overflow: hidden;
        margin-right: 40px;
        .account-header-name {
          display: inline-block;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .font-weight-3;
        }
        .account-header-left-right-part {
          display: flex;
          .account-header-span {
            .font-size-1;
            flex-shrink: 0;
            margin-left: @space-4;
            display: inline-block;
            height: 40px;
            line-height: 40px;
            padding: 0 @space-3;
            background-color: @color-white-6;
            border-radius: @radius-2;
            color: @color-white-1;
          }
        }
      }
      .account-header-right {
        z-index: 1;
        position: absolute;
        right: 0;
        // top: 0;
        > span {
          font-size: medium;
        }
      }
    }
    .account-content {
      padding: 46px 0;
      color: @color-white-1;
      .account-content-inner {
        .font-weight-3;
        height: 44px;
        font-size: 44px;
        line-height: 1;
        width: 100%;
        display: inline-block;
        text-align: center;
      }
    }
    .account-footer {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      .font-size-2;
      height: 40px;
      line-height: 40px;
      color: @color-white-1;
      overflow: hidden;
      .account-bank-name {
        z-index: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: @space-4;
        .font-weight-2;
      }
      .remark-item {
        z-index: 1;
      }
      .account-footer-right {
        flex: 1;
        text-align: right;
      }
      .account-icon-box {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        width: 32px;
        background-color: @color-white-1;
        border-radius: 50%;
        margin-right: @space-4;
        .account-icon {
          width: 32px;
          height: 32px;
        }
      }
    }
    @keyframes fade{
      100%{ height: 300px;}
    }
    .account-shadow{
      position: relative;
      display: flex;
      flex-direction: column;
      background-color: rgba(255, 255, 255, 0.15);
      border-radius: @radius-3;
      color: @color-white-1;
      margin: @space-5 -@space-6 -@space-5;
      padding: @space-4 @space-5;
      font-size: @font-size-base;

      .toggle {
        transform: rotate(180deg);
        transition: transform 0.3s;
      }

      &.open{
          animation: fade 2.5s;
          .toggle{
            transform: rotate(0deg);
          }
          .other-files{
            display: flex;
          }
      }
      .other-files{
        display: none;
        justify-content: space-between;
        font-size: 14px;
        word-break: break-all;
        &:first-child{
          display: flex;
        }
      }
      .icon{
        width: 40px;
        font-size: 18px;
        display: flex;
      }
    }
  }
}