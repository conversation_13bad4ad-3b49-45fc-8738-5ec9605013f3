/**
 *  Created by gym on 2018/12/29 下午3:57.
 */
import React, { PureComponent } from 'react'
import { RenderWaitting } from './util'
// @ts-ignore
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager-mobile'
import { app as api } from '@ekuaibao/whispered'
// @ts-ignore
import styles from './WaitAnimationModal.module.less'

interface Props {
  layer: any
}

interface State {}

export default class WaitAnimationModal extends PureComponent<Props, State> {
  timer: any
  componentDidMount() {
    this.timer = setInterval(() => {
      api.invokeService('@approve:get:batch:approve:result').then((result: any) => {
        if (!result.value || result.value.residue === 0) {
          clearInterval(this.timer)
          this.props.layer.emitCancel()
        }
      })
    }, 3000)
  }

  componentWillUnmount() {
    clearInterval(this.timer)
  }

  render() {
    return (
      <div className={styles['wait-animation-wrapper']}>
        <RenderWaitting />
      </div>
    )
  }
}
