import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by nanyuantingfeng on 23/09/2016 13:48.
 **************************************************/
import Big from 'big.js'
import { getAmount } from '../../lib/money-conversion'

class Form {
  constructor(line) {
    this.line = line
    this.template = line.template
    this.department = line.department
    if (this.template) {
      this.flowPlanTemplate = this.template.flowPlanTemplate
    }
  }

  getAmount() {
    let details = this.line.details || []

    let aa = details.map(line => line.feeTypeForm)

    return aa.map(a => getAmount(a.amount)).reduce((a, b) => a.plus(b), new Big(0))
  }

  getLoanAmount() {
    let loanMoney = this.line.loanMoney || 0
    const { thousandBitSeparator } = app.require('@components/utils/fnThousandBitSeparator')
    return thousandBitSeparator(new Big(loanMoney).toFixed(2))
  }

  getTemplateId() {
    return this.line.specification
  }

  getTemplateName() {
    return this.template.name
  }

  getFlowTemplateId() {
    return this.template.flowPlanId
  }

  get(key) {
    return this.line[key]
  }
}

export default Form
