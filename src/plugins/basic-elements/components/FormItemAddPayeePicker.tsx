import React, { FC, useEffect, useState } from 'react'
import { OutlinedDirectionRight } from '@hose/eui-icons'
import { Input, Picker } from '@hose/eui-mobile'
interface IProps {
  value?: string[]
  data?: any[]
  onChange?: (value: string[]) => void
  title?: string //picker标题
  id?: string
}

const FormItemAddPayeePicker: FC<IProps> = props => {
  const { value, title, onChange, data, id } = props
  const [pickerVisible, setPickerVisible] = useState(false)
  const [inputValue, setInputValue] = useState()
  const handleChange = (value: string[]) => {
    setPickerVisible(false)
    onChange?.(value)
  }
  useEffect(() => {
    setInputValue(data?.find(item => item.value === value?.[0])?.label)
  }, [value, data])

  return (
    <div
      id={id}
      onClick={() => {
        setPickerVisible(true)
      }}
    >
      <Picker
        columns={[data]}
        title={title}
        visible={pickerVisible}
        value={value}
        onCancel={() => {
          setPickerVisible(false)
        }}
        onConfirm={handleChange}
      />
      <Input readOnly suffix={<OutlinedDirectionRight />} placeholder={i18n.get('请选择')} value={inputValue} />
    </div>
  )
}

export default FormItemAddPayeePicker
