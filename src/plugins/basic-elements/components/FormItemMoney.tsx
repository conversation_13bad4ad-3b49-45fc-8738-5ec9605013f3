import React, { FC, useEffect, useState } from 'react'
import { OutlinedDirectionDown } from '@hose/eui-icons'
import { Input, Picker, Divider } from '@hose/eui-mobile'
import styles from './FormItemMoney.module.less'
import { PickerValue } from '@hose/eui-mobile/es/components/picker'
import Big from 'big.js'
interface IProps {
  value?: { numCode: string; amount: string; strCode: string }
  bankRatesList?: any[]
  updateRate: (value: string) => void
  onChange?: (value: { numCode: string; amount: string; strCode: string }) => void
  standardAmount: any
  currentAmount: any //当前收款币种
  isDateChange: boolean //是否是更新日期
  allowEditAmount: boolean //金额是否可编辑
}

const FormItemMoney: FC<IProps> = props => {
  const {
    onChange,
    bankRatesList,
    updateRate,
    value,
    standardAmount,
    currentAmount,
    isDateChange = false,
    allowEditAmount
  } = props
  const [pickerVisible, setPickerVisible] = useState(false)
  const [data, setData] = useState([])

  useEffect(() => {
    if (bankRatesList?.length && standardAmount) {
      const list = bankRatesList.map(item => ({ value: item.numCode, label: item.strCode, other: item }))
      const changeData =
        (isDateChange
          ? bankRatesList.find(item => item.numCode === value?.numCode)
          : bankRatesList.find(item => item.numCode === currentAmount?.numCode)) || bankRatesList[0]
      setData(list)
      const changeAmount = changeData.rate ? new Big(standardAmount.standard).div(changeData.rate).toFixed(2) : '0.00'
      const oldeAmount = value?.amount ?? changeAmount
      onChange?.({
        numCode: changeData.numCode,
        strCode: changeData.strCode,
        amount: allowEditAmount ? oldeAmount : changeAmount
      })
      if (!allowEditAmount) {
        //金额可编辑的时候不需要更新新汇率，因为不显示汇率这个字段
        updateRate(changeData.rate)
      }
    }
  }, [bankRatesList, standardAmount])

  useEffect(() => {
    if (bankRatesList?.length && standardAmount && currentAmount) {
      const list = bankRatesList.map(item => ({ value: item.numCode, label: item.strCode, other: item }))
      const changeData = bankRatesList.find(item => item.numCode === currentAmount?.numCode) || bankRatesList[0]
      setData(list)
      onChange?.({
        numCode: changeData.numCode,
        strCode: changeData.strCode,
        amount: changeData.rate ? new Big(standardAmount.standard).div(changeData.rate).toFixed(2) : '0.00'
      })
      if (!allowEditAmount) {
        //金额可编辑的时候不需要更新新汇率，因为不显示汇率这个字段
        updateRate(changeData.rate)
      }
    }
  }, [currentAmount])

  const handleCurrencySelectChange = (selectedItem: PickerValue[]) => {
    const selectedData = bankRatesList.find((v: any) => v?.numCode === selectedItem?.[0])
    onChange?.({
      numCode: selectedData?.numCode,
      strCode: selectedData?.strCode,
      amount: selectedData.rate ? new Big(standardAmount.standard).div(selectedData.rate).toFixed(2) : '0.00'
    })
    updateRate(selectedData?.rate)
    setPickerVisible(false)
  }
  return (
    <div className={styles['form-item-money']}>
      <div className="form-item-money-input">
        <Picker
          columns={[data]}
          title={i18n.get('币种选择')}
          visible={pickerVisible}
          value={[value?.numCode]}
          onCancel={() => {
            setPickerVisible(false)
          }}
          onConfirm={handleCurrencySelectChange}
        />
        <div
          className="form-item-money-input-unit"
          onClick={() => {
            if (data?.length) {
              setPickerVisible(true)
            }
          }}
        >
          {`${i18n.get('实付币种')}（${value?.strCode ?? i18n.get('暂无')}）`}
          <OutlinedDirectionDown color="var(--eui-icon-n2)" fontSize={12} />
        </div>
        <Divider direction="vertical" />
        <Input
          onChange={(amount: string) => {
            if (allowEditAmount) {
              onChange?.({
                ...value,
                amount
              })
            }
          }}
          disabled={!allowEditAmount}
          placeholder={i18n.get("请输入")}
          value={value?.amount ?? '0.00'}
        />
      </div>
    </div>
  )
}

export default FormItemMoney
