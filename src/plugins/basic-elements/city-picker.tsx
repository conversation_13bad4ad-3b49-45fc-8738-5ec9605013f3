/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-03-04 21:43:20
 * @Last Modified by: <PERSON><PERSON>k<PERSON>
 * @Last Modified time: 2019-03-08 15:42:54
 */

import '@ekuaibao/eui-mobile/less/CitiesPicker.less'
import './city-picker.less'
import { getV } from '@ekuaibao/lib/lib/help'
import React, { PureComponent } from 'react'
import <PERSON>han<PERSON><PERSON><PERSON><PERSON>Hook from './enhance-title-hook'
// @ts-ignore
import { CityPicker } from '@ekuaibao/eui-mobile'
// import CityPicker from './test_components/City';
import { connect } from '@ekuaibao/mobx-store'
import { app as api } from '@ekuaibao/whispered'
import { toJS, observable } from 'mobx'

interface CPProps {
  isMulti: boolean
  cityValue: any[]
  hotCity: any
  field: any
  cityList: any[]
  searchCity: any[]
  selectedCity: any[]
  [key: string]: any
  travelerId?: string //出行人id
  travelType?: string //行程类型，区分出发地和目的地
  maxSelectCount?: number
  disableCityList?: string[] // city id []
}
type  behaviorType =  'notAllow' | 'none' | 'allow'
interface CPState {
  selectCity: any
  cityGroup: any[]
  blackCity: { 
    behavior: behaviorType
    cities: string[]
  },
  loading: boolean
}

// @ts-ignore
@connect(store => {
  const { hotCity, cityList, searchCity, selectedCity, cityGroupList, blackIds, isActive } = store.states['@layout']
  return { hotCity, cityList, searchCity, selectedCity, cityGroupList, blackIds, isActive }
})
// @ts-ignore
@EnhanceTitleHook(i18n.get('自定义城市'))
export default class CityPickerModal extends PureComponent<CPProps, CPState> {
  constructor(props: CPProps) {
    super(props)
    this.state = {
      selectCity: null,
      cityGroup: props.cityGroup || [],
      blackCity: props.blackCity || {
        behavior: 'none',
        cities: []
      },
      loading: false
    }
    props.overrideGetResult(this.getResult)
  }

  componentWillReceiveProps(nextPros: CPProps) {
    const { cityGroupList } = nextPros
    if (cityGroupList !== this.props.cityGroupList) {
      this.setState({
        cityGroup: cityGroupList
      })
    }
  }

  componentDidMount() {
    const { cityValue = [], travelerId = '', travelType = '', isMultipleTraveler, notFetchCityGroup } = this.props

    const ids = cityValue.map((c: any) => c.key)
    api.store.dispatch('@layout/setSelectedCity')(ids)
    if (travelType && (travelerId || isMultipleTraveler)) {
      //传了行程类型，并且行程类型不是自定义的时候才调用接口
      !notFetchCityGroup && api.store.dispatch('@layout/setCityGroup')(travelType, travelerId)
    } else {
      this.setState({
        cityGroup: []
      })
    }
    
    this.setState({ loading: true }, () => {
      api.store.dispatch('@layout/setHotCity')(travelerId, travelType)
      api.store.dispatch('@layout/setCityList')({
        travelerId,
        travelType,
        cb: ()=> this.setState({ loading: false})
      })
    })
    this.fetchDisabled()
  }

  fetchCity = (cityId: string = '', cityType: 'inland' | 'international' = 'inland',cityGroupId:string = "") => {
    const { travelerId = '', travelType = '' } = this.props
    api.store.dispatch('@layout/setCityList')({
      cityGroupId,
      travelerId,
      travelType,
      cityId,
      cityType,
      cb: ()=> this.setState({ loading: false})
    })
  }
  searchCity = (value: string) => {
    this.setState({ loading: true }, () => {
      const { travelerId = '', travelType = '' } = this.props
      api.store.dispatch('@layout/setSearchCity')({
        keyword: value,
        travelerId,
        travelType,
        cb: () => this.setState({ loading: false })
      })
    })
  }

  handleGetValue = (value: any) => {
    this.setState({ selectCity: value }, () => {
      this.props.layer.emitOk()
    })
  }

  getCityGroupActive = (staff: any = {}) => {
    const { field, travelerId } = this.props;
    api.store.dispatch('@layout/findLimitedCityIds')(field?.cityGroupId, staff.id || travelerId)
  }

  fetchDisabled = async () => {
    const { field, bus, submitter } = this.props;
    const value = await bus?.getFieldsValue?.('submitterId')
    const { submitterId } = value || {}
    const hasForbiddenCityGroup = getV(field, 'hasForbiddenCityGroup', false)
    if (hasForbiddenCityGroup) {
      this.getCityGroupActive(submitterId || submitter)
    } else {
      api.store.dispatch('@layout/clearBlackIds')()
    }
  }

  resetSelectedCity = () => {
    api.store.dispatch('@layout/setSelectedCity')([])
  }

  fnFormatCityLabel = (label: string) => {
    return label.replace(/^中国,\s*/, '').replace(/,\s*/g, '/')
  }

  getResult = () => {
    const { selectCity } = this.state
    if (!selectCity) {
      return null
    }

    const value = Array.isArray(selectCity) ? [...selectCity] : [selectCity]

    if (!value.length) {
      return null
    }

    return JSON.stringify(
      value.map((v: any) => {
        if (v.desc) {
          return { key: v.id, label: v.name, type: 'cityGroup' }
        } else {
          return { key: v.id, label: this.fnFormatCityLabel(v.fullName) }
        }
      })
    )
  }

  render() {
    let {
      hotCity,
      cityList,
      isMulti,
      maxSelectCount,
      searchCity,
      selectedCity,
      travelerId,
      travelType,
      blackIds,
      disableCityList,
      showHistory,
    } = this.props

    disableCityList && Array.isArray(disableCityList) && (disableCityList = disableCityList.concat(toJS(blackIds)))

    const { cityGroup, blackCity, loading } = this.state
    const lang = {
      domestic: i18n.get('国内'),
      international: i18n.get('国际/中国港澳台'),
      results: i18n.get('搜索结果'),
      hotCities: i18n.get('热门城市'),
      recently: i18n.get('历史选择（含国内/国际）'),
      selected: i18n.get('已选城市'),
      cityGrop: i18n.get('区域范围'),
      noResults: i18n.get('没有搜索结果，要不换个关键词'),
      loading: i18n.get('努力加载中'),
      search: i18n.get('搜索'),
      backTitle:i18n.get('返回全部'),
      toastInfo:i18n.get('城市范围和城市选择不能同时使用'),
      viewCity:i18n.get('查看城市'),
      submit: i18n.get('确 定'),
      noCity: i18n.get('您的权限无可用城市'),
      maxSelectInfo: i18n.get('城市最多添加{__k0}个', { __k0: maxSelectCount }),
      clearAll: i18n.get('清空'),
      emptyText: i18n.get('可选多个城市')
    }

    const showRecently = showHistory ? true : travelerId && travelType ? false : true
    return (
      <div className="city-picker-warper inertial-rolling h-100-percent">
        <CityPicker
          loading={loading}
          travelType={travelType}
          showRecently={showRecently}
          cityGroupList={cityGroup}
          mode={isMulti ? 'multiple' : undefined}
          hotCity={hotCity}
          cityList={cityList}
          searchList={[...searchCity]}
          fetchCity={this.fetchCity}
          searchCity={this.searchCity}
          selectedCity={toJS(selectedCity)}
          onChange={this.handleGetValue}
          resetSelectedCity={this.resetSelectedCity}
          maxSelectCount={maxSelectCount}
          lang={lang}
          disabledCityList={disableCityList || blackIds || []}
          blackCity={blackCity}
        />
      </div>
    )
  }
}
