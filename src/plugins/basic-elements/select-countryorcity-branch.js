import style from './select-bank.module.less'
import React, { PureComponent } from 'react'
import { app } from '@ekuaibao/whispered'
import { SearchBar, List } from '@hose/eui-mobile'
import EnhanceTitleHook from './enhance-title-hook'
import Highlighter from 'react-highlight-words'
import { debounce } from 'lodash'
import { OutlinedTipsClose, OutlinedTipsDone } from '@hose/eui-icons'
const EmptyWidget = app.require('@home5/EmptyWidget')

@EnhanceTitleHook(props => {
  let { label } = props.data
  return i18n.get('请选择{__k0}', { __k0: label })
})
export default class SelectCountryorCityBranch extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      searchText: '',
      isSearch: false,
      list: props.data.searchList || [],
      allList: props.data.searchList || [],
      fieldName: props.data.fieldName,
      placeholder: props.data.placeholder
    }
    props.overrideGetResult(this.getResult)
    this.flag = true
  }

  componentWillUnmount = () => {
    this.flag = false
  }

  componentWillReceiveProps(nextProps) {
    nextProps.visible && this._onCancel()
  }

  getResult = () => {
    return this.__value
  }

  handleLineClick(value) {
    this.__value = value
    this.props.layer.emitOk()
  }

  _onChange(searchValue) {
    const searchText = searchValue.trim()
    this.searchBranchList(searchText)
  }

  searchBranchList = debounce((searchText = '') => {
    const { allList, fieldName } = this.state
    let list = allList
    if (searchText) {
      list = allList.filter(item => item[fieldName].indexOf(searchText) >= 0)
    }
    return this.flag && this.setState({ list, isSearch: Boolean(searchText), searchText })
  }, 200)

  _onCancel() {
    this.searchBranchList()
  }
  handleCancel = () => {
    this.props.layer.emitCancel()
  }
  renderSearch() {
    const { modalTitle } = this.props
    return (
      <div className="search-wrapper">
        <SearchBar
          placeholder={`请输入${modalTitle}`}
          onCancel={this._onCancel.bind(this)}
          onChange={this._onChange.bind(this)}
        />
      </div>
    )
  }

  renderContentList() {
    let { list = [], fieldName } = this.state
    if (list.length === 0) return <EmptyWidget size={200} type="noCentent" backgroundColor="white" />
    const flg = this.state.isSearch
    const searchWords = [this.state.searchText]
    return (
      <List className="select-bank-list">
        {list.map(line => {
          const option = fieldName.includes('country')
            ? line[fieldName]
            : line.state
            ? `${fieldName === 'cnName' ? line.cnState : line.state}-${line[fieldName]}`
            : line[fieldName]
          const isChecked = this.props.value === line.id
          return (
            <List.Item>
              <div key={line[fieldName]} className="select-bank-repeat" onClick={this.handleLineClick.bind(this, line)}>
                {flg ? (
                  <div className="bank-name">
                    <Highlighter
                      highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                      searchWords={searchWords}
                      textToHighlight={option}
                    />
                  </div>
                ) : (
                  <div className="bank-name">{option}</div>
                )}
                {isChecked ? (
                  <div>
                    <OutlinedTipsDone fontSize={20} color="var(--eui-primary-pri-500)" style={{ marginLeft: 8 }} />
                  </div>
                ) : null}
              </div>
            </List.Item>
          )
        })}
      </List>
    )
  }

  render() {
    const { modalTitle } = this.props

    return (
      <div className={style['select-bank-wrapper']}>
        <div className="modal-header-wrapper">
          <OutlinedTipsClose fontSize={24} onClick={this.handleCancel} color="var(--eui-icon-n1)" />
          <span className="modal-header-title">{modalTitle}</span>
        </div>
        <div className="layout-content-wrapper">
          {this.renderSearch()}
          {this.renderContentList()}
        </div>
      </div>
    )
  }
}
