.add-payee-view-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  :global {
    .header-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--eui-bg-body, #fff);
      padding: 24px 32px;
      border-bottom: 16px solid #f2f3f5;
    }

    .list-wrapper {
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      background: #f2f3f5;
      -webkit-overflow-scrolling: touch;

      .bottom-line {
        background-color: #f2f3f5;
        height: 16px;
      }
      
      .eui-list-body {
        border-top: 0;
        border-bottom: 0;

        .eui-list-item {
          .eui-list-item-content-wrapper {
            border-top: 0px;
            border-bottom: var(--border-inner);
          }
          &:last-child {
            .eui-list-item-content-wrapper {
              border-bottom: 0px !important;
            }
          }
        }
      }

      .intelligent-filling-btn {
        margin: 16px 32px;
        width: calc(100% - 64px);
      }

      .intelligent-filling-content {
        margin: 16px 32px;

        .text-area-content {
          position: relative;

          .text-area {
            border-radius: 12px;
            height: 240px;

            .eui-text-area-element {
              height: 144px;
              overflow-y: scroll;
            }
          }

          .btns {
            position: absolute;
            bottom: 16px;
            right: 24px;
          }
        }

        .tips {
          margin-top: 8px;
          color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
          font-size: 28px;
          font-style: normal;
          font-weight: 400;
          line-height: 40px;

          .blue {
            margin-left: 24px;
            color: var(--eui-primary-pri-500, #2555ff);
          }
        }
      }
    }

    .text-error {
      height: 0.42rem;
      width: 0.42rem;
      margin-left: 0.12rem;
      background-image: url('./images/error-warning.svg');
      background-size: 0.42rem auto;
      position: absolute;
      right: 30px;
      top: 28px;
    }

    .text-top {
      top: 28px;
    }

    .am-list-arrow {
      margin-right: 40px;
    }

    .bank-other {
      position: relative;

      .am-input-clear {
        margin-right: 30px;
      }

      img {
        position: absolute;
        width: 40px;
        height: 40px;
        top: 25px;
        right: 10px;
      }
    }

    .action-wrapper {
      position: relative;
      padding: 20px 32px;
      display: flex;
      background: var(--eui-bg-body, #fff);
      box-shadow: var(--eui-shadow-up-1);
    }
  }
}
:global {
  .addPayee-create-popup {
    overflow: hidden;
    .am-modal-content {
      border-radius: 20px 20px 0 0 !important;
    }
  }
}
.dialog-wrap {
  display: flex;
  flex-direction: column;

  :global {
    span {
      color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
      font-size: 32px;
      font-style: normal;
      font-weight: 400;
      line-height: 48px;
    }
  }
}
