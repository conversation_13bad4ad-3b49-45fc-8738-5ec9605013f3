import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by nanyuanting<PERSON> on 02/10/2016 18:24.
 **************************************************/

import SVG_AGREE from './images/approve-status-resolve.svg'
import SVG_COUNTERSIGE_REJECT from './images/countersige-reject.svg'
import SVG_SAVE from './images/save.svg'
import SVG_REMOVE from './images/remove.svg'
import SVG_PRINT from './images/toolbar_print.svg'
import SVG_REMINDER from './images/reminder.svg'
import SVG_RETRACT from './images/retract.svg'
import SVG_COMMENT from './images/toolbar_comment.svg'
import SVG_BILLVERSION from './images/bill_version.svg'
import { OutlinedGeneralLoading } from '@hose/eui-icons'

export const EXPENSE_STATE = {
  draft: { color: '#ffae63', colorKey: 'warn', label: i18n.get('待提交') },
  pending: { color: '#48ade7', colorKey: 'info', label: i18n.get('提交中') },
  approving: { color: '#48ade7', colorKey: 'info', label: i18n.get('审批中') },
  sending: { color: '#48ade7', colorKey: 'info', label: i18n.get('待寄送') },
  receiving: { color: '#48ade7', colorKey: 'info', label: i18n.get('待收单') },
  receivingExcep: { color: '#48ade7', colorKey: 'info', label: i18n.get('收单异常') },
  rejected: { color: '#ff7c7c', colorKey: 'danger', label: i18n.get('已驳回') },
  paying: { color: '#48ade7', colorKey: 'info', label: i18n.get('待支付') },
  paid: { color: '#a1dc63', colorKey: 'success', label: i18n.get('已完成') },
  archived: { color: '#a1dc63', colorKey: 'success', label: i18n.get('已完成') },
  nullify: { color: '#ff7c7c', colorKey: 'danger', label: i18n.get('已作废') }
}

export const PLAN_STATE = {
  '0': { v: 0, label: i18n.get('审批中'), color: '#54595B', css: 'approving' },
  '1': { v: 1, label: i18n.get('同意'), color: '#a2abaf', css: 'resolve' },
  '2': { v: 2, label: '', color: '#a2abaf', css: '' },
  '3': { v: 3, label: '', color: '#a2abaf', css: 'completion' },
  '4': { v: 4, label: '', color: '#a2abaf', css: 'justApproving' },
  '5': { v: 5, label: '', color: '#a2abaf', css: 'justPay' },
  '6': { v: 6, label: '', color: '#a2abaf', css: 'justApproving' },
  undefined: { label: '', color: '#a2abaf', css: '' },
  '-1': { v: -1, label: i18n.get('驳回'), color: '#f17b7b', css: 'reject' },
  '-2': { v: -2, label: i18n.get('驳回'), color: '#f17b7b', css: 'justReject' },
  '-3': { v: -3, label: i18n.get('撤回'), color: '#ffae63', css: 'retract' },
  '-4': { v: -4, label: i18n.get('作废'), color: '#f17b7b', css: 'reject' }
}

// const SVG_SAV_RESOLVE = require('../../images/approve-resolve.svg')
// const SVG_SAV_REJECT = require('../../images/approve-reject.svg')

export const confirm = () => ({ name: 'confirm', label: i18n.get('确认'), weight: 120 })
export const submit = () => ({ name: 'submit', label: i18n.get('提交送审'), weight: 120, buttonType: 'main' })
export const submiting = () => ({ name: 'submit', loadingText: i18n.get('提交中'), icon: <OutlinedGeneralLoading spin /> , loading: true, weight: 120, buttonType: 'main' })
export const permitSave = () => ({ name: 'permitSave', label: i18n.get('保存'), weight: 120, buttonType: 'main' })
export const permitCancel = () => ({ name: 'permitCancel', label: i18n.get('返回'), weight: 120 })
export const expressSubmit = () => ({ name: 'submit', label: i18n.get('提交'), buttonType: 'main' })
export const addExpressInfo = () => ({
  name: 'addExpressInfo',
  label: i18n.get('添加寄送信息'),
  weight: 120,
  buttonType: 'main'
})
export const receiveExpress = () => ({
  name: 'receiveExpress',
  label: i18n.get('确认收单'),
  weight: 120,
  buttonType: 'main'
})
export const receiveExceptionExpress = () => ({
  name: 'receiveExceptionExpress',
  label: i18n.get('收单异常'),
  weight: 96,
  buttonType: 'main'
})
export const cancelReceiveExceptionExpress = () => ({
  name: 'cancelReceiveExceptionExpress',
  label: i18n.get('取消收单异常'),
  weight: 96
})
export const skipExpress = () => ({
  name: 'skipExpress',
  label: i18n.get('跳过寄送'),
  weight: 105
})
export const save = (disabled) => ({
  name: 'save',
  label: i18n.get('存为草稿'),
  img: SVG_SAVE,
  weight: 104,
  disabled:disabled
})
export const remove = () => ({ name: 'delete', label: i18n.get('删除'), img: SVG_REMOVE, weight: 103 })
export const modifyNullify = () => ({ name: 'modify_nullify', label: i18n.get('作废'), weight: 102.1 })
export const submitterNullify = () => ({ name: 'submitter_nullify', label: i18n.get('作废'), weight: 102.1 })
export const modifyFlow = () => ({ name: 'modify_flow', label: i18n.get('修改'), weight: 102 })
export const retract = () => ({ name: 'retract', label: i18n.get('撤回'), img: SVG_RETRACT, weight: 101 })
export const comment = () => ({
  name: 'comment',
  label: i18n.get('评论'),
  img: SVG_COMMENT,
  weight: 98
})
export const reminder = () => ({
  name: 'reminder',
  label: i18n.get('催办'),
  img: SVG_REMINDER,
  weight: 99
})
export const shift = () => ({ name: 'shift', label: i18n.get('转交审批'), weight: 100 })
export const signShift = () => ({ name: 'signShift', label: i18n.get('加签审批'), weight: 100.1 })
export const printRemind = () => ({ name: 'printRemind', label: i18n.get('打印提醒'), weight: 97 })
export const printReceive = () => ({ name: 'printReceive', label: i18n.get('收到打印'), weight: 96.5 })
export const exportAttachment = () => ({ name: 'exportAttachment', label: i18n.get('导出全部附件'), weight: 100.1 })
export const hangUp = () => ({ name: 'hangUp', label: i18n.get('暂挂审批'), weight: 96.5 })

export const billVersionBtn = () => ({
  name: 'billVersionBtn',
  label: i18n.get('辅助信息'),
  img: SVG_BILLVERSION,
  weight: 96
})

export const print = () => ({
  name: 'print',
  label: i18n.get('打印单据'),
  img: SVG_PRINT,
  weight: 96
})

export const printInvoice = () => ({
  name: 'printInvoice',
  label: i18n.get('打印单据和发票'),
  img: SVG_PRINT,
  weight: 96
})

export const changeBtn = () => ({
  name: 'changeBtn',
  label: i18n.get('变更'),
  img: SVG_PRINT,
  weight: 96
})

export const saveAndcopy = () => ({ name: 'saveAndcopy', label: i18n.get('复制'), weight: 95 })
export const copy = () => ({ name: 'copy', label: i18n.get('复制'), weight: 95 })
export const addNote = () => ({
  name: 'addNote',
  label: i18n.get('添加批注'),
  weight: 80
})
export const cancelAddNote = () => ({
  name: 'cancelAddNote',
  label: i18n.get('退出批注模式'),
  weight: 81,
  buttonType: 'main'
})
export const carbonCopyRead = () => ({
  name: 'carboncopy_read',
  label: i18n.get('标为已读'),
  weight: 94,
  buttonType: 'main'
})
export const carbonCopyRead_single = () => ({ name: 'carbonCopyRead_single', label: i18n.get('设为已读'), weight: 93 })
export const carbonCopyRead_all = () => ({
  name: 'carbonCopyRead_all',
  label: i18n.get('全设已读'),
  weight: 92,
  buttonType: 'main'
})
export const cancel = () => ({ name: 'cancel', label: i18n.get('取消'), weight: 90 })
export const selectFeetype = () => ({
  name: 'select.feetype',
  label: i18n.get('生成消费记录'),
  weight: 91,
  buttonType: 'main'
})
export const backPreStep = () => ({ name: 'back.prestep', label: i18n.get('上一步'), weight: 90, buttonType: 'main' })
export const blank = () => ({ name: 'blank', label: '', weight: 0 }) //用于位置占位
export const selectApprover = () => ({ name: 'freeflow.select.approver', label: i18n.get('修改审批人'), weight: 101 })
export const edit = () => ({ name: 'freeflow.edit', label: i18n.get('修改') })
export const submitTo = () => ({ name: 'freeflow.submit', label: i18n.get('送审给') })
export const handleAllowWithdraw = () => ({ name: 'isAllowWithdraw', label: i18n.get('撤回审批') })
export const pay = () => ({
  name: 'freeflow.pay',
  label: i18n.get('支  付'),
  img: app.require('@images/approve-resolve.svg'),
  page: 'PlanPay',
  bgColor: 'primary',
  weight: 105,
  buttonType: 'main'
})
export const agree = () => ({
  name: 'freeflow.agree',
  label: i18n.get('同  意'),
  img: app.require('@images/approve-resolve.svg'),
  page: 'PlanResolve',
  bgColor: 'primary',
  weight: 105,
  buttonType: 'main'
})
export const reject = () => ({
  name: 'freeflow.reject',
  label: i18n.get('驳  回'),
  img: app.require('@images/approve-reject.svg'),
  page: 'PlanReject',
  bgColor: 'orange',
  weight: 104,
  buttonType: 'minor',
  dangerous: true
})
export const paymentReviewReject = () => ({
  name: 'payment.review.reject',
  label: i18n.get('驳  回'),
  img: app.require('@images/approve-reject.svg'),
  weight: 104,
  buttonType: 'minor',
  dangerous: true
})
export const paymentReviewAgree = () => ({
  name: 'payment.review.agree',
  label: i18n.get('同意支付'),
  bgColor: 'primary',
  weight: 105,
  buttonType: 'main'
})
export const addTag = () => ({
  name: 'detail.addTag',
  label: i18n.get('添加审批意见'),
  buttonType: 'main',
  weight: 106
})

export const ACTIONS_STATE = {
  'freeflow.edit': edit,
  'freeflow.submit': submitTo,
  'freeflow.reject': reject,
  'freeflow.agree': agree,
  'freeflow.pay': pay,
  'freeflow.select.approver': selectApprover,
  'payment.review.agree': paymentReviewAgree,
  'payment.review.reject': paymentReviewReject,
  'detail.addTag': addTag
}

export const SKIPMAP = {
  APPROVER_NOT_FOUND_BY_ROLE: i18n.get('根据流程配置,该环节匹配不到合适的审核人'),
  APPROVER_SAME_AS_SUBMITTER_BY_ROLE: i18n.get('角色匹配出来的审批人与提交人相同'),
  APPROVER_NOT_FOUND: i18n.get('根据流程配置，该节点匹配不到审批人'),
  PAY_AMOUNT_IS_0: i18n.get('无需选择出纳，该单据无需支付'),
  NO_ABILITY: i18n.get('无需选择出纳，该单据无需支付'),
  AI_APPROVAL: i18n.get('已由 AI 代为同意'),
}
export const SkipMapHistory = {
  APPROVER_NOT_FOUND_BY_ROLE: i18n.get('匹配不到合适的审批人'),
  APPROVER_SAME_AS_SUBMITTER_BY_ROLE: i18n.get('审批人与提交人相同'),
  APPROVER_NOT_FOUND: i18n.get('匹配不到审批人'),
  PAY_AMOUNT_IS_0: i18n.get('该单据无需支付'),
  NO_ABILITY: i18n.get('该单据无需支付'),
  NO_SKIPPED: i18n.get('禁止提交'),
  REQUISITION_NO_ABILITY: i18n.get('自动跳过'),
  AI_APPROVAL: i18n.get('已由 AI 代为同意'),
}

export const COUNTERSIGN_STATE = {
  'freeflow.reject': SVG_COUNTERSIGE_REJECT,
  'freeflow.agree': SVG_AGREE,
  PROCESSED: SVG_AGREE
}

export const COUNTERSIGN_POLICY = {
  ALL: i18n.get('所有人审批同意后，审批通过'),
  ANY: i18n.get('任意审批人同意，审批通过')
}
export const COUNTERSIGN_AGREE = {
  APPROVER_SAME_AS_SUBMITTER: i18n.get('该节点的审批人与提交人重复，审批人自动同意'),
  APPROVER_REPEATED: i18n.get('该节点的审批人与前面节点的审批人重复，审批人自动同意'),
  FAILED_AUTO_AGREE: i18n.get('自动同意失效，需要您指定下一审批人'),
  CREDIT_INSPECTION_AUTO: i18n.get('提交人信用抽检未中，该节点自动同意'),
  APPROVER_SAME_AS_OWNER: i18n.get('该节点的审批人与创建人重复，审批人自动同意'),
}

export const channelMap = {
  OFFLINE: i18n.get('线下支付'),
  CREDITEASE: i18n.get('在线支付'),
  ERP: i18n.get('ERP付款'),
  WALLET: i18n.get('钱包支付'),
  SOUCHE: i18n.get('搜车支付'),
  FINGARD: i18n.get('保融支付'),
  CHANPAY: i18n.get('银企联支付'),
  CHANPAYV2: i18n.get('银企联支付'),
  HZBANK: i18n.get('杭州银行'),
  ANTALIPAY: i18n.get('企业付-支付宝'),
  ALIPAY: i18n.get('支付宝(办公)')
}

export const actionMap = {
  'freeflow.retract': retract,
  'freeflow.urge': reminder,
  'freeflow.copy': copy,
  'freeflow.agree': agree,
  'freeflow.reject': reject,
  'freeflow.printed': print,
  'freeflow.printInvoice': printInvoice,
  'freeflow.remind': printRemind,
  'freeflow.select.approver': selectApprover,
  'freeflow.delete': remove,
  'freeflow.edit': modifyFlow,
  'freeflow.submit': submit,
  'freeflow.pay': pay,
  'freeflow.isAllowWithdraw': handleAllowWithdraw,
  'payment.review.agree': paymentReviewAgree,
  'payment.review.reject': paymentReviewReject
}

export const actionWhiteList = [
  'freeflow.agree',
  'freeflow.reject',
  'freeflow.printed',
  'freeflow.remind',
  'freeflow.pay'
]

// 获取KA-外币账户打开后需要展示的字段
export const getFormItemsArrHSBC = () => [
  {
    label: '国家/地区（Country/Area）',
    name: 'extensions.country',
    max: 140,
    type: 'select',
    payee: '收款人地址'
  },
  {
    label: '城市（City）',
    name: 'extensions.city',
    max: 140,
    type: 'select',
    payee: '收款人地址'
  },
  {
    label: '镇/区（Town/District）',
    name: 'extensions.town',
    max: 140,
    type: 'input',
    payee: '收款人地址'
  },
  {
    label: '街道（Street）',
    name: 'extensions.street',
    max: 140,
    type: 'input',
    payee: '收款人地址'
  },
  {
    label: '邮编（Zip code）',
    name: 'extensions.zipCode',
    max: 140,
    type: 'input',
    notChineseOREnglish: true
  },
  {
    label: '付款银行所在国家',
    name: 'extensions.bankCountry',
    max: 140,
    type: 'select',
    notChineseOREnglish: true
  },
  {
    label: '邮箱',
    name: 'extensions.email',
    max: 140,
    type: 'input',
    notChineseOREnglish: true,
    checkEmail: true
  },
  {
    label: 'Account Name',
    name: 'extensions.accountName',
    max: 140,
    type: 'input',
    onlyEn: true
  },
  {
    label: 'Bank Name',
    name: 'extensions.bankName',
    max: 140,
    type: 'input',
    onlyEn: true
  },
  {
    label: 'Bank Address',
    name: 'extensions.bankAddress',
    max: 140,
    type: 'input',
    onlyEn: true
  },
  {
    label: 'Country/Area',
    name: 'extensions.countryEn',
    onlyEn: true,
    max: 140,
    type: 'select',
    payee: 'Payee address'
  },
  {
    label: 'City',
    name: 'extensions.cityEn',
    onlyEn: true,
    max: 140,
    type: 'select',
    payee: 'Payee address'
  },
  {
    label: 'Town/District',
    name: 'extensions.townEn',
    onlyEn: true,
    max: 140,
    type: 'input',
    payee: 'Payee address'
  },
  {
    label: 'Street',
    name: 'extensions.streetEn',
    onlyEn: true,
    max: 140,
    type: 'input',
    payee: 'Payee address'
  },
  {
    label: 'BIC代码（BIC code）',
    name: 'extensions.bicCode',
    max: 140,
    type: 'input'
  }
]
