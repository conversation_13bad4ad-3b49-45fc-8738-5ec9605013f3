import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import EnhanceTitleHook from './enhance-title-hook'
import { DepartmentSelector } from '@hose/pro-eui-mobile-components'
import '@hose/pro-eui-mobile-components/lib/index.css';

const SelectDepartment = (props) => {
  const {
    layer,
    dataSource,
    params
  } = props
  const corporationId = dataSource?.[0]?.corporationId

  const flatDept = (data, searchValue) => {
    const res = []
    const flat = (arr) => {
      arr.forEach((item) => {
        const { name, enName, code, selectable } = item
        if (name?.includes(searchValue) || enName?.includes(searchValue) || code?.includes(searchValue)) {
          selectable && res.push({ ...item, derived: item.fullPath.split('/') })
        }
        if (item.children?.length) {
          flat(item.children)
        }
      })
    }
    flat(data)
    return res
  }

  const getDepartment = async (searchValue) => {
    const { items } = await api.invokeService('@bill:get:department:visible:data', {
      ...params,
      searchText: searchValue
    })
    return flatDept(items, searchValue)
  }

  const handleSearch = async (searchValue) => {
    const data = await getDepartment(searchValue)
    return data
  }

  const handleSelect = (departments) => {
    if (departments.length > 0) {
      layer.emitOk(departments[0])
    }
  }

  return (
    <DepartmentSelector
      departments={dataSource}
      activeId={corporationId}
      onSearch={handleSearch}
      onSelect={handleSelect}
    />
  )
}

export default EnhanceTitleHook()(SelectDepartment)