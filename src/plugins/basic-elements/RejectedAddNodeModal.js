import { app, app as api } from '@ekuaibao/whispered'
import styles from './plan-shift.module.less'
import React from 'react'
import { Dialog, TextArea } from '@hose/eui-mobile'
import { List, Radio } from 'antd-mobile'
import { toast } from '../../lib/util'
import { FilledEditDeleteTrash } from '@hose/eui-icons'
import { EnhanceConnect } from '@ekuaibao/store'
import Avatar from './avatars'
import { cloneDeep, get } from 'lodash'
import ApproveCommentWords from './approve/ApproveCommentWords'
import { UploadWithConfig } from '../../elements/puppet/Upload'
import SVG_ARROW from './images/arrow_bold.svg'
import loadable from '@loadable/component'
const FilesUploader = loadable(() => import('@ekuaibao/uploader/esm/FilesUploader'))
const HuaWeiUploader = loadable(() => import('@ekuaibao/uploader/esm/HuaWeiUploader'))
const { getStaffShowByConfig } = app.require('@components/utils/fnDataLinkUtil')
const {
  buildData,
  fnClickAttachments,
  fnPreviewAttachments,
  getUploadUrl,
  getToken,
  parseAsSaveValue,
  uploadDone
} = app.require('@components/utils/fnAttachment')
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
import ApproveMoney from './approve/ApproveMoney'
const UploadItem = app.require('@elements/puppet/Upload/UploadItem')
import ApproveSignature from './approve/ApproveSignature'
import { defaultInvalidSuffixes, onInvalidFile } from '../../lib/invalidSuffixFile'
import Track from './track'
import ClipboardUploader from './approve/ClipboardUploader'
const RadioItem = Radio.RadioItem
const maxSize = 64
const defaultNode = [
  {
    approverId: []
  }
]
const defPolicyType = 'ALL'
const maxBatchAddNodes = 20

/**
 * @link https://hose2019.feishu.cn/wiki/FVtTwT9HSiVjRtksH1jc9UEdnth
*/

@EnhanceConnect(state => ({
  staffList: state['@common'].staffs,
  uploadServiceUrl: state['@common'].uploadServiceUrl
}))
@EnhanceFormCreate()
export default class RejectedAddNodeModal extends UploadWithConfig {
  constructor(props, ...args) {
    super(props, ...args)
    this.state = {
      type: 'PRE_ADD_NODE',
      value: '',
      fileList: [],
      token: '',
      attachmentSetting: {},
      approveDetail: {},
      batchAddNodes: defaultNode
    }
  }

  componentWillMount() {
    getToken().then(token => {
      this.setToken(token)
    })
    this.handleInitConfig()
    this.bus.watch('element:attachments:line:click', this.handleAttachment)
  }

  componentDidMount() {
    const { flowDatas } = this.props
    this.bus.on('add:comment:input', this.handleCommentInput)
    this.getAmountConfig()
    const nodes = get(flowDatas, '0.flowId.plan.nodes', [])
    const taskId = get(flowDatas, '0.flowId.plan.taskId', '')
    const planNode = nodes.find(v => v.id === taskId) || {}
    const type = planNode.forbidBeforeAddNode ? 'AFT_ADD_NODE' : 'PRE_ADD_NODE'
    this.setState({ type })
  }

  componentWillUnmount() {
    this.setToken = () => {}
    this.bus.un('element:attachments:line:click')
    this.bus.un('add:comment:input', this.handleCommentInput)
    this.bus.un('over', this.handleOver)
  }

  handleCommentInput = commentWord => {
    const { value } = this.state
    this.setState({ value: value + commentWord })
    Track.newTrack('Click_to_use_common_words', { actionName: i18n.get('点击使用常用语') })
  }
  getAmountConfig = () => {
    const { flowDatas = [] } = this.props
    api
      .invokeService('@bill:get:approve:detail', { flowIds: flowDatas.map(it => it?.flowId?.id ?? it.id) })
      .then(res => {
        this.setState({ approveDetail: res?.value })
      })
  }
  handleAttachment = (value, index) => {
    fnPreviewAttachments({ value, index })
  }

  setToken(token) {
    this.setState({ token })
  }

  onChange = value => {
    this.setState({ type: value })
  }

  handlePolicyChange = (value, index) => {
    const batchAddNodes = cloneDeep(this.state.batchAddNodes)
    batchAddNodes[index].policy = value
    this.setState({ batchAddNodes })
  }

  fnCheckStaff = batchAddNodes => {
    const emptyList = []
    batchAddNodes.forEach((item, index) => {
      if (item.approverId.length === 0) {
        emptyList.push(index + 1)
      }
    })
    return emptyList
  }

  handleOK = async () => {
    const { type, value, batchAddNodes } = this.state
    const emptyList = this.fnCheckStaff(batchAddNodes)
    if (emptyList.length) {
      return Dialog.alert({
        content: i18n.get('操作失败'),
        content: `第${emptyList.join(',')}加签节点请选择审批人`,
        confirmText: i18n.get('确定')
      })
    }
    let autographImageId = await this.bus.invoke('get:approve:signature:result')
    let { fileList } = this.state
    let attachments = []
    if (fileList && fileList.length) {
      attachments = fileList.map(parseAsSaveValue)
    }
    const param = {
      approverId: [],
      type,
      comment: value,
      attachments,
      autographImageId,
      batchAddNodes
    }
    this.props.layer.emitOk(param)
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleAddBatchNode = () => {
    const batchAddNodes = cloneDeep(this.state.batchAddNodes)
    if (batchAddNodes.length === maxBatchAddNodes) {
      toast.info(i18n.get(`加签最多支持${maxBatchAddNodes}个`))
      return
    }
    const newNode = [...batchAddNodes, ...defaultNode]
    this.setState({ batchAddNodes: newNode })
  }

  handleRemove = index => {
    const batchAddNodes = cloneDeep(this.state.batchAddNodes)
    if (batchAddNodes.length === 1) {
      toast.info(i18n.get(`加签最少保留一个`))
      return
    }
    batchAddNodes.splice(index, 1)
    this.setState({ batchAddNodes })
  }

  handleMultipleSelect = (item, index) => {
    const { staffList } = this.props
    const batchAddNodes = cloneDeep(this.state.batchAddNodes)
    api
      .invokeService('@layout:select:multiple:staff', {
        users: item?.approverId || [],
        blacklist: [],
        isNeedSearch: true,
        isVisibilityStaffs: false
      })
      .then(res => {
        let arr = []
        let staff
        res.forEach(staffId => {
          staff = staffList.find(el => el.id === staffId)
          if (staff) arr.push(staff)
        })
        batchAddNodes[index].approverId = res
        if (res.length > 1) {
          batchAddNodes[index].policy = defPolicyType
        }
        this.setState({
          batchAddNodes
        })
      })
  }

  handleCommentChange = value => {
    this.setState({ value })
  }

  handleChange = uploaderFileList => {
    this.setState({ uploaderFileList })
  }

  handleDone = uploaderFileList => {
    this.setState({ uploaderFileList: [] }, () => {
      let { fileList } = this.state
      uploadDone.call(this, {
        list: uploaderFileList,
        value: fileList,
        onChange: list => {
          this.setState({ fileList: list })
        }
      })
    })
  }

  handleUploadFinish = attachments => {
    const { fileList = [] } = this.state
    this.setState({ fileList: fileList.concat(attachments), uploaderFileList: [] })
  }

  handleDelete = (line, index) => {
    let { fileList } = this.state
    fileList = fileList.slice(0) // Copy 一份, 不让我列表不会刷新
    fileList.splice(index, 1)
    this.setState({ fileList })
  }

  handleLineClick = (line, index) => {
    let { fileList } = this.state
    fnClickAttachments({ bus: this.bus, value: fileList, line, index })
  }

  /**
   *
   * @param {File[]} invalidFiles // 错误文件列表
   * @param {'invilidaFileType' | 'otherInvalid'} type // 文件错误类型
   */
  handleInvalidFiles = (invalidFiles, type) => {
    let { invalidSuffixes = defaultInvalidSuffixes } = this.props
    onInvalidFile(invalidFiles, invalidSuffixes, type)
  }

  renderItem = (line, index) => {
    return (
      <UploadItem
        key={index}
        isEdit={true}
        file={line}
        onRemoveItem={() => this.handleDelete(line, index)}
        onClickItem={() => this.handleLineClick(line, index)}
      />
    )
  }

  renderSelectMultipleStaff = (itemBatch, index) => {
    const { staffList } = this.props
    return (
      <div className="selected-box" onClick={() => this.handleMultipleSelect(itemBatch, index)}>
        {!!itemBatch.approverId.length ? (
          <div className="info-wrapper">
            {itemBatch.approverId.slice(0, 2).map((item, index) => {
              return (
                <div key={index} className="items">
                  <Avatar imgList={item.avatar} style={{ marginRight: 8 }} />
                  <span style={{ color: '#262626' }} className="name-box">
                    {getStaffShowByConfig(staffList.find(staffId => staffId.id === item))}
                  </span>
                </div>
              )
            })}
            {itemBatch.approverId.length > 3 && <div>{`等${itemBatch.approverId.length}人`}</div>}
          </div>
        ) : (
          <div className="info-wrapper">
            <span className="name-box">{i18n.get('请选择审批人')}</span>
          </div>
        )}
        <img className="arrow-right" src={SVG_ARROW} />
      </div>
    )
  }

  render() {
    const { type, fileList, token, attachmentSetting, approveDetail = {}, batchAddNodes } = this.state
    const {
      isPaying = false,
      uploadServiceUrl,
      form,
      flowDatas = [],
      invalidSuffixes = defaultInvalidSuffixes
    } = this.props
    const uploadUrl = uploadServiceUrl && uploadServiceUrl.uploadUrl
    const nodes = get(flowDatas, '0.flowId.plan.nodes', [])
    const taskId = get(flowDatas, '0.flowId.plan.taskId', '')
    const planNode = nodes.find(v => v.id === taskId) || {}
    const data = []
    if (!planNode.forbidBeforeAddNode) {
      data.push({ value: 'PRE_ADD_NODE', label: i18n.get('TA在我之前审批本单据（前加签）') })
    }
    if (!isPaying && !planNode.forbidAftAddNode) {
      data.push({ value: 'AFT_ADD_NODE', label: i18n.get('我同意单据，还要TA审批（后加签）') })
    }
    const validAccept = this.getAccept()
    return (
      <div className={styles['planShiftModal-wrapper']}>
        <div className="planShiftModal-header">{i18n.get('加签审批')}</div>
        <div className="planShiftModal-content">
          <div className="planShiftModal-tip">
            {i18n.get('当此单据你无法作出审批决策，或者需要他人辅助审批决策时，可通过「{__k0}」来进行处理', {
              __k0: i18n.get('加签审批')
            })}
          </div>
          <div className="line" />
          <ApproveMoney approveData={flowDatas} approveDetail={approveDetail} />
          {/* {moneyView({ datas: flowDatas, approveDetail })} */}
          <div className="line" />
          <div className="plan-shift-wrapper">
            {batchAddNodes.map((itemBatch, index) => {
              return (
                <div className="planShiftModal-item" key={index}>
                  <div className="plan-shift-title">
                    <span>加签{index + 1}:</span>
                    <FilledEditDeleteTrash onClick={() => this.handleRemove(index)} />
                  </div>
                  <span>
                    <span style={{ color: '#f5222d', marginRight: 4 }}>*</span>
                    {i18n.get('请选择加签人：')}
                  </span>
                  {this.renderSelectMultipleStaff(itemBatch, index)}
                  <div className="planShiftModal-text">{i18n.get('请谨慎选择，确定后即不可修改')}</div>
                  {itemBatch.approverId.length > 1 && (
                    <div className="policy">
                      <List>
                        {dataList.map(line => {
                          return (
                            <RadioItem
                              key={line.value}
                              checked={itemBatch.policy === line.value}
                              onChange={this.handlePolicyChange.bind(this, line.value, index)}
                            >
                              {line.label}
                            </RadioItem>
                          )
                        })}
                      </List>
                    </div>
                  )}
                </div>
              )
            })}
            <div className="planShiftModal-item-add-btn" onClick={this.handleAddBatchNode}>
              + 添加加签节点
            </div>
          </div>
          <div className="line" />
          <div className="planShiftModal-radio">
            <List
              renderHeader={() => (
                <div style={{ fontSize: 14 }}>
                  <span style={{ color: '#f5222d', marginRight: 4 }}>*</span>
                  {i18n.get('请选择加签理由：')}
                </div>
              )}
            >
              {data.map(i => (
                <RadioItem key={i.value} checked={type === i.value} onChange={() => this.onChange(i.value)}>
                  {i.label}
                </RadioItem>
              ))}
            </List>
          </div>
          <div className="content">
            <TextArea
              className="comment-text-area"
              value={this.state.value}
              onChange={this.handleCommentChange}
              placeholder={i18n.get('输入意见(1400个字)')}
              rows={3}
              maxLength={1400}
              showCount
            />
            <ApproveCommentWords bus={this.bus} commentArea={this.state.value} commentType="COUNTERSIGN" />
            <div className="line" />
            <ApproveSignature bus={this.bus} form={form} data={flowDatas} />
            <div className="line" />
            <div className="content-line">
              <div>
                {i18n.get('附件2')}
                <span>{i18n.get('（选填）')}</span>
              </div>
              {window.__PLANTFORM__ === 'HUAWEI' && window.isAndroid ? (
                <HuaWeiUploader
                  action={IS_STANDALONE ? getMinioUploadUrl() : uploadUrl}
                  onChange={this.handleChange}
                  onDone={this.handleDone}
                  onStart={this.handleOnStart}
                  data={file => buildData(file, token, uploadServiceUrl)}
                >
                  <div className="content-attachment">{i18n.get('上传附件', {})}</div>
                </HuaWeiUploader>
              ) : (
                // 转交审批，加签审批上传附件
                <FilesUploader
                  accept={validAccept}
                  action={IS_STANDALONE ? getUploadUrl : uploadUrl}
                  type={IS_STANDALONE}
                  maxSize={maxSize}
                  onChange={this.handleChange}
                  onDone={this.handleDone}
                  onStart={this.handleOnStart}
                  onInvalidFile={this.handleInvalidFiles}
                  invalidSuffixes={invalidSuffixes}
                  data={file => buildData(file, token, uploadServiceUrl)}
                  invalidSuffixesConfig={attachmentSetting?.invalidSuffixesConfig}
                >
                  <div className="content-attachment">{i18n.get('上传附件', {})}</div>
                  <ClipboardUploader
                    useClipboard
                    onStartUpload={this.handleOnStart}
                    onUploadChange={this.handleChange}
                    onUploadFinish={this.handleUploadFinish}
                  />
                </FilesUploader>
              )}
            </div>
            <div className="attachment-wrapper">
              {fileList.length > 0 && fileList.map((line, index) => this.renderItem(line, index))}
            </div>
          </div>
        </div>
        <div className="footer-wrapper">
          <div className="btn" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </div>
          <div className="btn" onClick={this.handleOK}>
            {i18n.get('确定')}
          </div>
        </div>
      </div>
    )
  }
}

const dataList = [
  { value: 'ALL', label: i18n.get('需所有审批人都同意才算通过') },
  { value: 'ANY', label: i18n.get('任意审批人同意即认为通过') }
]
