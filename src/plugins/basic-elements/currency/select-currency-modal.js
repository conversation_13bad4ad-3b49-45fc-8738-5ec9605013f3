/**
 * Created by <PERSON><PERSON> on 2017/11/29.
 */
import React, { PureComponent } from 'react'
import { List } from 'antd-mobile'
const Item = List.Item
import { EnhanceLayer } from '@ekuaibao/enhance-layer-manager-mobile'
import EnhanceTitleHook from '../enhance-title-hook'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'

@EnhanceTitleHook()
export default class SelectCurrencyModal extends PureComponent {
  constructor(props) {
    super(props)
    let { allCurrencyRates } = props
    let sections = this.initData(allCurrencyRates)
    this.state = {
      sections
    }
  }

  initData(allCurrencyRates) {
    let { standardCurrency, showRate = true } = this.props
    return [
      standardCurrency?.strCode && {
        header: { title: i18n.get('本位币'), subTitle: showRate ? i18n.get('汇率') : null },
        items: [{ ...standardCurrency }]
      },
      {
        header: { title: i18n.get('消费币种'), subTitle: showRate ? i18n.get('汇率') : null },
        items: allCurrencyRates
      }
    ]
  }

  handleItemOnClick = item => {
    this.props.layer.emitOk(item)
  }

  renderItem(item, index) {
    const { showRate = true } = this.props
    let { name, rate, icon } = item
    return (
      <Item
        key={index}
        extra={showRate ? rate : undefined}
        thumb={icon}
        multipleLine
        onClick={this.handleItemOnClick.bind(this, item)}
      >
        {name}
      </Item>
    )
  }

  render() {
    let { allCurrencyRates } = this.props
    let { sections } = this.state
    return (
      <div className="h-100p ovr-y-a" style={{ background: '#F8F9F9' }}>
        {sections.map((section, index) => {
          if (!section) return null
          let { header, items } = section
          if (!items.length) return null
          return (
            <List key={index} renderHeader={Header(header)} renderFooter={allCurrencyRates.length === 0 && Footer()}>
              {items.map((item, index) => {
                return this.renderItem(item, index)
              })}
            </List>
          )
        })}
      </div>
    )
  }
}

const Header = ({ title, subTitle }) => (
  <div className="dis-f jc-sb">
    <span>{title}</span>
    <span>{subTitle}</span>
  </div>
)

const Footer = () => i18n.get('您的企业还未进行币种设置，请联系管理员前往企业管理 > 多币种进行设置')
