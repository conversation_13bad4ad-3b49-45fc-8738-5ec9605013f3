.select-enum-wrapper{
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--eui-bg-base);
    .select-enum-header{
        display: flex;
        align-items: center;
        padding: 16px 32px;
        justify-content: space-between;
        background-color: var(--eui-bg-body);
        .select-enum-header-title-wrapper{
            display: flex;
            justify-content: center;
            flex: 1;
            height: 52px;
            .select-enum-header-title{
                color: var(--eui-text-title);
                font: var(--eui-font-head-b2);
            }
        }
    }
    .select-enum-search{
        padding: 16px 32px;
        background-color: var(--eui-bg-body);
    }
    .select-enum-list{
        overflow-y: auto;
        // max-height: calc(80vh - 192px);
        // overflow-y: auto;
        text-align: left;
    }
    .select-enum-empty{
        height: 100%;
        margin-bottom: 200px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .select-enum-empty-text{
            margin-top: 16px;
            color: var(--eui-text-placeholder);
            font: var(--eui-font-body-r1);
        }
    }
    .select-enum-loading{
        height: 100%;
        padding: 0 32px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .skeleton-paragraph-item{
            margin: 32px 0;
        }
    }
}

.select-enum-popup{
    .am-modal-body div{
        font: var(--eui-font-head-r1);
    }
    .am-modal-popup-slide-up{
        max-height: 80%;
        // height: auto;
        .am-modal-header{
            height: 88px;
            padding: 18px 32px;
            .am-modal-title{
                font: var(--eui-font-head-b2);
                color: var(--eui-text-title);
            }
        }
        .am-modal-close{
            right: unset;
            left: 32px;
            top: 28px;
            color: var(--eui-icon-n1);
        }
    }
}