import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by nany<PERSON>ingfeng on 30/09/2016 15:24.
 **************************************************/
import styles from './flow-plan-editable.module.less'
import cloneDeep from 'lodash/cloneDeep'
import _get from 'lodash/get'

import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
const BasicComponent = app.require('@elements/enhance/basic-component')
import DataMap from './flow-plan-editable.datamap'
const SVG_AVATAR_NULL = app.require('@images/avatar-null.svg')
const SVG_MENU_ARROW_RIGHT = app.require('@images/menu-arrow-right.svg')
const SVG_MENU_APPRO_ARROW_RIGHT = app.require('@images/menu-appro-arrow-right.svg')

const SVG_DOWN = app.require('@images/arrow-down.svg')
const SVG_UP = app.require('@images/arrow-up.svg')
const SVG_COMMENT = app.require('@images/comment.svg')
const SVG_ATTAC = app.require('@elements/puppet/Details/images/attachment.svg')

const IconTag = app.require('@elements/puppet/IconTag')
import MessageCenter from '@ekuaibao/messagecenter'
const { fnPreviewAttachments } = app.require('@components/utils/fnAttachment')

import { toast, getNodeValueByPath } from '../../lib/util'
import Avatar from './avatars'
import FlowConunterSignStaff from './countersign/flow-conuntersign-staff'
import { SKIPMAP } from '../basic-elements/basic-state-const'
const EkbIcon = app.require('@elements/ekbIcon')
import { getV } from '@ekuaibao/lib/lib/help'
import { isString } from '@ekuaibao/helpers'
import { fnSubstr16 } from '../../elements/approve-log/history-log-item'
import { Fetch } from '@ekuaibao/fetch'
import { getUserDisplayName } from "../../components/utils/fnDataLinkUtil";
const { renderComment } = app.require('@elements/history-log-item')
import { getAIAgentObj, getAIAgentLabelDom } from '../../elements/ai-agent-utils'
import classNames from 'classnames'

export function formatNodes(data, id, node) {
  const plan = data.plan || getV(data, 'flow.plan') || getV(data, 'flowId.plan')
  plan.nodes = plan.nodes.map(line => {
    if (line.id === id) {
      line.staff = line.staff || {}
      line.staff.avatar = node.avatar
      line.staff.name = node.name
      line.approverId = node
    }
    return line
  })
}

@EnhanceConnect(state => ({
  staffDisplayConfigField: state['@common'].organizationConfig?.staffDisplayConfig?.[1] || '',
  authStaffStaffMap: state['@common'].authStaffStaffMap || {},
  nodesAIAgentMap: state['@common'].nodesAIAgentMap || {},
}))
export default class FlowPlanEditable extends BasicComponent {
  constructor(props) {
    super(props)
    this.bus = new MessageCenter()
    this.state = { type: 0, nodekey: '', data: null }
    this.datamap = new DataMap(props, props.flow)
    this.initializeFnBindContext(this, 'handleNodeClick')
  }

  componentWillMount() {
    this.setState({ data: this.props })
  }

  componentWillReceiveProps(nextProps) {
    const fn = this.fnCompareProps(this.props, nextProps)
    fn('flow', () => {
      this.setState({ data: nextProps })
      this.datamap = new DataMap(nextProps, nextProps.flow)
      this.forceUpdate()
    })
  }

  handleTipsClick(value) {
    let data = { value: value.data, index: 0 }
    fnPreviewAttachments(data)
  }

  /** 过滤流程点击事件
   * line.stateEx:
   * ** 0: 节点审批中；
   * ** 2: 节点未处理；
   */
  handleNodeClick(line) {
    let { isReadOnly } = this.props
    let isSubmitterChoice = line.config && line.config.isSubmitterChoice
    if (isReadOnly || isSubmitterChoice || ['ebot', 'recalculate', 'invoicingApplication', 'aiApproval'].includes(line.type)) {
      return
    }
    // 节点状态不是审批中和未处理时，拦截点击事件；
    if (line.stateEx !== 2 || line.skippedType !== 'NO_SKIPPED') {
      return this
    }
    api
      .invokeService('@layout:select:staff', {
        defValue: line.approverId,
        whiteList: line.isAllStaffs ? undefined : line.staffIds,
        isVisibilityStaffs: false
      })
      .then(data => {
        const id = this.datamap.flow.id

        const idata = {
          id: line.id,
          approverId: data.id,
          name: 'freeflow.select.approver'
        }

        api.invokeService('@common:update:plan:node', id, idata).then(() => {
          this.datamap.updateNode4UI(line.id, data)
          this.props.onChange && this.props.onChange(line.id, data)
          this.handleFlowPlanEditableValueChange(line.id, data)
        })
      })
  }

  handleFlowPlanEditableValueChange = (id, node) => {
    const data = cloneDeep(this.state.data)
    formatNodes(data, id, node)
    this.datamap = new DataMap(data, data.flow)
    this.setState({ data })
  }

  getAvatarList(line) {
    let avatars = []
    if (line.type === 'countersign') {
      line.counterSigners.forEach(line => {
        let avatar = line.signerId && line.signerId.avatar
        if (avatar) {
          avatars.push(avatar)
        } else {
          avatars.push(SVG_AVATAR_NULL)
        }
      })
      avatars.length === 0 && avatars.push(SVG_AVATAR_NULL)
    } else if (line.type === 'carbonCopy') {
      const staffIds = _get(line, 'carbonCopy[0].staffIds', [])
      staffIds.map(id => avatars.push(this.props.authStaffStaffMap[id]?.avatar || SVG_AVATAR_NULL))
      avatars.length === 0 && avatars.push(SVG_AVATAR_NULL)
    } else {
      let avatar =
        line.type === 'ebot' || line.type === 'invoicingApplication'
          ? 'EBotIconNode'
          : line.approverId && line.approverId.avatar
            ? line.approverId.avatar
            : SVG_AVATAR_NULL
      if (line.type === 'aiApproval') {
        const { agent } = getAIAgentObj(line, this.props.nodesAIAgentMap)
        avatar = agent?.icon
      }
      if (line?.skippedType === 'PAY_AMOUNT_IS_0' || line?.skippedType === 'NO_ABILITY') {
        avatar = SVG_AVATAR_NULL
      }
      avatars.push(avatar)
    }
    return avatars
  }

  handleClick(line) {
    if (line.skippedType !== 'NO_SKIPPED') {
      return this
    }
    let currentKey = line.id + ':' + line.name
    if (this.state.nodekey === currentKey || this.state.type === 0) {
      const type = (this.state.type + 1) % 2
      this.setState({ type, nodekey: currentKey })
    } else {
      this.setState({ nodekey: currentKey })
    }
  }

  handleAttachments = curLog => {
    let length = curLog.attachments && curLog.attachments.length
    if (length) {
      return {
        img: SVG_ATTAC,
        name: i18n.get('{__k0}个附件', { __k0: length }),
        type: 'attachment',
        data: curLog.attachments
      }
    }
    return undefined
  }

  handlePlanClick = (line, isCountersign, isCarbonCopy, showCarbonCopy) => {
    if (isCountersign) {
      return this.handleClick(line)
    }
    if (isCarbonCopy) {
      return showCarbonCopy && this.handleClick(line)
    }
    return this.handleNodeClick(line)
  }

  renderWrapper() {
    const flowLogs = this.datamap.getFlowLogs()
    const userInfo = api.getState()['@common'].me_info
    const { flow, staffDisplayConfigField, authStaffStaffMap } = this.props

    return (
      <ul className={styles['flow-plan-list']}>
        {this.datamap.getPlan().nodes.map(line => {
          const curLogs = flowLogs.filter(o => o.attributes && o.attributes.nodeId && o.attributes.nodeId === line.id)
          const curLog = curLogs.length > 0 ? curLogs[curLogs.length - 1] : {}
          const action = curLog && curLog.action
          const attributes = curLog && curLog.attributes
          //前加签、转交不显示签名 后加签才显示
          const isShowTag =
            (action === 'freeflow.addnode' && attributes.addNodeType === 'AFT_ADD_NODE') ||
            action !== 'freeflow.addnode'

          const attachments = this.handleAttachments(curLog)
          const stateEx = this.datamap.getNodeLabel(line.stateEx)
          const isPass = line.skippedType !== 'NO_SKIPPED'
          const avatars = this.getAvatarList(line)
          const isCountersign = line.type === 'countersign'
          const isCarbonCopy = line.type === 'carbonCopy'
          const counterState = isCountersign && this.datamap.getCounterSignerState(line)
          let currentKey = line.id + ':' + line.name
          const params = {
            isCountersign,
            isCarbonCopy,
            avatars,
            currentKey,
            line,
            isPass,
            curLog
          }
          let conditionText = line.conditionalDescription
          let showConditionInBill = getNodeValueByPath(line, 'config.showConditionInBill')
          showConditionInBill = showConditionInBill === undefined ? true : showConditionInBill
          if (line.approverId && conditionText) {
            conditionText =
              userInfo.staff.id === line.approverId.id
                ? conditionText.replace(i18n.get('需要此环节审批'), i18n.get('需要您审批'))
                : conditionText
          }
          const { type } = line
          if ((type === 'ebot' || type === 'invoicingApplication' || type === 'aiApproval') && line.ebotConfig && line.ebotConfig.hiddenNode) {
            const hiddenModule = _get(line, 'ebotConfig.hiddenModule', ['feeflow']) || ['feeflow']
            if (hiddenModule?.includes('feeflow')) {
              return
            }
          } else if (isPass && line.config && line.config.hiddenNode) {
            return
          }
          const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage
          //@i18n-ignore
          const name = line.name === '出纳支付' ? (line.label ? line.label : line.name) : line.name
          let langName = (lang === 'en-US' && line?.enName) ? line?.enName : name
          let customStaff
          let showCarbonCopy = false
          if (isCarbonCopy) {
            const staffIds = _get(line, 'carbonCopy[0].staffIds', [])
            customStaff = staffIds.map(id => authStaffStaffMap[id])
            showCarbonCopy = staffIds.length > 0
          }
          let statusText = isCountersign ? counterState : stateEx.label
          if(line.type === 'aiApproval'){
            langName = getAIAgentLabelDom(langName)
            const result = curLog.attributes?.extras?.result
            statusText = result || statusText
          }
          const tips = curLog.attributes?.extras?.tips
          return (
            <li className={`item ${stateEx.css}`} key={line.id + ':' + line.name}>
              <div className="status-bg">
                <span className="gray-line" />
                <span className="circle">
                  <EkbIcon name="#EDico-check-circle" className="icon-circle" />
                </span>
              </div>

              <div className="content">
                <div className="header">
                  <div>
                    <span>{i18n.get(langName)}</span>
                    {line.conditionalDescription && showConditionInBill && (
                      <span className="condition" onClick={() => toast.info(conditionText, 1500)}>
                        {i18n.get('条件', {})}
                      </span>
                    )}
                  </div>
                  <div>
                    <div className="header-state">
                      {!isPass && (
                        <span className="status-text" style={{ color: stateEx.color }}>
                          {statusText}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div
                  className="b-main"
                  onClick={() => this.handlePlanClick(line, isCountersign, isCarbonCopy, showCarbonCopy)}
                >
                  {this.renderHeader(params)}
                  {this.renderRightArrow(params, showCarbonCopy)}
                  {!isCountersign && this.renderAutoGraphImage(curLog)}
                </div>

                {this.state.type === 1 && (isCountersign || isCarbonCopy) && this.state.nodekey === currentKey && (
                  <FlowConunterSignStaff
                    node={line}
                    flowLogs={flowLogs}
                    staffDisplayConfigField={staffDisplayConfigField}
                    customStaff={customStaff}
                  />
                )}

                {curLog.attributes &&
                  curLog.attributes.comment &&
                  (stateEx.v === 1 || stateEx.v === -1) &&
                  !isCountersign && <div className={classNames('reason', { [styles['has-content-msg-tips']]: !!tips })}>
                    {!!tips && <div className={styles['content-msg-tips']}>{tips}</div>}
                    {renderComment(curLog, flow)}
                  </div>}

                {!isCountersign && isShowTag && attachments && (
                  <IconTag
                    key={attachments.key}
                    className="tag"
                    src={attachments.img}
                    text={attachments.name}
                    onClick={this.handleTipsClick.bind(this, attachments)}
                  />
                )}
              </div>
            </li>
          )
        })}
      </ul>
    )
  }

  renderAutoGraphImage(curLog) {
    let action = curLog && curLog.action
    let attributes = curLog && curLog.attributes
    const isShowTag =
      (action === 'freeflow.addnode' && attributes.addNodeType === 'AFT_ADD_NODE') || action !== 'freeflow.addnode'
    return isShowTag && attributes && attributes.autographImageId ? (
      <div style={{ width: 50, height: 30 }}>
        <img style={{ width: 50, height: 30, borderRadius: 5 }} src={attributes.autographImageId.url} />
      </div>
    ) : null
  }

  renderRightArrow(params, showCarbonCopy) {
    let { isCountersign, isCarbonCopy, isPass, line } = params
    let { isReadOnly } = this.props
    if (isCountersign) {
      return this.renderCountersignArrow(params)
    }
    if (isCarbonCopy) {
      return showCarbonCopy && this.renderCountersignArrow(params)
    }
    let isSubmitterChoice = line.config && line.config.isSubmitterChoice
    let flag = !isPass && line.stateEx !== 0 && line.stateEx !== 1 && line.stateEx !== 6
    return !isReadOnly &&
      flag &&
      !isSubmitterChoice &&
      !['ebot', 'recalculate', 'invoicingApplication'].includes(line.type) ? (
      <img className="arrow-right" src={SVG_MENU_ARROW_RIGHT} alt="" />
    ) : null
  }

  renderCountersignArrow(params) {
    let { currentKey, isPass } = params
    return !isPass ? (
      <img
        className="arrow-right"
        src={this.state.type === 0 ? SVG_DOWN : this.state.nodekey === currentKey ? SVG_UP : SVG_DOWN}
        alt=""
      />
    ) : null
  }

  renderHeader(params) {
    if (params.isCountersign) {
      return this.renderCountersHeader(params)
    }
    if (params.isCarbonCopy) {
      return this.renderCarbonCopyHeader(params)
    }
    return this.renderNomalHeader(params)
  }

  renderCarbonCopyHeader(params) {
    const { line, avatars, currentKey } = params
    const { authStaffStaffMap } = this.props
    const { nodekey, type } = this.state
    const staffIds = _get(line, 'carbonCopy[0].staffIds', [])
    const staffs = staffIds.map(id => authStaffStaffMap[id])
    const str = i18n.get('抄送{__k0}人', { __k0: staffs.length })
    const flg = type === 1 && nodekey === currentKey

    return (
      <div className="info">
        {!flg && <Avatar imgList={avatars} />}
        {staffs.length === 0 ? (
          <div className="head-right">
            <span className="status-text ml-10">{i18n.get('匹配不到抄送人（自动跳过）')}</span>
            <img
              className="img-question"
              src={SVG_MENU_APPRO_ARROW_RIGHT}
              onClick={this.handleShowToast.bind(this, i18n.get('未匹配到抄送人'))}
            />
          </div>
        ) : (
          <div className="head-right">
            <span className={flg ? 'user-name' : 'user-name ml-10'}> {str}</span>
          </div>
        )}
      </div>
    )
  }

  handleShowToast = (info, e) => {
    e = e || window.event
    e.stopPropagation()
    if (e.cancelable) {
      e.preventDefault && e.preventDefault()
    }
    toast.info(info, 1500)
  }

  renderCountersHeader(params) {
    let { currentKey, line, avatars, curLog, isPass } = params
    let { nodekey, type } = this.state
    let flg = type === 1 && nodekey === currentKey
    let showComment = (type === 0 && nodekey === currentKey) || nodekey === ''
    let policyStr = this.datamap.getCountersignNodeType(line.policy)
    let counterSigners = line.counterSigners || []
    let str = flg
      ? i18n.get('{__k0}人会签，{__k1}', { __k0: counterSigners.length, __k1: policyStr })
      : i18n.get('{__k0}人会签', { __k0: counterSigners.length })
    let agreeTypeFlag = line.agreeType && line.agreeType !== 'NO_AUTO_AGREE'
    const { info } = agreeTypeFlag ? this.getAgreeInfo(line) : {}
    return (
      <div className="info">
        {(isPass || !flg) && <Avatar imgList={avatars} />}
        {isPass ? (
          <div className="head-right">
            <span className="status-text">{i18n.get('匹配不到审批人（自动跳过）')}</span>
            <img
              className="img-question"
              src={SVG_MENU_APPRO_ARROW_RIGHT}
              onClick={this.handleShowToast.bind(this, i18n.get('根据流程配置，该环节匹配不到合适的审核人'))}
            />
          </div>
        ) : (
          <div className="head-right">
            <span className={flg ? 'user-name' : 'user-name ml-10'}> {str}</span>
            {(line.agreeType === 'REJECT_FORCE_APPROVALS' ||
              line.agreeType === 'ROLLBACK_FORCE_APPROVALS' ||
              line.agreeType === 'EDIT_FORCE_APPROVALS') && (
                <img
                  className="img-question"
                  src={SVG_MENU_APPRO_ARROW_RIGHT}
                  onClick={this.handleShowToast.bind(this, info)}
                />
              )}
            {curLog.attributes && curLog.attributes.comment && showComment && (
              <img className="comment-img" src={SVG_COMMENT} />
            )}
          </div>
        )}
      </div>
    )
  }

  renderImg = (avatars, type) => {
    if (type === 'ebot' || type === 'invoicingApplication') {
      return <EkbIcon name="#EDico-EBot-AI" style={{ width: 32, height: 32 }} />
    }

    if (type === 'recalculate') {
      return <EkbIcon name="#EDico-sys-recount" style={{ width: 32, height: 32 }} />
    }

    return <Avatar imgList={avatars} />
  }

  getName = line => {
    const { type } = line
    if (type === 'ebot') {
      return 'EBot'
    }
    if (type === 'invoicingApplication') {
      return i18n.get('开票申请')
    }
    if (type === 'recalculate') {
      return i18n.get('重算节点')
    }
    if (type === 'aiApproval') {
      const { agent } = getAIAgentObj(line, this.props.nodesAIAgentMap)
      return agent?.name
    }
    return line.approverId ? getUserDisplayName(line.approverId) : i18n.get('未选择')
  }

  getStaffDisplay = line => {
    const { staffDisplayConfigField } = this.props
    return line?.approverId?.[staffDisplayConfigField]
  }
  getAgreeInfo = (line, staffDisplay) => {
    const isAdminSkip = line.agreeType === 'ADMIN_SKIP_NODE_AUTO'
    const info = isAdminSkip ? line.skipMessage : this.datamap.getNormalAgree(line)
    let agreeMsg = '自动同意'
    if (
      line.agreeType === 'REJECT_FORCE_APPROVALS' ||
      line.agreeType === 'ROLLBACK_FORCE_APPROVALS' ||
      line.agreeType === 'EDIT_FORCE_APPROVALS'
    ) {
      agreeMsg = ''
    }
    const msg = isAdminSkip ? `（跳过）` : agreeMsg ? `（${agreeMsg}）` : ''
    const agreeTypeText = i18n.get(`{__k0} ${msg}`, {
      __k0:
        staffDisplay && isString(staffDisplay)
          ? line?.approverId?.name + `(${fnSubstr16(staffDisplay)})`
          : line?.approverId?.name
    })
    return { agreeTypeText, info }
  }
  renderNomalHeader(params) {
    let { avatars, line, isPass } = params
    const name = this.getName(line)
    // 通讯录员工显示配置
    const staffDisplay = this.getStaffDisplay(line)
    let agreeTypeFlag = line.agreeType && line.agreeType !== 'NO_AUTO_AGREE'
    const { agreeTypeText, info } = agreeTypeFlag ? this.getAgreeInfo(line, staffDisplay) : {}
    let isNoAbility = line.skippedType === 'NO_ABILITY' || line.skippedType === 'PAY_AMOUNT_IS_0'
    let statusText = isNoAbility ? i18n.get('该单据无需支付') : i18n.get('匹配不到审批人（自动跳过）')
    if(line.skippedType === 'AI_APPROVAL'){
      statusText = i18n.get('自动跳过')
    }
    return (
      <div className="info">
        {this.renderImg(avatars, line.type)}
        {isPass ? (
          <div className="head-right ml-10">
            <span className="status-text">
              {statusText}
            </span>
            <img
              className="img-question"
              src={SVG_MENU_APPRO_ARROW_RIGHT}
              onClick={() => toast.info(SKIPMAP[line.skippedType], 1500)}
            />
          </div>
        ) : (
          <div className="head-right">
            <span className="user-name ml-10">
              {agreeTypeFlag
                ? agreeTypeText
                : staffDisplay && isString(staffDisplay)
                  ? name + `(${fnSubstr16(staffDisplay)})`
                  : name}
            </span>
            {agreeTypeFlag && (
              <img
                className="img-question"
                src={SVG_MENU_APPRO_ARROW_RIGHT}
                onClick={this.handleShowToast.bind(this, info)}
              />
            )}
          </div>
        )}
      </div>
    )
  }

  render() {
    return <div className={styles['flow-plan-wrapper']}>{this.renderWrapper()}</div>
  }
}
