import React from 'react'
import styles from './PayeeItemNew.module.less'
import { app } from '@ekuaibao/whispered'
import { getPayeeAccountBg, getPayeeTextColor } from '../../lib/enums'
import _compact from 'lodash/compact'
import { OutlinedTipsDone, FilledGeneralBonusPayroll } from '@hose/eui-icons'
const EKBIcon = app.require('@elements/ekbIcon') as any
const BANK_BG = require('./images/bank_bank_bg.svg')
const BANK_OTHER_BG = require('./images/bank_other_bg.svg')
const BANK_WECHAT_BG = require('./images/bank_wechat_bg.svg')

interface PayeeProps {
  accountName: string
  accountNo: string
  defaultChannel: string
  active: true
  bank: string
  bankLinkNo?: string
  branch: string
  cardNo: string
  certificateNo: string
  certificateType: string
  city: string
  code: string
  detail: any
  corporationId: string
  createTime: number
  icon: string
  id: string
  isDefault: boolean
  logs: any[]
  name: string
  nameSpell: string
  owner: string
  province: string
  sort: string
  staffId?: any
  type: string
  unionBank: string
  unionIcon: string
  updateTime: number
  version: number
  visibility: any
}

interface Props {
  selected?: boolean
  payeeInfo: PayeeProps
  onlyOffline?: boolean
  dynamicChannelMap?: any
  style?: any
  fnHandleLineClick?: (e: any, account: PayeeProps) => void
}

export default function(props: Props) {
  const { payeeInfo, style = {}, fnHandleLineClick, onlyOffline = false, selected, dynamicChannelMap = {} } = props
  const { icon, sort, bank, unionBank, id, accountName, accountNo, defaultChannel, name, code, remark } = payeeInfo
  const defaultChannelValue = onlyOffline ? 'OFFLINE' : defaultChannel || 'OFFLINE'
  const bankName = bank || unionBank
  const _remark = remark?.length < 6 ? remark : remark?.substr(0, 5) + '...'
  const nameValue = name ? name : accountName
  const getBgImg = (sort: string) => {
    switch (sort) {
      case 'WEIXIN':
        return BANK_WECHAT_BG
      case 'BANK':
        return BANK_BG
      default:
        return BANK_OTHER_BG
    }
  }
  return (
    <div
      key={id}
      style={selected ? { ...style, border: '1px solid var(--eui-primary-pri-500, #2555FF)' } : style}
      className={styles['account-item-wrapper']}
      onClick={(e: any) => fnHandleLineClick(e, payeeInfo)}
    >
      <div style={{ position: 'relative' }}>
        <div className="account-item-top" style={{ background: getPayeeAccountBg(sort, false, false) }}>
          <img src={getBgImg(sort)} className="account-item-top-bg"></img>
          <div className="account-item-top-title">
            <div className="account-item-top-title-left" style={{ color: getPayeeTextColor(sort, false, false) }}>
              <div className="card-name">{nameValue}</div>
              {code && <div className='card-code'>{`（${code}）`}</div>}
            </div>
          </div>
          <div className="account-item-top-number" style={{ color: getPayeeTextColor(sort, false, false) }}>
            <span className="card-number">{formatCardNo(accountNo)}</span>
            {remark && (
              <span className="card-remark">
                {i18n.get('备注：')}
                {_remark}
              </span>
            )}
          </div>
        </div>
        <div className="account-item-bottom">
          <img className="account-item-bottom-icon" src={`${icon}?type=new`} />
          <span className="account-item-bottom-bankName">{bankName}</span>
          <EKBIcon name={dynamicChannelMap[defaultChannelValue]?.icon} fontSize={16} />
          <span className="channel-info">{dynamicChannelMap[defaultChannelValue]?.name}</span>
        </div>
        {selected && <div className="account-item-mask" />}
        {selected && <OutlinedTipsDone className="mask-icon" color="white" fontSize={10.8} />}
      </div>
    </div>
  )
}

const formatCardNo = (cardNo: string) => {
  if (/[^0-9]/.test(cardNo)) {
    return cardNo
  }
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1 ')
}
