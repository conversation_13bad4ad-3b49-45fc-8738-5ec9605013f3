/**************************************************
 * Created by nanyuanting<PERSON> on 27/09/2016 16:57.
 **************************************************/
@import "../../styles/layout.less";
@import "../../styles/theme";

.flow-wrapper {
  //padding-top : 26px;

  .detail-content-wrapper {
    position       : absolute;
    top            : 0;
    width          : 100%;
    left           : 0;
    right          : 0;
    bottom         : 0;
    padding-bottom : 100px;

    .mt13 {
      background-color : #F0F3F6;
      height           : 26px;
      width            : 100%;
    }

    .detail-content {
      background-color : #FFFFFF;
      padding          : 0 0 0 40px;
      margin-top       : 24px;
      border-bottom    : 2px solid #EBEBEB;
      border-top       : 2px solid #EBEBEB;

      .fm-item {
        padding          : 0;
        min-height       : 90px;
        background-color : #FFFFFF;
        border-bottom    : 2px solid @border-color-base;
        display          : flex;
        justify-content  : flex-start;
        align-items      : center;
        font-size        : @input-font-size;
        color            : @color-text-base;
        .lb {
          min-width   : 180px;
          flex-shrink : 0;
        }
        .vl {
          flex          : 1;
          word-break    : break-all;
          padding-right : 40px;
        }
        .money {
          color       : #F39575;
          font-size   : 32px;
          font-weight : 500
        }
      }
      div:last-of-type {
        .fm-item {
          border-bottom : none;
        }
      }
    }

    .detail-details {
      background-color : #FFFFFF;

      .title {
        border-bottom   : 2px solid #F3F3F3;
        display         : flex;
        justify-content : flex-start;
        align-items     : center;
        height          : 112px;
        font-weight     : 500;
        color           : #54595B;

        .l {
          height         : 100%;
          display        : flex;
          flex-direction : column;
          flex           : 1;
          .t {
            flex            : 1;
            font-size       : 26px;
            text-align      : center;
            color           : #54595B;
            display         : flex;
            justify-content : flex-start;
            padding-left    : 52px;
            align-items     : center;
            font-weight     : normal;
          }
          .b {
            flex            : 1;
            font-size       : 26px;
            text-align      : center;
            color           : var(--brand-base);
            display         : flex;
            justify-content : flex-start;
            padding-left    : 64px;
            align-items     : center;
          }
        }
        .r {
          flex            : 1;
          height          : 100%;
          display         : flex;
          justify-content : flex-end;
          align-items     : center;
          padding-right   : 40px;
        }
      }

      .list-wrap {
        .list {
          padding-left : 48px;
          .item {
            display       : block;
            padding-left  : 40px;
            border-bottom : 2px solid #F3F3F3;
            &:last-child {
              border-bottom : none;
            }
            .invoice-tip {
              margin-left : 66px;
              display     : flex;
              align-items : center;
              font-size   : 22px;
              color       : #FF7C7C;
            }
            .item-content {
              display         : flex;
              justify-content : flex-start;
              align-items     : center;
            }
            .i {
              background-color : #A1C7E7;
              width            : 60px;
              height           : 60px;
              border-radius    : 30px;
              img {
                width       : 60px;
                height      : 60px;
                flex-shrink : 0;
              }
            }
            .p {
              margin         : 0 24px;
              flex           : 1;
              display        : flex;
              flex-direction : column;
              align-items    : flex-start;
              overflow       : hidden;
              div:nth-child(1) {
                width         : 100%;
                padding       : 4px;
                font-size     : 28px;
                color         : #54595B;
                overflow      : hidden;
                white-space   : nowrap;
                text-overflow : ellipsis;
              }
              .consume-text-left {
                text-align : left !important;
                display    : flex;
                img { flex-shrink : 0; }
              }
              div:nth-child(2) {
                width         : 95%;
                font-size     : 24px;
                color         : #B1B9BD;
                overflow      : hidden;
                white-space   : nowrap;
                text-overflow : ellipsis;
              }
            }
            .t {
              max-width    : 220px;
              flex-shrink  : 0;
              margin-right : 40px;
              text-align   : right;
              font-size    : 28px;
              line-height  : 1;
              color        : #54595B;
            }

          }
        }
      }
    }

    .loan-view {
      display          : flex;
      flex-direction   : row;
      height           : 88px;
      align-items      : center;
      justify-content  : center;
      background-color : #FFFFFF;
      margin           : 16px;
      font-size        : 26px;
      color            : #A2ABAF;
      position         : relative;
      img {
        width        : 44px;
        height       : 44px;
        margin-right : 20px;
      }
      .right {
        height   : 100%;
        width    : 100px;
        position : absolute;
        right    : 0px;
        .loan-arrow {
          position    : absolute;
          right       : 60px;
          display     : flex;
          height      : 100%;
          align-items : center;
          .warn {
            width            : 28px;
            height           : 28px;
            background-color : #F17B7B;
            border           : solid 2px #F39575;
            border-radius    : 14px;
            display          : flex;
            flex-direction   : column;
            align-items      : center;
            justify-content  : center;
            div:first-child {
              width            : 2.6px;
              height           : 12px;
              background-color : #FFFFFF;
            }
            div:last-child {
              margin-top       : 2px;
              width            : 2.6px;
              height           : 2px;
              background-color : #FFFFFF;
            }
          }
        }

        .arrow {
          display  : flex;
          position : absolute;
          right    : -10px;
          height   : 100%;
          img {
            height      : 100%;
            line-height : 100%;
          }
        }
      }
    }
    .condition-remind {
      height           : 80px;
      width            : 100%;
      display          : flex;
      justify-content  : center;
      align-items      : center;
      font-size        : 26px;
      color            : #F39575;
      background-color : #FCFCFC;
      border-bottom    : 2px solid #EEEEEE;
    }
    .loan-remind {
      height           : 80px;
      background-color : #FFFCE6;
      border           : 2px solid #E9E9E9;
      margin           : 24px;
      display          : flex;
      align-content    : space-between;
      align-items      : center;
      .info {
        flex         : 1;
        font-size    : 26px;
        color        : #F39575;
        padding-left : 32px;
      }
      .icon {
        margin-right : 30px;
        width        : 44px;
        height       : 44px;
      }

    }
    .flow-wrapper {
      background-color : #FFFFFF;
      padding-top      : 21px;
      padding-bottom   : 50px;
      margin-top       : 46px;
      border           : solid 2px #EBEBEB;
      .detail-content-wrapper {
        width: 100%;
        top : 0;
      }
    }
  }

  .plan-content-wrapper {
    overflow-x : hidden;
    position   : relative;

    ul, li {
      list-style : none;
      border     : none;
    }

    ul {
      -webkit-margin-before : 0;
      -webkit-margin-after  : 0;
      -webkit-margin-start  : 0;
      -webkit-margin-end    : 0;
      -webkit-padding-start : 0;

      li.item {
        position  : relative;
        font-size : 26px;
        color     : #54595B;
        padding   : 24px 24px 2px 80px;
        height    : 100%;
        .content {
          background-color : #FAFAFA;
        }
        .status-bg {
          position : absolute;
          top      : 0;
          bottom   : 0;
          left     : 0;
          width    : 80px;
          .gray-line {
            position         : absolute;
            top              : 0;
            left             : 40px;
            bottom           : 0;
            display          : block;
            width            : 2px;
            background-color : #E5E5E5;
          }
          .circle {
            position         : absolute;
            z-index          : 2;
            top              : 50px;
            left             : 34px;
            display          : block;
            width            : 14px;
            height           : 14px;
            border-radius    : 14px;
            background-color : #DDDDDD;
          }
        }
        .content {
          padding          : 0 16px;
          border-radius    : 8px;
          background-color : #FFFFFF;
          //border           : solid 2px #E0E0E0;
          box-shadow       : 0 2px 0 rgba(229, 229, 229, 0.5);
          border           : solid 2px #EEEEEE;
          width            : 100%;
          .header {
            display         : flex;
            align-items     : center;
            justify-content : space-between;
            border-bottom   : #EEEEEE 2px solid;
            padding         : 10px 0;
            .condition {
              display          : inline-block;
              margin-left      : 16px;
              width            : 88px;
              height           : 40px;
              line-height      : 40px;
              text-align       : center;
              border-radius    : 4px;
              background-color : #FFFFFF;
              font-size        : 22px;
              font-weight      : 500;
              color            : #A2ABAF;
              border           : solid 2px #E6E6E6;
            }
            .header-state {
              width      : 180px;
              text-align : right;
            }
          }
          .b-main {
            padding         : 0 8px;
            display         : flex;
            align-items     : center;
            justify-content : space-between;
            height          : 94px;
            font-size       : 28px;
            .info {
              display     : flex;
              align-items : center;
              .no-choose {
                color : #A2ABAF;
              }
              .user-img {
                margin-right  : 16px;
                width         : 60px;
                height        : 60px;
                border-radius : 30px;
              }
              .user-name {
                font-weight : 500;
                color       : #54595B;
              }
              .comment-img {
                width       : 40px;
                height      : 40px;
                margin-left : 16px;
                margin-top  : 5px;
              }
              .head-right {
                display     : flex;
                align-items : center;
                .status-text {
                  font-size : 26px;
                  color     : #A2ABAF;
                }
                .img-question {
                  width       : 44px;
                  margin-left : 10px;
                }
              }
            }
            .arrow-right {
              width : 44px;
            }
          }

          .reason {
            position         : relative;
            margin           : 0 8px 24px;
            padding          : 12px;
            border-radius    : 8px;
            background-color : #FFFFFF;
            line-height      : 1.3;
            border           : solid 2px #EEEEEE;
            font-size        : 24px;
            color            : #A2ABAF;
            word-break       : break-all;
            .arrow-top {
              position         : absolute;
              top              : -9px;
              left             : 20px;
              display          : block;
              width            : 18px;
              height           : 18px;
              background-color : #FFFFFF;
              border-left      : solid 2px #EEEEEE;
              border-top       : solid 2px #EEEEEE;
              transform        : rotate(45deg);
            }
          }
        }

        &.resolve {
          .status-bg {
            .gray-line {
              &::after {
                content    : '';
                display    : block;
                position   : absolute;
                z-index    : 2;
                top        : 0;
                left       : 0;
                bottom     : -60px;
                width      : 2px;
                background : var(--brand-base);
              }
            }
            .circle {
              &::after {
                content         : '';
                display         : block;
                position        : absolute;
                z-index         : 2;
                top             : -10px;
                left            : -9px;
                width           : 32px;
                height          : 32px;
                background      : url("./images/approve-status-resolve.svg");
                background-size : contain;
              }
            }
          }
        }
        &.justApproving {
          .status-bg {
            .gray-line {
              display : none;
              &::after {
                display : none;
              }
            }
            .circle {
              top              : 45%;
              background-color : transparent;
              &::after {
                content         : '';
                display         : block;
                position        : absolute;
                z-index         : 2;
                //top             : 50%;
                left            : -9px;
                width           : 32px;
                height          : 32px;
                background      : url("./images/approve-status-approving.svg");
                background-size : contain;
              }
            }
          }
        }
        &.justReject {
          .status-bg {
            .gray-line {
              display : none;
              &::after {
                display : none;
              }
            }
            .circle {
              top              : 45%;
              background-color : transparent;
              &::after {
                content         : '';
                display         : block;
                position        : absolute;
                z-index         : 2;
                //top             : 50%;
                left            : -9px;
                width           : 32px;
                height          : 32px;
                background      : url("./images/approve-status-reject.svg");
                background-size : contain;
              }
            }
          }
        }
        &.justPay {
          .status-bg {
            .gray-line {
              display : none;
              &::after {
                display : none;
              }
            }
            .circle {
              top              : 45%;
              background-color : transparent;
              &::after {
                content         : '';
                display         : block;
                position        : absolute;
                z-index         : 2;
                //top             : 50%;
                left            : -9px;
                width           : 32px;
                height          : 32px;
                background      : url("./images/approve-status-resolve.svg");
                background-size : contain;
              }
            }
          }
        }
        &.completion {
          .status-bg {
            .gray-line {
              &::after {
                content    : '';
                display    : block;
                position   : absolute;
                z-index    : 2;
                top        : 0;
                left       : 0;
                bottom     : 0;
                width      : 2px;
                background : var(--brand-base);
              }
            }
            .circle {
              &::after {
                content         : '';
                display         : block;
                position        : absolute;
                z-index         : 2;
                top             : -10px;
                left            : -9px;
                width           : 32px;
                height          : 32px;
                background      : url("./images/approve-status-resolve.svg");
                background-size : contain;
              }
            }
          }
        }
        &.approving {
          .circle {
            &::after {
              content         : '';
              display         : block;
              position        : absolute;
              z-index         : 2;
              top             : -14px;
              left            : -13px;
              width           : 40px;
              height          : 40px;
              background      : url("./images/approve-status-approving.svg");
              background-size : contain;
            }
          }
        }
        &.reject {
          .content {
            .header {
              .status-text {
                color : #FF7C7C;
              }
            }
            .reason {
              background-color : #FFFFFF;
              .arrow-top {
                background-color : #FFFFFF;
              }
            }
          }
          .circle {
            &::after {
              content         : '';
              display         : block;
              position        : absolute;
              z-index         : 2;
              top             : -14px;
              left            : -13px;
              width           : 40px;
              height          : 40px;
              background      : url("./images/approve-status-reject.svg");
              background-size : contain;
            }
          }
        }

        &:first-child {
          .status-bg {
            .gray-line {
              top : 50px;
            }
          }
        }
        &:last-child {
          .status-bg {
            .gray-line {
              height : 50px;
            }
          }
        }
      }
    }

  }

  .expense-content-wrapper {

    .header-wrapper {
      //.am-list .am-list-item .am-list-line .am-list-content {
      //  font-size   : 26px;
      //  line-height : 1;
      //  color       : #A2ABAF;
      //}
    }

    .body-wrapper {
      .fix-wrapper-font-size-and-color();
      .fix-wrapper-form-list-item();

      //.am-list-item .am-list-line .am-list-extra { color : #54595B; flex : 1 }
      //.am-textarea-control {
      //  padding : 33px 10px 31px 0;
      //}

      .specification-input {
        .am-list-item.am-list-item-middle {
          padding-left : 0;
          .am-list-extra {
            flex       : 1;
            text-align : left;
          }
        }

        .am-list-item .am-list-line .am-list-content {
          flex  : initial;
          width : 27%;
        }
        .am-list-item .am-list-line .am-list-extra {
          flex-basis : initial;
        }
      }

      //.am-list {
      //  border-top    : 2px solid #EBEBEB;
      //  border-bottom : 2px solid #EBEBEB;
      //
      //  .am-list-item:after {
      //    border-bottom : 2px solid #F3F3F3;
      //  }
      //  //去掉最后一个cell的分割线
      //  .am-list-body > div:last-child .am-list-item:after {
      //    border-bottom : none;
      //  }
      //  .am-list-body {
      //    .am-input-item {
      //      .am-input-control {
      //        input {
      //          width : 90%;
      //        }
      //      }
      //    }
      //  }
      //}
      .flow-field {
        &:last-child {
          border-bottom : none;
          padding-left  : 0;
        }
        .am-list-item {
          padding-left : 0;
        }
      }
    }

    .body2-wrapper {
      background-color : #FFFFFF;
      margin           : 32px 0;
      border-top       : 2px solid #EBEBEB;
      border-bottom    : 2px solid #EBEBEB;

      .action {
        height        : 96px;
        display       : flex;
        font-size     : 28px;
        text-align    : center;
        color         : var(--brand-base);
        border-bottom : 2px solid #F3F3F3;

        .l, .r {
          flex            : 1;
          display         : flex;
          align-items     : center;
          justify-content : center;

          div:first-child {
            margin-right : 20px;
            width        : 32px;
            height       : 32px;
            img {
              width  : 32px;
              height : 32px;
            }
          }
        }
        .m {
          width            : 2px;
          height           : 100%;
          background-color : @border-color-base;
        }
      }

      .title {
        border-bottom   : 2px solid #F3F3F3;
        display         : flex;
        justify-content : flex-start;
        align-items     : center;
        height          : 112px;
        font-weight     : 500;
        .l {
          height         : 100%;
          display        : flex;
          flex-direction : column;
          flex           : 1;
          .t {
            flex            : 1;
            font-size       : 26px;
            text-align      : center;
            font-weight     : normal;
            color           : #54595B;
            display         : flex;
            justify-content : flex-start;
            padding-left    : 52px;
            align-items     : center;
          }
          .b {
            flex            : 1;
            font-size       : 26px;
            text-align      : center;
            color           : var(--brand-base);
            display         : flex;
            justify-content : flex-start;
            padding-left    : 64px;
            align-items     : center;
          }
        }
        .r {
          flex            : 1;
          height          : 100%;
          display         : flex;
          justify-content : flex-end;
          align-items     : center;
          padding-right   : 40px;
          color           : #54595B;
        }
      }

      .list-wrap {
        .list {
          .fix-swipe();

          .item {
            padding-top    : 10px;
            padding-bottom : 10px;
            display        : block;
            border-bottom  : 2px solid #F3F3F3;
            padding-left   : 40px;
            .item-content {
              display         : flex;
              justify-content : flex-start;
              align-items     : center;
              .p {
                .consume-text-left {
                  img { flex-shrink : 0; }
                }
              }
            }
            .invoice-tip {
              margin-left   : 144px;
              display       : flex;
              align-items   : center;
              font-size     : 22px;
              color         : #FF7C7C;
              overflow-x    : hidden;
              text-overflow : ellipsis;
              white-space   : nowrap;
            }
            .d {
              width       : 60px;
              height      : 60px;
              padding-top : 10px;
              img {
                width  : 44px;
                height : 44px;
              }
            }
            .i {
              background-color : #A1C7E7;
              width            : 60px;
              height           : 60px;
              border-radius    : 30px;
              img {
                width  : 60px;
                height : 60px;
              }
            }
            .p {
              margin-left    : 24px;
              flex           : 1;
              display        : flex;
              flex-direction : column;
              width          : 30%;
              div:nth-child(1) {
                padding       : 4px;
                flex          : 1;
                font-size     : 28px;
                text-align    : left;
                color         : #54595B;
                width         : 95%;
                overflow      : hidden;
                white-space   : nowrap;
                text-overflow : ellipsis;
              }
              div:nth-child(2) {
                flex          : 1;
                font-size     : 24px;
                text-align    : left;
                color         : #B1B9BD;
                width         : 95%;
                overflow      : hidden;
                white-space   : nowrap;
                text-overflow : ellipsis;
              }
            }
            .t {
              flex-shrink  : 0;
              max-width    : 2.3rem;
              margin-right : 40px;
              font-size    : 28px;
              line-height  : 1;
              color        : #54595B;
            }
          }
          //.am-swipe:last-of-type .am-swipe .am-swipe-content .item {
          //  border-bottom : none;
          //}
        }
      }

    }
  }

  .expense-code {
    text-align : center;
    color      : #C5C5C5;
    font-size  : 26px;
    margin     : 40px 0;
  }
}

.am-list-body {
  .payee-item {
    padding          : 0;
    background-color : #FFFFFF;
    //border-bottom    : 2px solid #F3F3F3;
    display          : flex;
    justify-content  : flex-start;
    min-height       : 100px;
    line-height      : 100px;
    &.am-list-item.am-input-item {
      height : auto;
    }

    .payee-label {
      font-size    : 28px;
      color        : @color-text-base;
      min-width    : 116px;
      margin-right : 30px;
    }

    .payee-info {
      flex      : 1;
      font-size : 28px;
      color     : #54595B;
    }
  }
}
