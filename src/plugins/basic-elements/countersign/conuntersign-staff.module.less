@import '~@ekuaibao/eui-styles/less/token.less';

.staff-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  :global {
    .staff-list {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      .staff-item {
        display: flex;
        align-items: center;
        padding-left: 32px;
        &:active {
          background: @color-bg-3;
        }
        .staff-avatar {
          flex-shrink: 0;
        }
        .staff-info {
          flex: 1;
          margin-left: 32px;
          border-bottom: 2px solid #eeeeee;
          margin-top: 24px;
          .username {
            color: @color-black-1;
            font-size: 28px;
            text-align: left;
          }
          .reason {
            font-size: 24px;
            color: #a2abaf;
            margin-bottom: 24px;
            text-align: left;
          }
        }
        .check-box {
          margin-right: 32px;
        }
      }
    }
    .staff-bottom {
      flex-shrink: 0;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .checked-all {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 32px;
        span {
          font-size: 28px;
          color: #696d6d;
          margin-left: 16px;
        }
      }
      .submit {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 204px;
        height: 100%;
        background-color: var(--brand-base);
        font-size: 28px;
        color: #ffffff;
      }
    }
    .am-search-value {
      font-size: 28px;
    }
  }
}
