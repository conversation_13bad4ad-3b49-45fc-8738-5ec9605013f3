import { app } from '@ekuaibao/whispered'
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/6/27 下午3:32.
 */
import style from './flow-countersign.module.less'

import React from 'react'
import * as BasicStateConst from '../basic-state-const'
const COUNT_STATE = BasicStateConst.COUNTERSIGN_STATE
// import SVG_AVATAR_NULL from './../../../images/avatar-null.svg'
const SVG_AVATAR_NULL = app.require('@images/avatar-null.svg')
// import SVG_MENU_APPRO_ARROW_RIGHT from './../../../images/menu-appro-arrow-right.svg'
const SVG_MENU_APPRO_ARROW_RIGHT = app.require('@images/menu-appro-arrow-right.svg')
const COUNTER_AGREE = BasicStateConst.COUNTERSIGN_AGREE
// import SVG_ATTAC from '../../../elements/puppet/Details/images/attachment.svg'
const SVG_ATTAC = app.require('@elements/puppet/Details/images/attachment.svg')
// import IconTag from '../../../elements/puppet/IconTag'
const IconTag = app.require('@elements/puppet/IconTag')
// import { fnPreviewAttachments } from '../../../components/utils/fnAttachment'
const { fnPreviewAttachments } = app.require('@components/utils/fnAttachment')

import { toast } from '../../../lib/util'
import Avatar from '../avatars'
import { isString } from '@ekuaibao/helpers'
import { fnSubstr16 } from '../../../elements/approve-log/history-log-item'
import { getUserDisplayName } from "../../../components/utils/fnDataLinkUtil";

export default class FlowConunterSignStaff extends React.Component {
  constructor(props) {
    super(props)
  }

  handleFlowLog(flowLogs, line, node) {
    return flowLogs.filter(o => {
      let signerId = line.signerId.id
      let nodeId = o.attributes && o.attributes.nodeId
      let operatorId
      if (!o.operatorId || typeof o.operatorId === 'string') {
        operatorId = o.operatorId
      } else {
        operatorId = o.operatorId.id
      }
      return nodeId && nodeId === node.id && operatorId && signerId === operatorId
    })
  }

  handleAttachments = curLog => {
    const length = curLog.attachments && curLog.attachments.length
    const name = length > 0 ? i18n.get('{__k0}个附件', { __k0: length }) : i18n.get('无附件')
    if (length) {
      return { img: SVG_ATTAC, name: name, type: 'attachment', data: curLog.attachments }
    }
    return undefined
  }

  handleTipsClick(value) {
    let data = { value: value.data, index: 0 }
    fnPreviewAttachments(data)
  }

  fnGetAgreeDesc(line) {
    if (line.agreeType === 'ADMIN_SKIP_NODE_AUTO') {
      return i18n.get('（管理员跳过）')
    }
    if (
      line.agreeType === 'REJECT_FORCE_APPROVALS' ||
      line.agreeType === 'ROLLBACK_FORCE_APPROVALS' ||
      line.agreeType === 'EDIT_FORCE_APPROVALS'
    ) {
      return ''
    }
    return i18n.get('（自动同意）')
  }

  renderItem(node, flowLogs, customStaff) {
    if (customStaff) {
      return customStaff.map((staff, key) => {
        const { staffDisplayConfigField } = this.props
        const staffDisplay = staff?.[staffDisplayConfigField]
        const name = getUserDisplayName(staff)
        return (
          <div className="flow-countersign-item" key={staff.id}>
            <div className={key === 0 ? 'flow-countersign-item-content' : 'flow-countersign-item-content mt-16'}>
              <div className="staff-infos">
                <Avatar className="staff-avatar" imgList={[staff?.avatar || SVG_AVATAR_NULL]} />
                <div className="staff-des">
                  <div className="countersign-name">
                    {staffDisplay && isString(staffDisplay) ? name + ` (${fnSubstr16(staffDisplay)}) ` : name}
                  </div>
                  {staff?.description && <div className="countersign-reason">{staff.description}</div>}
                </div>
              </div>
            </div>
          </div>
        )
      })
    }

    return (
      node.counterSigners &&
      node.counterSigners.map((line, key) => {
        const curLogs = this.handleFlowLog(flowLogs, line, node)
        const curLog = curLogs.length > 0 ? curLogs[curLogs.length - 1] : {}
        const attachments = this.handleAttachments(curLog)
        let signerId = line.signerId
        let img
        if (line.action) {
          img = COUNT_STATE[line.action]
        } else {
          if (line.state === 'PROCESSED') {
            img = COUNT_STATE['PROCESSED']
          }
        }
        let agreeType = line.agreeType
        let comment = curLog && curLog.attributes && curLog.attributes.comment
        let attributes = curLog.attributes
        // 通讯录员工显示配置
        const { staffDisplayConfigField } = this.props
        const staffDisplay = signerId?.[staffDisplayConfigField]
        const agreeTypeDesc = this.fnGetAgreeDesc(line)
        const name = signerId ? getUserDisplayName(signerId) : ''
        return (
          <div className="flow-countersign-item" key={signerId.id}>
            <div className={key === 0 ? 'flow-countersign-item-content' : 'flow-countersign-item-content mt-16'}>
              <div className="staff-infos">
                <Avatar className="staff-avatar" imgList={[signerId.avatar || SVG_AVATAR_NULL]} />
                <div className="staff-des">
                  <div className="countersign-name">
                    {agreeType && agreeType !== 'NO_AUTO_AGREE'
                      ? staffDisplay && isString(staffDisplay)
                        ? name + ` (${fnSubstr16(staffDisplay)}) ` + agreeTypeDesc
                        : name + agreeTypeDesc
                      : staffDisplay && isString(staffDisplay)
                      ? name + ` (${fnSubstr16(staffDisplay)}) `
                      : name}
                    {agreeType !== 'NO_AUTO_AGREE' &&
                      agreeType !== 'REJECT_FORCE_APPROVALS' &&
                      agreeType !== 'ROLLBACK_FORCE_APPROVALS' &&
                      agreeType !== 'EDIT_FORCE_APPROVALS' &&
                      COUNTER_AGREE[agreeType] && (
                        <img
                          src={SVG_MENU_APPRO_ARROW_RIGHT}
                          onClick={() => toast.info(COUNTER_AGREE[agreeType], 1500)}
                        />
                      )}
                  </div>
                  <div className="countersign-reason">{line.description}</div>
                </div>
              </div>
              <div className="countersign-right">
                {this.renderAutoGraphImage(attributes)}
                {img && (
                  <img
                    className={attributes && attributes.autographImageId ? 'staff-checked-autograph' : 'staff-checked'}
                    src={img}
                  />
                )}
              </div>
            </div>
            {comment && (
              <div className="reason">
                <span>{comment}</span>
              </div>
            )}
            {attachments && (
              <IconTag
                key={attachments.key}
                className="tag"
                src={attachments.img}
                text={attachments.name}
                onClick={this.handleTipsClick.bind(this, attachments)}
              />
            )}
          </div>
        )
      })
    )
  }

  renderAutoGraphImage(attributes) {
    return attributes && attributes.autographImageId ? (
      <div style={{ width: 50, height: 30, marginRight: 25 }}>
        <img style={{ width: 50, height: 30, borderRadius: 5 }} src={attributes.autographImageId.url} />
      </div>
    ) : null
  }

  renderNotMatch(noMatchNodes) {
    return (
      noMatchNodes &&
      noMatchNodes.map((line, key) => {
        const isPass = line.skippedType !== 'NO_SKIPPED'
        return (
          <div className="flow-countersign-item" key={key}>
            <div className="flow-countersign-item-content">
              <div className="staff-infos">
                <Avatar className="staff-avatar" imgList={[SVG_AVATAR_NULL]} />
                <div className="staff-des">
                  <div className="countersign-name">
                    {i18n.get('匹配不到审批人（自动跳过）')}
                    {isPass && (
                      <img
                        src={SVG_MENU_APPRO_ARROW_RIGHT}
                        onClick={() => toast.info(i18n.get('根据流程配置，该节点匹配不到审批人'), 1500)}
                      />
                    )}
                  </div>
                  <div className="countersign-reason">{line.description}</div>
                </div>
              </div>
            </div>
          </div>
        )
      })
    )
  }

  render() {
    let { node, flowLogs, customStaff } = this.props
    let nonMatchedDefines = node.nonMatchedDefines
    return (
      <div className={style['flow-countersign-wrapper']}>
        {nonMatchedDefines && nonMatchedDefines.length > 0 && this.renderNotMatch(nonMatchedDefines)}
        {node && this.renderItem(node, flowLogs, customStaff)}
      </div>
    )
  }
}
