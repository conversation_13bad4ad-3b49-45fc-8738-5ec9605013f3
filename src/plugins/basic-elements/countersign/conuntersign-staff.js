/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/6/27 下午2:07.
 */
import { app } from '@ekuaibao/whispered'
import styles from './conuntersign-staff.module.less'
import React, { PureComponent } from 'react'
import { Checkbox, SearchBar } from 'antd-mobile'
import <PERSON>han<PERSON><PERSON><PERSON><PERSON>Hook from '.././enhance-title-hook'
import { app as api } from '@ekuaibao/whispered'
import { cloneDeep, noop, debounce } from 'lodash'
import Avatar from '../avatars'
import { randomUUID } from '../../../lib/util'
const { getStaffShowValue } = app.require('@components/utils/fnDataLinkUtil')

const SVG_AVATAR_NULL = api.require('@images/avatar-null.svg')

@EnhanceTitleHook(i18n.get('会签的审批人'))
export default class ConuntersignStaff extends PureComponent {
  constructor(props) {
    super(props)
    let { isEditConfig, data } = props
    let cloneData = cloneDeep(data)
    let counterSigners = this.initData(isEditConfig, cloneData)
    this.state = {
      counterSigners,
      searchText: '',
      searchSingers: []
    }
    props.overrideGetResult(this.getResult)
  }

  initData(isEditConfig, cloneData) {
    let { isAllStaffs, counterSignersCandidate, signers = cloneData.counterSigners } = cloneData
    let staffs = api.getState()['@common'].staffs
    let candidateData = isAllStaffs
      ? staffs.map(line => ({ signerId: line })).filter(line => !cloneData?.signersBlacklist.includes(line.signerId?.id))
      : counterSignersCandidate
    let counterSigners = isEditConfig ? candidateData : this.handleData(cloneData)
    let signersIds = isEditConfig ? signers.map(line => line.signerId.id) : []
    signersIds.length &&
      counterSigners.forEach(line => {
        if (signersIds.includes(line.signerId.id)) {
          line.checked = true
        }
      })
    return counterSigners
  }

  handleData(node) {
    let counterSigners = node.counterSigners || []
    let nonMatchedDesc = node.nonMatchedDefines || []
    if (nonMatchedDesc.length > 0) {
      let nonMatched = []
      nonMatchedDesc.forEach(line => {
        let lineObj = line
        let signer = {}
        signer.name = i18n.get('匹配不到审批人')
        signer.id = randomUUID(0, 99999)
        lineObj.signerId = signer
        nonMatched.push(lineObj)
      })
      counterSigners = nonMatched.concat(counterSigners)
    }
    return counterSigners
  }

  getResult = () => {
    const { counterSigners } = this.state
    let result = counterSigners.filter(line => line.checked)
    counterSigners.map(line => delete line.checked)
    return result
  }

  handleSubmit = () => {
    this.props.layer.emitOk()
  }

  handleSearch = debounce(value => {
    const { counterSigners } = this.state
    let searchSingers = []
    if (value && value.trim()) {
      searchSingers = counterSigners.filter(line => {
        return !!~line.signerId.name.indexOf(value)
      })
    }
    this.setState({ searchText: value, searchSingers })
  }, 400)

  handleCancel = () => {
    this.setState({
      searchText: '',
      searchSingers: []
    })
  }

  renderSearch() {
    return <SearchBar placeholder={i18n.get('搜索名称')} onChange={this.handleSearch} onClear={this.handleCancel} />
  }

  handleChecked = line => {
    line.checked = !line.checked
    this.forceUpdate()
  }

  getValue(line) {
    const { deptMap, staffDisplayConfig } = this.props
    const {
      signerId: { name, defaultDepartment }
    } = line
    if (deptMap) {
      line.signerId.defaultDepartment = deptMap[defaultDepartment]
    }
    const showData = getStaffShowValue(line.signerId, staffDisplayConfig)
    let showValue = showData ? `${name}(${showData})` : name
    return showValue
  }

  renderItem() {
    const { isEditConfig } = this.props
    const { searchText, searchSingers, counterSigners } = this.state
    const staffsList = searchText.length ? searchSingers : counterSigners
    const onClick = isEditConfig ? this.handleChecked : noop
    return staffsList.map(line => {
      const {
        checked,
        description,
        signerId: { id, avatar }
      } = line
      let showValue = this.getValue(line)
      return (
        <div className="staff-item" key={id} onClick={onClick.bind(this, line)}>
          {isEditConfig && (
            <div className="check-box">
              <Checkbox checked={checked} />
            </div>
          )}
          <Avatar className="staff-avatar" imgList={[avatar || SVG_AVATAR_NULL]} />
          <div className="staff-info">
            <div className="username">{showValue}</div>
            <div className="reason">{description}</div>
          </div>
        </div>
      )
    })
  }

  checkedCount() {
    const { counterSigners } = this.state
    return counterSigners.filter(line => line.checked).length
  }

  render() {
    const { isEditConfig } = this.props
    return (
      <div className={styles['staff-content']}>
        {isEditConfig && this.renderSearch()}
        <div className="staff-list">{this.renderItem()}</div>
        {isEditConfig && (
          <div className="staff-bottom">
            <div className="submit" onClick={this.handleSubmit}>
              {i18n.get('确定（{__k0})', { __k0: this.checkedCount() })}
            </div>
          </div>
        )}
      </div>
    )
  }
}
