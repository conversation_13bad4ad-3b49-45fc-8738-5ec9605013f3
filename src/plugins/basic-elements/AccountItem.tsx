/**************************************
 * Created By LinK On 2019/12/12 18:38.
 **************************************/

import React,{ useState, memo, useMemo} from 'react'
import { app as api } from '@ekuaibao/whispered'
import styles from './AccountItem.module.less'
const EKBIcon = api.require('@elements/ekbIcon')
import { accountSortImgMap } from '../../lib/enums'
import { toast } from '../../lib/util'
import _compact from 'lodash/compact'

interface AccountProps {
  accountName: string
  accountNo: string
  active: true
  bank: string
  bankLinkNo?: string
  branch: string
  branchId: any
  cardNo: string
  certificateNo: string
  certificateType: string
  city: string
  code: string
  corporationId: string
  createTime: number
  icon: string
  id: string
  isDefault: boolean
  logs: any[]
  name: string
  nameSpell: string
  owner: string
  province: string
  sort: string
  staffId?: any
  type: string
  unionBank: string
  unionIcon: string
  updateTime: number
  version: number
  visibility: any
  remark: any,
  filterActive: boolean,
  customFields?:[]
}

interface Props {
  isDefault: boolean
  selected?: boolean
  account: AccountProps
  payeeConfig?: any
  fnHandleLineClick: (e: any, account: AccountProps) => void
  fnHandleSelectType: (account: AccountProps) => void
  globalFields:[]
}
const PERSONAL = 'PERSONAL'

const colorMap:any = {
  WEIXIN:'account-wrapper-green',
  ALIPAY:'account-wrapper-blue',
  OVERSEABANK: 'account-wrapper-blue',
  OTHER:'account-wrapper-blue',
}

export default function(props: Props) {
  const { account, fnHandleSelectType, fnHandleLineClick, isDefault, selected, payeeConfig, globalFields} = props
  const { accountName, accountNo, sort } = account
  const me_info = api.getState()['@common'].me_info
  const owner = account.owner === 'CORPORATION' ? i18n.get('企业') : account.staffId && account.staffId.name
  const isShared = account.staffId && account.staffId.id !== me_info.staff.id
  const icon = !!account.unionBank ? account.unionIcon : account.icon
  const bankName = sort === 'ALIPAY' ? i18n.get('支付宝(办公)') : account?.branchId?.name || account?.branch || account.unionBank || account.bank
  const _bankName = bankName?.length < 9 ? bankName : bankName?.substr(0, 8) + '...'
  const type = account.type === PERSONAL ? i18n.get('个人账户') : i18n.get('对公账户')
  const iconName = accountSortImgMap[sort]
  const iconStyle = sort === 'OVERSEABANK' ? { width: '14px' } : {}
  const payeeSetConfig = account.type === 'PUBLIC' ? payeeConfig?.publicAccountConfig :payeeConfig.personalAccountConfig

  let wrapClass = styles['account-wrapper']
  if(payeeSetConfig?.publicBackgroundCls){
    wrapClass += ' ' +  styles[payeeSetConfig?.publicBackgroundCls] // ‘mod-green-bg’ 莫迪兰绿 对公专属色
  }else{
    wrapClass += ` ${styles[colorMap[sort]]}`
  } 

  wrapClass = selected ? wrapClass + ' ' + styles['account-wrapper-selected'] : wrapClass
  if (!account.filterActive) {
    wrapClass += ' ' + styles['account-wrapper-gray']
  }

  const handleSelectType = (e: any) => {
    e && e.stopPropagation && e.stopPropagation()
    fnHandleSelectType(account)
  }

  const remarkDisplay =
    account.type === 'PERSONAL'
      ? payeeConfig?.personalAccountConfig?.remarkDisplay
      : payeeConfig?.publicAccountConfig?.remarkDisplay
  const remark = account?.remark || ''
  const _remark = remark?.length < 6 ? remark : remark?.substr(0, 5) + '...'
  const showRemark = remark && remarkDisplay

  return (
    <div key={account.id} className={wrapClass} onClick={(e: any) => fnHandleLineClick(e, account)}>
      <div className="account-icon-wrap">
        <EKBIcon name={iconName} className="account-bg-icon" />
      </div>
      <div className="account-selected-mask">
        <EKBIcon name="#EDico-check-circle" className="account-selected-icon" />
      </div>
      <div className="account-header">
        <div className="account-header-left">
          <span className="account-header-name">{accountName}</span>
          <div className="account-header-left-right-part">
            {isDefault && <span className={'account-header-span'}>{i18n.get('默认')}</span>}
            <span className="account-header-span">{type}</span>
            {isShared && <span className="account-header-span">{i18n.get('共享')}</span>}
          </div>
        </div>
        {sort !== 'VIRTUALCARD' && account.filterActive && <div className="account-header-right" onClick={handleSelectType}>
          <EKBIcon onClick={handleSelectType} name="#EDico-more2" className="icon-22" />
        </div>}
      </div>
      <div className="account-content">
        <div className="account-content-inner">{formatCardNo(accountNo)}</div>
      </div>
      <div className="account-footer">
        <div className="account-icon-box">
          <img className="account-icon" src={icon} style={iconStyle} />
        </div>
        <span className="account-bank-name">
          {bankName?.length < 9 ? bankName : <span onClick={() => toast.info(bankName)}>{_bankName}</span>}
        </span>
        {showRemark && (
          <span className="remark-item ml-10">
            {remark?.length < 6 ? remark : <span onClick={() => toast.info(remark)}>{_remark}</span>}
          </span>
        )}
        {owner ? (
          <span className="account-footer-right">
            {i18n.get('所有者：')}
            {owner}
          </span>
        ) : null}
      </div>
      <DisplayMore account={account} globalFields={globalFields} payeeSetConfig={payeeSetConfig}/>
    </div>
  )
}

// 账户组件展示更多
const DisplayMore = (props:any) =>{
  const {account,globalFields,payeeSetConfig} = props
  const [displayArrow,setDisplayArrow] = useState(false)

  const customFilesData = useMemo(()=>{
    const showCardCustomFields = payeeSetConfig?.showCardCustomFields || []
    return _compact(showCardCustomFields.map(item=>{
      if((account.customFields && account.customFields[item])){
        return {
          label: globalFields.filter((v:any) => v.name === item)[0]?.label || '',
          value:(account.customFields && account.customFields[item]) ?? ''
        }
      }
    }))
  },[])

  const handleOpen = (e: any) => {
    e && e.stopPropagation && e.stopPropagation()
    setDisplayArrow(!displayArrow)
  }

  
  return (
    customFilesData.length ? 
      <div className={ displayArrow ? 'account-shadow  open' : 'account-shadow'} onClick={handleOpen}>
        {
          customFilesData.map((item:any,index)=>{
            return <div className="other-files" key={item.value}>
              <span>{item.label}：{item.value}</span>
                {
                  index === 0 && customFilesData.length !== 1 ?
                  <span><EKBIcon className={'toggle'} name="#EDico-up-default"/></span>
                  : null
                }
            </div>
          })
        }
      </div>
    :<></>
  )
}

const formatCardNo = (cardNo: string) => {
  if (/[^0-9]/.test(cardNo)) {
    return cardNo
  }
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1 ')
}
