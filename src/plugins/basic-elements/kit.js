/**************************************************
 * Created by nanyuanting<PERSON> on 02/10/2016 22:14.
 **************************************************/
import { cloneDeep as clone } from 'lodash'
import UUID from '../../lib/uuid'

export function fixArray4Picker(data, bool = true) {
  let map = {}
  data = clone(data)
  data = data.map(line => {
    if (bool) {
      line.value = line.id
      line.label = line.name
    }
    map[line.id] = line
    return line
  })
  return { data, map }
}

/**
 * @param  array.$.id
 */
export function array2map(array = []) {
  array = clone(array)
  let map = {}
  array.forEach(line => {
    map[line.id] = line
  })
  return map
}

export function genFlowPlanNodeId() {
  return UUID()
}

export function recursion4FixTreeName(tree) {
  const name = tree.name
  const children = tree.children || []
  children.map(line => {
    line.label = (name && name + '/') + line.name
    return recursion4FixTreeName(line)
  })
  return tree
}

export function recursionSearch(tree, val = null) {
  const ret = []
  const children = tree.children || []
  const label = tree.label || ''

  if (label && !!~label.indexOf(val)) {
    ret.push(tree)
  }

  children.forEach(line => {
    ret.push.apply(ret, recursionSearch(line, val))
  })

  return ret
}
