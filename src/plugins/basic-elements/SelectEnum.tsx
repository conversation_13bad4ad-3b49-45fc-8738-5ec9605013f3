import React, { useEffect, useState } from 'react'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager-mobile'
import { List, SearchBar, SkeletonParagraph } from '@hose/eui-mobile'
import { IllustrationMiddleNoContent, OutlinedTipsClose } from '@hose/eui-icons'
import Highlighter from 'react-highlight-words'
import { app } from '@ekuaibao/whispered'
import { formatLang, debounce } from '../../lib/util'
import './SelectEnum.less'
interface IEnum {
  pipeline: number
  grayver: string
  dbVersion: number
  threadId: string
  id: string
  version: number
  active: boolean
  createTime: number
  updateTime: number
  name: string
  nameSpell: string
  code: string
  corporationId: string
  sourceCorporationId: null
  dataCorporationId: null
  parentId: string
  enumCode: string
  required: boolean
  orders: number
  enName: string
  jpName: string
  children: any[]
}

export interface ISelectEnumProps extends ILayerProps {
  data: IEnum[]
  popupTitle: string
  params: Record<string, any>
}

const SelectEnum: React.FC<ISelectEnumProps> = props => {
  const { data, layer, popupTitle = i18n.get('请选择'), params } = props
  const [searchText, setSearchText] = useState('')
  const [datasource, setDatasource] = useState<IEnum[]>(data || [])
  const [showData, setShowData] = useState<IEnum[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!data.length) {
      setLoading(true)
      app
        .invokeService('@common:get:property:by:name', params)
        .then((res: any) => {
          const data = res.items.filter((item: IEnum) => item.active)
          setDatasource(data)
          setShowData(data)
          setLoading(false)
        })
        .catch(() => {
          setLoading(false)
        })
    }
  }, [])

  const fnSearchData = (searchText: string): IEnum[] => {
    if (!searchText) {
      return datasource
    }
    return datasource.filter(item => {
      const name = ((item as any)[formatLang()] || item.name).toLowerCase()
      const code = (item.code ?? '').toLowerCase()
      const nameSpell = (item.nameSpell ?? '').toLowerCase()
      const valueLower = searchText.toLowerCase()
      return name.includes(valueLower) || code.includes(valueLower) || nameSpell.includes(valueLower)
    })
  }

  const handleSearchValueChange = debounce((search: string) => {
    const searchData = fnSearchData(search)
    setShowData(searchData)
    setSearchText(search)
  }, 400)

  const handleSearchSubmit = debounce(() => {
    const searchData = fnSearchData(searchText)
    setShowData(searchData)
  }, 400)

  const handleSearchCancel = () => {
    setSearchText('')
    setShowData(datasource)
  }

  const handleItemClick = (item: IEnum) => {
    layer.emitOk(item)
  }

  const handleCancel = () => {
    layer.emitCancel()
  }

  const renderHeader = () => {
    return (
      <div className="select-enum-header">
        <OutlinedTipsClose fontSize={24} color="var(--eui-icon-n1)" onClick={handleCancel} />
        <div className="select-enum-header-title-wrapper">
          <div className="select-enum-header-title">{popupTitle}</div>
        </div>
      </div>
    )
  }

  const renderSearch = () => {
    return (
      <div className="select-enum-search">
        <SearchBar
          placeholder={i18n.get('请搜索名称、编码')}
          clearOnCancel={false}
          onSearch={handleSearchSubmit}
          onChange={handleSearchValueChange}
          onCancel={handleSearchCancel}
        />
      </div>
    )
  }

  const renderEmpty = () => {
    return (
      <div className="select-enum-empty">
        <IllustrationMiddleNoContent fontSize={200} />
        <div className="select-enum-empty-text">{i18n.get('暂无数据')}</div>
      </div>
    )
  }

  const renderLoading = () => {
    return (
      <div className="select-enum-loading">
        {Array.from({ length: 8 }).map((_, index) => (
          <SkeletonParagraph key={index} className="skeleton-paragraph-item" lineCount={2} animated />
        ))}
      </div>
    )
  }

  const renderContent = () => {
    if (showData.length === 0) {
      return renderEmpty()
    }
    return (
      <List className="select-enum-list" hideBorderTop>
        {showData.map(item => {
          let name = (item as any)[formatLang()] || item.name
          if (!!searchText.length) {
            name = `${name}(${item.code})`
          }
          return (
            <List.Item
              key={item.id}
              className="select-enum-list-item"
              arrow={false}
              onClick={() => handleItemClick(item)}
            >
              <Highlighter
                highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                searchWords={[searchText]}
                textToHighlight={name}
              />
            </List.Item>
          )
        })}
      </List>
    )
  }

  return (
    <div className="select-enum-wrapper">
      {renderHeader()}
      {renderSearch()}
      {loading ? renderLoading() : renderContent()}
    </div>
  )
}

export default SelectEnum
