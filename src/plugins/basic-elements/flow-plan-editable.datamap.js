/**************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 02/10/2016 16:24.
 **************************************************/
import cloneDeep from 'lodash/cloneDeep'
import * as BasicStateConst from './basic-state-const'

const EXPENSE_STATE = BasicStateConst.EXPENSE_STATE
const PLAN_STATE = BasicStateConst.PLAN_STATE
const COUNTERSIGN_POLICY = BasicStateConst.COUNTERSIGN_POLICY
const NORMMAL_AGREE = BasicStateConst.COUNTERSIGN_AGREE
import get from 'lodash/get'

class FlowPlanEditableDataMap {
  constructor(props, flowData) {
    this.flow = cloneDeep(flowData.flowId || flowData) //兼容  flow 和 backlog//TODO
    this.flowState = this.flow.state
    this.flowStateLabel = EXPENSE_STATE[this.flowState].label
    this.plan = cloneDeep(flowData.plan || flowData.flowId.plan)
    this.fixNodes4UI()
  }

  getFlowLogs() {
    let logs = this.flow.logs || []
    return logs.filter(line => line.action !== 'freeflow.comment')
  }

  fnHandleState(plan) {
    plan = plan || {}
    plan.nodes = plan.nodes || []
    let { logs } = this.flow
    let log = logs.length > 0 ? logs[logs.length - 1] : {}
    let isRetract = log.action === 'freeflow.retract'
    const isNullify = log.action === 'freeflow.nullify'
    /**
     * stateEx:
     * 0  : 审批中
     * 1  : 通过
     * 2  : 未处理
     * 3  : 出纳支付完成
     * 4  : 单节点流程：审核中
     * 5  : 单节点流程：已支付
     * 6  : 单节点流程：自己审批自己
     * -1 : 驳回
     * -2 : 单节点流程：驳回
     * -3 : 撤回
     */
    // 单节点流程截获
    if (plan.nodes.length === 1) {
      switch (plan.taskId) {
        case 'SUBMIT':
          plan.nodes[0].stateEx = -2
          break // 驳回
        case null:
          plan.nodes[0].stateEx = 5
          break // 已支付
        default:
          plan.nodes[0].stateEx = 4
      }
      if (plan.nodes[0].stateEx === 4 && plan.ownerId === plan.nodes[0].approverId) {
        //判断审批人是不是本人
        plan.nodes[0].stateEx = 6
      }
      return plan
    }

    let taskId = plan.taskId
    let prevId = plan.prevId

    plan.nodes.reduce(
      (prev, next) => {
        // 审批流程
        if (prevId === 'SUBMIT') {
          if (next.id === taskId) {
            prev.stateEx = 1
            next.stateEx = 0
            return next
          }
          if (prev.id === taskId) {
            next.stateEx = 2
            return next
          }
          if (prev.stateEx === 0 || prev.stateEx === 2) {
            next.stateEx = 2
            return next
          }
          prev.stateEx = 1
          return next
        }

        // 已支付的审批流程
        if (taskId === null) {
          prev.stateEx = 1
          next.stateEx = 1
          if (next.id === prevId || next.skippedType === 'NO_ABILITY' || next.skippedType === 'PAY_AMOUNT_IS_0') {
            next.stateEx = 3
          }
          return next
        }

        // 驳回的审批流程
        if (taskId === 'SUBMIT') {
          if (next.id === prevId) {
            next.stateEx = isRetract ? -3 : isNullify ? -4 : -1
            prev.stateEx = 1
            return next
          }
          if (prev.stateEx === -1 || prev.stateEx === -3 || prev.stateEx === 2 || prev.stateEx === -4) {
            next.stateEx = 2
            return next
          }
          prev.stateEx = 1
          return next
        }

        /**
         * taskId和PrevId都是id时：
         */
        if (next.id === taskId) {
          prev.stateEx = 1
          next.stateEx = 0
          return next
        }
        if (prev.stateEx === 0 || prev.stateEx === 2) {
          next.stateEx = 2
          return next
        }
        prev.stateEx = 1

        return next
      },
      { id: 'SUBMIT' }
    )
    return plan
  }

  getNodeLabel(stateCode) {
    return PLAN_STATE[String(stateCode)]
  }

  getCountersignNodeType(policy) {
    return COUNTERSIGN_POLICY[policy]
  }

  getNormalAgree(line) {
    const approveMsg = {
      REJECT_FORCE_APPROVALS: '驳回',
      ROLLBACK_FORCE_APPROVALS: '回退',
      EDIT_FORCE_APPROVALS: '修改'
    }
    const msg = approveMsg[line.agreeType]
    if (!!msg) {
      return i18n.get(`因单据被${msg}，此节点需再次审批`)
    }
    return NORMMAL_AGREE[line.agreeType]
  }

  fixNodes4UI() {
    this.plan = this.fnHandleState(this.plan)
    this.plan.nodes = this.plan.nodes.map(line => {
      if (!line.staff) {
        line.staff = {}
      }
      return line
    })
  }

  getPlan() {
    let flowNodes = cloneDeep(this.plan)
    flowNodes.nodes = flowNodes.nodes.filter(
      line => get(line, 'ebotConfig.type') !== 'costControlCheck' && get(line, 'config.isNeedCashierNode', true)
    )
    return flowNodes
  }

  updateNode4UI(id, node) {
    this.plan.nodes = this.plan.nodes.map(line => {
      if (line.id === id) {
        line.staff.avatar = node.avatar
        line.staff.name = node.name
        line.approverId = node.id
      }
      return line
    })
    return this
  }

  getCounterSignerState(node) {
    let counterLabel
    let info = this.getCounterSignerInfo(node.counterSigners)
    let { rejectIndex, agreeIndex } = info
    let allCounters = node.counterSigners && node.counterSigners.length
    let stateEx = this.getNodeLabel(node.stateEx)
    switch (node.policy) {
      case 'ALL': //所有审批人都同意才算会签通过
        if (rejectIndex === 0) {
          if (agreeIndex === allCounters) {
            counterLabel = node.stateEx === 1 ? i18n.get('全部同意') : stateEx.label
          } else {
            if (node.stateEx === 0 || node.stateEx === 1) {
              counterLabel = stateEx.label + (agreeIndex + '/' + allCounters)
            } else {
              counterLabel = stateEx.label
            }
          }
        }
        break
      case 'ANY': //任意审批人同意即认为会签通过
        counterLabel = stateEx.label
        break
    }
    return counterLabel
  }

  getCounterSignerInfo(counterSigners) {
    let agreeIndex = 0
    let rejectIndex = 0
    counterSigners &&
      counterSigners.forEach(line => {
        if (line.state === 'PROCESSED') {
          agreeIndex++
        } else if (line.state === 'REJECT') {
          rejectIndex++
        }
      })
    return { agreeIndex, rejectIndex }
  }
}

export default FlowPlanEditableDataMap
