import React from 'react'
import styles from './add-payee.module.less'
import classNames from 'classnames'
import { app, app as api } from '@ekuaibao/whispered'
import { Checkbox, List, Picker, Icon } from 'antd-mobile'
import { Button, Dialog, TextArea, Toast } from '@hose/eui-mobile'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from '../basic-elements/enhance-title-hook'
const BasicComponent = app.require('@elements/enhance/basic-component')
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
const InputWrapper = app.require('@elements/form/input-wrapper')
import { getFormItemsArrHSBC } from './basic-state-const'
import { getBankSortArr, accountChannelNameMap } from '../../lib/enums'
import { toast, hideKeyboard, getFormItemsCityCode } from '../../lib/util'
import { cloneDeep, compact } from 'lodash'
import { getV } from '@ekuaibao/lib/lib/help'
import AddPayeeItem from './AddPayeeItem'
import { Fetch } from '@ekuaibao/fetch'
import { OutlinedEditCopy, OutlinedTipsMaybe } from '@hose/eui-icons'
const SVG_SAVE_O = app.require('@images/save.svg')
const SVG_ARROW_RIGHT = app.require('@images/menu-arrow-right.svg')

const FORM_TYPE = {
  create: '1',
  edit: '2'
}

const PERSONAL = 'PERSONAL'

const titleMap = {
  PERSONAL: '您可以在对应银行APP的账户管理中查找开户网点，或咨询银行客户经理',
  PUBLIC: '您可以在对应的合同中查询此信息'
}

@EnhanceConnect(state => ({
  bankList: state['@common'].bankList,
  provinceList: state['@common'].provinceList,
  branchList: state['@common'].branchByCityIdList,
  cityList: state['@common'].cityList,
  certificateTypeList: state['@common'].CertificateTypeList,
  CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
  ALIPAY: state['@common'].powers.ALIPAY,
  unionBankList: state['@common'].unionBankList,
  userInfo: state['@common'].me_info,
  payeeConfig: state['@common'].payeeConfig,
  payeeConfigCheck: state['@bill'].payeeConfigCheck,
  KA_FOREIGN_ACCOUNT: state['@common'].powers.KA_FOREIGN_ACCOUNT,
  searchCountries: state['@common'].searchCountries.items,
  searchCities: state['@common'].searchCities.items,
  globalFields: state['@common'].baseDataProperties.data
}))
@EnhanceTitleHook(props => props.title)
@EnhanceFormCreate()
export default class AddPayee extends BasicComponent {
  constructor(props) {
    super(props)
    this.currentBank = undefined
    this.currentCity = undefined
    this.bankLinkNo = undefined
    this.branchId = undefined
    const { CHANGJIEPay, payeeConfig, payeeConfigCheck = {}, data, form } = props
    const type = data?.type ? data.type : payeeConfigCheck.personalAccount ? 'PERSONAL' : 'PUBLIC'
    const accountConfig =
      type === 'PERSONAL'
        ? getV(payeeConfig, 'personalAccountConfig', {})
        : getV(payeeConfig, 'publicAccountConfig', {})
    const accountSort = getV(accountConfig, 'accountSort')
    this.bankSortArr = accountSort
      ? getBankSortArr().filter(item => accountSort.includes(item.value))
      : getBankSortArr()
    if (window.IS_SMG) {
      this.bankSortArr = this.bankSortArr && this.bankSortArr.filter(line => line.value !== 'ALIPAY')
    }
    const allowConcisionType = accountConfig.conciseInput && CHANGJIEPay
    const segmentActiveKey = data?.sort
      ? [data?.sort]
      : [(this.bankSortArr.length && this.bankSortArr[0].value) || 'BANK']
    const isForbiddenAdd = type === 'PERSONAL' && accountConfig.forbiddenAdd
    const isRequired = accountConfig?.networkNotRequired || false // 网点是否必填
    this.bankLinkNo = data?.bankLinkNo
    this.branchId = data?.branchId?.id
    if (data && form) {
      const formData = {
        accountName: data.accountName,
        accountNo: data.accountNo,
        branch: data?.branchId?.name || data.branch,
        certificateNo: data.certificateNo,
        certificateType: [data.certificateType],
        remark: data.remark,
        sort: [data.sort],
        ...data?.customFields
      }
      Object.keys(formData).forEach(key => {
        form.getFieldProps(key)
      })
      form.setFieldsValue(formData)
    }

    this.state = {
      type: data ? FORM_TYPE.edit : FORM_TYPE.create,
      formData: { type, owner: 'INDIVIDUAL' },
      bankData: {},
      branchData: data ? data?.branchId?.name || data.branch : '',
      branchFlag: false,
      areaList: [],
      areaValue: [],
      cols: 1,
      // 添加收款信息模式
      isRegularType: true,
      currentUnionBank: {},
      segmentActiveKey,
      allowConcisionType,
      allowCreate: payeeConfig.allowCreate,
      isForbiddenAdd,
      isRequired,
      cityCodeData: ['090'],
      extraTextFields: [],
      showIntelligentFilling: false,
      intelligentFillingValue: ''
    }
    this.initBindProps = this.initializeBindPropsWithChangeHandler(this.state.formData, this.handleFormChange)
    props.overrideGetResult(this.getResult)
  }

  componentWillMount() {
    this._$UUID = String(Math.random())
    api.backControl.create(this._$UUID, () => {
      this.backHookCallback()
    })
  }

  backHookCallback = () => {
    Dialog.confirm({
      content: i18n.get('确定退出此次编辑吗？'),
      closeOnAction: true,
      confirmText: i18n.get('退出'),
      onConfirm: () => {
        api.backControl.remove(this._$UUID)
        api.backControl.invoke()
      }
    })
  }

  componentWillUnmount() {
    api.backControl.remove(this._$UUID)
  }

  componentDidMount() {
    const { KA_FOREIGN_ACCOUNT } = this.props
    api.invokeService('@layout:set:header:title', this.props.title)
    api.invokeService('@common:get:banks', { start: 0, count: 50 })
    api.invokeService('@common:get:bank:provinces')
    api.invokeService('@common:get:unionBanks')
    api.invokeService('@bill:get:payee:config:check')
    if (KA_FOREIGN_ACCOUNT) {
      api.dataLoader('@common.searchCountries').load()
    }
    this.initPayeeExtraTextFields(this.state.formData.type)
  }

  componentWillReceiveProps(nextProps) {
    const { payeeConfigCheck = {} } = this.props
    const diff =
      nextProps.payeeConfigCheck.personalAccount != payeeConfigCheck.personalAccount ||
      nextProps.payeeConfigCheck.publicAccount != payeeConfigCheck.publicAccount
    if (diff) {
      const isPersonalHaveCreate = nextProps.payeeConfigCheck.personalAccount
      const type = isPersonalHaveCreate ? 'PERSONAL' : 'PUBLIC'
      this.handleTypeChange(type)
    }
  }

  initPayeeExtraTextFields(type = PERSONAL) {
    const { payeeConfig = {}, globalFields } = this.props
    const accountConfig = type == PERSONAL ? payeeConfig.personalAccountConfig : payeeConfig.publicAccountConfig
    const customFields = accountConfig.customFields ?? []

    if (customFields) {
      // 额外字符串赋值
      this.setState({
        extraTextFields: compact(
          customFields.map(item => {
            return globalFields.find(v => v.name == item)
          })
        )
      })
    }
  }

  getResult = async () => {
    const { payeeConfigCheck = {}, form, KA_FOREIGN_ACCOUNT, data: editTargetData } = this.props
    const { formData, segmentActiveKey, extraTextFields } = this.state
    let data = { ...formData, ...form.getFieldsValue() }
    const customFields = {}
    if (
      (segmentActiveKey.includes('OVERSEABANK') ||
        segmentActiveKey.includes('BANK') ||
        segmentActiveKey.includes('ACCEPTANCEBILL')) &&
      KA_FOREIGN_ACCOUNT
    ) {
      const extensions = {
        bankCountry: this.state['extensions.bankCountry'] || null,
        bankCountryStr: this.state['extensions.bankCountryStr'] || null,
        bankCountry_shortCode: this.state['extensions.bankCountry_shortCode'] || null,
        city: this.state['extensions.city'] || null,
        cityEn: this.state['extensions.cityEn'] || null,
        cityEnStr: this.state['extensions.cityEnStr'] || null,
        cityStr: this.state['extensions.cityStr'] || null,
        country: this.state['extensions.country'] || null,
        countryEn: this.state['extensions.countryEn'] || null,
        countryEnStr: this.state['extensions.countryEnStr'] || null,
        countryStr: this.state['extensions.countryStr'] || null,
        numCode: this.state['extensions.numCode'] || null,
        shortCode: this.state['extensions.shortCode'] || null
      }
      data.extensions = { ...data.extensions, ...extensions }
    }
    data = this.fnFormatData(data)
    data.accountNo = data.accountNo && data.accountNo.replace(/\s/g, '')
    extraTextFields.forEach(item => {
      customFields[item.name] = data[item.name]
      delete data[item.name]
    })
    data['customFields'] = customFields

    if (!editTargetData) {
      return api.invokeService('@common:add:payee', data)
    } else {
      data.id = editTargetData.id
      return api.invokeService('@common:edit:payee', data)
    }
  }

  fnFormatData(data) {
    const { payeeConfig, userInfo = {} } = this.props
    const { formData } = this.state
    let bankData = cloneDeep(data)
    if (bankData.certificateType) {
      bankData.certificateType = bankData.certificateType[0] || ''
    }
    if (this.bankLinkNo) {
      bankData.bankLinkNo = this.bankLinkNo
    }
    if (this.branchId) {
      bankData.branchId = this.branchId
    }
    if (bankData?.nationCode) {
      bankData.nationCode = bankData?.nationCode?.[0]
    }
    const accountConfig =
      formData.type === 'PERSONAL'
        ? getV(payeeConfig, 'personalAccountConfig')
        : getV(payeeConfig, 'publicAccountConfig')
    return {
      asPayee: true,
      visibility: {
        roles: null,
        staffs: [`${userInfo?.staff?.id}`],
        departments: null,
        fullVisible: getV(accountConfig, 'createAccount.visible', false),
        departmentsIncludeChildren: false
      },
      ...bankData,
      sort: bankData.sort[0]
    }
  }

  handleSubmit = () => {
    return this.props.form.validateFields((error, values) => {
      if (error) return
      const result = this.getResult().catch(err => toast.fail(err?.errorMessage || i18n.get('保存失败')))
      this.props.layer.emitOk(result)
      Toast.show({
        icon: 'success',
        content: '收款账户添加成功'
      })
    })
  }

  handleTypeChange = type => {
    const { payeeConfig, CHANGJIEPay } = this.props
    let { formData } = this.state
    formData = { ...formData, type }
    const accountConfig =
      type === 'PERSONAL'
        ? getV(payeeConfig, 'personalAccountConfig', {})
        : getV(payeeConfig, 'publicAccountConfig', {})
    this.bankSortArr = accountConfig.accountSort
      ? getBankSortArr().filter(item => accountConfig.accountSort.includes(item.value))
      : getBankSortArr()
    const segmentActiveKey = [(this.bankSortArr.length && this.bankSortArr[0].value) || 'BANK']
    const allowConcisionType = accountConfig.conciseInput && CHANGJIEPay
    const isForbiddenAdd = type === 'PERSONAL' && accountConfig.forbiddenAdd
    const isRequired = accountConfig?.networkNotRequired || false // 网点是否必填
    this.setState({ formData, allowConcisionType, segmentActiveKey, isForbiddenAdd, isRequired })
    this.props.form &&
      this.props.form.setFieldsValue({
        certificateType: '',
        certificateNo: '',
        sort: segmentActiveKey,
        accountName: ''
      })
    this.initPayeeExtraTextFields(type)
  }

  handleFormChange = (key, value) => {
    let { formData } = this.state
    formData = { ...formData, [key]: value }
    this.setState({ formData })
  }

  handleSelectBankBranch = () => {
    // 有空指针异常的风险
    hideKeyboard()
    api
      .open('@basic:SelectBankBranch', {
        data: {
          branchData: this.state.branchData,
          currentCity: this.currentCity
        }
      })
      .then(result => {
        let cBankData = this.state.bankData
        this.props.form.setFieldsValue({ branch: result.name })
        if (result) {
          this.bankLinkNo = result?.code
          this.branchId = result?.id
          this.props.form.setFieldsValue({ bank: result.bankName })
          cBankData = { ...cBankData, name: result.bankName, icon: result.bankIcon }
        }
        this.currentBank = cBankData
        this.setState({ bankData: cBankData, branchData: result.name, branchFlag: result.name === i18n.get('其他') })
      })
  }

  checkNumber(rule, value, callback) {
    const { segmentActiveKey } = this.state
    if (segmentActiveKey[0] === 'ALIPAY' || segmentActiveKey[0] === 'OTHER' || segmentActiveKey[0] === 'WEIXIN') {
      // if (value) {
      //   if (!/^(?:1[3-9]\d{9}|[a-zA-Z\d._-]*\@[a-zA-Z\d.-]{1,20}\.[a-zA-Z\d]{1,20})$/.test(value.replace(/\s/g, ''))) {
      //     return callback(i18n.get('支付宝账号格式不正确'))
      //   }
      // }
    } else {
      if (value) {
        if (!/^[0-9a-zA-Z-]*$/.test(value.replace(/\s/g, ''))) return callback(i18n.get('银行账号格式不正确'))
      }
    }
    callback()
  }

  checkcertificateNo = (rule, value, callback) => {
    const cer = this.props.form.getFieldValue('certificateType')
    const certificateType = cer && cer[0]
    if (certificateType === '01' && value) {
      if (!value.length) return callback(i18n.get('身份证号不能为空'))
      if (value.length > 18) return callback(i18n.get('身份证号不能超过18位'))
      if (!/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
        return callback(i18n.get('身份证号格式不正确'))
      }
    }
    callback()
  }

  checkRoutingNumber = (rule, value, callback) => {
    if (!!value && !/^[0-9]+$/.test(value)) {
      return callback(i18n.get('汇款路线号码必须是数字'))
    }
    callback()
  }

  checkRemark = (rule, value, callback) => {
    if (!!value && /\n/.test(value)) {
      const newValue = value.replace(/\n/g, '')
      this.props.form.setFieldsValue({ remark: newValue })
    }
    callback()
  }

  handleCertificateTypeChange = value => {
    // 证件类型为空，将证件号重置
    if (value.toString() === '') {
      this.props.form.resetFields(['certificateNo'])
    }
    this.forceUpdate() //为了强制更新form,刷新CertificateNo的require
  }

  handleSelectNewBank = () => {
    hideKeyboard()
    api
      .open('@basic:SelectNewBank', {
        bankList: this.props.unionBankList
      })
      .then(res => {
        this.setState({ currentUnionBank: res, canClear: true })
      })
  }

  handleClearUnionBank = e => {
    if (e.cancelable) {
      e.preventDefault && e.preventDefault()
    }
    e.stopPropagation && e.stopPropagation()
    e.stopImmediatePropagation && e.stopImmediatePropagation()
    this.setState({ currentUnionBank: {}, canClear: false })
  }

  renderUnionBanks = () => {
    const { form } = this.props
    const { currentUnionBank, canClear } = this.state
    const error = form.getFieldError('unionBank')
    return (
      <AddPayeeItem label={i18n.get('开户行')} require>
        <div
          className={'bank am-list-item am-input-item'}
          onClick={this.handleSelectNewBank}
          {...form.getFieldProps('unionBank', {
            initialValue: currentUnionBank.name || '',
            rules: [{ required: true }]
          })}
        >
          {/*<div className={'bank-label am-input-label am-input-label-5'}>{i18n.get('开户行')}</div>*/}
          {!currentUnionBank.name && <span className="thePlaceHolder">{i18n.get('请选择开户行')}</span>}
          <div className="branch-name">{currentUnionBank.name || ''}</div>
          {error && <div className="text-error" />}
          {canClear ? (
            <div className="branch-clear" onClick={this.handleClearUnionBank}>
              <Icon type="cross-circle" size="xs" />
            </div>
          ) : (
            <img className="img-arrow" src={SVG_ARROW_RIGHT} />
          )}
        </div>
      </AddPayeeItem>
    )
  }

  handleAccountChange = value => {
    const { setFieldsValue } = this.props.form
    setFieldsValue({ accountNo: '' })
    this.setState({ segmentActiveKey: value })
  }

  handleIconClick = () => {
    const { formData } = this.state
    const title = titleMap?.[formData?.type]
    return toast.info(i18n.get(title))
  }

  renderBranchLabel = () => {
    return (
      <>
        {i18n.get('开户网点')}
        <OutlinedTipsMaybe onClick={this.handleIconClick} style={{ marginLeft: 4 }} />
      </>
    )
  }

  renderBankBranch() {
    let form = this.props.form
    let error = form.getFieldError('branch')
    const { isRequired } = this.state
    return (
      <AddPayeeItem label={this.renderBranchLabel()} require={!isRequired}>
        <div
          className={'bank am-list-item am-input-item'}
          onClick={this.handleSelectBankBranch}
          {...form.getFieldProps('branch', {
            initialValue: '',
            rules: [{ required: !isRequired }]
          })}
        >
          {!this.state.branchData && <span className="thePlaceHolder">{i18n.get('请选择开户网点')}</span>}
          <div className="branch-name">{this.state.branchData}</div>
          {error && <div className="text-error" />}
          <img className="img-arrow" src={SVG_ARROW_RIGHT} />
        </div>
      </AddPayeeItem>
    )
  }

  renderHeader = () => {
    const { payeeConfigCheck = {} } = this.props
    const { formData } = this.state
    const isPersonalHaveCreate = payeeConfigCheck.personalAccount
    const isPublicHaveCreate = payeeConfigCheck.publicAccount
    const isDisabledPersonal = !isPersonalHaveCreate
    const isDisabledPublic = !isPublicHaveCreate
    return (
      <div className="header-wrapper">
        <div
          className="header-select-item"
          onClick={isDisabledPersonal ? null : this.handleTypeChange.bind(this, 'PERSONAL')}
        >
          <Checkbox disabled={isDisabledPersonal} checked={formData.type === 'PERSONAL'} />
          <div className={classNames({ 'disabled-label': isDisabledPersonal })}>{i18n.get('个人账户')}</div>
        </div>
        <div
          className="header-select-item"
          onClick={isDisabledPublic ? null : this.handleTypeChange.bind(this, 'PUBLIC')}
        >
          <Checkbox disabled={isDisabledPublic} checked={formData.type === 'PUBLIC'} />
          <div className={classNames({ 'disabled-label': isDisabledPublic })}>{i18n.get('对公账户')}</div>
        </div>
      </div>
    )
  }

  renderCertify = () => {
    const { form, certificateTypeList } = this.props
    const { formData } = this.state
    let list = certificateTypeList.map(v => ({
      value: v.code,
      label: i18n.get(v.name)
    }))
    if (formData.type === 'PUBLIC') {
      list = list.filter(item => item.value === '11' || item.value === '54')
      if (window.IS_SMG) {
        list = list && list.filter(item => item.value === '54')
      }
    }
    list = [{ value: '', label: i18n.get('无') }, ...list]
    const isSmg = window.IS_SMG && formData.type === 'PUBLIC'
    const certificateTypeValue = form.getFieldValue('certificateType') || ''
    const isDisabled = certificateTypeValue.toString() === ''
    return (
      <>
        <AddPayeeItem label={i18n.get('证件类型')} require={isSmg}>
          <Picker
            extra={<span className="placeholder-color">{i18n.get('请选择证件类型')}</span>}
            data={list}
            cols={1}
            {...form.getFieldProps('certificateType', {
              onChange: this.handleCertificateTypeChange,
              rules: [{ required: !isDisabled, message: i18n.get('请选择证件类型') }]
            })}
          >
            <List.Item className="select-area" arrow="horizontal" />
          </Picker>
        </AddPayeeItem>
        <AddPayeeItem label={i18n.get('证件号码')} labelDisabled={isDisabled} require={isSmg}>
          <InputWrapper
            form={form}
            name="certificateNo"
            disabled={isDisabled}
            placeholder={i18n.get('请输入证件号码')}
            {...form.getFieldProps('certificateNo', {
              rules: [
                // { required: !isDisabled, message: i18n.get('请输入账号') },、
                { max: 100, message: i18n.get('账号不能超过100个字') },
                { validator: this.checkcertificateNo }
              ]
            })}
          />
        </AddPayeeItem>
      </>
    )
  }

  fnHandleAlipayWarnningClick = text => {
    const { segmentActiveKey } = this.state
    if (text && segmentActiveKey[0] === 'ALIPAY') {
      toast.info(text)
    }
  }

  fnGetAlipayAccountNoWarnningText = value => {
    const { segmentActiveKey } = this.state
    if (
      value &&
      segmentActiveKey[0] === 'ALIPAY' &&
      !/^(?:1[3-9]\d{9}|[a-zA-Z\d._-]*\@[a-zA-Z\d.-]{1,20}\.[a-zA-Z\d]{1,20})$/.test(value.replace(/\s/g, ''))
    ) {
      return i18n.get('支付宝账户可能存在错误')
    }
    return ''
  }

  handleValidator = (rule, value, callback) => {
    const { userInfo } = this.props
    if (!value) {
      callback()
    }
    if (value != userInfo.staff.name) {
      callback(i18n.get('开户名称与当前用户姓名不一致！'))
    }
    callback()
  }

  checkEn = (rule, value, callback) => {
    if (value) {
      if (value.match(/[\u4e00-\u9fa5]/)) return callback(i18n.get('Please enter in English'))
    }
    callback()
  }

  checkEmail = (rule, value, callback) => {
    let reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
    if (value && !reg.test(value)) {
      return callback(i18n.get('请输入正确格式的邮箱地址'))
    }
    callback()
  }

  inputValidator = (el, rule, value, callback) => {
    if (el?.onlyEn) {
      this.checkEn(rule, value, callback)
    }
    if (el?.checkEmail) {
      this.checkEmail(rule, value, callback)
    }
    callback()
  }

  handleSelectCountryorCityBranch = (name, placeholder, label) => {
    // 有空指针异常的风险
    hideKeyboard()
    const { form, searchCountries, searchCities } = this.props
    const fieldNameMap = {
      'extensions.country': 'fullCname',
      'extensions.city': 'cnName',
      'extensions.countryEn': 'fullEname',
      'extensions.cityEn': 'name',
      'extensions.bankCountry': 'fullCname'
    }
    const fieldName = fieldNameMap[name]
    let data = {
      fieldName,
      placeholder,
      label
    }
    if (name.indexOf('city') >= 0) {
      data.searchList = searchCities
    } else {
      data.searchList = searchCountries
    }
    api.open('@basic:SelectCountryorCityBranch', { data }).then(result => {
      if (result) {
        const obj = {}
        if (name !== 'extensions.bankCountry') {
          const formItemMap = {
            'extensions.country': 'extensions.countryEn',
            'extensions.city': 'extensions.cityEn',
            'extensions.countryEn': 'extensions.country',
            'extensions.cityEn': 'extensions.city'
          }
          const labelname = formItemMap[name]
          //国家、城市 中英文联动
          if (labelname.includes('country')) {
            obj[labelname] = searchCountries.filter(item => item.id === result.id)[0]?.id
            obj['extensions.countryStr'] = searchCountries.filter(item => item.id === result.id)[0]?.fullCname
            obj['extensions.countryEnStr'] = searchCountries.filter(item => item.id === result.id)[0]?.fullEname
            obj['extensions.shortCode'] = searchCountries.filter(item => item.id === result.id)[0]?.shortCode
            obj['extensions.numCode'] = searchCountries.filter(item => item.id === result.id)[0]?.numCode
          } else {
            const selectedCity = searchCities.filter(item => item.id === result.id)
            obj[labelname] = selectedCity[0]?.id
            if (selectedCity[0]) {
              obj['extensions.cityStr'] = selectedCity[0]?.cnState
                ? `${selectedCity[0].cnState}-${selectedCity[0].cnName}`
                : selectedCity[0].cnName
              obj['extensions.cityEnStr'] = selectedCity[0]?.state
                ? `${selectedCity[0].state}-${selectedCity[0].name}`
                : selectedCity[0].name
            }
          }
          obj[name] = result.id
          form.setFieldsValue(obj)
          this.setState({
            ...obj
          })
          // 切换国家 清空城市
          if (name.includes('country')) {
            api.invokeService('@common:get:CityList', result.id)
            form.resetFields(['extensions.city', 'extensions.cityEn'])
            this.setState({
              'extensions.city': undefined,
              'extensions.cityEn': undefined
            })
          }
        } else {
          obj[name] = result.id
          obj['extensions.bankCountryStr'] = searchCountries.filter(item => item.id === result.id)[0]?.fullCname
          obj['extensions.bankCountry_shortCode'] = searchCountries.filter(item => item.id === result.id)[0]?.shortCode
          form.setFieldsValue(obj)
          this.setState({
            ...obj
          })
        }
      }
    })
  }

  handleCityCodeChange = cityCodeData => {
    this.setState({ cityCodeData })
  }

  handleAccountNoBlur = async () => {
    const { setFieldsValue, getFieldsValue } = this.props.form
    const values = await getFieldsValue()
    const { accountNo, sort } = values || {}
    const { formData } = this.state
    if (sort?.[0] === 'BANK' && formData?.type === 'PERSONAL' && accountNo?.length > 2) {
      const res = await Fetch.GET('/api/pay/v1/banks/branchByCardNo', { cardNo: accountNo })
      if (res?.value?.id) {
        let cBankData = this.state.bankData
        const data = res?.value || {}
        setFieldsValue({ branch: data?.name })
        this.bankLinkNo = data?.code
        this.branchId = data?.id
        cBankData = { ...cBankData, name: data?.bankName, icon: data?.bankIcon }
        this.currentBank = cBankData
        this.setState({ bankData: cBankData, branchData: data?.name, branchFlag: data?.name === i18n.get('其他') })
      }
    }
  }

  handleTextAreaChange = intelligentFillingValue => {
    this.setState({ intelligentFillingValue })
  }

  handleIntelligentFillingClick = showIntelligentFilling => {
    this.setState({ showIntelligentFilling })
  }

  handleShowTipClick = () => {
    Dialog.alert({
      content: (
        <div className={styles['dialog-wrap']}>
          <span>{i18n.get('户名：合小思')}</span>
          <span>{i18n.get('卡号：****************')}</span>
          <span>{i18n.get('开户行：招商银行双榆树支行')}</span>
        </div>
      )
    })
  }

  handleRecognitionClcik = () => {
    const { intelligentFillingValue } = this.state
    const { setFieldsValue } = this.props.form
    const _value = intelligentFillingValue.trim()
    const data = JSON.stringify(_value)
    const text = data.replace(/^"|"$/g, '')
    return Fetch.POST('/api/pay/v2/accounts/parsing', {}, { body: { text } })
      .then(res => {
        const { accountName, accountNo, branches } = res?.value || {}
        const data = branches?.[0] || {}
        setFieldsValue({ accountName, accountNo, branch: data?.name })
        toast.info('已填入可识别信息，请确认')
        if (data?.id) {
          let cBankData = this.state.bankData
          this.bankLinkNo = data?.code
          this.branchId = data?.id
          cBankData = { ...cBankData, name: data?.bankName, icon: data?.bankIcon }
          this.currentBank = cBankData
          this.setState({ bankData: cBankData, branchData: data?.name, branchFlag: data?.name === i18n.get('其他') })
        }
      })
      .catch(err => {
        toast.error(err?.errorMessage)
      })
  }

  renderOverseaFieldForHSBC = segmentActiveKey => {
    const { KA_FOREIGN_ACCOUNT, form } = this.props
    const formItemsArrHSBC = segmentActiveKey.includes('OVERSEABANK')
      ? getFormItemsArrHSBC()
      : getFormItemsArrHSBC().filter(item => item.name !== 'extensions.bankCountry')
    if (
      (segmentActiveKey.includes('OVERSEABANK') ||
        segmentActiveKey.includes('BANK') ||
        segmentActiveKey.includes('ACCEPTANCEBILL')) &&
      KA_FOREIGN_ACCOUNT
    ) {
      return (
        <>
          {formItemsArrHSBC.map(el => {
            let error = form.getFieldError(el.name)
            const label = el.payee ? `${el.payee}-${el.label}` : el.label
            const __k0 = el.payee ? `${i18n.get(el.payee)}-${i18n.get(el.label)}` : i18n.get(el.label)
            const placeholder = el.notChineseOREnglish
              ? i18n.get('请输入{__k0}', { __k0: __k0 })
              : el.onlyEn
              ? i18n.get(`请输入{__k0}_English`, { __k0: __k0 })
              : i18n.get(`请输入{__k0}_Chinese`, { __k0: __k0 })
            if (el.type === 'select') {
              let selectNameMap = {
                'extensions.country': 'extensions.countryStr',
                'extensions.city': 'extensions.cityStr',
                'extensions.countryEn': 'extensions.countryEnStr',
                'extensions.cityEn': 'extensions.cityEnStr',
                'extensions.bankCountry': 'extensions.bankCountryStr'
              }
              return (
                <AddPayeeItem key={el.name} label={label}>
                  <div
                    className={'bank am-list-item am-input-item'}
                    onClick={() => this.handleSelectCountryorCityBranch(el.name, placeholder, el.label)}
                    {...form.getFieldProps(el.name, {
                      initialValue: ''
                    })}
                  >
                    {!this.state[el.name] && <span className="thePlaceHolder">{placeholder}</span>}
                    <div className="branch-name">{this.state[selectNameMap[el.name]]}</div>
                    {error && <div className="text-error" />}
                    <img className="img-arrow" src={SVG_ARROW_RIGHT} />
                  </div>
                </AddPayeeItem>
              )
            } else {
              return (
                <AddPayeeItem key={el.name} label={label}>
                  <InputWrapper
                    form={form}
                    name={el.name}
                    placeholder={placeholder}
                    {...form.getFieldProps(el.name, {
                      rules: [
                        {
                          max: el.max,
                          message: i18n.get(el.label) + i18n.get('不能超过{__k0}个文字', { __k0: el.max })
                        },
                        { validator: this.inputValidator.bind(this, el) }
                      ]
                    })}
                  />
                </AddPayeeItem>
              )
            }
          })}
        </>
      )
    }
    return null
  }

  renderIntelligentFillingContent = () => {
    const { showIntelligentFilling, intelligentFillingValue } = this.state
    return showIntelligentFilling ? (
      <div className="intelligent-filling-content">
        <div className="text-area-content">
          <TextArea
            className="text-area"
            placeholder={i18n.get('在银行APP账户管理中查询卡片信息，复制粘贴，自动识别开户名称/银行卡号/开户网点')}
            value={intelligentFillingValue}
            onChange={this.handleTextAreaChange}
            maxLength={100}
            border
          />
          <div className="btns">
            <Button
              shape="rounded"
              category="secondary"
              style={{ marginRight: 8 }}
              onClick={() => this.handleIntelligentFillingClick(false)}
            >
              {i18n.get('取消')}
            </Button>
            <Button
              onClick={this.handleRecognitionClcik}
              disabled={!intelligentFillingValue}
              shape="rounded"
              category="secondary"
              theme="highlight"
            >
              {i18n.get('识别')}
            </Button>
          </div>
        </div>
        <div className="tips">
          {i18n.get('标准格式更快识别')}
          <span className="blue" onClick={this.handleShowTipClick}>
            {i18n.get('查看示例')}
          </span>
        </div>
      </div>
    ) : (
      <Button
        block
        size="small"
        category="secondary"
        className="intelligent-filling-btn"
        icon={<OutlinedEditCopy />}
        onClick={() => this.handleIntelligentFillingClick(true)}
      >
        {i18n.get('智能填写')}
      </Button>
    )
  }

  renderList = () => {
    const { form } = this.props
    const { segmentActiveKey, allowConcisionType, extraTextFields } = this.state
    const isOther = segmentActiveKey[0] === 'OTHER' || segmentActiveKey[0] === 'WEIXIN'
    const accountChannelNameMapEnum = accountChannelNameMap()
    const accountNoPlaceHolder =
      segmentActiveKey[0] === 'ALIPAY'
        ? i18n.get('请输入与开户名称相匹配的支付宝账号')
        : i18n.get(`请输入{__k0}`, { __k0: accountChannelNameMapEnum[segmentActiveKey[0]] })
    return (
      <List className="list-wrapper">
        {this.renderHeader()}
        <AddPayeeItem label={i18n.get('账户类别')} require>
          <Picker
            extra={<span className="placeholder-color">{i18n.get('请选择账户类别')}</span>}
            data={this.bankSortArr}
            cols={1}
            {...form.getFieldProps('sort', {
              initialValue: segmentActiveKey,
              rules: [{ required: true, message: i18n.get('请选择账户类别') }],
              onChange: this.handleAccountChange
            })}
          >
            <List.Item className="select-area" arrow="horizontal">
              {form.getFieldError('sort') && <div className="text-error" />}
            </List.Item>
          </Picker>
        </AddPayeeItem>
        {segmentActiveKey[0] === 'BANK' && this.renderIntelligentFillingContent()}
        <AddPayeeItem label={i18n.get('开户名称')} require>
          <InputWrapper
            form={form}
            name={'accountName'}
            placeholder={i18n.get('请输入真实姓名、公司名或组织机构名称')}
            {...form.getFieldProps('accountName', {
              rules: [
                { required: true, message: i18n.get('请输入真实姓名、公司名或组织机构名称') },
                { max: 100, message: i18n.get('户名不能超过100个字') },
                { validator: this.state.isForbiddenAdd ? this.handleValidator.bind(this) : null }
              ]
            })}
          />
        </AddPayeeItem>
        <AddPayeeItem label={accountChannelNameMapEnum[segmentActiveKey[0]]} require={!isOther}>
          <InputWrapper
            form={form}
            name={'accountNo'}
            placeholder={accountNoPlaceHolder}
            fnGetWarnningText={this.fnGetAlipayAccountNoWarnningText}
            fnHandleWarnningClick={this.fnHandleAlipayWarnningClick}
            onBlur={this.handleAccountNoBlur}
            {...form.getFieldProps('accountNo', {
              rules: [
                {
                  required: !isOther,
                  message: i18n.get(`请输入{__k0}`, { __k0: accountChannelNameMapEnum[segmentActiveKey[0]] })
                },
                { max: 100, message: i18n.get('账号不能超过100个字') },
                { validator: this.checkNumber.bind(this) }
              ]
            })}
          />
        </AddPayeeItem>
        {this.renderSortItems(allowConcisionType)}
        {this.renderCertify()}
        {this.renderOverseaFieldForHSBC(segmentActiveKey)}
        <AddPayeeItem label={i18n.get('备注信息')}>
          <InputWrapper
            form={form}
            name={'remark'}
            rows={2}
            placeholder={i18n.get('请输入{__k0}', { __k0: i18n.get('备注信息') })}
            {...form.getFieldProps('remark', {
              rules: [
                {
                  required: false,
                  message: i18n.get('请输入{__k0}', { __k0: i18n.get('备注信息') })
                },
                { validator: this.checkRemark },
                { max: 140, message: i18n.get('备注信息不能超过140个字') }
              ]
            })}
          />
        </AddPayeeItem>
        {extraTextFields.map(field => (
          <AddPayeeItem label={i18n.get(field.label)} key={field.name}>
            <InputWrapper
              form={form}
              name={field.name}
              placeholder={i18n.get('请输入{__k0}', { __k0: i18n.get(field.label) })}
              {...form.getFieldProps(field.name, {
                rules: [
                  { required: false, message: i18n.get('请输入{__k0}', { __k0: i18n.get(field.name) }) },
                  { max: 50, message: i18n.get(`${field.label}不能超过50个字`) }
                ]
              })}
            />
          </AddPayeeItem>
        ))}
      </List>
    )
  }

  renderOverseaBank = () => {
    const { form, KA_FOREIGN_ACCOUNT } = this.props
    const { cityCodeData } = this.state
    const cityCodeList = getFormItemsCityCode()
    return (
      <>
        <AddPayeeItem label={i18n.get('银行名称')} require={KA_FOREIGN_ACCOUNT}>
          <InputWrapper
            form={form}
            name="bankName"
            placeholder={i18n.get('请输入银行名称')}
            {...form.getFieldProps('bankName', {
              rules: [
                { max: 140, message: i18n.get('账号不能超过140个字') },
                {
                  required: KA_FOREIGN_ACCOUNT,
                  whitespace: true,
                  message: i18n.get('请输入银行名称')
                }
              ]
            })}
          />
        </AddPayeeItem>
        <AddPayeeItem label={i18n.get('银行国际代码(Swift Code)')} require>
          <InputWrapper
            form={form}
            name="swiftCode"
            placeholder={i18n.get('请输入银行国际代码')}
            {...form.getFieldProps('swiftCode', {
              rules: [
                { required: true, whitespace: true, message: i18n.get('请输入银行国际代码(Swift Code)') },
                {
                  validator: (_, value, callback) => {
                    if (!value || value.length === 8 || value.length === 11) {
                      return callback()
                    }
                    return callback('只能输入8位或11位')
                  }
                }
              ]
            })}
          />
        </AddPayeeItem>
        <AddPayeeItem label={i18n.get('银行所在地区代码(Nation Code)')} require>
          <Picker
            extra={<span className="placeholder-color">{i18n.get('银行所在地区代码(Nation Code)')}</span>}
            data={cityCodeList}
            cols={1}
            {...form.getFieldProps('nationCode', {
              initialValue: cityCodeData,
              rules: [{ required: true, message: i18n.get('银行所在地区代码(Nation Code)') }],
              onChange: this.handleCityCodeChange
            })}
          >
            <List.Item className="select-area" arrow="horizontal" />
          </Picker>
        </AddPayeeItem>
        <AddPayeeItem label={i18n.get('收款人地址(Receiver Address)')}>
          <InputWrapper
            form={form}
            name="receiverAddress"
            placeholder={i18n.get('请输入收款人地址，不超过35个汉字')}
            {...form.getFieldProps('receiverAddress')}
          />
        </AddPayeeItem>
        <AddPayeeItem label={i18n.get('汇款路线号码(Routing No.)')}>
          <InputWrapper
            form={form}
            name="routingNumber"
            placeholder={i18n.get('请输入汇款路线号码')}
            {...form.getFieldProps('routingNumber', {
              rules: [
                { max: 9, message: i18n.get('汇款路线号码不能超过9个字符') },
                { validator: this.checkRoutingNumber }
              ]
            })}
          />
        </AddPayeeItem>
        <AddPayeeItem label={i18n.get('联行号')}>
          <InputWrapper
            form={form}
            name="bankCode"
            placeholder={i18n.get('请输入联行号')}
            {...form.getFieldProps('bankCode', {
              rules: [{ max: 140, message: i18n.get('账号不能超过140个字') }]
            })}
          />
        </AddPayeeItem>
        <AddPayeeItem label={i18n.get('支行号')}>
          <InputWrapper
            form={form}
            name="branchCode"
            placeholder={i18n.get('请输入支行号')}
            {...form.getFieldProps('branchCode', {
              rules: [{ max: 140, message: i18n.get('账号不能超过140个字') }]
            })}
          />
        </AddPayeeItem>
      </>
    )
  }

  renderSortItems = allowConcisionType => {
    const { segmentActiveKey } = this.state
    switch (segmentActiveKey[0]) {
      case 'BANK':
      case 'ACCEPTANCEBILL':
        if (allowConcisionType) {
          return this.renderUnionBanks()
        } else {
          return this.renderBankBranch()
        }
      case 'ALIPAY':
        return null
      case 'OVERSEABANK':
        return this.renderOverseaBank()
      default:
        return null
    }
  }

  render() {
    const { payeeConfigCheck = {} } = this.props
    const isPersonalHaveCreate = payeeConfigCheck.personalAccount
    const isPublicHaveCreate = payeeConfigCheck.publicAccount
    return (
      <div className={styles['add-payee-view-wrapper']}>
        {this.renderList()}
        <div className="action-wrapper">
          {(isPersonalHaveCreate || isPublicHaveCreate) && (
            <Button
              block
              category="primary"
              size="middle"
              icon={<img src={SVG_SAVE_O} width={20} className="eui-icon" />}
              onClick={this.handleSubmit}
            >
              {i18n.get('保存')}
            </Button>
          )}
        </div>
      </div>
    )
  }
}
