import { app } from '@ekuaibao/whispered'
import styles from './payee-detail-label.module.less'
import React, { Component } from 'react'
import PropTypes from 'prop-types'
const SVG_DOWN = app.require('@images/mine-loan-down.svg')
const SVG_UP = app.require('@images/up.svg')

export default class PayeeDetailLabel extends Component {
  static propTypes = {
    info: PropTypes.object
  }

  constructor(props) {
    super(props)
    this.handleClick = this.handleClick.bind(this)
    this.state = { type: 0 }
  }

  handleClick() {
    const type = (this.state.type + 1) % 2
    this.setState({ type })
  }

  fnGenText() {
    if (this.state.type === 1) {
      return null
    }

    let { cardNo, name } = this.props.info
    cardNo = String(cardNo).replace(/\s/gi, '')
    name = String(name)
    const n = name.length > 3 ? name.slice(0, 2) + '...' : name
    const b = cardNo.slice(cardNo.length - 4)
    return i18n.get(`{__k0} （{__k1}）`, { __k0: b, __k1: n })
  }

  render() {
    let bankData = this.props.info
    let bankName = bankData.bank
    bankName = bankName && bankName.length > 4 ? bankName.slice(0, 4) + '...' : bankName
    return (
      <div className={styles['payee-detail-label-wrapper-past']} onClick={!this.props.flagEdit && this.handleClick}>
        <div className="title">
          <div className="line0" onClick={this.props.handleSelectPayee}>
            <div className="bank-img">
              <img className="payee-icon" src={bankData.icon} />
              <span>{bankName}</span>
            </div>
            <div className="right">{this.fnGenText()}</div>
          </div>

          <div className="n-icon" onClick={this.props.flagEdit && this.handleClick}>
            <img src={this.state.type == 0 ? SVG_DOWN : SVG_UP} />
          </div>
        </div>

        {this.state.type === 1 && <div className="line1">{this.props.info.name}</div>}

        {this.state.type === 1 && (
          <div className="line2">{this.props.info.cardNo.replace(/\s/g, '').replace(/(.{4})/g, '$1 ')}</div>
        )}

        {this.state.type === 1 && <div className="line3">{this.props.info.branch}</div>}
      </div>
    )
  }
}
