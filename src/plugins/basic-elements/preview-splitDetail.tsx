import style from './preview-splitDetail.module.less'
import React, { PureComponent } from 'react'
import { LightTable } from '@ekuaibao/eui-isomorphic'
import Money from '../../elements/puppet/Money'
import LayerContainer from './layer-container'
import <PERSON>han<PERSON><PERSON><PERSON><PERSON>Hook from './enhance-title-hook'

@EnhanceTitleHook()
export default class PreviewSplitDetail extends PureComponent {
  constructor(props) {
    super(props)
    props.overrideGetResult(this.getResult)
  }

  getResult = () => {
    return null
  }

  renderTitle() {
    const { data, firstCols } = this.props
    return (
      <div className="split-detail-title">
        <div>{data[firstCols[0]?.key]}</div>
        <div>
          <span className="split-detail-money">
            {i18n.get('费用总金额')}
            {i18n.get('：')}
          </span>
          <Money
            currencySize={14}
            fontWeight={600}
            valueSize={14}
            value={data?.money || 0}
            style={{ display: 'inline-block' }}
          />
        </div>
      </div>
    )
  }

  renderTable() {
    const { data, columns } = this.props
    const dataSource = data.secondTableData
    return (
      <div className="split-detail-table">
        <LightTable
          columns={columns}
          dataSource={dataSource}
          rowKey={(record: any) => {
            return record.id
          }}
        />
      </div>
    )
  }

  render() {
    return (
      <LayerContainer>
        <div className={style['preview-split-detail']}>
          {this.renderTitle()}
          {this.renderTable()}
        </div>
      </LayerContainer>
    )
  }
}
