import { app } from '@ekuaibao/whispered'
import './select-requisition.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { SearchBar } from 'antd-mobile'
import { Button } from '@hose/eui-mobile'
import EnhanceTitleHook from './enhance-title-hook'
const FlowLinkListModule = app.require('@requisition/elements/flow-link-list')
import EKBDropDown from '../bill/elements/EKBDropDown'
import { debounce } from '../../lib/util'
import { QuerySelect } from 'ekbc-query-builder'
import { isEqual } from 'lodash'
import EKBIcon from '../../elements/ekbIcon'
const EmptyWidget = app.require('@home5/EmptyWidget')

export const popupTypeNameMap = {
  type: 'type'
}

export const sortFieldMap = {
  submitDate: 'form.submitDate'
}

export const sortTypeMap = {
  DESC: 'DESC',
  ASC: 'ASC'
}

export const popupTypeMap = {
  type: sortFieldName => {
    return [
      { type: sortTypeMap.DESC, label: i18n.get('从近到远'), checkVisible: true },
      { type: sortTypeMap.ASC, label: i18n.get('从远到近'), checkVisible: true }
    ]
  }
}

export const fieldLabelMap = {
  submitDate: i18n.get('提交时间')
}

export const typeLabelMap = sortFieldName => {
  return {
    DESC: i18n.get('从近到远'),
    ASC: i18n.get('从远到近')
  }
}

@EnhanceConnect(state => ({
  flowLinkInfo: state['@bill'].flowLinkInfo
}))
@EnhanceTitleHook(i18n.get('选择单据'))
export default class FlowLinkList extends PureComponent {
  menuTypeActiveTypeMap = {
    type: sortTypeMap.DESC
  }

  constructor(props) {
    super(props)
    this.state = {
      flowLinkInfo: [],
      searchText: '',
      filterList: '',
      popupType: popupTypeNameMap.type, // dropdown 当前渲染的列表类型
      sortType: sortTypeMap.DESC,
      showDropDown: false,
      sortFieldName: sortFieldMap.submitDate
    }

    this.__result = this.formatResult(props?.param?.dataSource)
    props.overrideGetResult(this.getResult)
  }

  componentWillMount() {
    this.setState({ flowLinkInfo: this.props.flowLinkInfo })
  }

  componentWillReceiveProps(nextProps) {
    this.onSort(nextProps)
  }

  getResult = () => {
    return Object.values(this.__result)
  }

  formatResult = dataList => {
    const result = {}
    if (dataList?.length === 0) return result
    dataList.forEach(v => (result[v.id] = v))
    return result
  }

  handleRowClick = line => {
    const { flowLinkInfo } = this.state
    //第二次查询的时候，先判断带过来的上一次的查询结果是否还符合存在的条件，不符合则删除
    let arr = []
    for (let i in flowLinkInfo) {
      arr.push(flowLinkInfo[i]?.id)
    }
    for (let j in this.__result) {
      if (!arr.includes(j)) {
        delete this.__result[j]
      }
    }

    if (line.id in this.__result) {
      delete this.__result[line.id]
    } else {
      this.__result[line.id] = line
    }
    this.forceUpdate()
  }

  handleSelectNothing = () => {
    this.__result = {}
    this.props.layer.emitOk()
  }

  handleOk = () => {
    this.props.layer.emitOk()
  }

  onSort = nextProps => {
    if (!isEqual(this.props.flowLinkInfo, nextProps.flowLinkInfo)) {
      const { searchText } = this.state
      const { flowLinkInfo } = nextProps
      if (searchText) {
        const newList = flowLinkInfo.filter(
          el => el?.form?.code.includes(searchText) || el?.form?.title.includes(searchText)
        )
        this.setState({ filterList: newList })
      } else {
        this.setState({ filterList: '' })
      }
      this.setState({ flowLinkInfo })
    }
  }

  renderContentNullBody(text) {
    return <EmptyWidget size={200} type="noCentent" tips={text} />
  }

  renderList = () => {
    const { flowLinkInfo, searchText, filterList } = this.state
    if (searchText && filterList && !filterList.length) {
      return this.renderContentNullBody(i18n.get('未搜索到相关单据'))
    }
    const flowLinkList = filterList ? filterList : flowLinkInfo
    return (
      <FlowLinkListModule
        handleRowClick={this.handleRowClick}
        flowLinkList={flowLinkList}
        checkedList={this.__result}
      />
    )
  }

  handleSearchEnter = val => {
    if (!val) {
      this.setState({ filterList: '' })
      return
    }
    let { flowLinkInfo } = this.state
    flowLinkInfo = flowLinkInfo.filter(el => el?.form?.code.includes(val) || el?.form?.title.includes(val))
    this.setState({ filterList: flowLinkInfo })
  }

  debounceSearchValueChange = debounce(val => this.handleSearchEnter(val), 400)

  handleSearch = val => {
    const value = val.trim()
    this.setState({ searchText: value })
    return this.debounceSearchValueChange(value)
  }

  handleCancel = () => {
    this.setState({ filterList: '', searchText: '' })
  }

  handleOpenPopup = key => {
    this.setState({ showDropDown: true, popupType: key })
  }

  handleSortChange = menuType => {
    const { popupType, sortFieldName } = this.state
    const { param } = this.props
    let query = ''
    this.setState({
      sortType: menuType,
      showDropDown: false
    })
    query = new QuerySelect()
      .orderBy(sortFieldName, menuType)
      .filterBy(`state.in("archived", "paid", "approving")`)
      .value()

    this.menuTypeActiveTypeMap[popupType] = menuType

    app.invokeService('@bill:get:flow:link:list', { ...param, query })
  }

  handleCloseDropDown = () => {
    this.setState({ showDropDown: false })
  }

  renderGroupHeader = () => {
    const { showDropDown, sortFieldName, sortType, popupType } = this.state
    const arrowClass = showDropDown ? 'rotate' : ''

    return (
      <div className="withNote-detail-header">
        <div className="record-header-select">
          <div className="select-item" onClick={this.handleOpenPopup.bind(this, popupTypeNameMap.type)}>
            <span>{typeLabelMap(sortFieldName)[sortType]}</span>
            <EKBIcon name="#EDico-titledown" className={`${popupType === popupTypeNameMap.type ? arrowClass : ''}`} />
          </div>
        </div>
      </div>
    )
  }

  renderContent() {
    const { flowLinkInfo } = this.state
    if (!flowLinkInfo.length) return this.renderContentNullBody(i18n.get('您目前没有申请事项'))

    return (
      <div className="main">
        <SearchBar
          placeholder={i18n.get('请搜索名称或编码')}
          value={this.state.searchText}
          showCancelButton={this.state.isSearch}
          onChange={this.handleSearch}
          onCancel={this.handleCancel}
          onClear={this.handleCancel}
        />
        {this.renderGroupHeader()}
        <div className="content">{this.renderList()}</div>
      </div>
    )
  }

  renderFooter = () => {
    return (
      <div className="footer">
        <Button block size="middle" category="secondary" onClick={this.handleSelectNothing}>
          {i18n.get('取消')}
        </Button>
        <Button block size="middle" className="ml-16" onClick={this.handleOk}>
          {i18n.get('确认')}
        </Button>
      </div>
    )
  }

  render() {
    const { popupType, showDropDown, sortFieldName } = this.state
    const menuList = popupTypeMap[popupType](sortFieldName)

    return (
      <div className="select-requisition-wrapper">
        <div className="layout-content-wrapper">
          {showDropDown && (
            <EKBDropDown
              downContentClassName={
                popupType === popupTypeNameMap.type ? 'dropdown-content has-icon' : 'dropdown-content'
              }
              show={showDropDown}
              activeType={this.menuTypeActiveTypeMap[popupType]}
              menuList={menuList}
              fnChangeType={this.handleSortChange}
              fnCancel={this.handleCloseDropDown}
            />
          )}
          {this.renderContent()}
          {this.renderFooter()}
        </div>
      </div>
    )
  }
}
