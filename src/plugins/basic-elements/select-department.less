/**************************************************
 * Created by nanyuantingfeng on 31/10/2016 16:30.
 **************************************************/
@import "../../styles/layout.less";

.select-department-view-wrapper {
  .app-layout-content();
  display          : flex;
  flex-direction   : column;
  background-color : #FFFFFF;
  
  .layout-content-wrapper {
    
    .search-wrapper {
      height           : 86px;
      background-color : #F0F3F6;
      display          : flex;
      align-items      : center;
      
      .input {
        flex : 1;
        input {
          border    : 0;
          font-size : 28px;
        }
        //.am-list-item {
        //  padding-left : 0;
        //  min-height   : 0;
        //  .am-input-control {
        //    width : 100%;
        //    input {
        //      width : 100%;
        //    }
        //  }
        //}
      }
      
    }
    
    .list-wrapper {
      flex           : 1;
      display        : flex;
      flex-direction : column;
      
      .item {
        height           : 120px;
        background-color : #FFFFFF;
        display          : flex;
        align-items      : stretch;
        
        div.wrap {
          display       : flex;
          border-bottom : 2px solid #F3F3F3;
          margin-left   : 46px;
          align-items   : center;
          flex          : 1;
          
          div.left {
            height      : 100%;
            font-size   : 28px;
            line-height : 1;
            color       : #54595B;
            flex        : 1;
            display     : flex;
            align-items : center;
          }
          
          div.line {
            width            : 2px;
            background-color : #F3F3F3;
            height           : 70%;
          }
          
          div.right {
            margin-left : 16px;
            width       : 150px;
            display     : flex;
            height      : 100%;
            
            div:first-child {
              display         : flex;
              align-items     : center;
              justify-content : center;
              img {
                width  : 44px;
                height : 44px;
              }
            }
            div:last-child {
              font-size       : 28px;
              line-height     : 1;
              color           : var(--brand-base);
              display         : flex;
              justify-content : flex-start;
              align-items     : center;
            }
          }
        }
        
      }
    }
  }
  //.am-search-clear {
  //  display : none;
  //}
  //.am-search-input .am-search-synthetic-ph, .am-search-input input[type="search"] {
  //  font-size : 28px;
  //}
}
