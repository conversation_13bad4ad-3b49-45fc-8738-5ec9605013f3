/**************************************************
 * Created by nanyuanting<PERSON> on 22/09/2016 17:32.
 **************************************************/
import { EXPENSE_STATE } from './basic-state-const'

import Form from './Form'

class Flow {
  constructor(line) {
    this.updateUseNewLine(line)
  }

  updateUseNewLine(line) {
    this.id = line.id
    this.corporationId = line.corporationId
    this.ownerId = line.ownerId
    this.state = line.state
    this.type = line.type
    this.updateTime = line.updateTime
    this.actions = line.actions
    this.orderCode = line.orderCode
    this.active = line.active
    this.logs = line.logs
    this.plan = line.plan
    this.formType = line.formType

    this.form = new Form(line.form)
    this.template = this.form.flowPlan
    return this
  }

  setPlanData(planData) {
    this.planData = planData
    return this
  }

  getTemplateId() {
    return this.template.id
  }

  getState() {
    return EXPENSE_STATE[this.state]
  }
}

export default Flow
