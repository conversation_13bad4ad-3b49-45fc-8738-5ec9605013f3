/**
 *  Created by pw on 2023/6/10 10:05.
 */
import { uuid } from '@ekuaibao/helpers'
import { get } from 'lodash'
import { Fetch } from '@ekuaibao/fetch'

export interface IParams {
  selectData: any[]
}

export interface INodeDisplay {
  configNodeId?: string
  value: string
  label: string
}

export function getCurrentNodeBeforeNodes({ selectData }: IParams): INodeDisplay[] {
  let firstData
  if (selectData?.length === 1) {
    firstData = selectData[0]
  }
  let label = i18n.get('提交人')
  if (!!firstData) {
    // let submitter = get(firstData, 'flow.ownerId', undefined) || get(firstData, 'backlog.flowId.ownerId', undefined)
    let submitter = firstData?.flow?.ownerId || firstData?.backlog?.flowId.ownerId
    submitter = submitter ? submitter : get(firstData, 'ownerId', {})
    label = submitter.name ? `${label}${i18n.get('（')}${submitter.name}${i18n.get('）')}` : label
  }
  const mergeNodes: INodeDisplay[] = [
    {
      value: null,
      label
    }
  ]
  if (!selectData?.length) {
    return mergeNodes
  }
  // 检查选择的数据是否是同一个审批流程
  if (!checkDataSameFlowPlan(selectData)) {
    return mergeNodes
  }
  // 获取所有选择的单据的之前的节点二维数组
  let allBeforeNodes: INodeDisplay[][] = selectData.map((data: any) => getNodeBeforeNodes(data))
  allBeforeNodes = allBeforeNodes.sort((a, b) => a.length - b.length)
  const [firstNodes, ...otherNodes] = allBeforeNodes
  if (!firstNodes?.length) {
    return mergeNodes
  }
  let mergeMap = firstNodes.reduce((map, node) => {
    const configNodeId =  node.configNodeId
    if (!configNodeId) {
      node.configNodeId = uuid()
    }
    map[node.configNodeId] = node
    return map
  }, {} as Record<string, INodeDisplay>)
  mergeMap = otherNodes?.reduce((result, currentNodes) => {
    currentNodes.forEach(node => {
      if (!result[node.configNodeId]) {
        delete result[node.configNodeId]
      }
    })
    return result
  }, mergeMap as any)
  const beforeNodes = Object.values(mergeMap)
  return mergeNodes.concat(beforeNodes)
}

export function getNodeBeforeNodes(data: any): INodeDisplay[] {
  const flow = data?.backlog?.flowId || data?.backlog || data
  const nodes = get(flow, 'plan.nodes', [])
  const taskId = get(flow, 'plan.taskId', '')
  const taskIndex = nodes.findIndex((v: { id: any }) => v.id === taskId)
  const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage

  return nodes.reduce(
    (
      list: Array<{ value: any; label: string; configNodeId: string }>,
      line: {
        type: any
        skippedType: any
        id: any
        name: any
        enName:string
        approverId: any
        counterSigners?: any[]
        configNodeId: string
      },
      index: number
    ) => {
      const { type, skippedType, id, name, enName = '', approverId, counterSigners = [], configNodeId } = line
      const isNeedCashierNode = get(line, 'config.isNeedCashierNode', true)
      const isEsignature = line.config && line.config.type === 'esignature' // 电子签节点
      const langName = (lang === 'en-US' && enName) ? enName : name
      if (
        isNeedCashierNode &&
        skippedType === 'NO_SKIPPED' &&
        type !== 'ebot' &&
        type !== 'invoicingApplication' &&
        type !== 'recalculate' &&
        type !== 'aiApproval' &&
        !isEsignature &&
        index < taskIndex
      ) {
        let approver = ''
        if (type === 'countersign') {
          approver = i18n.get('{__k0}人会签', { __k0: counterSigners.length })
        } else {
          approver = approverId ? approverId.name : i18n.get('未选择')
        }
        list.push({
          configNodeId,
          value: id,
          label: langName + (approver ? i18n.get('（') + approver + i18n.get('）') : '')
        })
      }
      return list
    },
    []
  )
}

function checkDataSameFlowPlan(data: any[] = []) {
  if (!data.length) {
    return false
  }
  const [first] = data
  const flow = first?.backlog?.flowId || first?.backlog || first
  const flowPlanId = get(flow, 'plan.flowPlanConfigId')
  return (
    data.filter(item => {
      const flow = item?.backlog?.flowId || item?.backlog || item
      return get(flow, 'plan.flowPlanConfigId') === flowPlanId
    }).length === data.length
  )
}

export function getNodeIdByConfigNodeId(configNodeId: string, selectData: any[] = []) {
  if (!configNodeId) {
    return null
  }
  return selectData.reduce((result, data) => {
    const flow = data?.backlog?.flowId || data?.backlog || data
    const nodes = get(flow, 'plan.nodes', [])
    const rejectNode = nodes.find((node: { configNodeId: string }) => node.configNodeId === configNodeId)
    const backlogId = data?.backlog?.flowId ? data?.backlog?.id : flow?.backlogId
    if (backlogId) {
      result[backlogId] = rejectNode.id
    }
    return result
  }, {})
}
