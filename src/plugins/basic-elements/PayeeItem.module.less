@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.payeeItem-wrapper-blue {
  background: linear-gradient(101deg, rgba(55, 142, 208, 1) 0%, rgba(55, 100, 191, 1) 100%) !important;
}

.payeeItem-wrapper-green {
  background: linear-gradient(100deg, #34cb4c 8%, #22ac38 93%) !important;
}

.payeeItem-wrapper-selected {
  :global {
    .account-selected-mask {
      display: flex !important;
      opacity: 1 !important;
      background: @color-black-3;
    }
  }
}

.payeeItem-wrapper {
  overflow: hidden;
  position: relative;
  padding-top: @space-5;
  border-radius: @radius-3;
  background: linear-gradient(101deg, rgba(253, 117, 102, 1) 0%, rgba(252, 82, 98, 1) 100%);
  :global {
    .account-absolute-class {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      overflow: hidden;
    }
    .account-selected-mask {
      .account-absolute-class;
      display: none;
      align-items: center;
      justify-content: center;
      opacity: 0;
      .account-selected-icon {
        width: 148px;
        height: 148px;
        color: @color-white-2;
      }
    }
    .account-icon-wrap {
      .account-absolute-class;
      .account-bg-icon {
        position: absolute;
        right: 10%;
        top: 0;
        height: 100%;
        width: 36%;
        transform: rotate(-15deg);
        color: @color-white-5;
      }
    }
    .payeeItem-header {
      width: 100%;
      .font-size-2;
      height: 40px;
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: @color-white-1;
      padding: 0 @space-6;
      .font-weight-3;
      .payee-icon-box {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        width: 32px;
        background-color: @color-white-1;
        border-radius: 50%;
        .payee-icon {
          width: 32px;
          height: 32px;
        }
      }
      span {
        margin-left: @space-4;
      }
    }
    .payeeItem-content {
      padding: 36px 48px;
      color: @color-white-1;
      line-height: 1;
      .payeeItem-content-inner {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        height: 44px;
        .font-size-3;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        .font-weight-3;
        .payeeItem-code {
          height: 24px;
          .font-size-1;
          .font-weight-2;
          line-height: 28px;
          margin-left: @space-2;
        }
      }
    }
    .payeeItem-footer {
      display: flex;
      flex-direction: row;
      align-items: center;
      .font-size-2;
      height: 80px;
      line-height: 80px;
      color: @color-white-1;
      background-color: @color-line-1;
      padding: 0 @space-6;
      justify-content: flex-start;
      .font-weight-3;
      .icon {
        width: 40px;
        height: 40px;
        margin-right: @space-4;
      }
    }
  }
}
