@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.nullify_flow_wrapper {
  :global {
    .nullify_flow_header {
      height: 120px;
      line-height: 104px;
      text-align: center;
      font-size: 40px;
      font-weight: bold;
      color: #000000;
    }
    .nullify_flow_content {
      height: 208px;
      padding: 24px 32px;
      .checkbox_wrapper {
        margin-top: 24px;
        .check-box {
          margin-right: 12px;
        }
      }
    }
    .nullify_flow_footer {
      height: 112px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      border-top: 2px solid #e8e8e8;
      .btn {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32px;
        text-align: right;
        color: #3a3f3f;
        &:last-child {
          color: var(--brand-base);
          border-left: 2px solid #e6e6e6;
        }
      }
    }
  }
}
