@import "../../styles/layout.less";
@import "../../elements/popup-wrapper";

.ekb-bank-card {

  text-align: left;
  margin: 32px 32px 0 32px;
  border-radius: 3px;
  background-color: @white;
  border: solid 2px #f5f5f5;

  .tp {
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    height: 80px;
    flex-direction: row;
    border-bottom: 2px solid #F3F3F3;
    margin: 0 16px 0 16px;

    div:first-child {
      font-weight: 500;
      .fix-ellipsis();
      flex: 1;
      font-size: 28px;
      color: #54595B;
    }
    div:last-child {
      display: flex;
      width: 40px;
      text-align: center;
      height: 100%;
      color: #A2ABAF;
      flex-shrink: 0;
      font-size: 40px;
      font-weight: 500;
    }
  }
  .item-w {
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    padding: 16px;
    .default {
      font-size: 26px;
      font-weight: 500;
      margin-top: 14px;
      line-height: 1;
      color: @gray-6;
      margin-left: 10px;
    }
    .payee-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      .l1 {
        margin-top: 15px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .bank-img {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: row;
          border-radius: 2px;
          background-color: #f9f9f9;
          border: 2px solid #eeeeee;
          padding: 0 16px;

          .payee-icon {
            height: 40px;
            width: 40px;
            flex-shrink: 0;
          }
          span {
            font-size: 26px;
            color: #3A3F3F;
            margin-left: 5px;
          }
        }
        .complement-warning {
          color: #ec0f03;
          .warning {
            margin-right: 8px;
            fill: #ec0f03;;
          }
        }
      }
      .md {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        position: relative;
        height: 40px;
        div:first-child {
          .fix-ellipsis();
          position: absolute;
          left: 0;
          right: 0;
          font-size: 32px;
          color: #3A3F3F;
        }
      }
      .bm {
        margin-top: 15px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        position: relative;
        height: 42px;
        color: @gray-7;

        div:first-child {
          .fix-ellipsis();
          position: absolute;
          left: 0;
          right: 0;
        }
      }

    }
    .card-bottom-wrapper {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      color: @gray-7;
    }
    .card-right-wrapper {
      display: flex;
      flex-direction: row;
      //padding-top: 30px;
      //padding-right: 32px;
      justify-content: space-between;
    }
  }
  .fix-swipe();
}

.ekb-bank-card:last-child {
  margin-bottom: 32px;
}
