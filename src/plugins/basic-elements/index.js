/**************************************************
 * Created by nanyuanting<PERSON> on 26/07/2017 15:24.
 **************************************************/
import loadable from '@loadable/component'

export default [
  {
    path: '/flow-plan-readonly/:type/:id',
    ref: '/',
    onload: () => import('./flow-plan-readonly')
  },
  {
    path: '/flow-plan-readonly/:type/:id/:isReadOnly',
    ref: '/',
    onload: () => import('./flow-plan-readonly')
  },
  {
    path: '/flow-plan-snapshot/:type/:id/:planVersionId',
    ref: '/',
    onload: () => import('./flow-plan-snapshot')
  },
  {
    point: '@@layers',
    prefix: '@basic',
    onload: () => require('./layers').default
  },
  {
    point: '@@components',
    namespace: '@basic',
    onload: () => [
      { key: 'FlowPlanEditable', component: () => import('./flow-plan-editable') },
      { key: 'FlowPlanReadonly', component: () => import('./flow-plan-readonly') }
    ]
  },
  {
    path: '/express-budget-child-list',
    ref: '/',
    onload: () => import('./budget/budget-child-list')
  },
  {
    resource: '@basic-elements',
    value: {
      ['avatars']: loadable(() => import('./avatars')),
      ['layer-container']: loadable(() => import('./layer-container')),
      ['loan/loanpackage-distributed']: loadable(() => import('./loan/loanpackage-distributed')),
      ['loan/writtenoff-record-list']: loadable(() => import('./loan/writtenoff-record-list')),
      ['loan/writtenoff-record-list-logs']: loadable(() => import('./loan/writtenoff-record-list-logs')),
      ['basic-state-const']: require('./basic-state-const'),
      ['BackLog']: require('./BackLog').default,
      ['budget/report-list-view']: loadable(() => import('./budget/report-list-view')),
      ['budget/report-detail-view']: loadable(() => import('./budget/report-detail-view')),
      ['bank-card']: loadable(() => import('./bank-card')),
      ['signature/user-signature']: loadable(() => import('./signature/UserSignature'))
    }
  }
]
