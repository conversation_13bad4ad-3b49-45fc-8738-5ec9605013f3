@import '~@ekuaibao/eui-styles/less/token.less';
@import '~./sort-dropdown.less';

.loan-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  height: 100%;

  .search-bar-wrapper {
    padding: 24px;
    z-index: 4;
  }

  .withNote-detail-header {
    .sort-detail-header;
  }

  .loan-list {
    display: flex;
    background-color: #ffffff;
    flex: 1;
    overflow-y: auto;
    position: relative;
  }

  .list-item {
    display: flex;
    height: 144px;
    flex-direction: row;
    justify-content: space-between;
    padding: 24px;
    border-bottom: 2px solid #f3f3f3;

    &.checked {
      background-color: @color-bg-2;
    }

    .left {
      display: flex;
      flex-direction: row;
      height: 100%;
      align-items: center;
      flex: 1;
      overflow-x: hidden;

      .am-radio-wrapper {
        display: flex;
        align-items: center;

        &.square {
          .am-radio {
            height: 43px;
            width: 43px;
            border: 2px solid var(--gray-14);
            border-radius: 50%;
          }

          .am-radio-checked {
            border: 2px solid var(--brand-base);
          }

          .am-radio-input {
            margin: 0;
            border-radius: 50%;
          }

          .am-radio-inner {
            margin: 0;
            width: 100%;
            height: 100%;

            &::after {
              padding: 0;
              margin: 0;
              top: 50%;
              right: 50%;
              transform: translate(50%, -55%);
              border: 2px solid var(--brand-base);
              border-radius: 50%;
              background-color: var(--brand-base);
              content: '';
              width: 60%;
              height: 60%;
            }
          }
        }
      }

      .am-checkbox-wrapper {
        display: flex;
        flex: 1;
        width: 100%;
        align-items: center;

        &.square {
          .am-checkbox-inner {
            border-radius: 0;
          }
        }

        .am-checkbox {
          flex-shrink: 0;
          height: 43px;
        }

        span:last-child {
          flex: 1;
          overflow-x: hidden;
        }
      }

      .loan-info-wrapper {
        margin-left: @space-6;
      }

      .divider {
        margin: 0 @space-6;
        font-size: 24px;
      }

      .remain {
        font-size: 24px;
      }
    }

    .title {
      font-size: 32px;
      color: #54595b;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: @space-4;
      font-weight: bold;
    }

    .loan-info {
      display: flex;
      flex-flow: row;
      flex: 1;
      overflow: hidden;
      color: @color-black-3;

      .date {
        font-size: 24px;
        color: #b1b9bd;
      }

      .sub-title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .normal {
        color: @color-black-3;
      }

      .red {
        color: #f17b7b;
      }

      .yellow {
        color: #ed9634;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      height: 100%;
      flex-shrink: 0;
      text-align: end;

      .money {
        color: @color-black-1;
        font-size: 32px;
        margin-bottom: @space-4;
      }

      .loan-text {
        color: @color-black-3;
        font-size: 24px;
      }
    }
  }

  .empty-box {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #ffffff;
    border-bottom: 2px solid #ebebeb;
    flex-direction: column;

    div.empty-img {
      padding-left: 16%;
      padding-right: 16%;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;

      img {
        max-height: 100%;
        max-width: 100%;
        width: 100%;
      }
    }

    .empty-txt {
      height: 40px;
      font-size: 28px;
      color: #b3bbbf;

      &:last-child {
        margin-bottom: 22px;
      }
    }
  }

  .submit {
    padding: 20px 32px;
    background-color: var(--eui-bg-body);
    box-shadow: var(--eui-shadow-up-1);
  }

  .ekb-list-box-wrapper {
    .list-view {
      position: relative;
    }
  }

  .ekb-drop-down-cover;
}
