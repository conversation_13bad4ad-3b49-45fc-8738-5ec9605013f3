/**************************************************
 * Created by nanyuanting<PERSON> on 27/09/2016 16:57.
 **************************************************/
@import "../../styles/layout.less";
@import "../../styles/theme.less";

.flow-plan-wrapper {
  height: 100%;
  flex: 1;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  background-color: @gray-1;
}

.flow-plan-list {
  list-style: none;
  padding: none;
  margin: none;
  :global {
    li.item {
      font-size: 26px;
      color: #54595B;
      padding: 24px 24px 2px 80px;
      position: relative;
      .status-bg {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 80px;
        .gray-line {
          position: absolute;
          top: 0;
          left: 40px;
          bottom: 0;
          display: block;
          width: 2px;
          background-color: #E5E5E5;
        }
        .circle {
          position: absolute;
          z-index: 2;
          top: 50px;
          left: 34px;
          display: block;
          width: 14px;
          height: 14px;
          border-radius: 14px;
          background-color: #DDDDDD;
          .icon-circle{
            display: none;
          }
        }
      }
      .content {
        padding: 0 16px;
        border-radius: 8px;
        background-color: #f9f9f9;
        box-shadow: 0 2px 0 rgba(229, 229, 229, 0.5);
        border: solid 2px #EEEEEE;
        width: 100%;
        .header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: #EEEEEE 2px solid;
          padding: 10px 0;
          .condition {
            display: inline-block;
            margin-left: 16px;
            width: 88px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            border-radius: 4px;
            background-color: #FFFFFF;
            font-size: 22px;
            font-weight: 500;
            color: #A2ABAF;
            border: solid 2px #E6E6E6;
          }
          .header-state {
            width: 180px;
            text-align: right;
          }
        }
        .b-main {
          padding: 0 8px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 94px;
          font-size: 28px;
          .info {
            display: flex;
            align-items: center;
            .no-choose {
              color: #A2ABAF;
            }
            .user-img {
              margin-right: 16px;
              width: 60px;
              height: 60px;
              border-radius: 30px;
            }
            .user-name {
              font-weight: 500;
              color: #54595B;
            }
            .comment-img {
              width: 40px;
              height: 40px;
              margin-left: 16px;
              margin-top: 5px;
            }
            .head-right {
              display: flex;
              align-items: center;
              .status-text {
                font-size: 26px;
                color: #A2ABAF;
              }
              .img-question {
                width: 44px;
                margin-left: 10px;
              }
            }
          }
          .arrow-right {
            width: 44px;
          }
        }
        .reason {
          position: relative;
          margin: 0 8px 12px 80px;
          padding: 12px;
          border-radius: 8px;
          background-color: #FFFFFF;
          line-height: 1.3;
          border: solid 2px #FFFFFF;
          font-size: 24px;
          color: #A2ABAF;
          word-break: break-all;
          .arrow-top {
            position: absolute;
            top: -9px;
            left: 20px;
            display: block;
            width: 18px;
            height: 18px;
            background-color: #FFFFFF;
            border-left: solid 2px #EEEEEE;
            border-top: solid 2px #EEEEEE;
            transform: rotate(45deg);
          }
        }
        .tag {
          margin-left: 80px;
          border-radius: 0.08rem;
          background-color: #FFFFFF;
          border: solid 0.02rem #FFFFFF;
          margin-bottom: 10px;
        }
      }
      &.resolve {
        .status-bg {
          .gray-line {
            &::after {
              content: '';
              display: block;
              position: absolute;
              z-index: 2;
              top: 0;
              left: 0;
              bottom: -60px;
              width: 2px;
              background: var(--brand-base);
            }
          }
          .circle {
            .icon-circle{
              display: block;
              position: absolute;
              z-index: 2;
              top: -10px;
              left: -9px;
              width: 32px;
              height: 32px;
              color: var(--brand-base);
            }
            // &::after {
            //   content: '';
            //   display: block;
            //   position: absolute;
            //   z-index: 2;
            //   top: -10px;
            //   left: -9px;
            //   width: 32px;
            //   height: 32px;
            //   background: url("./images/approve-status-resolve.svg");
            //   background-size: contain;
            // }
          }
        }
      }
      &.justApproving {
        .status-bg {
          .gray-line {
            display: none;
            &::after {
              display: none;
            }
          }
          .circle {
            top: 45%;
            background-color: transparent;
            &::after {
              content: '';
              display: block;
              position: absolute;
              z-index: 2;
              //top             : 50%;
              left: -9px;
              width: 32px;
              height: 32px;
              background: url("./images/approve-status-approving.svg");
              background-size: contain;
            }
          }
        }
      }
      &.justReject {
        .status-bg {
          .gray-line {
            display: none;
            &::after {
              display: none;
            }
          }
          .circle {
            top: 45%;
            background-color: transparent;
            &::after {
              content: '';
              display: block;
              position: absolute;
              z-index: 2;
              //top             : 50%;
              left: -9px;
              width: 32px;
              height: 32px;
              background: url("./images/approve-status-reject.svg");
              background-size: contain;
            }
          }
        }
      }
      &.justPay {
        .status-bg {
          .gray-line {
            display: none;
            &::after {
              display: none;
            }
          }
          .circle {
            top: 45%;
            background-color: transparent;
            .icon-circle{
              display: block;
              position: absolute;
              z-index: 2;
              // top: -10px;
              left: -9px;
              width: 32px;
              height: 32px;
              color: var(--brand-base);
            }
            // &::after {
            //   content: '';
            //   display: block;
            //   position: absolute;
            //   z-index: 2;
            //   //top             : 50%;
            //   left: -9px;
            //   width: 32px;
            //   height: 32px;
            //   background: url("./images/approve-status-resolve.svg");
            //   background-size: contain;
            // }
          }
        }
      }
      &.completion {
        .status-bg {
          .gray-line {
            &::after {
              content: '';
              display: block;
              position: absolute;
              z-index: 2;
              top: 0;
              left: 0;
              bottom: 0;
              width: 2px;
              background: var(--brand-base);
            }
          }
          .circle {
            .icon-circle{
              display: block;
              position: absolute;
              z-index: 2;
              top: -10px;
              left: -9px;
              width: 32px;
              height: 32px;
              color: var(--brand-base);
            }
            // &::after {
            //   content: '';
            //   display: block;
            //   position: absolute;
            //   z-index: 2;
            //   top: -10px;
            //   left: -9px;
            //   width: 32px;
            //   height: 32px;
            //   background: url("./images/approve-status-resolve.svg");
            //   background-size: contain;
            // }
          }
        }
      }
      &.approving {
        .circle {
          &::after {
            content: '';
            display: block;
            position: absolute;
            z-index: 2;
            top: -14px;
            left: -13px;
            width: 40px;
            height: 40px;
            background: url("./images/approve-status-approving.svg");
            background-size: contain;
          }
        }
      }
      &.retract {
        .content {
          .header {
            .status-text {
              color: yellow;
            }
          }
          .reason {
            background-color: #FFFFFF;
            .arrow-top {
              background-color: #FFFFFF;
            }
          }
        }
        .circle {
          &::after {
            content: '';
            display: block;
            position: absolute;
            z-index: 2;
            top: -14px;
            left: -13px;
            width: 40px;
            height: 40px;
            background: url("./images/approve-status-retract.svg");
            background-size: contain;
          }
        }
      }
      &.reject {
        .content {
          .header {
            .status-text {
              color: #FF7C7C;
            }
          }
          .reason {
            background-color: #FFFFFF;
            .arrow-top {
              background-color: #FFFFFF;
            }
          }
        }
        .circle {
          &::after {
            content: '';
            display: block;
            position: absolute;
            z-index: 2;
            top: -14px;
            left: -13px;
            width: 40px;
            height: 40px;
            background: url("./images/approve-status-reject.svg");
            background-size: contain;
          }
        }
      }
      &:first-child {
        .status-bg {
          .gray-line {
            top: 50px;
          }
        }
      }
      &:last-child {
        .status-bg {
          .gray-line {
            height: 50px;
          }
        }
      }
    }
  }
}

.content-msg-tips {
  color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.50));
  font: var(--eui-font-body-r1);
  border-bottom: 1px dashed var(--eui-line-divider-default);
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.has-content-msg-tips {
  flex-direction: column;
}