import React, { useState, useEffect, useMemo } from 'react'
import './manual-repay-popup.less'
import EKBDropDown from '../bill/elements/EKBDropDown'
import EKBIcon from '../../elements/ekbIcon'
import { app } from '@ekuaibao/whispered'
import { MoneyMath } from '@ekuaibao/money-math'
import LayerContainer from './layer-container'
import classNames from 'classnames'
import * as util from '../../lib/util'
const { thousandBitSeparator } = app.require('@components/utils/fnThousandBitSeparator')
import { QuerySelect } from 'ekbc-query-builder'
const ListBoxWrapper = app.require('@elements/list-box-wrapper')
import { Radio } from 'antd-mobile'
import { SearchBar, Button } from '@hose/eui-mobile'

const SVG_WRITEOFF_NULL = app.require('@images/writeoff-null.svg')

const popupTypeMap = {
  field: [{ type: 'loanDate', label: i18n.get('借款日期'), checkVisible: true }],
  type: [
    { type: 'DESC', label: i18n.get('从近到远'), checkVisible: true },
    { type: 'ASC', label: i18n.get('从远到近'), checkVisible: true }
  ]
}

const fieldMap = {
  loanDate: i18n.get('借款日期')
}

const typeMap = {
  DESC: i18n.get('从近到远'),
  ASC: i18n.get('从远到近')
}

const menuTypeActiveTypeMap = {
  field: 'loanDate',
  type: 'DESC'
}

const ManualRepayPopup = props => {
  const [items, setItems] = useState([])
  const [searchText, setSearchText] = useState('')
  const [searchList, setSearchList] = useState([])
  const [sortType, setSortType] = useState('DESC')
  const [popupType, setPopupType] = useState('field')
  const [showDropDown, setShowDropDown] = useState(false)
  const [sortFieldName, setSortFieldName] = useState('loanDate')
  const [checkedItem, setCheckedItem] = useState([])

  useEffect(() => {
    initData(props)
  }, [props])

  // 初始化数据
  const initData = (data, query = '') => {
    const { canSelectedWrittenOff, preCheckedItem } = data
    let filterLists = JSON.parse(JSON.stringify(canSelectedWrittenOff))
    if (query) {
      const { orderBy } = query
      filterLists = filterLists.sort((a, b) => {
        if (orderBy[0]['order'] == 'DESC') return a.repaymentDate - b.repaymentDate
        else return b.repaymentDate - a.repaymentDate
      })
      setItems(filterLists)
    } else {
      setItems(canSelectedWrittenOff)
    }

    setCheckedItem(preCheckedItem)
  }

  // 处理搜索框取消事件
  const handleCancel = () => {
    setSearchText({ searchText: '' })
    initData(props)
  }

  // 处理页面确认回调事件
  const handleSubmit = () => {
    const filterItems = items.filter(item => item.id == checkedItem[0].loanInfoId.id)
    props.layer.emitOk(filterItems)
  }

  // 处理搜索事件
  const handleSearch = val => {
    const newList = items.filter(loan => !!~loan.title.indexOf(val))
    setSearchText(val)
    setSearchList(newList)
  }

  // 处理下拉框打开事件
  const handleOpenPopup = key => {
    setShowDropDown(true)
    setPopupType(key)
  }

  // 处理下拉框关闭事件
  const handleCloseDropDown = () => {
    setShowDropDown(false)
  }

  // 处理排序事件
  const handleSortChange = menuType => {
    let query = ''
    if (popupType === 'field') {
      setSortFieldName(menuType)
      setShowDropDown(false)
      query = new QuerySelect().orderBy(menuType, sortType).value()
    } else {
      setSortType(menuType)
      setShowDropDown(false)
      query = new QuerySelect().orderBy(sortFieldName, menuType).value()
    }

    menuTypeActiveTypeMap[popupType] = menuType

    initData(props, query)
  }

  // 渲染头部
  const renderGroupHeader = () => {
    const arrowClass = showDropDown ? 'rotate' : ''

    return (
      <div className="withNote-detail-header">
        <div className="record-header-select">
          <div className="select-item" onClick={handleOpenPopup.bind(this, 'field')}>
            <span>{fieldMap[sortFieldName]}</span>
            <EKBIcon name="#EDico-titledown" className={`${popupType === 'field' ? arrowClass : ''}`} />
          </div>
          <div className="select-item" onClick={handleOpenPopup.bind(this, 'type')}>
            <span>{typeMap[sortType]}</span>
            <EKBIcon name="#EDico-titledown" className={`${popupType === 'type' ? arrowClass : ''}`} />
          </div>
        </div>
      </div>
    )
  }

  //渲染空状态页面
  const renderContentNullBody = () => {
    return (
      <div className="empty-box">
        <div className="empty-img">
          <img src={SVG_WRITEOFF_NULL} />
        </div>
        <div className="empty-txt">{searchText ? i18n.get('没搜索到借款') : i18n.get('没有添加可核销的借款')}</div>
      </div>
    )
  }

  // 渲染借款包列表
  const renderList = () => {
    const { scale, strCode } = app.getState('@common').standardCurrency
    let list = loanList.map((line, key) => {
      let { title, repaymentDate, amount, foreignCurrencyLoan, totalMoneyNode, loanInfoId, remain } = line
      const standardStrCode = totalMoneyNode?.standardStrCode
      const checked = checkedItem.length
        ? (line.id ? line.id : line.loanInfoId.id) ==
          (checkedItem[0].id ? checkedItem[0].id : checkedItem[0].loanInfoId.id)
          ? true
          : false
        : false
      const clsName = classNames('list-item', { checked: checked })
      const strCodeValue = `${foreignCurrencyLoan ? foreignCurrencyLoan.foreignStrCode : standardStrCode || strCode} `
      const scaleValue = foreignCurrencyLoan ? foreignCurrencyLoan.foreignScale : scale
      const remainText = foreignCurrencyLoan
        ? foreignCurrencyLoan.foreign
        : loanInfoId.remain
        ? loanInfoId.remain
        : remain

      return (
        <div className={clsName} key={key}>
          <div className="left">
            <Radio className="square" checked={checked} onChange={handleRadioLineChage.bind(this, line)}>
              <div className="loan-info-wrapper">
                <div className="title">{title}</div>
                <div className="loan-info">
                  <div className={'date sub-title ' + util.repaymentDateColor(repaymentDate)}>
                    {util.repaymentDateTip(repaymentDate)}
                  </div>
                  <div className="divider">|</div>
                  <div className="remain">{strCodeValue + thousandBitSeparator(remainText)}</div>
                </div>
              </div>
            </Radio>
          </div>
          <div className="right">
            <div className="money">
              {strCodeValue + thousandBitSeparator(Number(new MoneyMath(amount).value).toFixed(scaleValue))}
            </div>
            <div className="loan-text">{i18n.get('本次核销')}</div>
          </div>
        </div>
      )
    })
    return <ListBoxWrapper className="loan-list" elementHeight={72} items={list} />
  }

  // 处理单选框change事件
  const handleRadioLineChage = (line, event) => {
    setCheckedItem([line])
  }

  // 借款包列表
  const loanList = useMemo(() => {
    return searchText ? searchList : items
  }, [searchText, items])

  // 条件筛选下拉框列表
  const menuList = useMemo(() => {
    return popupTypeMap[popupType]
  }, [popupType])

  return (
    <LayerContainer>
      <div className="loan-wrapper">
        <div className="search-bar-wrapper">
          <SearchBar
            showCancelButton
            onClear={handleCancel}
            onChange={handleSearch}
            onCancel={handleCancel}
            value={searchText}
            placeholder={i18n.get('搜索标题')}
          />
        </div>
        {showDropDown && (
          <EKBDropDown
            downContentClassName={popupType === 'type' ? 'dropdown-content has-icon' : 'dropdown-content'}
            show={showDropDown}
            activeType={menuTypeActiveTypeMap[popupType]}
            menuList={menuList}
            fnChangeType={handleSortChange}
            fnCancel={handleCloseDropDown}
          />
        )}
        {renderGroupHeader()}
        {loanList.length ? renderList() : renderContentNullBody()}
        <div className="submit">
          <Button block category="primary" size="large" onClick={handleSubmit.bind(this)}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    </LayerContainer>
  )
}

export default ManualRepayPopup
