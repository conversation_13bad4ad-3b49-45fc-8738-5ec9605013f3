/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/7 上午11:12
 */
@import '../../styles/ekb-colors';

.city_wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  background: @gray-1;
}

.city_search {
  flex-shrink: 0;
}

.city_selected_wrap {
  padding: 0 40px;
  overflow-y: auto;
}

.city_selected {
  padding: 20px 0;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 2px solid @gray-3;
}

.city_search_result {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: @gray-6;
  font-size: 28px;
}

.city_search_result_loading {
  width: 180px;
  height: 128px;
}

.city_search_result_img {
  width: 128px;
  height: 128px;
}

.city_list {
  padding-left: 40px;
  flex: 1;
  min-height: 300px;
  overflow-y: auto;
  font-size: 28px;
}

.city_title {
  height: 100px;
  line-height: 100px;
  color: @gray-6;
}

.city_item {
  height: 100px;
  line-height: 100px;
  border-bottom: 2px solid @gray-3;
  :first-child {
    color: @gray-9;
  }
  :last-child {
    margin-left: 32px;
    color: @gray-6;
  }
}

.city_btn {
  margin: 32px;
  flex-shrink: 0;
}

.city_tag {
  margin-bottom: 16px;
  margin-right: 16px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  height: 64px;
  font-size: 28px;
  color: @gray-9;
  border-radius: 8px;
  white-space: nowrap;
  background-color: @gray-3;
  border: solid 2px @gray-4;
}

.city_tag_del {
  margin-left: 20px;
  transform: rotate(-315deg);
  color: @gray-7;
}
