import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from './enhance-title-hook'
const { getSelectableDepObj } = app.require('@components/utils/fnInitalValue')
import { DepartmentSelector, searchInFields } from '@hose/pro-eui-mobile-components'
import { iteration } from '@ekuaibao/helpers'
import { compact } from 'lodash'
import '@hose/pro-eui-mobile-components/lib/index.css';

@EnhanceConnect(state => ({
  departments: state['@common'].departments.data,
  departmentsByParent: state['@common'].departmentsByParent.data,
  searchDeptList: state['@common'].searchDeptList.data,
  departmentsVisibility: state['@common'].departmentsVisibility.data,
  departmentsVisibilityByParent: state['@common'].departmentsVisibilityByParent.data
}))
@EnhanceTitleHook()
export default class SelectDepartment extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { deptList: null, deptTreeForSearch: [], flatteningIds: undefined }
  }

  componentDidMount() {
    this.getDepartments(this.props)
  }

  getDepartments = props => {
    let {
      dataSource, // 档案关系依赖性返回值
      isVisibilityDep, // 部门可见效（钉钉平台）
      onlyBelongDepartment,
      canSelectParent,
      submitter,
      departmentsVisibility,
      departments
    } = props
    let deptList = isVisibilityDep ? departmentsVisibility : departments
    deptList = dataSource || deptList

    // 当选择了「只可选择所属部门」和「使用档案关系依赖性」时，dataSource存在假值（null），需要过滤
    if (dataSource && onlyBelongDepartment) {
      deptList = this.fnRemoveFalsyDeep(deptList)
    }

    const visibleDepObj = getSelectableDepObj({ onlyBelongDepartment, canSelectParent }, deptList, submitter)
    this.setState({ flatteningIds: visibleDepObj.selectableDepIdArr })

    // 当选择了「只可选择最末级」「使用档案关系依赖性」未选择「只可选择所属部门」时，
    // visibleDepObj.selectableDepIdArr为undefined，会层级展示部门,
    // 需要根据visibleDepObj.deptsAfterFilter过滤可选部门id，实现平铺展示
    if (!canSelectParent && dataSource && !onlyBelongDepartment) {
      const selectableDepIdArr = []
      iteration(visibleDepObj.deptsAfterFilter, item => {
        if (item.selectable) {
          selectableDepIdArr.push(item.id)
        }
      })
      this.setState({ flatteningIds: selectableDepIdArr })
    }

    // 层级展示时，添加叶子节点标识
    if (this.isShowLayerDep(props)) {
      iteration(deptList, item => {
        if (item.children && item.children.length > 0) {
          item.isLeaf = false
        } else {
          item.isLeaf = true
        }
      })
    }
    this.setState({ deptList })
  }

  /**
   * 部门是否层级展示
   */
  isShowLayerDep = ({ dataSource, onlyBelongDepartment, canSelectParent }) => {
    if (onlyBelongDepartment) {
      return false
    }
    if (canSelectParent) {
      return true
    }
    return !dataSource
  }

  /**
   * 过滤假值（null）
   */
  fnRemoveFalsyDeep = data => {
    if (!Array.isArray(data) || data.length === 0) return data
    data = compact(data)
    data.forEach(item => {
      item.children = this.fnRemoveFalsyDeep(item.children)
    })
    return data
  }

  handleSelect = departments => {
    if (departments.length > 0) {
      this.props.layer.emitOk(departments[0])
    }
  }

  handleSearch = async (searchValue ) => {
    const { isVisibilityDep, departments, canSelectParent  } = this.props
    const { deptList } = this.state
    let dataSource = deptList
    // 可见性部门搜索的源数据获取
    if (isVisibilityDep) {
      const config = await app.dataLoader('@common.organizationConfig').load()
      // 通讯录中配置搜索范围不受可见性限制搜索范围为全部部门
      if (config?.filterScope === 'UNLIMITED') {
        dataSource = departments
      }
    }
    return searchTreeBFS(dataSource, searchValue, !canSelectParent)
  }
  getSearchAction = () => {
    const { onlyBelongDepartment, dataSource } = this.props
    // 外部数据源或者只可选择所属部门时，使用组件中的搜索（搜索的数据源是传入组件的数据源）
    if (!dataSource && !onlyBelongDepartment) {
      return this.handleSearch
    }
    return undefined
  }
  // 获取公司顶级部门id，顶级部门默认选中
  getRootDeptId = () => {
    const { deptList } = this.state
    const { departments } = this.props
    // 如果第一个部门是公司且有子部门时默认选中
    const first = deptList?.[0]?.children.length > 0 && deptList?.[0].id
    const rootId = departments?.[0]?.id
    if (first && first === rootId) {
      return first
    }
    return undefined
  }

  render() {
    const { deptList, flatteningIds } = this.state
    const { canSelectParent } = this.props
    return (
      <DepartmentSelector
        departments={deptList}
        flatteningIds={flatteningIds}
        leafOnly={!canSelectParent}
        onSelect={this.handleSelect}
        activeId={this.getRootDeptId()}
        onSearch={this.getSearchAction()}
      />
    )
  }
}

const searchTreeBFS = (tree, searchValue, leafOnly) => {
  if (!tree.length || !searchValue) {
    return []
  }
  // derived是部门选择器中使用用来展示路径
  let queue = [...tree].map(node => ({ ...node, derived: [node] }))
  const result = []
  while (queue.length) {
    let node = queue.shift()
    if (searchInFields(node, searchValue)) {
      if (leafOnly && !node.children.length) {
        result.push(node)
      } else if (!leafOnly) {
        result.push(node)
      }
    }
    if (node.children) {
      queue = [...queue, ...[...node.children].map(child => ({ ...child, derived: [...node.derived, child] }))]
    }
  }
  return result
}
