import { app } from '@ekuaibao/whispered'
/**************************************
 * Created By LinK On 2019/12/12 18:38.
 **************************************/

import React from 'react'
import styles from './PayeeItem.module.less'
const EKBIcon = app.require('@elements/ekbIcon')
import { payeeSortImgMap } from '../../lib/enums'
import { toast } from '../../lib/util'

interface PayeeProps {
  accountName: string
  accountNo: string
  defaultChannel: string
  active: true
  bank: string
  bankLinkNo?: string
  branch: string
  cardNo: string
  certificateNo: string
  certificateType: string
  city: string
  code: string
  detail: any
  corporationId: string
  createTime: number
  icon: string
  id: string
  isDefault: boolean
  logs: any[]
  name: string
  nameSpell: string
  owner: string
  province: string
  sort: string
  staffId?: any
  type: string
  unionBank: string
  unionIcon: string
  updateTime: number
  version: number
  visibility: any
}

interface Props {
  selected?: boolean
  payeeInfo: PayeeProps
  onlyOffline?: boolean
  dynamicChannelMap?: any
  fnHandleLineClick?: (e: any, account: PayeeProps) => void
}

export default function(props: Props) {
  const { payeeInfo, fnHandleLineClick, onlyOffline = false, selected, dynamicChannelMap = {} } = props
  const { icon, sort, bank, unionBank, id, accountName, accountNo, defaultChannel, code, name, remark } = payeeInfo
  const defaultChannelValue = onlyOffline ? 'OFFLINE' : defaultChannel || 'OFFLINE'
  const accountCode = code ? '(' + code + ')' : '' //@i18n-ignore
  const bankName = bank || unionBank
  const iconName = payeeSortImgMap[sort]
  const _remark = remark?.length < 6 ? remark : remark?.substr(0, 5) + '...'
  let wrapClass = styles['payeeItem-wrapper']
  if (sort === 'ALIPAY' || sort === 'OVERSEABANK') {
    wrapClass += ' ' + styles['payeeItem-wrapper-blue']
  } else if (sort === 'WEIXIN') {
    wrapClass += ' ' + styles['payeeItem-wrapper-green']
  }
  const nameValue = name ? name : accountName
  wrapClass = selected ? wrapClass + ' ' + styles['payeeItem-wrapper-selected'] : wrapClass

  return (
    <div key={id} className={wrapClass} onClick={(e: any) => fnHandleLineClick(e, payeeInfo)}>
      <div className="account-icon-wrap">
        <EKBIcon name={iconName} className="account-bg-icon" />
      </div>
      <div className="account-selected-mask">
        <EKBIcon name="#EDico-check-circle" className="account-selected-icon" />
      </div>
      <div className="payeeItem-header">
        <div className="payee-icon-box">
          <img className="payee-icon" src={icon} />
        </div>
        {bankName && <span>{bankName}</span>}
        {accountNo && <span>{formatCardNo(accountNo)}</span>}
      </div>
      <div className="payeeItem-content">
        <div className="payeeItem-content-inner">
          <span>{nameValue}</span>
          {accountCode && <span className="payeeItem-code">{accountCode}</span>}
        </div>
      </div>
      <div className="payeeItem-footer">
        <EKBIcon name={dynamicChannelMap[defaultChannelValue]?.icon} className="account-bg-icon" />
        <span>{dynamicChannelMap[defaultChannelValue]?.name}</span>
        {remark && (
          <span className="ml-10">
            {remark?.length < 6 ? remark : <span onClick={() => toast.info(remark)}>{_remark}</span>}
          </span>
        )}
      </div>
    </div>
  )
}

const formatCardNo = (cardNo: string) => {
  if (/[^0-9]/.test(cardNo)) {
    return cardNo
  }
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1 ')
}
