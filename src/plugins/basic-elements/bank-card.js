import { app } from '@ekuaibao/whispered'
import './bank-card.less'
import React, { PureComponent } from 'react'
// import EKBIcon from '../../elements/ekbIcon'
const EKBIcon = app.require('@elements/ekbIcon')

export default class BankCard extends PureComponent {
  handleSelectType = () => {
    const { dataSource } = this.props
    this.props.onSelect && this.props.onSelect(dataSource)
  }

  handleLineClick = () => {
    const { dataSource } = this.props
    this.props.onLineClick && this.props.onLineClick(dataSource)
  }

  formatCardNo = cardNo => {
    if (/[^0-9]/.test(cardNo)) {
      return cardNo
    }
    return cardNo.replace(/(\d{4})(?=\d)/g, '$1 ')
  }

  render() {
    const { dataSource, shouldComplement = false } = this.props
    const owner = dataSource.owner === 'CORPORATION' ? i18n.get('企业') : dataSource.staffId && dataSource.staffId.name
    const icon = !!dataSource.unionBank ? dataSource.unionIcon : dataSource.icon
    return (
      <div className="ekb-bank-card">
        <div className="tp" onClick={this.handleSelectType}>
          <div>{dataSource.name || dataSource.accountName}</div>
          {this.props.onSelect && <div>...</div>}
        </div>
        <div className="item-w" onClick={this.handleLineClick}>
          <div className="payee-item">
            <div className="md">
              <div>{this.formatCardNo(dataSource.cardNo || dataSource.accountNo)}</div>
            </div>
            <div className="l1">
              <div className="bank-img">
                <img className="payee-icon" src={icon} />
                <span>{dataSource.unionBank || dataSource.bank}</span>
              </div>
              {shouldComplement && (
                <div className="complement-warning">
                  <EKBIcon name="#EDico-plaint-circle" className="warning" />
                  <span>{i18n.get('待补充')}</span>
                </div>
              )}
            </div>
            {!dataSource.unionBank && (
              <div className="bm mb-4">
                <div className="fs-14">{dataSource.branch}</div>
              </div>
            )}
          </div>
          <div className="card-bottom-wrapper">
            {owner ? (
              <div className="flex-1">
                <span className="fs-14">{i18n.get('所有者：')}</span>
                <span className="fs-14">{owner}</span>
              </div>
            ) : null}
            {dataSource.isDefault && <div className="default">{i18n.get('默认')}</div>}
          </div>
        </div>
      </div>
    )
  }
}
