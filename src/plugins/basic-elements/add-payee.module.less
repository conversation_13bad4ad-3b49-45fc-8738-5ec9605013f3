/**************************************************
 * Created by nanyuanting<PERSON> on 8/23/16 11:55.
 **************************************************/
@import '../../styles/ekb-colors';
@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.add-payee-view-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  :global {
    .header-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--eui-bg-base);
      padding: 32px;

      .header-select-item {
        flex: 1;
        height: 144px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: @radius-3;
        background-color: @color-white-1;
        box-shadow: 0 0 0 1px rgba(29, 43, 61, 0.03), 0 2px 8px 0 rgba(29, 43, 61, 0.09);

        &:first-child {
          margin-right: @space-4;
        }

        .am-checkbox-wrapper {
          margin-right: @space-5;
        }

        .disabled-label {
          opacity: 0.3;
        }
      }

      .l,
      .r {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;

        div:last-child {
          margin-left: 20px;
          height: 28px;
          font-size: 28px;
          line-height: 1;
          color: #54595b;
        }
      }
    }

    .list-wrapper {
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;

      .am-list-body {
        &:after {
          display: none;
        }

        .addPayeeItem_wrapper {
          padding: @space-7 0;
          padding-left: @space-6;
          border-bottom: 2px solid @color-line-2;

          &:last-child {
            border: none;
          }
        }

        .am-list-item.am-input-item {
          height: unset;
          margin-top: @space-6;
          padding-left: 0;
        }

        .am-list-item.am-textarea-item {
          padding-left: 0;
        }

        .am-list-item {
          .am-list-line {
            position: relative;
            display: flex;
            align-items: center;

            &::after {
              display: none;
            }
          }
        }

        .intelligent-filling-btn {
          margin: 24px 32px;
          width: calc(100% - 64px);
        }

        .intelligent-filling-content {
          margin: 24px 32px;

          .text-area-content {
            position: relative;

            .text-area {
              border-radius: 12px;
              height: 240px;

              .eui-text-area-element {
                height: 144px;
                overflow-y: scroll;
              }
            }

            .btns {
              position: absolute;
              bottom: 16px;
              right: 24px;
            }
          }

          .tips {
            margin-top: 8px;
            color: var(--eui-text-caption, rgba(29, 33, 41, 0.70));
            font-size: 28px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px;

            .blue {
              margin-left: 24px;
              color: var(--eui-primary-pri-500, #2555FF);
            }
          }
        }
      }

      .item {
        display: flex;
        height: 102px;
        justify-content: center;
        align-items: center;

        .label {
          width: 160px;
          font-size: 26px;
          line-height: 1;
          color: #a2abaf;
          margin-left: 40px;
        }

        .input {
          flex: 1;
          height: 100%;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          border-bottom: 2px solid #f3f3f3;
          font-size: 28px;
        }
      }

      .select-area.am-list-item.am-list-item-middle {
        margin-top: @space-6;
        padding-left: 0;
      }

      .tip-content {
        font-size: 24px;
        font-weight: 400;
        color: rgba(29, 43, 61, 0.3);
        line-height: 34px;
        margin: 16px 14px 0 14px;
        padding-bottom: 100px;

        .attention-tip {
          margin-top: 8px;
        }

        .about-reason {
          color: var(--brand-base);
          margin-left: 4px;
        }
      }

      .switch-button-wrapper {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: space-evenly;
        border-bottom: 2px solid rgba(29, 43, 61, 0.15);

        button {
          text-align: center;
          height: 80px;
          font-size: 28px;
          font-weight: 400;
          color: rgba(29, 43, 61, 0.75);
          line-height: 40px;
          flex: 1;
          border: none;
          background: none;
        }

        span {
          width: 2px;
          height: 50px;
          background-color: rgba(29, 43, 61, 0.15);
        }

        .active {
          color: var(--brand-base);
        }
      }

      .select-area .am-list-line {
        .am-list-content {
          flex: initial;
        }

        .am-list-extra {
          flex: 1;
          text-align: left;
          color: #54595b;
          font-size: 28px;
        }

        .placeholder-color {
          color: #bbbbbb;
        }
      }

      .bank {
        height: auto;

        &.am-list-item.am-input-item {
          &:after {
            display: none;
          }
        }

        .thePlaceHolder {
          color: #bbbbbb;
          font-size: 28px;
        }

        .img-arrow {
          margin-right: 70px;
          width: 44px;
          height: 100px;
        }

        .bank-icon {
          width: 50px;
          height: 50px;
        }

        .bank-name {
          margin-left: 20px;
          color: #54595b;
          font-size: 28px;
        }

        .branch-name {
          color: #54595b;
          font-size: 28px;
          flex: 1;
          padding-right: 32px;
        }

        .branch-clear {
          position: absolute;
          right: 70px;
          top: 24px;
          width: 44px;
        }

        &.disabled {
          background: #f7f7f7;
        }

        .disabled-label {
          color: #bbbbbb;
        }
      }
    }

    .text-error {
      height: 0.42rem;
      width: 0.42rem;
      margin-left: 0.12rem;
      background-image: url('./images/error-warning.svg');
      background-size: 0.42rem auto;
      position: absolute;
      right: 30px;
      top: 28px;
    }

    .text-top {
      top: 28px;
    }

    .am-list-arrow {
      margin-right: 40px;
    }

    .bank-other {
      position: relative;

      .am-input-clear {
        margin-right: 30px;
      }

      img {
        position: absolute;
        width: 40px;
        height: 40px;
        top: 25px;
        right: 10px;
      }
    }

    .action-wrapper {
      position: relative;
      padding: 20px 36px;
      display: flex;
      gap: 32px;
      background-color: var(--eui-bg-float);
      box-shadow: var(--eui-shadow-up-1);
    }
  }
}

.dialog-wrap {
  display: flex;
  flex-direction: column;

  :global {
    span {
      color: var(--eui-text-caption, rgba(29, 33, 41, 0.70));
      font-size: 32px;
      font-style: normal;
      font-weight: 400;
      line-height: 48px;
    }
  }
}