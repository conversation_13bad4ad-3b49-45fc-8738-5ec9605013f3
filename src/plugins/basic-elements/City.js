import { app } from '@ekuaibao/whispered'
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/5 下午4:27
 */

import React from 'react'
import EnhanceTitleHook from './enhance-title-hook'
import { EnhanceLayer } from '@ekuaibao/enhance-layer-manager-mobile'
import { Button, SearchBar } from 'antd-mobile'

import { app as api } from '@ekuaibao/whispered'

import styles from './City.module.less'
// import SVG_SEARCH from '../../images/city-search.svg'
const SVG_SEARCH = app.require('@images/city-search.svg')
// import SVG_LOADING from '../../images/city-search-loading.gif'
const SVG_LOADING = app.require('@images/city-search-loading.gif')
import { Fetch } from '@ekuaibao/fetch'
import { debounce, filterCity } from '../../lib/util'

@EnhanceTitleHook(i18n.get('自定义城市'))
class City extends React.Component {
  constructor(props) {
    super(props)
    let selectCity = this.props.value ? this.fnValueToShow(this.props.value) : []
    this.state = {
      searchKey: '', //搜索关键字
      searchList: [], //搜索结果
      selectCity: selectCity, //已选择的城市
      loading: true, //搜索状态
      focused: true //焦点状态
    }
    props.overrideGetResult(this.getResult)
  }

  fnValueToShow(valueString) {
    let value = ''
    if (valueString) {
      try {
        value = JSON.parse(valueString)
      } catch (e) {
        return valueString
      }
    }
    value.map(item => (item.id = item.key))
    return value
  }

  fnSearch = val => {
    api.invokeService('@common:search:city', val).then(data => {
      let searchList = data.items
      const { tripType } = this.props
      searchList = searchList.filter(filterCity(tripType))
      this.setState({ loading: false, searchKey: val, searchList })
    })
  }

  fnFormatCityLabel(label) {
    return label.replace(/^中国,\s*/, '').replace(/,\s*/g, '/')
  }

  handleChangeSearch = debounce(val => {
    this.setState({ loading: true })
    if (val !== '') {
      return this.fnSearch.call(this, val)
    }
    return this.setState({ loading: false, searchKey: '', searchList: [] })
  }, 500)

  handleCancel() {
    this.props.layer.emitCancel()
  }

  handleClick(data) {
    let { selectCity } = this.state
    let { isMulti } = this.props
    let { id } = data

    let isSame = selectCity.filter(el => el.id === id) //校验重复
    if (isSame.length === 0) {
      if ((!isMulti && selectCity.length === 0) || isMulti) {
        //单选并且已选择0个 或者 多选
        selectCity.push(data)
      }
    }
    this.setState({ selectCity: selectCity })
    if (!isMulti) {
      return this.props.layer.emitOk()
    }
  }

  handleClickTag(data) {
    let { id } = data
    let { selectCity } = this.state
    selectCity = selectCity.filter(el => el.id !== id)
    this.setState({ selectCity })
  }

  handleGetValue = () => {
    return this.props.layer.emitOk()
  }

  handleCancelFocus = () => {
    this.setState({
      focused: false
    })
  }

  getResult = () => {
    let { selectCity } = this.state
    if (selectCity.length > 0) {
      let valueArr = []
      selectCity.forEach(el => {
        valueArr.push({ key: el.id, label: el.label })
      })
      let valueString = JSON.stringify(valueArr)
      return valueString
    } else {
      return null
    }
  }

  renderSearchBar() {
    let { focused } = this.state
    return (
      <div className={styles.city_search}>
        <SearchBar
          placeholder={i18n.get('搜索城市(市/县/海外城市）')}
          focused={focused}
          onFocus={this.handleCancelFocus.bind(this)}
          onChange={this.handleChangeSearch.bind(this)}
          onCancel={this.handleCancel.bind(this)}
        />
      </div>
    )
  }

  renderSelectTags(isMulti) {
    let { selectCity } = this.state

    if (isMulti && selectCity.length > 0) {
      return (
        <div className={styles.city_selected_wrap}>
          <div className={styles.city_selected}>
            {selectCity.map((el, i) => {
              let { name, fullName = '', key, label } = el
              let showLabel = key ? label : this.fnFormatCityLabel(fullName)
              el.label = showLabel
              return (
                <div className={styles.city_tag} key={i} onClick={this.handleClickTag.bind(this, el)}>
                  {showLabel}
                  <div className={styles.city_tag_del}>+</div>
                </div>
              )
            })}
          </div>
        </div>
      )
    }
    return null
  }

  renderSearchResult() {
    let { searchKey, searchList, loading } = this.state
    if (searchKey === '') {
      return <div className={styles.city_search_result}>{i18n.get('搜索结果会在这里显示')}</div>
    }
    if (loading === true) {
      return (
        <div className={styles.city_search_result}>
          <div>
            <img className={styles.city_search_result_loading} src={SVG_LOADING} />
          </div>
          <div>&nbsp;</div>
        </div>
      )
    }
    if (searchKey !== '' && searchList.length === 0 && loading === false) {
      return (
        <div className={styles.city_search_result}>
          <div>
            <img className={styles.city_search_result_img} src={SVG_SEARCH} />
          </div>
          <div>{i18n.get('没有找到您所要的结果')}</div>
        </div>
      )
    }
    return (
      <div className={styles.city_list}>
        {searchList.length > 0 && <div className={styles.city_title}>{i18n.get('搜索结果')}</div>}
        {searchList.map((el, i) => {
          return this.renderCityByLang(el, i)
        })}
      </div>
    )
  }

  renderCityByLang(el, i) {
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage
    let { enName, name, fullName = '' } = el
    let label = lang === 'en-US' ? enName : this.fnFormatCityLabel(fullName)
    el.label = label
    if (lang === 'en-US') {
      return (
        <div key={i} onClick={this.handleClick.bind(this, el)} className={styles.city_item}>
          <span>{enName}</span>
        </div>
      )
    }

    return (
      <div key={i} onClick={this.handleClick.bind(this, el)} className={styles.city_item}>
        <span>{name}</span>
        <span>{fullName}</span>
      </div>
    )
  }

  renderBtnWrap(isMulti) {
    if (isMulti) {
      return (
        <div className={styles.city_btn}>
          <Button onClick={this.handleGetValue.bind(this)} type="primary">
            {i18n.get('完成')}
          </Button>
        </div>
      )
    }
    return null
  }

  render() {
    let { isMulti } = this.props
    return (
      <div className={styles.city_wrap}>
        {this.renderSearchBar.call(this)}
        {this.renderSelectTags.call(this, isMulti)}
        {this.renderSearchResult.call(this)}
        {this.renderBtnWrap.call(this, isMulti)}
      </div>
    )
  }
}

City.defaultProps = {
  isMulti: true //是否多选
}

export default City
