@import '../../../styles/ekb-colors';

.express-add-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #F2F2F2;
  :global {
    .express-add-item {
      padding: 24px 32px;
      background-color: #FFFFFF;
      text-align: left;
      color: @black-65;
      margin-bottom: 16px;
      .express-add-item-count {
        color: @black-65;
        font-size: 24px;
        text-align: right;
        span {
          color: @black-25;
        }
      }
      .express-add-item-error {
        color: #f5222d;
        font-size: 24px;
        height: 40px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .icon {
          width: 28px;
          height: 28px;
          margin-right: 8px;
          fill: #f5222d;
        }
      }
      .express-add-item-label {
        font-size: 28px;
        line-height: 44px;
      }
      .express-add-item-input {
        line-height: 48px;
        padding: 0;
        textarea {
          font-size: 32px;
        }
        .am-input-control {
          input {
            font-size: 32px;
          }
        }
        &::after {
          display: none;
        }
      }
      .express-add-scan-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .express-add-item-input {
          flex: 1;
        }
        .iconBox {
          display: flex;
          width: 48px;
          height: 48px;
          margin-left: 32px;
        }
      }
    }
  }
}
