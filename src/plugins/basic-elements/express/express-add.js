import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import styles from './express-add.module.less'
import EnhanceTitleHook from '../../basic-elements/enhance-title-hook'
import { showLoading, hideLoading } from '../../../lib/util'
import { InputItem, TextareaItem } from 'antd-mobile'
import { Dialog } from '@hose/eui-mobile'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
const ActionsPanel = app.require('@elements/ActionsPanel')
import { expressSubmit, cancel } from '../../basic-elements/basic-state-const'
import { getV } from '@ekuaibao/lib/lib/help'
const i18n = window.i18n

@EnhanceTitleHook(props => props.label)
@EnhanceFormCreate()
export default class ExpressAdd extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {
    const { type, remark, num } = this.props
    if (type === 'edit') {
      this.props.form.setFieldsValue({ expressNo: num, remark })
    }
  }

  handleChange = val => {
    this.phoneError = ''
    let phoneRequired
    if (val.startsWith('SF')) {
      phoneRequired = true
    } else {
      phoneRequired = false
    }
    this.setState({ phoneRequired })
  }

  handleScan = () => {
    return api.invokeService('@layout:invoice:scan').then(code => {
      if (!getV(code, 'text')) return
      this.props.form.setFieldsValue({ expressNo: code.text })
    })
  }

  _batchAction = values => {
    const { type, backlogIds } = this.props
    const num = type === 'skipExpress' ? null : values.expressNo
    showLoading()
    return api
      .invokeService('@approve:send:express', {
        backlogIds,
        num,
        ...values
      })
      .then(res => {
        hideLoading()
        this.props.layer.emitOk(res)
        const list = (res.value && res.value.errors) || []
        const errorList = list.filter(item => item.resultCode !== 'OK')
        if (errorList.length) {
          const errors = getV(data, 'value.error', [])
          const message = errors.length ? errors[0].message : ''
          return Dialog.length({ title: i18n.get('操作失败'), content: message })
        }
      })
  }

  _buttonsClick = data => {
    if (data.name === 'submit') {
      this.props.form.validateFieldsAndScroll((err, values) => {
        if (err) return
        const { type, backlog, backlogIds, flowIds } = this.props
        if (type === 'supplement' || type === 'edit') return this.props.layer.emitOk(values)
        const num = type === 'skipExpress' ? null : values.expressNo
        const phone = type === 'skipExpress' ? null : values.phone
        const isBatch = !!backlogIds
        let selectData = backlogIds
        if (isBatch && flowIds?.length && flowIds?.length === backlogIds?.length) {
          selectData = backlogIds.map((id, index) => {
            return { id, flowId: { id: flowIds[index] } }
          })
        }
        let data = isBatch ? { isBatch: true, selectData, billTitle: i18n.get('批量寄送') } : { data: { backlog } }
        if (values.expressNo || type === 'skipExpress') {
          return api.open('@basic:PlanResolve', data).then(value => {
            if (backlogIds) {
              return this._batchAction({
                ...value,
                ...values,
                comment: value.comment,
                attachments: value.attachments,
                autographImageId: value.autographImageId
              })
            }
            showLoading()
            return api
              .invokeService('@approve:send:express', {
                backlogIds: [backlog.id],
                num,
                phone,
                remark: values.remark,
                comment: value.comment,
                attachments: value.attachments,
                autographImageId: value.autographImageId,
                participants:value.participants,
                keepItSecret:value.keepItSecret,
                reminder:value.reminder
              })
              .then(data => {
                hideLoading()
                this.props.layer.emitCancel()
                const list = (data.value && data.value.errors) || []
                const errorList = list.filter(item => item.resultCode !== 'OK')
                if (errorList.length) {
                  return Dialog.alert({ title: i18n.get('操作失败'), content: data.value.errors[0].message })
                }
                api.go(-1)
              })
          })
        }
      })
    } else {
      this.props.layer.emitCancel()
    }
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  checkNumber = (rule, value, callback) => {
    const pattern = new RegExp(/^[A-Za-z0-9]+$/)
    let errMsg
    if (!value) {
      errMsg = i18n.get('请输入寄送单号')
    } else if (!pattern.test(value)) {
      errMsg = i18n.get('请输入正确格式寄送单号')
    } else if (value.trim().length < 3) {
      errMsg = i18n.get('最短寄送单号长度为3')
    } else if (value.trim().length > 30) {
      errMsg = i18n.get('最长寄送单号长度为30')
    }
    this.expressError = errMsg
    callback(errMsg)
  }

  renderExpressItem() {
    const { getFieldDecorator } = this.props.form
    const { type } = this.props
    if (type === 'skipExpress') return null
    const expressNo = getFieldDecorator('expressNo', {
      rules: [
        { required: true, whitespace: true, message: i18n.get('寄送单号不能为空') },
        { validator: this.checkNumber }
      ]
    })
    return (
      <div className="express-add-item">
        <div className="express-add-item-label">{i18n.get('寄送单号')}</div>
        <div className="express-add-scan-item">
          {expressNo(
            <InputItem
              className="express-add-item-input"
              autoFocus={true}
              clear
              rows={1}
              placeholder={i18n.get('请输入寄送单号')}
              onChange={this.handleChange}
            />
          )}
          <div className="iconBox" onClick={this.handleScan}>
            <svg className="icon" aria-hidden="true">
              <use xlinkHref="#EDico-scan" />
            </svg>
          </div>
        </div>
        {this.expressError && (
          <div className="express-add-item-error">
            <svg className="icon" aria-hidden="true">
              <use xlinkHref="#EDico-plaint-circle-o" />
            </svg>
            {this.expressError}
          </div>
        )}
      </div>
    )
  }

  checkPhone = (rule, value, callback) => {
    const pattern = new RegExp(/^1\d{10}$/)
    let errMsg
    if (!value) {
      errMsg = i18n.get('请输入寄件人或收件人手机号')
    } else if (!pattern.test(value)) {
      errMsg = i18n.get('手机格式不正确')
    }
    this.phoneError = errMsg
    callback(errMsg)
  }

  renderPhoneItem = () => {
    const { getFieldDecorator } = this.props.form
    const { type } = this.props
    if (type === 'skipExpress') return null
    const expressPhone = getFieldDecorator('phone', {
      rules: [
        { required: true, whitespace: true, message: i18n.get('请输入寄件人或收件人手机号') },
        { validator: this.checkPhone }
      ]
    })
    return (
      <div className="express-add-item">
        <div className="express-add-item-label">{i18n.get('寄件人或收件人手机号')}</div>
        <div className="express-add-scan-item">
          {expressPhone(
            <InputItem className="express-add-item-input" clear rows={1} placeholder={i18n.get('请输入手机号')} />
          )}
        </div>
        {this.phoneError && (
          <div className="express-add-item-error">
            <svg className="icon" aria-hidden="true">
              <use xlinkHref="#EDico-plaint-circle-o" />
            </svg>
            {this.phoneError}
          </div>
        )}
      </div>
    )
  }

  checkRemark = (rule, value, callback) => {
    const { type } = this.props
    const remarkRequired = type === 'skipExpress'
    if (value) {
      this.remarkError = ''
      this.remarkLength = value.trim().length
    } else {
      this.remarkLength = 0
    }
    if (remarkRequired) {
      if (!value) {
        this.remarkError = i18n.get('请输入自定义备注信息')
        return callback(i18n.get('请输入自定义备注信息'))
      }
    }
    callback()
  }

  renderRemarkItem() {
    const { getFieldDecorator } = this.props.form
    const remark = getFieldDecorator('remark', { rules: [{ validator: this.checkRemark }] })

    return (
      <div className="express-add-item">
        <div className="express-add-item-label">{i18n.get('备注')}</div>
        {remark(
          <TextareaItem
            className="express-add-item-input"
            autoFocus={true}
            autoHeight={true}
            count={200}
            style={{ fontSize: 16 }}
            placeholder={i18n.get('自定义备注信息')}
          />
        )}
        <div className="express-add-item-count">
          {this.remarkLength || 0}
          <span>{'/200'}</span>
        </div>
        {this.remarkError && (
          <div className="express-add-item-error">
            <svg className="icon" aria-hidden="true">
              <use xlinkHref="#EDico-plaint-circle-o" />
            </svg>
            {this.remarkError}
          </div>
        )}
      </div>
    )
  }

  render() {
    const { phoneRequired } = this.state
    const rightButtons = [expressSubmit(), cancel()]
    return (
      <div className={styles['express-add-wrapper']}>
        {this.renderExpressItem()}
        {phoneRequired && this.renderPhoneItem()}
        {this.renderRemarkItem()}
        <ActionsPanel rightButtons={rightButtons} leftButtons={[]} buttonAction={this._buttonsClick} />
      </div>
    )
  }
}
