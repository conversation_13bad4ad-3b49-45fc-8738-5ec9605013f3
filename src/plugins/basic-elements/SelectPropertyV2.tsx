import { app, app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import EnhanceTitleHook from './enhance-title-hook'
import { DataItem } from '../../elements/puppet/SelectTreeNodeV2'
import { Fetch } from '@ekuaibao/fetch'
import { getV, isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { uuid } from '@ekuaibao/helpers'
const SelectTreeNodeV2 = app.require<any>('@elements/puppet/SelectTreeNodeV2')
const SelectTreeNodeV3 = app.require<any>('@elements/puppet/TreeSelect/SelectTreeNodeV3')

import { enableRecordSelectOptimization } from '../../lib/featbit'

interface SelectPropertyProps {
  data: { items?: DataItem[] }
  canSelectParent?: boolean
  showBottom?: boolean
  type?: string
  layer: any
  overrideGetResult: any
  onCancel?(): void
  multiple?: boolean
  selected: any[]
  hideCode?: boolean
  submitterId?: string
  blackListCanNotSelect: any[]
}

interface SelectPropertyState {
  shouldGetCommonGroupList: boolean // 是否包含当前企业，灰度【自定义档案搜索优化】使用
  commonGroupList: any
}

// @ts-ignore
@EnhanceTitleHook(props => {
  const { label } = props
  return label || i18n.get('请选择{__k0}', { __k0: label })
})
export default class SelectProperty extends Component<SelectPropertyProps, SelectPropertyState> {
  constructor(props: SelectPropertyProps) {
    super(props)
    this.state = {
      shouldGetCommonGroupList: isHongShanTestingEnterprise(Fetch.ekbCorpId),
      commonGroupList: null
    }
    props.overrideGetResult(this.getResult)
  }

  private __result: any

  componentDidMount() {
    this.fnSetDimensionList()
  }

  private fnSetDimensionList = async () => {
    const { data, submitterId } = this.props
    const { shouldGetCommonGroupList } = this.state
    if (!shouldGetCommonGroupList || !data?.items?.length) return
    const dimensionId = data.items[0]?.dimensionId
    if (dimensionId) {
      const dimensionConfig = await api.invokeService('@bill:get:dimension:config', dimensionId)
      const queryMethod = getV(dimensionConfig, 'items[0].queryMethod')
      if (queryMethod === 'LIST' && submitterId) {
        // 判断当前自定义档案是否有常用分组数据
        const commonDimensionConfig = await api.invokeService('@bill:get:common:dimension:config', {
          submitterId,
          dimensionId
        })
        if (commonDimensionConfig?.value) {
          // 获取当前自定义档案的常用分组数据
          const commonDimension = await api.invokeService('@bill:get:common:dimension', { submitterId, dimensionId })
          if (commonDimension?.value) {
            const result = this.formatCommonDimensionList(commonDimension, data.items)
            this.setState({ commonGroupList: result })
          }
        }
      }
    }
  }

  formatCommonDimensionList = (commonDimension: any, items: any[]) => {
    const obj = getV(commonDimension, 'value')
    if (!obj) return null
    // 获取可见范围
    const { canSelectParent } = this.props
    const visibleIds: any = []
    const loop = (items: any[]) => {
      items.forEach(el => {
        if (el.active) {
          if (canSelectParent || (!canSelectParent && !el.children?.length)) {
            visibleIds.push(el.id)
          }
        }
        if (el.children?.length) loop(el.children)
      })
    }
    loop(items)

    // 分组
    const result: any[] = []
    Object.keys(obj).forEach(key => {
      const arr = obj[key].filter(el => visibleIds.includes(el.id))
      if (!arr?.length) return
      result.push(
        {
          isGroupItem: true,
          id: uuid(),
          name: key,
          active: true
        },
        ...arr
      )
    })
    return result
  }

  isDimension = () => {
    const { type } = this.props
    return type?.startsWith('ref:basedata.Dimension') || type?.startsWith('list:ref:basedata.Dimension')
  }

  render() {
    const {
      data,
      canSelectParent = false,
      onCancel,
      type = '',
      showBottom,
      multiple,
      selected,
      hideCode,
      blackListCanNotSelect
    } = this.props
    if (!data || !data.items) {
      return null
    }

    let Component = SelectTreeNodeV2

    if(enableRecordSelectOptimization() && this.isDimension()) {
      Component = SelectTreeNodeV3
    }

    return (
      <Component
        dataSource={data.items}
        canSelectParent={canSelectParent}
        commonGroupList={this.state.commonGroupList}
        type={type}
        showBottom={showBottom}
        multiple={multiple}
        selected={selected}
        onClearData={this.handleClearData}
        onItemClick={this.handleLineClick}
        onCancel={onCancel}
        hideCode={hideCode}
        blackListCanNotSelect={blackListCanNotSelect}
      />
    )
  }

  private handleClearData = () => {
    this.__result = ''
    this.props.layer.emitOk()
  }

  private handleLineClick = (item: DataItem | DataItem[]) => {
    this.__result = item
    this.props.layer.emitOk(item)
  }

  private getResult = () => {
    return this.__result
  }
}
