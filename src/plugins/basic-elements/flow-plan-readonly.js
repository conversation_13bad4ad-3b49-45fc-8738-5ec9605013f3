/**************************************************
 * Created by nany<PERSON>ingfeng on 30/09/2016 15:24.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import EnhanceTitleHook from './enhance-title-hook'

import FLowPlanEditable from './flow-plan-editable'
import { getBoolVariation } from '../../lib/featbit'

@EnhanceConnect(state => ({
  flow: state['@common'].current_flow
}))
@EnhanceTitleHook(i18n.get('全部审批流程'))
export default class Plan4ReadOnly extends PureComponent {
  componentWillMount() {
    const {
      params: { id, type }
    } = this.props
    api.invokeService('@common:get:flow:detail:info', { id, type, checkPermissions: !getBoolVariation('hailiang_loan_permission') })
  }

  render() {
    const {
      flow,
      params: { isReadOnly }
    } = this.props
    let readOnly = true
    if (isReadOnly && isReadOnly === 'false') {
      readOnly = false
    }
    return flow && flow.plan ? <FLowPlanEditable {...this.props} isReadOnly={readOnly} /> : null
  }
}
