import { app } from '@ekuaibao/whispered'
import './ekb-orders.less'
import React, { PureComponent } from 'react'
import { ListView, PullToRefresh, List, Button, Checkbox } from 'antd-mobile'
import { app as api } from '@ekuaibao/whispered'
import remove from 'lodash/remove'
import { getEkbOrders } from './ekb-orders.action'
import key from './key'
import { EnhanceConnect } from '@ekuaibao/store'

//import LayerContainer from '../basic-elements/layer-container'
const LayerContainer = api.require("@basic-elements/layer-container")
// import { thousandBitSeparator } from '../../components/utils/fnThousandBitSeparator'
const { thousandBitSeparator } = app.require('@components/utils/fnThousandBitSeparator')
//@codemod-sync-{thousandBitSeparator}

import IMG_NO_ORDER from './images/ekb-no-orders-data.png'

import { toast, showLoading, hideLoading } from '../../lib/util'

import Big from 'big.js'

const { Item } = List
const dataSource = new ListView.DataSource({
  rowHasChanged: (row1, row2) => row1 !== row2
})
@EnhanceConnect(state => ({
  ekbOrders: state[key.ID].ekbOrders,
  standardCurrency: state['@common'].standardCurrency
}))
export default class EkbOrders extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      dataSource: dataSource.cloneWithRows([]),
      checkedOrders: [],
      loading: true,
      refreshing: false
    }
  }

  componentWillMount() {
    const { ids } = this.props
    showLoading(i18n.get('正在加载订单～'))
    api.dispatch(
      getEkbOrders(ids, () => {
        this.setState({ loading: false })
        hideLoading()
      })
    )
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.ekbOrders && nextProps.ekbOrders.length) {
      this.setState({
        dataSource: dataSource.cloneWithRows([...nextProps.ekbOrders])
      })
    }
  }

  onRefresh() {
    const { ids } = this.props
    this.setState({
      refreshing: true,
      checkedOrders: [],
      dataSource: dataSource.cloneWithRows([])
    })
    api.dispatch(
      getEkbOrders(ids, () => {
        this.setState({ refreshing: false })
      })
    )
  }

  onRowCheckChange = (data, e) => {
    let { checkedOrders } = this.state
    let orders = [...checkedOrders]
    if (e.target.checked) {
      // 增加数据
      orders.push(data)
      data.ekbChecked = true
    } else {
      // 删除数据
      data.ekbChecked = false
      remove(orders, item => {
        return item.id === data.id
      })
    }
    this.setState({ checkedOrders: orders })
  }

  getResult = () => {
    return this.orders
  }

  openHowOrder() {
    api.invokeService('@layout:open:link', 'http://cn.mikecrm.com/Kl56HKX')
  }

  importOrders() {
    let { checkedOrders } = this.state
    if (!checkedOrders.length) {
      toast.fail(i18n.get('请选择要导入的订单'))
      return
    }
    this.orders = checkedOrders
    this.props.layer.emitOk()
  }

  renderNoOrder() {
    const { ekbOrders } = this.props
    const { loading } = this.state
    if (loading || (ekbOrders && ekbOrders.length)) {
      return null
    }

    return (
      <div className="no-orders">
        <img src={IMG_NO_ORDER} alt="" />
        <p>{i18n.get('您暂时没有相关订单')}</p>
        <a onClick={this.openHowOrder}>{i18n.get('了解如何产生订单？')}</a>
      </div>
    )
  }

  renderFooter = () => {
    if (this.state.refreshing) {
      return null
    }
    if (this.props.ekbOrders && this.props.ekbOrders.length > 10) {
      return (
        <div className="list_footer">
          <p>{i18n.get('呀！-.- 没有更多订单了')}</p>
        </div>
      )
    } else {
      return null
    }
  }

  renderOrders() {
    const { ekbOrders } = this.props
    const { loading } = this.state

    if (!ekbOrders) {
      return null
    }
    if (loading || (ekbOrders && !ekbOrders.length)) {
      return null
    }

    const separator = (sectionID, rowID) => <div key={`${sectionID}-${rowID}`} />
    const row = (rowData, sectionID, rowID) => {
      let data = rowData.data
      let route = data.data.routes[0] || {}
      return (
        <Item key={rowID}>
          <div className="orders-item">
            <Checkbox
              className="order-checkbox"
              onClick={e => {
                this.onRowCheckChange(rowData, e)
              }}
            >
              <div className="content">
                <div className="header">
                  {route.depart_city} - {route.arrive_city}
                </div>
                <p>
                  {route.airline_name} {route.flight_no}
                </p>
                <p>{data.dateFormat}</p>
              </div>
              <strong data-symbol={window.CURRENCY_SYMBOL}>{thousandBitSeparator(data.actual_price)}</strong>
            </Checkbox>
          </div>
        </Item>
      )
    }

    return (
      <ListView
        dataSource={this.state.dataSource}
        renderRow={row}
        renderSeparator={separator}
        initialListSize={10}
        pageSize={10}
        pullToRefresh={<PullToRefresh refreshing={this.state.refreshing} onRefresh={this.onRefresh.bind(this)} />}
        scrollRenderAheadDistance={200}
        scrollerOptions={{ scrollbars: true }}
        renderFooter={this.renderFooter}
      />
    )
  }

  renderOrdersFooter() {
    let {
      standardCurrency: { scale, symbol }
    } = this.props
    const { ekbOrders } = this.props
    const { checkedOrders, loading } = this.state
    if (!ekbOrders || !ekbOrders.length || loading) {
      return null
    }

    let totalPrice = new Big(0)
    if (checkedOrders.length) {
      checkedOrders.forEach(order => {
        totalPrice = totalPrice.add(order.data.actual_price)
      })
    }

    totalPrice = totalPrice.gt(0) ? totalPrice.toFixed(scale) : '0.00'

    return (
      <footer className="orders-footer">
        <div className="content">
          <p className="info">
            <strong>{checkedOrders.length}</strong>
            {i18n.get('个行程')} {i18n.get('，共')}
            <strong className="price">{`${symbol}${totalPrice}`}</strong>
          </p>
        </div>
        <Button type="primary" className="import-btn" onClick={this.importOrders.bind(this)}>
          {i18n.get('导入订单', {})}
        </Button>
      </footer>
    )
  }

  render() {
    return (
      <LayerContainer>
        <div className="ekb-orders-wrapper">
          {this.renderNoOrder()}
          {this.renderOrders()}
          {this.renderOrdersFooter()}
        </div>
      </LayerContainer>
    )
  }
}
