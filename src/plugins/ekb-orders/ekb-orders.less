@import '../../styles/layout.less';
@import '../../styles/theme.less';

.ekb-orders-wrapper {
  min-height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9f9;

  .am-list-view-scrollview {
    flex: 1;
  }

  .no-orders {
    height: 100%;
    flex: 1;

    img {
      display: block;
      width: 496px;
      margin: 90px auto 48px;
    }

    a {
      display: block;
      color: var(--brand-base);
    }

    a,
    p {
      text-align: center;
      font-size: @font-size-subhead;
      margin-bottom: 32px;
    }
  }

  .list_footer {
    padding-bottom: 80px;
    text-align: center;

    p {
      margin: 0;
    }
  }

  .am-list-body {
    padding-left: 0;

    .am-list-item {
      padding-left: 0;
    }

    .am-checkbox-inner {
      border: 2px solid #959898;
    }

    .am-checkbox.am-checkbox-checked .am-checkbox-inner {
      border: 2px solid var(--brand-base);
    }

    .am-list-line {
      padding-right: 0;
    }

    .am-list-content {
      padding: 0;
    }
  }

  .am-list-footer {
    min-height: 120px;
  }

  .orders-item {
    .order-checkbox {
      display: flex;
      padding: 32px;
      flex-direction: row;
      align-items: center;
      cursor: pointer;

      &>* {
        pointer-events: none;
      }
    }

    .am-checkbox {
      margin-right: 32px;
    }

    .content {
      flex: 1;

      .header {
        font-size: 28px;
        color: #3a3f3f;

        &::before {
          content: '';
          display: inline-block;
          width: 36px;
          height: 36px;
          margin-right: 12px;
          background: url('./images/icon-flight-order.svg') no-repeat;
          background-size: cover;
          vertical-align: middle;
        }
      }

      p {
        margin: 0;
        font-size: 26px;
        line-height: 1.5;
        color: #bbbdbd;
      }
    }

    strong {
      color: #3a3f3f;
      font-size: 32px;
      font-weight: 500;

      &::before {
        content: attr(data-symbol);
        font-size: 24px;
      }
    }
  }

  .orders-footer {
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 99999;
    display: flex;
    padding: @v-spacing-md @h-spacing-lg;
    box-sizing: border-box;
    background: @fill-base;
    align-items: center;
    border-top: @border-width-sm solid @border-color-base;

    .content {
      flex: 1;
    }

    .select-all {
      font-size: @font-size-base;
      color: @color-text-base;
      vertical-align: middle;

      .am-checkbox {
        margin-right: 20px;
      }
    }

    .info {
      margin: 10px auto 0;
      font-size: @font-size-base;
      color: @color-text-base;

      strong {
        font-weight: 400;
        font-size: @font-size-base;
        color: var(--brand-base);
      }

      .price {
        margin-left: 10px;
      }

      .price::before {
        font-size: @font-size-icontext;
      }
    }

    .import-btn {
      width: 194px;
    }
  }
}