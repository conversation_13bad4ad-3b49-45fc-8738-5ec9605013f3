import { app } from '@ekuaibao/whispered'
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/8/8 下午3:21.
 */
// const SVG_IMPORT_EKB = require('../../../images/import-menu-old-flight.svg')
const SVG_IMPORT_EKB = app.require('@images/import-menu-old-flight.svg')
import { fnOpenEkbOrders } from './utils'

const info = {
  icon: SVG_IMPORT_EKB,
  title: i18n.get('机票（旧）'),
  group: 'ekbMall',
  type: 'ekbFlight',
  onClick(...args) {
    return fnOpenEkbOrders(...args).then(result => {
      return { type: 'ekb', data: result }
    })
  }
}

export default info
