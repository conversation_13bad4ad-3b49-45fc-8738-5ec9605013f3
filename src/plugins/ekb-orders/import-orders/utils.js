import { app as api } from '@ekuaibao/whispered'

function openFeetype(orders, resolve) {
  api.open('@feetype:SelectFeeTypeModal').then(result => {
    api.invokeService('@feetype:get:feetype', result.id).then(feetype => {
      resolve({ orders, feetype })
    })
  })
}

export function fnOpenEkbOrders(line, data) {
  const { promise, resolve } = window.PromiseDefer()
  const ids = getImportedMallData(data)
  api.open('@ekborders:EKBOrder', { ids }).then(result => {
    openFeetype(result, resolve)
  })
  return promise
}

function getImportedMallData(data) {
  let ids = []
  data?.forEach(o => {
    if (o.feeTypeForm) {
      ids = ids.concat(o.feeTypeForm.orders)
    }
  })
  return ids
}
