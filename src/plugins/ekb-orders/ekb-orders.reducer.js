import { Reducer } from '@ekuaibao/store'
import key from './key'
import moment from 'moment'
import { toast } from '../../lib/util'

const reducer = new Reducer(key.ID, {})

const chineseWeekDayName = {
  Monday: i18n.get('周一'),
  Tuesday: i18n.get('周二'),
  Wednesday: i18n.get('周三'),
  Thursday: i18n.get('周四'),
  Friday: i18n.get('周五'),
  Saturday: i18n.get('周六'),
  Sunday: i18n.get('周日')
}

reducer.handle(key.GET_EKB_ORDERS, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return { ...state }
  }

  let ekbOrders = action.payload.items
  // 当没有 保存／提交 的时候过滤掉已经选择的订单
  if (action.ids && action.ids.length) {
    ekbOrders = ekbOrders.filter(o => {
      return !action.ids.some(id => o.id === id)
    })
  }

  // 格式化时间
  ekbOrders = ekbOrders.map(order => {
    let routes = order.data.data.routes || []
    let departureTime = routes.length && routes[0].depart_time
    departureTime = new moment(departureTime)

    let dateFormat = `${departureTime.format(i18n.get('MM月DD日 HH:mm'))}  ${chineseWeekDayName[
      departureTime.format('dddd')
    ] || ''}`
    order.data.dateFormat = dateFormat

    return order
  })

  return { ...state, ekbOrders: ekbOrders }
})

export default reducer
