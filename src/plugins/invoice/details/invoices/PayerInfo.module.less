.payerinfo-wrapper {
  :global {
    .payerInfo-number {
      display: flex;
      margin-top: 8px;
      align-items: flex-start !important;
    }
    .value-number {
      flex-direction: column;
    }
    .flex {
      display: flex;
    }
    .payerinfo-content {
      display: flex;
      align-items: center;
      margin-top: 8px;
      .label {
        font-size: 28px;
        color: #d37b45;
        width: 200px;
        text-align: left;
        margin-right: 20px;
        flex-shrink: 0;
      }
      .value {
        display: flex;
        font-size: 28px;
        color: #262626;
        .warning-text {
          margin: 6px 0;
          padding: 4px 10px;
          font-size: 24px;
          color: #545454;
          white-space: normal;
          word-break: break-all;
          background-color: #fffbe5;
          border: solid 2px #faefcb;
        }

        img {
          width: 36px;
          height: 36px;
        }
      }
    }
  }
}

.separation-line {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 34px 0;
  :global {
    .info {
      border-radius: 24px;
      background-color: #f5f5f5;
      font-size: 28px;
      color: #8c8c8c;
      padding: 2px 24px;
    }
    .separation {
      width: 9%;
      background-color: #e8e8e8;
      height: 2px;
    }
  }
}

.invoice-details {
  :global {
    .payerinfo-content {
      display: flex;
      .label {
        font-size: 28px;
        color: #d37b45;
        text-align: left;
        margin-right: 20px;
        flex-shrink: 0;
        width: 168px;
      }
      .value {
        overflow: hidden;
        flex: 1;

        .projectDetail {
          overflow: hidden;
          .name {
            display: flex;
            align-items: center;
            font-size: 28px;
            margin-bottom: 8px;
            overflow: hidden;
            .title {
              flex: 1;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .dot {
              width: 12px;
              height: 12px;
              border-radius: 12px;
              background-color: #d37b45;
              margin-right: 16px;
              flex-shrink: 0;
            }
            .count {
              flex-shrink: 0;
            }
          }

          .item-line {
            width: 100%;
            height: 2px;
            background-color: #e8e8e8;
            margin-top: 16px;
          }
          .item {
            display: flex;
            margin-right: 16px;
            align-items: center;
            .label-item {
              font-size: 28px;
              color: #8c8c8c;
              margin-right: 16px;
            }
            .value-item {
              font-size: 28px;
              color: #3a3f3f;
              margin-bottom: 8px;
            }
          }
        }
      }
    }
  }
}
