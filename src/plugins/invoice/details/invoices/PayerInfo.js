import { app } from '@ekuaibao/whispered'
/**
 *  Created by pan<PERSON> on 2018/7/25 下午12:46.
 */
import React from 'react'
import { checkPayerInfo } from '../../../../lib/util'
import RichMessage from '../../rich-message'
const SVG_RIGHT = app.require('@images/invoice-payerno-right.svg')
const SVG_WAR = app.require('@images/invoice-payerno-warning.svg')
const SVG_ERROR = app.require('@images/invoice-payerno-error-big.svg')
import styles from './PayerInfo.module.less'

export function PayerInfoName(props) {
  const { invoiceDetail, showTitleMessage, payerInfo, title, message, status, inputOutputMark, isSeller } = props
  if (invoiceDetail.payer === '个人') {
    // @i18n-ignore
    return (
      <div className={styles['payerinfo-wrapper']}>
        <div className="payerinfo-content">
          <div className="label">{title}</div>
          <div className="value">
            <span>{i18n.get('个人')}</span>
            <img className="ml-5" src={SVG_RIGHT} />
          </div>
        </div>
      </div>
    )
  }
  if (isNotShowCheck(isSeller, inputOutputMark)) {
    return (
      <div className={styles['payerinfo-wrapper']}>
        <div className="payerinfo-content">
          <div className="label">{title}</div>
          <div className="value">
            <span>{invoiceDetail.payer}</span>
          </div>
        </div>
      </div>
    )
  }
  const payerInfoArr = payerInfo
  const { checkPayerName, checkPayerNumber } = checkPayerInfo(invoiceDetail)
  const isNumberError = checkPayerNumber !== 'true'
  let sameNameObj = isNumberError ? payerInfoArr.find(el => el.name === invoiceDetail.payer) : undefined

  const STATUS_MSG = {
    SUCCESS: i18n.get('校验正确，和公司抬头结果一致'),
    NO_VISIBLE: message || i18n.get('校验失败，不属于可用的公司抬头')
  }
  const STATUS_IMG = { SUCCESS: SVG_RIGHT, NO_VISIBLE: SVG_WAR, NO_RESULT: SVG_ERROR }
  let ischeckName = checkPayerNumber === 'no',
    payeeTip = '',
    payeeImg

  if (ischeckName) {
    payeeTip = i18n.get('开票信息不符')
    payeeImg = SVG_WAR
  } else {
    payeeTip = !checkPayerName ? STATUS_MSG['NO_VISIBLE'] : STATUS_MSG[status] || message
    payeeImg = !checkPayerName ? STATUS_IMG['NO_RESULT'] : STATUS_IMG[status]
  }
  return (
    <div className={styles['payerinfo-wrapper']}>
      <div className="payerinfo-content">
        <div className="label">{title}</div>
        <div className="value">
          <div
            className={checkPayerName ? '' : 'color-red-6 flex'}
            onClick={() => showTitleMessage(<RichMessage message={payeeTip} />)}
          >
            <span>{invoiceDetail.payer}</span>
            <img className="ml-5" src={payeeImg} />
          </div>
          {checkPayerName && isNumberError && (
            <div className="warning-text">
              <font className="mr-5">{i18n.get('对应税号')}</font>
              {sameNameObj && sameNameObj.payerNo}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export function PayerInfoNumber(props) {
  const { invoiceDetail, payerInfo, showMessage, title, message, status, isSeller, inputOutputMark } = props
  let payertaxno = invoiceDetail.payertaxno && invoiceDetail.payertaxno.toUpperCase()
  if (invoiceDetail.payer === '个人') {
    // @i18n-ignore
    return (
      <div className={styles['payerinfo-wrapper']}>
        <div className="payerinfo-content">
          <div className="label">{title}</div>
          {payertaxno && (
            <div className="value">
              <span>{payertaxno}</span>
              <img className="ml-5" src={SVG_RIGHT} />
            </div>
          )}
        </div>
      </div>
    )
  }
  if (isNotShowCheck(isSeller, inputOutputMark)) {
    return (
      <div className={styles['payerinfo-wrapper']}>
        <div className="payerinfo-content payerInfo-number">
          <div className="label">{title}</div>
          <div className="value value-number">
            <span>{payertaxno}</span>
          </div>
        </div>
      </div>
    )
  }
  const payerInfoArr = payerInfo
  const { checkPayerName, checkPayerNumber } = checkPayerInfo(invoiceDetail)
  if (checkPayerNumber === 'noCheck') {
    return <span>{payertaxno || i18n.get('无法获取')}</span>
  }

  const STATUS_MSG = {
    SUCCESS: i18n.get('校验正确，和公司纳税人识别号一致'),
    NO_VISIBLE: message || i18n.get('校验失败，不属于可用的纳税人识别号')
  }
  const STATUS_IMG = { SUCCESS: SVG_RIGHT, NO_VISIBLE: SVG_WAR, NO_RESULT: SVG_ERROR }

  let ischeckName = checkPayerNumber === 'no',
    payeeTip = '',
    payeeImg
  if (ischeckName) {
    payeeTip = i18n.get('无法获取购买方纳税人识别号')
    payeeImg = SVG_WAR
  } else {
    payeeTip = !checkPayerName ? STATUS_MSG['NO_VISIBLE'] : STATUS_MSG[status] || message
    payeeImg = !checkPayerName ? STATUS_IMG['NO_RESULT'] : STATUS_IMG[status]
  }

  const isNameError = !checkPayerName
  let sameNumberObj
  if (isNameError) {
    sameNumberObj = payerInfoArr.find(el => el.payerNo === payertaxno)
  }

  return (
    <div className={styles['payerinfo-wrapper']}>
      <div className="payerinfo-content payerInfo-number">
        <div className="label">{title}</div>
        <div className="value value-number">
          <div
            onClick={() => showMessage(<RichMessage message={payeeTip} />)}
            className={checkPayerNumber === 'true' ? 'flex' : 'color-red'}
          >
            {checkPayerNumber === 'no' ? i18n.get('无法获取') : payertaxno}
            <img className="ml-5" src={payeeImg} />
          </div>
          {checkPayerNumber === 'true' && isNameError && (
            <div className="warning-text">
              <font className="mr-5">{i18n.get('对应购买方')}</font>
              {sameNumberObj && sameNumberObj.name}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

const isNotShowCheck = (isSeller, invoiceMark) => {
  if (invoiceMark === 'OUTPUT_INVOICE_RECEIPT') {
    return !isSeller
  } else {
    return isSeller
  }
}
