import React, { PureComponent } from 'react'
import <PERSON>hanceT<PERSON>le<PERSON>ook from '../../../lib/EnhanceTitleHook'
import { app as api } from '@ekuaibao/whispered'
import InvoiceDetailView from './../details/invoice-detail-view'

@EnhanceTitleHook()
export default class InvoiceDetailReadOnly extends PureComponent {
  constructor(props) {
    super(props)
  }

  componentWillMount() {
    api
      .dataLoader('@common.payerInfo')
      .load()
      .then(_ => {
        this.forceUpdate()
      })
  }

  render() {
    return <InvoiceDetailView {...this.props} />
  }
}
