import { app } from '@ekuaibao/whispered'
import styles from './invoice-detail-view.module.less'
import React from 'react'

import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { toast } from '../../../lib/util'
import SVG_STAMP from '../images/fp-stamp.svg'
import get from 'lodash/get'

import { PayerInfoNumber, PayerInfoName } from './invoices/PayerInfo'
import { InvoiceItem, SeparationLine, InvoiceMoney, InvoiceDetail, TotalView } from './invoices/InvoiceContainer'
import { getShowViewName } from '../../importOCR/utils'
const { formatInvoiceData } = app.require('@elements/puppet/ThirdCard/FormatInvoiceData')
const { getPreviewUrl } = app.require('@components/utils/fnAttachment')
const overseasBillItem = [
  '税率',
  '消费国家',
  '票据号码',
  '购买方名称',
  '购买方纳税人识别号',
  '销售方名称',
  '销售方纳税人识别号'
]

class InvoiceDetailView extends React.Component {
  constructor(props) {
    super(props)
    this.state = { message: undefined, status: undefined, ischeck: undefined }
  }

  componentDidMount() {
    let { data, submitterId } = this.props
    let masterId = get(data, 'master.id')
    const entityId = get(data, 'master.entityId')
    // 增值税发票走状态校验
    if (!masterId || !submitterId || entityId !== 'system_发票主体') return 
    api.invokeService('@bill:get:invoice:state', { masterId, submitterId }).then(res => {
      const { ischeck, message, status } = res
      this.setState({ isLoading: false, ischeck, message, status })
    })
  }

  handleOpenPDF = ({ fpdm, fphm, id }) => {
    if (id) {
      api.invokeService('@invoice-form:getinvoice-images', [id]).then(res => {
        const item = res.items.shift()
        if (item?.url) {
          let link = `${window.PREVIEW_DOMAIN}/view/url?url=${encodeURIComponent(`${item.url}&name=${item.fileName}`)}`
          const watermark = api.getState()['@common'].waterMark
          if (watermark) {
            link = link + '&watermark=' + encodeURIComponent(watermark)
          }
          this.fnGotoPreview(link)
        }
      })
    } else {
      Fetch.GET('/api/v1/upload/invoice/preview', {
        fpdm: fpdm,
        fphm: fphm
      }).then(result => {
        if (result) {
          const { name, url } = result
          let link = `${window.PREVIEW_DOMAIN}/view/url?url=${encodeURIComponent(url)}&name=${name}`
          const watermark = api.getState()['@common'].waterMark
          if (watermark) {
            link = link + '&watermark=' + encodeURIComponent(watermark)
          }
          this.fnGotoPreview(link)
        } else {
          toast.fail(result.message)
        }
      })
    }
  }

  handlePreviewPDF = paramData => {
    const { data } = this.props
    const key = data?.master?.form?.['E_system_发票主体_图片'] || data?.master?.form?.['E_system_发票主体_PDF']
    if (key?.length) {
      const params = { key, corpId: Fetch.ekbCorpId }
      getPreviewUrl(params)
        .then(result => {
          const link = result.value?.url
          this.fnGotoPreview(link)
        })
        .catch(err => {
          console.error(err)
          this.handleOpenPDF(paramData)
        })
    } else {
      this.handleOpenPDF(paramData)
    }
  }

  fnGotoPreview = link => {
    const isApp = window.__PLANTFORM__ === 'APP'
    if (isApp) {
      api.invokeService('@layout:open:link', link, null, i18n.get('预览PDF'))
    } else {
      api.invokeService('@layout:open:link', link)
    }
  }

  showMessage = message => {
    toast.info(message, 4000)
  }

  showTitleMessage = message => {
    toast.info(message, 4000)
  }

  renderItem = (item, index, params) => {
    let { sourcePage } = this.props
    const { type, label, value, isShow } = item
    switch (type) {
      case 'separationLine':
        return <SeparationLine key={index} isShow={isShow} sourcePage={sourcePage} />
      case 'money':
        return <InvoiceMoney key={index} value={value} label={label} />
      case 'list':
        return <InvoiceDetail key={index} value={value} label={label} {...this.props} />
      case 'total':
        return <TotalView key={index} value={value} label={label} />
      case 'sellerName':
        return (
          <PayerInfoName
            key={index}
            isSeller
            title={item.label}
            status={params.status}
            invoiceDetail={item.value}
            showTitleMessage={this.showTitleMessage}
            payerInfo={params.payerInfo}
            message={params.message}
            inputOutputMark={params.inputOutputMark}
          />
        )
      case 'sellerNumber':
        return (
          <PayerInfoNumber
            key={index}
            isSeller
            status={params.status}
            title={item.label}
            invoiceDetail={item.value}
            showMessage={this.showMessage}
            payerInfo={params.payerInfo}
            message={params.message}
            inputOutputMark={params.inputOutputMark}
          />
        )
      default:
        return <InvoiceItem key={index} value={value} label={label} isShow={isShow} />
    }
  }

  //被继承的类调用了
  renderFooterView() {}

  render() {
    let { data, selectData, sourcePage } = this.props

    if (!data) return null

    const payerInfo = api.getState()['@common'].payerInfo
    const {
      invoiceInfo,
      invoiceInfo: { buyer, taxIdNum, items, fpdm, fphm, title, hasPdf }
    } = formatInvoiceData({ data, selectData, sourcePage })
    let displayItems = items
    const isFullDigital = ['FULL_DIGITAl_SPECIAL', 'FULL_DIGITAl_NORMAL'].includes(
      get(data, 'master.form.E_system_发票主体_发票类别')
    ) //全电票
    let { message, status } = this.state
    if (message === undefined) {
      message = invoiceInfo.message
    }
    if (status === undefined) {
      status = invoiceInfo.status
    }
    if (isFullDigital) {
      const blackList = ['发票代码', '购方地址电话', '购方开户行账号']
      displayItems = displayItems.filter(el => !blackList.includes(el?.label))
    }
    const fileName = get(data, 'master.form.E_system_发票主体_图片')
    const id = data?.master?.id
    const inputOutputMark = get(data, 'master.form.E_system_发票主体_进销项标识')
    const isOverseasBill = data?.master?.entityId === 'system_海外发票'
    if (isOverseasBill) {
      displayItems = overseasBillItem.map(item => {
        const value = get(data, `master.form.E_system_海外发票_${item}`)
        if (item === '税率') {
          return { label: item, value: value ? `${value}%` : '-' }
        } else if (item === '不计税金额') {
          return { label: item, value, type: 'money' }
        }
        return { label: item, value: value || '-' }
      })
    }
    return (
      <div className={styles['invoice-detail-wrapper']}>
        <div className="invoice-content">
          <div className="header">
            <div className="stamp_big">
              <img src={SVG_STAMP} />
            </div>
            <div className="title">{title}</div>
            {hasPdf && (
              <div className="pdf" onClick={() => this.handlePreviewPDF({ fphm, fpdm, id })}>
                {isOverseasBill ? i18n.get('查看文件') : getShowViewName(fileName)}
              </div>
            )}
          </div>
          <div className="line" />
          <div className="line mt-1" />
          <div className="detail">
            {!isOverseasBill && (
              <PayerInfoName
                title={buyer.label}
                status={status}
                invoiceDetail={buyer.value}
                showTitleMessage={this.showTitleMessage}
                payerInfo={payerInfo}
                message={message}
                inputOutputMark={inputOutputMark}
              />
            )}
            {!isOverseasBill && (
              <PayerInfoNumber
                status={status}
                title={taxIdNum.label}
                invoiceDetail={taxIdNum.value}
                showMessage={this.showMessage}
                payerInfo={payerInfo}
                message={message}
                inputOutputMark={inputOutputMark}
              />
            )}
            {displayItems.map((item, index) => {
              return this.renderItem(item, index, { payerInfo, message, status, inputOutputMark })
            })}
          </div>
          {this.renderFooterView()}
        </div>
      </div>
    )
  }
}

export default InvoiceDetailView
