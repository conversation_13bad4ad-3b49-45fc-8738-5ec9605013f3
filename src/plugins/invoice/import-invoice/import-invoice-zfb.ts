/**************************************************
 * Created by nanyuantingfeng on 28/07/2017 10:08.
 **************************************************/

import { fnImportZfb, fnCheckCorporationInfo } from '../utils/index'
import SVG_IMPORT_ZFB from '../images/payzfb.svg'
import { fnConinvoiceTrack, fnInvoicepathTrack } from '../utils/invoiceTrack'
import { app as api } from '@ekuaibao/whispered'

const invoiceWay = 'query'

const info = {
  icon: SVG_IMPORT_ZFB,
  title: i18n.get('支付宝发票'),
  type: 'invoiceAlipay',
  enterType: 'invoiceAlipay',
  group: 'invoice',
  weight: 30,
  async onClick(...args: any[]) {
    let params: any = {}
    args.forEach(line => {
      params = { ...params, ...line }
    })
    const { submitter, importWay, count } = params
    const check = await api.invokeService('@record-expends:check:alipaycard:auth')
    if (!check) {
      return { type: 'invoice', data: [] }
    }
    importWay ? fnInvoicepathTrack(submitter, importWay, invoiceWay) : fnConinvoiceTrack(submitter, count, invoiceWay)
    return fnCheckCorporationInfo(fnImportZfb, params).then((result: { type: string }) => {
      if (result && result.type === 'invoiceOCR') {
        return result
      }
      return { type: 'invoice', data: result }
    })
  }
}

export default info
