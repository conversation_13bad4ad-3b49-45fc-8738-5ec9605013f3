/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/7/20 上午11:30.
 */
import info0 from './import-invoice'
import info1 from './import-typein-invoice'
import info2 from './import-invoice-photo'
import info3 from './import-invoice-wx'
import info4 from './import-invoice-zfb'
import info5 from './import-invoice-afp'
import poolInvoicePoint from './import-invoice-from-pool'
import { app as api } from '@ekuaibao/whispered'

const defaultPoints = [info0, info1, info2, info3, info5, poolInvoicePoint]
if (api.getState('@common').powers.AliPayCard) {
  defaultPoints.push(info4)
}

export default defaultPoints

const points = [info0, info1]
if (window.__PLANTFORM__ === 'APP' || window.__PLANTFORM__ === 'WEIXIN') {
  points.push(info2)
}

export { points }
