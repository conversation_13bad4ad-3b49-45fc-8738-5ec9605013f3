/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/7/18 下午4:43.
 */

import SVG_INVOICE_PHONE from '../images/invoice-phone.svg'
import { app as api } from '@ekuaibao/whispered'
import { fnConinvoiceTrack, fnInvoicepathTrack } from '../utils/invoiceTrack'

const invoiceWay = 'photo'

const info = {
  icon: SVG_INVOICE_PHONE,
  title: i18n.get('发票照片'),
  type: 'invoicePhoto',
  group: 'invoice',
  onClick(...args) {
    let params = {}
    args.forEach(line => {
      params = { ...params, ...line }
    })
    const { submitter, importWay, count } = params
    importWay ? fnInvoicepathTrack(submitter, importWay, invoiceWay) : fnConinvoiceTrack(submitter, count, invoiceWay)
    return api.invokeService('@invoice-form:upload-invoice-photo').then(_ => {
      return { type: 'invoicePhoto' }
    })
  }
}
export default info
