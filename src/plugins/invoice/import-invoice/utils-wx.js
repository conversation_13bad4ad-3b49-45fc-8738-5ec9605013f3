import { app as api } from '@ekuaibao/whispered'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { Fetch } from '@ekuaibao/fetch'
import { Dialog } from '@hose/eui-mobile'
import { toast, showLoading, hideLoading } from '../../../lib/util'

export function fnWxFapiao({
  fnInvoiceDataResult,
  enterType,
  submitter,
  specificationVersionId,
  notShowModalIfAllInvoiceSuccess
}) {
  return api.invokeService('@layout:invoice:wx').then(info => {
    /**
     * 错误处理android与iOS的返回不一样，android没有取肖导入，只有返回，并且没有选择记录也可以选择确定
     */
    const err_msg = info?.err_msg || ''
    const choose_invoice_info = info?.choose_invoice_info || '[]'
    if (err_msg.indexOf('ok') >= 0) {
      if (choose_invoice_info.length > 0) {
        let cards = JSON.parse(choose_invoice_info)
        if (cards && cards instanceof Array && cards.length > 0) {
          showLoading(i18n.get('查询中...'))
          return getInvoiceData({
            cards,
            fn: fnInvoiceDataResult,
            enterType,
            submitter,
            specificationVersionId,
            notShowModalIfAllInvoiceSuccess
          })
        }
      }
      toast.info(i18n.get('没有选择有效的微信发票'), 3000)
    } else if (err_msg.indexOf('cancel') >= 0) {
      toast.info(i18n.get('取消选择微信发票'), 3000)
    } else {
      toast.error(i18n.get('微信发票导入失败'), 3000)
    }
  })
}

function getInvoiceData({
  cards,
  fn,
  enterType,
  submitter,
  specificationVersionId,
  notShowModalIfAllInvoiceSuccess = false
}) {
  let data = []
  cards.forEach(card => data.push({ card_id: card.card_id, encrypt_code: card.encrypt_code }))
  return api.invokeService('@layout:get:wxinvoice:data', data).then(
    result => {
      hideLoading()
      if (result.errorCode) {
        Dialog.alert({ title: i18n.get('提示'), content: result.errorMessage })
        return
      }
      let listData = result.length > 0 ? result : []
      return importResultFilter(listData).then(sucListData => {
        if (sucListData.length) {
          return fn({
            value: sucListData,
            type: 'invoice-wx',
            enterType,
            submitter,
            specificationVersionId,
            notShowModalIfAllInvoiceSuccess
          })
        }
      })
    },
    error => {
      hideLoading()
      Dialog.alert({ title: i18n.get('提示'), content: error.msg || JSON.stringify(error) })
      return
    }
  )
}

function importResultFilter(listData) {
  const { promise, resolve } = window.PromiseDefer()
  const sucListData = listData.slice().filter(line => line.ischeck)
  const failLen = listData.length - sucListData.length
  if (listData?.length && isHongShanTestingEnterprise(Fetch.ekbCorpId)) {
    // 红杉的直接跳转到发票列表，在发票列表去判断是否有不合格的发票
    resolve(listData)
  } else {
    if (failLen > 0) {
      Dialog.alert({
        title: i18n.get('成功导入发票{__k0}张！', { __k0: sucListData.length }),
        content: i18n.get(
          '{__k0}张发票导入失败，原因可能为：购方信息与企业开票信息不符；发票明细全部被关联；发票金额为负。',
          { __k0: failLen }
        ),
        confirmText: i18n.get('确定'),
        onConfirm: () => resolve(sucListData)
      })
    } else {
      resolve(sucListData)
    }
  }
  return promise
}
