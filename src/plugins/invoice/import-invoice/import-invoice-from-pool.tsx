/**************************************************
 * Created by nanyuantingfeng on 28/07/2017 10:07.
 **************************************************/

import React from 'react'
import { fnInvoiceDataResult, fnCheckCorporationInfo, fnImportFromInvoicePool } from '../utils/index'
import { fnConinvoiceTrack, fnInvoicepathTrack } from '../utils/invoiceTrack'
import { OutlinedGeneralFolder } from '@hose/eui-icons'

const invoiceWay = 'formPoolSelect'

const info = {
  icon: <OutlinedGeneralFolder fontSize={20} color="#665ab5" style={{ marginRight: 6, marginTop: 2 }} />,
  title: i18n.get('票池选择'),
  type: 'formPoolSelect',
  group: 'invoice',
  weight: 20,
  isCustomIcon: true,
  onClick(...args: any[]) {
    let params: any = {}
    args.forEach(line => {
      params = { ...line, ...params }
    })
    params.inImportInvoice = true
    const fns = { fnInvoiceDataResult, ...params }
    const { submitter, importWay, count } = params
    importWay ? fnInvoicepathTrack(submitter, importWay, invoiceWay) : fnConinvoiceTrack(submitter, count, invoiceWay)
    return fnCheckCorporationInfo(fnImportFromInvoicePool, fns).then((result: any) => {
      return { type: 'invoice', data: result }
    })
  }
}

export default info
