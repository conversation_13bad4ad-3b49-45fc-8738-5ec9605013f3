import React, { useEffect, useState, useMemo } from 'react'
import { Dynamic, IExtendBus } from '@ekuaibao/template'
import './import-invoice-pool.less'
import { app } from '@ekuaibao/whispered'
// @ts-ignore
import { createForm } from 'rc-form'
import MessageCenter from '@ekuaibao/messagecenter'
import { Button } from '@hose/eui-mobile'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager-mobile'
import { getInvoiceFromPool } from '../invoice.action'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import moment from 'moment'
import { toast } from '../../../lib/util'
const editable = app.require<any>('@components/dynamic/index.editable')

interface Props extends ILayerProps {
  appId: string
  specificationId: string
  parentBus?: IExtendBus
}
const ImportInvoicePool: React.FC<Props> = props => {
  const { appId, specificationId } = props
  const [bus, setBus] = useState<IExtendBus>()
  const [invoiceType, setInvoiceType] = useState('')
  const [values, setValues] = useState()
  useEffect(() => {
    const bus = new MessageCenter()
    setBus(bus as IExtendBus)
  }, [])

  const handleOK = async () => {
    const result = await bus.getValueWithValidate(1)
    const invoiceDate = moment(result.invoiceDate).valueOf()
    getInvoiceFromPool({
      ...result,
      invoiceAmount: result.invoiceAmount.standard,
      invoiceDate,
      appId,
      specificationId
    })
      .then(({ value }) => {
        props.layer.emitOk([value])
      })
      .catch(error => {
        toast.error(error.message)
      })
  }

  const handleCancel = () => {
    props.layer.emitCancel()
  }

  const invoiceOptions = [
    { label: i18n.get('增值税普通发票'), value: 'PAPER_NORMAL' },
    { label: i18n.get('增值税专用发票'), value: 'PAPER_SPECIAL' },
    { label: i18n.get('全电纸质发票（增值税专用发票）'), value: 'FULL_DIGITAl_PAPER' },
    { label: i18n.get('全电纸质发票（增值税普通发票）'), value: 'FULL_DIGITAl_PAPER_NORMAL' },
    { label: i18n.get('增值税电子普通发票'), value: 'DIGITAL_NORMAL' },
    { label: i18n.get('增值税电子专用发票'), value: 'DIGITAL_SPECIAL' },
    { label: i18n.get('电子发票（普通发票）'), value: 'FULL_DIGITAl_NORMAL' },
    { label: i18n.get('电子发票（增值税专用发票）'), value: 'FULL_DIGITAl_SPECIAL' },
    { label: i18n.get('电子发票（航空运输电子客票行程单）'), value: 'ELECTRONIC_AIRCRAFT_INVOICE' },
    { label: i18n.get('电子发票（铁路电子客票）'), value: 'ELECTRONIC_TRAIN_INVOICE' }
  ]

  const isOptionalInvoiceCode = useMemo(() => {
    return invoiceType === 'FULL_DIGITAl_NORMAL' || invoiceType === 'FULL_DIGITAl_SPECIAL'
  }, [invoiceType])

  const template = useMemo(() => {
    bus?.getValue?.().then((values: any) => {
      setValues({ ...values, invoiceType: [invoiceType] })
    })
    return [
      {
        name: 'invoiceType',
        label: i18n.get('发票类型'),
        maxLength: 20,
        type: 'select',
        optional: false,
        placeholder: i18n.get('请选择发票类型'),
        editable: true,
        visible: true
      },
      {
        name: 'invoiceCode',
        label: i18n.get('发票代码'),
        maxLength: 20,
        type: 'text',
        optional: isOptionalInvoiceCode,
        placeholder: i18n.get('请输入发票代码'),
        editable: true,
        visible: true
      },
      {
        name: 'invoiceNumber',
        label: i18n.get('发票号码'),
        maxLength: 20,
        type: 'text',
        optional: false,
        placeholder: i18n.get('请输入发票号码'),
        editable: true,
        visible: true
      },
      {
        name: 'invoiceDate',
        label: i18n.get('发票日期'),
        maxLength: 20,
        type: 'text',
        optional: false,
        placeholder: i18n.get('请输入发票日期 例如:20160817'),
        editable: true,
        visible: true
      },
      {
        name: 'invoiceAmount',
        label: i18n.get('发票金额'),
        maxLength: 20,
        type: 'money',
        optional: false,
        placeholder: i18n.get('请发票金额'),
        editable: true,
        visible: true
      }
    ]
  }, [isOptionalInvoiceCode])
  const create = (T: React.ReactNode) => {
    return createForm({
      onValuesChange: (props: any, changed: Record<string, any>) => {
        if (changed.invoiceType) {
          setInvoiceType(changed.invoiceType[0])
        }
      }
    })(T)
  }

  if (!bus) {
    return null
  }

  return (
    <div className="import-invoice-pool-wrapper">
      <div className="import-invoice-pool-content">
        <Dynamic
          isSimple
          bus={bus}
          template={template}
          elements={editable}
          create={create}
          tags={{ invoiceType: invoiceOptions }}
          value={values}
        />
      </div>
      <div className="footer">
        <Button className="button" size={'large'} category={'secondary'} onClick={handleCancel}>
          {i18n.get('取消')}
        </Button>
        <Button className="button" size={'large'} category={'primary'} onClick={handleOK}>
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  )
}

export default EnhanceTitleHook(props => props.title)(ImportInvoicePool)
