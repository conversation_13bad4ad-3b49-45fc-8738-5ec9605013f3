/**************************************************
 * Created by nanyuantingfeng on 28/07/2017 10:08.
 **************************************************/

import { fntypeinFaPiao, fnCheckCorporationInfo } from '../utils/index'
import SVG_IMPORT_LURUFAPIAO from '../images/import-lurufapiao.svg'
import { fnConinvoiceTrack, fnInvoicepathTrack } from '../utils/invoiceTrack'

const invoiceWay = 'query'

const info = {
  icon: SVG_IMPORT_LURUFAPIAO,
  title: i18n.get('手录发票'),
  type: 'invoiceManual',
  enterType: 'invoiceManual',
  group: 'invoice',
  weight: 30,
  onClick(...args) {
    let params = {
      title: i18n.get('手录发票')
    }
    args.forEach(line => {
      params = { ...params, ...line }
    })
    const { submitter, importWay, count } = params
    importWay ? fnInvoicepathTrack(submitter, importWay, invoiceWay) : fnConinvoiceTrack(submitter, count, invoiceWay)
    return fnCheckCorporationInfo(fntypeinFaPiao, params).then(result => {
      if (result && result.type === 'invoiceOCR') {
        return result
      }
      return { type: 'invoice', data: result }
    })
  }
}

export default info
