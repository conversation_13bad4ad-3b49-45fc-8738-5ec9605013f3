/**
 * @author: geyumin
 * @date: 2021-03-26 16:05
 */

import { app as api } from '@ekuaibao/whispered'
import { fnCheckCorporationInfo } from '../utils/index'
// @ts-ignore
import SVG_IMPORT_AFP from '../images/aifapiao.svg'
import { fnConinvoiceTrack, fnInvoicepathTrack } from '../utils/invoiceTrack'
import { fnAifapiao, checkAifapiaoCardAuth } from './afp-utils'

const invoiceWay = 'query'

const info = {
  icon: SVG_IMPORT_AFP,
  title: i18n.get('爱发票'),
  type: 'aifapiao',
  enterType: 'aifapiao',
  group: 'invoice',
  weight: 30,
  async onClick(...args: any[]) {
    let params: any = {}
    args.forEach(line => {
      params = { ...params, ...line }
    })

    const callbackFn = () => {
      const { submitter, importWay, count } = params
      importWay ? fnInvoicepathTrack(submitter, importWay, invoiceWay) : fnConinvoiceTrack(submitter, count, invoiceWay)
      //TODO 导入发票列表
      return fnCheckCorporationInfo(fnAifapiao, params).then((result: { type: string }) => {
        if (result && result.type === 'invoiceOCR') {
          return result
        }
        return { type: 'invoice', data: result }
      })
    }

    const check = await checkAifapiaoCardAuth(callbackFn)
    if (!check) {
      return { type: 'invoice', data: [] }
    }

    return callbackFn()
  }
}

export default info
