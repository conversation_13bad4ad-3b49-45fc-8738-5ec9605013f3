/**
 * Created by fisher on 2017/12/6.
 */

import { fnInvoiceDataResult, fnCheckCorporationInfo } from '../utils/index'
import { fnWxFapiao } from './utils-wx'
import { fnConinvoiceTrack, fnInvoicepathTrack } from '../utils/invoiceTrack'

const SVG_IMPORT_FAPIAO_WX = require('../images/import-fapiao-wx.svg')
const invoiceWay = 'weixin'

const info = {
  icon: SVG_IMPORT_FAPIAO_WX,
  title: i18n.get('微信发票'),
  type: 'invoiceWeChat',
  weight: 40,
  onClick(...args) {
    let params = {}
    args.forEach(line => {
      params = { ...params, ...line }
    })
    const { submitter, importWay, count, specificationVersionId, notShowModalIfAllInvoiceSuccess } = params
    let fns = { fnInvoiceDataResult, enterType: params.enterType, submitter, specificationVersionId, notShowModalIfAllInvoiceSuccess }
    importWay ? fnInvoicepathTrack(submitter, importWay, invoiceWay) : fnConinvoiceTrack(submitter, count, invoiceWay)
    return fnCheckCorporationInfo(fnWxFapiao, fns).then(result => {
      return { type: 'invoice', data: result }
    })
  },
  group: 'invoice'
}

export default info
