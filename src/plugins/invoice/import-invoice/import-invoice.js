/**************************************************
 * Created by nanyuantingfeng on 28/07/2017 10:07.
 **************************************************/

import { fnScanFapiao, fnInvoiceDataResult, fnCheckCorporationInfo } from '../utils/index'
import SVG_IMPORT_FAPIAO from '../images/import-fapiao.svg'
import { fnConinvoiceTrack, fnInvoicepathTrack } from '../utils/invoiceTrack'

const invoiceWay = 'scan'

const info = {
  icon: SVG_IMPORT_FAPIAO,
  title: i18n.get('扫描发票'),
  type: 'invoiceScan',
  group: 'invoice',
  weight: 20,
  onClick(...args) {
    let params = {}
    args.forEach(line => {
      params = { ...line, ...params }
    })
    params.inImportInvoice = true
    let fns = { fnInvoiceDataResult, ...params }
    const { submitter, importWay, count } = params
    importWay ? fnInvoicepathTrack(submitter, importWay, invoiceWay) : fnConinvoiceTrack(submitter, count, invoiceWay)
    return fnCheckCorporationInfo(fnScanFapiao, fns).then(result => {
      return { type: 'invoice', data: result }
    })
  }
}

export default info
