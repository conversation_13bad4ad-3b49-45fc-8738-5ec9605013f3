/**
 *  Created by pw on 2021/4/1 上午11:43.
 */
import React, { Component } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { saveAifapiaoAuthorize } from '../invoice.action'
import { app as api } from '@ekuaibao/whispered'
import styles from './ImportAiFaPiaoH5.module.less'
const WXQRCODE_IMG = require('../images/wxqrcode.jpg')
const ZFBQRCODE_IMG = require('../images/zfbqrcode.jpg')
import { afpAuthUrl } from '../utils/constants'
import { IllustrationSmallNoBilling } from '@hose/eui-icons'


@EnhanceConnect(state => ({
  userInfo: state['@common'].me_info
}))
export default class ImportAiFaPiaoH5 extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = {
      isSuccess: false,
      afpAuthUrl: props.afpAuthUrl || afpAuthUrl,
      phone: ''
    }
  }
  componentDidMount() {
    window.addEventListener('message', this.handleMessage, false)
  }

  handleMessage = (event: any) => {
    if (typeof event?.data === 'string') {
      const data = JSON.parse(event?.data)
      event?.data &&
        api.dispatch(saveAifapiaoAuthorize(data)).then((result: any) => {
          if (result.value === 'SUCCESS') {
            this.setState({ isSuccess: true, phone: data.cellphone })
          }
        })
    }
  }

  componentWillUnmount() {
    window.removeEventListener('message', this.handleMessage)
  }

  handleModalOk = () => {
    this.props.layer.emitOk({})
  }

  dealTel = (val:string) => {
    var reg = /^(\d{3})\d{4}(\d{4})$/
    return val.replace(reg, "$1****$2")
  }

  handleGoJump = () => {
    const { callbackFn } = this.props
    callbackFn && callbackFn()
    this.props.layer.emitCancel()
  }

  render() {
    const { isSuccess, phone, afpAuthUrl } = this.state
    const staff = this.props.userInfo?.staff
    const callBackUrl = window.location.origin
    const src = `${afpAuthUrl}#/pages/auth/auth?corporationId=${staff?.corporationId?.id ?? ''}&staffId=${staff?.id ??
      ''}&callBackUrl=${callBackUrl}`
    const { modalSource } = this.props
    return (
      <div className={styles['afp_authorization']}>
        {isSuccess ? (
          <div className="authorization_tip">
          <div className="auth_tip_top">
            <IllustrationSmallNoBilling fontSize={110}/>   
            <div className="auth_status">授权成功!</div> 
            <div className="auth_message">
              成功绑定{' '}
              <span className='auth_number'>{this.dealTel(phone)}</span>
              {' '}的爱发票账号
            </div>      
            {modalSource === 'invoiceImport' &&  <div className="auth_button" onClick={this.handleGoJump}>开始导入发票</div> }
          </div>
          <p className="tip_suggestion_weight">你也可以</p>
          <p className='tip_suggestion_normal'>扫描以下二维码，前往爱发票小程序 进行发票收集与管理</p>
          <div className="qrCode">
            <div>
              <div className="imgWrap">
                <img src={WXQRCODE_IMG} />
              </div>
              <p className='qrSource'>微信小程序</p>
            </div>
            <div className='ml-16'>
              <div className="imgWrap border_color">
                <img src={ZFBQRCODE_IMG} />
              </div>
              <p className='qrSource'>支付宝小程序</p>
            </div>
          </div>
        </div>
        ) : (
          <iframe
            sandbox="allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-same-origin allow-scripts allow-popups"
            src={src}
            frameBorder="0"
            name="iframeA"
            width="100%"
            height="100%"
          ></iframe>
        )}
      </div>
    )
  }
}
