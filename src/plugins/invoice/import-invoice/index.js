/**************************************************
 * Created by nanyuanting<PERSON> on 14/08/2017 11:20.
 **************************************************/

import info0 from './import-invoice'
import info1 from './import-typein-invoice'
import info2 from './import-invoice-zfb'
import info3 from './import-invoice-afp'
import { app as api } from '@ekuaibao/whispered'
const points = [info0, info1, info3]
if (api.getState('@common').powers.AliPayCard) {
  points.push(info2)
}
export default points
