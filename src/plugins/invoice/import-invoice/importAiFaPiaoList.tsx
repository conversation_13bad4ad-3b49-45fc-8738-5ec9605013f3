/**
 * @description 爱发票导入发票列表
 * <AUTHOR>
 */
import { app as api } from '@ekuaibao/whispered'
import React, { useState, useEffect } from 'react'
import { toast } from '../../../lib/util'
type IProps = {
  src: string
  styles?: any
  layer: any
}
function IframeModal(props: IProps) {
  const { src, submitter } = props

  const listener = async (event: MessageEvent<any>) => {
    const { ids, type } = event.data
    if (type === 'importInvoice') {
      try {
        const { items } = await api.invokeService('@invoice:import:invoice:from:aifapiao', { ids, staffId: submitter?.id })
        props.layer.emitOk(items)
      }catch (e) {
        e?.errorMessage && toast.info(e.errorMessage)
      }
      return
    }
  }
  
  useEffect(() => {
    window.addEventListener('message', listener, false)
    return () => {
      window.removeEventListener('message', listener, false)
    }
  }, [])
  return (
    <div style={props.styles ?? {width: '100vw', height: 'calc(100vh - 0.84rem)'}}>
      <iframe
        className="iframe_afp"
        width="100%"
        height="100%"
        sandbox="allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-same-origin allow-scripts allow-popups" 
        frameBorder="0"
        src={src}
      ></iframe>
    </div>
  )
}
export default IframeModal