/**
 *  Created by pw on 2021/3/30 下午12:07.
 */
import { app as api } from '@ekuaibao/whispered'
import { getAifapiaoCardAuthState, getAifapiaoPublicUrl, getAifapiaoEnvUrl } from '../invoice.action'
import { invoiceActions } from '../utils'

// export async function fnAifapiao({ fnInvoiceDataResult, enterType, submitter, specificationVersionId }) {
export async function fnAifapiao(params = {}) {
  const url = await getAifapiaoPublicUrl()
  const data = await api.open('@invoice:ImportAiFaPiaoList', { src: url, submitter: params?.submitter })
  let dataProps = { ...params, value: data }
  return invoiceActions(dataProps)
}

export async function checkAifapiaoCardAuth(callbackFn:any): Promise<boolean> {
  const { value: isAuth } = await getAifapiaoCardAuthState()
  if (isAuth) {
    return true
  }
  const afpAuthUrl = await getAifapiaoEnvUrl()
  await api.open('@invoice:ImportAiFapiaoH5',{ modalSource: 'invoiceImport', afpAuthUrl, callbackFn: callbackFn })
  return false
}
