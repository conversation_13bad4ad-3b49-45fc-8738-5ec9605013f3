/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/7/20 下午4:53.
 */
import React, { PureComponent } from 'react'
import { Dialog } from '@hose/eui-mobile'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import ActionsView from './ActionsView'
import styles from './CheckView.module.less'
import InvoiceDetailView from './../details/invoice-detail-view'
import { app as api } from '@ekuaibao/whispered'
import { getInvoiceRelation } from '../invoice.action'

@EnhanceTitleHook()
export default class CheckInvoiceView extends PureComponent {
  constructor(props) {
    super(props)
  }

  handleContinue = () => {
    this.props.layer.emitOk({ type: 'suc' })
  }
  handleClose = () => {
    this.props.layer.emitCancel()
  }

  handleOnItemClick = line => {
    let user = api.getState()['@common'].me_info
    const isInvoice = user.permissions && user.permissions.includes('INVOICE_MANAGE')
    isInvoice &&
      api.dispatch(getInvoiceRelation(line.id)).then(result => {
        if (result && result.type === 'FLOW') {
          api.open('@bill:BillDetailModal', {
            isShowWaitInvoice: false,
            isNeedBudget: false,
            params: { id: result.flowId }
          })
        } else {
          Dialog.alert({
            title: i18n.get('提示'),
            content: i18n.get('该明细关联的是统一开票批次，请到PC端查看')
          })
        }
      })
  }

  render() {
    return (
      <div className={styles['checkView-wrapper']}>
        <div className="checkView-content">
          <div className="details">
            <InvoiceDetailView {...this.props} onItemClick={this.handleOnItemClick} sourcePage="checkInvoice" />
          </div>
          <ActionsView onContinue={this.handleContinue} onClose={this.handleClose} />
        </div>
      </div>
    )
  }
}
