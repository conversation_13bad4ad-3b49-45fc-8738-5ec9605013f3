/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/7/20 下午5:46.
 */
import React, { PureComponent } from 'react'
import { Button } from 'antd-mobile'
import styles from './ActionsView.module.less'

export default class ActionsView extends PureComponent {
  constructor(props) {
    super(props)
  }

  render() {
    let { onContinue, onClose } = this.props
    return (
      <div className={styles['actions-wrapper']}>
        <Button className="continue" onClick={onContinue}>
          {i18n.get('继续校验')}
        </Button>
        <Button className="close" onClick={onClose}>
          {i18n.get('关闭')}
        </Button>
      </div>
    )
  }
}
