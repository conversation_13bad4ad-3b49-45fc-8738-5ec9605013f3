/*!
 * Copyright 2019 yang<PERSON><PERSON> <yang<PERSON><PERSON>@shimo.im>. All rights reserved.
 * @since 2019-06-06 14:50:25
 */
@import '~@ekuaibao/eui-styles/less/token.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.wait-invoice-expense-tab-pane {
  height: 100%;
  overflow: auto;
  background-color:var(--eui-bg-base);
  
  .bill-empty {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .title {
      font: var(--eui-font-head-b1);
      color: var(--eui-text-title);
    }
  }
}

.wait-invoice-expense-item {
  background-color: @color-white-1;
  padding: 32px 32px 0;
  .invoice-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    border-bottom: 2px solid rgba(39, 46, 59, 0.08);
    padding-bottom: 32px;
    > .left {
      flex-shrink: 0;
      padding-right: 32px;

      > img {
        display: block;
        width: 32 * 2px;
        height: 32 * 2px;
        border-radius: 50%;
      }
    }

    > .body {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      overflow: hidden;
      > .title {
        display: flex;
        flex-direction: row;
        align-items: center;
        color: @eui-ref-color-grey;
        font-size: 28px;
        font-weight: 500;
        > .name {
          flex: 1;
          color: @color-black-1;
          line-height: 24 * 2px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: pre;
          word-break: keep-all;
        }

        > .money,
        > .money > *,
        > .money > * > * {
          flex-shrink: 0;
          margin-top: 0 !important;
          line-height: 24 * 2px !important;
        }
      }

      > .intro {
        margin-top: 8px;
        color: rgba(39, 46, 59, 0.8);
        font-size: 24px;
      }
    }
  }

}
