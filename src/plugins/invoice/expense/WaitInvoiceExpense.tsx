import * as React from 'react'
import './WaitInvoiceExpense.less'
import classNames from 'classnames'
import MessageCenter from '@ekuaibao/messagecenter'
import { QuerySelect } from 'ekbc-query-builder'
import memoize from 'lodash/memoize'
import moment from 'moment'
import { createRef, PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { Resource } from '@ekuaibao/fetch'
import { toast } from '../../../lib/util'
import { ExpenseModel } from './types'
const ETabs = api.require<any>('@elements/EUITabs')
const Money = api.require<any>('@elements/puppet/Money')
const EmptyWidget = api.require<any>('@home5/EmptyWidget')
const SkeletonListEUI = api.require<any>('@home5/SkeletonList')
const waitInvoiceRemote = new Resource('/api/v2/flow/invoice/wait/search/my')

const PAGE_SIZE = 20

export type WaitKind = 'wait' | 'confirm'

export interface WaitInvoiceExpenseState {
  waitLoading: boolean
  waitList: ExpenseModel[]
  waitHasMore: number
  waitStart: number
  confirmLoading: boolean
  confirmList: ExpenseModel[]
  confirmHasMore: number
  confirmStart: number
  tabSelected: string
  showSkeleton: boolean
}

enum TabKey {
  'wait' = 'wait',
  'confirm' = 'confirm'
}

export default class WaitInvoiceExpense extends PureComponent<{}, WaitInvoiceExpenseState> {
  private bus = new MessageCenter()
  private tab: WaitKind = 'wait'
  private fetchs = { wait: 0, confirm: 0 }

  constructor(props: {}) {
    super(props)
    this.state = {
      waitHasMore: 1,
      waitStart: 0,
      waitList: [],
      waitLoading: false,
      confirmHasMore: 0,
      confirmStart: 0,
      tabSelected: TabKey.wait,
      confirmList: [],
      confirmLoading: false,
      showSkeleton: true
    }
  }
  componentWillMount() {
    this.bus.watch('invoice:updata:bills', this.handleUpDataBills)
  }

  handleUpDataBills = () => {
    this.fetch('wait', true)
    this.fetch('confirm', true)
  }

  private fetch(type: WaitKind, reload: boolean) {
    const state = this.state as any
    if (state[type + 'Loading'] && !reload) {
      return
    }
    const id = ++this.fetchs[type]
    const list = reload ? [] : state[type + 'List']
    const start = reload ? 0 : state[type + 'Start'] + PAGE_SIZE
    this.setState({ [type + 'List']: list, [type + 'Loading']: true, showSkeleton: start === 0 ? true : false } as any)
    const query = new QuerySelect()
      .filterBy('state !="draft"')
      .filterBy('state !="pending"')
      .filterBy('state !="rejected"')
      .limit(start, PAGE_SIZE)
      .value()

    waitInvoiceRemote
      .POST('', query, {
        type,
        join$1: 'feeTypeId,feeTypeId,/v1/form/feeTypes',
        join$2: 'specificationId,specificationId,/form/v1/specificationVersions',
        join$3: 'feeTypeForm.invoiceForm.invoices.invoiceId,invoiceId,/v2/invoice',
        join$4: 'feeTypeForm.invoiceForm.invoices.itemIds,itemIds,/v2/invoice/item',
        join$5: 'feeTypeForm.invoiceForm.attachments.fileId,fileId,/v1/attachment/attachments'
      })
      .then(
        ({ items = [] }: any) => {
          this.setState({ showSkeleton: false })
          if (id !== this.fetchs[type]) {
            return
          }
          this.setState(
            {
              [type + 'HasMore']: items.length,
              [type + 'Start']: start,
              [type + 'List']: list.concat(items),
              [type + 'Loading']: false
            } as any,
            () => {
              this.handleScroll(type)()
            }
          )
          if (this.tab === type) {
            this.setTitle('change')
          }
        },
        (error: any) => {
          if (id !== this.fetchs[type]) {
            return
          }
          toast.fail(error.msg)
          this.setState({ [type + 'Loading']: false } as any)
        }
      )
  }

  private setTitle(type: string) {
    api.apply('@layout', `${type}:header:title`, i18n.get(`待开票费用`))
  }

  private handleChangeTab = (key: string) => {
    this.setState({ tabSelected: key })
    this.tab = key as any
    this.setTitle('change')
    setTimeout(this.handleScroll(this.tab), 0)
  }

  private handleClickItem = (item: ExpenseModel) => () => {
    api.go(`/flow_detail/${item.flowId}/${item.feeTypeForm.detailId}/${true}`)
  }

  private renderItem = (item: ExpenseModel, index: number) => {
    return (
      <div className="wait-invoice-expense-item" key={index} onClick={this.handleClickItem(item)}>
        <div className="invoice-item">
          <div className="left">
            <img src={item.feeTypeId.icon} style={{ backgroundColor: item.feeTypeId.color }} />
          </div>
          <div className="body">
            <div className="title">
              <div className="name">{item.feeTypeId.name}</div>
              <div className="money">
                <Money value={item.feeTypeForm.amount} showShorthand />
              </div>
            </div>
            <div className="intro">{moment(item.feeTypeForm.feeDate).format('YYYY/MM/DD')}</div>
          </div>
        </div>
      </div>
    )
  }

  private scrollers = {
    wait: createRef<HTMLDivElement>(),
    confirm: createRef<HTMLDivElement>()
  }
  private handleScroll = memoize((type: WaitKind) => () => {
    const { current } = this.scrollers[type]
    const state = this.state as any
    const hasMore = state[type + 'HasMore']
    if (
      this.tab === type &&
      hasMore &&
      current &&
      current.scrollHeight - current.clientHeight - current.scrollTop < 30
    ) {
      this.fetch(type, false)
    }
  })

  componentDidMount() {
    this.fetch('wait', true)
    this.fetch('confirm', true)
    this.setTitle('set')
  }

  componentWillUnmount() {
    this.setTitle('get')
    this.bus.un('invoice:updata:bills', this.handleUpDataBills)
  }

  renderUnderline = (props: any) => (
    <div {...props}>
      <div className="small-underline" />
    </div>
  )
  renderContent = (list: any[]) => {
    const { showSkeleton } = this.state
    if (showSkeleton) {
      return <SkeletonListEUI showImage={false} />
    }
    if (list.length) {
      return <>{list.map(this.renderItem)}</>
    } else {
      return <EmptyWidget size={200} type="billList" tips={'暂无数据'} />
    }
  }
  render() {
    const { waitList, confirmList, tabSelected, showSkeleton } = this.state
    const tabs = [
      {
        key: 'wait',
        title: i18n.get('待补充')
      },
      {
        key: 'confirm',
        title: i18n.get('确认中')
      }
    ]
    const isWait = tabSelected === TabKey.wait
    const list = isWait ? waitList : confirmList
    return (
      <>
        <ETabs
          animated={true}
          swipeable={false}
          defaultActiveKey={TabKey.wait}
          dataSource={tabs}
          onChange={this.handleChangeTab}
        />
        <div
          className={classNames('wait-invoice-expense-tab-pane', { 'pt-8': !showSkeleton && list.length })}
          onScroll={this.handleScroll(isWait ? 'wait' : 'confirm')}
          ref={isWait ? this.scrollers.wait : this.scrollers.confirm}
        >
          {this.renderContent(list)}
        </div>
      </>
    )
  }
}
