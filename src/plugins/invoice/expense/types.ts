/*!
 * Copyright 2019 yangjunbao <yang<PERSON><PERSON>@shimo.im>. All rights reserved.
 * @since 2019-06-06 15:06:27
 */
export interface Amount {
  standard: string
  standardUnit: string
  standardScale: number
  standardSymbol: string
  standardNumCode: string
  standardStrCode: string
}

export interface Invoices {
  itemIds: string[]
  invoiceId: any
}

export interface InvoiceForm {
  type: string
  invoices: Invoices[]
}

export interface FeeTypeForm {
  amount: Amount
  feeDate: number
  detailId: string
  attachments: any[]
  invoiceForm: InvoiceForm
  consumptionReasons: string
}

export interface FeeTypeModel {
  id: string
  version: number
  active: boolean
  createTime: number
  updateTime: number
  name: string
  nameSpell: string
  code: string
  corporationId: string
  parentId: string
  icon: string
  color: string
  editorId?: any
  expenseSpecificationId: string
  requisitionSpecificationId: string
  fullname: string
}

export interface ExpenseModel {
  flowId: any
  feeTypeId: FeeTypeModel
  feeTypeForm?: FeeTypeForm
  specificationId: any
}
