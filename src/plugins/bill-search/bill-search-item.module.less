/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/20 下午2:18
 */
@import "../../styles/ekb-colors";

.bill_search_item {
  display: flex;
  flex-shrink: 0;
  height: 140px;
  overflow: hidden;
  background: @gray-1;
  border-bottom: 2px solid @gray-3;
}

.avatar {
  display: flex;
  align-items: center;
  margin-right: 32px;
  :global {
    img {
      width: 64px;
      height: 64px;
      border-radius: 64px;
    }
  }
}

.bill_search_item_left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  overflow: hidden;
}

.bill_search_item_title {
  height: 48px;
  display: flex;
  align-items: center;
  font-size: 28px;
  color: @gray-9;
  img {
    margin-right: 16px;
  }
  span {
    word-break: break-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.bill_search_item_spe {
  font-size: 24px;
  color: @gray-6;
  & > :first-child {
    margin-right: 20px;
  }
}

.bill_search_item_right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-shrink: 0;
  div {
    justify-content: flex-end;
  }
}

.bill_search_item_state {
  display: flex;
  align-items: center;
  font-size: 24px;
  color: @gray-6;
  :first-child {
    margin-right: 12px;
    width: 16px;
    height: 16px;
    border-radius: 16px;
  }
}

.money_gray {
  display: block;
  font-size: 28px;
  text-align: right;
  color: rgba(0, 0, 0, 0.45);
}