import { app } from '@ekuaibao/whispered'
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/20 下午2:17
 */

import React from 'react'
import SVG_BILL_EXPENSE from './images/bill-type-expense.svg'
import SVG_BILL_LOAN from './images/bill-type-loan.svg'
import SVG_BILL_REQUISITION from './images/bill-type-requisition.svg'
const Money = app.require('@elements/puppet/Money')
import styles from './bill-search-item.module.less'
import Highlighter from 'react-highlight-words'
import get from 'lodash/get'
const SVG_AVATAR_NULL = app.require('@images/avatar-null.svg')

let moneyMap = {
  expense: 'expenseMoney',
  loan: 'loanMoney',
  requisition: 'requisitionMoney'
}

let iconMap = {
  expense: SVG_BILL_EXPENSE,
  loan: SVG_BILL_LOAN,
  requisition: SVG_BILL_REQUISITION
}

const BillSearchItem = props => {
  let { data, searchValue, onHandleClick } = props
  let title = data.form.title || i18n.get('[无标题]')
  const name = data.form.specificationId.name || data.form.template.name
  let submitterId = get(data, 'form.submitterId', {})
  let desc = name + ' #' + data.form.code
  desc = submitterId.name ? submitterId.name + ' | ' + desc : desc
  return (
    <div onClick={onHandleClick.bind(this, data)} className={styles.bill_search_item}>
      <div className={styles.avatar}>
        <img src={submitterId.avatar || SVG_AVATAR_NULL} />
      </div>
      <div className={styles.bill_search_item_left}>
        <div className={styles.bill_search_item_title}>
          {renderIcon(data.formType)}
          <Highlighter
            highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
            searchWords={[searchValue]}
            textToHighlight={title}
          />
        </div>
        <div className={styles.bill_search_item_spe}>
          <Highlighter
            highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
            searchWords={[searchValue]}
            textToHighlight={desc}
          />
        </div>
      </div>
      <div className={styles.bill_search_item_right}>{renderMoney(data)}</div>
    </div>
  )
}

const renderMoney = data => {
  const money = data.form[moneyMap[data.formType]]
  const noMoney = data.form[`${data.formType}Money`]
  return (
    <div>
      {noMoney ? (
        <Money value={money} valueSize={16} color="#3a3f3f" />
      ) : (
        <span className={styles.money_gray}>{i18n.get('暂无金额')}</span>
      )}
    </div>
  )
}

const renderIcon = formType => {
  return <img className={styles.bill_item_icon} src={iconMap[formType]} />
}

BillSearchItem.defaultProps = {
  data: {},
  onHandleClick: () => {}
}

export default BillSearchItem
