/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/13 下午3:30
 */
@import '../../styles/ekb-colors';

.bill_search_wrap {
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  :global {
    .item_wrapper_forFix {
      padding-left: 0;
    }
  }
}

.bill_search_head {
  z-index: 1;
  flex-shrink: 0;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  :global {
    .am-search {
      height: 100px;
      background-color: transparent;
      .am-search-input {
        border-radius: 36px;
        background-color: #f5f5f5;
        .am-search-synthetic-ph-icon {
          width: 24px;
          height: 24px;
          background-size: 24px auto;
          margin-bottom: 4px;
          margin-right: 20px;
        }
      }
    }

    .am-search-synthetic-ph {
      min-width: 400px;
    }

    .am-search-start {
      .am-search-input {
        .am-search-synthetic-ph {
          min-width: 200px;
        }
      }
    }
  }
}

.bill_search_content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  background: #ffffff;
  > div {
    margin: 0;
  }
  :global {
    .search_list {
      flex: 1;
    }
    .list-view-section-body {
      padding-top: 88px;
    }

    .am-list-view-scrollview-content {
      min-width: auto !important;
      left: 0;
      right: 0;
    }
  }
}

.bill_search_tip {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: @gray-6;
}
