/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/13 下午3:25
 */

import React, { PureComponent } from 'react'
import { SearchBar } from 'antd-mobile'

import { app as api } from '@ekuaibao/whispered'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import styles from './bill-search.module.less'
import { UIContainer as Container } from '@ekuaibao/whispered'

let timeout

@EnhanceTitleHook(i18n.get('搜索单据'))
class BillSearch extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      list: [],
      searchValue: ''
    }
    let { params } = this.props
    let { from } = params
    this.from = from
  }

  searchMap = (from, val) => {
    if (from === 'home') {
      //来自首页的搜索
      let filterBy = `form.title.containsIgnoreCase("${val}") || form.code.containsIgnoreCase("${val}")`
      api.invokeService('@common:search:bill', { filterBy: filterBy }).then(result => {
        let arr = result.items
        let list = arr.filter(el => el.flow.state !== 'paid' && el.flow.state !== 'archived')
        this.setState({ list })
      })
    }
    if (from === 'approving' || from === 'paying') {
      //来自待办或者待支付页面的搜索
      let filterBy = `flowId.form.title.containsIgnoreCase("${val}") || flowId.form.code.containsIgnoreCase("${val}")||flowId.form.submitterId.name.containsIgnoreCase("${val}")`
      api.invokeService(`@approve:get:${from}:list`, { filterBy }).then(data => {
        let arr = data.items
        let list = arr.map(el => {
          el.flowId.flowId = el.id
          return el.flowId
        })
        this.setState({ list })
      })
    }
    if (from === 'archived') {
      //来自已完成页面的搜索
      let filterBy = `(state == "paid" || state == "archived") && (form.title.containsIgnoreCase("${val}") || form.code.containsIgnoreCase("${val}") || form.submitterId.name.containsIgnoreCase("${val}"))`
      api
        .invokeService('@common:search:bill', {
          filterBy: filterBy,
          orderBy: [{ value: 'updateTime', order: 'DESC' }]
        })
        .then(data => {
          let list = data.items
          this.setState({ list })
        })
    }
    if (from === 'approved') {
      //来自已审批页面的搜索
      let filterBy = `form.title.containsIgnoreCase("${val}") || form.code.containsIgnoreCase("${val}")||form.submitterId.name.containsIgnoreCase("${val}")`
      api.invokeService('@mine:get:approved:list', { filterBy }).then(data => {
        let list = data.items
        this.setState({ list })
      })
    }
    if (from === 'receiving') {
      let filterBy = `flowId.form.title.containsIgnoreCase("${val}") || flowId.form.code.containsIgnoreCase("${val}")||flowId.form.submitterId.name.containsIgnoreCase("${val}")`
      api.invokeService('@approve:get:express:list:receiving', filterBy).then(data => {
        let arr = data.items
        let list = arr.map(el => {
          el.flowId.flowId = el.id
          return el.flowId
        })
        this.setState({ list })
      })
    }
    if (from === 'sending') {
      let filterBy = `flowId.form.title.containsIgnoreCase("${val}") || flowId.form.code.containsIgnoreCase("${val}")||flowId.form.submitterId.name.containsIgnoreCase("${val}")`
      api.invokeService('@approve:get:express:list:sending', filterBy).then(data => {
        let arr = data.items
        let list = arr.map(el => {
          el.flowId.flowId = el.id
          return el.flowId
        })
        this.setState({ list })
      })
    }
    if (from === 'received') {
      let filterBy = `form.title.containsIgnoreCase("${val}") || form.code.containsIgnoreCase("${val}")||form.submitterId.name.containsIgnoreCase("${val}")`
      api.invokeService('@approve:get:express:list:received', filterBy).then(data => {
        let list = data.items
        this.setState({ list })
      })
    }
    if (from === 'sent') {
      let filterBy = `form.title.containsIgnoreCase("${val}") || form.code.containsIgnoreCase("${val}")||form.submitterId.name.containsIgnoreCase("${val}")`
      api.invokeService('@approve:get:express:list:sent', filterBy).then(data => {
        let list = data.items
        this.setState({ list })
      })
    }

    if (from && from.startsWith('carbonCopy')) {
      let status = from.split('_')[1]
      let filterBy = `flowId.form.title.containsIgnoreCase("${val}") || flowId.form.code.containsIgnoreCase("${val}")||flowId.form.submitterId.name.containsIgnoreCase("${val}")`
      api.invokeService(`@approve:get:carboncopy:list`, { status, filterBy }).then(data => {
        let arr = data.items
        let carboncopyList = arr.map(el => {
          return { ...el.flowId, flowId: el.flowId.id, carbonCopyId: el.id }
        })
        this.setState({ list: carboncopyList })
      })
    }
  }

  handleSearch = (from, val) => {
    this.setState({ searchValue: val })
    if (val === '') {
      return this.setState({ list: [] })
    } else {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      timeout = setTimeout(() => {
        this.searchMap.call(this, from, val)
      }, 400)
    }
  }

  handleClear = () => {
    this.setState({ searchValue: '', list: [] })
  }

  handleCancel() {
    api.go(-1)
  }

  handleClickItem = flow => {
    let { from } = this
    let { flowId } = flow
    if (from === 'approving') {
      api.go('/approve/approving/expense/' + flowId + '/search')
    } else if (from === 'paying') {
      api.go('/approve/paying/expense/' + flowId + '/search')
    } else if (from === 'sending') {
      api.go('/approve/sending/expense/' + flowId + '/search')
    } else if (from === 'receiving') {
      api.go('/approve/receiving/expense/' + flowId + '/search')
    } else if (from === 'approved') {
      api.go('/detail/' + flow.id + '/search')
    } else if (from && from.startsWith('carbonCopy')) {
      let status = from.split('_')[1]
      api.go(`/detail/${status}/carbonCopy/${flowId}/${flow.carbonCopyId}/search`)
    } else {
      api.invokeService('@home:click:bill', flow, 'homePage')
    }
  }

  renderList(list, searchValue) {
    if (searchValue === '') {
      return <div className={styles.bill_search_tip}>{i18n.get('搜索结果会在这里显示')}</div>
    }
    if (searchValue !== '' && list.length === 0) {
      return <div className={styles.bill_search_tip}>{i18n.get('没有找到您所要的结果')}</div>
    }
    return (
      <Container
        name="@bill:BillList"
        itemClick={this.handleClickItem.bind(this)}
        searchValue={searchValue}
        billList={list}
        emptyText={''}
        style={{ paddingTop: 0 }}
        useNewItem
      />
    )
  }

  render() {
    let { list, searchValue } = this.state
    let { from } = this
    const placeholder = from !== 'home' ? i18n.get('搜索标题、单号或提交人') : i18n.get('搜索标题或单号')
    return (
      <div className={styles.bill_search_wrap}>
        <div className={styles.bill_search_head}>
          <SearchBar
            placeholder={placeholder}
            onChange={this.handleSearch.bind(this, from)}
            onClear={this.handleClear.bind(this)}
            onCancel={this.handleCancel.bind(this)}
          />
        </div>
        <div className={styles.bill_search_content}>{this.renderList.call(this, list, searchValue)}</div>
      </div>
    )
  }
}

export default BillSearch
