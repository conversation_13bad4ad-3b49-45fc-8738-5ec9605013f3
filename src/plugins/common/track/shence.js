/**************************************************
 * Created by nanyuantingfeng on 2018/5/3 18:16.
 **************************************************/
import { EventTrack, ShenCeAdapter } from '@ekuaibao/event-track'
import { app } from '@ekuaibao/whispered'

export default function(staff, appConfig) {
  let serverURL = `https://sensor.ekuaibao.com/sa?project=default`
  if (process.env.NODE_ENV === 'production') {
    serverURL = `https://sensor.ekuaibao.com/sa?project=production`
  }
  const staffId = staff.id
  const corporation = staff.corporationId
  const corpId = corporation.id
  const adapter = new ShenCeAdapter({
    serverUrl: serverURL,
    appVersion: appConfig?.appVersion ? appConfig?.appVersion : '',
    showLog: process.env.NODE_ENV !== 'production',
    autoTrack: true,
    is_track_single_page: false,
    host: location.host
  })

  const serviceEventTrack = new EventTrack('applet', adapter)
    .login({
      corpId: corpId,
      staffId: staffId,
      name: staff.name,
      phone: staff.cellphone,
      company: corporation.name
    })
    .setPublicParams({
      url() {
        return location.href
      },
      referrer: document.referrer
    })
  app.__event_track__ = serviceEventTrack
  // const params = {
  //   show_log: process.env.NODE_ENV !== 'production',
  //   name: staff.name,
  //   phone: staff.cellphone,
  //   company: corporation.name
  // }

  // const tracker = new EventTrack(
  //   new ShenCeAdapter({
  //     serverURL,
  //     corpId,
  //     staffId,
  //     platForm: global.PLATFORM_FEATURE.name,
  //     dataSource: 'EKB'
  //   })
  // )
  // tracker.register(params).listen()
  // app.__event_track__ = tracker
}
