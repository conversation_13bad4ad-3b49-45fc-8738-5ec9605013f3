// import * as Sentry from '@sentry/browser'

export function globleConfig(user: any) {
  if (user) {
    // sentryConfig(user)
    datafluxConfig(user)
  }
}

// export function sentryConfig(user: any) {
//   const { id = '', userId = '', name = '', corporationId } = user?.staff || {}
//   const { corpId = '', corpName = '' } = corporationId || {}
//   Sentry.configureScope(scope => {
//     scope.setUser({
//       id: userId,
//       username: name
//     })
//     scope.setTag('staffId', id)
//     scope.setTag('corpId', corpId)
//     scope.setTag('corName', corpName)
//     scope.setTag('corpName', corpName)
//   })
// }

export function datafluxConfig(user: any) {
  if (!window.HSM) return
  const { id = '', name = '', corporationId } = user?.staff || {}
  const { id: corpId = '', name: corpName = '' } = corporationId || {}

  window.HSM.datafluxRum.setUser({ id, name })
  window.HSM.datafluxRum.addRumGlobalContext('corpId', corpId)
  window.HSM.datafluxRum.addRumGlobalContext('corpName', corpName)
  window.HSM.datafluxRum.addAction('用户登录系统', user?.staff)

  window.HSM.datafluxLogs.setUser({ id, name })
  window.HSM.datafluxLogs.setGlobalContext({ corpId, corpName })
}
