/**************************************************
 * Created by nany<PERSON>ing<PERSON> on 8/30/16 14:31.
 **************************************************/
import { Resource } from '@ekuaibao/fetch'
import { ID } from './key'
import { QuerySelect } from 'ekbc-query-builder'
import { ParsePower } from '@ekuaibao/lib/lib/parsePower'
import { Actions, action, node, reducer } from '@ekuaibao/store'
import { hideLoading, showLoading, toast } from '../../lib/util'
import { reorganizeRiskWarningData } from './util/riskWarningUtil'
import { cloneDeep, get, isString } from 'lodash'
import { joinMoney, joinParam, selectEntity, selectEntityV2 } from './join/bill.join'
import {
  fetchAttachment,
  fetchInvoice,
  fetchInvoiceV2,
  handleInvoiceData,
  fetchPlanConfig
} from '../../lib/attachment-fetch'

import getWorkerResult from './worker/index' //----worker
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { visit } from '@ekuaibao/helpers'
import { GET } from '@ekuaibao/auth'
import { enableStaffApiSwitch, enableBillDetailOptimization, getBoolVariation } from '../../lib/featbit'
import { isErrorItem } from '../../elements/puppet/RiskNotice/ai-audit-result/utils'

const enumItems = new Resource('/api/v1/basedata/enumItems/byEnumCode')
const feeTypes = new Resource('/api/v1/form/feeTypes')
const tripTypes = new Resource('/api/trip/v2/type')
const payeesV2 = new Resource('/api/pay/v2/accounts')
const payAccounts = new Resource('/api/v2/pay/accounts/byFlowIds')
const staffs = new Resource('/api/v1/organization/staffs')
const staffsV2 = new Resource('/api/v2/organization/staffs')
const glueStaffs = new Resource('/api/glue/v1/organization/staffs')
const departments = new Resource('/api/v1/organization/departments')
const externalDepartment = new Resource('/api/v1/organization/externalDepartment')
const attachments = new Resource('/api/v1/attachment/attachments')
const flows = new Resource('/api/flow/v1/flows')
const flowV2 = new Resource('/api/flow/v2')
const report = new Resource('/api/v1/budget/report')
const powers = new Resource('/api/charge/powers')
const staffItems = new Resource('/api/v1/basedata/staffDimensionItems')
const delegate = new Resource('/api/v1/organization/delegate')
const specification = new Resource('/api/v1/form/specifications')
const pay = new Resource('/api/pay/v1')
const payertaxno = new Resource('/api/v1/corporationset/payerinfo')
const third = new Resource('/api/ekbmall/v1/third')
const dimensionItems = new Resource('/api/v1/basedata/dimensionItems')
const requisition = new Resource('/api/form/v2/requisition')
const payeeActionV2 = new Resource('/api/pay/v2/payeeinfoconfigs')

const property = new Resource('/api/flow/v2/propertySet')

const permission = new Resource('/api/glue/v1/organization/permissions')
const permissionWithoutGlue = new Resource('/api/v1/organization/permissions')
const environment = new Resource('/api/v1/corporationset/environment')

const referables = new Resource('/api/flow/v2/referables')

const loanPackage = new Resource('/api/v1/loan/loanInfo')

const city = new Resource('/api/v1/basedata/city')
const cityNew = new Resource('/api/v2/basedata/city')
const specificationsAll = new Resource('/api/form/v2/specifications/search')
const specifications = new Resource('/api/form/v1/specificationVersions')
const apportion = new Resource('/api/form/v2/apportion')

const searchPrint = new Resource('/api/v1/print/remind/search')

const currencyAction = new Resource('/api/v2/currency')

const carbonCopy = new Resource('/api/flow/v2/carbonCopy')
const lastChoice = new Resource('/api/flow/v2/lastChoice')

const BillByCode = new Resource('/api/flow/v2/flows/code')

const riskwarning = new Resource('/api/flow/v2/riskwarning/coalition')

const singleInvoiceRiskWarning = new Resource('/api/flow/v3/riskwarning/invoice/riskWarning')

const extensionCenter = new Resource('/api/extension/center')
const staffSetting = new Resource('/api/v1/organization/staffs/staffSetting')
const setOrganization = new Resource('/api/v2/organization')

const invoiceAction = new Resource('/api/v2/invoice')
const tppAction = new Resource('/api/tpp/v2')
const spaceAction = new Resource('/api/dingtalk/v2/cspace')
const enumAction = new Resource('/api/v1/basedata/enumItems')
const safeSetting = new Resource('/api/v2/corporationset/watermark')
const condition = new Resource('/api/form/v2/condition')
const commonAccountAction = new Resource('/api/pay/v2/accounts/common')
const dynamicChannel = new Resource('/api/pay/v2/channel/dict')
const delegateAccess = new Resource('/api/glue/v1/organization/delegateSet')
const messageCenterConfig = new Resource('/api/message/v1/messageCenterConfig')

const countries = new Resource('/api/pay/v1/payeeInfoOversea/country')
const cities = new Resource('/api/pay/v1/payeeInfoOversea/city')

const tradeeditionAction = new Resource('/api/v1/tradeedition')
const isWhiteList = new Resource('/api/menu/v2/questionnaires/isWhiteList')

const fillAction = new Resource('/v2/upload/invoice')
const payerActionV2 = new Resource('/api/pay/v2/payerinfoconfigs')

const branchesSearch = new Resource('/api/pay/v1/branches/search')
const toggle = new Resource('/api/common/v1/toggle')

let joinAssignmentRule = `components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`
let joinReferenceData = `components.referenceData,referenceData,/v2/datalink/entity`

const authEnv = new Resource('/api/v2/environment')
const eCardAction = new Resource('/api/v1/ecard')
const virtualCard = new Resource('/api/engine/hdt/api/v1/virtual-card')

// 查询附件是否可以被下载
const fileCanDownload = new Resource('/api/v1/attachment/attachments/allowDownload')
import { fetchNodesAIAgentMap } from '../../elements/ai-agent-utils'

let specificationJoin = () => ({
  join: joinAssignmentRule,
  join$1: joinReferenceData
})

function getMessageCode() {
  return api.invokeService('@layout:dd:message:code')
}

const filterSystemBillStr = () => {
  const userInfo = api.getState()['@common'].me_info.staff
  const corpId = userInfo?.corporationId?.id
  return `INSTR(form.specificationId,"${corpId}:system:对账单")==0`
}

class CommonActions extends Actions {
  static pluginName = ID

  /***
   * 获取费用类型
   * @returns {{payload: *}}
   */
  @reducer(action => {
    let tree = get(action, 'payload.items') || {},
      list = [],
      map = {}
    if (tree.length) {
      const fn = children =>
        children.forEach(line => {
          list.push(line)
          line.children && line.children.length && fn(line.children)
        })
      fn(tree)
      list.forEach(o => (map[o.id] = o))
    }
    return { data: tree, list, map: cloneDeep(map) }
  })
  @node('feetypes', { data: [], list: [], map: {} })
  @action
  listFeeTypes() {
    return { payload: feeTypes.GET('/tree/active') }
  }

  /***
   * 获取行程类型
   * @returns {{payload: *}}
   */
  @reducer(action => {
    let tree = get(action, 'payload.items') || {},
      list = [],
      map = {},
      listActive = []
    if (tree.length) {
      const fn = children =>
        children.forEach(line => {
          list.push(line)
          line.children && line.children.length && fn(line.children)
        })
      fn(tree)
      list.forEach(o => (map[o.id] = o))
      listActive = list.filter(el => el.active === true)
    }
    return { data: tree, list, listActive, map }
  })
  @node('tripTypes', { data: [], list: [], listActive: [], map: {} })
  @action
  listTripTypes() {
    let selectQuery = new QuerySelect().orderBy('createTime', 'ASC').value()
    return {
      payload: tripTypes.POST('/search', selectQuery, {
        join: `specificationId,specification,/form/v1/specificationVersions?join=${joinAssignmentRule}&join=components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`
      })
    }
  }

  /**
   * 获取当前人是否可以下载附件
   * @returns
   */
  @reducer(action => action.payload.value)
  @node('fileCanDownload', false)
  @action
  getFileCanDownload() {
    return {
      payload: fileCanDownload.GET('/')
    }
  }

  @reducer(action => action.payload.items)
  @node('userPayeeAccount')
  @action
  listPayeesAction(params) {
    return { payload: payeesV2.GET('/mine', params) }
  }

  // 支付模块改版后的获取账户列表接口
  listPayees(params) {
    const searchNew = getBoolVariation('account_search_optimization', false)
    const { join, ...rest } = params
    const _join = isString(join) ? { join } : { ...join }
    return payeesV2.POST(searchNew ? '/mineByEs' : '/mine', rest, _join)
  }

  getStaffListAsync(params) {
    return setOrganization.POST('/staffs/list', params)
  }

  @reducer(action => get(action, 'payload.value'))
  @node('defaultPayee')
  @action
  getDefaultPayee() {
    return { payload: payeesV2.GET('/default') }
  }

  /***
   * 删除收款信息
   * @param id
   * @returns {{payload: *}}
   */

  @reducer
  @action
  deletePayee(id) {
    return { payload: payeesV2.PUT('/$id/disable', { id }) }
  }

  /***
   * 设置默认收款信息
   * @param id
   * @returns {{payload: *}}
   */

  @reducer
  @action
  setDefaultPayee(id) {
    return {
      payload: payeesV2.PUT('/$id/default', { id }).then(data => {
        api.dispatch(this.getDefaultPayee())
        return data
      })
    }
  }

  /***
   * 取消默认收款信息
   * @param accountId
   * @returns {{payload: *}}
   */

  @reducer
  @action
  cancelDefaultPayee(accountId) {
    return {
      payload: payeesV2.DELETE('/$accountId/removeDefault', { accountId }).then(data => {
        api.dispatch(this.getDefaultPayee())
        return data
      })
    }
  }

  // 补充收款账户身份证
  setCertificate(data) {
    return payeesV2.PUT('/$id/certificate', data)
  }

  @reducer
  @action
  updatePayee(value) {
    return {
      payload: getMessageCode()
        .then(messageCode => {
          return payeesV2.PUT('/$id', value, { messageCode })
        })
        .catch(e => Promise.reject(e))
    }
  }

  @reducer(function(action) {
    if (action.typeName === 'worker') {
      return { ...action.data }
    } else {
      let _this = this
      let data = action.payload.items
      data.length && api.dispatch(_this.listDepartments('worker', getWorkerResult('getDepartmentFullPath', data)))
      return { data, pathMap: {}, mapData: {}, noRootPathMap: {}, enPathMap: {}, noRootEnPathMap: {} }
    }
  })
  @node('departments', {})
  @action
  listDepartments(typeName, data) {
    return typeName ? { typeName, data } : { payload: departments.GET('/tree/actives') }
  }

  @reducer(function(action) {
    if (action.typeName === 'worker') {
      return { ...action.data }
    } else {
      const data = action.payload.items
      let _this = this
      data.length
        ? api.dispatch(_this.listExternalDepartments('worker', getWorkerResult('getDepartmentFullPath', data)))
        : api.dispatch(_this.listExternalDepartments('init'))
      if (action.typeName === 'init') {
        api.dispatch(_this.listExternalStaffs())
      }
      return { data, pathMap: {}, mapData: {}, noRootPathMap: {}, enPathMap: {}, noRootEnPathMap: {} }
    }
  })
  @node('externalDepartments', {})
  @action
  listExternalDepartments(typeName, data) {
    if (typeName === 'init') {
      return { typeName, payload: externalDepartment.POST('/initDept') }
    } else {
      return typeName ? { typeName, data } : { payload: externalDepartment.GET('/tree') }
    }
  }

  /***
   * 根据可见性过滤出全部的可见部门数据
   * @param page
   * @returns {{payload: *}}
   */
  @reducer(function(action) {
    if (action.typeName === 'worker') {
      return { ...action.data }
    } else {
      let _this = this
      let data = action.payload.items
      data.length &&
        api.dispatch(_this.listVisibilityDepartments(null, 'worker', getWorkerResult('getDepartmentFullPath', data)))
      return { data, pathMap: {}, mapData: {}, noRootPathMap: {}, enPathMap: {}, noRootEnPathMap: {} }
    }
  })
  @node('departmentsVisibility', {})
  @action
  listVisibilityDepartments(staffId, typeName, data) {
    return typeName ? { typeName, data } : { payload: departments.GET('/tree/visibility/actives', { staffId }) }
  }

  /***
   * 获取企业外部员工列表
   * @param page
   * @returns {{payload: *}}
   */

  @reducer(action => action.payload.items)
  @node('externalStaffs', [])
  @action
  listExternalStaffs() {
    let items = []
    let pages = 1
    let pageSize = 2999
    let params = {
      start: 0,
      count: pageSize,
      external: true,
      range: true
    }
    let getExternalStaffs = () => {
      return staffs.GET('/actives', params).then(async resp => {
        items = items.concat(resp.items)
        // 人员是有可见性的，前端传了pageSize，但是根据可见性返回的不一定是pageSize的个数，所以不能按照pageSize去判断是否还有剩余的
        if (resp.items.length !== 0) {
          params.start = pages * pageSize
          pages++
          return getExternalStaffs()
        }
        // 把外部员工转换为一个map对象
        await api.dispatch(this.externalStaffMapAction(items))
        return { items }
      })
    }
    return { payload: getExternalStaffs() }
  }

  @reducer(action => action.payload)
  @node('externalStaffMap', {})
  @action
  externalStaffMapAction(staffs) {
    const parseStaffsMap = (staffs = []) => {
      const staffMap = {}
      if (!staffs?.length) {
        return staffMap
      }
      staffs.forEach(staff => {
        staffMap[staff.id] = staff
      })
      return staffMap
    }
    return { payload: parseStaffsMap(staffs) }
  }

  /***
   * 获取企业外部已移除删除的员工
   * @param page
   * @returns {{payload: *}}
   */

  @reducer(action => action.payload.items)
  @node('externalStaffsRemoved', [])
  @action
  listExternalStaffsRemoved() {
    let items = []
    let pages = 1
    let pageSize = 2999
    let params = {
      start: 0,
      count: pageSize,
      external: true,
      range: false
    }
    let getExternalStaffsRemoved = () => {
      return staffs.GET('/actives', params).then(resp => {
        items = items.concat(resp.items)
        if (resp.items.length !== 0) {
          params.start = pages * pageSize
          pages++
          return getExternalStaffsRemoved()
        }
        return { items }
      })
    }
    return { payload: getExternalStaffsRemoved() }
  }

  /**
   * 获取限制范围内的人员
   * @returns {{payload : *}}
   */
  @reducer(action => ({
    data: action.payload.items,
    count: action.payload.count
  }))
  @node('staffRangeByRule', { data: [] })
  @action
  getStaffRangeByRule(ags) {
    return this.getStaffByRule(ags)
  }
  //根据人员可选范围id获取人员
  getStaffByRule(data = {}) {
    let items = []
    let pages = 1
    const pageSize = 2999
    const params = {
      start: 0,
      count: pageSize,
      ...data
    }
    const getStaffsByRule = () => {
      return condition.POST('/getStaffList/$ruleId', params).then(resp => {
        items = items.concat(resp.items)
        if (resp.items.length === pageSize) {
          params.start = pages * pageSize
          pages++
          return getStaffsByRule()
        }
        return { items }
      })
    }
    return { payload: getStaffsByRule() }
  }

  /***
   * 企业中所有被授权激活的人员
   * @param page
   * @returns {{payload: *}}
   */

  @reducer(action => action.payload.items)
  @node('staffs', [])
  @action
  listStaffs() {
    if (enableStaffApiSwitch()) {
      return this.listStaffsV2()
    }
    let items = []
    let pages = 1
    let pageSize = 2999
    let params = {
      start: 0,
      count: pageSize,
      join: 'defaultDepartment,defaultDepartment,/v1/organization/departments'
    }
    let getStaffs = () => {
      return glueStaffs.GET('/authorized', params).then(async resp => {
        items = items.concat(resp.items)
        //返回值中的count(resp.count), 是后端返回的items数组的元素数量，不是全部的数量
        if (resp.count === pageSize) {
          params.start = pages * pageSize
          pages++
          return getStaffs()
        }
        await api.dispatch(this.authStaffStaffMap(items))
        return { items }
      })
    }
    return { payload: getStaffs() }
  }

  listStaffsV2() {
    let items = []
    let pages = 1
    let pageSize = 2999
    let params = {
      query: {
        limit: {
          start: 0,
          count: pageSize
        },
        filterBy: '(authState==true)'
      },
      maskSensitiveInfo: true
    }
    const qs = {
      join: 'defaultDepartment,defaultDepartment,/v1/organization/departments'
    }
    let getStaffs = () => {
      return staffsV2.POST('/list', params, qs).then(async resp => {
        items = items.concat(resp.items || [])
        //返回值中的count(resp.count), 是全部的数量
        if (resp.count > items.length) {
          params.query.limit.start = pages * pageSize
          pages++
          return getStaffs()
        }
        await api.dispatch(this.authStaffStaffMap(items))
        return { items }
      })
    }
    return { payload: getStaffs() }
  }

  @reducer(action => action.payload)
  @node('authStaffStaffMap', {})
  @action
  authStaffStaffMap(staffs) {
    const parseStaffsMap = (staffs = []) => {
      const staffMap = {}
      if (!staffs?.length) {
        return staffMap
      }
      staffs.forEach(staff => {
        staffMap[staff.id] = staff
      })
      return staffMap
    }
    return { payload: parseStaffsMap(staffs) }
  }

  /**
   * 获取可见性员工列表
   * 普通用户可见的界面：包括
   单据中选择的部门架构（选人组件、选部门组件），和评论艾特功能的架构，以及委托授权的架构（按提交人的权限），还有审批流选择人员、转交审批，申请事项的转交、共享
   * @returns {{payload : *}}
   */

  @reducer(action => action.payload.items)
  @node('staffsVisibility', [])
  @action
  getVisibilityStaffsAuthorized(staffId) {
    if (enableStaffApiSwitch()) {
      return this.getVisibilityStaffsAuthorizedV2(staffId)
    }
    let items = []
    let pages = 1
    let pageSize = 2999
    let params = {
      start: 0,
      count: pageSize,
      staffId,
      join: 'defaultDepartment,defaultDepartment,/v1/organization/departments'
    }
    let getVisibilityStaffs = () => {
      return glueStaffs.GET('/visibility/authorized', params).then(async resp => {
        items = items.concat(resp.items)
        // 人员是有可见性的，前端传了pageSize，但是根据可见性返回的不一定是pageSize的个数，所以不能按照pageSize去判断是否还有剩余的
        if (resp.items.length !== 0) {
          params.start = pages * pageSize
          pages++
          return getVisibilityStaffs()
        }
        await api.dispatch(this.parseStaffsVisibilityMap(items))
        return { items }
      })
    }
    return { payload: getVisibilityStaffs() }
  }

  getVisibilityStaffsAuthorizedV2(staffId) {
    let items = []
    let pages = 1
    let pageSize = 2999
    let params = {
      query: {
        limit: {
          start: 0,
          count: pageSize
        },
        filterBy: '(authState==true)'
      },
      maskSensitiveInfo: true,
      visible: true,
      visibleRestrictedStaffId: staffId
    }

    const qs = {
      join: 'defaultDepartment,defaultDepartment,/v1/organization/departments'
    }

    let getVisibilityStaffs = () => {
      return staffsV2.POST('/list', params, qs).then(async resp => {
        items = items.concat(resp.items || [])
        // 人员是有可见性的，前端传了pageSize，但是根据可见性返回的不一定是pageSize的个数，所以不能按照pageSize去判断是否还有剩余的
        if (resp.count > items.length) {
          params.query.limit.start = pages * pageSize
          pages++
          return getVisibilityStaffs()
        }
        await api.dispatch(this.parseStaffsVisibilityMap(items))
        return { items }
      })
    }
    return { payload: getVisibilityStaffs() }
  }

  @reducer(action => action.payload.items)
  @node('activeStaffs', [])
  @action
  getActiveStaffs() {
    let items = []
    let pages = 1
    const pageSize = 2999
    const params = {
      start: 0,
      count: pageSize,
      range: true
    }
    const getStaffs = () => {
      return staffs.GET('/actives', params).then(async resp => {
        items = items.concat(resp.items)
        //返回值中的count(resp.count), 是后端返回的items数组的元素数量，不是全部的数量
        if (resp.count === pageSize) {
          params.start = pages * pageSize
          pages++
          return getStaffs()
        }
        return { items }
      })
    }
    return { payload: getStaffs() }
  }

  /***
   * 企业全员
   * @param page
   * @returns {{payload: *}}
   */

  @reducer(action => action.payload.items)
  @node('staffsActives', [])
  @action
  listStaffsActives(page) {
    return { payload: staffs.GET('/actives', page) }
  }

  /***
   * 黑名单
   * @returns {{}}
   */

  @reducer((action, state) => {
    const staffs = state.staffs.map(el => el.id)
    const staffsActives = state.staffsActives.map(el => el.id)
    const staffsBlacklist = staffsActives.filter(v => !staffs.includes(v))
    return { staffsBlacklist }
  })
  @node('staffsBlacklist', [])
  @action
  listStaffsBlacklist() {
    return {}
  }

  @reducer
  @action
  getStaffById(data) {
    return { payload: staffs.GET('/$id', data) }
  }

  @reducer
  @action
  getStaffByIds(data) {
    return { payload: staffs.GET('/[ids]', data) }
  }

  @reducer(action => action.payload.items)
  @node('staffsAdmin', [])
  @action
  getStaffsByPermission(data) {
    return { payload: staffs.GET('/byPermission', data) }
  }

  /***
   *
   * @param data
   * @returns {{payload: *}}
   */

  @reducer
  @action
  createPayee(data) {
    return { payload: payeesV2.POST(data) }
  }

  @reducer
  @action
  editPayee(data) {
    return { payload: payeesV2.PUT(`/$${data.id}`, data) }
  }

  /***
   *
   * @param flowId
   * @param node
   * @returns {{payload: *}}
   */

  @reducer
  @action
  updatePlanNode(flowId, node) {
    flowId = { flowId }
    return { payload: flows.POST('/$flowId', node, flowId) }
  }

  @reducer
  @action
  getAttachmentToken() {
    return { payload: attachments.GET('/token') }
  }

  @reducer
  @action
  getAttachmentDetail(id) {
    let payload

    if (Array.isArray(id)) {
      payload = attachments.GET('/[ids]', { ids: id })
      return { payload }
    }

    payload = attachments.GET('/$id', { id })
    return { payload }
  }

  /**
   * 根据订单号查询单据详情
   */
  @reducer(action => {
    const { flow, buttons, backLogId } = get(action, 'payload.value') || {}
    return {
      scan_data: { flow, backLogId },
      billActions: buttons
    }
  })
  @action
  getBillByCode(code) {
    showLoading()
    return {
      payload: BillByCode.POST('/$code', { code }).then(data => {
        hideLoading()
        return data
      })
    }
  }

  /**
   * 获取单条单据详情
   * 'current_flow' 作为key，不必dispatch传入
   * @param query
   * @returns {{type: string, payload: *}}
   */
  @reducer(action => {
    if (action.isUpdate) {
      let current_flow =
        action.billstype === 'carbonCopy' ? get(action, 'payload.value.flowId') : get(action, 'payload.value')
      return { ...current_flow }
    }
    return null
  })
  @node('current_flow', null)
  @action
  getFlowDetailInfo(param = {}, isUpdate = true, useOptimization = false) {
    if (useOptimization && enableBillDetailOptimization()) {
      return this.getFlowDetailInfoV2(param, isUpdate)
    }

    let { budgetId, nodeId, flowVersionedId, id, type, isBack = true } = param
    let flowId = flowVersionedId ? flowVersionedId : id
    let selectQuery = new QuerySelect()
      .filterBy(`id=="${decodeURIComponent(flowId)}"`)
      .select(`${selectEntity()}`)
      .value()
    let params = {
      ...joinParam(),
      ...param
    }
    let request,
      prefix = ''
    if (budgetId && nodeId) {
      // 查询预算中 已用详情中 报销单的详情
      request = report.POST('/detail/flow/$id', selectQuery, params)
    } else if (flowVersionedId) {
      //查询历史单据
      request = flowV2.POST('/flowVersioneds/version/$flowVersionedId', selectQuery, params).then(resp => {
        let records = get(resp, 'value.writtenOff.records') || []
        if (records.length > 0) {
          return loanPackage.GET('/[ids]', { ids: records.map(o => o.loanInfoId) }).then(res => {
            let loansMap = {}
            res.items.forEach(o => (loansMap[o.id] = o))
            records.forEach(record => (record.loanInfoId = loansMap[record.loanInfoId]))
            return resp
          })
        }
        return resp
      })
    } else if (type && type === 'carbonCopy') {
      prefix = 'flowId'
      selectQuery = new QuerySelect()
        .filterBy(`id=="${decodeURIComponent(flowId)}"`)
        .select(`${selectEntity('flowId')}`)
        .value()
      let params = joinParam(`${prefix}.`)
      params = { ...params, id: flowId }
      request = carbonCopy.POST(`/$id`, selectQuery, params)
    } else {
      const postFlows =
        type === 'formRequisition'
          ? requisition.POST('/$id/searchflow', selectQuery, params)
          : flows.POST('/flowId/$id', selectQuery, params)
      request = postFlows
        .then(res => {
          let flow = get(res, 'value')
          let records = get(flow, 'writtenOff.records', [])
          let recordMap = {}
          records.forEach(record => {
            if (!(record.loanInfoId instanceof Object)) {
              recordMap[record.loanInfoId] = record
            }
          })
          return res
        })
        .catch(err => {
          if (err.errorCode === 412) {
            toast.error(err.errorMessage)
            return Promise.reject(err)
          }
          if (isBack) {
            setTimeout(() => api.go(-1), 2000)
          }
          return Promise.reject(err)
        })
    }
    return {
      isUpdate,
      payload: request.then(res => fetchAttachment(res, prefix)).then(res => fetchInvoice(res, prefix)),
      billstype: type
    }
  }

  getFlowDetailInfoV2(param = {}, isUpdate = true) {
    let { budgetId, nodeId, flowVersionedId, id, type, isBack = true } = param
    let flowId = flowVersionedId ? flowVersionedId : id
    let selectQuery = new QuerySelect()
      .filterBy(`id=="${decodeURIComponent(flowId)}"`)
      .select(`${selectEntity()}`)
      .value()
    let params = {
      ...joinParam(),
      ...param
    }
    let request,
      prefix = ''

    // 主接口是否携带了 plan 数据
    let withPlanData = true
    if (budgetId && nodeId) {
      // 查询预算中 已用详情中 报销单的详情
      request = report.POST('/detail/flow/$id', selectQuery, params)
    } else if (flowVersionedId) {
      //查询历史单据
      request = flowV2.POST('/flowVersioneds/version/$flowVersionedId', selectQuery, params).then(resp => {
        let records = get(resp, 'value.writtenOff.records') || []
        if (records.length > 0) {
          return loanPackage.GET('/[ids]', { ids: records.map(o => o.loanInfoId) }).then(res => {
            let loansMap = {}
            res.items.forEach(o => (loansMap[o.id] = o))
            records.forEach(record => (record.loanInfoId = loansMap[record.loanInfoId]))
            return resp
          })
        }
        return resp
      })
    } else if (type && type === 'carbonCopy') {
      prefix = 'flowId'
      selectQuery = new QuerySelect()
        .filterBy(`id=="${decodeURIComponent(flowId)}"`)
        .select(`${selectEntity('flowId')}`)
        .value()
      let params = joinParam(`${prefix}.`)
      params = { ...params, id: flowId }
      request = carbonCopy.POST(`/$id`, selectQuery, params)
      // 上面的 flowId 其实是抄送ID，需要替换为实际的flowId
      if (param.__flowId) {
        flowId = param.__flowId
      }
    } else {
      let selectQueryV2 = new QuerySelect()
        .filterBy(`id=="${decodeURIComponent(flowId)}"`)
        .select(`${selectEntityV2()}`)
        .value()

      withPlanData = type === 'formRequisition'
      const postFlows =
        type === 'formRequisition'
          ? requisition.POST('/$id/searchflow', selectQuery, params)
          : flows.POST('/flowId/$id', selectQueryV2, params)
      request = postFlows
        .then(res => {
          let flow = get(res, 'value')
          let records = get(flow, 'writtenOff.records', [])
          let recordMap = {}
          records.forEach(record => {
            if (!(record.loanInfoId instanceof Object)) {
              recordMap[record.loanInfoId] = record
            }
          })
          return res
        })
        .catch(err => {
          if (err.errorCode === 412) {
            toast.error(err.errorMessage)
            return Promise.reject(err)
          }
          if (isBack) {
            setTimeout(() => api.go(-1), 2000)
          }
          return Promise.reject(err)
        })
    }

    return {
      isUpdate,
      payload: Promise.all([request, fetchInvoiceV2(flowId), fetchPlanConfig(flowId, withPlanData)]).then(res => {
        const [flowInfo, invoiceInfo, planData] = res
        if (!withPlanData && flowInfo.value) {
          flowInfo.value.plan = planData
        }
        return handleInvoiceData(flowInfo, invoiceInfo, prefix)
      }),
      billstype: type
    }
  }

  /**
   * 提交单子成功后清空单条单据详情
   * 'current_flow' 作为key，不必dispatch传入
   */
  @reducer(action => {
    return null
  })
  @node('current_flow', null)
  @action
  clearFlowDetailInfo() {
    return {
      payload: Promise.resolve()
    }
  }

  /**
   * 根据单据号获取单据所有风险
   * @param {Object} params
   * @param {OutOfLimitReject} params.level 风险级别 枚举类型(OutOfLimitReject) 选填 不传则为所有级别
   * @param {String} params.types 风险的类型 budget | invoice | loan | costControl | datalink
   */
  @reducer(action => get(action, 'payload.value'))
  @node('riskwarning')
  @action
  getRiskwarningById(params) {
    const { flowId, types, level, reasonModify = false } = params
    const param = {
      flowId: flowId
    }
    if (level) {
      param.level = level
    }
    if (types) {
      param.types = types.join(',')
    }
    return {
      payload: Promise.all([
        riskwarning.GET('/info/$flowId', { ...param, newUI: 'v2' }),
        singleInvoiceRiskWarning.GET('/[flowId]', { flowId: flowId, ...params }).catch(() => {
          return Promise.resolve({
            items: []
          })
        })
      ]).then(result => {
        if (result[0]?.value) {
          result[0].value = riskWaringSort(result[0].value)
        }
        //金龙借款未核销提示需前端重新编辑
        if (IS_SZJL) {
          const loanRiskWarning =
            result[0]?.value?.riskWarning?.filter(v => {
              return v.type === 'loan'
            }) || []
          loanRiskWarning.map(loan => {
            loan?.messages?.map((text, index) => {
              // @i18n-ignore
              if (text === '有可核销借款但未核销' || text === 'Have a write-off of the loan but have not written off') {
                loan.messages[index] = i18n.get('有可核销的借款或预付款尚未核销')
              }
            })
          })
        }
        const cloneRiskWaring = cloneDeep(result[0])
        const currentFlow = result[1]?.items?.find(v => v.flowId == flowId) || {}
        const curWarningMsg = currentFlow?.invoiceMsg || []
        // 重组新的发票msg数据,取自一个新的实时查询接口  ---- start
        const riskWarningData = reorganizeRiskWarningData(curWarningMsg) || []
        if (currentFlow.versionFlag > 1) {
          let otherRiskWarning = []
          // 票据连号需要填写原因，输出判断条件
          if (reasonModify) {
            result[0]?.value?.riskWarning?.forEach(v => {
              if (
                (v.type === 'invoice' && (v?.controlName === '票据连号：' || v?.controlName === '存在连号票据：')) ||
                (v.type !== 'invoice' && v?.controlName === '')
              ) {
                otherRiskWarning.push(v)
              }
            })
          } else {
            otherRiskWarning = result[0]?.value?.riskWarning?.filter(v => {
              return v.type !== 'invoice'
            })
          }
          const newRiskWarning = [...otherRiskWarning, ...riskWarningData]
          result[0].value.riskWarning = newRiskWarning
        }
        // 重组新的发票msg数据,取自一个新的实时查询接口  ---- end
        // 票据连号原因写进当前messages展示
        const noRiskWarningReason = currentFlow?.invoiceMsg?.every(item => !item.riskWarningReason?.length)
        if (currentFlow && noRiskWarningReason && currentFlow?.invoiceRiskExplainContent?.length) {
          result[0]?.value?.riskWarning?.forEach(v => {
            if (v?.controlName === '票据连号：' || v?.controlName === '存在连号票据：') {
              let item = i18n.get('连号原因：') + currentFlow?.invoiceRiskExplainContent
              v.messages.push(item)
            }
          })
        }
        const res = {
          ...result[0],
          riskWarningV2: cloneRiskWaring?.value?.riskWarning || [],
          singleInvoiceRiskWarning: curWarningMsg,
          invoiceRiskExplainContent: currentFlow.invoiceRiskExplainContent
        }
        return res
      })
    }
  }

  /**
   * 获取申请单详情，后台为了判断权限新写了个专门获取申请单flow详情的接口，目前只用在单据填写界面
   * @param param
   * @returns {{payload: *}}
   */
  @reducer(action => get(action, 'payload.value'))
  @action
  getRequisitionDetailInfo(param = {}) {
    let { id } = param
    let selectQuery = new QuerySelect()
      .filterBy(`id=="${decodeURIComponent(id)}"`)
      .select(`${selectEntity()}`)
      .value()
    let params = {
      ...param,
      ...joinParam()
    }
    return {
      payload: requisition
        .POST('/$id/searchflow', selectQuery, params)
        .then(fetchAttachment)
        .then(fetchInvoice)
    }
  }

  @reducer(action => action.payload.items)
  @node('budgetYears', [])
  @action
  getBudgetYears() {
    return { payload: report.GET('/allyear') }
  }

  @reducer(action => {
    let items = action?.payload?.items || []
    const { powerMap, powerCodeMap } = ParsePower(items)
    return {
      ...powerMap,
      powerCodeMap,
      powersList: items
    }
  })
  @node('powers', {}, true)
  @action
  getPowers() {
    return { payload: powers.GET() }
  }

  @reducer(action => {
    return action?.payload || {}
  })
  @node('nodesAIAgentMap', {}, true)
  @action
  getNodesAIAgentMap({ currentMap, nodes, logs }) {
    return { payload: fetchNodesAIAgentMap(currentMap, nodes, logs) }
  }

  /***
   * 预定的可见性
   * @returns {{payload: *}}
   */

  @reducer(action => get(action, 'payload.value'))
  @node('corpConsume')
  @action
  getCorpConsume() {
    return { payload: powers.GET('/corporation/CorpConsume', {}) }
  }

  // @reducer(action => action.payload.items)
  @reducer(action => {
    let _items = action?.payload?.items || []
    _items = _items?.map(item => {
      const children = item?.children?.map(v => ({ ...v, fromNodeId: item.nodeId }))
      return { ...item, children, fromNodeId: item?.fromNodeId ?? item.nodeId }
    })
    return _items
  })
  @node('flowReportList')
  @action
  getFlowReportList(param) {
    return { payload: report.GET('/flow/$id', param) }
  }

  @reducer
  @node
  @action
  getBudgetStatus(param) {
    return { payload: report.GET('/flow/$id/status', param) }
  }

  @reducer(actions => {
    let budgetMsg = {}
    let msgObj = actions.payload.items[0]
    msgObj && (budgetMsg[msgObj.id] = msgObj)
    return budgetMsg
  })
  @node('budgetMsg', {})
  @action
  getBudgetMsg(ids) {
    return { payload: report.GET('/message/flow/[ids]', { ids }) }
  }

  @reducer
  @action
  getPropertyById(id) {
    return { payload: staffItems.GET('/byPropertyId/$id', { id }) }
  }

  @reducer(action => action.payload.items)
  @node('staffProperties', [])
  @action
  getStaffProperties() {
    return { payload: property.GET('/$type', { type: 'STAFF' }) }
  }

  @reducer(action => action.payload.items)
  @node('referables')
  @action
  getPropertyByName(param) {
    return { payload: referables.GET('/$name', param) }
  }

  @reducer(action => action.payload.value)
  @node('MCPermission')
  @action
  getMCPermission(permissionName) {
    return { payload: permission.GET('/query', { model: permissionName }) }
  }

  @reducer(action => action.payload.items)
  @action
  getFullName(ids) {
    return { payload: dimensionItems.GET(`/fullName/[ids]`, { ids }) }
  }

  @reducer(action => action.payload.items)
  @node('referables')
  @action
  getPropertyWithFullPathByName(param) {
    return { payload: referables.GET('/$name', param) }
  }

  @reducer(action => action.payload)
  @action
  getDimensionById(param) {
    return { payload: dimensionItems.POST('/byEntity/$id/search', param) }
  }

  @reducer
  @action
  getPropertyByNames(names) {
    return { payload: referables.GET('/[names]', { names }) }
  }

  @reducer(action => action.payload.items)
  @node('delegators')
  @action
  getDelegators() {
    const data = {
      join: 'defaultDepartment,defaultDepartment,/v1/organization/departments'
    }
    return {
      payload: delegate.GET('/byDelegateeId', data)
    }
  }

  @reducer
  @action
  getSpecification(id) {
    return { payload: specification.GET('/$id', id) }
  }

  @reducer(action => action.payload.value)
  @node('getSpecificationByOriginalId')
  @action
  getSpecificationByOriginalId(id) {
    return { payload: specifications.GET('/originalId/$id', id) }
  }

  @reducer
  @action
  getSpecificationByIds(ids) {
    return { payload: specifications.GET('/[ids]', { ids }) }
  }

  /***
   * 更新过滤的费用类型
   * @param filterFeeTypes
   * @returns {{payload: *}}
   */

  @reducer(action => action.payload.filterFeeTypes)
  @node('filterFeeTypes')
  @action
  getFilterFeeTypes(filterFeeTypes) {
    return { payload: filterFeeTypes }
  }

  /****
   * 费用类型过滤接口(模版限制，档案关系限制)
   * @param formData
   * @returns {{payload:feeType}}
   */
  @reducer(action => {
    const items = action.payload.items || []
    let map = undefined
    if (items.length) {
      map = {}
      visit(items, node => {
        map[node.id] = node
      })
    }
    return { items, map }
  })
  @node('newFilterFeeTypes', { items: [], map: undefined }) // map赋值为undefined，如果为空就不做任何过滤，用的时候要判断一下map是否存在
  @action
  newGetFilterFeeTypeList(formData) {
    return { payload: feeTypes.POST('/list', formData) }
  }

  /***
   * 更新过滤的行程类型
   * @param filterTripTypes
   * @returns {{payload: *}}
   */

  @reducer(action => action.payload.filterTripTypes)
  @node('filterTripTypes')
  @action
  getFilterTripTypes(filterTripTypes) {
    return { payload: filterTripTypes }
  }

  /***
   * 获取企业账户相关接口
   * @returns {{payload: *}}
   */

  @reducer(action => action.payload.items)
  @node('payAccountList', [])
  @action
  getPayAccountList(args) {
    const { flowIds, isManual = false } = args || {}

    const params = {
      ids: flowIds,
      deviceType: 'MOBILE' //标识应用类型
    }

    if (isManual) params.ids = []
    return { payload: payAccounts.POST('', params) }
  }

  @reducer(action => action.payload.items)
  @node('cityList')
  @action
  getCityList(data) {
    return { payload: pay.GET('/banks/cities', data) }
  }

  @reducer(action => action.payload.items)
  @node('provinceList')
  @action
  getProvinceList() {
    return { payload: pay.GET('/banks/provinces') }
  }

  @reducer(action => {
    let branchList = action.payload.items
    branchList.push(i18n.get('其他'))
    return branchList
  })
  @node('branchList')
  @action
  getBranchList(data) {
    return { payload: pay.GET('/banks/branches', data) }
  }

  @reducer(action => {
    const { searchText } = action
    let branchList = action.payload.items
    // if (!searchText || (searchText && searchText === i18n.get('其他'))) {
    //   branchList.push({ name: i18n.get('其他') })
    // }
    return action.payload.items
  })
  @node('branchByCityIdList')
  @action
  getBranchByCityIdList(data) {
    return { payload: pay.GET('/banks/branchesByKeyNew', data), searchText: data.searchKey }
  }

  @reducer(action => {
    let bankList = action.payload.items
    bankList.push({
      icon: 'https://images.ekuaibao.com/bank/bank-other.svg',
      name: i18n.get('其他')
    })
    return bankList
  })
  @node('bankList')
  @action
  getBankList(data) {
    return { payload: pay.GET('/banks/banks', data) }
  }

  @reducer(action => action.payload.items)
  @node('unionBankList')
  @action
  getUnionBankList(data) {
    return { payload: pay.GET('/banks/getUnionBanks', data) }
  }

  @reducer(action => action?.payload?.items)
  @node('payerInfo', [])
  @action
  getPayerInfo(param = {}) {
    const data = {
      join: 'visibility.staffs,staffs,/v1/organization/staffs?select=id,name',
      join$1: 'visibility.departments,departments,/v1/organization/departments?select=id,name',
      join$2: 'visibility.roles,roles,/v1/organization/roles?select=id,name'
    }
    return { payload: payertaxno.GET('', { ...data, ...param }) }
  }

  /***
   * 获取全局字段
   * @returns {{payload: *}}
   */

  @reducer(action => {
    const data = action.payload.items || []
    const extensionFields = data.filter(v => !v.ability)
    const preset = data.filter(v => v.ability)
    const baseDataPropertiesMap = {}
    data.forEach(line => (baseDataPropertiesMap[line.name] = line))
    return { data, extensionFields, preset, baseDataPropertiesMap }
  })
  @node('baseDataProperties', { data: [], extensionFields: [], preset: [], baseDataPropertiesMap: {} })
  @action
  getBaseDataProperties() {
    return { payload: property.GET('') }
  }

  /***
   * 同步企业是否购买了预算使用权限
   * @param budgetCharge
   * @returns {{payload: *}}
   */

  @reducer(action => action.payload.budgetCharge)
  @node('budgetCharge')
  @action
  updateBudgetCharge(budgetCharge) {
    return { payload: budgetCharge }
  }

  @reducer(action => get(action, 'payload.value'))
  @node('payeeConfig', {})
  @action
  getPayeeSharingStatue() {
    return { payload: payeeActionV2.GET('') }
  }

  @reducer(action => action.payload.items)
  @node('permissions', [])
  @action
  getTotalPermission() {
    return { payload: permission.GET('') }
  }

  //通过权限名称 获取权限下的管理员人员和角色
  @reducer(action => action.payload.value)
  @node('staffsAndRolesNames', [])
  @action
  staffsAndRolesByName(param) {
    return { payload: permissionWithoutGlue.GET('/$name/authorizedInfo', param) }
  }

  /**
   * 获取上传地址，覆盖默认地址
   * @returns {{payload : *}}
   */

  @reducer(action => {
    const data = get(action, 'payload.value')
    let { uploadServiceUrl } = data || {}
    if (uploadServiceUrl) {
      window.UPLOAD_INVOICE_FILE_URL = uploadServiceUrl
    }
    return data
  })
  @node('uploadServiceUrl', {})
  @action
  getEnvironment() {
    return { payload: environment.GET() }
  }

  @reducer(action => action.payload.items)
  @node('submitter_loans')
  @action
  getSubmitterLoanList(param) {
    let { submitterId } = param
    submitterId = typeof submitterId === 'string' ? submitterId : submitterId.id
    param.submitterId = submitterId
    return {
      payload: loanPackage.GET('/byOwnerIdAndLoanInfoId/$submitterId', param)
    }
  }

  @reducer()
  @action
  getloanpackageDetailInfo(data) {
    let departments = 'departments,departmentArr,/v1/organization/departments?select=id,name'
    let params = {
      ...data,
      join: `repaymentRecords.casherId,staff,/v1/organization/staffs?join=${departments}`,
      join$1: `repaymentRecords.accountId,accountCompany,/pay/v1/accounts`,
      join$2: `repaymentRecords.ownerId,ownerId,/v1/organization/staffs`,
      join$3: `repaymentRecords.shiftStaffId,shiftStaffId,/v1/organization/staffs`,
      join$4: `flowSpecificationId,flowSpecificationId,/form/v1/specificationVersions`
    }
    return {
      payload: loanPackage.GET('/$id', params)
    }
  }

  @reducer(action => action.payload.items)
  @node('citySearch')
  @action
  searchCity(keyword) {
    keyword = JSON.stringify(keyword).replace(/%/g, '\\\\%')
    let query = new QuerySelect()
      .filterBy(
        `name.containsIgnoreCase(${keyword})||enName.containsIgnoreCase(${keyword})||fullName.containsIgnoreCase(${keyword})||nameSpell.containsIgnoreCase(${keyword})`
      )
      .limit(0, 50)
    query = query.value()
    return { payload: city.POST('/search', query) }
  }
  @reducer
  @action
  newSearchCity(keyword) {
    keyword = JSON.stringify(keyword).replace(/%/g, '\\\\%')
    let query = new QuerySelect()
      .filterBy(
        `name.containsIgnoreCase(${keyword})||enName.containsIgnoreCase(${keyword})||fullName.containsIgnoreCase(${keyword})||nameSpell.containsIgnoreCase(${keyword})`
      )
      .limit(0, 50)
    query = query.value()
    return { payload: cityNew.POST('/search', query) }
  }

  @reducer(action => action.payload.items)
  @node('myBillList')
  @action
  getMyBill(params) {
    const { limit, hasArchived, filterSystemBill } = params
    const delegatedFilter = get(params, 'delegatedFilter')
    let query = new QuerySelect().filterBy((params && params.filterBy) || '').select(`
      calcRiskWarning(...),
      form(
        specificationId(name,id),
        submitterId(id,name,enName,avatar),
        requisitionMoney,loanMoney,expenseMoney,title,code,payMoney
        )
      ,state
      ,formType
      ,logs
      ,createTime
      ,id`)
    if (limit) {
      query = query.limit(limit.start, limit.count)
    } else {
      query = query.limit(0, 600)
    }
    if (!hasArchived) {
      query = query.filterBy('state != "archived"')
    }
    if (filterSystemBill) {
      query.filterBy(filterSystemBillStr())
    }
    if (params.orderBy) {
      params.orderBy.forEach(item => {
        const { value, order } = item
        query[order]?.(value)
      })
    }
    query = query.value()
    return { payload: flows.POST('/my', query, { type: 'home5', delegatedFilter }) }
  }

  @reducer
  @action
  getPrintBill(params) {
    const { limit } = params
    let query = new QuerySelect()
      .filterBy(`active == true`)
      .desc('flowId.form.code')
      .select(`${selectEntity('flowId', true)}`)
    if (limit) {
      query = query.limit(limit.start, limit.count)
    } else {
      query = query.limit(0, 600)
    }

    query = query.value()
    const param = {
      join: 'flowId.plan.nodes.approverId,approver,/v1/organization/staffs?select=id,name',
      ...joinMoney('flowId.')
    }
    return {
      payload: searchPrint.POST('', query, param).then(res => {
        const { count, items } = res
        const dataSource = items.map(element => element.flowId)
        return { items: dataSource, count }
      })
    }
  }

  @reducer(actions => actions.payload.items)
  @node('apportionsSpecifications')
  @action
  getApportionSpecifications() {
    let data = {
      type: 'apportion',
      ...specificationJoin()
    }
    return { payload: specifications.GET('/actives', data) }
  }

  @reducer(actions => actions.payload.items)
  @node('amortizeSpecifications')
  @action
  getAmortizeSpecifications() {
    let data = {
      type: 'amortize',
      ...specificationJoin()
    }
    return { payload: specifications.GET('/actives', data) }
  }

  /***
   * 获取导入公务卡的可见性
   * @returns {{type: string, payload}}
   */

  @reducer(action => {
    return get(action, 'payload.value.open')
  })
  @node('isHaveOfficialCardPower')
  @action
  getCorpOfficialCard() {
    return { payload: third.GET('/account/isOpen/$code', { code: '120203' }) }
  }

  /**
   * 系统租户本位币，没有开通法人多币种时，就是企业的本位币
   */
  @reducer(action => {
    const value = get(action, 'payload.value') || {}
    window.CURRENCY_SYMBOL = value.symbol || i18n.get('￥')
    window.CURRENCY_STRCODE = value.strCode || 'CNY'
    return value
  })
  @node('standardCurrency', {})
  @action
  getStandardCurrency() {
    return {
      payload: currencyAction.GET('/standard')
    }
  }

  /**
   * 法人实体多币种多本位币列表
   */

  @reducer(action => {
    const { items = [] } = action.payload
    return items
  })
  @node('allStandardCurrency', [])
  @action
  getAllStandardCurrency() {
    return { payload: currencyAction.GET('/legalEntity/standards') }
  }

  @reducer(action => get(action, 'payload.value'))
  @node('currencyConfig')
  @action
  getCurrencyConfig() {
    return {
      payload: currencyAction.GET('/config')
    }
  }

  @reducer(action => action?.payload?.value)
  @node('currencyEditable')
  @action
  getCurrencyEditable() {
    return {
      payload: currencyAction.GET('/config/modifiable')
    }
  }

  /**
   * 系统租户本位币对应的外币汇率，即没有开通法人多币种时(本位币唯一)
   * @returns {{payload: Promise<any>}}
   */
  @reducer(action => action.payload.items)
  @node('allCurrencyRates', [])
  @action
  getEnterpriseAllCurrencyRate() {
    return { payload: currencyAction.GET('/active') }
  }

  /**
   * 多本位币时，根据本位币id获取对应的外币汇率
   * @returns {{payload: Promise<any>}}
   */
  @reducer(action => action.payload.items)
  @node('currencyRates', [])
  @action
  getCurrencyRatesById(id) {
    return { payload: currencyAction.GET('/active/$id', { id }) }
  }

  /**
   * 根据ID获取币种信息
   * @param params
   */
  getCurrencyInfoById(params) {
    return enumAction.GET('/$id', params)
  }

  @reducer(action => action.payload.items)
  @node('specification_expense', [])
  @action
  getExpenseSpecifications() {
    let type = { type: 'expense' }
    let data = {
      ...type,
      ...specificationJoin()
    }
    return {
      type,
      payload: specifications.GET('/actives', data)
    }
  }

  @reducer()
  @action
  getApportionByApi(data) {
    return {
      payload: apportion.POST('/getApportionByApi', data)
    }
  }

  @reducer(action => action.payload.items)
  @node('specification_loan', [])
  @action
  getLoanSpecifications() {
    let type = { type: 'loan' }
    let data = {
      ...type,
      ...specificationJoin()
    }
    return {
      type,
      payload: specifications.GET('/actives', data)
    }
  }

  @reducer(action => action.payload.items)
  @node('specification_requisition', [])
  @action
  getRequisitionSpecifications() {
    let type = { type: 'requisition' }
    let data = {
      ...type,
      ...specificationJoin()
    }
    return {
      type,
      payload: specifications.GET('/actives', data)
    }
  }

  @reducer(action => action.payload.items)
  @node('allCurrencyList', [])
  @action
  getAllCurrencyList() {
    return { payload: enumItems.GET('/$currency', {}) }
  }

  @reducer(action => action.payload.items)
  @node('specification_all', [])
  @action
  getAllRequisitionSpecifications() {
    // let type = { type: 'requisition' }
    let selectQuery = new QuerySelect().filterBy('type=="requisition"').value()
    return {
      payload: specificationsAll.POST('', selectQuery)
    }
  }

  @reducer(action => get(action, 'payload.value'))
  @node('me_info', {})
  @action
  getMeInfo() {
    let params = {
      join: 'staff.departments,departments,/v1/organization/departments',
      join$1: 'staff.corporationId,corporationId,/v1/organization/corporations',
      join$2: 'staff.id,autograph,/v1/basedata/autographs?join=imageId,/v1/attachment/attachments',
      join$3: 'staff.defaultDepartment,defaultDepartment,/v1/organization/departments',
      join$4: 'staff.id,roles,/v1/organization/staffs/role'
    }
    return {
      payload: staffs.GET('/me', { ...params })
    }
  }

  @reducer(action => action.payload)
  @node('carbonCopyCount', { count: 0 })
  @action
  getCarbonCopyCount() {
    let query = new QuerySelect().filterBy(`state.in("${'UNREAD'}")`).value()
    return { payload: carbonCopy.POST('/count', query) }
  }

  @reducer(action => get(action, 'payload'))
  @node('mallRole', {})
  @action
  getMallRole() {
    return { payload: messageCenterConfig.GET('/getMallRole') }
  }

  @reducer(action => {
    let items = action.payload.items || []
    let choiceValue = items.length > 0 ? items[0].choiceValue : []
    return { choiceValue }
  })
  @node('lastChoice', {})
  @action
  getLastChoice(userId) {
    return { payload: lastChoice.GET('/$id', { id: userId }) }
  }

  @reducer(action => {
    let items = action.payload.items || []
    let data = items.map(d => {
      let { departmentNode, isLeaf } = d
      return { ...departmentNode, isLeaf }
    })
    hideLoading()
    return { data }
  })
  @node('departmentsByParent', [])
  @action
  getDepartmentsByParent(parentId = '') {
    showLoading()
    return { payload: departments.GET('/byParentId/actives', { parentId }) }
  }

  @reducer()
  @node()
  @action
  getDepartmentFullNameByIds(ids) {
    return { payload: departments.GET('/withFullName/$[ids]', { ids }) }
  }

  /**
   * 根据父级id获取过滤后的可见部门
   * @returns {{payload : *}}
   */
  @reducer(action => {
    let items = action.payload.items || []
    let data = items.map(d => {
      let { departmentNode, isLeaf } = d
      return { ...departmentNode, isLeaf }
    })
    hideLoading()
    return { data }
  })
  @node('departmentsVisibilityByParent', [])
  @action
  getVisibilityDepartmentsByParent(parentId, staffId) {
    showLoading()
    return {
      payload: departments.GET('/visibility/byParentId/actives', { parentId, staffId, allowHoseVisibility: false })
    }
  }

  @reducer(action => {
    hideLoading()
    return { data: action.payload.items }
  })
  @node('searchDeptList', [])
  @action
  searchDepartments({ keyword, active }) {
    showLoading()
    let query = new QuerySelect()
      .filterBy(
        `(name.containsIgnoreCase(${JSON.stringify(keyword)})||code.containsIgnoreCase(${JSON.stringify(keyword)}))`
      )
      .filterBy(active)
      .limit(0, 99)
      .value()
    return { payload: departments.POST('/search', query) }
  }

  @reducer(action => action.payload.items)
  @node('CertificateTypeList', [])
  @action
  getCertificateTypeList() {
    return { payload: enumItems.GET('/$id', { id: 'CertificateType' }) }
  }

  @reducer(action => action.payload.items)
  @node('enumListByCode')
  @action
  getEnumItems(enumCode) {
    return { payload: enumItems.GET(`/$code`, { code: enumCode }) }
  }

  /**
   * 获取当前登录人可使用的支付列表
   *
   */
  @reducer(action => action.payload.items)
  @node('payAccountsVisibilityList', [])
  @action
  getPayAccountsVisibilityList() {
    return { payload: pay.GET('/accounts/visibility') }
  }

  @reducer
  @action
  isNeedPhone(params) {
    return { payload: extensionCenter.GET('/getPower/$type/needPhone', params) }
  }

  @reducer()
  @action
  getStaffSetting() {
    return { payload: staffSetting.GET('/$MOBILE', { MOBILE: 'MOBILE', locale: Fetch.defaultLanguage }) }
  }

  /**
   * 获取全局获取通讯录配置
   * @returns {{payload : *}}
   */
  @reducer(action => get(action, 'payload.value'))
  @node('organizationConfig', {})
  @action
  getOrganizationConfig() {
    return { payload: setOrganization.GET('/config') }
  }

  @reducer(action => action.payload.items)
  @node('invoiceRules', [])
  @action
  getInvoiceRules() {
    return { payload: invoiceAction.GET('/mapping') }
  }

  getInvoiceCity(param) {
    return invoiceAction.GET('/mapping/getCity/[cities]', param)
  }

  @reducer(action => get(action, 'payload.value'))
  @node('didiPersonalIsAuthorized', [])
  @action
  checkDiDiPersonalAuthorized() {
    return { payload: tppAction.GET('/didi/isAuthorized') }
  }

  @reducer(action => action.payload.value)
  @node('specificationVersionConfig', { hideVersion: false })
  @action
  getSpecificationVersionedConfig() {
    return { payload: specifications.GET('/config') }
  }

  @reducer(action => action.payload.id)
  @node('spaceId')
  @action
  getSpaceId() {
    return { payload: spaceAction.GET('/spaceid') }
  }

  @reducer(action => action.payload.id)
  @node('dingPanAuthorize')
  @action
  dingPanAuthorize() {
    return { payload: spaceAction.GET('/authorize/add') }
  }

  @reducer(action => action.payload.id)
  @node('dingPanAuthorizeDownload')
  @action
  dingPanAuthorizeDownload(param = {}) {
    return { payload: spaceAction.GET('/authorize/download/[fileIds]', param) }
  }

  getCommonAccount() {
    return commonAccountAction.GET('/$MOBILE', { MOBILE: 'MOBILE' })
  }

  saveCommonAccount(params) {
    return commonAccountAction.POST('', params)
  }

  @reducer(action => {
    const channelList = action.payload && Object.values(action.payload)
    return { dynamicChannelMap: action.payload, channelList }
  })
  @action //后端获取动态添加的支付渠道
  getDynamicChannel() {
    return { payload: dynamicChannel.GET() }
  }

  //文档预览水印
  @reducer(action => action.payload.value)
  @node('waterMark', null)
  @action
  getWaterMark() {
    return { payload: safeSetting.GET('/getText') }
  }

  @reducer(action => action.payload.value)
  @node('invoicePriceAndTaxEditPermission', null)
  @action
  getInvoicePriceAndTaxEditPermission() {
    return { payload: invoiceAction.GET('/priceAndTaxSeparatedPermission/staff') }
  }

  getDimentionValuesByIds(params) {
    return dimensionItems.GET('/[ids]', params)
  }

  /***
   * 获取委托审批已授权员工列表
   * @param page
   * @returns {{payload: *}}
   */

  @reducer(function(action) {
    api.dispatch(this.setDelegateStaffsLoading(false))
    return action.payload.items
  })
  @node('delegateStaffs', [])
  @action
  getDelegateStaffsAuthorized(delegateType) {
    showLoading(i18n.get('加载中……'))
    let items = []
    let pages = 1
    let pageSize = 2999
    let params = {
      start: 0,
      count: pageSize,
      join: 'defaultDepartment,defaultDepartment,/v1/organization/departments',
      delegateType: delegateType
    }
    api.dispatch(this.setDelegateStaffsLoading(true))
    let getStaffs = delegateType => {
      return delegateAccess.GET('/staffs/$delegateType', params).then(resp => {
        items = items.concat(resp.items)
        //返回值中的count(resp.count), 是后端返回的items数组的元素数量，不是全部的数量
        if (resp.count !== 0) {
          params.start = pages * pageSize
          params.delegateType = delegateType
          pages++
          return getStaffs(delegateType)
        }
        if (resp.count === 0) {
          hideLoading()
        }
        return { items }
      })
    }
    return { payload: getStaffs(delegateType) }
  }

  @reducer(function(action) {
    return action.isDelegateStaffsLoading
  })
  @node('isDelegateStaffsLoading', false)
  @action
  setDelegateStaffsLoading(isDelegateStaffsLoading) {
    return { isDelegateStaffsLoading }
  }

  /**
   * 支付账户所属国家
   * @param data
   * @returns {{payload: *}}
   */
  @reducer(action => action.payload)
  @node('searchCountries', [])
  @action
  getCountries(data) {
    return {
      payload: countries.GET('', { ...data })
    }
  }

  /**
   * 根据国家id获取支付款账号所属城市
   * @param data
   * @returns {{payload: *}}
   */
  @reducer(action => action.payload)
  @node('searchCities', [])
  @action
  getCities(data) {
    return {
      payload: cities.GET('/$id', { id: data })
    }
  }

  async getPreviewURL() {
    const defaultPreviewURL = 'https://doc.ekuaibao.com'
    try {
      // 预览地址
      const { value } = await Fetch.GET(`${location.origin}/api/previewUrl`)
      window.PREVIEW_DOMAIN = value || defaultPreviewURL
    } catch (error) {
      window.PREVIEW_DOMAIN = defaultPreviewURL
    }
  }

  // 钉钉场景群已选人员
  @reducer(action => action.payload)
  @node('selectedStaffs', [])
  @action
  setSelectedStaffs(data) {
    return { payload: data }
  }

  formatTreeToMap(data) {
    return getWorkerResult('departmentMap', data)
  }

  @reducer(action => action.payload.value)
  @node('standardLimtConfig')
  @action
  getStandardLimtConfig() {
    return { payload: tradeeditionAction.GET('/limitConfig') }
  }

  @reducer(action => action.payload.value)
  @node('isWhiteList')
  @action
  getIsWhiteList() {
    return { payload: isWhiteList.GET('') }
  }

  @reducer(action => action.payload.value)
  @node('uploadWhiteList')
  @action
  getUploadFillWhiteList(params) {
    return { payload: fillAction.GET('/isInWhtieList', params) }
  }

  @reducer(action => action.payload.value)
  @node('payerConfig', {})
  @action
  payerConfig() {
    return { payload: payerActionV2.GET('') }
  }

  @reducer
  @node
  @action
  getBranchedSearchData(data) {
    return { payload: branchesSearch.POST('', data) }
  }

  @reducer(action => action?.payload?.value)
  @node('authEnv')
  @action
  authEnvironment() {
    return { payload: authEnv.GET() }
  }

  @reducer(action => action?.payload)
  @node('hoseAccessToken')
  @action
  getHoseAccessToken() {
    return { payload: getHoseAccessToken() }
  }

  /**
   * @desc 特定单个开关
   * @param {*} params
   * @returns
   */
  @reducer()
  @node()
  @action
  getToggleSwitchByName(params) {
    return { payload: toggle.GET('/byCorpId', params) }
  }

  /**
   * @desc 全部开关
   * @returns
   */
  @reducer(action => action?.payload?.value)
  @node('toggleManage', false)
  @action
  getToggleAll() {
    return { payload: toggle.GET('/all') }
  }

  checkECardState(params) {
    return eCardAction.GET('/state', params)
  }

  getStaffCardInfo(params) {
    //获取当前用户所有的易商卡卡片信息
    return virtualCard.POST('/staffCardInfo', params)
  }

  @reducer(action => action.payload)
  @node('staffsVisibilityMap', {})
  @action
  parseStaffsVisibilityMap(staffs) {
    const parseStaffsMap = (staffs = []) => {
      const staffMap = {}
      if (!staffs?.length) {
        return staffMap
      }
      staffs.forEach(staff => {
        staffMap[staff.id] = staff
      })
      return staffMap
    }
    return { payload: parseStaffsMap(staffs) }
  }

  @reducer(action => action.payload?.value)
  @node('visibilityStaffOptions', { fullVisible: true, staffIds: [] })
  @action
  getVisibilityStaffIdsByCorpId() {
    return {
      payload: setOrganization.GET('/staffs/getVisibilityStaffIds')
    }
  }
}

/**
 * 获取统一登陆的token
 * @returns {Promise<string>}
 */
export async function getHoseAccessToken() {
  try {
    const config = await api.dataLoader('@common.authEnv').load()
    const result = await GET(
      '/auth/oauth2/grant/ekb/accessToken',
      {
        corpId: Fetch.ekbCorpId,
        ekbAccessToken: Fetch.accessToken,
        environment: config.environment
      },
      {
        baseURL: config.bcGateWayUrl,
        withCredentials: true,
        timeout: 100000
      }
    )
    return result?.data?.data ?? ''
  } catch (_e) {
    return ''
  }
}

// 风险排序
function riskWaringSort(data) {
  const sortedData = data?.riskWarning?.sort((a, b) => isErrorItem(a) - isErrorItem(b)) || []
  return {
    ...data,
    riskWarning: sortedData
  }
}

export async function getTranslate(param) {
  let result = param
  if (i18n.currentLocale === 'en-US') {
    try {
      const resp = await Fetch.POST(`/api/common/v1/translate`, {}, { body: { sourceText: JSON.stringify(param) } })
      resp?.forEach(el => {
        if (el?.index) {
          result[el.index] = el.translated?.replace('.', '')
        }
      })
    } catch (error) {}
  }
  return result
}

const commonActions = new CommonActions()

export default commonActions
