/**
 *  Created by pw on 2019-09-19 20:54.
 */

import { visit } from '@ekuaibao/helpers'

export function getDepartmentFullPath(items: DepartmentIF[] = []) {
  if (!items.length) {
    return { data: items, mapData: {}, pathMap: {},  enPathMap: {}, noRootEnPathMap: {} }
  }
  const { mapData, pathMap, noRootPathMap, enPathMap, noRootEnPathMap } = mapDataFn(items)
  return { data: items, mapData, pathMap, noRootPathMap, enPathMap, noRootEnPathMap }
}

export function departmentMap(items: DepartmentIF[]) {
  const mapData: any = {}
  visit(items, node => {
    mapData[node.id] = node
  })
  return mapData
}

function mapDataFn(items: DepartmentIF[] = []) {
  const mapData: any = {}
  const pathMap: any = {}
  const noRootPathMap: any = {}
  const noRootEnPathMap: any = {}
  const enPathMap: any = {}
  visit(items, node => {
    mapData[node.id] = node
    const enName = getEnName(node)
    if (node.parentId && node.parentId.length) {
      const parentPath = pathMap[node.parentId] || ''
      const parentEnPath = enPathMap[node.parentId] || ''
      const noRootParentPath = noRootPathMap[node.parentId] || ''
      const noRootParentEnPath = noRootEnPathMap[node.parentId] || ''
      pathMap[node.id] = parentPath.length ? `${parentPath}/${node.name}` : node.name
      enPathMap[node.id] = parentEnPath.length ? `${parentEnPath}/${enName}` : enName
      noRootPathMap[node.id] = noRootParentPath.length ? `${noRootParentPath}/${node.name}` : node.name
      noRootEnPathMap[node.id] = noRootParentEnPath.length ? `${noRootParentEnPath}/${enName}`: enName
    }else {
      pathMap[node.id] = node.name
      enPathMap[node.id] = enName
    }
  })
  return { mapData, pathMap, noRootPathMap, enPathMap, noRootEnPathMap }
}

const getEnName = (item) => {
  return item.enName || item.name
}

export interface DepartmentIF {
  version: number
  active: boolean
  createTime: number
  updateTime: number
  name: string
  nameSpell: string
  code: string
  corporationId: string
  parentId: string
  order: number
  id: string
  children: DepartmentIF[]
}
