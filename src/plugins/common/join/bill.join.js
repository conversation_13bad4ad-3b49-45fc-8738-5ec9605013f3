import { app as api } from '@ekuaibao/whispered'
import { parsePropertySet2Select } from 'ekbc-query-builder'
import { joinPropertiesBuilder } from '../../../lib/util'

const joinSigner = () => {
  return 'nodes.counterSigners.signerId,signerId,/v1/organization/staffs?select=id,name,enName,nickName,avatar,code,email,cellphone,note'
}
const joinStaffs = () => `nodes.approverId,staff,/v1/organization/staffs&join=${joinSigner()}`
const joinPrintTemplate = () => 'configs.printTemplateId,printTemplateId,/v1/print/template'
const joinOriginal = () => 'originalId,originalId,/form/v2/specifications' //获取最新打印模版
const joinOwner = (prefix = '') => prefix + 'ownerId,owner,/v1/organization/staffs?select=name,id,nickName'
const selectOwner = () => `ownerId(defaultDepartment(...),...)`
const joinFlowPlan = (prefix = '') => prefix + `id,plan,/v1/flow/plans?join=${joinStaffs()}`
const selectFlowPlan = () => `plan(...)`

const joinFlowPlanNodes = prefix => [
  prefix + `plan.nodes.approverId,approverId,/v1/organization/staffs`,
  prefix +
  `plan.nodes.counterSigners.signerId,signerId,/v1/organization/staffs?select=id,name,enName,nickName,avatar,code,email,cellphone,note`
]

const joinWrittenOff = (prefix = '') => {
  return (
    prefix +
    'id,writtenOff,/v1/loan/writtenOff?join=records.loanInfoId,loanInfoId,/v1/loan/loanInfo?select=remain,id,flowId,code,source,foreignCurrencyLoan,foreignRemain,totalMoneyNode'
  )
}
const joinRequisition = (prefix = '') => prefix + 'form.expenseLink,expenseLink,/form/v2/requisition'

const selectRequisition = () => {
  return `expenseLink(specificationId(...),...)`
}

const selectRequisitions = () => {
  return `expenseLinks(specificationId(...),...)`
}

const selectFlowLinks = () => {
  return `flowLinks(id,form(...),...)`
}

const selectLinkRequisitionInfo = () => {
  return `linkRequisitionInfo(specificationId(...),...)`
}

const joinFormMoney = (prefix = '') => [
  prefix + moneyJoin('pay'),
  prefix + moneyJoin('expense'),
  prefix + moneyJoin('loan'),
  prefix + moneyJoin('requisition')
]

const joinDataLink = (prefix = '') => [
  prefix +
    'components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform?join=icon.fileId,fileId,/v1/attachment/attachments',
  prefix + 'components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping',
  prefix + 'components.appletAssignmentRule,appletAssignmentRule,/v1/mapping/fieldMapping',
]

const joinLinkDetails = (prefix = '') => [
  prefix +
  'form.details.feeTypeForm.linkDetailEntities.linkDetailEntityId,linkDetailEntityId,/form/v3/requisition/info/details/byId?join=feeTypeId,feeTypeId,/v1/form/feeTypes'
]

const joinInvoiceForm = (prefix = '') => [
  // prefix + 'form.details.feeTypeForm.invoiceForm.invoices.invoiceId,invoiceId,/v2/invoice',
  // prefix + 'form.details.feeTypeForm.invoiceForm.invoices.itemIds,itemIds,/v2/invoice/item',
  prefix + 'form.details.feeTypeForm.invoiceForm.attachments.fileId,fileId,/v1/attachment/attachments',
  prefix +
  'form.details.feeTypeForm.invoiceForm.invoiceCorporationId,invoiceCorporationId,/v2/invoice/unify/corporation/list'
]

const joinAmortizesForm = (prefix = '') => [
  prefix + 'form.details.feeTypeForm.amortizes.specificationId,specificationId,/form/v1/specificationVersions'
]

const joinManualRepayment = (prefix = '') => prefix + 'id,loanManualRepayment,/v1/loanManual/repayment'

const moneyJoin = (type = '') => `form.${type}Money.standardNumCode,standardNumCode,/v1/basedata/enumItems`
const joinRule = (prefix = '') => prefix + 'id,flowRulePerformLogs,/v1/rule/flowRulePerformLogs'
const selectRule = () => `flowRulePerformLogs(...)`
const selectriskWarning = () => `riskWarning(...)`
const selectCalcRiskWarning = () => `calcRiskWarning(...)`

const joinForm = path => {
  return joinPropertiesBuilder(path, {
    'form.Specification.Versioned': `join=${joinOriginal()}?join=${joinPrintTemplate()}`,
    'pay.PayeeInfo': `join=staffId,staffId,/v1/organization/staffs`
  })
}

const selectForm = (isFeeTypeForm = false) => {
  let properties = api.getState()['@common'].baseDataProperties.data
  let extensionFields = api.getState()['@common'].baseDataProperties.extensionFields
  let list = isFeeTypeForm ? extensionFields : properties
  return parsePropertySet2Select(list)
}

//暂时join 项目和部门（4.2.0费用分摊功能）
const joinApportions = prefix => {
  return [
    prefix +
    'form.details.feeTypeForm.apportions.apportionForm.expenseDepartment,expenseDepartment,/v1/organization/departments',
    prefix + i18n.get('form.details.feeTypeForm.apportions.apportionForm./(项目)/,$1,/v1/basedata/dimensionItems')
  ]
}

const selectApportions = () => `apportions(specificationId(...),apportionForm(${selectForm(true)},...),...)`

const joinDetails = (prefix = '') => [
  prefix +
  'form.details.feeTypeId,feeTypeId,/v1/form/feeTypes?join=specificationId,specificationId,/form/v1/specificationVersions',
  prefix + 'form.details.feeTypeForm.orders,orders,/v1/order/orders',
  prefix + 'form.details.specificationId,specificationId,/form/v1/specificationVersions',
  prefix + 'form.details.feeTypeForm.apportions.specificationId,specificationId,/form/v1/specificationVersions',
  prefix + 'form.details.feeTypeForm.amortizes.specificationId,specificationId,/form/v1/specificationVersions',
  ...joinForm(prefix + 'form.details.feeTypeForm'),
  ...joinApportions(prefix)
]

const joinFeeTypeId = (prefix = '') => {
  return (
    prefix +
    'form.details.feeTypeId,feeTypeId,/v1/form/feeTypes?join=specificationId,specificationId,/form/v1/specificationVersions'
  )
}
const joinOrders = (prefix = '') => prefix + 'form.details.feeTypeForm.orders,ordersData,/v1/order/orders'

const joinThirdPartyOrders = (prefix = '') =>
  prefix + 'form.details.feeTypeForm.orders,thirdPartyOrders,/v2/order/orders'

const selectFeeDetailPayeeId = () => `feeDetailPayeeId(staffId(...),...)`

const selectFeeTypeForm = () => {
  return `feeTypeForm(${selectFeeDetailPayeeId()},${selectApportions()},${selectFlowLinks()},${selectForm(true)},...)`
}

export const selectDetails = () => {
  return `details(specificationId(...),${selectFeeTypeForm()},...)`
}

const joinLogs = prefix => [
  prefix + 'logs.operatorId,operatorId,/v1/organization/staffs',
  prefix + 'logs.nextOperatorId,nextOperatorId,/v1/organization/staffs',
  prefix + 'logs.nextOperatorIds,nextOperatorIds,/v1/organization/staffs',
  prefix + 'logs.attributes.oldApproverId,oldApproverId,/v1/organization/staffs',
  prefix + 'logs.attributes.newApproverId,newApproverId,/v1/organization/staffs',
  // prefix + 'logs.attributes.delOperatorId,delOperatorId,/v1/organization/staffs',
  prefix + 'logs.attributes.paymentAccountId,paymentAccountId,/pay/v1/accounts',
  prefix + 'logs.attributes.autographImageId,autographImageId,/v1/attachment/attachments',
  prefix + 'logs.attributes.carbonCopyIds,carbonCopy,/v1/organization/staffs',
  prefix + 'logs.byDelegateId,byDelegateId,/v1/organization/staffs',
  prefix + 'logs.modifyFlowLog.byDelegateId,byDelegateId,/v1/organization/staffs'
]

const joinLogs1 = prefix => [
  prefix + 'logs.attributes.oldApproverId,oldApproverId,/v1/organization/staffs',
  prefix + 'logs.attributes.newApproverId,newApproverId,/v1/organization/staffs',
  prefix + 'logs.attributes.paymentAccountId,paymentAccountId,/pay/v1/accounts',
  prefix + 'logs.attributes.participants,participants,/v1/organization/staffs',
  prefix + 'logs.attributes.receiverIds,receiverIds,/v1/organization/staffs',
  // prefix + 'logs.attributes.delOperatorId,delOperatorId,/v1/organization/staffs',
  prefix + 'logs.attributes.autographImageId,autographImageId,/v1/basedata/autographs/imageIds',
  prefix + 'logs.modifyFlowLog.operatorId,operatorId,/v1/organization/staffs',
  prefix + 'logs.attributes.carbonCopyIds,carbonCopy,/v1/organization/staffs',
  prefix + 'logs.attributes.resubmitOperatorIds,resubmitOperatorIds,/v1/organization/staffs',
  prefix + 'logs.byDelegateId,byDelegateId,/v1/organization/staffs',
  prefix + 'logs.modifyFlowLog.byDelegateId,byDelegateId,/v1/organization/staffs'
]

const joinExpenseLogs = prefix => [
  `${prefix}form.expenseLink.changeLogs.operatorId,operatorId,/v1/organization/staffs`,
  `${prefix}form.expenseLink.changeLogs.fromStaff,fromStaff,/v1/organization/staffs`,
  `${prefix}form.expenseLink.changeLogs.toStaffs,toStaffs,/v1/organization/staffs`,
  `${prefix}form.expenseLink.ownerId,ownerId,/v1/organization/staffs`,
  `${prefix}form.expenseLinks.changeLogs.operatorId,operatorId,/v1/organization/staffs`,
  `${prefix}form.expenseLinks.changeLogs.fromStaff,fromStaff,/v1/organization/staffs`,
  `${prefix}form.expenseLinks.changeLogs.toStaffs,toStaffs,/v1/organization/staffs`,
  `${prefix}form.expenseLinks.ownerId,ownerId,/v1/organization/staffs`
]

const joinLinkRequisitionInfoChangeLogs = prefix => [
  `${prefix}form.linkRequisitionInfo.changeLogs.operatorId,operatorId,/v1/organization/staffs`,
  `${prefix}form.linkRequisitionInfo.changeLogs.fromStaff,fromStaff,/v1/organization/staffs`,
  `${prefix}form.linkRequisitionInfo.changeLogs.toStaffs,toStaffs,/v1/organization/staffs`,
  `${prefix}form.linkRequisitionInfo.ownerId,ownerId,/v1/organization/staffs`
]

const joinPermitRestTime = prefix => [prefix + `id,restTime,/permit/v2/flowDuration`]

const selectLogs = () => {
  return `logs(operatorId(...),nextOperatorId(...),nextOperatorIds(...),...)`
}

const basic = prefix => [...joinForm(prefix + 'form'), joinFlowPlan(prefix), ...joinLogs(prefix), joinOwner(prefix)]

export const paramsToMap = params => {
  let paramsMap = {}
  params.forEach((url, idx) => {
    paramsMap[`join$${idx}`] = url
  })
  return paramsMap
}

export const joinFLow = (type, prefix = '') => {
  let params = []
  if (type === 'expense' || type === 'requisition') {
    params = [
      ...basic(prefix),
      ...joinDetails(prefix),
      joinWrittenOff(prefix),
      joinRequisition(prefix),
      joinRule(prefix)
    ]
  }
  if (type === 'loan' || type === 'form') {
    params = [...basic(prefix)]
  }
  return { type, params }
}

const selectTripForm = () => {
  return `tripForm(${selectForm(true)}
                   ,...)`
}

const selectTrips = () => {
  return `trips(specificationId(...),
                  tripTypeId(specificationId(...),...),
                  ${selectTripForm()},
                  ...)`
}

const selectSubmitterDefaultDept = () => {
  return `submitterId(defaultDepartment(...),...)`
}

const selectNoFlowEntity = () => {
  return `form(${selectForm()},
                ${selectDetails()},
                ${selectTrips()},
                ${selectSubmitterDefaultDept()},
                ${selectRequisition()},
                ${selectRequisitions()},
                ${selectFlowLinks()},
                ${selectLinkRequisitionInfo()}
                ,...),
            ${selectLogs()},
            ${selectOwner()},
            ${selectFlowPlan()},
            ${selectRule()},
            ${selectriskWarning()}
            ,...`
}

const selectNoFlowEntityV2 = () => {
  return `form(${selectForm()},
                ${selectDetails()},
                ${selectTrips()},
                ${selectSubmitterDefaultDept()},
                ${selectRequisition()},
                ${selectRequisitions()},
                ${selectFlowLinks()}, 
                ${selectLinkRequisitionInfo()}
                ,...),
            ${selectLogs()},
            ${selectOwner()},
            ${selectRule()},
            ${selectriskWarning()} 
            ,...`
}

const selectNoFlowEntityLite = () => {
  return `form(${selectForm()},...),...`
}

const selectNoFlowEntityCalcRisk = () => {
  return `form(${selectForm()},
              ${selectDetails()},
              ${selectTrips()},
              ${selectSubmitterDefaultDept()},
              ${selectRequisition()},
              ${selectRequisitions()},
              ${selectFlowLinks()},
              ${selectLinkRequisitionInfo()}
              ,...),
          ${selectLogs()},
          ${selectOwner()},
          ${selectFlowPlan()},
          ${selectRule()},
          ${selectCalcRiskWarning()}
          ,...`
}

export const selectEntity = (prefix = '', isCalcRisk = false) => {
  if (prefix) {
    return `${prefix}(${isCalcRisk ? selectNoFlowEntityCalcRisk() : selectNoFlowEntity()}),...`
  }
  return isCalcRisk ? selectNoFlowEntityCalcRisk() : selectNoFlowEntity()
}

export const selectEntityV2 = (prefix = '') => {
  if(prefix) {
    return `${prefix}(${selectNoFlowEntityV2()}),...`
  }

  return selectNoFlowEntityV2()
}

export const selectEntityLite = (prefix = '') => {
  if (prefix) {
    return `${prefix}(${selectNoFlowEntityLite()})
            ,...`
  }
  return selectNoFlowEntityLite()
}

//不支持select使用join
export const joinParam = (prefix = '') => {
  let params = [
    ...joinFlowPlanNodes(prefix),
    ...joinLogs1(prefix),
    ...joinExpenseLogs(prefix),
    ...joinLinkRequisitionInfoChangeLogs(prefix),
    joinWrittenOff(prefix),
    joinFeeTypeId(prefix),
    joinOrders(prefix),
    joinThirdPartyOrders(prefix),
    joinPermitRestTime(prefix),
    joinFormMoney(prefix),
    joinDataLink(prefix + 'form.specificationId.'),
    joinDataLink(prefix + 'form.details.specificationId.'),
    joinDataLink(prefix + 'form.trips.tripTypeId.specificationId.'),
    joinInvoiceForm(prefix),
    joinLinkDetails(prefix),
    joinAmortizesForm(prefix),
    joinManualRepayment(prefix)
  ]
  return { ...paramsToMap(params) }
}

export const joinParamsLite = (prefix = '') => {
  let params = [
    joinDataLink(prefix + 'form.specificationId.'),
    joinDataLink(prefix + 'form.trips.specificationId.'),
    joinDataLink(prefix + 'form.trips.tripTypeId.specificationId.')
  ]
  return { ...paramsToMap(params) }
}

export const joinMoney = (prefix = '') => {
  return { ...paramsToMap(joinFormMoney(prefix)) }
}

export const joinPlan = () => {
  const join = [
    `nodes.approverId,approverId,/v1/organization/staffs`,
    `nodes.counterSigners.signerId,signerId,/v1/organization/staffs?select=id,name,enName,avatar,code,email,cellphone,note`
  ]
  return { ...paramsToMap(join) }
}
