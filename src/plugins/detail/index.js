/************************************************
 * Created By nanyuantingfeng On 6/6/16 11:21.
 ************************************************/
import loadable from '@loadable/component'
import { getVersionsFlowplan } from './detail.action'

export default [
  {
    id: '@detail',
    reducer: () => require('./detail.reducer').default,
    path: '/flow-report-detail/:flowId/:budgetId/:fromNodeId/:nodeId/:periodTime',
    ref: '/',
    onload: () => import('./flow-report-detail'),
    getVersionFlowPlanById: async params => {
      const result = await getVersionsFlowplan(params)
      return result.value
    }
  },

  {
    point: '@@layers',
    prefix: '@detail',
    onload: () => require('./layers').default
  },
  {
    resource: '@detail',
    value: {
      ['flow-node-view']: loadable(() => import('./flow-node-view')),
      ['history']: loadable(() => import('./history'))
    }
  },
  {
    resource: '@detail',
    value: {
      ['history-fetchUtil']: require('./history-fetchUtil')
    }
  }
]
