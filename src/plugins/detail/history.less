/**************************************************
 * Created by nanyuantingfeng on 8/23/16 11:55.
 **************************************************/
@import '../../styles/layout.less';

.detail-history-wrapper {
  .app-layout-content();
  transform: translate3d(0, 0, 0);
  background-color: @gray-1;
  padding: 32px 32px 0 32px;
  width: 100%;
  .history-title {
    flex-shrink: 0;
    align-items: center;
    display: flex;
    height: 100px;
    color: @gray-9;
    img {
      margin-left: 16px;
      width: 40px;
      height: 40px;
    }
  }

  .history-wrapper {
    width: 100%;
    flex: 1;
    transform: translate3d(0, 0, 0); // trick again

    .line-wrapper {
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      &:last-child {
        .time-line {
          display: none;
        }
      }

      .line-content-wrapper {
        width: 100%;
        padding-right: 32px;
        margin-left: 32px;
        margin-bottom: 64px;
        display: flex;
        flex: 1;
        flex-direction: column;
        color: @gray-7;
        .time {
          font-size: 28px;
          text-align: left;
          color: @gray-9;
        }
        .line-content {
          font-size: 28px;
          padding-top: 12px;
          align-items: flex-start;
        }
      }
      .mark-circle {
        align-self: flex-start;
        margin-top: 8px;
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        border-radius: 10px;
        border: solid 2px @gray-5;
      }
      .time-line {
        position: absolute;
        top: 36px;
        bottom: -2px;
        left: 9px;
        width: 2px;
        background: @gray-5;
      }
    }
  }
}
