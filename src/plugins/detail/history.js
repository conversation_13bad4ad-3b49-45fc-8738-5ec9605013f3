import { app } from '@ekuaibao/whispered'
import './history.less'
import { app as api } from '@ekuaibao/whispered'
import moment from 'moment'
import { isAIAgentNode, getAIAgentObj } from '../../elements/ai-agent-utils'

import React from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import PageContainer from '../page-container'
const PopoverMenus = app.require('@elements/PopoverMenus')

const { flowStateMap, fnMapLogs } = app.require('@elements/approve-log/history-log-item')
import { getFeeTypeByIds, getPayeeInfoByIds } from './history-fetchUtil'

import SVG_DOWN from './images/bill-down.svg'
import get from 'lodash/get'
import * as actions from '../approve/approve.action'
import { toast } from '../../lib/util'
import _debounce from 'lodash/debounce';
import { HistoryApi } from '../../plugins/bill/history/api';

const ApprovalHistoryFilterList = [
  { label: '全部历史', value: 'ALL', type: 'all' },
  { label: '包含评论的历史', value: 'CONTAIN_COMMENT', type: 'comment' },
  { label: '审批事件的历史', value: 'APPROVAL_EVENT', type: 'approve' },
  { label: '人员审批的历史', value: 'STAFF_APPROVAL', type: 'staff_approval' }
]
@EnhanceConnect(state => ({
  userInfo: state['@common'].me_info,
  dynamicChannelMap: state['@common'].dynamicChannelMap,
  staffDisplayConfigField: state['@common'].organizationConfig?.staffDisplayConfig?.[1] || '',
  KA_DETAIL_LAYOUT_CONFIG: state['@common'].powers.KA_DETAILS_LAYOUT, // KA-单据详情展示
  authStaffStaffMap: state['@common'].authStaffStaffMap,
  nodesAIAgentMap: state['@common'].nodesAIAgentMap
}))
export default class History extends PageContainer {
  menus = [
    { label: i18n.get('包含评论的历史'), type: 'comment', value: 'CONTAIN_COMMENT' },
    { label: i18n.get('审批事件的历史'), type: 'approve', value: 'APPROVAL_EVENT' },
    { label: i18n.get('人员审批的历史'), type: 'staff_approval', value: 'STAFF_APPROVAL' },
    { label: i18n.get('有效审批的历史'), type: 'effective', value: 'EFFECTIVE_APPROVAL' },
    { label: i18n.get('全部历史'), type: 'all', value: 'ALL' }
  ]
  constructor(props) {
    super(props)
    const { logType } = props
    const flow = this.props.value
    this.state = {
      defaultSelectedMenu: null,
      menus: [],
      flow: flow,
      flowlogType: logType,
      selectedMenu: {},
      formatLogs: []
    }
  }

  /**
   * 通过 layout config 控制默认展示的 flow doc type
   * @returns {Promise<void>}
   */
  init = async () => {
    const { value, logType, KA_DETAIL_LAYOUT_CONFIG } = this.props
    let _menus = this.menus
    let defaultSelectedMenu = this.menus.find(v => v.value === 'ALL')
    if (KA_DETAIL_LAYOUT_CONFIG) {
      const specificationId = value?.form?.specificationId?.id
      if (!specificationId) return
      const layoutConfig = await api.dispatch(actions.getLayoutConfig({ type: 'ARRANGE', specificationId }))
      const approvalHistoryList = layoutConfig?.value?.configDetail?.approvalHistoryList
      if (approvalHistoryList && approvalHistoryList.length) {
        _menus = this.menus.filter(menuItem => !!~approvalHistoryList.indexOf(menuItem.value))
      }
      const approvalHistory = layoutConfig?.value?.configDetail?.approvalHistory
      defaultSelectedMenu = this.menus.find(v => v.value === approvalHistory)
    }
    const { selectedMenu, formatLogs } = this._refreshData(logType || defaultSelectedMenu?.type || 'all', value, _menus)
    this.setState(
      {
        menus: _menus,
        defaultSelectedMenu: defaultSelectedMenu,
        selectedMenu,
        formatLogs
      },
      () => this.fnFillPaymentLogs()
    )
  }

  componentDidMount() {
    this.init()
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.value !== nextProps.value) {
      const { logType } = nextProps
      const flow = nextProps.value
      const { selectedMenu, formatLogs } = this._refreshData(logType, flow)
      this.setState(
        {
          flow: flow,
          flowlogType: logType,
          selectedMenu,
          formatLogs
        },
        () => this.fnFillPaymentLogs()
      )
    }
  }

  componentWillMount() {
    super.componentWillMount()
    const { bus } = this.props
    bus && bus.on('history:type:change', this.historyTypeChange)
  }

  componentWillUnmount() {
    super.componentWillUnmount()
    const { bus } = this.props
    bus && bus.un('history:type:change')
  }

  historyTypeChange = type => {
    const { flow } = this.state
    this.setState(this._refreshData(type, flow))
  }

  fnFillPaymentLogs = () => {
    let { formatLogs } = this.state
    let paymentLogs = []
    formatLogs.forEach(log => {
      if (get(log, 'attributes.paymentInfos')) {
        paymentLogs = paymentLogs.concat(log.attributes.paymentInfos)
      }
    })
    if (paymentLogs.length) {
      //汇总id
      let payees = {}
      let feeTypes = {}
      paymentLogs.forEach(item => {
        if (item.payeeId && !payees[item.payeeId]) {
          payees[item.payeeId] = item.payeeId
        }
        if (item.feeTypeId && !feeTypes[item.feeTypeId]) {
          feeTypes[item.feeTypeId] = item.feeTypeId
        }
      })
      //发送请求
      const payeeIds = Object.keys(payees)
      const feeTypeIds = Object.keys(feeTypes)
      if (payeeIds.length > 0 && feeTypeIds.length > 0) {
        return Promise.all([getPayeeInfoByIds(payeeIds), getFeeTypeByIds(feeTypeIds)]).then(data => {
          formatLogs = formatLogs.map(el => {
            if (get(el, 'attributes.paymentInfos')) {
              el.attributes.paymentInfos = el.attributes.paymentInfos.map(payment => {
                payment.payeeId =
                  typeof payment.payeeId === 'string'
                    ? data[0].items.find(payee => payee.id === payment.payeeId)
                    : payment.payeeId
                payment.feeTypeId =
                  typeof payment.feeTypeId === 'string'
                    ? data[1].items.find(feeType => feeType.id === payment.feeTypeId)
                    : payment.feeTypeId
                return payment
              })
            }
            return el
          })
          this.setState({ formatLogs })
        })
      } else {
        return getPayeeInfoByIds(payeeIds).then(data => {
          formatLogs = formatLogs.map(el => {
            if (get(el, 'attributes.paymentInfos')) {
              el.attributes.paymentInfos = el.attributes.paymentInfos.map(payment => {
                payment.payeeId =
                  typeof payment.payeeId === 'string'
                    ? data.items.find(payee => payee.id === payment.payeeId)
                    : payment.payeeId
                return payment
              })
            }
            return el
          })
          this.setState({ formatLogs })
        })
      }
    }
  }

  _refreshData = (logtype, flow = {}, _menus) => {
    if (!_menus) {
      _menus = this.state?.menus || this.menus
    }
    let selectedMenu = _menus.find(item => item.type === logtype)
    const { logs = [] } = flow
    const format = fnMapLogs(logs) || []

    let formatLogs
    if (!selectedMenu) {
      if (logtype) {
        let targetMenu = this.menus.find(item => item.type === logtype)
        toast.info(i18n.get(`无查看${targetMenu.label}权限`))
      }
      if (this.state?.defaultSelectedMenu) {
        selectedMenu = this.state.defaultSelectedMenu
      } else {
        selectedMenu = this.menus.find(item => item.type === 'all')
      }
    }
    formatLogs = this._filterLog(selectedMenu.type, format)
    return { selectedMenu, formatLogs }
  }

  _filterLog(type, logs) {
    if (!logs) return []
    if (type === 'comment') {
      return logs.filter(item => {
        return item.action !== 'freeflow.carbonCopy' && item.attributes && (item.attributes.comment?.length > 0 || item.attributes.delOperatorId)
      })
    }

    if (type === 'approve') {
      return logs.filter(
        item => !~['freeflow.comment', 'freeflow.select.approver', 'freeflow.modify'].indexOf(item.action)
      )
    }

    if (type === 'staff_approval') {
      return logs.filter(item => {
        const isStaffApprovalAction = [
          'freeflow.submit',
          'freeflow.reject',
          'freeflow.agree',
          'freeflow.addnode',
          'freeflow.pay', //支付完成
          'freeflow.pay.by.offline', //转线下支付
          'freeflow.paying', //支付中
          'freeflow.pay.partial.paying', //部分支付中
          'freeflow.pay.partial.success', //部分支付成功
          'freeflow.pay.partial.failure', //部分支付失败
          'freeflow.repaying', //重新支付中
          'freeflow.failure' //支付失败
        ].includes(item.action)
        if (!isStaffApprovalAction) return false
        if (get(item, 'attributes.isEbotNode')) return false
        if (get(item, 'attributes.isRecalNode')) return false
        if (get(item, 'attributes.isInvoiceApplicationNode')) return false
        if (item.action === 'freeflow.reject') {
          return item.attributes && !item.attributes.isAuto
        }
        return true
      })
    }

    if (type === 'effective') {
      return logs.filter(item => {
        const skippedType = [
          'APPROVER_NOT_FOUND',
          'APPROVER_NOT_FOUND_BY_ROLE',
          'NO_ABILITY',
          'PAY_AMOUNT_IS_0'
        ].includes(item?.attributes?.skippedType)
        return !(item?.action === 'freeflow.skipped' && skippedType)
      })
    }
    return logs
  }

  handleLogTypeChange = node => {
    const { type } = node
    const { flow } = this.state
    this.setState(this._refreshData(type, flow))
  }

  handleHistoryVersionClick = _debounce(async (item)=>{
    const isHistoryVersion = await HistoryApi.getFlowVersionModifyState(item.flowVersionedId)
    if(isHistoryVersion?.value){
      const { formType, id } = this.state.flow
      const { isNeedBudget } = this.props
      if (formType === 'permit') {
        return api.go('/version/detail/' + id + '/' + item.flowVersionedId)
      }
      if (isNeedBudget === false) {
        return api.open('@bill:BillDetailModal', {
          isNeedBudget: false,
          fromHistory: true,
          params: { id, flowVersionedId: item.flowVersionedId },
          showFlowPlan: false
        })
      }
      return api.go('/version/detail/' + id + '/' + item.flowVersionedId)
    }else{
      api.open('@bill:BillHistoryModal',{
        flowVersionedId:item.flowVersionedId
      })
    }
  },500) 
  
  _renderLine = (line, index) => {
    const { userInfo, dynamicChannelMap = {}, staffDisplayConfigField, authStaffStaffMap } = this.props
    const { flow } = this.state
    const { time, action } = line
    const isEbotNode = get(line, 'attributes.isEbotNode')
    const isRecalNode = get(line, 'attributes.isRecalNode')
    const isInvoiceApplicationNode = get(line, 'attributes.isInvoiceApplicationNode')
    if (isEbotNode) {
      line.operatorId = { name: 'EBot', avatar: 'EBotIconNode' }
    }
    if (isInvoiceApplicationNode) {
      line.operatorId = { name: i18n.get('开票申请'), avatar: 'EBotIconNode' }
    }
    if (isRecalNode) {
      line.operatorId = { name: i18n.get('重算节点'), avatar: 'RecalculateIconNode' }
    }
    if(isAIAgentNode(line)){
      const {agent} = getAIAgentObj(line, this.props.nodesAIAgentMap)
      line.operatorId = { name: agent?.name, avatar: agent?.icon }
    }
    if (dynamicChannelMap) line.dynamicChannelMap = dynamicChannelMap

    // 通讯录员工显示配置
    flowStateMap.staffDisplayConfigField = staffDisplayConfigField

    return (
      <div key={index} className="line-wrapper">
        <div className="time-line" />
        <div className="mark-circle" />
        <div className="line-content-wrapper">
          <span className="time">{moment(time).format('YYYY-MM-DD HH:mm:ss')}</span>
          <div className="line-content">
            {flowStateMap[action] && flowStateMap[action].render(line, this.handleHistoryVersionClick, userInfo, flow, authStaffStaffMap)}
          </div>
        </div>
      </div>
    )
  }

  render() {
    const { menus = [], selectedMenu, formatLogs } = this.state
    if (!formatLogs) {
      return null
    }
    return (
      <div className="detail-history-wrapper">
        <PopoverMenus
          menus={menus}
          placement="bottomLeft"
          align={{ offset: [0, 0] }}
          onSelect={this.handleLogTypeChange}
        >
          <div className="history-title">
            {selectedMenu.label}
            <img src={SVG_DOWN} alt="" />
          </div>
        </PopoverMenus>
        <div className="history-wrapper" style={{ display: 'block' }}>
          {formatLogs.map((line, index) => this._renderLine(line, index))}
        </div>
      </div>
    )
  }
}
