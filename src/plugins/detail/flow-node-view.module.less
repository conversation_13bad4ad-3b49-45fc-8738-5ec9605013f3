@import '~@ekuaibao/eui-styles/less/token.less';

.flow-config-wrap {
  width: 100%;
  margin-bottom: 24px;
  background: #ffffff;
  position: relative;

  :global {
    .flow-config-con {
      padding: 50px 20px 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-around;

      .flow-config-repeat {
        position: relative;
        padding: 10px 0;
        width: 192px;
        height: 250px;
        display: flex;
        align-items: center;
        flex-direction: column;

        .user-status {
          position: absolute;
          right: -14px;
          bottom: -14px;
          width: 40px;
          height: 40px;
          border-radius: 40px;
          background: url('./images/flow-config-wait.svg') no-repeat center;
          // pc web 统一
          display: none;
        }

        &:first-child {
          .flow-config-bg {
            left: 50%;
          }

          .flow-left-bg {
            left: -30%;
          }
        }

        &:last-child {
          .flow-config-bg {
            right: 50%;
          }

          .flow-right-bg {
            right: -30%;
          }
        }

        &.done,
        &.now {
          .user-status {
            display: none;
          }

          svg {
            position: absolute;
            width: 36px;
            height: 36px;
            color: var(--brand-base);
          }
        }

        &.done {
          .user-pic {
            .user-pic-img {
              opacity: 1;
            }
          }

          .user-status {
            background: url('./images/flow-config-done.svg') no-repeat center;
            display: none;
            // pc web 统一
          }

          svg {
            right: -4px;
            bottom: -10px;
          }
        }

        &.now {
          width: 224px;
          height: 284px;

          svg {
            right: -2px;
            bottom: -10px;
          }

          .flow-config-bg {
            top: 58px;
          }

          .flow-config-bg-all {
            top: 58px;
          }

          .user-pic {
            width: 128px;
            height: 128px;

            .user-pic-img {
              width: 96px;
              height: 96px;
              opacity: 1;
              border-radius: 8px;
              overflow: hidden;

              img {
                width: 100%;
                height: 100%;
              }
            }
          }

          .countersign {
            .count {
              font-size: 28px;
            }
          }

          .flow-name {
            width: 224px;
            font-size: 28px;
            font: var(--eui-font-body-r1);
            margin-top: 20px;
            color: #262626;
          }

          .user-name {
            font-size: 28px;
          }

          .user-status {
            background: url('./images/flow-config-now.svg') no-repeat center;
            display: none;
            // pc web 统一
          }
        }

        &.reject {
          .user-pic {
            .user-pic-img {
              opacity: 1;
            }
          }

          .user-status {
            background: url('./images/flow-config-reject.svg') no-repeat center;
          }
        }

        &.retract {
          .user-pic {
            .user-pic-img {
              opacity: 1;
            }
          }

          .user-status {
            background: url('./images/flow-config-retract.svg') no-repeat center;
          }
        }

        .user-pic {
          position: relative;
          z-index: 2;
          width: 96px;
          height: 96px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #fff;

          .user-pic-img {
            width: 64px;
            height: 64px;
            border-radius: 4px;
            opacity: 0.5;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }

        .flow-name {
          margin-top: 30px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 192px;
          font-size: 24px;
          line-height: 44px;
          text-align: center;
          color: #8c8c8c;
        }

        .user-name {
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          font-size: 24px;
          text-align: center;
          line-height: 44px;
          color: #8c8c8c;
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          .user-staff-config {
            position: relative;
            padding: 0 10px;
            max-width: 100%;
            overflow: hidden;
            text-align: center;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
            &::after,
            &::before {
              position: absolute;
              top: -5%;
              // transform: translateY(-50%);
            }
            &::after {
              right: 0;
              content: ")";
            }
            &::before {
              left: 0;
              content: "(";
            }
          }
        }

        .countersign {
          display: flex;

          .count {
            font-size: 24px;
            text-align: center;
            line-height: 44px;
            color: #8c8c8c;
          }
        }

        .flow-config-bg {
          position: absolute;
          top: 42px;
          width: 200%;
          height: 2px;
          background: #e8e8e8;
        }

        .flow-config-bg-all {
          position: absolute;
          top: 42px;
          left: -30%;
          right: -30%;
          height: 2px;
          background: #e8e8e8;
        }
      }
    }

    .plan-view-all {
      font-size: 28px;
      line-height: 80px;
      color: var(--brand-base);
      text-align: center;
    }
  }

  &.simple-mode {
    overflow: hidden;
    :global {
      & .flow-config-con {
        padding: 0;
  
        .flow-config-repeat {
          .user-pic {
            width: 36px;
            height: 36px;
  
            .user-pic-radius {
              border-radius: 0;
            }
            .user-pic-img, .icon {
              width: 36px;
              height: 36px;
            }
          }
          .flow-config-bg {
            top: 35px;
            border-bottom: 2px solid var(--brand-base);
          }
          &.normal svg {
            position: absolute;
            bottom: -10px;
          }
          &.now {
            width: 192px;
            height: 250px;
            .flow-name {
              margin-top: 45px;
            }     
          }
        }
      } 
    }
  }
}


.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-name-text {
  width: 100%;
  .ellipsis;
}
