/**************************************************
 * Created by nanyuantingfeng on 27/09/2016 16:57.
 **************************************************/
import { Resource } from '@ekuaibao/fetch'
import key from './key'
import * as util from '../../lib/util'

const plan = new Resource('/api/v1/flow/plans')
const report = new Resource('/api/v1/budget/report')

export function getFlowPlanStateById(id, done) {
  return {
    type: key.GET_FLOW_PLAN_STATE,
    payload: plan.GET('/$id', { id }),
    done(_, action) {
      done && done(action.payload.value)
    }
  }
}

export function getFlowReportInfo(param) {
  util.showLoading()
  return {
    type: key.FLOW_REPORT_INFO,
    payload: report.POST('/flow/$id/info/$budgetId/node/$nodeId/time/$periodTime', { name: param.name }, param),
    isAddMore: param.isAddMore
  }
}

export function clearReportInfo() {
  return {
    type: key.CLEAR_REPORT_INFO,
    payload: null
  }
}

export function getVersionsFlowplan({ planId }) {
  return plan.GET('/versioned/$id', {
    id: planId,
    join: 'flowPlan.nodes.counterSigners.signerId,signerId,/v1/organization/staffs?select=id,name,enName,avatar,email,cellphone,note',
    join$1: 'flowPlan.nodes.approverId,approverId,/v1/organization/staffs'
  })
}
