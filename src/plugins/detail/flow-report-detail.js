/**
 * Created by <PERSON><PERSON><PERSON> on 23/03/2017.
 */
import React from 'react'
import './flow-report-detail.less'
// import ReportContainer from '../../plugins/report/report-container'
import { app as api } from '@ekuaibao/whispered'
const ReportContainer = api.require('@report/report-container')
import key from './key'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import { EnhanceConnect } from '@ekuaibao/store'

import * as actions from './detail.action'
// import ReportDetailView from '../basic-elements/budget/report-detail-view'
const ReportDetailView = api.require('@basic-elements/budget/report-detail-view')

@EnhanceTitleHook(i18n.get('查看预算'))
@EnhanceConnect(state => ({
  reportInfo: state[key.ID].flowReportInfo,
  reportMapper: state[key.ID].flowReportMapper,
  reportInfoMapper: state[key.ID].reportInfoMapper,
  standardCurrency: state['@common'].standardCurrency
}),
  null,
  'reportUsedDetail'
)
class FlowReportDetail extends ReportContainer {
  componentWillMount() {
    super.componentWillMount()
  }

  componentWillReceiveProps(nextProps) {
    super.componentWillReceiveProps(nextProps)
  }

  componentWillUnmount() {
    super.componentWillMount()
    api.dispatch(actions.clearReportInfo())
  }

  handleReportInfo(fetchParams) {
    const { nodeId, start = 0, count = 10, name = '', isAddMore } = fetchParams
    let { details, reportInfo } = this.state
    if (details && details[nodeId]) {
      let { info } = reportInfo
      let childrenList = reportInfo.items.filter(item => info.nodeId !== item.nodeId)

      this.setState({
        reportDetail: details[nodeId],
        childrenList: childrenList,
        period: reportInfo.info.period.period
      })
    } else {
      let param = {
        id: this.state.flowId,
        budgetId: this.state.budgetId,
        nodeId: nodeId,
        periodTime: this.state.periodTime,
        start,
        count,
        name,
        isAddMore
      }
      return api.dispatch(actions.getFlowReportInfo(param))
    }
  }

  handleChildLineClick(line) {
    this.handleReportInfo({ nodeId: line.nodeId })
    api.go('/flow-report-detail/' + this.state.flowId + '/' + line.budgetId + '/' + line.fromNodeId +  '/' + line.nodeId + '/' + line.periodTime)
  }

  handleFetchBudget = params => {
    return this.handleReportInfo(params)
  }

  handleUsedDetail = detailData => {
    this.props.setState(detailData)
    api.go('/used-detail')
  }

  render() {
    let { standardCurrency } = this.props
    if (this.state.reportMapper) {
      return (
        <ReportDetailView
          reportMapper={this.state.reportMapper}
          reportDetail={this.state.reportDetail}
          reportInfo={this.state.reportInfo}
          nodeId={this.state.nodeId}
          period={this.state.period}
          periodTime={this.state.periodTime}
          flowId={this.state.flowId}
          headerTitle={this.state.headerTitle}
          childrenList={this.state.childrenList}
          standardCurrency={standardCurrency}
          handleChildLineClick={this.handleChildLineClick.bind(this)}
          handleFetchBudget={this.handleFetchBudget}
          usedDetail={this.handleUsedDetail}
        />
      )
    } else {
      return null
    }
  }
}

export default FlowReportDetail
