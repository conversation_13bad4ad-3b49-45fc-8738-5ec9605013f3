import styles from './flow-node-view.module.less'
import React, { PureComponent } from 'react'
import classnames from 'classnames'
import { app } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
// import SVG_AVATAR_NULL from '../../images/avatar-null.svg'
const SVG_AVATAR_NULL = app.require('@images/avatar-null.svg')
// import SVG_AVATAR_NULL_BIG from '../../images/avatar-null.svg'
const SVG_AVATAR_NULL_BIG = app.require('@images/avatar-null.svg')
// import Avatar from '../basic-elements/avatars'
const Avatar = app.require('@basic-elements/avatars')
import { Avatar as AvatarEui } from '@hose/eui-mobile'
// import EkbIcon from '../../elements/ekbIcon'
const EkbIcon = app.require('@elements/ekbIcon')
import { cloneDeep, get, isString } from 'lodash'
import { Fetch } from '@ekuaibao/fetch'
import { getUserDisplayName } from "../../components/utils/fnDataLinkUtil";
import { getAIAgentLabelDom, getAIAgentObj } from '../../elements/ai-agent-utils'
import { FilledTipsClose } from '@hose/eui-icons'

@EnhanceConnect(state => ({
  staffDisplayConfigField: state['@common'].organizationConfig?.staffDisplayConfig?.[1] || '',
  authStaffStaffMap: state['@common'].authStaffStaffMap || {},
  nodesAIAgentMap: state['@common'].nodesAIAgentMap || {},
}))
export default class FlowNodeView extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      isLeftBg: false,
      isRightBg: false,
      displayNodes: [],
    }
  }

  componentWillMount() {
    const value = this.props.value
    const plan = value.flowId ? value.flowId.plan : value.plan
    const logs = value.flowId ? value.flowId.logs : value.logs
    const nodes = this.fnHandleState(plan, logs)
    this.fetchAIAgentObj(plan?.nodes, logs)
    this.setState({ displayNodes: nodes })
  }

  fetchAIAgentObj = async (nodes, logs) => {
    const { nodesAIAgentMap } = this.props
    app.dataLoader('@common.nodesAIAgentMap').load({
      currentMap: nodesAIAgentMap,
      nodes,
      logs
    })
  }

  handleNodeClick(node) {
    return () => {
      const { handleNodeClick } = this.props
      handleNodeClick && handleNodeClick(node)
    }
  }

  handleGoToViewApprove = () => {
    const { layerManager } = this.props
    const { value, isReadOnly } = this.props
    let { id, formType } = value.flowId ? value.flowId : value
    formType = formType === 'requisition' ? 'formRequisition' : formType
    if (layerManager) {
      app.open('@requisition:FlowPlanReadonly', {
        params: { id, formType, isReadOnly: isReadOnly.toString() }
      })
    } else {
      let path = `/flow-plan-readonly/${formType}/${id}`
      if (!isReadOnly) {
        path = path + `/${!!isReadOnly}`
      }
      app.go(path)
    }
  }

  fnHandleState(plan, logs) {
    const isRetract = logs && logs.length > 0 && logs[logs.length - 1].action === 'freeflow.retract'
    const state = logs && logs.length > 0 && logs[logs.length - 1].state
    const isCompleted = state && (state === 'paid' || state === 'archived')
    const flowPlan = cloneDeep(plan)
    const taskId = flowPlan.taskId
    const prevId = flowPlan.prevId
    const Num = 3
    flowPlan.nodes.reduce(
      (prev, next) => {
        if (prevId === 'SUBMIT' && taskId === next.id) {
          if (prev.stateEx === undefined) {
            prev.stateEx = 'done'
          }

          next.stateEx = 'now' // 审批中
          return next
        }

        if (taskId === 'SUBMIT' && prevId === prev.id) {
          prev.stateEx = isRetract ? 'retract' : 'reject' //驳回
          next.stateEx = ''
          return next
        }

        if (flowPlan.nodes.length === 1 && taskId === 'SUBMIT' && prevId === next.id) {
          //只有一个节点
          next.stateEx = isRetract ? 'retract' : 'reject'
          return next
        }

        if (prev.stateEx === 'now' || prev.stateEx === '') {
          next.stateEx = ''
          return next
        }

        if (next.id === prevId) {
          //最后一个节点
          if (taskId === null) {
            next.stateEx = 'done'
          } else if (taskId === 'SUBMIT') {
            next.stateEx = isRetract ? 'retract' : 'reject'
          }

          prev.stateEx = 'done'
          return next
        }

        if (prev.id === prevId && next.id === taskId) {
          prev.stateEx = 'done'
          next.stateEx = 'now'
          return next
        }

        if (
          (prev.skippedType === 'APPROVER_NOT_FOUND_BY_ROLE' ||
            prev.skippedType === 'APPROVER_SAME_AS_SUBMITTER_BY_ROLE') &&
          next.id === taskId
        ) {
          next.stateEx = 'now'
        }

        if (next.id === taskId) {
          prev.stateEx = 'done'
          next.stateEx = 'now'
        }

        prev.stateEx = 'done' //通过
        return next
      },
      { id: 'SUBMIT' }
    )

    let flowNodes = cloneDeep(flowPlan.nodes)
    let node = flowNodes.filter(line => get(line, 'ebotConfig.type') !== 'costControlCheck')
    let ids = []
    node.forEach(oo => {
      if ((oo.type === 'ebot' || oo.type === 'invoicingApplication') && get(oo, 'ebotConfig.hiddenNode')) {
        const hiddenModule = get(oo, 'ebotConfig.hiddenModule', ['feeflow']) || ['feeflow']
        if (hiddenModule?.includes('feeflow')) {
          ids.push(oo.id)
        }
      } else if (oo.type === 'recalculate' && get(oo, 'config.hiddenNode')) {
        ids.push(oo.id)
      } else if (oo.skippedType !== 'NO_SKIPPED' && get(oo, 'config.hiddenNode')) {
        ids.push(oo.id)
      }
    })

    let nodes = node.filter(vv => !~ids.indexOf(vv.id))
    let displayNodes = []

    if (isCompleted) {
      nodes[nodes.length - 1].stateEx = 'done'
    }

    if (nodes.length <= Num) {
      displayNodes = nodes
    }

    const firstNodes = nodes[0].stateEx === 'now' || nodes[0].stateEx === 'reject' || nodes[0].stateEx === 'retract'
    const stateEx = nodes[nodes.length - 1].stateEx
    const lastNodes = stateEx === 'now' || stateEx === 'reject' || stateEx === 'done'

    if (nodes.length > Num && firstNodes) {
      displayNodes = nodes.slice(0, Num)
      this.setState({ isRightBg: true })
    }

    if (nodes.length > Num && lastNodes) {
      displayNodes = nodes.slice(nodes.length - Num)
      this.setState({ isLeftBg: true })
    }

    if (nodes.length > Num && !firstNodes && !lastNodes) {
      let i = nodes.findIndex(item => item.stateEx === 'now' || item.stateEx === 'reject' || item.stateEx === 'retract')
      displayNodes = nodes.slice(i - 1, i + 2)

      if (i < 0) {
        //单据状态为提交中
        displayNodes = nodes.slice(0, Num)
        this.setState({ isRightBg: true })
      } else {
        displayNodes = nodes.slice(i - 1, i + 2)

        if (i + 1 !== nodes.length - 1) {
          this.setState({ isRightBg: true })
        }
        if (i - 1 !== 0) {
          this.setState({ isLeftBg: true })
        }
        if (i - 1 !== 0 && i + 1 !== nodes.length - 1) {
          this.setState({ isLeftBg: true, isRightBg: true })
        }
      }
    }
    //isNeedCashierNode 为空或是true显示出纳节点，否则删除
    return displayNodes.filter(v => get(v, 'config.isNeedCashierNode', true))
  }

  fnGetCountersignerAvatar(node) {
    let counterSigners = node.counterSigners
    let imageList = counterSigners.map(line => {
      return line.signerId.avatar && line.signerId.avatar.length ? line.signerId.avatar : SVG_AVATAR_NULL_BIG
    })
    return imageList.length ? imageList : [SVG_AVATAR_NULL_BIG]
  }

  fnGetCarbonCopyStaffsAvatar(node) {
    const staffIds = get(node, 'carbonCopy[0].staffIds', [])
    if (!staffIds.length) {
      return [SVG_AVATAR_NULL_BIG]
    }
    return staffIds.map(id => this.props.authStaffStaffMap[id]?.avatar || SVG_AVATAR_NULL_BIG)
  }

  renderCountersignProgress(node) {
    const counterSigners = node.counterSigners
    return (
      <div className="countersign">
        <span className="count">{i18n.get('{__k0}人会签', { __k0: counterSigners.length })}</span>
      </div>
    )
  }

  renderCarbonCopy(node) {
    const { authStaffStaffMap: staffMap, staffDisplayConfigField } = this.props
    const staffIds = get(node, 'carbonCopy[0].staffIds', [])
    if (staffIds.length > 1) {
      return (
        <div className="countersign">
          <span className="count">{i18n.get('抄送{__k0}人', { __k0: staffIds.length })}</span>
        </div>
      )
    } else if (staffIds.length === 1) {
      const name = getUserDisplayName(staffMap[staffIds[0]])
      const staffDisplay = get(node, `staffIds[0].${staffDisplayConfigField}`)
      return (
        <div className={classnames('user-name', node.stateEx)}>
          {name}
          <div className="user-staff-config">{staffDisplay}</div>
        </div>
      )
    } else {
      return (
        <div className={classnames('user-name', node.stateEx)}>
          {i18n.get('自动跳过')}
        </div>
      )
    }
  }

  getApprover = node => {
    if (node.type === 'ebot') {
      return 'EBot'
    } else if (node.type === 'invoicingApplication') {
      return i18n.get('开票申请')
    } else if (node.approverId) {
      return getUserDisplayName(node.approverId)
    } else if (node.type === 'aiApproval') {
      const { agent } = getAIAgentObj(node, this.props.nodesAIAgentMap)
      return agent?.name
    }
    return i18n.get('未选择')
  }

  renderStaff(node) {
    const { staffDisplayConfigField } = this.props
    const staffDisplay = get(node, `approverId.${staffDisplayConfigField}`)
    let name = this.getApprover(node)
    if (node?.skippedType === 'PAY_AMOUNT_IS_0' || node?.skippedType === 'NO_ABILITY') {
      name = i18n.get('自动跳过')
    }
    return (
      <div className={classnames('user-name', node.stateEx)}>
        <span className={styles['user-name-text']}>{name}</span>
        {staffDisplay && isString(staffDisplay) && <div className="user-staff-config">{staffDisplay}</div>}
      </div>
    )
  }

  renderLine() {
    const { isLeftBg, isRightBg } = this.state
    const leftAndRightBottomLine = 'flow-config-bg-all'
    const leftBottomLine = 'flow-config-bg flow-left-bg'
    const rightBottomLine = 'flow-config-bg flow-right-bg'
    return (
      <div
        className={
          isLeftBg
            ? isRightBg
              ? leftAndRightBottomLine
              : leftBottomLine
            : isRightBg
            ? rightBottomLine
            : 'flow-config-bg'
        }
      />
    )
  }

  renderImage = node => {
    if (this.props.isSimpleMode) {
      if (node.stateEx === 'done') {
        return <div />
      }
      return <EkbIcon name="#EDico-xingzhuangjiehe" style={{ width: '0.36rem', height: '0.36rem' }} />
    }
    const type = get(node, 'type')
    if (type === 'countersign') {
      return <Avatar imgList={this.fnGetCountersignerAvatar(node)} isStyleChange={true} />
    }

    if (type === 'carbonCopy') {
      return <Avatar imgList={this.fnGetCarbonCopyStaffsAvatar(node)} isStyleChange={true} />
    }

    if (type === 'ebot' || type === 'invoicingApplication') {
      return <EkbIcon name="#EDico-EBot-AI" style={{ width: 32, height: 32 }} />
    }

    if (type === 'recalculate') {
      return <EkbIcon name="#EDico-sys-recount" style={{ width: 32, height: 32 }} />
    }

    if (node?.skippedType === 'PAY_AMOUNT_IS_0' || node?.skippedType === 'NO_ABILITY') {
      return <img src={SVG_AVATAR_NULL} />
    }

    if (node.type === 'aiApproval') {
      const { agent } = getAIAgentObj(node, this.props.nodesAIAgentMap)
      return <AvatarEui size={node.stateEx === 'now' ? "large" : "small"} shape="square" src={agent?.icon}>{agent?.name}</AvatarEui>
    }

    return <img src={(node.approverId && node.approverId.avatar) || SVG_AVATAR_NULL} />
  }

  renderApprover = node => {
    if (node.type === 'countersign') {
      return this.renderCountersignProgress(node)
    } else if (node.type === 'carbonCopy') {
      return this.renderCarbonCopy(node)
    } else {
      return this.renderStaff(node)
    }
  }

  render() {
    const {
      isNeedBudget,
      value: { form = {}, state },
      fromDataLink,
      isSimpleMode = false
    } = this.props

    // fromDataLink: 从台账中穿透进来时，不提供【查看全部流程】入口
    const hideBtn = fromDataLink || isNeedBudget === false

    const { displayNodes } = this.state
    const isAlterFlag = form?.alterFlag && form?.alterFlag >= '1' && state === 'draft'
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage

    return (
      <div className={classnames(styles['flow-config-wrap'], { [`${styles['simple-mode']}`]: isSimpleMode })}>
        <div className={classnames('flow-config-con')}>
          {displayNodes.map(node => {
            //@i18n-ignore
            let name = i18n.get((lang === 'en-US' && node?.enName) ? node?.enName : node?.name)
            name = name === '出纳支付' ? (node.label ? node.label : name) : name
            name = node.type === 'aiApproval' ? getAIAgentLabelDom(name) : name
            return (
              <div
                key={node.id}
                className={classnames('flow-config-repeat', node.stateEx || 'normal')}
                onClick={this.handleNodeClick(node)}
              >
                <div className="user-pic">
                  <div className="user-pic-img">{this.renderImage(node)}</div>
                  <div className="user-status" />
                  {node.stateEx === 'now' && !isAlterFlag && <EkbIcon name="#EDico-xingzhuangjiehe" />}
                  {node.stateEx === 'done' && !isAlterFlag && <EkbIcon name="#EDico-yiqueren" />}
                  {node.stateEx === 'reject' && !isAlterFlag && <FilledTipsClose fontSize={20} color="var(--eui-function-danger-500, #F53F3F)" style={{position: 'absolute', right: '-4px', bottom: '-6px'}}/>}
                </div>
                <div className="flow-name">{name}</div>
                {node.type !== 'recalculate' && this.renderApprover(node)}
                {displayNodes.length > 1 && this.renderLine()}
              </div>
            )
          })}
        </div>
        {!isSimpleMode && !hideBtn && (
          <div className="plan-view-all" onClick={this.handleGoToViewApprove}>
            {i18n.get('查看全部流程')}
          </div>
        )}
      </div>
    )
  }
}
