/**************************************************
 * Created by nanyuanting<PERSON> on 27/09/2016 16:57.
 **************************************************/
import { Reducer } from '@ekuaibao/store'
import key from './key'
import { get } from 'lodash'
import { toast, hideLoading } from '../../lib/util'
import { app as api } from '@ekuaibao/whispered'

const reducer = new Reducer(key.ID, {
  plan_map: {},
  flowReportInfo: [],
  flowReportMapper: {},
  reportInfoMapper: {}
})

reducer.handle(key.GET_FLOW_PLAN_STATE, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  let plan_map = state.plan_map
  let value = action.payload.value
  plan_map = { ...plan_map, [value.id]: value }
  return { ...state, plan_map }
})

reducer.handle(key.FLOW_REPORT_INFO, (state, action) => {
  hideLoading()
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  // let flowReportInfo = action.payload
  const _payload = action.payload
  let _items = _payload?.items || []
  const flowReportList = api.getState()['@common'].flowReportList || []
  if (!!_items.length) {
    const rootNode = flowReportList.find(v => v.budgetId === _items?.[0]?.budgetId)
    _items = _items.map(item => ({ ...item, fromNodeId: rootNode?.nodeId }))
  }
  let flowReportInfo = { info: _payload.info, items: _items }
  const items = get(state, 'flowReportInfo.items') || []
  if (action.isAddMore) {
    flowReportInfo = { info: flowReportInfo.info, items: [...items, ...flowReportInfo.items] }
  }
  let { flowReportMapper, reportInfoMapper } = state
  flowReportMapper[flowReportInfo.info.nodeId] = flowReportInfo.items.filter(
    item => item.nodeId === flowReportInfo.info.nodeId
  )[0]
  reportInfoMapper[flowReportInfo.info.nodeId] = flowReportInfo

  return { ...state, flowReportInfo, flowReportMapper, reportInfoMapper }
})

// clear reportMapper
reducer.handle(key.CLEAR_REPORT_INFO, (state, action) => {
  return {
    ...state,
    flowReportInfo: {},
    flowReportMapper: {},
    reportInfoMapper: {}
  }
})

export default reducer
