/**
 * Created by wq on 16/01/2018.
 */
import './switch-enterprise.less'
import React, { PureComponent } from 'react'
import { ActivityIndicator } from 'antd-mobile'
import { EnhanceConnect } from '@ekuaibao/store'
import { Fetch } from '@ekuaibao/fetch'
import { connect } from '@ekuaibao/mobx-store'
import EnhanceTitleHook from '../basic-elements/enhance-title-hook'
import { app as api } from "@ekuaibao/whispered";
import * as actions from './join.action'
import { session } from '@ekuaibao/session-info'
import { Dialog, Toast } from '@hose/eui-mobile'
import {
  toast,
  getUrlParamString,
  generateQs,
  CORP_VERSION_MAP,
  detectClientBrowser,
  CorpTabsData
} from '../../lib/util'
import { switchcorporationForLog } from './enterprise-fetch-util'
import { disableMC } from '../mine/mine-address-book/mine-util'
const SkeletonListEUI = api.require('@home5/SkeletonList')
import Cookies from 'js-cookie'
import { Button, Tabs, SearchBar, ErrorBlock } from '@hose/eui-mobile'
import { OutlinedTipsDone, IllustrationMiddleNoContent } from '@hose/eui-icons'
import { highLightSpan } from '../../lib/util/highLightSpan'
import { I18nCorpName } from '../../lib/util/corp-util'
import { getBoolVariation } from "../../lib/featbit";

@EnhanceTitleHook(i18n.get('选择企业'))
@connect(store => ({
  clusterCorpList: store.states['@home5']?.clusterCorpList || []
}))
@EnhanceConnect(state => ({
  list: state['@join-enterprise5'].corporationsList,
  externalCorpList: state['@join-enterprise5'].externalCorpList,
  me_info: state['@common'].me_info,
  isEnableMC: disableMC(state['@common'].MCPermission)
}))
export default class SwitchEnterprises extends PureComponent {
  constructor() {
    super()
    this.state = {
      list: [],
      checkedId: '',
      loading: false,
      clusterCorpList: [],
      showSkeleton: true,
      showTab: false,
      activeKey: CorpTabsData[0].key,
      currentList: [],
      searchValue: '',
      searchList: [],
      allCount: 0
    }
  }

  readData = propData => {
    let showTab = false
    let currentList = propData.list
    let activeKey = CorpTabsData[0].key
    let allCount = propData.list.length
    if (propData.list?.length > 0 && propData.externalCorpList?.length > 0) {
      showTab = true
      currentList = propData.list
      allCount = propData.list?.length + propData.externalCorpList?.length
    } else if (propData.externalCorpList?.length > 0) {
      currentList = propData.externalCorpList
      allCount = currentList.length
      activeKey = CorpTabsData[1].key
    }
    this.setState({ showTab, currentList, allCount, activeKey })
  }

  componentDidMount() {
    // 集群环境获取集群企业列表
    if (window.PLATFORMINFO?.clusterURL) {
      api.store
        .dispatch('@home5/getClusterCorporations')()
        .then(() => {
          this.setState({ showSkeleton: false })
        })
    }
    api.dispatch(actions.getCorporations()).then(() => {
      this.setState({ showSkeleton: false })
    })
    api.invokeService('@common:get:mc:permission:byName', 'DEPARTMENT')
    this.setState({ checkedId: this.props.me_info?.staff?.corporationId?.id })
  }

  componentWillUnmount() {
    clearTimeout(this.timer)
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.me_info !== nextProps.me_info) {
      this.setState({ checkedId: nextProps.me_info?.staff?.corporationId?.id })
    }
    if (
      window.PLATFORMINFO?.clusterURL &&
      (this.props.list !== nextProps.list || this.props.clusterCorpList !== nextProps.clusterCorpList)
    ) {
      this.handleClusterCorpList(nextProps)
    }
    if (this.props.list !== nextProps.list || this.props.externalCorpList !== nextProps.externalCorpList) {
      this.readData(nextProps)
    }
  }

  handleClusterCorpList = props => {
    let { clusterCorpList, list } = props
    // 过滤集群列表与企业列表中重复的企业
    clusterCorpList = clusterCorpList?.filter(
      item => list.findIndex(v => v.corporation.id === item.corporation.id) === -1
    )

    this.setState({
      clusterCorpList: [...list, ...clusterCorpList]
    })
  }
  handleCloseLoading = () => {
    this.setState({ checkedId: '', loading: false })
  }

  switchCorp = o => {
    let checkedId = o.corporation.id
    this.state.selectCorp = o
    this.setState({ checkedId, loading: !this.state.loading }, async _ => {
      if (this.state.selectCorp.corporation.sourceChannel === 'V1') {
        let url = this.state.selectCorp.corporation.sourceId
        var token = getUrlParamString(url, 'token')
        token = decodeURIComponent(token)
        api
          .dispatch(actions.getV1UserInfo({ token: token }))
          .then(data => {
            let json = data.id
            json = JSON.parse(json)
            json.token = token
            if (json.code === 100) {
              api.invokeService('@layout:login:v1', { loginData: json })
              return
            } else {
              toast.info(i18n.get('登录失效，请重新登录'))
              api.gotoLoginPage()
            }
          })
          .catch(e => {
            toast.info(e.message)
            return
          })
        return
      }
      if (getBoolVariation('haf-22-idp_login', false) && api.sdk?.switchCorporation) {
        session.remove('user')
        const staff = api.getState()['@common'].me_info?.staff
        return api.sdk.switchCorporation(o.corporation, staff.id)
      }else {
        api.sdk?.logout?.()
      }
      // 判断密码是否过期
      const checkPwdExpire = await api.invokeService('@account5:password:expire:modal', {
        corpId: this.state.checkedId,
        checkInApplication: true,
        cancelCallback: this.handleCloseLoading
      })
      if (!checkPwdExpire) return
      const authResult = await api.invokeService('@account5:check:multilAuth', this.state.checkedId)
      if (authResult === 'cancel') {
        this.handleCloseLoading()
        return
      }

      if (Fetch.ekbCorpId === this.state.checkedId) return api.go(-1)

      Fetch.ekbCorpId = this.state.checkedId
      if (!window.PLATFORMINFO?.clusterURL) {
        switchcorporationForLog(this.state.checkedId)
      }
      // 灰度方案的兜底策略，在登录后选择企业的时候，将cropId存到cookie
      const grayDomain =
        detectClientBrowser().indexOf('DingTalk') !== -1
          ? window.location.host
          : document.domain
              .split('.')
              .slice(-2)
              .join('.')
      Cookies.set('corpId', Fetch.ekbCorpId, { path: '', domain: grayDomain, expires: 365 })
      const params = Fetch.makeUrlParams(
        {
          corpId: Fetch.ekbCorpId,
          ekbCorpId: Fetch.ekbCorpId,
          wxCorpId: Fetch.wxCorpId
        },
        ['corpId', 'ekbCorpId', 'wxCorpId']
      )

      session.set('user', {
        accessToken: Fetch.accessToken,
        corpId: Fetch.ekbCorpId
      })
      this.timer = setTimeout(() => {
        this.setState({ loading: !this.state.loading })
        if (window.PLATFORMINFO?.clusterURL) {
          location.href = `${window.PLATFORMINFO?.clusterURL}tenant/login?corporationId=${Fetch.ekbCorpId}&${params}`
        } else {
          location.replace('?' + params)
        }
      }, 1000)
    })
  }
  onChange = o => {
    const { activeKey } = this.state
    if (Fetch.ekbCorpId === o.corporation.id) {
      return
    }
    if (activeKey === 'ext') {
      Toast.show({ icon: 'fail', content: i18n.get('移动端暂不支持供应商协同门户功能，请在电脑端登录后进行操作。') })
      return
    }
    const urlParams = new URLSearchParams(window.location.search)
    const isOem = urlParams.get('type') === 'oem'
    Fetch.GET('/api/oem/v1/corporation/oemCheck', { oem: isOem, selectedCorpId: o.corporation.id })
      .then(res => {
        if (res === false) {
          //没有权限进入企业
          Dialog.alert({
            iconType: 'warn',
            title: '企业受控',
            content: '该企业登录受控，请从第三方进入'
          })
        } else {
          this.switchCorp(o)
        }
      })
      .catch(() => {
        this.switchCorp(o)
      })
  }

  handleCreateEnterprise() {
    api.go('/create-enterprise-start5')
  }

  tabChange = activeKey => {
    const currentList = activeKey === CorpTabsData[0].key ? this.props.list : this.props.externalCorpList
    this.setState({ activeKey, currentList })
  }

  handleSearch = searchValue => {
    this.setState({
      searchValue,
      searchList: this.state.currentList?.filter(item => {
        const name = I18nCorpName(item.corporation)
        return name.includes(searchValue)
      })
    })
  }

  render() {
    const { me_info, isEnableMC } = this.props
    const { showSkeleton, activeKey, showTab, checkedId, currentList, searchValue, searchList, allCount } = this.state
    if (showSkeleton) {
      return <SkeletonListEUI />
    }
    const isAdmin = me_info.permissions && me_info.permissions.includes('SYS_ADMIN')
    const showCreateBtn = window.IS_ICBC && !isAdmin ? false : this.props.isEnableMC
    let hasCorpVersion = false
    if (this.props.list && this.props.list.length > 0) {
      hasCorpVersion =
        this.props.list.filter(
          v =>
            v.corporation.sourceChannel === 'V1' ||
            v.corporation.sourceChannel === 'GROUP' ||
            v.corporation.sourceChannel === 'SHARED'
        ).length > 0
    }
    const corporationVersionKeyMap = CORP_VERSION_MAP()

    let list = window.PLATFORMINFO?.clusterURL ? this.state.clusterCorpList : currentList
    list = searchValue ? searchList : list
    return (
      <div className="switch-enterprise">
        <div className={showCreateBtn ? 'switch-content' : 'switch-content big'}>
          {showTab && (
            <Tabs activeKey={activeKey} onChange={this.tabChange}>
              {CorpTabsData.map(item => {
                return <Tabs.Tab title={item.label} key={item.key} />
              })}
            </Tabs>
          )}
          <div className={['switch-inner-content', showTab ? 'switch-has-tab-list' : ''].join(' ')}>
            {allCount > 5 && (
              <div className="switch-content-search">
                <SearchBar placeholder={i18n.get('搜索企业名称')} onChange={this.handleSearch} />
              </div>
            )}
            <div className={['switch-content-list', allCount > 5 ? 'switch-has-search-list' : ''].join(' ')}>
              {list &&
                list.length > 0 &&
                list.map(o => {
                  let type = o.corporation.sourceChannel
                  if (IS_STANDALONE && type === 'V1') return null
                  return (
                    <div
                      onClick={() => this.onChange(o)}
                      key={o.corporation.id}
                      className={checkedId === o.corporation.id ? 'switch-corp-item active' : 'switch-corp-item'}
                    >
                      <div>
                        <div>
                          {highLightSpan(I18nCorpName(o.corporation), searchValue)}{' '}
                          {highLightSpan(`${hasCorpVersion ? corporationVersionKeyMap[type] : ''}`, searchValue)}
                        </div>
                        <div>{o.staffCount + ' 人'}</div>
                      </div>
                      {checkedId === o.corporation.id && (
                        <div>
                          <OutlinedTipsDone />
                        </div>
                      )}
                    </div>
                  )
                })}
              {list && list.length === 0 && (
                <ErrorBlock status="empty" image={<IllustrationMiddleNoContent />} title="暂无内容" />
              )}
            </div>
          </div>
        </div>
        {(showCreateBtn || isEnableMC) && (
          <div className="switch-footer">
            <Button block category="primary" size="large" onClick={this.handleCreateEnterprise.bind(this)}>
              {i18n.get('创建企业')}
            </Button>
          </div>
        )}
        <ActivityIndicator text={i18n.get('正在切换中...')} animating={this.state.loading} toast />
      </div>
    )
  }
}
