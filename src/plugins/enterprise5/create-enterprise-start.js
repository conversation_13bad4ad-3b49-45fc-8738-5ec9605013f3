/**
 * Created by wq on 22/12/2017.
 */
import './join-enterprise.less'
import React, { PureComponent } from 'react'
import { List, InputItem, ActivityIndicator } from 'antd-mobile'
import { Fetch } from '@ekuaibao/fetch'
import { session } from '@ekuaibao/session-info'
import EnhanceTitleHook from '../basic-elements/enhance-title-hook'
import EnhanceFormCreate from '../../elements/enhance/enhance-form-create'
import { app as api } from '@ekuaibao/whispered'
import { toast, detectClientBrowser } from '../../lib/util'
import * as actions from './join.action'
import Cookies from 'js-cookie'
import { Form, Input, Button } from '@hose/eui-mobile';



const HeaderImg = require('./images/header.svg')
@EnhanceTitleHook(i18n.get('创建企业'))
@EnhanceFormCreate()
export default class CreateEnterPriseView extends PureComponent {
  constructor() {
    super()
    this.state = {
      channelInviteCode: '',
      loading: false
    }
  }

  componentWillUnmount() {
    clearTimeout(this.closeTimer)
  }

  handleSubmit = (values) => {
    this.setState({ loading: true })
    api
      .dispatch(
        actions.createOrg({
          name: values.name,
          enName: values.enName,
          channelInviteCode: this.state.channelInviteCode
        })
      )
      .then(async resp => {
        Fetch.ekbCorpId = resp.id

        // 灰度方案的兜底策略，在登录后选择企业的时候，将cropId存到cookie
        const grayDomain =
          detectClientBrowser().indexOf('DingTalk') !== -1
            ? window.location.host
            : document.domain
              .split('.')
              .slice(-2)
              .join('.')
        Cookies.set('corpId', Fetch.ekbCorpId, { path: '', domain: grayDomain, expires: 365 })
        session.set('user', {
          accessToken: Fetch.accessToken,
          corpId: Fetch.ekbCorpId
        })
        api
          .dataLoader('@common.me_info')
          .reload()
          .then(data => {
            this.sendCrm(data.staff.cellphone, data.staff.email)
            this.closeTimer = setTimeout(() => {
              this.setState({ loading: false })
              api.go('/create-enterprise-finished5')
            }, 100)
          })

        // this.closeTimer = setTimeout(() => {
        //   this.setState({ loading: false })
        //   api.go('/create-enterprise-finished5')
        // }, 100)
      })
      .catch(e => {
        this.setState({ loading: false })
      })
  }

  sendCrm(cellphone, email) {
    const isEmail = !cellphone && !!email
    api.dispatch(
      actions.sendCrm({
        userEmail: email,
        cellphone: cellphone,
        entryPoint: isEmail ? i18n.get('APP 邮箱注册') : i18n.get('V2 APP 创建企业')
      })
    )
  }


  render() {
    return (
      <div className="enterprise-view create-enterprise-view">
        <div className="header">
          <img src={HeaderImg} />
        </div>
        <Form
          name="form"
          onFinish={this.handleSubmit}
          loading={this.state.loading}
          footer={
            <Button loading={this.state.loading} block type="submit" color="primary" size="large">
              {i18n.get('创建企业')}
            </Button>
          }
        >
          <Form.Item
            name="name"
            label={i18n.get('中文名称')}
            rules={[
              { required: true, message: i18n.get('请输入企业名称，最多可50个字符') },
              { max: 50, message: i18n.get('请输入企业名称，最多可50个字符') }
            ]}
          >
            <Input placeholder={i18n.get('请输入企业名称，最多可50个字符')} border />
          </Form.Item>
          <Form.Item
            name="enName"
            label={i18n.get('英文名称')}
            rules={[{ max: 50, message: i18n.get('请输入企业名称，最多可50个字符') }]}
          >
            <Input placeholder={i18n.get('请输入企业名称，最多可50个字符')}  border/>
          </Form.Item>
        </Form>
        <ActivityIndicator text={i18n.get('企业初始化中，请稍等...')} animating={this.state.loading} toast />
      </div>
    )
  }
}
