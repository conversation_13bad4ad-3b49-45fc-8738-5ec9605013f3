@px6: 0.12rem;
@px12: 0.24rem;
@px14: 0.28rem;
@px16: 0.32rem;
@px18: 0.36rem;
@px24: 0.48rem;
@px36: 0.72rem;
@px40: 0.8rem;
@px46: 0.92rem;
@px48: 0.96rem;
@px42: 0.84rem;
@px56: 1.12rem;
@px80: 1.6rem;
@px96: 1.92rem;

.select-enterprises {
  z-index: 999;
  height: calc(100% - 0.84rem);
  background-color: white;

  .title-div {
    .title {
      width: 100%;
      font-size: @px24;
      color: rgba(29, 33, 41, 0.9);
      padding: 0.16rem 0.64rem;
      font-weight: 500;
    }
  }

  .am-radio-inner {
    display: none !important;
  }

  .enterprises-list {
    overflow: auto;
    padding: 0.16rem 0.32rem;
    height: calc(100% - 2.76rem);

    &.big{
      height: calc(100% - 3rem) !important;
    }

    .enterprises-list-checked {
      background: #f7f8fa;
      border-radius: 4px;



      .am-list-content {
        color: var(--brand-base) !important;

        .corp-info-text {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;

          .checked-icon{
            display: inline-block !important;
            >*{
              font-size: 0.32rem;
              font-weight: 500;
            }
          }

          & > div {
            color: var(--eui-primary-pri-500) !important;
            font-weight: 500 !important;
          }
        }
      }
    }

    &::-webkit-scrollbar {
      -webkit-appearance: none;
    }

    &::-webkit-scrollbar:vertical {
      width: 12px;
    }

    &::-webkit-scrollbar:horizontal {
      height: 12px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      border: 2px solid #ffffff;
    }

    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background-color: #ffffff;
    }

    .am-list-body {
      &:before {
        display: none;
      }

      &:after {
        display: none;
      }

      .am-list-item {
        padding: 0;
        padding-left: 0.32rem;
        height: @px48;
        font-size: @px14;
        line-height: @px48;
        border-bottom: none;
        background: none;

        .am-list-line {
          padding-right: 0;

          .am-list-content {
            display: flex;
            margin-right: 70px;
            align-items: center;
            flex-direction: row;
            color: rgba(29, 33, 41, 0.9);

            .corp-info-text {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;

              .checked-icon{
                display:none;
                position: absolute;
                right: 0.32rem;
              }

              & > div:first-child {
                display: block;
                margin-right: 0.24rem;
                font-size: 0.4rem;
              }

              & > div:last-child {
                color: var(--eui-text-title);
                font-weight: 400;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }

            .brief {
              color: #888888;
              margin-left: 20px;
            }

            .type {
            }
          }

          .am-list-extra {
            // .am-radio-inner {
            //   &:after {
            //     display: 'inline-block';
            //     width: @px16;
            //     height: @px16;
            //     background: url(./images/check.svg) no-repeat center center;
            //     border: none;
            //     transform: translate(@px18, @px6);
            //   }
            // }
          }
        }
        .am-list-line::after {
          display: none;
        }
      }
    }
  }

  .btn {
    margin: 30px 32px;
  }

  .footer-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0.16rem 0.32rem 0.92rem;
    text-align: center;
    font-size: 0;
    position: fixed;
    bottom: 0;
    left: 0;
    background: #ffffff;
    z-index: 99;
    box-shadow: 0px -2px 8px 2px rgba(29, 33, 41, 0.02), 0px -2px 4px rgba(29, 33, 41, 0.02),
      0px -1px 2px -2px rgba(29, 33, 41, 0.02);

    .create-enterprise {
      margin-right: 0.32rem;
    }
  }
}
