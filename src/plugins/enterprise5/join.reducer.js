import { Reducer } from '@ekuaibao/store'
import key from './key'
import { toast } from '../../lib/util'

const reducer = new Reducer(key.ID, {
  corporationsList: [],
  me_info: { staff: {} }
})

reducer.handle(key.BY_CODE_JOIN_ENTERPRISE, (state, action) => {
  if (action.error) {
    if (action.payload.status === 403) {
      toast.info(action.payload.msg)
    } else {
      toast.fail(action.payload.msg)
    }
    return state
  }
  return { ...state }
})

reducer.handle(key.CREATE_ORG, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  return { ...state }
})
reducer.handle(key.GET_V1_USERINFO, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  return { ...state }
})
reducer.handle(key.GENERATE_INVITE_LINKS, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  return { ...state }
})

reducer.handle(key.GET_CORPORATIONS, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  let corporationsList = action?.payload?.items || []
  let externalCorpList = action?.payload?.externalItems || []
  return { ...state, corporationsList, externalCorpList }
})

reducer.handle(key.GET_INFO, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  return { ...state }
})

reducer.handle(key.GET_INVITE_INFO, (state, action) => {
  if (action.error) {
    toast.fail(action.payload.msg)
    return state
  }
  return { ...state, inviteInfo: action.payload.value, inviteCode: action.inviteCode }
})

export default reducer
