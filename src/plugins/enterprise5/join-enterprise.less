@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.enterprise-view {
  height: 100%;
  background-color: #ffffff;
  .header {
    margin: 80px 0 48px 0;
    text-align: center;
  }
  .eheader {
    margin: 180px 0 80px 0;
    text-align: center;
  }
  .flex {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .btn {
    margin: 32px;
    height: 90px;
  }
  
  .btnc {
    overflow: visible;
  }
}

.join-enterprise-view {
  .txt-w {
    font-size: 32px;
    color: var(--brand-base);
    font-weight: bolder;
    margin-bottom: 16px;
  }
  .enterprise {
    font-size: 34px;
    color: #3a3f3f;
    font-weight: bolder;
  }
}

.create-enterprise-view {
  
  :global {
    .eui-list{
      border: none !important;
    }
  }
  
  .c-txt {
    font-size: 35px;
    color: #bdbdbd;
    font-weight: normal;
  }
}

.notInvited-create-enterprise {
  position: relative;
  .c-txt {
    font-size: 35px;
    color: #bdbdbd;
    font-weight: normal;
    margin-bottom: 65px;
    padding-top: 35px;
  }
  .list {
    margin: 76px 64px 48px;
    .am-list-body {
      border-top: none;
      .am-input-control {
        input {
          text-align: center;
        }
      }
    }
  }
  .toCreate {
    position: absolute;
    left: 50%;
    bottom: 10%;
    transform: translate(-50%, 0);
    color: var(--brand-base);
    font-size: 28px;
  }
}

.application-admission-view {
  .application-admission-main {
    .name {
      font-size: 32px;
      font-weight: bolder;
      color: #3a3f3f;
    }
    .tel {
      margin: 16px 0 80px 0;
      font-size: 28px;
      color: #bbbdbd;
      font-weight: normal;
    }
    .agree-btn {
      height: 100px;
      border-radius: 4px;
      background-color: var(--brand-base);
    }
    .apply-time {
      position: absolute;
      width: 100%;
      bottom: 10%;
      font-size: 28px;
      color: #bbbdbd;
      font-weight: normal;
      text-align: center;
    }
  }
}

.waiting-join-enterprise {
  padding: 0.8rem;

  .txt-send {
    font-size: 32px;
    font-weight: bolder;
    color: var(--brand-base);
  }

  .txt-m {
    margin: 32px 0 48px;
    font-size: 28px;
    color: #bbbdbd;
    font-weight: normal;
  }

  .title {
    font-size: 0.48rem;
    color: hsla(214, 36%, 18%, 1);
    margin-bottom: 0.12rem;
    line-height: 0.64rem;
    font-weight: 500;
  }

  .subtitle {
    font-size: 0.28rem;
    color: hsla(214, 36%, 18%, 0.75);
    line-height: 1;
    margin: 0 0 0.8rem;
  }

  .header img {
    width: 3.4rem;
    height: 3.4rem;
  }
}

.invited-join-enterprise {
  .content {
    display: flex;
    flex-direction: column;
    align-items: left;
    margin: 80px 0;
    padding-left: 0.8rem;
    .title {
      font-size: 0.28rem;
      font-weight: normal;
      color: hsla(214, 36%, 18%, 0.75);
      .name {
        font-weight: bold;
      }
    }
    .company {
      font-size: 0.48rem;
      font-weight: bolder;
      color: hsla(214, 36%, 18%, 1);
      margin: 0.12rem 0 0.8rem 0;
    }
    .m {
      margin: 0 30px;
    }
  }

  .userCard {
    margin: 0 0.8rem 0.8rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.36rem 0;
    border-top: 2px solid hsla(214, 36%, 18%, 0.15);
    border-bottom: 2px solid hsla(214, 36%, 18%, 0.15);
    .avatar {
      width: 0.92rem;
      height: 0.92rem;
      border-radius: 0.16rem;
    }

    .check {
      width: 0.32rem;
      height: 0.32rem;
    }

    .contact {
      margin-left: 0.32rem;
      flex: auto;
    }

    .name {
      font-size: 0.32rem;
      line-height: 0.48rem;
      color: hsla(214, 36%, 18%, 1);
      margin: 0;
    }

    .phone {
      font-size: 0.28rem;
      line-height: 0.44rem;
      color: hsla(214, 36%, 18%, 0.5);
      margin: 0;
    }
  }

  .btn {
    margin: 0 auto 0.24rem auto;
  }
}

.unlogged-content {
  .account-type-change-wrapper {
    margin-bottom: @space-2;
    .account-type-change {
      color: var(--brand-base);
      margin-left: @color-brand;
    }
    .account-type-change:hover {
      cursor: pointer;
    }
  }
  margin: 0 0.8rem;
  input {
    font-size: 0.32rem !important;
  }

  .btn {
    margin-top: 0.8rem;
  }
}

.create-enterprise-finished {
  display: flex;
  flex-direction: column;
  height: 100%;
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .msg {
      text-align: center;
      font-size: 40px;
      color: var(--brand-base);
      font-weight: normal;
      margin: 40px 0 40px 0;
    }
    .msg1 {
      text-align: center;
      font-size: 32px;
      color: #02212b;
      font-weight: 400;
      margin: 80px 0;
    }
    .qrcode-div {
      display: flex;
      justify-content: center;
    }
  }
  .share {
    display: flex;
    flex-direction: column;
    margin: 60px 0;
    .txt {
      text-align: center;
      margin-bottom: 60px;
      font-size: 28px;
      color: #3a3f3f;
    }
    .button-layout {
      z-index: 999;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin: 0 20px;
    }
  }
}

.switch-enterprise {
  display: flex;
  flex-direction: column;
  height: 100%;
  z-index: 999;
  .title-div {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .title {
      font-size: 28px;
      color: #bbbdbd;
      margin: 20px 32px;
    }
  }
  .enterprises-list {
    display: flex;
    flex: 1;
    overflow: auto;
    .am-list-body {
      width: 100%;
      .am-list-content {
        display: flex;
        margin-right: 50px;
        align-items: center;
        flex-direction: row;
        .text {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .brief {
          color: #888888;
          margin-left: 20px;
        }
        .type {
          padding-left: 20px;
        }
      }
    }
  }
  .btn {
    margin: 30px 32px;
  }
}
