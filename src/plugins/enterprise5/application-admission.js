/**
 * Created by wq on 23/12/2017.
 */
import './join-enterprise.less'
import React, { PureComponent } from 'react'
import { Button } from 'antd-mobile'
import EnhanceTitleHook from '../basic-elements/enhance-title-hook'
import { app as api } from '@ekuaibao/whispered'
const HeaderImg = require('./images/header.svg')
const moment = require('moment')
import { toast } from '../../lib/util'

@EnhanceTitleHook(i18n.get('加入申请'))
export default class ApplicationForAdmissionView extends PureComponent {
  constructor(props) {
    super(props)
    let { id, content, createTime } = this.props.params
    createTime = moment(createTime / 1).calendar()
    const urls = id.split('&') //实际传的是上个页面的url
    let applyId = urls && urls.length > 2 ? urls[1].split('=') : []

    const cs = content.split('(')
    this.state = {
      applyId: applyId[1] || '',
      name: cs[0] || '',
      phone: cs[1] ? cs[1].replace(')', '') : '',
      createTime: createTime
    }
  }

  handleSubmit = () => {
    const { applyId, name } = this.state
    let data = {
      event: 'BATCH',
      action: 'ACCEPT',
      items: [
        {
          applyId: applyId,
          staff: {
            departments: [],
            name: name
          }
        }
      ]
    }
    // let data = {
    //   event: 'SINGLE',
    //   action: 'ACCEPT',
    //   value: {
    //     applyId: applyId
    //   }
    // }
    api.invokeService('@mine:post:applyApproval', data).then(res => {
      if (res.value == 1) {
        api.go(-1)
      } else if (res.value == 0) {
        toast.info(i18n.get('同意加入失败或员工已加入'))
        api.go(-1)
      }
    })
  }

  render() {
    const { name, phone, createTime } = this.state
    return (
      <div className="enterprise-view application-admission-view">
        <div className="application-admission-main">
          <div className="header">
            <img src={HeaderImg} />
          </div>
          <div className="flex name">{name && <span>{name}</span>}{i18n.get("申请加入公司")}</div>
          <div className="flex tel">
            {i18n.get("手机号：")}<span>{phone}</span>
          </div>
          <Button className="btn agree-btn" type="primary" onClick={this.handleSubmit}>
            {i18n.get("同意加入")}</Button>
          <div className="apply-time">
            {i18n.get("申请时间：")}<span>{createTime}</span>
          </div>
        </div>
      </div>
    )
  }
}
