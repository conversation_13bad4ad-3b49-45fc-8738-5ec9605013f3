/**
 * Created by wq on 22/12/2017.
 */
import { Resource } from '@ekuaibao/fetch'
import key from './key'
const users = new Resource('/api/app/account/users/')
const organization = new Resource('/api/app/organization/')
const vorganization = new Resource('/api/v1/organization/')
const session = new Resource('/api/account/v2/session')
const crm = new Resource('/api/v1/crm')

export function byCodeJoinEnterprise(data) {
  return {
    type: key.BY_CODE_JOIN_ENTERPRISE,
    payload: users.POST('join', null, data)
  }
}

export function createOrg(data) {
  return {
    type: key.CREATE_ORG,
    payload: users.POST('createOrg', data)
  }
}

export function sendCrm(data) {
  return {
    type: key.SEND_CRM,
    payload: crm.POST('', data)
  }
}

export function generateInviteLinks(data) {
  return {
    type: key.GENERATE_INVITE_LINKS,
    payload: organization.POST('staffs/invite', data)
  }
}

export function getCorporations(data) {
  return {
    type: key.GET_CORPORATIONS,
    payload: vorganization.GET('corporations/andCount', data)
  }
}

export function getInfo(data) {
  return {
    type: key.GET_INFO,
    payload: session.GET('', data)
  }
}

export function getInviteInfo(code) {
  return {
    type: key.GET_INVITE_INFO,
    payload: organization.GET('staffs/invite/$id', { id: code }),
    inviteCode: code
  }
}

export function getV1UserInfo(data) {
  return {
    type: key.GET_V1_USERINFO,
    payload: users.GET('userInfoByToken', data)
  }
}
