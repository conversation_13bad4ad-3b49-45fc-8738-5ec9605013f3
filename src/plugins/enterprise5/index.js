/**
 * Created by wq on 22/12/2017.
 */
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { byCodeJoinEnterprise, getCorporations, getV1UserInfo } from './join.action'
import loadable from '@loadable/component'
import { switchcorporationForLog } from './enterprise-fetch-util'

export default [
  {
    id: '@join-enterprise5',
    reducer: () => require('./join.reducer').default,
    'join:enterprise': code => {
      api
        .dispatch(
          byCodeJoinEnterprise({
            code: code,
            accessToken: Fetch.accessToken
          })
        )
        .then(data => {
          api.go('/waiting-join-enterprise5')
        })
        .catch(err => {
          api.go('/selected-enterprises5')
        })
    },
    'get:corporations': () => {
      api.dispatch(getCorporations())
    },
    'get:v1:userInfo': (param) => {
      return getV1UserInfo(param)
    },
    'switch:corporationForLog': (corpId) => {
      switchcorporationForLog(corpId)
    },
  },

  {
    path: '/join-enterprise5',
    ref: '/',
    onload: () => import('./join-enterprise-success')
  },
  {
    path: '/create-enterprise-start5',
    ref: '/',
    onload: () => import('./create-enterprise-start')
  },
  {
    path: '/create-enterprise-finished5',
    ref: '/',
    onload: () => import('./create-enterprise-finished')
  },
  {
    path: '/unjoin-enterprise5',
    ref: '/',
    onload: () => import('./unjoin-enterprise')
  },
  {
    path: '/empty-enterprise5',
    ref: '/',
    onload: () => import('./empty-enterprise')
  },
  {
    path: '/application-admission5/:id/:content/:createTime',
    ref: '/',
    onload: () => import('./application-admission')
  },
  {
    path: '/application-admission/:id/:content/:createTime',
    ref: '/',
    onload: () => import('./application-admission')
  },
  {
    path: '/application-admission5',
    ref: '/',
    onload: () => import('./application-admission')
  },
  {
    path: '/application-admission',
    ref: '/',
    onload: () => import('./application-admission')
  },
  {
    path: '/waiting-join-enterprise5',
    ref: '/',
    onload: () => import('./join-enterprise-waiting')
  },
  {
    path: '/invite/:code',
    ref: '/',
    onload: () => import('./invite-join-enterprise')
  },
  {
    path: '/invite5/:code',
    ref: '/',
    onload: () => import('./invite-join-enterprise')
  },
  {
    path: '/selected-enterprises5',
    ref: '/',
    onload: () => import('./selected-enterprises')
  },
  {
    path: '/switch-enterprises5',
    ref: '/',
    onload: () => import('./switch-enterprise')
  } ,
  {
    path: '/enterprise-share',
    ref: '/',
    exact: true,
    onload: () => import('./enterprise-share')
  },

  {
    resource :"@join-enterprise5",
    value : {
      ["EnterpriseShareView"] : loadable(() => import("./enterprise-share"))
    }
  }
]
