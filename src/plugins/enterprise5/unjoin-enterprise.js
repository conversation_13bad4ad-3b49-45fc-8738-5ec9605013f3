/**
 * Created by wq on 22/12/2017.
 */
import './join-enterprise.less'
import React, { PureComponent } from 'react'
import { Button, List, InputItem } from 'antd-mobile'
import { Fetch } from '@ekuaibao/fetch'

import EnhanceTitleHook from '../basic-elements/enhance-title-hook'
import { app as api } from '@ekuaibao/whispered'
const HeaderImg = require('./images/header2.svg')
import { toast } from '../../lib/util'
import * as actions from './join.action'

@EnhanceTitleHook(i18n.get('加入企业'))
export default class UnjoinEnterPrise extends PureComponent {
  constructor() {
    super()
    this.state = {
      code: ''
    }
  }

  handleCreateEnterprise = () => {
    api.go('/create-enterprise-start5')
  }

  handleChangeCode = val => {
    this.setState({
      code: val
    })
  }

  handleSubmit = () => {
    if (!this.state.code) {
      toast.info(i18n.get('请输入企业邀请码'))
    }
    api
      .dispatch(
        actions.byCodeJoinEnterprise({
          code: this.state.code,
          accessToken: Fetch.accessToken
        })
      )
      .then(data => {
        api.go('/waiting-join-enterprise5')
      })
  }

  render() {
    return (
      <div className="enterprise-view notInvited-create-enterprise">
        <div className="header">
          <img src={HeaderImg} />
        </div>
        <div className="flex c-txt">{i18n.get('您还没有加入企业')}</div>
        <List className="list">
          <List.Item>
            <InputItem
              placeholder={i18n.get('请输入8位企业邀请码申请加入企业')}
              value={this.state.code}
              onChange={this.handleChangeCode}
            />
          </List.Item>
        </List>
        <Button className="btn" type="primary" onClick={this.handleSubmit}>
          {i18n.get('加入企业')}
        </Button>
        <div className="toCreate" onClick={this.handleCreateEnterprise}>
          {i18n.get('我想创建一个企业')}
        </div>
      </div>
    )
  }
}
