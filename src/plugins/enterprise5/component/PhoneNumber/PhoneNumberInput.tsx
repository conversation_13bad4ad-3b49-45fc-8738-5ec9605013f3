import styles from './PhoneNumberInput.module.less'
import React, { useEffect, useState } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { InputItem } from 'antd-mobile'
import SVG_DOWN from '../../../account5/images/down.svg'

export interface PhoneNumberProps {
  phoneNumberChange: (phone: string) => void
  areaChange: (area: string) => void
  phone?: string
  area?: string
}

export const PhoneNumberInput: React.FunctionComponent<PhoneNumberProps> = props => {
  const { phoneNumberChange, areaChange, phone, area } = props

  const [areaCode, setAreaCode] = useState(area)
  const [phoneNumber, setPhoneNumber] = useState(phone)

  useEffect(() => {
    if (!areaCode) {
      updateAreaCode('86')
    }
  }, [area])

  const handlePhoneChange = (phone: string) => {
    setPhoneNumber(phone)
    phoneNumberChange(phone)
  }

  const updateAreaCode = (code: string) => {
    setAreaCode(code)
    areaChange(code)
  }

  const handleSelectTimeZone = () => {
    return api.open('@account5:SelectTimeZone').then((resp: string) => {
      updateAreaCode(resp)
    })
  }

  return (
    <div className={styles['phone-wrapper']}>
      <span onClick={handleSelectTimeZone} className="input-label">
        {'+' + areaCode}
        <img src={SVG_DOWN} />
      </span>
      <InputItem
        autoFocus
        maxLength={20}
        value={phoneNumber}
        onChange={handlePhoneChange}
        placeholder={i18n.get('请输入您的手机号')}
        type="number"
      />
    </div>
  )
}

export default PhoneNumberInput