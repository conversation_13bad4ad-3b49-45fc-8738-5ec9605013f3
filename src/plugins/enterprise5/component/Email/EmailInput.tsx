import React, { useState } from 'react'
import { InputItem } from 'antd-mobile'
import styles from './EmailInput.module.less'

export interface PhoneNumberProps {
  emailChange: (phone: string) => void
}

export const EmailInput: React.FunctionComponent<PhoneNumberProps> = props => {
  const { emailChange } = props

  const [email, setEmail] = useState('')

  const handleEmailChange = (email: string) => {
    setEmail(email)
    emailChange(email)
  }

  return (
    <div className={styles['email-wrapper']}>
      <InputItem
        autoFocus
        maxLength={50}
        value={email}
        onChange={handleEmailChange}
        placeholder={i18n.get('请输入您的邮箱')}
      />
    </div>
  )
}

export default EmailInput
