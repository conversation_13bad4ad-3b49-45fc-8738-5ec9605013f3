/**
 * Created by wq on 22/12/2017.
 */
import './empty-enterprise.less'
import React, { PureComponent } from 'react'
import { Button } from 'antd-mobile'
import <PERSON>hanceTitleHook from '../basic-elements/enhance-title-hook'
import { app as api } from '@ekuaibao/whispered'
import EMPTY_SVG from './images/empty.svg'

@EnhanceTitleHook(i18n.get('选择企业'))
export default class EmptyjoinEnterPrise extends PureComponent {
  constructor() {
    super()
    this.state = {
      code: ''
    }
  }

  handleCreateEnterprise = () => {
    api.go('/create-enterprise-start5')
  }

  render() {
    return (
      <div className="title-wrapper">
        <div className="title">{i18n.get('未发现企业')}</div>
        <div className="subtitle">{i18n.get('未发现任何企业，请创建企业吧！')}</div>
        <div className="pic">
          <img src={EMPTY_SVG} />
        </div>
        <Button className="btn" type="primary" onClick={this.handleCreateEnterprise}>
          {i18n.get('立即创建企业')}
        </Button>
      </div>
    )
  }
}
