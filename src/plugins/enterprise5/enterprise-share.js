/**
 * Created by wq on 23/12/2017.
 */
import './join-enterprise.less'
import React, { PureComponent } from 'react'
import { Button } from 'antd-mobile'
import { Fetch } from '@ekuaibao/fetch'
import { EnhanceConnect } from '@ekuaibao/store'
import { get } from 'lodash'
import Enhan<PERSON><PERSON>itleHook from '../basic-elements/enhance-title-hook'
import { app as api } from '@ekuaibao/whispered'
const actions = require('./join.action')

@EnhanceTitleHook(i18n.get('邀请员工'))
@EnhanceConnect(state => ({
  me_info: state['@common'].me_info
}))
export default class EnterpriseShareView extends PureComponent {
  constructor() {
    super()
    this.state = {
      inviteUrl: '',
      qrCode: ''
    }
  }

  componentWillMount() {
    this.handleGenerateLinks()
  }

  handleGenerateLinks = () => {
    api.dispatch(actions.generateInviteLinks()).then(resp => {
      let data = resp.value
      if (data) {
        this.setState({ inviteUrl: data.inviteUrl, qrCode: data.qrCode })
      }
    })
  }

  handleShare(type) {
    let name = get(this.props, 'me_info.staff.name')
    let { inviteUrl, qrCode } = this.state
    let data =
      'link' === type
        ? {
            url: inviteUrl,
            title: i18n.get(`{__k0}邀请您加入企业`, { __k0: name }),
            content: i18n.get('我们都在用易快报，快加入让报销成为快事一件吧！'),
            image: 'https://static.ekuaibao.com/ekb/hose_share.png'
          }
        : {
            image: qrCode
          }
    api.invokeService('@layout:share:ekuaibao', data)
  }
  render() {
    let { qrCode } = this.state
    return (
      <div className="create-enterprise-finished">
        <div className="content">
          <div className="msg1">{i18n.get('通过易快报扫码 邀请员工加入企业')}</div>
          {qrCode && (
            <div className="qrcode-div">
              <img
                src={'data:image/jpeg;base64,' + qrCode}
                style={{
                  width: 160,
                  height: 160,
                  marginTop: 50,
                  marginBottom: 50
                }}
              />
            </div>
          )}
        </div>
        <div className="share">
          <div className="button-layout">
            <Button onClick={this.handleShare.bind(this, 'code')} style={{ paddingRight: 20, paddingLeft: 20 }}>
              {i18n.get('分享邀请码')}
            </Button>
            <Button style={{ paddingRight: 20, paddingLeft: 20 }} onClick={this.handleShare.bind(this, 'link')}>
              {i18n.get('分享邀请链接')}
            </Button>
          </div>
        </div>
      </div>
    )
  }
}
