/**
 * Created by wq on 23/12/2017.
 */
import './join-enterprise.less'
import React, { PureComponent } from 'react'
import { Button } from 'antd-mobile'

import <PERSON>hanceTitleHook from '../basic-elements/enhance-title-hook'
// const HeaderImg = require('./images/recommendation.svg')

@EnhanceTitleHook(i18n.get('加入企业'))
export default class JoinEnterPriseWaitingView extends PureComponent {
  handleSubmit = () => {
    location.replace('https://a.app.qq.com/o/simple.jsp?pkgname=com.hose.ekuaibao')
  }
  render() {
    return (
      <div className="enterprise-view waiting-join-enterprise">
        <div className="title">{i18n.get("申请已发送")}</div>
        <div className="subtitle">{i18n.get("请等待管理员审核同意")}</div>
        <div className="header">
          {/*<img src={HeaderImg} />*/}
        </div>
        <Button className="btn" type="primary" onClick={this.handleSubmit}>
          {i18n.get("下载易快报App")}</Button>
      </div>
    )
  }
}
