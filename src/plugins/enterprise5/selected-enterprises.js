/**
 * Created by wq on 23/12/2017.
 */
import './selected-enterprises.less'
import React, { PureComponent } from 'react'
import { List, Radio } from 'antd-mobile'
import { EnhanceConnect } from '@ekuaibao/store'
import { Fetch } from '@ekuaibao/fetch'
import { getV } from '@ekuaibao/lib/lib/help'
import EnhanceTitleHook from '../basic-elements/enhance-title-hook'
import { app as api } from '@ekuaibao/whispered'
import * as actions from './join.action'
import { toast, SELECT_CORP_VERSION_MAP, CorpTabsData } from '../../lib/util'
import { loginAfterSelectCorp } from '../account5/utils/util'
import { Tabs, Button, Toast, Dialog } from '@hose/eui-mobile'
import { FilledGeneralRoom, OutlinedGeneralRoom, OutlinedTipsDone } from '@hose/eui-icons'
import { I18nCorpName } from '../../lib/util/corp-util'

const RadioItem = Radio.RadioItem

@EnhanceTitleHook(i18n.get('选择企业'))
@EnhanceConnect(state => ({
  list: state['@join-enterprise5'].corporationsList,
  externalCorpList: state['@join-enterprise5'].externalCorpList
}))
export default class SelectedEnterprises extends PureComponent {
  constructor() {
    super()
    this.state = {
      list: [],
      extList: [],
      checkedId: '',
      userId: '',
      selectCorp: '',
      onlyShowEmailPage: false,
      showTab: false,
      activeKey: CorpTabsData[0].key,
      currentList: [],
      corpTitle: ''
    }
  }

  componentWillMount() {
    Promise.all([
      api.invokeService('@account5:isOnlyShowEmailPage'),
      api.dispatch(actions.getInfo({ accessToken: Fetch.accessToken })),
      api.dispatch(actions.getCorporations())
    ]).then(result => {
      const [onlyShowEmailPage, res] = result
      Fetch.isAssistPlatform = res.value.isAssistPlatform
      this.setState({ userId: res.value.id, onlyShowEmailPage })
    })
  }

  readData = propData => {
    let corpTitle = CorpTabsData[0].label
    let activeKey = CorpTabsData[0].key
    let showTab = false
    let currentList = propData.list
    if (propData.list?.length > 0 && propData.externalCorpList?.length > 0) {
      showTab = true
      currentList = propData.list
    } else if (propData.externalCorpList?.length > 0) {
      currentList = propData.externalCorpList
      corpTitle = CorpTabsData[1].label
      activeKey = CorpTabsData[1].key
    }
    this.setState({ showTab, currentList, corpTitle, activeKey })
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.list !== this.props.list) {
      this.readData(nextProps)
    }
  }

  handleStart = async () => {
    if (!this.state.checkedId) {
      return toast.info(i18n.get('请先选择企业'))
    }
    const selectedCorporation = getV(this.state, 'selectCorp.corporation')
    const urlParams = new URLSearchParams(window.location.search)
    const isOem = urlParams.get('type') === 'oem'
    Fetch.GET('/api/oem/v1/corporation/oemCheck', {
      oem: isOem,
      selectedCorpId: this.state.checkedId,
      corpId: this.state.checkedId
    })
      .then(res => {
        if (res === false) {
          //没有权限进入企业
          Dialog.alert({
            iconType: 'warn',
            title: '企业受控',
            content: '该企业登录受控，请从第三方进入'
          })
        } else {
          return loginAfterSelectCorp({ corp: selectedCorporation, needMulAuth: true, tracePage: 'enterprisesPage' })
        }
      })
      .catch(() => {
        return loginAfterSelectCorp({ corp: selectedCorporation, needMulAuth: true, tracePage: 'enterprisesPage' })
      })
  }

  onChange = o => {
    const { activeKey } = this.state
    if (activeKey === 'ext') {
      Toast.show({ icon: 'fail', content: i18n.get('移动端暂不支持供应商协同门户功能，请在电脑端登录后进行操作。') })
      return
    }
    let checkedId = o.corporation.id
    if (this.state.checkedId === checkedId) return
    this.setState({ checkedId, selectCorp: o })
  }

  handleCreateEnterprise() {
    api.go('/create-enterprise-start5')
  }

  tabChange = activeKey => {
    const currentList = activeKey === CorpTabsData[0].key ? this.props.list : this.props.externalCorpList
    this.setState({ activeKey, currentList })
  }

  render() {
    let hasCorpVersion = false
    if (this.props.list && this.props.list.length > 0) {
      hasCorpVersion =
        this.props.list.filter(
          v =>
            v.corporation.sourceChannel === 'V1' ||
            v.corporation.sourceChannel === 'GROUP' ||
            v.corporation.sourceChannel === 'SHARED'
        ).length > 0
    }
    const corporationVersionKeyMap = SELECT_CORP_VERSION_MAP()

    const { currentList, showTab, activeKey, corpTitle } = this.state

    return (
      <div className="select-enterprises">
        {!showTab && corpTitle && (
          <div className="title-div">
            <div className="title">{corpTitle}</div>
          </div>
        )}
        {showTab && (
          <Tabs activeKey={activeKey} onChange={this.tabChange}>
            {CorpTabsData.map(item => {
              return <Tabs.Tab title={item.label} key={item.key} />
            })}
          </Tabs>
        )}
        <List className={showTab ? 'enterprises-list' : 'enterprises-list big'}>
          {currentList &&
            currentList.length > 0 &&
            currentList.map(o => {
              let type = o.corporation.sourceChannel
              if (IS_STANDALONE && type === 'V1') return null
              return (
                <div
                  key={o.corporation.id}
                  className={this.state.checkedId === o.corporation.id ? 'enterprises-list-checked' : ''}
                >
                  <RadioItem
                    key={o.corporation.id}
                    checked={this.state.checkedId === o.corporation.id}
                    onChange={() => this.onChange(o)}
                  >
                    <div className="corp-info-text">
                      <div>
                        {this.state.checkedId === o.corporation.id ? <FilledGeneralRoom /> : <OutlinedGeneralRoom />}
                      </div>
                      <div>{I18nCorpName(o.corporation)}</div>
                      {this.state.checkedId === o.corporation.id && (
                        <span className="checked-icon">
                          <OutlinedTipsDone />
                        </span>
                      )}
                    </div>
                    {hasCorpVersion && <div className="type">{corporationVersionKeyMap[type]}</div>}
                  </RadioItem>
                </div>
              )
            })}
        </List>
        <div className="footer-buttons">
          {!(window.PLATFORMINFO?.clusterURL && window.PLATFORMINFO?.loginEnv === 'MC') &&
            !this.state.onlyShowEmailPage &&
            !window.IS_ICBC && (
              <div className="create-enterprise">
                <Button
                  category="secondary"
                  size="large"
                  onClick={this.handleCreateEnterprise.bind(this)}
                  style={{
                    width: '160px',
                    display: 'inline-block',
                    margin: '0'
                  }}
                >
                  {i18n.get('创建企业')}
                </Button>
              </div>
            )}
          <div className="enter-enterprise">
            <Button
              category="primary"
              size="large"
              onClick={this.handleStart}
              disabled={!this.state.checkedId}
              style={{
                width: '160px',
                display: 'inline-block',
                margin: '0'
              }}
            >
              {i18n.get('进入企业')}
            </Button>
          </div>
        </div>
      </div>
    )
  }
}
