/**
 * Created by wq on 23/12/2017.
 */
import './join-enterprise.less'
import React, { PureComponent } from 'react'
import { Button } from 'antd-mobile'
import { Fetch } from '@ekuaibao/fetch'
import { EnhanceConnect } from '@ekuaibao/store'
import { get } from 'lodash'
import <PERSON>han<PERSON><PERSON><PERSON>leHook from '../basic-elements/enhance-title-hook'
import { app as api } from '@ekuaibao/whispered'
import * as actions from './join.action'
import { getBoolVariation } from '../../lib/featbit'

@EnhanceTitleHook(i18n.get('创建企业'))
@EnhanceConnect(state => ({
  me_info: state['@common'].me_info
}))
export default class CreateEnterpriseFinishedView extends PureComponent {
  constructor() {
    super()
    this.state = {
      inviteUrl: '',
      qrCode: ''
    }
  }

  componentWillMount() {
    if (!window.IS_ICBC) {
      this.handleGenerateLinks()
    }
  }

  handleGenerateLinks = () => {
    api.dispatch(actions.generateInviteLinks()).then(resp => {
      let data = resp.value
      if (data) {
        this.setState({ inviteUrl: data.inviteUrl, qrCode: data.qrCode })
      }
    })
  }

  handleShare = type => {
    let name = get(this.props, 'me_info.staff.name')
    let { inviteUrl, qrCode } = this.state
    let data =
      'link' === type
        ? {
            url: inviteUrl,
            title: i18n.get(`{__k0}邀请您加入企业`, { __k0: name }),
            content: i18n.get('我们都在用易快报，快加入让报销成为快事一件吧！'),
            image: 'https://static.ekuaibao.com/ekb/hose_share.png'
          }
        : {
            image: qrCode
          }
    api.invokeService('@layout:share:ekuaibao', data)
  }

  handleStart = () => {
    if (getBoolVariation('haf-22-idp_login', false) && api.sdk?.switchCorporation) {
      const staff = api.getState()['@common'].me_info?.staff
      return api.sdk.switchCorporation({ id: Fetch.ekbCorpId }, staff.id)
    }
    const params = Fetch.makeUrlParams({ corpId: Fetch.ekbCorpId })
    api.invokeService('@common:getStaffSetting').then(data => {
      localStorage.setItem('staffSettingConfig', JSON.stringify(data.value))
      if (data.value.mobileLayout === 'NEWPAGE') {
        window.isNewHome = true
        location.replace('?' + params + '#/home5')
      } else if (data.value.mobileLayout === 'GUIDEPAGE') {
        window.isNewHome = true
        location.replace('?' + params + '#/home5-guide')
      } else {
        window.isNewHome = false
        location.replace('?' + params + '#/home')
      }
    })
  }

  render() {
    let { qrCode } = this.state
    return (
      <div className="create-enterprise-finished">
        <div className="content">
          <div className="msg">{i18n.get('恭喜您，创建成功')}</div>
          <Button
            className="btn btnc"
            type="primary"
            style={{ marginLeft: 20, marginRight: 20 }}
            onClick={this.handleStart}
          >
            {i18n.get('开始使用')}
          </Button>
          {qrCode && (
            <div className="qrcode-div">
              <img
                src={'data:image/jpeg;base64,' + qrCode}
                style={{ width: 160, height: 160, marginTop: 20, marginBottom: 20 }}
              />
            </div>
          )}
        </div>
        {!window.IS_ICBC && (
          <div className="share">
            <div className="txt">{i18n.get('点击分享链接或二维码，邀请其他成员加入企业')}</div>
            <div className="button-layout">
              <Button
                className="btn"
                onClick={this.handleShare.bind(this, 'code')}
                style={{ paddingRight: 10, paddingLeft: 10 }}
              >
                {i18n.get('分享邀请码')}
              </Button>
              <Button
                className="btn"
                style={{ paddingRight: 10, paddingLeft: 10 }}
                onClick={this.handleShare.bind(this, 'link')}
              >
                {i18n.get('分享邀请链接')}
              </Button>
            </div>
          </div>
        )}
      </div>
    )
  }
}
