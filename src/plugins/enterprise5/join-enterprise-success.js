/**
 * Created by wq on 22/12/2017.
 */
import './join-enterprise.less'
import React, { PureComponent } from 'react'
import { Button } from 'antd-mobile'
import <PERSON>hanceTitleHook from '../basic-elements/enhance-title-hook'
import { app as api } from '@ekuaibao/whispered'
const HeaderImg = require('./images/header.svg')

@EnhanceTitleHook(i18n.get('加入企业'))
export default class JoinEnterPriseView extends PureComponent {
  handleSubmit = () => {
    api.go('/')
  }

  render() {
    return (
      <div className="enterprise-view join-enterprise-view">
        <div className="header">
          <img src={HeaderImg} />
        </div>
        <div className="flex txt-w">{i18n.get("恭喜您，成功加入企业")}</div>
        <div className="flex enterprise">{i18n.get("北京合思信息技术有限公司")}</div>
        <Button className="btn" type="primary" onClick={this.handleSubmit}>
          {i18n.get("开始使用")}</Button>
      </div>
    )
  }
}
