.switch-enterprise{
  height: 100%;
  position: relative;
  overflow-x: hidden;
  overflow-y: hidden;

  .switch-inner-content{
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    &.switch-has-tab-list{
      height: calc(100% - 0.84rem) !important;
    }
  }

  .switch-content{
    width: 100%;
    height: calc(100% - 1.28rem);
    &.big{
      height: 100% !important;
    }
    .switch-content-search{
      width: 100%;
      padding: 0.16rem 0.32rem;
      background-color: var(--eui-bg-body);
    }

    .switch-has-search-list{
      padding-top: 0 !important;
      height: calc(100% - 1.04rem) !important;
    }

    .switch-content-list{
      width: 100%;
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
      padding-top: 0.16rem;

      .eui-error-block{
        width: 100%;
        height: 100%;
        background-color: var(--eui-bg-body);
        .eui-error-block-image{
          width: 3.6rem;
          height: 3.6rem;
          span{
            font-size: 3.6rem;
          }
        }
      }



      .switch-corp-item{
        width: 100%;
        padding: 0.24rem 0.32rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        gap: 0.32rem;
        background-color: var(--eui-bg-body);

        &.active{
          >div:first-child>div:first-child{
            color: var(--eui-primary-pri-500) !important;
            font-weight: 500 !important;
          }
          >div:last-child{
            >*{
              color: var(--eui-primary-pri-500) !important;
            }
          }
        }

        >div:first-child{
          flex: 1;
          >div:first-child{
            color: var(--eui-text-title);
            font-weight: 400;
            font-size: 0.32rem;
            line-height: 0.48rem;
          }
          >div:last-child{
            color: var(--eui-text-placeholder);
            font-size: 0.28rem;
            line-height: 0.4rem;
          }
        }

        >div:last-child{
          >*{
            font-size: 0.4rem;
          }
        }

        &:after{
          content: ' ';
          position: absolute;
          width: 100%;
          height: 0.02rem;
          bottom: 0;
          left: 0.32rem;
          background-color: var(--eui-line-divider-module);
        }
      }

    }
  }
  .switch-footer{
    width: 100%;
    height: 1.28rem;
    padding: 0.2rem 0.32rem;
    background-color: var(--eui-bg-body);
    position: fixed;
    bottom: 0;
    z-index: 10;
    filter: drop-shadow(0px -1px 2px rgba(29, 33, 41, 0.02)) drop-shadow(0px -2px 4px rgba(29, 33, 41, 0.02)) drop-shadow(0px -2px 8px rgba(29, 33, 41, 0.02));
  }
}
