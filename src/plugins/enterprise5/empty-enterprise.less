@px6: .12rem;
@px14: .28rem;
@px24: .48rem;
@px40: .8rem;
@px128: 2.56rem;

.title-wrapper {
  padding: @px40;
  display: flex;
  flex-direction: column;

  .title {
    font-size: @px24;
    font-weight: bold;
  }

  .subtitle {
    margin-top: @px6;
    font-size: @px14;
  }

  .btn {
    width: 50%;
  }
  .pic {
    margin-top: @px40;
    margin-bottom: @px40;
    height: @px128;
    text-align: center;

    img {
      height: 100%;
    }
  }
}
