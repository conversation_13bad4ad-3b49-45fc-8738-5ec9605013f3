/**
 * Created by wq on 23/12/2017.
 */
import './join-enterprise.less'
import React, { PureComponent } from 'react'
import { Button, ActivityIndicator } from 'antd-mobile'
import EnhanceTitleHook from '../basic-elements/enhance-title-hook'
import { Store } from '@ekuaibao/i18n'
import { app as api } from '@ekuaibao/whispered'
import { toast, checkPhoneFormat, checkEmailFormat } from '../../lib/util'
import { session } from '@ekuaibao/session-info'
import commonActions from '../common/common.action'
import brandData from '../../i18n/brand'
import { getScope } from '../../i18n/i18nUtils'
import PhoneNumberInput from './component/PhoneNumber/PhoneNumberInput'
import { get } from 'lodash'
import EmailInput from './component/Email/EmailInput'
import { GET } from '@ekuaibao/fetch'

const HomeDelivery = require('./images/home-delivery.png')

@EnhanceTitleHook(i18n.get('加入企业'))
export default class InvitedJoinEnterpise extends PureComponent {
  state = {
    inviteInfo: null,
    loading: false,
    phone: '',
    area: '',
    email: '',
    accountType: 'phone',
    logged: false
  }

  handleSubmit = async () => {
    const { params } = this.props
    if (!params) return
    const code = get(this, 'props.params.code')
    const { accountType } = this.state
    try {
      let data
      if (accountType === 'phone') {
        data = await this.usePhone()
      } else {
        data = await this.useEmail()
      }
      if (data.value === true) {
        // 已注册
        toast.info(i18n.get('该账号已注册请先登录'), 1000, () => {
          api.go(`/login-invite5/${code}`)
        })
      } else if (data.value === false) {
        // 未注册
        api.go(`/register-invite5/${code}`)
      }
    } catch (e) {
      toast.info(e.msg || e.message)
    }
  }

  usePhone = async () => {
    const { area, phone } = this.state
    if (!checkPhoneFormat(area, phone)) {
      return Promise.reject()
    }
    const result = await GET(`/api/v1/account/users/phone/$${area}-${phone}/exists`)
    session.set('phone', {
      phone: phone,
      areaCode: area
    })
    return result
  }

  useEmail = async () => {
    const { email } = this.state
    if (!checkEmailFormat(email)) {
      return Promise.reject()
    }
    const result = await GET(`/api/v1/account/users/email/$${email}/exists`)
    session.get('email', email)
    return result
  }

  componentWillMount() {
    const code = this.props.params.code
    fetch('/api/app/organization/staffs/invite/$' + code)
      .then(data => {
        return data.status === 200 ? data.json() : Promise.reject(data)
      })
      .then(({ value }) => {
        const { corpWordsReplaceItems } = value
        if (corpWordsReplaceItems && corpWordsReplaceItems.length) {
          const store = Store.getInstance()
          const scope = getScope()
          store.changeScope(scope)
          const currentLocale = store.currentLocale
          const currentScope = store.currentScope
          const corpNatureObj = {}
          corpWordsReplaceItems.forEach(item => {
            corpNatureObj[`__global_${item.variable}`] = item.replaceKey
          })
          // @ts-ignore
          const currentScopeVariable = brandData[currentLocale][currentScope]
          // @ts-ignore
          brandData[currentLocale][currentScope] = {
            ...currentScopeVariable,
            ...corpNatureObj
          }
          store.addScopeVariables(brandData)
        }
        this.setState({ inviteInfo: value }, () => api.invokeService('@layout:set:header:title', i18n.get('加入企业')))
        session.set('inviteInfo', value)
      })
      .catch(err => {
        location.replace('app.html#/login5')
      })

    if (session.get('user')) {
      this.setState({ logged: true })
      api.dispatch(commonActions.getMeInfo())
    }
  }

  accountTypeChange = () => {
    const type = this.state.accountType === 'phone' ? 'email' : 'phone'
    this.setState({ accountType: type })
  }

  emailChange = email => {
    this.setState({ email })
  }

  phoneChange = phone => {
    this.setState({ phone })
  }

  areaChange = area => {
    this.setState({ area })
  }

  renderUnlogged = () => {
    const { accountType } = this.state
    return (
      <div className="unlogged-content">
        <div className="account-type-change-wrapper">
          {i18n.get('切换为')}
          <span className="account-type-change" onClick={this.accountTypeChange}>
            {accountType === 'phone' ? i18n.get('邮箱账号') : i18n.get('手机账号')}
          </span>
        </div>
        {accountType === 'phone' ? (
          <PhoneNumberInput phoneNumberChange={this.phoneChange} areaChange={this.areaChange} />
        ) : (
          <EmailInput emailChange={this.emailChange} />
        )}
        <Button className="btn" type="primary" onClick={this.handleSubmit} style={{ width: '160px' }}>
          {i18n.get('加入企业')}
        </Button>
      </div>
    )
  }

  render() {
    const { inviteInfo } = this.state
    let loading = this.state.loading
    if (inviteInfo == null) loading = true
    let content
    if (loading) {
      return (
        <div>
          <ActivityIndicator text={i18n.get('正在加载数据，请稍等...')} animating={loading} toast />
        </div>
      )
    } else {
      content = this.renderUnlogged()
      return (
        <div className="enterprise-view invited-join-enterprise">
          <div className="content">
            <div className="title">
              <span className="name">{inviteInfo.inviteStaffName}</span> {i18n.get('邀请你加入')}
            </div>
            <div className="company">{inviteInfo.corpName}</div>
            <div className="img">
              <img src={HomeDelivery} width="109px" height="109px" />
            </div>
          </div>
          {content}
        </div>
      )
    }
  }
}
