@import '../../../styles/layout.less';
.payee-info-wrapper {
  .cancel{
    position: absolute;
    bottom: 40px;
    width: inherit;
  }
  width: 100%;
  height: 100%;
  background-color: @white;
  :global {
    .item-wrapper {
      text-align: left;
      margin: 32px 32px 0 32px;
      border-radius: 3px;
      background-color: @white;
      border: solid 2px #f5f5f5;

      .tp {
        background-color: @white;
        display: flex;
        align-items: center;
        height: 80px;
        flex-direction: row;
        border-bottom: 2px solid #f3f3f3;
        margin: 0 16px 0 16px;

        .name {
          font-weight: 500;
          .fix-ellipsis();
          flex: 1;
          font-size: 28px;
          color: #54595b;
        }
      }
      .item-w {
        display: flex;
        flex-direction: column;
        background-color: @white;
        padding: 16px;
        .default {
          font-size: 26px;
          font-weight: 500;
          margin-top: 14px;
          line-height: 1;
          color: @gray-6;
          margin-left: 10px;
        }
        .payee-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          .l1 {
            margin-top: 15px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .bank-img {
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: row;
              border-radius: 2px;
              background-color: #f9f9f9;
              border: 2px solid #eee;
              padding: 0 16px;

              .payee-icon {
                height: 40px;
                width: 40px;
                flex-shrink: 0;
              }
              span {
                font-size: 26px;
                color: #3a3f3f;
                margin-left: 5px;
              }
            }
          }
          .md {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            height: 40px;
            div:first-child {
              .fix-ellipsis();
              position: absolute;
              left: 0;
              right: 0;
              font-size: 32px;
              color: #3a3f3f;
            }
          }
          .bm {
            margin-top: 15px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            height: 42px;
            color: @gray-7;

            div:first-child {
              .fix-ellipsis();
              position: absolute;
              left: 0;
              right: 0;
            }
          }
        }
        .card-bottom-wrapper {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          color: @gray-7;
        }
        .card-right-wrapper {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
        }
      }
      .fix-swipe();
    }

    .item-wrapper:last-child {
      margin-bottom: 32px;
    }

    .list-wrapper {
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      background-color: @white;
      height:100%;
      padding-top: 86px;
      padding-bottom: 180px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;

      .item-wrapper {
        text-align: left;
        margin: 32px 32px 0 32px;
        border-radius: 3px;
        background-color: @white;
        border: solid 2px #f5f5f5;

        .tp {
          background-color: @white;
          display: flex;
          align-items: center;
          height: 80px;
          flex-direction: row;
          border-bottom: 2px solid #f3f3f3;
          margin: 0 16px 0 16px;

          .name {
            font-weight: 500;
            .fix-ellipsis();
            flex: 1;
            font-size: 28px;
            color: #54595b;
          }
        }
        .item-w {
          display: flex;
          flex-direction: column;
          background-color: @white;
          padding: 16px;
          .default {
            font-size: 26px;
            font-weight: 500;
            margin-top: 14px;
            line-height: 1;
            color: @gray-6;
            margin-left: 10px;
          }
          .payee-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            .l1 {
              margin-top: 15px;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              .bank-img {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: row;
                border-radius: 2px;
                background-color: #f9f9f9;
                border: 2px solid #eee;
                padding: 0 16px;

                .payee-icon {
                  height: 40px;
                  width: 40px;
                  flex-shrink: 0;
                }
                span {
                  font-size: 26px;
                  color: #3a3f3f;
                  margin-left: 5px;
                }
              }
            }
            .md {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              position: relative;
              height: 40px;
              div:first-child {
                .fix-ellipsis();
                position: absolute;
                left: 0;
                right: 0;
                font-size: 32px;
                color: #3a3f3f;
              }
            }
            .bm {
              margin-top: 15px;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              position: relative;
              height: 42px;
              color: @gray-7;

              div:first-child {
                .fix-ellipsis();
                position: absolute;
                left: 0;
                right: 0;
              }
            }
          }
          .card-bottom-wrapper {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            color: @gray-7;
          }
          .card-right-wrapper {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
          }
        }
        .fix-swipe();
      }

      .item-wrapper:last-child {
        margin-bottom: 32px;
      }

      .wallet-wrapper {
        .wallet-img {
          display: flex;
          align-items: center;
          padding: 32px;
          background-color: @white;
          img {
            width: 42px;
            height: 42px;
          }
          span {
            flex: 1;
            padding-left: 10px;
            color: #54595b;
            font-size: 32px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .card-right-wrapper {
            font-size: 26px;
            color: #bbbdbd;
          }
        }
      }
    }
  }
}
