import { app } from '@ekuaibao/whispered'
import styles from './home5-message.module.less'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
const EKBIcon = app.require('@elements/ekbIcon')
const RedDot = app.require('@elements/ekb-badge')
export interface Props {
  total: number
  standardTrack?: (key: string, options: any) => void
}

const HomeMessage: React.FC<Props> = props => {
  const handleClick = () => {
    props?.standardTrack?.('messageCenterIconIcon', {
      current_url: '/message-center'
    })
    api.go('/message-center', false)
  }
  return (
    <div className={styles['home5Title-btn']} onClick={handleClick}>
      <EKBIcon name="#EDico-notifications" />
      <RedDot isCorner={true} text={props.total || 0} />
    </div>
  )
}

export default EnhanceConnect(state => ({
  total: state['@message-center'].messageTotal,
}))(HomeMessage)