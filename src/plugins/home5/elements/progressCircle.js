import React from 'react'
import styles from './progress-circle.module.less'
class ProgressCircleElement extends React.Component {
  render() {
    let { prefixCls, strokeLinecap, strokeWidth, percentageInfo, style, className, ...restProps } = this.props
    strokeWidth = strokeWidth ?? 4
    const radius = 50 - strokeWidth / 2
    const pathString = `M 50,50 m 0,-${radius}
     a ${radius},${radius} 0 1 1 0,${2 * radius}
     a ${radius},${radius} 0 1 1 0,-${2 * radius},Z`
    const len = Math.PI * 2 * radius
    let pathStyles = []
    let totalPercentage = 0
    percentageInfo.percents.map((item, index) => {
      let path = {}
      path.id = item.id ?? ''
      path.stroke = item.color
      totalPercentage += item.percent
      path.strokeWidth = strokeWidth
      path.pathStyle = {
        strokeDasharray: `${len} ${len}`,
        strokeDashoffset: `${((100 - item.percent * 100) / 100) * len}`,
        transition: 'stroke-dashoffset 0.3s ease 0s, stroke 0.3s ease'
      }
      pathStyles.push(path)
    })
    if (pathStyles.length >= 1) {
      let firstItem = pathStyles[0]
      firstItem.pathStyle = {
        strokeDasharray: `${len} ${len}`,
        strokeDashoffset: `${((100 - totalPercentage * 100) / 100) * len}`,
        transition: 'stroke-dashoffset 0.3s ease 0s, stroke 0.3s ease'
      }
      pathStyles[0] = firstItem
    }

    return (
      <div className={styles['progress-circle-wrapper']}>
        <svg className={`${prefixCls}-circle ${className}`} viewBox="0 0 100 100" style={style} {...restProps}>
          <path
            className={`${prefixCls}-circle-trail`}
            d={pathString}
            stroke="#eeeeee"
            strokeWidth={strokeWidth}
            fillOpacity="0"
          />
          {pathStyles.map((path, index) => {
            return (
              <path
                key={index}
                className={`${prefixCls}-circle-path`}
                d={pathString}
                strokeLinecap="round"
                stroke={path.stroke}
                strokeWidth={path.strokeWidth}
                fillOpacity="0"
                ref="path"
                style={path.pathStyle}
              />
            )
          })}
        </svg>
        <div className="progress-circle-info">
          {this.props.children}
        </div>
      </div>
    )
  }
}

export default ProgressCircleElement
