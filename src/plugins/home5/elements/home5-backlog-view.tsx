import { app } from '@ekuaibao/whispered'
/**
 * Created by LinK on 2019/3/1.
 */

// @ts-ignore
import styles from './home5-backlog-view.module.less'
// import EKBIcon from '../../../elements/ekbIcon'
const EKBIcon = app.require('@elements/ekbIcon')
import React from 'react'
import { VERY_HEAVY_WEIGHT, cardMap } from '../util/card_map'
import { getCardLabel } from '../homePage/HomeCardManage/utils/helps'

export interface Props {
  list: any[]
  fnAddCard: Function
}

export default function Home5BacklogView(props: Props) {
  const { list = [], fnAddCard } = props

  if (!list.length)
    return (
      <ul className={styles['home5BacklogView-wrap']}>
        <div className="homeEditableView-blank">
          <p>{i18n.get('无可添加的卡片')}</p>
        </div>
      </ul>
    )

  return (
    <ul className={styles['home5BacklogView-wrap']}>
      {list
        // 前端做写死的排序，注释掉后可解除
        .sort(
          (a, b) =>
            (cardMap(a.id).shadowWeight || VERY_HEAVY_WEIGHT) - (cardMap(b.id).shadowWeight || VERY_HEAVY_WEIGHT)
        )
        .map((value, index) => (
          <li key={index} className="home5BacklogView-item">
            <div className="home5BacklogView-item-action-wrap" onClick={_ => fnAddCard(value)}>
              <EKBIcon name="#EDico-plus-circle" />
            </div>
            <div className="home5BacklogView-item-content-wrap">
              <span>{cardMap(value.id).label || getCardLabel(value)}</span>
            </div>
          </li>
        ))}
    </ul>
  )
}
