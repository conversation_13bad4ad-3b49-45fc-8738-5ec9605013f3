import React, { Component } from 'react'
import Enhance<PERSON>it<PERSON>Hook from '../../basic-elements/enhance-title-hook'
import { Button } from '@hose/eui-mobile'
import styles from './home5-preview-setting.module.less'
import { connect } from '@ekuaibao/mobx-store'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import { IHomeTemplateVersion } from '../homePage/HomeCardManage/Types'
import { MeIF } from '@ekuaibao/ekuaibao_types'
import { EnhanceConnect } from '@ekuaibao/store'
import { T } from '@ekuaibao/i18n'
import HomeCardManage from '../homePage/HomeCardManage'
const EKBIcon = api.require<any>('@elements/ekbIcon')
const HomeBanner = api.require<any>('@elements/HomeBanner/HomeBanner')
// @ts-ignore
import classnames from 'classnames'
import { toast } from '../../../lib/util'
interface IProps {
  homePageVersion: IHomeTemplateVersion
  me_info: MeIF
  isNewMall: boolean
  home5BannerData: any[]
  groupedPreviewCardList: any[]
}
interface IState {
  loading: boolean
  showContent: string
}
// @ts-ignore
@connect(store => ({
  homePageVersion: store.states['@homePage']?.hasNewHomeTemplateVersion,
  home5BannerData: store.states['@home5']?.home5BannerData || [],
  groupedPreviewCardList: store.states['@homePage']?.groupedPreviewCardList
}))
@((EnhanceConnect as any)((state: any) => ({
  me_info: state['@common'].me_info || {},
  isNewMall: state['@common'].powers.newMall
})))
// @ts-ignore
@EnhanceTitleHook(i18n.get('管理员模版'))
export default class HomePreviewSetting extends Component<IProps, IState> {
  constructor(props: IProps) {
    super(props)
    this.state = {
      loading: false,
      showContent: 'showVersion'
    }
  }
  handleUseTemplate = (showContent: string) => {
    this.setState({
      loading: true
    })
    const { homePageVersion } = this.props
    api.store.dispatch('@homePage/useHomePageVersion')(
      homePageVersion?.homePageTemplate?.id,
      homePageVersion?.homePageTemplateVersionId,
      () => {
        this.setState({
          loading: false,
          showContent
        })
        toast.success(i18n.get('启用成功'))
      }
    )
  }
  handleShowTemplate = () => {
    this.setState({
      showContent: 'showTemplate'
    })
  }
  render() {
    const { homePageVersion, me_info, isNewMall, home5BannerData, groupedPreviewCardList } = this.props
    const { loading, showContent } = this.state
    return (
      <>
        {showContent === 'showVersion' ? (
          <div className={styles.homePreviewSetting}>
            <div className={styles.pageVersion}>
              <div className={styles.icon}>
                <EKBIcon name="#EDico-mobanzhuangshi" />
              </div>
              <div className={styles.name}>{homePageVersion?.templateName}</div>
              <div className={styles.time}>
                {i18n.get('下发时间')}: {moment(homePageVersion?.publishedTime).format('YYYY-MM-DD HH:mm')}
              </div>
              {!homePageVersion?.isHaveNewVersion && <div className={styles.activeStatus}>{i18n.get('使用中')}</div>}
            </div>
            {homePageVersion?.isHaveNewVersion && (
              <div className={styles['btn-wrap']}>
                <Button
                  disabled={loading}
                  className={styles.btn}
                  block
                  category="secondary"
                  onClick={this.handleShowTemplate}
                >
                  <T name={i18n.get('预览')} />
                </Button>
                <Button
                  disabled={loading}
                  loading={loading}
                  loadingText={i18n.get('启用中')}
                  className={styles.btn}
                  block
                  onClick={() => this.handleUseTemplate('showVersion')}
                >
                  <T name={i18n.get('启用模板')} />
                </Button>
              </div>
            )}
          </div>
        ) : (
          <>
            <div className={styles['home-page-wrapper']}>
              <HomeBanner className={`home-page-banner-container`} showDots={true} me_info={me_info} />
              <div className={classnames('home-page-card-container')}>
                <HomeCardManage isNewMall={isNewMall} me_info={me_info} groupedCardList={groupedPreviewCardList} />
              </div>
            </div>
            <div className={styles['btn-wraps']}>
              <Button
                disabled={loading}
                loading={loading}
                loadingText={i18n.get('启用中')}
                className={styles.btn}
                block
                onClick={() => this.handleUseTemplate('showVersion')}
              >
                <T name={i18n.get('启用模板')} />
              </Button>
            </div>
          </>
        )}
      </>
    )
  }
}
