@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.home5BacklogView-wrap {
  //padding: 0 0 @space-7;
  //margin: 0 0 @space-4;
  list-style: none;
  :global {
    .home5BacklogView-item {
      list-style: none;
      height: 112px;
      display: flex;
      flex-direction: row;
      align-items: center;
      background-color: @color-white-1;
      .home5BacklogView-item-action-wrap {
        flex-shrink: 0;
        width: 112px;
        display: flex;
        align-items: center;
        justify-content: center;
        .icon {
          width: 48px;
          height: 48px;
          color: @color-brand-2;
        }
      }
      .home5BacklogView-item-content-wrap {
        flex: 1;
        height: 100%;
        padding-right: @space-6;
        display: flex;
        user-select: none;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-bottom: 2px solid rgba(39, 46, 59, 0.08);
        min-width: 0;
        span {
          user-select: none;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-weight: 500;
          font-size: 28px;
          line-height: 40px;
          color: #272e3b;
        }
      }
    }
  }
}
