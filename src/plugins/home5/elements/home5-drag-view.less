@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.homeEditableView-sortable-wrap {
  padding: 0;
  //margin: 0 0 16px;
  list-style: none;
  .homeEditableView-sortable-item {
    box-shadow: none;
  }
}

.homeEditableView-blank {
  height: 216px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: @color-white-1;
  p {
    .font-size-2;
    color: @color-black-4;
  }
}

.homeEditableView-sortable-item {
  .shadow-black-4;
  list-style: none;
  height: 128px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: @color-white-1;
  .homeEditableView-sortable-item-action-wrap {
    flex-shrink: 0;
    width: 112px;
    text-align: center;
    .icon {
      width: 48px;
      height: 48px;
      color: #f4664a;
    }
  }
  .homeEditableView-sortable-item-content-wrap {
    flex: 1;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid rgba(39, 46, 59, 0.08);
    min-width: 0;
    .homeEditableView-sortable-item-content-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      min-width: 0;
      padding-right: @space-6;
      span {
        user-select: none;
        color: @color-black-3;
        .font-size-1;
      }
      .homeEditableView-sortable-item-content-left-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        max-width: 100%;
        span {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-weight: 500;
          font-size: 28px;
          line-height: 40px;
          color: #272e3b;
        }
        div {
          user-select: none;
          display: inline-block;
          height: 40px;
          text-align: center;
          margin-left: @space-4;
          width: 82px;
          background: rgba(137, 137, 137, 0.1);
          border-radius: 8px;
          font-weight: 400;
          font-size: 24px;
          color: rgba(39, 46, 59, 0.56);
        }
      }
      .homeEditableView-sortable-item-content-desc {
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 300px;
        font-weight: 400;
        font-size: 24px;
        line-height: 44px;
        color: rgba(39, 46, 59, 0.56);
      }
    }
    .homeEditableView-sortable-item-content-right {
      height: 100%;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      .homeEditableView-icon-wrap {
        height: 100%;
        display: flex;
        align-items: center;
        padding: 0 @space-6;
        margin-left: @space-2;
      }
      .icon {
        width: 42px;
        .icon-size-5;
        color: @color-black-4;
      }
    }
  }
}
