import React from 'react'
export function LinearGradientComponent(props: any) {
  const { className, id, stopColorMap = {}, position = {} } = props
  const { x1 = 1, y1 = 0, x2 = 0, y2 = 0 } = position
  return (
    <svg className={className}>
      <defs>
        <linearGradient id={id} x1={x1} y1={y1} x2={x2} y2={y2}>
          {Object.keys(stopColorMap)?.map((item, key) => {
            return <stop key={key} offset={item} stopColor={stopColorMap[item]}></stop>
          }) ?? null}
        </linearGradient>
      </defs>
    </svg>
  )
}