import React, { FC, useEffect, useState } from 'react'
import styles from './PayTypeList.module.less'
import PayTypeItem, { PayTypeItemInfo } from './PayTypeItem'
import { Button, Space, Toast } from '@hose/eui-mobile'

interface IProps {
  onChange: (key: string) => void
  list: PayTypeItemInfo[]
  defaultKey?: string
}

const PayTypeList: FC<IProps> = props => {
  const { onChange, list, defaultKey = '' } = props
  const [checkedItem, setCheckedItem] = useState(defaultKey)
  const onCheckboxChange = (key: string) => {
    setCheckedItem(key)
  }

  useEffect(() => {
    setCheckedItem(defaultKey)
  }, [defaultKey])
  const handleOnClick = () => {
    if (!checkedItem) {
      Toast.show({
        icon: 'fail',
        content: '请选择支付方式'
      })
      return
    }
    onChange(checkedItem)
  }
  return (
    <div className={styles['pay-type-list-wrapper']}>
      {list.map(item => {
        return <PayTypeItem onChange={onCheckboxChange} checked={checkedItem === item.key} data={item} />
      })}
      <Button className="bottom-button" size="large" block onClick={handleOnClick}>
        {i18n.get('确定')}
      </Button>
    </div>
  )
}

export default PayTypeList
