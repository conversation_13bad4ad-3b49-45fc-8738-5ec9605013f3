import React, { FC } from 'react'
import { OutlinedDirectionRight, FilledTipsClose } from '@hose/eui-icons'

interface IProps {
  onClick: () => void
  placeholder: string
  value?: { name?: string } | any
  isFeetype?: boolean
  onCancelClick?: () => void
}

const CustomWrapperWidget: FC<IProps> = props => {
  const { onClick, placeholder, value, isFeetype, onCancelClick } = props
  const v = value?.name || value
  return (
    <div className="from-content" onClick={onClick}>
      <div className="horizontal">
        {isFeetype && v ? (
          <img
            style={{ backgroundColor: value.color, width: 20, height: 20, borderRadius: '50%', marginRight: 8 }}
            src={value.icon}
          />
        ) : null}
        <div className={v ? 'from-content-vale' : 'from-content-label'}>{i18n.get(v || placeholder)}</div>
      </div>
      <div style={{ display: 'flex' }}>
        {onCancelClick && v && (
          <FilledTipsClose
            fontSize={20}
            color={'var(--eui-icon-n2)'}
            onClick={e => {
              e?.stopPropagation?.()
              e?.preventDefault?.()
              onCancelClick()
            }}
          />
        )}
        <OutlinedDirectionRight fontSize={20} color={'var(--eui-icon-n2)'} />
      </div>
    </div>
  )
}

export default CustomWrapperWidget
