.pay-type-item-wrapper {
  display: flex;
  flex-direction: row;
  border-radius: 16px;
  align-items: center;
  border: 2px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.1));
  justify-content: space-between;
  margin-top: 24px;
  padding: 32px;
  :global {
    .pay-type-item-left {
      display: flex;
      align-items: center;
      flex-direction: row;
      .pay-type-item-icon {
        width: 64px;
        height: 64px;
        margin-right: 16px;
      }
      .pay-type-item-title {
        color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
        font: var(--eui-font-head-b1);
      }
    }
  }
}
