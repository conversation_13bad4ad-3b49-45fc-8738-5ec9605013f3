import React, { FC, useMemo, useState } from 'react'
import CustomWrapperWidget from './CustomWrapperWidget'
import { Calendar } from 'antd-mobile'
import moment from 'moment'

interface IProps {
  type: 'one' | 'range'
  onChange?: (val: any) => void
  value?: [Date, Date] | Date
  placeholder: string
}

const SelectDateWidget: FC<IProps> = props => {
  const { value, type, placeholder, onChange } = props
  const [visible, setVisible] = useState(false)

  const handleSelectDate = () => {
    setVisible(true)
  }

  const showDate = useMemo(() => {
    if (type === 'one') {
      // @ts-ignore
      return value ? moment(value[0]).format('YYYY-MM-DD') : ''
    } else {
      // @ts-ignore
      return value ? `${moment(value[0]).format('YYYY-MM-DD')} - ${moment(value[1]).format('YYYY-MM-DD')}` : ''
    }
  }, [value])

  return (
    <>
      <CustomWrapperWidget value={showDate} onClick={handleSelectDate} placeholder={placeholder} />
      <Calendar
        visible={visible}
        type={type}
        onCancel={() => {
          setVisible(false)
        }}
        onConfirm={(startTime, endTime) => {
          if (type === 'one') {
            onChange?.([startTime])
          } else if (type === 'range') {
            onChange?.([startTime, endTime])
          }
          setVisible(false)
        }}
        onSelectHasDisableDate={(dates: Date[]) => {
          console.warn('onSelectHasDisableDate', dates)
        }}
        // @ts-ignore
        defaultValue={value}
        defaultDate={new Date(+new Date() - 62 * 24 * 3600 * 1000)}
      />
    </>
  )
}

export default SelectDateWidget
