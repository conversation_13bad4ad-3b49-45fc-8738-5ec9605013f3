import React, { useEffect } from 'react'
import { Form, Input } from '@hose/eui-mobile'
import { app } from '@ekuaibao/whispered'
import FeeTypeInfo from '../../../feetype/layers/FeeTypeInfoModal'
import CustomWrapperWidget from './CustomWrapperWidget'
import { RequisitionType } from '../../type'
import SelectDateWidget from './SelectDateWidget'

const hideFields = ['money', 'invoice', 'apportions', 'amortizes', 'flowLinks']
const FormItem = Form.Item
const ECardFeeTypeInfo = (props: any) => {
  const {
    form,
    bus,
    setFormBus,
    feeTypeValue,
    me_info,
    feeTypes,
    updateCurrentFeeType,
    supplementData,
    feeIncField,
    selectedRequisition,
    requisitionList,
    setSelectedRequisition,
    isFeetypeloaded,
    handleSelectFeeType
  } = props
  useEffect(() => {
    app.dataLoader('@common.staffs').reload()
  })
  const handleSelectApply = () => {
    const selectedMap = {} as any
    if (selectedRequisition) {
      selectedMap[selectedRequisition.id] = selectedRequisition
    }
    app
      .open('@basic:SelectRequisitionLink', {
        showBottom: true,
        dataSource: requisitionList,
        requisitionList,
        selectedMap,
        fromPage: 'eCardExpense'
      })
      .then((selectedRequisition: RequisitionType) => {
        form?.setFieldsValue({ requisitionValue: selectedRequisition })
        setSelectedRequisition(selectedRequisition)
      })
  }

  const handleCancelRequisitionClick = () => {
    form.setFieldsValue({ requisitionValue: '' })
    setSelectedRequisition(null)
  }

  const renderFeetypeItem = (className: string) => {
    return (
      <>
        <FormItem
          name={'eCardAmount'}
          className={!supplementData?.show?.length && !feeIncField?.length ? className : ''}
          label={i18n.get(`消费金额`)}
          rules={[
            {
              required: true,
              validator(_rule, value, callback) {
                if (value === undefined) {
                  callback('请输入消费金额')
                } else if (!value) {
                  callback('消费金额格式错误')
                } else if (value && value <= 0) {
                  callback(i18n.get('消费金额不能小于0'))
                } else {
                  callback()
                }
              }
            }
          ]}
        >
          <Input type="number" placeholder={`请输入消费金额`} back="元" />
        </FormItem>
        {supplementData?.show?.includes('select_requisition') && (
          <FormItem
            className={!feeIncField?.length ? className : ''}
            name="requisitionValue"
            label={i18n.get('选择申请事项')}
            rules={[{ required: supplementData?.required?.includes('select_requisition'), message: '请选择申请事项' }]}
          >
            <CustomWrapperWidget
              onClick={handleSelectApply}
              placeholder="选择申请事项"
              onCancelClick={handleCancelRequisitionClick}
            />
          </FormItem>
        )}
        {feeIncField?.map((item: any, index: any) => {
          const {
            name,
            label,
            dataType: { type }
          } = item
          const classNameValue = index === feeIncField.length - 1 ? className : ''
          if (type === 'text') {
            return (
              <FormItem
                className={classNameValue}
                name={name}
                label={i18n.get(`${label}`)}
                rules={[{ required: true, message: `请输入${label}` }]}
              >
                <Input type="text" placeholder={`请输入${label}`} />
              </FormItem>
            )
          } else if (type === 'number') {
            return (
              <FormItem
                className={classNameValue}
                name={name}
                label={i18n.get(`${label}`)}
                rules={[{ required: true, message: `请输入${label}` }]}
              >
                <Input type="number" placeholder={`请输入${label}`} />
              </FormItem>
            )
          } else if (type === 'date') {
            return (
              <FormItem
                className={classNameValue}
                name={name}
                label={i18n.get(`${label}`)}
                rules={[{ required: true, message: `请选择${label}` }]}
              >
                <SelectDateWidget type="one" placeholder={`请选择${label}`} />
              </FormItem>
            )
          } else if (type === 'dateRange') {
            return (
              <FormItem
                className={classNameValue}
                name={name}
                label={i18n.get(`${label}`)}
                rules={[{ required: true, message: `请选择${label}` }]}
              >
                <SelectDateWidget type="range" placeholder={`请选择${label}`} />
              </FormItem>
            )
          } else if (type === 'money') {
            return (
              <FormItem
                className={classNameValue}
                name={name}
                label={i18n.get(`${label}`)}
                rules={[{ required: true, message: `请输入${label}` }]}
              >
                <Input type="number" placeholder={`请输入${label}`} back="元" />
              </FormItem>
            )
          } else {
            return null
          }
        })}
      </>
    )
  }

  return feeTypeValue ? (
    <FeeTypeInfo
      bus={bus}
      type="new"
      setFormBus={setFormBus}
      billType="expense"
      feetype={feeTypeValue ?? {}}
      submitterId={me_info.staff}
      recordFeeTypes={feeTypes}
      sourcePage={'eCardExpense'}
      updateCurrentFeeType={updateCurrentFeeType}
      noNeedVerify={true}
      renderFeetypeItem={renderFeetypeItem}
      formData={{ submitterId: me_info.staff }}
      currentFlowNode={{}}
      billSpecification={{ components: [], type: 'expense', configs: [] }}
      notShowModalIfAllInvoiceSuccess={true}
      hideFields={hideFields}
    />
  ) : (
    <FormItem
      name="feeValue"
      disabled={!isFeetypeloaded}
      label={i18n.get('选择费用类型')}
      rules={[{ required: true, message: '请选择费用类型' }]}
    >
      <CustomWrapperWidget isFeetype={true} onClick={() => handleSelectFeeType(feeTypes)} placeholder="选择费用类型" />
    </FormItem>
  )
}

export default ECardFeeTypeInfo
