import React, { FC, useState } from 'react'
import styles from './PayTypeItem.module.less'
import { Checkbox } from '@hose/eui-mobile'

export interface PayTypeItemInfo {
  icon: any
  title: string
  key: string
}
interface IProps {
  onChange: (key: string) => void
  data: PayTypeItemInfo
  checked: boolean
}

const PayTypeItem: FC<IProps> = props => {
  const { onChange, data, checked } = props
  const handleOnChange = (check: boolean) => {
    if (check) {
      onChange(data.key)
    }
  }
  return (
    <div className={styles['pay-type-item-wrapper']}>
      <div className="pay-type-item-left">
        <img className="pay-type-item-icon" src={data.icon} alt="" />
        <span className="pay-type-item-title">{data.title}</span>
      </div>
      <Checkbox shape="circle" checked={checked} onChange={handleOnChange} />
    </div>
  )
}

export default PayTypeItem
