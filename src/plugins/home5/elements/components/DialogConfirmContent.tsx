import React, { useState, useImperativeHandle, forwardRef } from 'react'
import { Input } from '@hose/eui-mobile'
import { OutlinedDirectionDown } from '@hose/eui-icons'

interface IProps {
  ContentRender: any
  showInput: boolean
}
export type SupplementConfigRef = {
  config: { supplement: string }
}

const DialogConfirmContent = forwardRef<SupplementConfigRef, IProps>(({ ContentRender, showInput }, ref) => {
  const [supplement, setSupplement] = useState('')
  const [showDescription, setShowDescription] = useState<boolean>(false)

  const handleSupplementChange = (value: string) => {
    setSupplement(value)
  }

  useImperativeHandle(ref, () => ({
    get config() {
      return { supplement }
    }
  }))

  return (
    <div>
      {showDescription ? (
        <ContentRender />
      ) : (
        <div className="tips" onClick={() => setShowDescription(true)}>
          查看详情 <OutlinedDirectionDown />
        </div>
      )}
      {showInput && (
        <Input
          style={{ margin: '12px 0' }}
          value={supplement}
          onChange={handleSupplementChange}
          placeholder="请填写超额原因（必填）"
          maxLength={30}
          border
          showCount
        />
      )}
    </div>
  )
})

export default DialogConfirmContent
