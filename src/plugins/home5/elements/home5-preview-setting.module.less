@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.homePreviewSetting {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #fff;

    .pageVersion {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 80%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .icon {
            width: 160px;
            height: 160px;
            border: 2px solid rgba(29, 33, 41, 0.1);
            border-radius: 32px;
            padding: 40px;
            margin-bottom: 48px;

            svg {
                width: 100%;
                height: 100%;
            }
        }

        .name {
            color: var(--eui-neutral-grey-1);
            font: var(--eui-head-b1);
            font-weight: 500;
        }

        .time {
            color: var(--eui-text-placeholder);
            font: var(--eui-font-note-r2);
            margin: 16px 0 32px 0;
        }

        .activeStatus {
            width: 104px;
            height: 40px;
            border-radius: 8px;
            text-align: center;
            background-color: var(--eui-function-success-50);
            color: var(--eui-function-success-500);
            font: var(--eui-font-note-r2);
        }
    }

    .btn-wrap {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 120px;
        display: flex;
        padding: 0 32px;

        .btn {
            height: 88px;

            &:first-of-type {
                margin-right: 24px;
            }

        }
    }
}

.btn-wraps {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 120px;
    display: flex;
    padding: 0 32px;
    z-index: 999;
    align-items: center;
    background-color: #fff;

    .btn {
        height: 88px;
    }
}

.home-page-wrapper {
    display: flex;
    flex-direction: column;
    position: relative;
    overflow-y: auto;
    background-color: #f2f3f5;
    >* {
        pointer-events: none;
    }
    :global {
        .home-page-card-container {
            margin-top: 24px;
            z-index: 100;
            display: flex;
            padding-bottom: @space-12;
            flex-direction: column;
        }
    }
}