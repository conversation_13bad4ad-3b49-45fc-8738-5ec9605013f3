import styles from './e-card-expense-page.module.less'
import React, { FC, useEffect, useRef, useState } from 'react'
import { app } from '@ekuaibao/whispered'
import { Fetch, Resource } from '@ekuaibao/fetch'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import { uuid } from '@ekuaibao/helpers'
import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types'
import { Dialog, Form, Toast, Button, ImageUploader, NoticeBar, Popup } from '@hose/eui-mobile'
import { DialogShowHandler } from '@hose/eui-mobile/es/components/dialog'
import { FilledTipsWarning, OutlinedGeneralCamera } from '@hose/eui-icons'
import { nanoid } from 'nanoid'
import qs from 'qs'
import EnhanceTitleHook from '../../basic-elements/enhance-title-hook'
import DialogConfirmContent, { SupplementConfigRef } from './components/DialogConfirmContent'
import { Platform } from '../staticUtil'
import { logEvent } from '../../../lib/dataflux'
import { IProps, RequisitionType } from '../type'
import ECardFeeTypeInfo from './components/ECardFeeTypeInfo'
import {
  fnGetFeeTypeByECardConfig,
  fnGetFinishParams,
  fnHandleBefore,
  fnHandleUpload,
  getCurrentAddress,
  goAliPay,
  handleSaveECardExpense,
  PayTypeMap,
  sendData
} from '../util/ECardUtil'
import PayTypeList from './components/PayTypeList'

const ecardResource = new Resource('/api/v1/ecard')
const eCardUseListResource = new Resource('/api/form/v3/requisition/info/eCardUseList')
const controlResource = new Resource('/api/ecard/control/v1')

const FormItem = Form.Item
const mapId = 'map-container-expense-page'
const SkeletonListEUI = app.require('@home5/SkeletonList') as any
const EmptyWidget = app.require('@home5/EmptyWidget') as any
const bus: MessageCenter = new MessageCenter()

export const formatFeeTypes = (feeTypesMap: any, syncFeeTypeIds: string[]): any[] => {
  return syncFeeTypeIds.reduce((acc: any[], id) => {
    const feeType = feeTypesMap?.[id]
    if (feeType?.id) {
      acc.push(feeType)
    }
    return acc
  }, [])
}

const ECardExpensePage: FC<IProps> = props => {
  const [requisitionList, setRequisitionList] = useState<RequisitionType[]>([])
  const [selectedRequisition, setSelectedRequisition] = useState<RequisitionType>()
  const [feeTypeValue, setFeeType] = useState<any>()
  const [loading, setLoading] = useState(false)
  const [uploadPhoto, setUploadPhoto] = useState(false)
  const [feeTypes, setFeeTypes] = useState([])
  const [deviceId, setDeviceId] = useState<string>(nanoid(16))
  const [supplementData, setSupplementData] = useState<any>({})
  const [feeIncField, setFeeIncField] = useState<GlobalFieldIF[]>([])
  const [eCardState, setECardState] = useState<any>()
  const [formBus, setFormBus] = useState<any>()
  const [isFeetypeloaded, setIsFeetypeloaded] = useState(false) // 判断费用类型是否加载完成
  const [isError, setIsError] = useState(false) // 判断银行是否报错
  const [isLoading, setIsLoading] = useState(true) // 判断页面是否加载
  const [popupVisibile, setPopupVisibile] = useState(false)
  // const [payTypeList, setPayTypeList] = useState([])
  // const [formValue, setFormValue] = useState(undefined)
  // const [payChannel, setPayChannel] = useState(undefined)

  const [form] = Form.useForm()
  const supplementConfigRef = useRef<SupplementConfigRef>(null)
  const handler = useRef<DialogShowHandler>()

  let address = ''

  const { location, me_info } = props
  const userInfo = me_info?.staff

  useEffect(() => {
    fnGetCurrentAddress()
    app.invokeService('@layout:get:device:id').then((res: { deviceId: string }) => {
      if (res?.deviceId) {
        setDeviceId(res.deviceId)
      } else {
        setDeviceId(uuid(16))
      }
    })
    sendData({
      messageType: 'call',
      action: 'call:getIosCamera'
    })
    // Fetch.POST('/api/engine/hdt/api/v1/virtual-card/channel').then(res => {
    //   setPayTypeList(res?.data ?? [])
    // })
  }, [])

  useEffect(() => {
    if (userInfo) {
      fnGetECardState()
      getFeeTypeByECardConfig()
      app.dataLoader('@common.uploadServiceUrl').load()
      app.dataLoader('@common.baseDataProperties').load()
      app.invokeService('@common:get:basedata:properties')
    }
    return () => {
      Dialog.clear()
    }
  }, [userInfo])

  const fnGetECardState = async () => {
    const userInfo = me_info?.staff
    if (userInfo) {
      const { value } = await app.invokeService('@common:check:ecard:state', { ownerId: userInfo.id })
      setECardState(value)
    }
  }

  const fnGetCurrentAddress = () => {
    getCurrentAddress().then((data: { addr: string }) => {
      const { addr } = data
      address = addr
    })
  }

  const handleFinish = async () => {
    setTimeout(() => {
      Promise.all([form.validateFields(), handleSaveECardExpense(formBus)]).then(async (res: any[]) => {
        const staff = me_info?.staff
        const result: any = (await getCurrentAddress()) || {}
        const params = fnGetFinishParams({
          res,
          deviceId,
          address: result?.addr,
          staff,
          mapInfo: result?.mapInfo
        })
        // if (payTypeList.length > 1) {
        // setPayChannel('ALIPAY') // 默认选中支付宝
        // selectPayType(params)
        // } else if (payTypeList.length === 1) {
        checkInfo(params, 'ALIPAY')
        // } else {
        //   Toast.show({
        //     icon: 'fail',
        //     content: '未获取到支付渠道'
        //   })
        // }
      })
    }, 0)
  }

  const checkInfo = async (params: any, channel: string) => {
    // setPayChannel(channel)
    setLoading(true)
    const { value } = await ecardResource.POST('/consumption', { ...params, channel }).catch(e => {
      Toast.show(e?.errorMessage)
      setLoading(false)
    })
    setLoading(false)
    if (value?.success) {
      checkConsumptionStatus(value, channel)
    } else {
      Toast.show(value?.msg)
    }
  }

  const checkConsumptionStatus = (value: any, channel: string) => {
    const { type } = value || {}
    // CALCULATION 代表计算中，就是跳ai页面了
    // PAYING 代表支付中，跳转的是支付页面
    // REJECT 不可消费
    // ADDITIONAL 风险提示,带事中管控
    // FORBID 风控模型禁止
    switch (type) {
      case 'CALCULATION':
        return renderAIDialog(value, channel)
      case 'REJECT':
        return renderRejectDialog(value)
      case 'PAYING':
        return goToPayPage(value, channel)
      case 'ADDITIONAL':
        return renderAdditionalDialog(value, channel)
      case 'FORBID':
        return renderControlForbidDialog(value)
    }
  }

  const fnOpenPage = async (id: any, channel: string, params?: any) => {
    const { value } = await ecardResource.POST('/confirm/pay', params ?? null, { id })
    if (value?.success) {
      return fnGotoTypePage({ ...value, id }, channel)
    } else {
      Toast.show(value?.msg)
    }
  }

  const renderControlForbidDialog = (result: any) => {
    return Dialog.alert({ content: result?.msg || '风控禁止提交' })
  }

  const renderAdditionalDialog = (value: any, channel: string) => {
    const { id, msg, riskControlAmount } = value || {}
    const content = riskControlAmount !== null ? `风控金额上限为：${riskControlAmount} 元，${msg}` : msg

    handler.current = Dialog.show({
      title: '风险提示',
      content,
      closeOnAction: true,
      primarySecondaryActions: [
        {
          key: 'confirm',
          text: '确定',
          category: 'primary',
          onClick: () => {
            handler.current?.close()
            return fnOpenPage(id, channel)
          }
        },
        {
          key: 'close',
          text: '取消',
          category: 'secondary',
          onClick: () => {
            handler.current?.close()
          }
        }
      ]
    })
  }

  const goToAIPage = async (value: any, payChannel: string) => {
    const { id, url, type } = value
    Dialog.alert({
      content: i18n.get('点击确认按钮获取审批结果，审批完成将进入支付页面，审批未完成则返回 AI 页面继续审批。'),
      onConfirm: () => {
        ecardResource
          .GET('/update/state', { id, stage: type })
          .then(res => {
            return fnGotoTypePage({ ...res?.value }, payChannel)
          })
          .catch(e => {
            Toast.show(e?.errorMessage)
          })
      }
    })
    const newUrl = new URL(url)
    const params = newUrl.searchParams
    params.append('uuid', nanoid(16))
    app.invokeService('@layout:open:link', `${newUrl.toString()}`, false, true, false)
  }

  // const selectPayType = (value: any) => {
  // setFormValue(value)
  // setPopupVisibile(true)
  // }
  const goToPayPage = (value: any, channel: string) => {
    const { id, url, type } = value || {}
    Dialog.alert({
      content: i18n.get('订单支付中...'),
      onConfirm: () => {
        ecardResource.GET('/update/state', { id, stage: type }) // 解除订单锁定，不调用这个接口的话订单锁10分钟，再次消费要等10分钟后才能进行
        app?.sdk?.invoke('call:close')
        if (window.__PLANTFORM__ === 'APP' && !window.isPC) {
          app.emit('header:left:click') // 原生返回按钮有问题
        } else {
          app.go(-1)
        }
      }
    })
    switch (channel) {
      case 'ALIPAY': // 支付宝支付
        goAliPay(url)
        break
      case 'PA_TOKEN': // 平安token支付
        openLinkUrl(value)
        break
    }
  }

  const openLinkUrl = async (value: any) => {
    const { url } = value || {}
    const { eCardOpenPlatform = '' } = qs.parse(window.location.search.slice(1))
    if ((eCardOpenPlatform === Platform.app || window.__PLANTFORM__ === 'APP') && !window.isPC) {
      const dataInfo = { url, cardNo: eCardState?.cardNo, ticket: '' }
      app.invokeService('@layout:open:YQP', dataInfo) // 平安银行H5支付 或者sdk支付
    } else {
      app.invokeService('@layout:open:link', url, false, true, false)
    }
  }

  const fnGotoTypePage = (res: any, payChannel: string) => {
    const { type } = res
    // CALCULATION 代表计算中，就是跳ai页面了
    // PAYING 代表支付中，跳转的是支付页面
    // REJECT 不可消费
    // ADDITIONAL 风险提示,带事中管控
    // FORBID 风控模型禁止
    switch (type) {
      case 'CALCULATION':
        return goToAIPage(res, payChannel)
      case 'REJECT':
        return renderRejectDialog(res)
      case 'PAYING':
        return goToPayPage(res, payChannel)
      case 'ADDITIONAL':
        return renderAdditionalDialog(res, payChannel)
      case 'FORBID':
        return renderControlForbidDialog(res)
    }
  }

  const updateCurrentFeeType = async (feeType: any) => {
    return new Promise((resolve, reject) => {
      const feeTypeId = feeType?.id
      if (!feeTypeId || feeTypeId === feeTypeValue?.id) {
        reject()
      }
      const staff = me_info?.staff
      controlResource
        .GET('/feeIncField', { feeTypeId })
        .then(result => {
          const baseDataPropertiesMap = app.getState('@common.baseDataProperties.baseDataPropertiesMap') || {}
          const feeIncField = [] as GlobalFieldIF[]
          const list = result?.items ?? []
          list.forEach((item: string) => {
            feeIncField.push(baseDataPropertiesMap[item])
          })
          setFeeIncField(feeIncField)
          getRequisitionList(feeTypeId)
          fetchSupplement(feeTypeId)
          form.setFieldValue('fileList', [])
          form.setFieldsValue({ feeValue: feeType })
          setFeeType(feeType)
          logEvent('e-card-select-feetype', {
            sceneName: '选择费用类型',
            feeTypeId: feeType?.id,
            staffName: staff?.name,
            company: staff?.corporationId?.name,
            CostName: feeType?.name
          })
          resolve('')
        })
        .catch(e => {
          logEvent('e-card-select-feetype', {
            sceneName: '费用类型报错',
            feeTypeId: feeType?.id,
            errorMessages: e.errorMessage,
            CostName: feeType?.name,
            staffName: staff?.name,
            company: staff?.corporationId?.name
          })
          Dialog.alert({ content: e.errorMessage })
          reject()
        })
    })
  }
  const handleSelectFeeType = (feeTypes: any) => {
    app.open('@feetype:SelectFeeTypeModal', {
      feetype: feeTypes,
      currentFeeType: feeTypeValue,
      updateCurrentFeeType: updateCurrentFeeType
    })
  }

  const fetchSupplement = (feeTypeId: string) => {
    if (!feeTypeId) {
      return
    }
    ecardResource.GET('/supplement', { feeTypeId }).then(res => {
      setSupplementData(res?.value || {})
    })
  }

  const getRequisitionList = (feeTypeId: string) => {
    if (!feeTypeId) {
      return
    }

    eCardUseListResource.GET('', { feeTypeId }).then(res => {
      const data = (res?.items || []).map((item: RequisitionType) => {
        const name = item.title
        const requisitionMoney = Number(item.amount || 0)
        return {
          ...item,
          name,
          requisitionMoney,
          ownerId: {}
        }
      }) as RequisitionType[]
      const search = location.search
      let requisitionId: string
      if (search) {
        const params = new URLSearchParams(search)
        requisitionId = params.get('requisitionId')
      }
      const selectedRequisition = data.find(it => it.id === requisitionId || it.id === feeTypeValue?.id)
      setRequisitionList(data)
      setSelectedRequisition(selectedRequisition)
      form.setFieldsValue({ requisitionValue: selectedRequisition })
      app.invokeService('@requisition:get:requisition:data:source', data)
    })
  }

  const getFeeTypeByECardConfig = async () => {
    const ownerId = me_info.staff.id
    const result = await Fetch.GET('/api/v1/ecard/check/release', { ownerId })
    const hasError = !result?.value
    setIsError(hasError)
    setIsLoading(false)
    if (hasError) {
      return
    }
    const syncFeeTypeIds = await fnGetFeeTypeByECardConfig()
    app
      .dataLoader('@common.feetypes')
      .load()
      .then((res: any) => {
        const feeTypes = syncFeeTypeIds.length === 0 ? res.data : formatFeeTypes(res.map, syncFeeTypeIds)
        const feeTypeParams = res.map[props.params?.feetypeId]
        setFeeTypes(feeTypes)
        setIsFeetypeloaded(true)
        if (feeTypeParams) {
          updateCurrentFeeType(feeTypeParams)
        } else {
          if (!feeTypeValue) {
            handleSelectFeeType(feeTypes)
          }
        }
      })
  }

  const handleUpload = (file: File) => {
    return fnHandleUpload({ setUploadPhoto, address }, file)
  }

  const handleBefore = (file: File): Promise<File | null> => {
    return new Promise(resolve => {
      getCurrentAddress()
        .then((data: { addr: string }) => {
          const { addr } = data
          address = addr
          resolve(fnHandleBefore(file))
        })
        .catch(() => {
          Toast.show('获取定位失败，请到设置中打开定位权限')
          resolve(null)
        })
    })
  }

  const handleDelete = () => {
    return Dialog.confirm({
      iconType: 'warn',
      title: '确认删除？',
      confirmText: '删除'
    })
  }

  const desContentRender = (value: any) => {
    const {
      standardAmount,
      quotaAmount,
      remainingAmount,
      expenseName,
      requestName,
      singleDayLimitBalance,
      quota,
      socializeAmount,
      singleDayUnit,
      riskControlAmount
    } = value || {}
    return (
      <div className="description">
        {!!standardAmount && (
          <span>
            {expenseName}费用标准为：{standardAmount} 元
          </span>
        )}
        {!!quotaAmount && <span>易商卡单笔限额为：{quotaAmount} 元</span>}
        {!!remainingAmount && (
          <span>
            「{requestName}」申请事项余额为：{remainingAmount} 元
          </span>
        )}
        {!!quota && <span>剩余循环额度为：{quota} 元</span>}
        {!!singleDayLimitBalance && (
          <span>{`易商卡${getSingleDayUnit(singleDayUnit)}限额余额为：${singleDayLimitBalance} 元`}</span>
        )}
        {socializeAmount !== null && <span>交际应酬费为：{socializeAmount} 元</span>}
        {riskControlAmount !== null && <span>风控金额上限为：{riskControlAmount} 元</span>}
      </div>
    )
  }

  const getSingleDayUnit = (type: string) => {
    switch (type) {
      case 'D':
        return '单日'
      case 'W':
        return '每周'
      case 'M':
        return '每月'
      case 'Q':
        return '季度'
      default:
        return '单日'
    }
  }
  const renderAIDialog = (value: any, channel: string) => {
    const { excessAmount, quotaCeilingAmount, id } = value || {}
    handler.current = Dialog.show({
      bodyClassName: styles['e-card-dialog-AI-wrap'],
      header: (
        <FilledTipsWarning
          style={{
            fontSize: 48,
            color: 'var(--eui-function-warning-400)'
          }}
        />
      ),
      title: `你已超额 ${excessAmount} 元`,
      content: (
        <div className="content">
          <div className="content-title">本次消费额度上限为：{quotaCeilingAmount} 元</div>
          <DialogConfirmContent
            ref={supplementConfigRef}
            ContentRender={() => desContentRender(value)}
            showInput={true}
          />
        </div>
      ),
      actions: [
        {
          key: 'AI',
          text: 'AI审批，无需等待',
          className: 'AI-action',
          onClick: () => {
            const { supplement } = supplementConfigRef.current?.config || {}
            if (!supplement) {
              Toast.show({ content: '请填写超额原因' })
              return Promise.reject()
            }
            handler.current?.close()
            return fnOpenPage(id, channel, { supplement })
          }
        },
        {
          key: 'close',
          text: '取消',
          className: 'cancel',
          onClick: () => {
            handler.current?.close()
          }
        }
      ]
    })
  }

  const renderRejectDialog = (value: any) => {
    const { excessAmount, quotaCeilingAmount, msg } = value || {}
    Dialog.show({
      bodyClassName: styles['e-card-dialog-reject-wrap'],
      header: (
        <FilledTipsWarning
          style={{
            fontSize: 48,
            color: 'var(--eui-function-warning-400)'
          }}
        />
      ),
      title: `你已超额 ${excessAmount} 元`,
      content: (
        <div className="content">
          <div className="content-title">本次消费额度上限为：{quotaCeilingAmount} 元</div>
          <DialogConfirmContent
            ref={supplementConfigRef}
            ContentRender={() => desContentRender(value)}
            showInput={false}
          />
          {msg && <div className="reject-des">{msg}</div>}
        </div>
      ),
      closeOnAction: true,
      actions: [
        {
          key: 'reject',
          text: '返回修改',
          category: 'primary',
          className: 'reject-action'
        }
      ]
    })
  }
  if (isLoading) {
    // 必要接口没有请求回来的时候显示骨架屏
    return <SkeletonListEUI />
  }

  if (isError) {
    return <EmptyWidget type="Input" tips={i18n.get('银联账单维护中，请联系系统管理员进行咨询')} />
  }

  return (
    <div className={styles['e-card-expense-page-wrapper']}>
      {/* <NoticeBar
        content={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {i18n.get('企业支付完成后，您的消费信息将推送给您的直属领导')}
          </div>
        }
        color="info"
      />
      <Popup
        title={i18n.get('选择支付方式')}
        visible={popupVisibile}
        showCloseButton
        onMaskClick={() => {
          setPopupVisibile(false)
        }}
        onClose={() => {
          setPopupVisibile(false)
        }}
        bodyStyle={{
          minHeight: '40vh'
        }}
        radius
      >
        <PayTypeList
          list={payTypeList.map(item => PayTypeMap[item])}
          defaultKey={payChannel}
          onChange={(channel: string) => {
            setPopupVisibile(false)
            checkInfo(formValue, channel)
          }}
        />
      </Popup> */}
      <Form
        name="form"
        footer={
          <Button block size="large" type="submit" loading={loading} disabled={uploadPhoto} onClick={handleFinish}>
            {i18n.get('填写完毕，发起支付')}
          </Button>
        }
        form={form}
      >
        <ECardFeeTypeInfo
          form={form}
          bus={bus}
          setFormBus={setFormBus}
          feeTypeValue={feeTypeValue}
          me_info={me_info}
          feeTypes={feeTypes}
          updateCurrentFeeType={updateCurrentFeeType}
          supplementData={supplementData}
          feeIncField={feeIncField}
          selectedRequisition={selectedRequisition}
          requisitionList={requisitionList}
          setSelectedRequisition={setSelectedRequisition}
          isFeetypeloaded={isFeetypeloaded}
          handleSelectFeeType={handleSelectFeeType}
        />
        {feeTypeValue && supplementData?.show?.includes('scene_ocr') && (
          <FormItem
            rules={[{ required: true, message: i18n.get('请选择照片') }]}
            name="fileList"
            label={i18n.get('拍照')}
          >
            <ImageUploader
              style={{ '--cell-size': '101px' }}
              capture="environment"
              accept="image/*"
              description={i18n.get(
                '照片应体现消费现场或事件，支持png、jpg、jpeg格式，最多上传 {__k0} 张，单张最大 {__k1} M',
                { __k0: 3, __k1: 20 }
              )}
              upload={(file: File) => handleUpload(file)}
              beforeUpload={(file: File) => handleBefore(file)}
              onDelete={handleDelete}
              maxCount={3}
            >
              <div className={styles['e-card-expense-page-image']}>
                <OutlinedGeneralCamera fontSize={20} />
              </div>
            </ImageUploader>
          </FormItem>
        )}
      </Form>
      <div id={mapId} />
    </div>
  )
}

export default EnhanceConnect((state: { [key: string]: any }) => ({
  me_info: state['@common'].me_info
}))(EnhanceTitleHook(i18n.get('易商卡消费信息'))(ECardExpensePage))
