.e-card-expense-page-wrapper {
  overflow: auto;

  :global {
    .eui-form {
      --border-bottom: none;

      .eui-list {
        --border-bottom: none;
        padding-bottom: 170px;
      }

      .eui-list-body {
        border-top: 0px;
      }
    }

    .eui-form-footer {
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #fff;
      padding: 20px 32px;
    }

    .from-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .from-content-label {
      color: var(--eui-text-placeholder);
      font: var(--eui-font-head-r1);
    }

    .from-content-vale {
      color: var(--color);
      font: var(--eui-font-head-r1);
    }
  }
}

.dialog-custom-wrapper {
  .tips {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.e-card-dialog-AI-wrap {
  :global {
    .eui-dialog-content {
      padding-bottom: 0;

      .content {
        .content-title {
          text-align: center;
          margin: 8px 0 16px;
          font: var(--eui-font-head-r1);
          color: var(--eui-text-title);
        }

        .tips {
          font: var(--eui-font-body-r1);
          color: var(--eui-text-caption);
          text-align: center;
        }

        .description {
          display: flex;
          font: var(--eui-font-body-r1);
          flex-direction: column;
          padding: 24px;
          border-radius: 16px;
          background-color: var(--eui-bg-body-overlay);
          color: var(--eui-text-caption);
        }
      }
    }

    .eui-dialog-footer {
      .eui-dialog-action-row {
        border: none;

        .AI-action {
          color: var(--eui-static-white);
          font: var(--eui-font-head-r1);
          margin: 0 24px 16px;
          border-radius: 12px;
          padding: 0;
          border: 0;
          height: 88px;
          background: linear-gradient(90deg, #8a66fc 0%, #03d5d5 100%);

          &:active {
            background: linear-gradient(90deg, #4f30b2 0%, #00a3a3 100%);
          }
        }

        .cancel {
          font: var(--eui-font-head-r1);
          margin: 0 24px 32px;
          border-radius: 12px;
          padding: 0;
          height: 88px;
          border: 1px solid var(--eui-line-border-component) !important;
        }
      }
    }
  }
}

.e-card-dialog-reject-wrap {
  :global {
    .eui-dialog-content {
      padding-bottom: 0;

      .content {
        .content-title {
          text-align: center;
          margin: 8px 0 16px;
          font: var(--eui-font-head-r1);
          color: var(--eui-text-title);
        }

        .tips {
          font: var(--eui-font-body-r1);
          color: var(--eui-text-caption);
          text-align: center;
        }

        .description {
          display: flex;
          flex-direction: column;
          padding: 24px;
          border-radius: 16px;
          font: var(--eui-font-body-r1);
          background-color: var(--eui-bg-body-overlay);
          color: var(--eui-text-caption);
        }

        .reject-des {
          margin: 24px 0 40px;
          font: var(--eui-font-head-r1);
          color: var(--eui-text-title);
        }
      }
    }

    .eui-dialog-footer {
      .eui-dialog-action-row {
        border: none;

        .reject-action {
          font: var(--eui-font-head-r1);
          margin: 0 24px 32px;
          border-radius: 12px;
          padding: 20px;
          border: 0;
          height: 88px;
        }
      }
    }
  }
}

.e-card-expense-page-image {
  width: 202px;
  height: 202px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  color: var(--eui-icon-n2);
  background-color: var(--eui-bg-body-overlay);
}