import { app } from '@ekuaibao/whispered'
import styles from './home5-payee-info.module.less'
import React from 'react'
import <PERSON>han<PERSON><PERSON><PERSON><PERSON><PERSON>ook from '../../../lib/EnhanceTitleHook'
// const SVG_WALLET = require('../../../images/home-loan.svg')
const SVG_WALLET = app.require('@images/home-loan.svg')

@EnhanceTitleHook(i18n.get('收款信息'))
export default class PayeeInfo extends React.PureComponent<any, any> {
  renderSwipeActionItemBank = (line: any) => {
    return (
      <div key={line.id} className="item-wrapper">
        <div className="tp">
          <div className="name">{line.accountName}</div>
        </div>
        <div className="item-w">
          <div className="payee-item">
            <div className="md">
              <div>{this.formatCardNo(line.accountNo)}</div>
            </div>
            <div className="l1">
              <div className="bank-img">
                <img className="payee-icon" src={line.icon} />
                <span>{line.bank || line.unionBank}</span>
              </div>
            </div>
            <div className="bm mb-4">
              <div className="fs-14">{line.branch}</div>
            </div>
          </div>
          <div className="card-bottom-wrapper">
            <div className="flex-1">
              <span className="fs-14">{i18n.get('所有者：')}</span>
              <span className="fs-14">
                {line.owner === 'CORPORATION' ? i18n.get('企业') : line.staffId && line.staffId.name}
              </span>
            </div>
            {line.isDefault && <div className="default">{i18n.get('默认')}</div>}
          </div>
        </div>
      </div>
    )
  }

  renderSwipeActionItemWallet = (line: any) => {
    return (
      <div className="list-wrapper" key={line.id}>
        <div className="item-wrapper wallet-wrapper mt-5">
          <div className="tp">
            <div className="name">{line.accountName}</div>
          </div>
          <div className="wallet-img">
            <img src={SVG_WALLET} />
            <span>{line.accountNo.replace(/(\d{4})(?=\d)/g, '$1 ')}</span>
            <div className="card-right-wrapper vertical-left">
              {line.isDefault && (
                <div className="default">
                  <div>{i18n.get('默认')}</div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  formatCardNo = (cardNo: string) => {
    if (/[^0-9]/.test(cardNo)) {
      return cardNo
    }
    return cardNo.replace(/(\d{4})(?=\d)/g, '$1 ')
  }

  render() {
    if (!window.payeeInfo) {
      return null
    }
    return (
      <div className={styles['payee-info-wrapper']}>
        {window.payeeInfo.sort !== 'WALLET'
          ? this.renderSwipeActionItemBank(window.payeeInfo)
          : this.renderSwipeActionItemWallet(window.payeeInfo)}
        {this.props.isModal ? (
          <div className={styles['cancel']} onClick={() => this.props.layer.emitCancel()}>
            <svg className="icon" aria-hidden="true">
              <use xlinkHref="#EDico-close-default" />
            </svg>
          </div>
        ) : null}
      </div>
    )
  }
}
