@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.plane_and_train_card_wrapper {
  display: flex;
  flex-direction: column;
  :global {
    .card-content-layout {
      margin: @space-5 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      .layout_left {
        display: flex;
        flex-direction: column;
        flex: 1;
        align-items: flex-start;
        .airport {
          .font-weight-2;
          .font-size-2;
          color: @color-black-1;
        }
        .time {
          .font-weight-3;
          .font-size-5;
          margin-top: @space-2;
          color: @color-black-1;
        }
        .info {
          .font-weight-2;
          margin-top: @space-2;
          .font-size-2;
          color: @color-black-3;
        }
      }
      .layout_right {
        .layout_left;
        align-items: flex-end;
      }
      .layout_center {
        display: flex;
        align-items: center;
        flex-direction: column;
        .number {
          .font-size-2;
          .font-weight-2;
          color: @color-black-1;
        }
        .duration {
          margin-top: @space-2;
          .font-size-2;
          .font-weight-2;
          color: @color-black-3;
        }
      }
    }
    .card-bottom {
      display: flex;
      flex-direction: row;
      img {
        width: 40px;
        height: 40px;
        margin-right: @space-2;
      }
      .seat {
        .font-weight-3;
        .font-size-2;
        color: @color-black-1;
      }
    }
  }
}

.hotel_card_wrapper {
  .font-weight-2;
  .font-size-2;
  color: @color-black-2;
  margin-top: @space-2;
}
