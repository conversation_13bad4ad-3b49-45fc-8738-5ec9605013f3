@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.cardSimple_loan-wrap {
  &:active {
    //transform: scale(.95);
    transform: scale(1);
    background-color: @color-bg-2;
  }
  //height: 152px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  margin: @space-5 @space-6;
  padding: @space-6;
  border-radius: @radius-3;
  background-color: @color-white-1;
  transition: 0.1s all ease-in-out;
  :global {
    .cardSimple_loan-title {
      user-select: none;
      width: 100%;
      text-align: left;
      //margin-left: @space-4;
      color: @color-black-1;
      .font-size-4;
      .font-weight-3;
    }

    .cardSimple_loan-content {
      margin-top: @space-7;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 240px;
      width: 100%;
      .cardSimple_loan-content-detail {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        border-radius: @radius-4;
        background-color: @color-bg-3;
        padding: @space-5;
        flex: 1;
        width: 100%;
      }
      .cardSimple_loan-content-detail-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        &:first-child {
          margin-top: 0;
        }
        .cardSimple_loan-content-detail-item-box {
          display: flex;
          align-items: center;
        }
        .cardSimple_loan-content-detail-item-mark {
          display: inline-block;
          width: 16px;
          height: 16px;
          border-radius: @radius-5;
          vertical-align: -2px;
          margin-right: @space-4;
          &.blue {
            background-color: @color-inform-2;
          }
          &.yellow {
            background-color: @color-warning-2;
          }
          &.green {
            background-color: @color-success-2;
          }
        }
        .cardSimple_loan-content-detail-item-label {
          .font-size-2;
          color: @color-black-1;
        }
        .cardSimple_loan-content-detail-item-money {
          margin-left: @space-6;
          .currency, .value {
            color: @color-black-1;
            .font-weight-3;
            .font-size-2 !important;
          }
        }
      }
      .cardSimple_loan-content-pie {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        .cardSimple_loan-content-detail-item {
          margin-top: 0;
          align-items: center;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          flex: 1;
          .cardSimple_loan-content-pie-money {
            padding-right: @space-6;
            .currency, .value {
              color: @color-black-1;
              .font-size-3 !important;
              .font-weight-3;
            }
          }
        }

        .cardSimple_loan-content-detail-item-line {
          margin: @space-4 @space-6 @space-6;
          height: 12px;
          position: relative;
          .normal, .percent_reserved, .percent_remain {
            height: 100%;
            border-radius: @radius-4;
            position: absolute;
          }
          .normal {
            background-color: @color-success-2;
            z-index: 1;
          }
          .percent_reserved {
            background-color: @color-warning-2;
            z-index: 2;
            border-right: 2px solid @color-white-1;
          }
          .percent_remain {
            background-color: @color-inform-2;
            z-index: 3;
            border-right: 2px solid @color-white-1;
          }
        }
      }
    }

    .cardSimple-money {
      span {
        user-select: none;
        color: @color-black-1;
        .font-size-3 !important;
        .font-weight-3;
      }
    }
  }
}
