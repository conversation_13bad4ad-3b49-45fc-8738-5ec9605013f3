/**************************************
 * Created By LinK On 2019/3/28 20:20.
 **************************************/
import React from 'react'

interface SemicircleProps {
  percent: number
  color: string
}

export default function Semicircle(props: SemicircleProps) {
  const { percent, color } = props
  if (!percent) return null

  let strokeWidth = 8
  const radius = 51
  const total = Math.PI * 2 * radius
  const length = total * percent
  const borderLength = total * (percent + 0.005)

  const borderStyle = {
    strokeDasharray: `${borderLength} ${total}`,
    transition: 'stroke-dashoffset 0.3s ease 0s, stroke 0.3s ease'
  }
  const pathStyle = {
    strokeDasharray: `${length} ${total}`,
    transition: 'stroke-dashoffset 0.3s ease 0s, stroke 0.3s ease'
  }

  return (
    <svg className="cardSimple_loan-content-pie-svg" viewBox="0 0 210 120">
      {percent !== 1 && (
        <path
          d="M 4 110 A 50 50 0 0 1 206 110"
          strokeLinecap="round"
          stroke={'white'}
          strokeWidth={strokeWidth + 2}
          style={borderStyle}
          fill="none"
        />
      )}
      <path
        // d="M 5 55 A 50 50 0 0 1 130 55"
        d="M 4 110 A 50 50 0 0 1 206 110"
        className={color}
        strokeLinecap="round"
        strokeWidth={strokeWidth}
        style={pathStyle}
        fill="none"
      />
    </svg>
  )
}
