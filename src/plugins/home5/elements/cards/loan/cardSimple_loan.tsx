import { app } from '@ekuaibao/whispered'
/**************************************
 * Created By LinK On 2019/3/27 19:59.
 **************************************/

import React from 'react'
import styles from './cardSimple_loan.module.less'
import { app as api } from '@ekuaibao/whispered'
import { mapForCodeToPath } from '../../../staticUtil'
import { Fetch } from '@ekuaibao/fetch'
import { Painter } from '@ekuaibao/painter'
export interface Props {
  data: any
  className?: string
}
export default class CardSimple_Loan extends React.Component<Props, any> {
  constructor(props: Props) {
    super(props)
    this.state = { value: undefined }
  }
  componentDidMount() {
    const me = api.getState()['@common']?.me_info?.staff
    if (me?.id) {
      Fetch.POST(`/api/engine/blockUI/$LOAN_STATISTICS`, {}, { body: { staffId: me.id } }).then(res => {
        if (res?.value) {
          this.setState({ value: res?.value })
        }
      })
    }
  }
  handleOnClick = () => {
    const { data = {} } = this.props
    const { detail = {} } = data
    const { data: loanData } = detail
    const { remain = 0, repayment = 0, reserved = 0, total = 0 } = loanData || {}
    if (data.code === 'myLoan') {
      api.go(`/${mapForCodeToPath[data.code]}/${remain}/${repayment}/${reserved}/${total}`, false)
    } else {
      api.go(`/${mapForCodeToPath[data.code]}`, false)
    }
  }

  render() {
    const { data = {}, className = '' } = this.props
    const { detail = {} } = data
    const { data: loanData } = detail
    const { remain, repayment, reserved } = loanData || {}
    // remain: 4589   剩余借款
    // repayment: 550 已还款
    // reserved: 5    还款中
    // total: 5144
    if (data.dynamicSupportValue && !remain && !repayment && !reserved) return null
    const { value } = this.state
    if (!value) {
      return null
    }
    return (
      <div
        className={`${styles['cardSimple_loan-wrap']} ${className}`}
        style={window.__PLANTFORM__ === 'HUAWEI' ? { marginBottom: '0.48rem' } : {}}
        onClick={this.handleOnClick}
      >
        <Painter {...value}></Painter>
      </div>
    )
  }
}
