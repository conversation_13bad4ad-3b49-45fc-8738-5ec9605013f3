import React from 'react'
export default class TripErrorBoundary extends React.Component {
    state = {
        error: null,
    };

    static getDerivedStateFromError(error) {
        return { error: error };
    }

    componentDidCatch(error, info) {

    }

    render() {
        if (this.state.error) {
            // 渲染出错时的 UI
            return <div></div>;
        }
        return this.props.children;
    }
}