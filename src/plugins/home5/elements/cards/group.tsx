import { app } from '@ekuaibao/whispered'
import _get from 'lodash/get'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'

import GIF_ON_THE_WAY from '../../images/onTheWay1.gif'
import { cardMap } from '../../util/card_map'
import styles from './group.module.less'
import { onCardClick } from './url'
import { connect } from '@ekuaibao/mobx-store'
import { hasInArray } from '../../layers/card_group_edit_modal/util'
import * as mobx from 'mobx'
import { CodeType, IMalCard, mallCardDataLength } from '../../home5.store'
const EKBIcon = app.require('@elements/ekbIcon')
import { EnhanceConnect } from '@ekuaibao/store'

export interface Props {
  cardList: any[]
  cardBacklogList: any[]
  data: any
  mallCardDataList: IMalCard[]
  isNewMall: boolean
}

@connect(store => ({
  cardList: store.states['@home5'].cardList,
  cardBacklogList: store.states['@home5'].cardBacklogList,
  mallCardDataList: store.states['@home5']?.mallCardDataList || [],
}))
  
@EnhanceConnect((state: any) => ({
  isNewMall: state['@common'].powers.newMall,
}))
export default class CardGroup extends React.Component<Props> {
  constructor(props: Props) {
    super(props)
    const { cardList = [], cardBacklogList = [] } = props
    this.state = { cardList, cardBacklogList }
  }

  handleOpenEditGroupModal = (pid?: string, groupLabel?: string) => {
    let { cardList, cardBacklogList, mallCardDataList, isNewMall } = this.props
    const showMallCard = mallCardDataList.length >= mallCardDataLength && isNewMall
    cardList = showMallCard ? cardList : cardList.filter(item => item.code !== CodeType.mall)
    cardBacklogList = showMallCard ? cardBacklogList : cardBacklogList.filter(item => item.code !== CodeType.mall)
    this.setState({ blur: true }, () => {
      api
        .open('@home5:CardGroupEditModal', { pid, label: groupLabel, cardList, cardBacklogList })
        .then((result: { data: any[]; label: string } | 'cancel') => {
          if (result === 'cancel') {
            return
          }
          // 展开变量
          const { data, label } = result
          let enlargedList = mobx.toJS(cardList) // mobx 下的 Observable Array 和各种展开操作冲突，所以这里处理下
          console.log(data, label, enlargedList)

          // 2. 会被显示的根节点卡片们（尤其包括卡片组）
          const rootCards = enlargedList
            // 所有不在分组中的卡片
            .filter(card => !card.pid && !hasInArray(data, c => c.id === card.id))
            // 重排 weight, 更新 label
            .reduce((acc, card) => {
              if (card.id === pid) {
                card.label = label
              }

              card.weight = (acc.length + 1) * 100 + 1000
              acc.push(card)
              return acc
            }, [])

          // 3. 隐藏在卡片组中的卡片们
          const leafCards = enlargedList
            // 未被编辑的卡片组的子卡片
            .filter(card => card.pid && card.pid !== pid)
            // 新增/编辑 出来的新子卡片，需要先重排 weight 并填充 pid
            .concat(
              data.reduce((acc, card) => {
                card.weight = (acc.length + 1) * 100 + 1000 // 重排 weight
                card.pid = pid // 新增卡片组的时候，子卡片并没有这个信息
                acc.push(card)
                return acc
              }, [])
            )

          // 4. 新剩余卡片
          const backlog = enlargedList
            // 原 cardList 中被剔除的卡片
            .filter(
              card => card.pid === pid && !hasInArray(data, c => c.id === card.id) // 原来在卡片分组中 // 现在不在分组中
            )
            // 上面的卡片清除 pid 信息
            // （注：这里的 map 和上面的 filter 可以使用一个 reduce 处理完，但是分开写可读性好一些，数据量少，性能没什么影响）
            .map(card => {
              card.pid = ''
              return card
            })
            // 原 backlog 中未被添加的卡片还保留
            .concat(cardBacklogList.filter(card => !hasInArray(data, c => c.id === card.id)))


          // 5. 新 cardList
          let list = rootCards.concat(leafCards)

          this.setState({ cardList: list, cardBacklogList: backlog })
          list
            .map((card, idx) => {
              // delete card.detail
              return {
                ...mobx.toJS(card),
                weight: (idx + 1) * 100 + 1000,
                detail: undefined
              }
            })
            .concat(list.filter(card => card.pid))
            .map(card => {
              card.selected = true
              return card
            })

          api.store.dispatch('@home5/changeCardList')(list, () => {
            api.store.dispatch('@home5/getCardList')()
          })
        })
    })
  }
  render() {
    const { data, isNewMall } = this.props;
    const RedDot = app.require('@elements/ekb-badge')
    return !data || !data.cards || !data.cards.length ? null : (
      <div
        className={styles['card-group-container']}
        style={window.__PLANTFORM__ === 'HUAWEI' ? { marginBottom: '0.48rem' } : {}}
      >
        <ul className="card-group-title">
          <li>{data.label === '我的常用' ? i18n.get(data.label) : data.label }</li>
          <li onClick={() => this.handleOpenEditGroupModal(data.id, data.label)}>
          <EKBIcon
          name={'#EDico-close-default'}
          className='open-edit-group-icon'
        />
          </li>
        </ul>
        <div className="card-group-body">
          {data.cards.map((card: any) => {
            let badge = getBadge(card.detail || {})
            if (isPrivateCarRunning(card) || card.code === 'waitInvoiceDetail') {
              badge = 0
            }
            const cardConfig = cardMap(card.id)
            return (
              <span className="card-group-item" key={card.id} onClick={() => onCardClick(api, card, isNewMall)}>
                <span className="card-group-avatar">
                  <GroupItemIcon card={card} />
                  <RedDot isCorner text={badge} />
                </span>
                <span className="card-group-desc">{cardConfig.label || card.label}</span>
              </span>
            )
          })}
        </div>
      </div>
    )
  }
}


/**
 * 根据后端给的 card.detail 决定要在 badge 上显示什么数值
 * 注：这里假设后端给的特定字段是 number 类型的
 * @param detail
 */
const getBadge = (detail: any): number | null => {
  const badgeType = _get(detail, ['prompt', 'type'])
  // v1 只在 NUMBER 时显示（目前遇到的数据中有 MONEY 类型的，不在 group 内的卡片上显示）
  if (badgeType === 'NUMBER') {
    return _get(detail, ['prompt', 'value']) as number
  }

  // v2 自定义的情况下，这个数字也要显示
  // 这里利用 JS 的特性，把 dataLinkCount === 0 的情况踢给了默认 return null
  if (detail.dataLinkCount) {
    return detail.dataLinkCount as number
  }

  return null
}

/**
 * 根据卡片信息返回对应的 Icon 组件
 * 注：这里特殊处理了『私车共用』在行程中时的特定 gif 图标
 */
const GroupItemIcon = React.memo(
  ({ card }: any) => {
    const EKBIcon = app.require('@elements/ekbIcon')

    return isPrivateCarRunning(card) ? (
      <img className="icon gifIcon icon-20" src={GIF_ON_THE_WAY} alt="" />
    ) : (
        <EKBIcon name={`#${card.icon}`} className="icon-24" />
      )
  },
  (p: any, n: any) => {
    return (
      p.card?.icon === n.card?.icon &&
      p.card?.code === n.card?.code &&
      p.card?.detail?.data?.state === n.card?.detail?.data?.state
    )
  }
)

const isPrivateCarRunning = (card: any) => card.code === 'privateCar' && _get(card, 'detail.data.state') === 'RUNNING'

