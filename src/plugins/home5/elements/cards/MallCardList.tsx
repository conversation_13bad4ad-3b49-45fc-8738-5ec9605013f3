import React, { FC, useRef } from 'react'
import styles from './MallCardList.module.less'
import { app as api } from '@ekuaibao/whispered'
import { IMalCard } from '../../home5.store'
import { Fetch } from '@ekuaibao/fetch'
interface IProps {
  data: any
  meInfo: any
  list: IMalCard[]
}
const MallCard: FC<IProps> = ({ data, meInfo, list }) => {
  const tokenId = useRef()
  const { staff } = meInfo
  const { name: userName, id: userId } = staff
  const { name: corpName, id: corpId } = staff?.corporationId || {}

  const fnGetParamsStr = async () => {
    const staffSetting = Fetch.staffSetting || {}
    const language = i18n.currentLocale || staffSetting.language
    let version = window.APPLICATION_VERSION
    // @ts-ignore
    let codePushVersion = window.CODEPUSH_VERSION ? window.CODEPUSH_VERSION : ''
    const token = await fnGetToken()
    return `token=${token}&language=${language}&version=${version}&codePushVersion=${codePushVersion}&refPlatform=${
      window.__PLANTFORM__
    }&refTime=${Date.now()}&corpId=${Fetch.ekbCorpId}&ekbAccessToken=${Fetch.accessToken || ''}`
  }

  const fnGetToken = async () => {
    if (!!tokenId.current) return tokenId.current
    const result = await api.invokeService('@mall:get:travel:intent:jwt', { type: 'MENU_MALL' })
    const token = result?.id ?? ''
    tokenId.current = token
    return token
  }

  const getClickName = (url: string) => {
    const urlArr = url.split('/')
    return urlArr[urlArr.length - 1] || url
  }

  const handleClickTrack = async (item: IMalCard) => {
    const { title, url } = item
    const type = /\?/.test(url) ? '&' : '?'
    const paramsStr = await fnGetParamsStr()
    const clickName = getClickName(url)
    let iframe = true
    if (window.isWxWork || window.isWebchat) {
      iframe = false
    }
    api.invokeService('@layout:open:link', `${url}${type}${paramsStr}`, undefined, undefined, iframe)
    // @ts-ignore
    api.track(clickName, {
      actionName: i18n.get(title),
      // userName,
      userId,
      // corpName,
      corpId,
      source: window.__PLANTFORM__,
      dataSource: 'EKB'
    })
  }
  return (
    <div className={styles.mallCardWrapper}>
      <div className={styles.mallCardContent}>
        <div className={styles.title}>{data.label}</div>
        <div className={styles.wrapper}>
          {list.map(item => {
            return (
              <div key={item.title} className={styles.item} onClick={() => handleClickTrack(item)}>
                <img src={item.picture} alt="" />
                <span>{item.title}</span>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default MallCard
