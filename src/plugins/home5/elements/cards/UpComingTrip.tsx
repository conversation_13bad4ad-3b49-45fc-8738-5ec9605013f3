/*
 * @Description:
 * @Creator: chencan<PERSON>han
 * @Date: 2021-07-19 10:48:41
 */
/**
 * @description 即将出发的行程
 * <AUTHOR>
 */
import { app } from '@ekuaibao/whispered'
import React from 'react'
import { Card } from '@hose/eui-mobile'
import { connect } from '@ekuaibao/mobx-store'
import styles from './UpComingTrip.module.less'
import { T } from '@ekuaibao/i18n'
import TripItem from './TripCardItem'
import { get } from 'lodash'
import TripErrorBoundry from './TripErrorBoundary'
import { EnhanceConnect } from '@ekuaibao/store'
import HomeCardRightPart from '../../util/HomeCardRight'


type IProps = {
  menuTripData?: any
  title?: string
  recentTripData?: any
  isOrder?: boolean
  tripAssistData?: any[]
  upgradeToMicroservice?: boolean
  prefixMap?: any
  isNewHome?: boolean
  isNewMall?: boolean
}
interface States {
  titleStr: string
}

@EnhanceConnect((state: any) => ({
  isNewMall: state['@common'].powers.newMall
}))
// @ts-ignore
@connect((store: any) => ({
  menuTripData: store.states['@home5'].menuTripData,
  title: store.states['@home5'].title,
  recentTripData: store.states['@home5'].recentTripData,
  isOrder: store.states['@home5']?.isOrder,
  tripAssistData: store.states['@home5'].tripAssistData,
  upgradeToMicroservice: store.states['@home5']?.upgradeToMicroservice,
  prefixMap: store.states['@home5'].prefixMap,
}))
export default class UpComingTrip extends React.Component<IProps, States> {
  private hiddenAction = false
  constructor(props: IProps) {
    super(props)
    this.state = {
      titleStr: ''
    }
  }
  componentDidMount(): void {
    if (this.props?.recentTripData) {
      const t = get(this.props?.recentTripData?.form, `订单类型`)
      this.hiddenAction = t === '用车'
      this.setTitle(this.props.title)
    } else {
      this.setTitle(this.props.title)
    }
  }

  componentWillReceiveProps(nextProps: Readonly<IProps>, nextContext: any): void {
    if (this.props.title !== nextProps.title) {
      this.setTitle(nextProps.title)
    }
  }

  setTitle = async (title: string) => {
    let titleStr = this.hiddenAction ? '即将出行' : title
    const titleObj = await app.invokeService('@common:get:translate', { title: titleStr })
    this.setState({ titleStr: titleObj?.title })
  }
  onGoTripListClick = () => {
    const { id, detail } = this.props?.menuTripData || {}
    const dataLinkEntity = get(detail, 'dataLinkEntity')
    if (!id || !dataLinkEntity) {
      return
    }
    const groupType = get(dataLinkEntity, 'platformId.groupType', '')
    const type = get(dataLinkEntity, 'platformId.type', '')
    app
      .require<any>('@importDataLink/utils')
      .call()
      .then(({ fnAikeIsSavePhone }: any) => {
        fnAikeIsSavePhone({ groupType, type }).then((res: any) => {
          if (res.isOpenNext) {
            const fields = dataLinkEntity.fields
            const obj: any = fields.find((line: any) => line.name.endsWith('_name'))
            const key = obj && obj.name
            const codeName = get(
              fields.find((item: any) => item.name.endsWith('_code')),
              'label'
            )
            const placeholder = codeName ? `${i18n.get('请输入名称或')}${codeName}` : i18n.get('搜索')
            const selectedKey = type === 'TRAVEL_MANAGEMENT' ? 'notStart' : 'all'
            app.go(
              `/mine/dataLinkList/${id}/${key}/${encodeURIComponent(placeholder)}/${selectedKey}/${type}/${true}`,
              false
            )
          }
        })
      })
    app.invokeService('@common:set:track', {
      key: 'mytrips_all_view',
      actionName: '行程集合页pv',
      from: 'next'
    })
  }

  rednerHoseTravelTips = (text: string = '无需垫资，无需开票，点此预订'): JSX.Element => (
    <div className="up_coming_trip_content_hose_tips">
      <span className="up_coming_trip_content_hose_tips_text">{text}</span>
      <span className="up_coming_trip_content_hose_tips_badge"></span>
    </div>
  )
  toMall = async () => {
    const { recentTripData } = this.props
    const code = get(recentTripData, 'code', '')
    if (code) {
      app.emit('tab:change', { key: 'mall', queryStr: `?orderNo=${code}&sticky=true&language=${i18n.currentLocale}` })
    }
  }
  render() {
    const { titleStr } = this.state
    return (
      <Card
        className={`${styles['up_coming_trip']} ${this.props.isNewHome ? `${styles['up_coming_trip_new_home']}` : ''}`}
        title={titleStr}
        extra={!this.hiddenAction && <HomeCardRightPart hasRight={true} label={i18n.get('全部')} onClick={this.toMall} />}
      >
        <TripErrorBoundry>
          <TripItem
            isNew
            from={'next'}
            isOrder={this.props?.isOrder}
            otherData={this.props?.tripAssistData}
            data={this.props?.recentTripData}
            entityPrefixForOrder={this.props?.prefixMap?.orderPrefix}
            entityPrefixForTrip={this.props?.prefixMap?.tripPrefix}
            onGoTripListClick={this.onGoTripListClick}
            upgradeToMicroservice={this.props.upgradeToMicroservice}
          />
        </TripErrorBoundry>
      </Card>
    )
  }
}
