import React from 'react'
import styles from './cardList.module.less'
import { CardProps, CodeType, IMalCard, mallCardDataLength } from '../../home5.store'
import MyBillListCard from './mybill/cardList_myBill'
import MallCardList from './MallCardList'

interface CardListProps {
  data: CardProps
  [propsName: string]: any
  me_info: any
  isHoseMall: boolean, 
  mallCardDataList: IMalCard[]
}

export default class CardList extends React.Component<CardListProps, any> {
  renderContent = () => {
    const { data, isHoseMall, mallCardDataList, me_info } = this.props
    if (data.code === CodeType.myBill) {
      // @ts-ignore
      return <MyBillListCard data={data} />
    } 
    if (data.code === CodeType.mall && isHoseMall && mallCardDataList.length >= mallCardDataLength) {
      return <MallCardList meInfo={me_info} data={data} list={mallCardDataList}/>
    }
    return <></>
  }

  render() {
    const { data } = this.props
    const { detail } = data || {}
    if (data.dynamicSupportValue && detail) {
      const { value } = detail.prompt
      if (!value) return null
    }
    return <div className={styles['cardList-wrap']}>{this.renderContent()}</div>
  }
}
