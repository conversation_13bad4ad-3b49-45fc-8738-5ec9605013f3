import React from 'react'
import styles from './cardDataBase.module.less'
import { CardProps, CodeType } from '../../home5.store'
import AuditPending from './auditpending/cardListAuditPending'

interface CardDataBaseProps {
  data: CardProps
  [propsName: string]: any
}

export default class CardDataBase extends React.Component<CardDataBaseProps, any> {
  renderContent = () => {
    const { data } = this.props
    if (data.code === CodeType.auditPending) {
      return <AuditPending data={data} />
    }
    return <></>
  }
  render() {
    const { data } = this.props
    const { detail } = data || {}
    if (data.dynamicSupportValue && detail) {
      const { value } = detail.prompt
      if (!value) return null
    }
    return <div className={styles['cardDataBase-wrap']}>{this.renderContent()}</div>
  }
}
