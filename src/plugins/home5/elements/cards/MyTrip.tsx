/**************************************
 * Created By LinK On 2019/3/4 16:28.
 **************************************/

import React from 'react'
import { planeAndTrainCard, hotelCard } from './TripCard'
import styles from './MyTrip.module.less'
import { app as api } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import { getTripCardTitle } from '../../staticUtil'

export interface Props {
  trips?: any[]
  tripType?: any
}
export interface State {}

// @ts-ignore
@connect(store => ({
  trips: store.states['@home5'].trips,
  tripType: store.states['@home5'].tripType
}))
export default class MyTrip extends React.Component<Props, State> {
  handleShowAll = () => {
    api.open('@home5:AllTripModal')
  }
  componentDidMount() {
    api.store.dispatch('@home5/getMyTrips')(1)
    api.store.dispatch('@home5/getTripType')()
  }

  render() {
    const { trips = [], tripType } = this.props
    if (trips.length === 0 || !tripType) {
      return null
    }
    const trip = trips[0].dataLink
    const type = tripType[trip.entityId]
    const isHotel = type === 'HOTEL'
    const title = getTripCardTitle(trip, isHotel)
    return (
      <div className={styles['my-trip-container']}>
        <div className="title">{i18n.get('我的行程')}</div>
        <div className="content">
          <div>{title.left}</div>
          <div>{title.right}</div>
        </div>
        {isHotel ? hotelCard(trip) : planeAndTrainCard(type, trip)}
        <div className="show-all" onClick={this.handleShowAll}>
          {i18n.get('查看全部')}
        </div>
      </div>
    )
  }
}
