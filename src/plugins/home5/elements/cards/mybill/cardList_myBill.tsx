import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import { get } from 'lodash'

import { cardDefaultValue } from '../../../home5.store'
import { mapForCodeToPath } from '../../../staticUtil'
import { cardMap } from '../../../util/card_map'
import { ListCardBottom, ListCardEmpty, ListCardHeader } from '../../view-util'

import { MyBillCardItemProps, MyBillCardProps } from './cardList_myBill.types'
export * from './cardList_myBill.types'

const myBillCardDefultValue = {
  ...cardDefaultValue,
  detail: { list: new Array<MyBillCardItemProps>(), prompt: { value: 0 } }
}

import { CardList_BillItem } from './cardList_myBill.CardList_BillItem'
export { CardList_BillItem }

import { CardList_Bill } from './cardList_myBill.CardList_Bill'
export { CardList_Bill }

import { BillQuickEntrance } from './cardList_myBill.BillQuickEntrance'
export { BillQuickEntrance }

class CardList_MyBill extends Component<MyBillCardProps, any> {
  public static defaultProps = {
    data: myBillCardDefultValue,
    paidList: new Array<any>()
  }

  componentDidMount() {
    // 获取已完成单据
    // api.store.dispatch('@home5/getArchivedAndPaidList')()
  }

  handleMyBillAll = () => {
    const { data } = this.props
    api.go(`/${mapForCodeToPath[data.code]}`, false)
  }

  renderBillList = () => {
    const { data } = this.props
    const list = get(data, 'detail.list') || []
    return <CardList_Bill list={list} />
  }

  render() {
    const { data, paidList } = this.props
    const { prompt = {} } = data?.detail ?? {}
    const { value } = prompt
    const title = cardMap(data.id).label || i18n.get(data.label)
    return (
      <>
        <ListCardHeader title={title} click={this.handleMyBillAll} />
        <BillQuickEntrance paidList={paidList} />
        {!!value ? this.renderBillList() : <ListCardEmpty title={i18n.get('暂无未完成单据')} />}
        {!!value && <ListCardBottom title={i18n.get('查看全部')} click={this.handleMyBillAll} />}
      </>
    )
  }
}

export default connect(store => ({
  paidList: store.states['@home5'].paidList
}))(CardList_MyBill)
