import './CompletedAndUnConfirmedSeg.less'
import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import EnhanceTitleHook from '../../../../../lib/EnhanceTitleHook'
const MessageListView = api.require('@home/elements/MessageListView')
const ArchivedView = api.require('@mine/mine-archived')
const ETabs = api.require<any>('@elements/EUITabs')
import { enableHidingFinishedBills } from '../../../../../lib/featbit/utils'

interface Props {
  type: string
  params: { type: string }
  segmentActiveKey: any
}

interface States {
  tabActiveKey: string
  unConfirmedTitle: string
}
@EnhanceTitleHook(i18n.get('已完成单据'))
@connect((store: any) => ({
  segmentActiveKey: store.states['@home5']?.segmentActiveKey
}))
export default class CompletedAndUnconfirmedSeg extends Component<Props, States> {
  private isHideFinishedBills: boolean

  constructor(props: Props) {
    super(props)
    let tabActiveKey = props.segmentActiveKey
    if (props.segmentActiveKey === undefined) {
      tabActiveKey = props.params.type === 'paidList' ? 'unConfirmed' : 'allFinish'
    } else if (props.segmentActiveKey === 0) {
      tabActiveKey = 'allFinish'
    } else if (props.segmentActiveKey === 1) {
      tabActiveKey = 'unConfirmed'
    }
    this.state = {
      tabActiveKey,
      unConfirmedTitle: i18n.get('未确认单据')
    }
    this.isHideFinishedBills = enableHidingFinishedBills()
  }

  componentWillMount() {
    api.watch('refresh:segment:count', this.refreshCount)
  }

  componentWillUnmount() {
    api.un('refresh:segment:count', this.refreshCount)
  }

  refreshCount = (title: string) => {
    this.setState({ unConfirmedTitle: title })
  }

  handleOnChange = (data: string) => {
    this.setState({ tabActiveKey: data }, () => {
      api.store.dispatch('@home5/setSegmentActiveKey')(this.state.tabActiveKey)
    })
  }

  renderContent = () => {
    const { tabActiveKey } = this.state
    const { params } = this.props
    if (tabActiveKey === 'allFinish') {
      return <ArchivedView isAlwaysPrint />
    } else {
      return <MessageListView params={params} isAlwaysPrint pageType={tabActiveKey}/>
    }
  }

  renderTabs() {
    const { tabActiveKey, unConfirmedTitle } = this.state
    const tabs = [
      { key: 'allFinish', tab: i18n.get('全部单据') },
      { key: 'unConfirmed', tab: unConfirmedTitle }
    ]
    return (
      <div className="segment-main">
        <ETabs
          animated={true}
          swipeable={false}
          activeKey={tabActiveKey}
          dataSource={tabs}
          onChange={this.handleOnChange}
        />
        <div className="segment-main-tab-content">{this.renderContent()}</div>
      </div>
    )
  }

  render() {
    return this.isHideFinishedBills ? <ArchivedView isAlwaysPrint /> : this.renderTabs()
  }
}
