import React, { useEffect, useRef } from 'react'
import get from 'lodash/get'
import { app as api, app } from '@ekuaibao/whispered'
import styles from './cardList_myBill.module.less'
import { OutlinedDirectionRight } from '@hose/eui-icons'
import BILL_AARCHIVED_ICON from './bill-archived.png'
import driveDoneBillsOnBoarding from '../../../../../lib/onboarding/done-bills'
import { enableHidingFinishedBills } from '../../../../../lib/featbit'

/***************************************************
 * Created by nanyuantingfeng on 2020/6/3 16:36. *
 ***************************************************/

const EKBIcon = app.require('@elements/ekbIcon')

// 前端界面上展示的叫『已完成』，但是属于『归档』类，不要和下面 complete 的说法混淆
const ArchivedBillEntrance = ({ style, newStyle = false }: { style?: any; newStyle?: boolean }) => {
  if (newStyle) {
    return (
      <div
        className={styles['cardList_myBill-archived-new-style']}
        style={style}
        onClick={() => api.go('/bills-segment/Archived', false)}
      >
        <div className="cardList_myBill-archived-content">
          <img className="myBill-archived-left-icon" src={BILL_AARCHIVED_ICON} />
          <div className="archived-title">
            <span>{i18n.get('查看已完成单据')}</span>
          </div>
          <OutlinedDirectionRight className={'archived-icon'} />
        </div>
      </div>
    )
  }
  return (
    <div
      className={styles['cardList_myBill-archived']}
      style={style}
      onClick={() => api.go('/bills-segment/Archived', false)}
    >
      <div className="archived-title">
        <span>{i18n.get('查看已完成单据')}</span>
      </div>
      <EKBIcon name="#EDico-right-default" />
    </div>
  )
}

// 下面如果看到 complete 的单子，属于操作做完，只剩下『确认』的『待确认』类别
const ConfirmBillEntrance = ({
  paidList,
  style,
  newStyle = false
}: {
  paidList: any[]
  style: any
  newStyle?: boolean
}) => {

  const handleConfirmAll = () => {
    const ids = paidList.map(item => get(item, 'flow.id'))
    api.store.dispatch('@home5/setSegmentActiveKey')(0)
    api
      .invokeService('@approve:do:confirm', ids, { name: 'freeflow.archive' })
      .then(() => {
        api.store.dispatch('@home5/getArchivedAndPaidList')()
      })
      .catch(() => {
        api.store.dispatch('@home5/getArchivedAndPaidList')()
      })
  }

  const count = paidList.length > 99 ? '99+' : paidList.length

  if (newStyle) {
    return (
      <div className={`${styles['cardList_myBill-complete-new-style']}`} style={style}>
        <div className="complete-content">
          <img className="myBill-archived-left-icon" src={BILL_AARCHIVED_ICON} />
          <div
            className="complete-title"
            onClick={() => {
              api.go('/bills-segment/paidList', false)
              api.store.dispatch('@home5/setSegmentActiveKey')(1)
            }}
          >
            <span>
              {count > 1
                ? i18n.get(`你有 {__k0} 张单据审批通过`, { __k0: count })
                : i18n.get(`你有  {__k0} 张单据审批通过`, { __k0: count })}
            </span>
          </div>
          <div className="all-confirmed" onClick={handleConfirmAll}>
            {i18n.get('全部确认')}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles['cardList_myBill-complete']} style={style}>
      <div
        className="complete-content"
        onClick={() => {
          api.go('/bills-segment/paidList', false)
          api.store.dispatch('@home5/setSegmentActiveKey')(1)
        }}
      >
        <div className="complete-title">
          <EKBIcon className="title-icon" name="#EDico-calendar" />
          <span>
            {count > 1
              ? i18n.get(`你有 {__k0} 张单据审批通过`, { __k0: count })
              : i18n.get(`你有  {__k0} 张单据审批通过`, { __k0: count })}
          </span>
        </div>
        <EKBIcon name="#EDico-right-default" />
      </div>
      <span className="complete-action" onClick={handleConfirmAll}>
        {i18n.get('全部确认')}
      </span>
    </div>
  )
}

export const BillQuickEntrance = ({
  paidList,
  style,
  newStyle = false
}: {
  paidList: any[]
  style?: any
  newStyle: boolean
}) => {
  const ref = useRef<HTMLDivElement>(null)
  const isHidingFinishedBills = enableHidingFinishedBills()

  useEffect(() => {
    driveDoneBillsOnBoarding()
  }, [])

  if (isHidingFinishedBills) {
    return null
  }

  if (paidList && paidList.length) {
    return <ConfirmBillEntrance paidList={paidList} style={style} newStyle={newStyle} />
  }
  return <ArchivedBillEntrance style={style} newStyle={newStyle} />
}

export default BillQuickEntrance
