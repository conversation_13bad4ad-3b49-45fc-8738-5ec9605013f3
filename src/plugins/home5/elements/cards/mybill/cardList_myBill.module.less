@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cardList_myBill-archived {
  height: 76px;
  flex-shrink: 0;
  user-select: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: @space-4 @space-5;
  margin: 0 @space-6;
  background-color: @color-black-2;
  border-radius: @radius-2;
  color: @color-white-1;
  transition: all 0.2s ease-in-out;
  .shadow-black-1;
  .font-size-2;
  .font-weight-2;
  &:active {
    background-color: @color-black-4;
  }
  :global {
    .archived-title {
      span {
        margin-left: @space-4;
      }
    }
  }
}

.cardList_myBill-complete {
  height: 76px;
  flex-shrink: 0;
  overflow: hidden;
  user-select: none;
  display: flex;
  margin: 0 @space-6;
  transition: height ease 0.2s;
  :global {
    .complete-content {
      display: flex;
      flex: 1;
      overflow: hidden;
      justify-content: space-between;
      align-items: center;
      padding: @space-4 @space-5;
      margin-right: @space-4;
      background-color: rgba(24, 144, 255, 0.1);
      border-radius: @radius-2;
      color: @color-inform-1;
      .font-size-2;
      .font-weight-2;
      &:hover {
        cursor: pointer;
      }
      &:active {
        background-color: @color-black-3;
      }
      .complete-title {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;
        .title-icon {
          flex-shrink: 0;
        }
        span {
          margin-left: @space-4;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
        }
      }
    }
    .complete-action {
      text-align: center;
      width: 160px;
      flex-shrink: 0;
      .font-size-2;
      .font-weight-3;
      padding: @space-4 0;
      border-radius: @space-2;
      border: 2px dashed rgba(24, 144, 255, 0.3);
      color: @color-inform-1;
      &:hover {
        cursor: pointer;
      }
      &:active {
        background-color: @color-bg-2;
      }
    }
  }
}

.cardList_myBill-complete-new-style {
  padding: 24px 32px;
  background: var(--eui-bg-body);
  :global {
    .complete-content {
      padding: 24px 32px;
      display: flex;
      flex: 1;
      overflow: hidden;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      background: #f7f8ff;
      position: relative;
      .myBill-archived-left-icon {
        width: 56px;
        height: 56px;
        position: absolute;
        left: -16px;
        top: -18px;
      }
      .complete-title {
        font: var(--eui-font-body-r1);
        color: var(--eui-primary-pri-500);
      }
      .all-confirmed {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 4px 8px;
        border-radius: 8px;
        border: 2px solid var(--eui-primary-pri-500);
        font: var(--eui-font-note-r2);
        color: var(--eui-primary-pri-500);
      }
    }
  }
}

.cardList_myBill-archived-new-style {
  padding: 24px 32px;
  background: var(--eui-bg-body);
  :global {
    .cardList_myBill-archived-content {
      padding: 24px 32px;
      display: flex;
      flex: 1;
      overflow: hidden;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      background: #f7f8ff;
      position: relative;
      .myBill-archived-left-icon {
        width: 56px;
        height: 56px;
        position: absolute;
        left: -16px;
        top: -18px;
      }
      .archived-title {
        font: var(--eui-font-body-r1);
        color: var(--eui-primary-pri-500);
      }
    }
    .archived-icon {
      color: var(--eui-primary-pri-500);
    }
  }
}

.cardList_myBill-content {
  margin-top: @space-5;
}

.cardList_BillItem-wrap {
  &:active {
    background-color: @color-bg-1;
  }
  overflow: hidden;
  display: flex;
  transition: 0.1s all ease-in-out;
  :global {
    .cardList_BillItem {
      overflow: hidden;
      flex: 1;
      padding: @space-6 0;
      margin: 0 @space-6;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .cardList_BillItem-left {
        flex: 1;
        overflow: hidden;
        //margin-right: @space-4;
        .cardList_BillItem-title {
          overflow: hidden;
          flex: 1;
          display: flex;
          align-items: center;
          .cardList_BillItem-Expedited {
            color: @color-error-1;
            margin-right: @space-2;
            flex-shrink: 0;
          }
          span {
            user-select: none;
            color: @color-black-1;
            .font-size-3;
            .font-weight-3;
            .ellipsis();
          }
        }
        .cardList_BillItem-content {
          display: flex;
          justify-content: space-between;
          padding-left: @space-6;
          margin-bottom: @space-2;
        }
        .cardList_BillItem-bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: @space-6;
          .cardList_BillItem-bottom-left {
            display: flex;
            //max-width: 220px;
          }
          .auto-create-type {
            width: 112px;
            height: 40px;
            background: rgba(29, 43, 61, 0.06);
            border-radius: 8px;
            font-size: 24px;
            font-weight: 400;
            text-align: center;
            color: rgba(29, 43, 61, 0.5);
            line-height: 40px;
            margin-left: 16px;
          }
          .cardList_BillItem-status {
            background-color: @color-warning-5;
            color: @color-warning-1;
            .font-size-1;
            padding: 0 @space-4;
            border-radius: @radius-1;
            height: 44px;
          }
        }
        .cardList_BillItem-Specification,
        .cardList_BillItem-code {
          margin-right: @space-6;
          color: @color-black-3;
          .font-size-2;
          .font-weight-2;
          .ellipsis();
        }
        .cardList_BillItem-warining {
          .font-size-1;
          margin-top: @space-1;
          color: @color-error-2;
        }
        .cardList_BillItem-log {
          user-select: none;
          color: @color-black-1;
          margin-bottom: @space-2;
          display: flex;
          align-items: center;
          .font-size-2;
          span {
            //margin-right: @space-4;
            .ellipsis();
          }
          //.plan-span {
          //  display: inline-block;
          //  margin: 0 @space-2;
          //  max-width: 200px;
          //}
          .operator-span {
            display: inline-block;
            //vertical-align: -@space-3;
            max-width: 240px;
          }
          .BillItem-State-mark {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: @radius-3;
            //vertical-align: -2px;
            margin-right: @space-4;
            &.blue {
              background-color: @color-inform-2;
            }
            &.orange {
              background-color: @color-warning-2;
            }
            &.green {
              background-color: @color-success-2;
            }
            &.brand {
              background-color: @color-brand;
            }
            &.red {
              background-color: @color-error-2;
            }
          }
        }
      }
      .cardList_BillItem-right {
        //margin-right: @space-6;
        flex-shrink: 0;
        .cardList_BillItem-money {
          padding-left: @space-6;
          span {
            user-select: none;
            color: @color-black-1;
            .font-size-3 !important;
            .font-weight-3;
          }
        }
        .cardList_BillItem-no-money {
          user-select: none;
          color: @color-black-3;
          .font-size-1;
          .font-weight-2;
        }
      }
    }
  }
}
