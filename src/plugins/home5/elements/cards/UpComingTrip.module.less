@import '~@e<PERSON><PERSON>bao/eui-styles/less/token-mobile.less';

.up_coming_trip_new_home {
  margin: 0 @space-5 @space-5 !important;
  padding: @space-6 !important;
}

.up_coming_trip {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: 0.1s all ease-in-out;
  margin: 0 @space-6;

  :global {
    .eui-card-header-title {
      color: var(--eui-text-title);
      font: var(--eui-font-head-b2);
    }

    .three<PERSON><PERSON> {
      margin-left: 110px;
      float: left;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 4px;
      border-color: transparent #cccccc transparent transparent;
      transform: rotate(180deg);
    }

    .up_coming_trip_content_hose_tips {
      position: relative;
      background: #fff7f0;
      height: 0.68rem;
      line-height: 0.68rem;
      text-align: center;
      border-radius: 0.41rem;
      margin-left: auto;
      padding: 0 0.36rem;

      &_text {
        color: #e06e00;
        font-size: 0.24rem;
      }

      &_badge {
        display: inline;
        position: absolute;
        top: -0.3rem;
        right: 0.73rem;
        width: 0;
        height: 0;
        border: 0.15rem solid transparent;
        border-bottom: 0.15rem solid #fff7f0;
      }
    }
  }
}
