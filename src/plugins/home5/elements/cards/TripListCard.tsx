/**
 * @description 首页上行程列表卡片
 * <AUTHOR>
 */
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import TripItem from './TripCardItem'
import TripErrorBoundry from './TripErrorBoundary'
import styles from './TripListCard.module.less'
import { get, isString } from 'lodash'
import { connect } from '@ekuaibao/mobx-store'
import { EnhanceConnect } from '@ekuaibao/store'
import { Card } from '@hose/eui-mobile'
import { T } from '@ekuaibao/i18n'
import HomeCardRightPart from '../../util/HomeCardRight'
const EmptyWidget = api.require('@home5/EmptyWidget')
export interface Props {
  tripData?: any[]
  entityPrefixForOrder?: any
  entityPrefixForTrip?: any
  upgradeToMicroservice?: boolean
  data?: any
  isNewMall?: boolean
}
export interface State {}
@EnhanceConnect((state: any) => ({
  isNewMall: state['@common'].powers.newMall
}))
@connect(
  store =>
    ({
      tripData: store.states['@home5'].tripData,
      entityPrefixForOrder: store.states['@home5'].entityPrefixForOrder,
      entityPrefixForTrip: store.states['@home5'].entityPrefixForTrip,
      upgradeToMicroservice: store.states['@home5']?.upgradeToMicroservice
    } as any)
)
export default class TripListCard extends React.Component<Props, State> {
  componentDidMount() {
    api.invokeService('@common:set:track', {
      key: 'mytrips_list_view',
      actionName: '列表模式行程组件的pv'
    })
  }
  renderItem = (it: any, index: number) => {
    const { entityPrefixForOrder, entityPrefixForTrip, upgradeToMicroservice } = this.props
    const obj = get(it, 'form')
    const tempArray = get(obj, `${entityPrefixForTrip}订单`, [])
    const orderArray = tempArray.filter((e: any) => !isString(e))
    if (orderArray.length > 1) {
      const arr = []
      for (let i = 0; i < orderArray.length; i++) {
        const el = orderArray[i]
        const temp: any = {}
        temp[`${entityPrefixForTrip}订单`] = [el]
        arr.push({ ...it, form: { ...obj, ...temp } })
      }
      return (
        <>
          {arr.map((itt, i) => {
            return (
              <TripItem
                from={'list'}
                key={`${itt?.createTime}-${i}`}
                data={itt}
                entityPrefixForOrder={entityPrefixForOrder}
                entityPrefixForTrip={entityPrefixForTrip}
                upgradeToMicroservice={upgradeToMicroservice}
              />
            )
          })}
        </>
      )
    } else {
      return (
        <TripItem
          from={'list'}
          key={`${it?.createTime}-${index}`}
          data={it}
          entityPrefixForOrder={entityPrefixForOrder}
          entityPrefixForTrip={entityPrefixForTrip}
          upgradeToMicroservice={upgradeToMicroservice}
        />
      )
    }
  }

  handleActionClick = () => {
    const { id, detail } = this.props?.data || {}
    const dataLinkEntity = get(detail, 'dataLinkEntity')
    if (!id || !dataLinkEntity) {
      return
    }
    const groupType = get(dataLinkEntity, 'platformId.groupType', '')
    const type = get(dataLinkEntity, 'platformId.type', '')
    api
      .require('@importDataLink/utils')
      .call()
      .then(({ fnAikeIsSavePhone }) => {
        fnAikeIsSavePhone({ groupType, type }).then((res: any) => {
          if (res.isOpenNext) {
            const fields = dataLinkEntity.fields
            const obj: any = fields.find((line: any) => line.name.endsWith('_name'))
            const key = obj && obj.name
            const codeName = get(
              fields.find((item: any) => item.name.endsWith('_code')),
              'label'
            )
            const placeholder = codeName ? `${i18n.get('请输入名称或')}${codeName}` : i18n.get('搜索')
            const selectedKey = type === 'TRAVEL_MANAGEMENT' ? 'notBuy' : 'all'
            api.go(
              `/mine/dataLinkList/${id}/${key}/${encodeURIComponent(
                placeholder
              )}/${selectedKey}/${type}/${true}`,
              false
            )
          }
        })
      })
    api.invokeService('@common:set:track', {
      key: 'mytrips_all_view',
      actionName: '行程集合页pv',
      from: 'list'
    })
  }

  render() {
    const { tripData } = this.props
    return (
      <Card 
        className={styles['card_trip_list']}
        title={<T name={'待订购'} />}
        extra={<HomeCardRightPart hasRight={true} label={i18n.get('全部')} onClick={this.handleActionClick} />}
      >
        <TripErrorBoundry>
          <div className="card_list">
            {tripData.length > 0 ? (
              tripData.map((it, index) => {
                return this.renderItem(it, index)
              })
            ) : (
              <div className="no_travel">
                <EmptyWidget size={100} type={'noTrip'} tips={i18n.get('暂无行程规划，你可以通过申请单创建行程')} />
              </div>
            )}
          </div>
        </TripErrorBoundry>
      </Card>
    )
  }
}
