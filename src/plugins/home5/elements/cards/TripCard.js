import { app } from '@ekuaibao/whispered'
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020-02-19 10:31.
 */
import styles from './TripCard.module.less'
const ARROW = app.require('@images/trip_arrow.svg')
import { getTripCardData } from '../../staticUtil'

const infoView = (type, value, showInfo, isLeft = true) => {
  return (
    <div className={isLeft ? 'layout_left' : 'layout_right'}>
      <div className="airport">{isLeft ? value.leftTop : value.rightTop}</div>
      <div className="time">{isLeft ? value.leftBottom : value.rightBottom}</div>
      {showInfo && <div className="info">{isLeft ? value.leftInfo : value.rightInfo}</div>}
    </div>
  )
}

export const planeAndTrainCard = (type, value, showInfo = false) => {
  const newValue = getTripCardData(type, value)
  return (
    <div className={styles.plane_and_train_card_wrapper}>
      <div className="card-content-layout">
        {infoView(type, newValue, showInfo)}
        <div className="layout_center">
          <div className="number">{newValue.centerTop}</div>
          <img src={ARROW} />
          <div className="duration">{newValue.centerBottom}</div>
        </div>
        {infoView(type, newValue, showInfo, false)}
      </div>
      <div className="card-bottom">
        <div className="seat">{newValue.bottom}</div>
      </div>
    </div>
  )
}

export const hotelCard = value => {
  return (
    <div className={styles.hotel_card_wrapper}>
      {value[Object.keys(value).find(o => !!o.endsWith('酒店地址'))]} {/* @i18n-ignore */}
    </div>
  )
}
