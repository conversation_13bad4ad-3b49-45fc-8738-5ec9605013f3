@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.mallCardWrapper {
  .mallCardContent {
    padding: @space-6 @space-5 0 @space-6;
    width: 100%;
    background: #ffffff;
    border-radius: @radius-3;
    .title {
      user-select: none;
      color: @color-black-1;
      .font-size-4;
      .font-weight-3;
      &:active {
        background-color: @color-bg-2;
      }
    }
    .wrapper {
      display: flex;
      align-items: center;
      margin-top: 0.4rem;
      flex-wrap: wrap;
      .item {
        display: flex;
        flex-direction: column;
        width: 25%;
        height: 100%;
        align-items: center;
        margin-bottom: 0.24rem;
        overflow: hidden;
        img {
          width: 0.6rem;
          height: 0.6rem;
          margin-bottom: 0.16rem;
        }
        span {
          .font-size-2;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}
