@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.card-group-container {
  box-sizing: border-box;
  margin: @space-5 @space-6;
  padding: @space-6;
  border: 0;
  border-radius: @radius-3;
  background-color: @color-white-1;

  display: flex;
  flex-direction: column;

  :global {
    * {
      user-select: none;
    }
    .card-group-title {
      color: @color-black-1;
      .font-size-4;
      .font-weight-3;
      display: flex;
      justify-content: space-between;
      .open-edit-group-icon{
        height: 0.4rem;
        color: @color-brand;
        transform: rotate(45deg);
        cursor: pointer;
      }
    }
    .card-group-body {
      margin-top: @space-7;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: flex-start;
      .card-group-item {
        display: flex;
        flex-direction: column;
        position: relative;
        box-sizing: border-box;
        width: 25%;
        min-width: 25%;
        min-height: 168px;
        padding-bottom: 18px;
        flex: 0;
        text-align: center;
        &:hover {
          cursor: pointer;
        }
        &:active {
          background-color: @color-bg-2;
          border-radius: @radius-2;
        }
        .card-group-badge {
          text-rendering: optimizeLegibility;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          position: absolute;
          box-sizing: border-box;
          top: 0;
          right: 0;
          transform: translate(45%, -45%);
          background-color: #f4526b;
          color: @color-white-1;
          height: 40px;
          min-width: 40px;
          padding: 0 @space-3;
          border-radius: 20px;
          .font-size-1;
          text-align: center;
          z-index: 10;
        }
        .ekb-badge-text {
          height: 40px;
          min-width: 40px;
          left: unset;
          right: -10px;
          border: 0;
          //top: -6px;
        }
        .card-group-avatar {
          position: relative;
          background: @color-bg-2;
          height: 0;
          display: block;
          width: 80px;
          padding-bottom: 80px;
          line-height: 80px;
          margin: 0 auto @space-3;
          border-radius: @radius-5;
          svg, img {
            display: inline-block;
            vertical-align: middle;
          }
        }
        .card-group-desc {
          width: 100%;
          padding: 0 @space-1;
          .font-size-2;
          overflow:hidden;
          text-overflow:ellipsis;
          display:-webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp:3;
        }
      }
    }
  }
}