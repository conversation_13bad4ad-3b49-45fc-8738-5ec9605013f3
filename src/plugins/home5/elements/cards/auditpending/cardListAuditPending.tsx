import { app } from '@ekuaibao/whispered'
/**************************************
 * Created By LinK On 2019/3/5 14:20.
 **************************************/
import React from 'react'
import styles from './cardListAuditPending.module.less'
//@ts-ignore
import Highlighter from 'react-highlight-words'
import { cardDefaultValue, CardProps } from '../../../home5.store'
import { MyBillCardItemProps } from '../mybill/cardList_myBill'
import { ListCardBottom, ListCardHeader } from '../../view-util'
import { get } from 'lodash'
import { getDateDiff } from '../../../../../lib/util'
import { mapForCodeToPath, Home5CardType } from '../../../staticUtil'
import { app as api } from '@ekuaibao/whispered'
import moment from 'moment'
import { cardMap } from '../../../util/card_map'
import { getStaffShowByConfig } from '../../../../../components/utils/fnDataLinkUtil'

interface DataItemProps extends StringAnyProps {
  title: string
  count: number
  filter: string
}

export interface AuditDataProps {
  [propName: number]: DataItemProps
}

interface StaffProps extends StringAnyProps {
  name: string
  id: string
  avatar: string
}

interface FormProps extends StringAnyProps {
  title: string
  submitterId: StaffProps
}

interface FlowProps extends StringAnyProps {
  form: FormProps
}

export interface AuditItemProps extends StringAnyProps {
  flowId: FlowProps
}

interface AuditPendingProps extends StringAnyProps {
  data: CardProps
  hideAvatar?: boolean
}

const auditPendingDefultValue = {
  ...cardDefaultValue,
  detail: { list: new Array<MyBillCardItemProps>(), prompt: { value: 0 }, data: new Array<DataItemProps>() }
}

export const ListItem = (props: {
  el: AuditItemProps
  idx: number
  hideAvatar: boolean
  onClick?: Function
  searchValue?: string
  approveShowState?: boolean
  extraClassName?: string
  searchFrom?: string
}) => {
  const EKBIcon = app.require('@elements/ekbIcon')
  const Money = app.require('@elements/puppet/Money')
  const SVG_AVATAR_NULL = app.require('@images/avatar-null.svg')
  const isUrgent = api.require('@bill/bill-content/bill-item.isUrgent')

  const { el, idx, hideAvatar = false, onClick, searchValue, approveShowState, extraClassName = '' } = props
  const title = get(el, 'flowId.form.title', '')
  const isRiskWarning = get(el, 'flowId.calcRiskWarning', '') || get(el, 'calcRiskWarning', '')
  const submitter = get(el, 'flowId.form.submitterId', '')
  const submitDate = get(el, 'flowId.form.submitDate', '')
  const form = get(el, 'flowId.form', '')
  const formType = get(el, 'flowId.formType', '')
  const specificationName = get(el, 'flowId.form.specificationId.name', '')
  const code = get(el, 'flowId.form.code', '')
  const deleteTime = get(el, 'flowId.deleteTime', '')
  const amount = form.amount ? form.amount : form[(formType === 'payment' ? 'pay' : formType) + 'Money'] || null
  const handleGotoDetail = (data: AuditItemProps) => {
    api.invokeService('@home:save:specification').then(() => {
      api.go('/approve/approving/expense/' + data.id + '/approving', false)
    })
  }
  const logs = get(el, 'flowId.logs', [])
  const lastLast = (logs.length && logs[logs.length - 1]) || {}
  const { action } = lastLast
  const shouldShowState = typeof approveShowState === 'boolean'
  const urgent = typeof el.isUrgent === 'boolean' ? el.isUrgent : isUrgent(logs)
  const auto = get(el, 'flowId.form.systemGeneration')
  const subsidyGeneration = get(el, 'flowId.form.subsidyGeneration') === 'surplus'
  const showRiskIcon = isRiskWarning && isRiskWarning !== null && isRiskWarning.status === 'HAVE'
  return (
    <>
      {
        props.searchFrom === 'recycle' ?
          <div
            key={idx}
            className={`${styles['billList-wrap']} ${styles['recycle-billList-wrap']} ${idx === 0 ? styles['filter-wrap'] : ''} ${extraClassName}`}
            onClick={onClick || handleGotoDetail.bind(this, el)}
          >
            {hideAvatar ? null : <img src={submitter.avatar || SVG_AVATAR_NULL} />}
            <div className="bill-content-recycle">
              <Highlighter
                className="bill-content-recycle-top"
                highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                searchWords={[searchValue]}
                textToHighlight={title || i18n.get('[无标题]')}
              />
              <div className="bill-content-recycle-bottom">
                <span>{`${code} ${specificationName}`}</span>
                <span>{deleteTime && moment(deleteTime).format('YYYY-MM-DD')}</span>
              </div>
            </div>
          </div>
           :
          <div
            key={idx}
            className={`${styles['billList-wrap']} ${idx === 0 ? styles['filter-wrap'] : ''} ${extraClassName}`}
            onClick={onClick || handleGotoDetail.bind(this, el)}
          >
            {hideAvatar ? null : <img src={submitter.avatar || SVG_AVATAR_NULL} />}
            <div className="bill-content">
              <div className="bill-content-top">
                <span>{getStaffShowByConfig(submitter)}</span>
                <span>{i18n.get('提交')}</span>
                <span>{getDateDiff(submitDate)}</span>
              </div>
              <div className="bill-content-middle">
              <span>
                {urgent && <EKBIcon name="#EDico-expedited" className="urgent-color" />}
                {searchValue ? (
                  showRiskIcon ? (
                    <>
                      <EKBIcon name="#EDico-plaint-circle" style={{ color: '#fa8c16' }} />
                      <span>
                        <Highlighter
                          highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                          searchWords={[searchValue]}
                          textToHighlight={title || i18n.get('[无标题]')}
                        />
                      </span>
                    </>
                  ) : (
                    <Highlighter
                      highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                      searchWords={[searchValue]}
                      textToHighlight={title || i18n.get('[无标题]')}
                    />
                  )
                ) : showRiskIcon ? (
                  <>
                    <EKBIcon name="#EDico-plaint-circle" style={{ color: '#fa8c16' }} /> <span>{title}</span>
                  </>
                ) : (
                  title
                )}
              </span>
                <span>
                {amount && Number(amount.standard) !== 0 ? (
                  <Money value={amount} showShorthand={true} />
                ) : (
                  i18n.get('暂无金额')
                )}
              </span>
              </div>
              <div className="bill-content-text">
                <div className="bill-content-bottom">{`${code} ${specificationName}`}</div>
                {(auto || subsidyGeneration) && <div className="auto-create-type">自动创建</div>}
              </div>
              {shouldShowState && !approveShowState && action === 'freeflow.failure' && (
                <div className="bill-content-warining">{i18n.get('支付失败')}</div>
              )}
            </div>
          </div>
      }
    </>
  )
}

export default class CardListAuditPending extends React.Component<AuditPendingProps, any> {
  static defaultProps = {
    data: auditPendingDefultValue,
    hideAvatar: false
  }

  componentDidMount() {
    const { filterEnum } = api.require('@approve/filterEnum')

    api.invokeService('@approve:get:filter:setting', {
      filterType: filterEnum.filters.approve
    })
  }

  componentWillUnmount() {}

  handleAuditPending = (item?: AuditItemProps) => {
    const { data } = this.props
    //通过 filter (条件)区分是否点击的是查看全部
    if (item && item.filter) {
      const filter = JSON.parse(item.filter)
      const filterType = item.isUrgent ? 'all' : filter.sceneIndex
      api.invokeService('@approve:setSelectBillsType', filterType)
    } else {
      api.invokeService('@approve:setSelectBillsType', 'all')
    }
    api.go(`/${mapForCodeToPath[data.code]}`, false)
  }

  handleGotoDetail = (data: AuditItemProps) => {
    api.invokeService('@home:save:specification').then(() => {
      api.go('/approve/approving/expense/' + data.id + '/approving', false)
    })
  }

  //列表
  renderBillList = () => {
    const NOT_BILLS = app.require('@images/not-bills.svg')
    const { data, hideAvatar } = this.props
    const { list = [] } = data?.detail || {}
    return (
      <>
        {list.length > 0 ? (
          list.map((el: AuditItemProps, idx: number) => (
            <ListItem el={el} idx={idx} key={idx} hideAvatar={hideAvatar} />
          ))
        ) : (
          <div className={styles['not-bills']}>
            <img src={NOT_BILLS} />
            {i18n.get('暂无待我审批的单据')}
          </div>
        )}
      </>
    )
  }

  //列表上的 filter 加急什么的
  renderFilter = () => {
    const {
      data: { detail }
    } = this.props
    let style: any =
      detail.data.length === 3 ? { justifyContent: 'center' } : detail.data.length ? {} : { paddingBottom: 32 }
    if (!detail.list.length) {
      style['paddingBottom'] = 32
    }
    return (
      <div className={styles['filter-wrap']} style={style}>
        {detail.data.length > 0 &&
          detail.data.map((data: any, idx: number) => {
            const urgent = data.isUrgent && idx === 0 ? 'isUrgent' : !data.isUrgent && idx === 0 ? 'custom' : ''
            return (
              <div key={idx} className={`filter-item ${urgent}`} onClick={this.handleAuditPending.bind(this, data)}>
                <span className="filter-item-top">{data.count > 999 ? '999+' : data.count}</span>
                <span className="filter-item-bottom">{data.title}</span>
              </div>
            )
          })}
      </div>
    )
  }

  renderBottom = () => {
    const {
      data: {
        detail: {
          prompt: { value }
        }
      }
    } = this.props
    if (!value) return <></>
    return <ListCardBottom title={i18n.get('查看全部')} click={() => this.handleAuditPending()} />
  }

  render() {
    const { data } = this.props
    const { list = [] } = data?.detail || {}
    const title = cardMap(data.id).label || i18n.get(data.label)
    return (
      <>
        <ListCardHeader click={this.handleAuditPending} title={title} />
        {/* {(data.showType === Home5CardType.alldata || data.showType === Home5CardType.database) && this.renderFilter()} */}
        {data.showType === Home5CardType.alldata && this.renderBillList()}
        {list.length > 0 && this.renderBottom()}
      </>
    )
  }
}
