@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';
@import '../../../../../styles/layout.less';

.filter-wrap {
  display: flex;
  justify-content: flex-start;
  padding: 0 @space-6;
  flex: 1;
  :global {
    .filter-item {
      &:last-child {
        margin-right: 0;
      }
      &:active {
        transform: scale(0.97);
        box-shadow: none;
        background-color: @color-bg-2;
      }
      &.isUrgent {
        background: @color-error-2;
        .shadow-error-3;
        color: @color-white-1;
        .filter-item-bottom {
          color: @color-white-1;
        }
      }
      &.custom {
        background: @color-inform-2;
        color: @color-white-1;
        .filter-item-bottom {
          color: @color-white-1;
        }
      }
      width: 33%;
      height: 176px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-right: @space-4;
      background: @color-white-1;
      .shadow-black-2;
      border-radius: @radius-3;
      justify-content: center;
      padding-left: @space-6;
      .filter-item-top,
      .filter-item-bottom {
        .font-weight-2;
      }
      .filter-item-top {
        .font-size-6;
      }
      .filter-item-bottom {
        .font-size-2;
        color: @color-black-2;
        .word-break();
        width: 140px;
      }
    }
  }
}
.recycle-billList-wrap {
  padding: 0 !important;
}
.billList-wrap {
  display: flex;
  justify-content: flex-start;
  padding: 0 @space-6;
  &:active {
    background-color: @color-bg-2;
  }
  > img {
    width: 48px;
    height: 48px;
    margin-top: @space-5;
    border-radius: @radius-2;
  }

  :global {
    .bill-content {
      display: flex;
      flex: 1;
      margin-left: @space-5;
      flex-direction: column;
      padding: @space-5 0;
      overflow: hidden;
      .bill-content-top,
      .bill-content-middle,
      .bill-content-bottom {
        display: flex;
        justify-content: space-between;
        color: @color-black-1;
      }
      .bill-content-text {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .auto-create-type {
          width: 112px;
          height: 40px;
          background: rgba(29, 43, 61, 0.06);
          border-radius: 8px;
          font-size: 24px;
          font-weight: 400;
          text-align: center;
          color: rgba(29, 43, 61, 0.5);
          line-height: 40px;
          margin-left: 16px;
        }
      }
      .bill-content-warining {
        .font-size-1;
        margin-top: @space-1;
        color: @color-error-2;
      }
      .bill-content-top {
        .font-size-2;
        .font-weight-2;
        color: @color-black-1;
        > span:nth-child(2) {
          flex: 1;
        }
        > span:first-child {
          max-width: 200px;
          .word-break();
        }
        > span:last-child {
          .font-size-1;
        }
        .just {
          color: @color-warning-2;
        }
      }
      .bill-content-middle {
        margin: @space-2 0;
        color: @color-black-1;
        .font-size-3;
        > span:first-child {
          .font-weight-3;
          overflow: hidden;
          flex: 1;
          .word-break();
          .urgent-color {
            color: @color-error-1;
            margin-right: @space-2;
            width: 36px;
            height: 36px;
          }
        }
        > span:last-child {
          .font-weight-3;
        }
      }
      .bill-content-bottom {
        .font-weight-2;
        .font-size-2;
        .word-break();
        color: @color-black-3;
        display: inline-block;
        max-width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    .bill-content-recycle {
      display: flex;
      flex: 1;
      flex-direction: column;
      padding: @space-5 0;
      overflow: hidden;
      .bill-content-recycle-top {
        color: @eui-ref-color-grey;
        margin: @space-2 0;
        font-size: 28px;
        font-weight: 500;
        overflow: hidden;
        flex: 1;
        .word-break();
        .active {
          .font-weight-3 !important;
          color: rgb(245, 34, 45) !important;
        }
      }
      .bill-content-recycle-bottom {
        display: flex;
        justify-content: space-between;
        color: @eui-sys-neutral-grey-3;
        .font-weight-2;
        font-size: 24px;
        > span:first-child {
          .word-break();
          color: rgba(39, 46, 59, 0.8);
          display: inline-block;
          max-width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
}



.payment-review-list-wrap {
  &:active {
    background: rgba(39, 46, 59, 0.03);
  }

  > img {
    border-radius: 8px !important;
  }

  :global {
    .bill-content {
      padding: 32px 0;
      .bill-content-top {
        font-weight: 500;
        font-size: 28px;
        line-height: 40px;
        color: #272E3B;
        > span:last-child {
          font-size: 32px;
          line-height: 44px;
        }
      }
    
      .bill-content-middle {
        > span {
          font-size: 24px;
          line-height: 36px;
          font-weight: normal !important;
          &:first-of-type {
            color: rgba(39, 46, 59, 0.8);
          }
          &:last-of-type {
            color: rgba(39, 46, 59, 0.48);
          }
        }
      }
    
      .bill-content-bottom {
        margin-top: 10px;
        max-width: 100%;
        font-size: 24px;
        line-height: 36px;
        color: rgba(39, 46, 59, 0.8);
        overflow: auto;
        white-space: unset;

        .account-name {
          display: inline;
        }

        .divder {
          color: #e5e6e7;
        }
      }
    }
  }
}

.not-bills {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: @space-6;
  min-height: 212px;
  .font-weight-2;
  .font-size-2;
  color: @color-black-4;
  > img {
    margin-bottom: @space-5;
  }
}
