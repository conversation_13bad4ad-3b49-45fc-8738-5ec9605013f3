import { app } from '@ekuaibao/whispered'
/**************************************
 * Created By LinK On 2019/3/5 14:20.
 **************************************/

import React from 'react'
import styles from './cardListMyRequisition.module.less'
// import EKBIcon from '../../../../../elements/ekbIcon'
const EKBIcon = app.require('@elements/ekbIcon')
import { mapForCodeToPath } from '../../../staticUtil'
import { app as api } from '@ekuaibao/whispered'
import { cardMap } from '../../../util/card_map'

interface MyRequisition {
  data: any
}

export default class CardListMyRequisition extends React.Component<MyRequisition, any> {
  gotoDetail = () => {
    const { data } = this.props
    api.go(`/${mapForCodeToPath[data.code]}`, false)
  }

  render() {
    const { data } = this.props
    const { detail } = data || {}
    const title = cardMap(data.id).label || data.label
    if (data.dynamicSupportValue && detail) {
      const { value } = detail.prompt
      if (!value) return null
    }
    return (
      <div className={styles['cardSimple-wrap']} onClick={this.gotoDetail}>
        <div className="top-img">
          <p className="top-count">
            {detail.prompt.value > 99 ? '99+' : detail.prompt.value}
            <span>{i18n.get('条')}</span>
          </p>
        </div>
        <div className="bottom-card-simple">
          <span className="bottom-card-simple-left">{title}</span>
          <span className="bottom-card-simple-right">
            {i18n.get('查看详情')}
            <EKBIcon name="#EDico-right-default" />
          </span>
        </div>
      </div>
    )
  }
}
