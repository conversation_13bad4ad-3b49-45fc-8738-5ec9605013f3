@import '~@ekuaibao/eui-styles/less/token-mobile.less';


.cardSimple-wrap {
  &:active {
    transform: scale(1);
    background-color: @color-bg-2;
  }
  display: flex;
  align-items: center;
  flex-direction: column;
  margin: @space-5 @space-6;
  padding: @space-6 @space-5 @space-6 @space-6;
  border-radius: @radius-3;
  background-color: @color-white-1;
  transition: 0.1s all ease-in-out;
  :global {
    .top-img {
      background-image: url(../../../../../images//myRequisition.jpg);
      background-position: center;
      background-size: cover;
      width: 100%;
      height: 176px;
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
      border-top-left-radius: @radius-3;
      border-top-right-radius: @radius-3;
      .top-count {
        .font-size-6;
        .font-weight-2;
        color: @color-white-1;
        padding: 0 @space-7 36px 0;
        > span {
          .font-size-3;
        }
      }
    }
    .bottom-card-simple {
      align-items: center;
      padding: @space-6 @space-6 0 @space-6;
      display: flex;
      width: 100%;
      justify-content: space-between;
      .bottom-card-simple-left {
        .font-size-4;
        .font-weight-3;
        color: @color-black-1;
      }
      .bottom-card-simple-right {
        color: @color-brand-2;
        .font-size-2;
        .icon {
          color: @color-brand-2;
        }
      }
    }
  }
}
