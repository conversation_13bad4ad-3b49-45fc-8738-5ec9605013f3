import styles from '../auditpending/cardListAuditPending.module.less'
import React, { FC } from 'react'
import { app } from '@ekuaibao/whispered'
import Highlighter from 'react-highlight-words'
import { getDateDiff, getSubStrAccountNo } from '../../../../../lib/util'
import { getStaffShowByConfig } from '../../../../../components/utils/fnDataLinkUtil'
import { PayeeInfoIF } from '@ekuaibao/ekuaibao_types'

const Money: any = app.require('@elements/puppet/Money')

export interface ReviewItemProps {
  id: string
  balance: any
  batchId: string
  payTime: number
  payerId: string
  account: PayeeInfoIF
  channelTradeNo: string
}

interface ICardListItem {
  el: ReviewItemProps
  idx: number
  onClick?: Function
  searchFrom?: string
  searchValue?: string
  extraClassName?: StringConstructor
  [key: string]: any
}

const CardListItem: FC<ICardListItem> = props => {
  const { el, idx, onClick, searchValue, extraClassName = '' } = props

  const handleGotoDetail = (data: ReviewItemProps) => {
    app.go('/paymentReviewDetails' + data.id, false)
  }

  return (
    <div
      key={el?.id}
      className={`${styles['billList-wrap']} ${idx === 0 ? styles['filter-wrap'] : ''} ${
        styles['payment-review-list-wrap']
      } ${extraClassName}`}
      onClick={onClick || handleGotoDetail.bind(this, el)}
    >
      <div className="bill-content">
        <div className="bill-content-top">
          <span>{getStaffShowByConfig(el?.payerId)}</span>
          <span>{i18n.get('发起支付')}</span>
          <span>
            {el?.balance && Number(el?.balance?.standard) !== 0 ? (
              <Money value={el?.balance} showShorthand={true} />
            ) : (
              i18n.get('暂无金额')
            )}
          </span>
        </div>
        <div className="bill-content-middle">
          <span>
            {searchValue ? (
              <Highlighter
                highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                searchWords={[searchValue]}
                textToHighlight={i18n.get('批次号：') + el?.channelTradeNo}
              />
            ) : (
              i18n.get('批次号：') + el?.channelTradeNo
            )}
          </span>
          <span>{getDateDiff(el?.payTime)}</span>
        </div>
        <div className="bill-content-text">
          <div className="bill-content-bottom">
            <span className="account-name">{el?.account?.accountName}</span>
            <span className="divder">｜</span> {el?.account?.bank}
            {el?.account?.accountNo?.length ? (
              <span className="accountNo-cut">({getSubStrAccountNo(el?.account?.accountNo, 4)})</span>
            ) : (
              void 0
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CardListItem
