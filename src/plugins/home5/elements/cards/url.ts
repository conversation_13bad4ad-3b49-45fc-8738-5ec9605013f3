import get from 'lodash/get'
import { mapForCodeToPath } from '../../staticUtil'
import { CodeType } from '../../home5.store'
import { fnClickMyCarBusiness } from '../../../home/<USER>/util'
import { Fetch } from '@ekuaibao/fetch'
import { goToECardPage } from '../../util/SimpleCardUtil'
export const onCardClick = (api: any, data: any, isNewMall: boolean) => {
  if (data?.actions?.[0]?.url) {
    const url = encodeURIComponent(data?.actions?.[0]?.url)
    const link = `${location.origin}/api/authorization/v1/redirectWithCode?redirectUrl=${url}&corpId=${Fetch.ekbCorpId}&accessToken=${Fetch.accessToken}`
    api.invokeService('@layout:open:link', link, false, false, false)
    return
  }
  switch (data.code) {
    case 'privateCar':
      fnClickMyCarBusiness(data)
      break
    case 'auditPending':
    case 'auditPayment':
    case 'waitSending':
    case 'receiveExpress':
    case 'auditCarbonCopy':
    case 'myBill':
    case 'approvePermission':
      api
        .invokeService('@approve:set:select:billsType', '')
        .then(() => api.go(`/${mapForCodeToPath[data.code]}`, false))
      break
    case 'recordExpends':
      api.go('/record-expends', false)
      break
    case 'quickExpense':
      api.go('/quick-expense', false)
      break
    case 'customMessage':
      api.go(`/message-center/${data.code}`, false)
      break
    case CodeType.approve:
      api.store.dispatch('@home5/redirectApproveOA')()
      break
    case CodeType.completed: // 已完成
      api.go('/bills-segment/Archived')
      break
    case CodeType.approved: // 已审批
      api.go('/mine/approved')
      break
    case CodeType.paymentHistory: //  还款记录
      api.go('/mine/loan')
      break
    case CodeType.dataLinkEntity:
      const {
        id,
        detail: { dataLinkEntity }
      } = data || {}
      if (!id) {
        return
      }
      const fields = dataLinkEntity.fields
      const type = get(dataLinkEntity, 'platformId.type', '')
      const obj: any = fields.find((line: any) => line.name.endsWith('_name'))
      const key = obj && obj.name
      const codeName = get(
        fields.find((item: any) => item.name.endsWith('_code')),
        'label'
      )
      const placeholder = codeName ? `${i18n.get('请输入名称或')}${codeName}` : i18n.get('搜索')
      const selectedKey = type === 'TRAVEL_MANAGEMENT' ? 'notStart' : 'all'
      api.go(`/mine/dataLinkList/${id}/${key}/${encodeURIComponent(placeholder)}/${selectedKey}/${type}`, false)
      break
    case CodeType.mall:
      api.go(isNewMall ? '/mallIframe' : '/mall', true)
      api.store.dispatch('@layout/setActiveTab')('mall')
      break
    case CodeType.eCard:
      goToECardPage()
      break
    default:
      api.go(`/${mapForCodeToPath[data.code]}`, false)
  }
}
