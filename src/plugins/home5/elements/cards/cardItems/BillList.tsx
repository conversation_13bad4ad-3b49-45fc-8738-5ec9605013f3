/**
 *  Created by pw on 2022/11/3 8:25 PM.
 */
import React, { useState, useEffect } from 'react'
import './BillList.less'
import { get } from 'lodash'
import { app } from '@ekuaibao/whispered'
import classNames from 'classnames'
import Highlighter from 'react-highlight-words'
import { getMyBillApproveStatus } from '../../../util/myBIllStatus'
import { FlowTypeEnum } from './enums'
import { transformData } from './transform'
import { Checkbox } from '@hose/eui-mobile'
const { getSpecificationName } = app.require<any>('@bill/utils/billUtils')
import { enableHidingFinishedBills } from '../../../../../lib/featbit/utils'
import { SourceSignEnum } from '../../../../../lib/enums'

const EKBIcon = app.require<any>('@elements/ekbIcon')
const Money = app.require<any>('@elements/puppet/Money')

interface Props {
  list: any[]
  searchValue?: string
  flowType?: FlowTypeEnum
  onClickItem?: (data: any) => void
  applyType?: 'list' | 'card'
  selectAble?: boolean
  style?: any
  onCheckedChange?: (params: { checked: boolean; data: any }) => void
  showEntrustMark?: boolean
}

export const BillListWrapper: React.FC<Props> = props => {
  const {
    list = [],
    searchValue,
    flowType,
    onClickItem,
    applyType = 'card',
    onCheckedChange,
    selectAble,
    style,
    showEntrustMark
  } = props
  if (!list.length) {
    return null
  }

  return (
    <div className="card-list-bill-container" style={style}>
      {list.map((el, idx) => (
        <BillItem
          key={idx}
          data={el}
          searchValue={searchValue}
          flowType={flowType}
          applyType={applyType}
          selectAble={selectAble}
          onClickItem={onClickItem}
          onCheckedChange={onCheckedChange}
          showEntrustMark={showEntrustMark}
        />
      ))}
    </div>
  )
}

interface ICardListBillItem {
  id?: string
  data: any
  searchValue?: string
  needOperatorFormPlan?: boolean
  isAlwaysPrint?: boolean
  flowType?: FlowTypeEnum
  applyType?: 'list' | 'card'
  onClickItem?: (data: any) => void
  selectAble?: boolean
  checked?: boolean
  onCheckedChange?: (params: { checked: boolean; data: any }) => void
  bus?: any
  showEntrustMark?: boolean
}

export const BillItem: React.FC<ICardListBillItem> = props => {
  const {
    data,
    searchValue,
    needOperatorFormPlan,
    isAlwaysPrint = false,
    flowType,
    applyType = 'card',
    selectAble,
    checked,
    onClickItem,
    onCheckedChange,
    bus,
    showEntrustMark = false
  } = props

  const [isNotAllowBatchApprove, setIsNotAllowBatchApprove] = useState(false)
  const refreshNotAllowBatchApprove = () => {
    if (bus && bus.has('get:bill:isNotAllowBatchApprove')) {
      if (data?.flowId?.backlogId) {
        bus.invoke('get:bill:isNotAllowBatchApprove', data?.flowId?.backlogId).then((isNotAllow: boolean) => {
          if (isNotAllow) {
            handleCheckedChange(false)
          }
          setIsNotAllowBatchApprove(isNotAllow)
        })
      }
    }
  }

  useEffect(() => {
    bus && bus.on('bill:item:refresh:notAllowBatchApprove', refreshNotAllowBatchApprove)
    return () => {
      bus && bus.un('bill:item:refresh:notAllowBatchApprove', refreshNotAllowBatchApprove)
    }
  }, [])

  const {
    id,
    state,
    payingFailure,
    urgent,
    title,
    isRiskWarning,
    code,
    amount,
    color,
    auto,
    alterFlag,
    subsidyGeneration,
    dateStr,
    type,
    staff,
    specification,
    sourceSign
  } = transformData(data, flowType)

  const handleCheckedChange = (checked: boolean) => {
    onCheckedChange && onCheckedChange({ checked, data: data?.flowId ? data?.flowId : data })
  }

  let t = title || i18n.get('[无标题]')
  if (searchValue) {
    t = (
      <Highlighter
        highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
        searchWords={[searchValue]}
        textToHighlight={t}
      />
    )
  }

  const handleClickItem = () => {
    if (onClickItem) {
      onClickItem({ id, formType: type, state, isAlwaysPrint, data })
    }
  }

  const operator = data.operator ? data.operator : staff ? { staff } : { staff: {} }
  const { name, str, staffAdditionInfo } = getMyBillApproveStatus({
    flow: { state },
    form: data.form,
    plan: data.plan || {},
    operator,
    needOperatorFormPlan,
    useStaffName: flowType === FlowTypeEnum.Backlog,
    needStaffObject: true
  } as any)

  let showMark = false
  const samePerson = data?.flow?.ownerId?.id === data?.form?.submitterId?.id ? true : false
  if (!samePerson && showEntrustMark) showMark = true

  return (
    <div
      className={classNames('card-bill-item-wrapper', {
        'card-bill-item-wrapper-card': applyType === 'card',
        'card-bill-item-wrapper-list': applyType === 'list',
        'card-bill-item-wrapper-selected': checked
      })}
    >
      <div className="card-bill-item-left">
        {selectAble && (
          <Checkbox className="check-box" checked={checked} key={data.id} onChange={handleCheckedChange} />
        )}
      </div>
      <div className="card-bill-item-right" onClick={handleClickItem}>
        <div className={'bill-info-title-wrapper'}>
          <div className={'left'}>
            {urgent && <div className="bill-info-urgent">{i18n.get('[急]')}</div>}
            {payingFailure && <div className="bill-info-pay-failure">{`[${i18n.get('支付失败')}]`}</div>}
            {alterFlag && <div className="bill-info-tag-placeholder">[变更]</div>}
            {(auto || subsidyGeneration) && <div className="bill-info-tag-placeholder">[自动创建]</div>}
            {sourceSign === SourceSignEnum.SYSTEM && (
              <div className="bill-info-tag-placeholder">{`[${i18n.get('系统')}]`}</div>
            )}
            <div className="ekb-highlight-box">
              {showMark && <span className="entrust-mark">{i18n.get('[委托]')}</span>}
              <div className="bill-info-title">
                <span>{t}</span>
              </div>
            </div>
          </div>
          {str && flowType !== FlowTypeEnum.Backlog && (
            <div
              className={classNames('right', {
                'right-card': applyType === 'card',
                'right-list': applyType === 'list'
              })}
            >
              <div className={`bill-state bill-state-${color}`}>{str}</div>
            </div>
          )}
        </div>
        {flowType !== FlowTypeEnum.Backlog && state !== 'draft' && !!name?.length ? (
          <div className="bill-info-staff-wrapper">
            <div className="left">
              <div className="bill-info-staff">{`${name}`}</div>
              {!!staffAdditionInfo?.length && (
                <div className="bill-info-addition-staff">{`(${staffAdditionInfo})`}</div>
              )}
              {!!str?.length && <div className="approve-desc">{str}</div>}
            </div>
          </div>
        ) : null}
        {flowType === FlowTypeEnum.Backlog && !!name?.length ? (
          <div className="bill-info-staff-wrapper">
            <div className="left">
              <div className="bill-info-staff">{`${name}`}</div>
              {!!staffAdditionInfo?.length && (
                <div className="bill-info-addition-staff">{`(${staffAdditionInfo})`}</div>
              )}
              {!!specification && (
                <div className="approve-specification">
                  <span className="mood-words">{i18n.get('的')}</span>
                  {getSpecificationName(specification)}
                </div>
              )}
            </div>
            <div className="right">
              {isRiskWarning && isRiskWarning.status === 'HAVE' ? (
                <EKBIcon name="#EDico-plaint-circle-o" style={{ color: '#fa8c16', marginLeft: 8 }} />
              ) : null}
            </div>
          </div>
        ) : null}
        <div className="bill-info">
          <div className="bill-info-code">
            {dateStr}
            {` ${code}`}
            {window.isNewHome && !enableHidingFinishedBills() && get(data, 'flow.state') === 'paid' && (
              <span className="cardList_BillItem-status">{i18n.get('未确认')}</span>
            )}
          </div>
          <div className="bill-info-money-wrapper">
            {amount && Number(amount?.standard || amount) !== 0 ? (
              <Money value={amount} isShowSymbol={false} isShowStrCode className="bill-info-money" />
            ) : (
              <span className="bill-info-money-no-money">{i18n.get('暂无金额')}</span>
            )}
          </div>
        </div>
        {selectAble && isNotAllowBatchApprove && (
          <div className="not-allow-batch-approve-tip">{i18n.get('该单据不可批量审批')}</div>
        )}
      </div>
      {applyType === 'list' && <div className="bill-info-divider" />}
    </div>
  )
}

export default BillListWrapper
