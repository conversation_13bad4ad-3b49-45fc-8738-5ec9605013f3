/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2022-03-21 17:15:33
 */
import React from 'react'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Fetch } from '@ekuaibao/fetch'
import { CardProps } from "./index"
const { getWeek } = api.require('@bill/elements/datalink/dataLinkUtil')
import { TwoToneGeneralFood } from '@hose/eui-icons';

export default (props: CardProps) => {
    const { dataSource } = props
    const { date, from_city, to_city, startTime, endTime, order, isNormalView, from } = dataSource
    const format = Fetch.lang === 'en-US' ? "MM.DD" : 'MM月DD日'
    return (
        <div className={style[isNormalView || from === 'next' ? 'trip_items_card_all' : 'trip_items_card']}>
            <div className="top_view">
                <div className="l_view">
                    <div className="title-view">
                        <TwoToneGeneralFood fontSize={22}/>
                        <span className='title'>{i18n.get('餐饮')}</span>
                    </div>
                    <div className={from === 'next' ? 'info_view noMaxWidth' : 'info_view'}>
                        <div className="info_o">{order ? `${from_city} - ${to_city}` : from_city}</div>
                        <div className="date">
                            {startTime === endTime
                                ? `${moment(date).format(format)} ${getWeek(date)}`
                                : `${moment(startTime).format(format)} ${getWeek(startTime)} - ${moment(endTime).format(format)} ${getWeek(endTime)}`}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )

}