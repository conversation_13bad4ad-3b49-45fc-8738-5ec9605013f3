/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2022-07-19 18:49:48
 */
import React from 'react'
import style from './TripItemCard.module.less'
import { T } from '@ekuaibao/i18n'
import { Button } from 'antd-mobile'
import { get } from 'lodash'
import { CardProps } from "./index"
const TRAIN = require('../../../images/train_new.svg')
const FLIGHT = require('../../../images/flight_new.svg')
const HOTEL = require('../../../images/hotel_new.svg')
const TAXI = require('../../../images/car_new.svg')
const FOOD = require('../../../images/food_new.svg')


const TRAVEL_TYPE: any = {
    飞机: FLIGHT,
    酒店: HOTEL,
    火车: TRAIN,
    用车: TAXI,
    餐饮: FOOD
}
export const TripFooter = (props: CardProps) => {
    const { otherData, onGoTripListClick } = props
    if (!otherData || !otherData.length) return null
    const typeMap: any = {}
    otherData.forEach((v: any) => typeMap[get(v, `form.行程类型`, '') || v?.type] = get(v, `form.行程类型`, '') || v?.type)
    return <div className={style['trip_items_card_other']} onClick={(e: any) => {
        e?.stopPropagation()
        onGoTripListClick && onGoTripListClick()
    }}>
        <div className="top_view_other">
            <div className="l_view_other">
                <div className="l1">
                    {Object.keys(typeMap).map((v: string) => <img key={TRAVEL_TYPE[v]} src={TRAVEL_TYPE[v]} alt="" />)}
                </div>
                <div className="other_view_other">
                    {i18n.get(`您有{__k0}条即将开始的行程待订购`, { __k0: otherData.length })}
                </div>
            </div>
            <div className="r_view_other">
                <Button
                    className="btn"
                    onClick={(e: any) => {
                        e?.stopPropagation()
                        onGoTripListClick && onGoTripListClick()
                    }}
                >
                    <T name={'去订购'} />
                </Button>
            </div>
        </div>
    </div>
}

const Card = (props: any) => {
    const { otherData, onGoTripListClick } = props
    if (!otherData || !otherData.length) return null
    const typeMap: any = {}
    otherData.forEach((v: any) => typeMap[get(v, `form.行程类型`, '') || v.type] = get(v, `form.行程类型`, '') || v.type)
    return <div className={style['trip_items_card_mix_test']} onClick={(e: any) => {
        e?.stopPropagation()
        onGoTripListClick && onGoTripListClick()
    }}>
        <div className="top_view">
            <div className="l_view">
                <div className="l1">
                    {Object.keys(typeMap).map((v: string) => <img key={TRAVEL_TYPE[v]} src={TRAVEL_TYPE[v]} alt={TRAVEL_TYPE[v]} />)}
                </div>
                <div className="other_view">
                    {i18n.get(`您有{__k0}条即将开始的行程待订购`, { __k0: otherData.length })}
                </div>
            </div>
            <div className="r_view">
                <Button
                    className="btn"
                    onClick={(e: any) => {
                        e?.stopPropagation()
                        onGoTripListClick && onGoTripListClick()
                    }}
                >
                    <T name={'去订购'} />
                </Button>
            </div>
        </div>
    </div>
}

export default Card