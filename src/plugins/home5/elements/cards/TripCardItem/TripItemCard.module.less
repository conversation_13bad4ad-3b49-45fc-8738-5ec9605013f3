@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';


.trip_items_card, .trip_items_card_all {
  display: flex;
  flex-direction: column;
  padding: @space-4 @space-5;
  background: var(--eui-bg-body-overlay);
  border-radius: 0.12rem;

  :global {
    .car_card {
      height: 120px;
      background: url(../../../images/position.png) var(--eui-bg-body-overlay) no-repeat right 16px bottom;
      border-radius: var(--eui-radius-s);
      padding: @space-4 @space-5;
      margin-top: @space-4;
      background-size: 33.33%;
      position: relative;
      .car_no {
        font: var(--eui-font-head-b1);
        color: var(--eui-text-title);
      }
      .car_type {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-caption);
        margin-top: 2px;
      }
      .car_color {
        width: 116px;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
    .desc {
      color: rgba(20, 34, 52, 0.44);
      display: flex;
      height: 120px;
      background-color: #F7F8FA;
      .text-nowrap {
        text-align: center;
        white-space: nowrap;
      }
      .text-taxi {
        text-align: center;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .line {
        margin-left: 16px;
        margin-right: 16px;
        color: #d9dbde;
      }
      .text {
        text-align: center;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        &:not(:last-child)::after {
          content: '|';
          margin-left: 16px;
          margin-right: 16px;
          color: #d9dbde;
        }
      }
    }
    .top_view {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      .l_view {
        flex: 1;
        .title-view {
          font: var(--eui-font-body-b1);
          color: var(--eui-text-title);
          display: flex;
          margin-left: -@space-2;
          span {
            margin-right: 0.08rem;
          }
          .title {
            margin-top: 2px;
          }
        }
        .mr-16 {
          margin-right: 16px;
        }
        .info_view, .info_view_flex {
          .status {
            margin-top: @space-3;
            padding: 2px @space-2;
            color: var(--eui-function-info-600);
            background: var(--eui-function-info-100);
            font: var(--eui-font-note-r2);
            border-radius: var(--eui-radius-xs);
            height: 40px;
            white-space: nowrap;
          }
          .cancel {
            color: var(--eui-function-danger-600);
            background: var(--eui-function-danger-100);
          }
          .change {
            color: var(--eui-function-warning-600);
            background: var(--eui-function-warning-100);
          }
          .roundabout {
            color: var(--eui-function-success-600);
            background: var(--eui-function-success-100);
          }
          .protected {
            color: var(--eui-function-info-600);
            background: var(--eui-function-info-100);
          }
          .date {
            color: var(--eui-text-title);
            font: var(--eui-font-body-r1);
            margin-top: @space-3;
            display: inline-block;
            max-width: 4.5rem;
          }
          .info_o {
            margin-top: @space-3;
            max-width: 4.5rem;
            color: var(--eui-text-title);
            font: var(--eui-font-body-b1);
            .fight_logo {
              width: 0.28rem;
              vertical-align: middle;
              margin-right: 0.08rem;
              margin-top: -6px;
            }
          }
          .train_info_o {
            color: var(--eui-text-title);
            font: var(--eui-font-body-r1);
            .line {
              margin-left: @space-2;
              margin-right: @space-2;
              display: inline-block;
              width: 2px;
              height: 24px;
              background: var(--eui-text-caption);
              vertical-align: initial;
            }
          }
        }
        .info_view_flex {
          display: flex;
          justify-content: space-between;
          .info_o {
            color: var(--eui-text-title);
            font: var(--eui-font-body-r1);
            display: inline-block;
          }
          .line {
            display: inline-block;
            span {
              margin-left: @space-2;
              margin-right: @space-2;
              display: inline-block;
              width: 2px;
              height: 24px;
              background: var(--eui-text-caption);
              vertical-align: initial;
            }
          }
        }
        .noMaxWidth {
          max-width: none;
          .info_o {
            max-width: none;
            font: var(--eui-font-head-b1);
          }
        }
        .info_view_taxi {
          .info_o {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: none;
          }
          .time {
            font: var(--eui-font-head-b1);
            color: var(--eui-text-title);
          }
          .address {
            font: var(--eui-font-note-r2);
            color: var(--eui-text-caption);
            margin-top: 4px;
            span {
              color: var(--eui-function-info-500);
            }
          }
        }
        .info_view_no_btn {
          .date {
            font: var(--eui-font-body-r1);
            color: var(--eui-text-title);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 5.5rem;
          }
          .info_o {
            color: var(--eui-text-title);
            font: var(--eui-font-body-b1);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 5.5rem;
          }
        }
      }
    }
    .bottom_view {
      border-radius: 0.12rem;
      margin-top: 0.16rem;
      display: flex;
      position: relative;
      .city_info {
        flex: 1;
        .time, .notime {
          color: var(--eui-text-title);
          position: relative;
          .add {
            font: var(--eui-font-note-b2);
            position: absolute;
            right: 0;
          }
        }
        .time {
          font: var(--eui-num-display-b2);
        }
        .notime {
          font: var(--eui-font-head-b2);
        }
        .addOneDay {
          padding-right: 0.32rem;
        }
        .change {
          font-size: 24px;
          color: rgba(39, 46, 59, 0.6);
        }
        .city {
          font: var(--eui-font-body-b1);
          color: var(--eui-text-title);
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          padding-right: @space-8;
        }
        .right {
          padding-left: @space-8;
          padding-right: 0;
        }
      }
      .hotel {
        .time {
          font: var(--eui-font-head-b1);
          color: var(--eui-text-title);
          padding-right: 0;
        }
        .city {
          font: var(--eui-font-note-r2);
          color: var(--eui-text-placeholder);
        }
        
      }
      .from {
        width: 40%;
      }
      .to {
        text-align: right;
      }
      .use_time {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-caption);
        white-space: nowrap;
        width: 124px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        img {
          width: 176px;
        }
        .check_time {
          text-align: center;
          .showIcon, .showText {
            display: inline-block;
            position: relative;
            padding: 0 @space-4;

            &::before {
              content: " ";
              width: 56px;
              height: 4px;
              background: linear-gradient(270deg,var(--eui-icon-disabled),var(--eui-bg-body) 114.29%);
              position: absolute;
              left: -58px;
              top: 50%;
              margin-top: -1px;
            }
            &::after {
              content: " ";
              width: 56px;
              height: 4px;
              background: linear-gradient(90deg, var(--eui-icon-disabled), var(--eui-bg-body) 114.29%);
              position: absolute;
              right: -58px;
              top: 50%;
              margin-top: -1px;
            }
          }
          .showIcon {
            font-size: 32px;
            color: var(--eui-icon-disabled);
          }
          .showText {
            border: 2px solid var(--eui-icon-disabled);
            border-radius: 20px;
            font: var(--eui-font-note-r2);
            color: var(--eui-text-caption);
          }
        }
      }
    }
    .bottom_view_hotel {
      background: rgba(20, 34, 52, 0.04);
      border-radius: 0.12rem;
      margin-top: 0.2rem;
      display: flex;
      align-items: center;
      position: relative;
      padding: 32px;
      .label {
        font-size: 28px;
        color: rgba(20, 34, 52, 0.72);
        width: 84px;
      }
      .descp {
        font-weight: 500;
        font-size: 28px;
        color: var(--eui-text-title);
        flex: 1;
      }
      .text {
        display: flex;
        align-items: flex-start;
      }
      .city {
        width: 100%;
      }
    }
    .bottom_view_car {
      height: 1.6rem;
      background: rgba(20, 34, 52, 0.04);
      border-radius: 0.12rem;
      margin-top: 0.2rem;
      display: flex;
      align-items: center;
      position: relative;
      padding: 32px;

      .icon_part {
        margin-right: 18px;
        .start_point {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #94da06;
        }
        .line {
          margin-top: 2px;
          margin-bottom: 2px;
          width: 2px;
          height: 36px;
          background: rgba(56, 46, 49, 0.05);
          margin-left: 2px;
        }
        .end_point {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #ff9d07;
        }
      }
      .city {
        font-weight: 500;
        font-size: 28px;
        color: var(--eui-text-title);
        width: 80%;
        .text {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .phone_part {
        position: absolute;
        right: 32px;
        .back {
          background-color: #ffffff;
          width: 64px;
          height: 64px;
          border-radius: 64px;
          box-shadow: 0px 2px 12px rgba(20, 34, 52, 0.12);
          position: relative;
          img {
            width: 24px;
            height: 26px;
            position: absolute;
            top: 20px;
            left: 20px;
          }
        }
        .text {
          font-size: 24px;
          color: rgba(20, 34, 52, 0.44);
          margin-top: 8px;
          text-align: center;
        }
      }
    }
    .other_view {
      margin-top: 0.2rem;
      margin-left: 0.65rem;
      color: rgba(20, 34, 52, 0.44);
      span {
        color: #e5e5e5;
      }
    }
  }
}
.trip_items_card_all {
  padding: 0;
  background: var(--eui-bg-body);
  .info_view {
    .info_o {
      margin-top: @space-3;
      max-width: 4.5rem;
      color: var(--eui-text-title);
      font: var(--eui-font-body-b1);
      .fight_logo {
        width: 0.28rem;
        vertical-align: middle;
        margin-right: 0.08rem;
      }
    }
  }
}

.bottom_btn_view, .bottom_btn_view_noborder {
  display: flex;
  justify-content: space-between;
  border-top: 2px dashed var(--eui-line-divider-default);
  margin-top: 0.24rem;
  padding-top: 0.24rem;
  :global {
    .text {
      font: var(--eui-font-note-r2);
      color: var(--eui-text-title);
      line-height: 0.6rem;
      height: 0.6rem;
    }
    .btn {
      font: var(--eui-font-body-r1);
      border: 2px solid var(--brand-base) !important;
      border-radius: @radius-2;
      padding: 0.08rem 0.16rem;
      height: 0.6rem;
      color: var(--brand-base);
    }
  }
}
.bottom_btn_view_noborder {
  border-top: 0;
  margin-top: 0;
}

.application_info {
  font: var(--eui-font-note-r2);
  color: var(--eui-text-caption);
  margin: @space-5 0 -@space-6 0;
  padding: @space-4 0;
  border-top: 2px solid var(--eui-fill-pressed);
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  text-overflow: ellipsis;
  :global {
    .line {
      margin-left: @space-2;
      margin-right: @space-2;
      display: inline-block;
      width: 2px;
      height: 20px;
      background: var(--eui-text-caption);
      vertical-align: initial;
    }
  }
}

.trip_items_card_mix_test {
  display: flex;
  flex-direction: column;
  :global {
    .top_view {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;
      .l_view {
        display: flex;
        flex-direction: column;
        .l1 {
          display: flex;
        }
        img {
          height: 0.4rem;
          width: 0.4rem;
        }
        .other_view {
          margin-top: 16px;
          color: var(--eui-text-title);
        }
        .mr-16 {
          margin-right: 16px;
        }
      }
      .r_view {
        .btn {
          font-size: 28px;
          color: #fff;
          height: 68px;
          width: 176px;
          background: var(--brand-base);
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          position: initial;
        }
      }
    }
  }
}

.trip_items_card_other {
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  border-top: 2px solid #e5e5e5;
  padding-top: 32px;
  :global {
    .top_view_other {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .l_view_other {
        display: flex;
        flex-direction: column;
        .l1 {
          display: flex;
        }
        img {
          height: 0.4rem;
          width: 0.4rem;
          margin-right: 16px;
        }
        .other_view_other {
          margin-top: 16px;
          color: var(--eui-text-title);
        }
        .mr-16 {
          margin-right: 16px;
        }
      }

      .r_view_other {
        .btn {
          font-size: 28px;
          color: #fff;
          height: 68px;
          width: 176px;
          background: var(--brand-base);
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          position: initial;
        }
      }
    }
  }
}

.trip_items_card_recent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  :global {
    .l1 {
      display: flex;
      color: #142234;
      img {
        height: 0.4rem;
        width: 0.4rem;
        margin-right: 16px;
      }
    }

    .action {
      color: var(--brand-base);
      position: relative;
      width: 136px;
      img {
        position: absolute;
        top: 6px;
        right: 0;
      }
    }
  }
}
