/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2022-03-18 17:54:53
 */
import React from 'react'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Fetch } from '@ekuaibao/fetch'
const { getWeek } = api.require('@bill/elements/datalink/dataLinkUtil')
import { TwoToneGeneralCustom } from '@hose/eui-icons';
interface Props {
    onBuy: (type: string) => void;
    dataSource: any;
    otherData: any

}
export default (props: Props) => {
    const { dataSource } = props
    const { date, from_city, to_city, startTime, endTime, order, isNormalView, from } = dataSource
    const format = Fetch.lang === 'en-US' ? "MM.DD" : 'MM月DD日'
    return (
        <div className={style[isNormalView || from === 'next' ? 'trip_items_card_all' : 'trip_items_card']}>
            <div className="top_view">
                <div className="l_view">
                    <div className="title-view">
                        <TwoToneGeneralCustom fontSize={22}/>
                    </div>
                    <div className={from === 'next' ? 'info_view noMaxWidth' : 'info_view'}>
                        <div className="info_o">{(from_city && to_city) ? `${from_city} - ${to_city}` : from_city}</div>
                        <div className="date">
                            {startTime === endTime || !endTime
                                ? `${moment(date).format(format)} ${getWeek(date)}`
                                : `${moment(startTime).format(format)} ${getWeek(startTime)} - ${moment(endTime).format(format)} ${getWeek(endTime)}`}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )

}