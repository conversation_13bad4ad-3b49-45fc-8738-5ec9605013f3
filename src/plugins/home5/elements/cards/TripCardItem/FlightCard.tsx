/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2021-10-28 16:22:38
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2022-04-07 15:51:20
 */
import React from 'react'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Button } from '@hose/eui-mobile'
import { T } from '@ekuaibao/i18n'
const { getWeek, getDiff } = api.require('@bill/elements/datalink/dataLinkUtil')
import { getDiffDay } from './formatHelper'
import { Fetch } from '@ekuaibao/fetch'
import { CardProps } from "./index"
import { TwoToneGeneralAirplane, FilledGeneralAirplane } from '@hose/eui-icons';
import { get } from 'lodash'

export default (props: CardProps) => {
    const { onBuy, toOrderDetail, dataSource } = props
    const { date, airline_company, flight_number = '', from_city, to_city, startTime, endTime, have_stop, stop_city, stop_time, order, orgDateTime, arrDateTime, flightChange, title, code, isNormalView, from, carrierCode, cancelFlight, flightStatus, taxiUrl, spendTime } = dataSource
    const addOneDay = getDiffDay(startTime, endTime)
    const format = Fetch.lang === 'en-US' ? "MM.DD" : 'MM月DD日'
    const statusMap = {
        '0': { class: 'change', text: i18n.get("航班变更") },
        '1': { class: 'cancel', text: i18n.get("航班取消") },
        '2': { class: 'protected', text: i18n.get("航班取消有保护") },
        '3': { class: 'roundabout', text: i18n.get("航班复飞") },
    }
    let useTimeDom = () => {
        if (have_stop && have_stop !== "0") {
            const city = stop_city?.length > 4 ? stop_city.substring(0, 3) + '...' : stop_city
            return (
                <>
                    <div>{stop_time && i18n.get('停留') + stop_time}</div>
                    <div className="check_time">
                        <span className="showText">{i18n.get('经停') + city}</span>
                    </div>
                </>
            )
        }
        return (
            <>
                <div>{i18n.currentLocale === 'en-US' ? spendTime?.replace('时', 'H')?.replace('分', 'M') : spendTime}</div>
                <div className="check_time">
                    <FilledGeneralAirplane className="showIcon" />
                </div>
            </>
        )
    }
    return (
        <>
            <div className={style[isNormalView || from === 'next' ? 'trip_items_card_all' : 'trip_items_card']}>
                <div className="top_view">
                    <div className="l_view">
                        <div className="title-view">
                            <TwoToneGeneralAirplane fontSize={22} />
                            <span className='title'>{i18n.get('机票')}</span>
                        </div>
                        <div className={from === 'next' ? "info_view_flex" : "info_view"}>
                            {
                                order && from !== 'all' ? (
                                    <div>
                                        <div className="date">
                                            {moment(date).format(format)} {getWeek(date)}
                                        </div>
                                        <div className="line">
                                            <span></span>
                                        </div>
                                        <div className="info_o">
                                            {from === 'next' && carrierCode && <img className="fight_logo" src={`https://statics.ekuaibao.com/mall/assets/airline-logo/${carrierCode}.png`} />}
                                            {order && from !== 'all' ? `${airline_company} ${flight_number}` : `${from_city}-${to_city}`}
                                        </div>

                                    </div>
                                ) : (
                                    <div>
                                        <div className="info_o">
                                            {from === 'next' && carrierCode && <img className="fight_logo" src={`https://statics.ekuaibao.com/mall/assets/airline-logo/${carrierCode}.png`} />}
                                            {order && from !== 'all' ? `${airline_company} ${flight_number}` : `${from_city}-${to_city}`}
                                        </div>
                                        <div className="date">
                                            {moment(date).format(format)} {getWeek(date)}
                                        </div>
                                    </div>
                                )
                            }

                            {flightStatus && from === 'next' && <div className={`status ${get(statusMap, `${flightStatus}.class`, '')}`}>
                                <span>{get(statusMap, `${flightStatus}.text`, '')}</span>
                            </div>}
                        </div>
                    </div>
                    {from !== 'next' && <div className="r_view">
                        <Button
                            category={isNormalView ? "secondary" : "ghost"} theme="highlight"
                            onClick={(e: any) => {
                                e?.stopPropagation()
                                if (order) {
                                    toOrderDetail && toOrderDetail(true)
                                } else {
                                    onBuy && onBuy('FLIGHT')
                                }
                            }}
                        >
                            <T name={order ? '查看订单' : '买机票'} />
                        </Button>
                    </div>}
                </div>
                {title && code && isNormalView ?
                    <div className={style['application_info']}>
                        <span>{title}</span>
                        <span className="line"></span>
                        <span>{code}</span>
                    </div> : null
                }
                {order && from === 'next' && (
                    <>
                        <div className="bottom_view">
                            <div className="city_info from">
                                {addOneDay && <div className="add"></div>}
                                <div className={cancelFlight ? "notime" : "time"}>{cancelFlight ? "--:--" : moment(startTime).format('HH:mm')}</div>
                                {(flightChange || cancelFlight) ? <div className="change">
                                    {`${i18n.get("原计划")}${moment(orgDateTime).format('HH:mm')}`}
                                </div> : <div className="city">{from_city}</div>}
                            </div>
                            <div className="use_time">
                                {useTimeDom()}
                            </div>
                            <div className="city_info to">
                                <div className={`${cancelFlight ? "notime" : "time"} ${addOneDay ? "addOneDay" : ""}`}>
                                    {addOneDay && <div className="add">{`+${addOneDay}`}</div>}
                                    {cancelFlight ? "--:--" : moment(endTime).format('HH:mm')}
                                </div>
                                {(flightChange || cancelFlight) ? <div className="change">
                                    {`${i18n.get("原计划")}${moment(arrDateTime).format('HH:mm')}`}
                                </div> : <div className="city right">{to_city}</div>}
                            </div>
                        </div>
                        {
                            taxiUrl && (
                                <div className={style['bottom_btn_view']}>
                                    <span className="text">{flightStatus === '1' ? i18n.get('申请改签或退票') : i18n.get('一键叫车省心省力')}</span>
                                    <Button
                                        category="secondary" theme="highlight"
                                        onClick={(e: any) => {
                                            e?.stopPropagation()
                                            if (flightStatus === '1') {
                                                toOrderDetail && toOrderDetail(true)
                                            } else {
                                                onBuy && onBuy('FLIGHT')
                                            }
                                        }}
                                    >
                                        <T name={flightStatus === '1' ? '去申请' : '去机场'} />
                                    </Button>
                                </div>
                            )
                        }
                    </>
                )}
            </div>
        </>
    )

}
