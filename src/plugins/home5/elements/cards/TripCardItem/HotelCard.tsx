/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2021-10-28 16:22:38
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2022-05-27 18:17:38
 */
import React, { useEffect, useState } from 'react'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { T } from '@ekuaibao/i18n'
import { Fetch } from '@ekuaibao/fetch'
import { CardProps } from './index'
const { getWeek } = api.require('@bill/elements/datalink/dataLinkUtil')
import { TwoToneGeneralHotel } from '@hose/eui-icons';
import { Button } from '@hose/eui-mobile'

export default (props: CardProps) => {
  const { onBuy, toOrderDetail, dataSource, } = props
  const {
    hotel_name,
    from_city,
    room_type,
    room_number,
    startTime,
    endTime,
    have_meal,
    hotel_adress,
    order,
    title,
    code,
    from,
    isNormalView
  } = dataSource

  const [translateObj, setTranslateObj] = useState({})
  useEffect(() => {
    let days = ''
    let startTimeStr = ''
    let toTimeStr = ''
    if (order) {
      const diff = Math.abs(endTime - startTime)
      days = `${Math.ceil(diff / (1000 * 3600 * 24))}晚`
      startTimeStr = moment(startTime).format('HH:mm') + '后入住'
      toTimeStr = moment(endTime).format('HH:mm') + '前离店'
    }
    api.invokeService('@common:get:translate', { hotel_name, from_city, room_type, days, startTimeStr, toTimeStr }).then((result: any) => {
      setTranslateObj(result)
    })
  }, [dataSource])
  const format = Fetch.lang === 'en-US' ? 'MM.DD' : 'MM月DD日'

  return (
    <div className={style[isNormalView || from === 'next' ? 'trip_items_card_all' : 'trip_items_card']}>
      <div className="top_view">
        <div className="l_view">
          <div className="title-view">
            <TwoToneGeneralHotel fontSize={22} />
            <span className='title'>{i18n.get('酒店')}</span>
          </div>
          <div className={from === 'next' ? 'info_view noMaxWidth' : 'info_view'}>
            <div className="info_o">{order ? translateObj.hotel_name : translateObj.from_city}</div>
            {
              from !== 'next' && (
                <div className="date">
                  {startTime === endTime
                    ? `${moment(startTime).format(format)} ${getWeek(startTime)}`
                    : `${moment(startTime).format(format)} ${getWeek(startTime)} - ${moment(endTime).format(format)} ${getWeek(endTime)}`}
                </div>
              )
            }
          </div>
        </div>
        {from !== 'next' && <div className="r_view">
          <Button
            category={isNormalView ? "secondary" : "ghost"} theme="highlight"
            onClick={(e: any) => {
              e?.stopPropagation()
              if (order) {
                toOrderDetail && toOrderDetail(true)
              } else {
                onBuy && onBuy('HOTEL')
              }
            }}
          >
            <T name={order ? '查看订单' : '订酒店'} />
          </Button>
        </div>}
      </div>
      {title && code && isNormalView ?
        <div className={style['application_info']}>
          <span>{title}</span>
          <span className="line"></span>
          <span>{code}</span>
        </div> : null
      }
      {order && from === 'next' ? (
        <>
          <div className="bottom_view">
            <div className="city_info from hotel">
              <div className="time">{moment(startTime).format(format)}</div>
              <div className="city">{translateObj.startTimeStr}</div>
            </div>
            <div className="use_time">
              <div>{translateObj.room_type?.length > 11 ? translateObj.room_type.substring(0, 11) + '...' : translateObj.room_type}</div>
              <div className="check_time">
                <span className="showText">{translateObj.days}</span>
              </div>
            </div>
            <div className="city_info to hotel">
              <div className="time">
                {moment(endTime).format(format)}
              </div>
              <div className="city right">{translateObj.toTimeStr}</div>
            </div>
          </div>
          <div className={style['bottom_btn_view']}>
            <span className="text">{i18n.get('一键叫车省心省力')}</span>
            <Button
              category="secondary" theme="highlight"
              onClick={(e: any) => {
                e?.stopPropagation()
                onBuy && onBuy('TAXI')
              }}
            >
              <T name={'去酒店'} />
            </Button>
          </div>
        </>
      ) : null}
    </div>
  )
}
