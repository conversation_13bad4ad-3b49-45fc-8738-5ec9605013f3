/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2021-10-28 16:22:38
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2022-05-26 17:10:37
 */
import React from 'react'
import {
  formatTrainData,
  formatFlightData,
  formatTaxiData,
  formatHotelData,
  formatFoodData,
  formatCommonData
} from './formatHelper'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { getOrder, goThirdResource, handleOrderClick, track } from './formatHelper'
import TaxiCard from './TaxiCard'
import FoodCard from './FoodCard'
import TrainCard from './TrainCard'
import CommonCard from './CommonCard'
import HotelCard from './HotelCard'
import TripMultipleCard from './TripMultipleCard'
import FlightCard from './FlightCard'

const { TemplateKind } = api.require('@bill/elements/datalink/datalink-detail-modal/types')
interface Props {
  data: any
  otherData: any[]
  entityPrefixForTrip: any
  entityPrefixForOrder: any
  from: string
  isOrder: boolean
  isNew: boolean
  onGoTripListClick: () => void
  upgradeToMicroservice?: boolean
  isNormalView?: boolean
}

export interface CardProps {
  isNew?: boolean
  onBuy: (type: string) => void
  dataSource: any
  otherData: any
  onGoTripListClick?: () => void
  toOrderDetail?: (clickByButton?: boolean) => void
  isNormalView?: boolean
  from?: string
}
const TYPEMAP: any = {
  flight: '飞机',
  train: '火车',
  taxi: '用车',
  hotel: '酒店',
  food: '餐饮'
}
const TripCardItem: React.FC<Props> = ({
  data,
  otherData,
  entityPrefixForTrip,
  from,
  isOrder,
  entityPrefixForOrder,
  isNew,
  onGoTripListClick,
  upgradeToMicroservice,
  isNormalView
}) => {
  const order = getOrder({ data, entityPrefixForTrip, entityPrefixForOrder, isOrder, isNew, upgradeToMicroservice })
  const form = data?.form || data
  let type = data?.type || get(data?.form, `行程类型`)
  if (order) {
    type = order.orderType ? TYPEMAP[order.orderType] : get(order?.form, `订单类型`)
  }
  const orderForm = order?.orderChildren?.[0] || order?.form || false
  const onBuy = async (type: any) => {
    const taxiUrl = order ? get(orderForm, `${entityPrefixForOrder}taxiUrl`) : null
    const orderType = order ? get(orderForm, '订单类型', null) : null
    track({
      key: 'mytrips_mall_click',
      actionName: '订购按钮点击量',
      from,
      type
    })
    const applyId = form?.flowId || get(form, `${entityPrefixForTrip}原始单据`, '')
    const tripId = data?.id === 'virtual' ? '' : data?.id 
    const white = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI', 'FOOD']
    if (!white.includes(type)) {
      return
    }
    const TK = type ? `BUTTON_${type}_ORDER` : 'BUTTON_TRAVEL_ORDER'
    const config = applyId ? await api.invokeService('@bill:get:getFlowDetailInfo', applyId) : {}
    const tripPlatform = get(config, 'value.form.specificationId.configs', []).find(
      (i: any) => i.ability === 'tripPlatform'
    )
    const checked = get(tripPlatform, 'checked')
    const platform = get(tripPlatform, 'platform') || []
    const businessNo = get(order, `form.${entityPrefixForOrder}航班号`)
    const orderNo = get(order, `form.${entityPrefixForOrder}订单号`)
    const beginDate = get(order, `form.${entityPrefixForOrder}出发时间`)
    let params: any = { applyId, tripId }
    if (type === 'TAXI' && businessNo && beginDate) {
      params = { ...params, businessNo, beginDate }
    }
    if (orderType === '酒店' && orderNo) {
      params = { ...params, businessType: 1, orderNo }
    }
    if (orderType === '飞机' && orderNo) {
      params = { ...params, businessType: 2, orderNo }
    }
    if (orderType === '火车' && orderNo) {
      params = { ...params, businessType: 3, orderNo }
    }
    goThirdResource(TK, params, { checked, taxiUrl, platform: platform.join(','), isNew })
  }

  const handleOnClick = async (clickByButton?: boolean) => {
    if (order && from === 'all' && !clickByButton) return
    if (order) {
      const id = order?.id
      if (isNew) {
        return handleOrderClick(order, isNew, entityPrefixForOrder)
      } else if (!entityPrefixForOrder) {
        return handleOrderClick(order)
      } else {
        const { value } = await api.invokeService('@bill:get:datalink:template:byId', {
          entityId: id,
          type: TemplateKind.DETAIL
        })

        const dataLink = get(value, 'data.dataLink')
        const flag = handleOrderClick(dataLink, undefined, entityPrefixForOrder)
        if (flag === false) {
          api.open('@bill:DataLinkEntityTripOrderDetailModal', {
            field: get(value, 'data.dataLink.entity.fields', []),
            value: value,
            title: i18n.get('订单详情')
          })
          api.invokeService('@common:set:track', {
            key: 'mytrips_order_view',
            actionName: '订单详情页pv',
            from
          })
        }
      }
    } else {
      upgradeToMicroservice ? api.go(`/tripDetailNew/${data.id}`) : api.go(`/tripToDetail/${data?.id}`)
      api.invokeService('@common:set:track', {
        key: 'mytrips_detail_view',
        actionName: '行程详情页pv',
        from
      })
    }
  }

  if (!data && from === 'next') {
    return <TripMultipleCard otherData={otherData} onBuy={onBuy} onGoTripListClick={onGoTripListClick} />
  }

  const params = {
      data,
      orderForm,
      entityPrefixForOrder,
      entityPrefixForTrip,
      upgradeToMicroservice,
      isNormalView,
      from
    }

  if (type === '打车' || type === '用车') {
    const dataSource = formatTaxiData(params)
    return (
      <div onClick={() => handleOnClick()}>
        <TaxiCard dataSource={dataSource} onBuy={onBuy} toOrderDetail={handleOnClick} otherData={otherData} onGoTripListClick={onGoTripListClick} />
      </div>
    )
  }
  if (type === '飞机') {
    const dataSource = formatFlightData(params)
    return (
      <div onClick={() => handleOnClick()}>
        <FlightCard
          dataSource={dataSource}
          onBuy={onBuy}
          toOrderDetail={handleOnClick}
          otherData={otherData}
          isNew={isNew}
          onGoTripListClick={onGoTripListClick}
        />
      </div>
    )
  }

  if (type === '火车') {
    const dataSource = formatTrainData(params)
    return (
      <div onClick={() => handleOnClick()}>
        <TrainCard dataSource={dataSource} onBuy={onBuy} toOrderDetail={handleOnClick} otherData={otherData} onGoTripListClick={onGoTripListClick} />
      </div>
    )
  }

  if (type === '酒店') {
    const dataSource = formatHotelData(params)
    return (
      <div onClick={() => handleOnClick()}>
        <HotelCard dataSource={dataSource} onBuy={onBuy} toOrderDetail={handleOnClick} otherData={otherData} onGoTripListClick={onGoTripListClick} />
      </div>
    )
  }

  if (type === '餐补' || type === '餐饮') {
    const dataSource = formatFoodData(params)
    return (
      <div onClick={() => handleOnClick()}>
        <FoodCard dataSource={dataSource} onBuy={onBuy} toOrderDetail={handleOnClick} otherData={otherData} />
      </div>
    )
  }
  const dataSource = formatCommonData(params)
  return (
    <div onClick={() => handleOnClick()}>
      <CommonCard dataSource={dataSource} onBuy={onBuy} toOrderDetail={handleOnClick} otherData={otherData} />
    </div>
  )
}

export default TripCardItem
