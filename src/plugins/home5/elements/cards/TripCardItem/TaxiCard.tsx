/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2022-06-28 13:29:18
 */
import React, { useEffect, useState } from 'react'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Button } from '@hose/eui-mobile'
import { T } from '@ekuaibao/i18n'
import { Fetch } from '@ekuaibao/fetch'
import { CardProps } from "./index"
import { get } from 'lodash'
import { getDiffDay } from './formatHelper'
const TelPhoneCall: React.FC<{
    phoneNumber: string
}> = api.require('@elements/TelPhoneCall')
const { getWeek } = api.require('@bill/elements/datalink/dataLinkUtil')
import { TwoToneGeneralTaxi } from '@hose/eui-icons';


export default (props: CardProps) => {
    const { onBuy, toOrderDetail, dataSource } = props
    const { date, car_number, car_type, from_city, to_city, startTime, endTime, driver_nubmer, car_use_type, driver_name, car_catagories, car_cat, car_color, order, title, code, isNormalView, from, orderStatus } = dataSource
    const format = Fetch.lang === 'en-US' ? "MM.DD" : 'MM月DD日'
    const diffDay = getDiffDay(moment().valueOf(), date)
    const setOutTime = startTime || date
    const setOutTimeFormat = moment(date).format('HH:mm')
    let receivedStr = i18n.get('预约今天{__k0}出发', { __k0: setOutTimeFormat })
    if (diffDay === 1) {
        receivedStr = i18n.get('预约明天{__k0}出发', { __k0: setOutTimeFormat })
    }
    if (diffDay > 1) {
        receivedStr = i18n.get('预约{__k0}天后{__k1}出发', { __k0: diffDay, __k1: setOutTimeFormat })
    }
    const statusMap = {
        '派单中': { class: '', text: '', timeText: receivedStr },
        '司机已接单': { class: 'change', text: i18n.get("司机已接单"), timeText: receivedStr },
        '司机已出发': { class: '', text: i18n.get("司机已出发"), timeText: i18n.get("司机正在赶来") },
        '乘客待上车': { class: 'roundabout', text: i18n.get("司机已到达"), timeText: i18n.get("司机已到达上车点") },
    }
    const colorMap = {
        '黑': 'black',
        '蓝': 'blue',
        '棕': 'brown',
        '绿': 'green',
        '红': 'red',
        '银': 'silver',
        '白': 'white',
        '黄': 'yellow',
        '金': 'yellow',
    }
    const carColor = get(colorMap, car_color, 'default')
    const [translateObj, setTranslateObj] = useState({})
    useEffect(() => {
        console.log('ddd===>', dataSource)
        api.invokeService('@common:get:translate', { car_cat, driver_name, car_color, from_city }).then((result: any) => {
            setTranslateObj(result)
        })
    }, [dataSource.car_cat])

    return (
        <div className={style[isNormalView || from === 'next' ? 'trip_items_card_all' : 'trip_items_card']}>
            <div className="top_view">
                <div className="l_view">
                    <div className="title-view">
                        <TwoToneGeneralTaxi fontSize={22} />
                        <span className='title'>{i18n.get('用车')}</span>
                    </div>
                    <div className={from === 'next' ? "info_view info_view_taxi" : "info_view"}>
                        {order && from === 'next' ? (
                            <>
                                <div className="info_o">
                                    <div className="time">{get(statusMap, `${orderStatus}.timeText`, '')}</div>
                                    {get(statusMap, `${orderStatus}.text`, '') && <div style={{ marginTop: -4 }} className={`status ${get(statusMap, `${orderStatus}.class`, '')}`}>
                                        {get(statusMap, `${orderStatus}.text`, '')}
                                    </div>}
                                </div>
                                <div className="address">
                                    {i18n.get('请前往')}
                                    <span>{translateObj.from_city}</span>
                                    {i18n.get('上车Taxi')}
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="info_o">{from_city}</div>
                                <div className="date">
                                    {endTime ? (
                                        setOutTime === endTime
                                            ? `${moment(setOutTime).format(format)} ${getWeek(setOutTime)}`
                                            : `${moment(setOutTime).format(format)} ${getWeek(setOutTime)} - ${moment(endTime).format(format)} ${getWeek(endTime)}`
                                    )
                                        : `${moment(setOutTime).format(format)} ${getWeek(setOutTime)}`
                                    }
                                </div>
                            </>
                        )}
                    </div>
                </div>
                {from !== 'next' && !['DING_TALK', 'WEIXIN'].includes(window.__PLANTFORM__) && (
                    <div className="r_view">
                        {<Button
                            category={isNormalView ? "secondary" : "ghost"} theme="highlight"
                            onClick={(e: any) => {
                                e?.stopPropagation()
                                if (order) {
                                    toOrderDetail && toOrderDetail(true)
                                } else {
                                    onBuy && onBuy('TAXI')
                                }
                            }}
                        >
                            <T name={order ? '查看订单' : "去用车"} />
                        </Button>}
                    </div>
                )}
            </div>
            {title && code && isNormalView ?
                <div className={style['application_info']}>
                    <span>{title}</span>
                    <span className="line"></span>
                    <span>{code}</span>
                </div> : null
            }
            {order && from === 'next' && orderStatus !== '派单中' && (
                <>
                    <div className='car_card'>
                        <div className='car_no'>
                            {car_number && <span className="text-nowrap">{car_number}</span>}
                            {car_color && <span className="text-taxi">·{translateObj.car_color}</span>}
                        </div>
                        <div className='car_type'>
                            {car_cat && <span className="text-taxi">{translateObj.car_cat}</span>}
                            {driver_name && <span className="text-taxi">·{translateObj.driver_name}</span>}
                        </div>
                        <img className='car_color' src={`https://statics.ekuaibao.com/mall/assets/car-panel/car-${carColor}.png`} alt='' />
                    </div>
                    {
                        driver_nubmer ? (
                            <div className={style['bottom_btn_view_noborder']}>
                                <span className="text"></span>
                                <Button category="secondary" theme="highlight" onClick={(e: any) => { e?.stopPropagation() }}>
                                    <TelPhoneCall phoneNumber={driver_nubmer}>
                                        <T name={'联系司机'} />
                                    </TelPhoneCall>
                                </Button>
                            </div>
                        ) : null
                    }
                </>
            )}
        </div >
    )

}