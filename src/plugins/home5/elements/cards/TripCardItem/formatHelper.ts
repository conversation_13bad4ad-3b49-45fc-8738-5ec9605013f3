import { get, isString } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import qs from 'qs'
import moment from 'moment'
import { Fetch } from '@ekuaibao/fetch'
interface OrderProps {
  data: any
  entityPrefixForTrip: string
  entityPrefixForOrder: string
  upgradeToMicroservice?: boolean
  orderForm?: any,
  isOrder?: boolean
  isNew?: boolean
  isNormalView?: boolean
  from?: string
}
export const getOrder: (data: OrderProps) => any = ({
  data,
  entityPrefixForTrip,
  entityPrefixForOrder,
  isOrder,
  isNew,
  upgradeToMicroservice
}) => {
  let order
  if (isNew) {
    order = isOrder ? data : false
  } else {
    const form = upgradeToMicroservice ? data : data?.form
    let tempArray = upgradeToMicroservice ? get(form, `orders`, []) : get(form, `${entityPrefixForTrip}订单`, [])
    if (!tempArray.length) {
      tempArray = get(form, `${entityPrefixForOrder}订单`, [])
    }
    const orderArray = tempArray.filter((e: any) => !isString(e))
    order = orderArray.length > 0 ? orderArray[0] : false
  }
  return order
}

function commonTripData(originData: OrderProps) {
  const { data, entityPrefixForTrip, upgradeToMicroservice, isNormalView, from } = originData
  const tripForm = upgradeToMicroservice ? data : data?.form
  const date =
    get(tripForm, `${entityPrefixForTrip}行程日期`, '') ||
    get(tripForm, `${entityPrefixForTrip}入住日期`, '') ||
    get(tripForm, `startTime`, '')
  const startTime =
    get(tripForm, `${entityPrefixForTrip}行程日期`, '') ||
    get(tripForm, `${entityPrefixForTrip}入住日期`, '') ||
    get(tripForm, `startTime`, '')
  const endTime = get(tripForm, `${entityPrefixForTrip}离店日期`, '') || get(tripForm, `endTime`, '')
  let from_city = get(tripForm, `${entityPrefixForTrip}出发地`)
    ? JSON.parse(get(tripForm, `${entityPrefixForTrip}出发地`))[0]['label']
    : get(tripForm, `${entityPrefixForTrip}住宿地`)
      ? JSON.parse(get(tripForm, `${entityPrefixForTrip}住宿地`))[0]['label']
      : ''
  let to_city = get(tripForm, `${entityPrefixForTrip}目的地`)
    ? JSON.parse(get(tripForm, `${entityPrefixForTrip}目的地`))[0]['label']
    : ''
  const code = get(tripForm, `${entityPrefixForTrip}申请单编码`, '')
  const title = get(tripForm, `${entityPrefixForTrip}申请单标题`, '')
  if (upgradeToMicroservice) {
    const from_city_str = get(tripForm, `originCity`) ? get(tripForm, `originCity`)[0]?.name : ''
    from_city = from_city_str?.split('/')?.pop()
    const to_city_str = get(tripForm, `arrivalCity`) ? get(tripForm, `arrivalCity`)[0]?.name : ''
    to_city = to_city_str?.split('/')?.pop()
  }
  return { date, startTime, endTime, from_city, to_city, code, title, isNormalView, from }
}

export const formatTrainData = (originData: OrderProps) => {
  const { entityPrefixForOrder, orderForm } = originData
  const tripData = commonTripData(originData)
  const orderData: any = {}
  if (orderForm) {
    orderData.date = get(orderForm, `${entityPrefixForOrder}出发时间`, '') || orderForm?.startTime
    orderData.startTime = get(orderForm, `${entityPrefixForOrder}出发时间`, '') || orderForm?.startTime
    orderData.endTime = get(orderForm, `${entityPrefixForOrder}到达时间`, '') || orderForm?.endTime
    orderData.from_city = get(orderForm, `${entityPrefixForOrder}出发车站`, '') || orderForm?.departureStation
    orderData.to_city = get(orderForm, `${entityPrefixForOrder}到达车站`, '') || orderForm?.arrivalStation
    orderData.train_site =
      get(orderForm, `${entityPrefixForOrder}火车席位`, '') ||
      get(orderForm, `${entityPrefixForOrder}坐席`, '') ||
      orderForm?.trainGrade
    orderData.train_number =
      get(orderForm, `${entityPrefixForOrder}火车车次`, '') ||
      get(orderForm, `${entityPrefixForOrder}车次`, '') ||
      orderForm?.trainNumber
    orderData.train_type = get(orderForm, `${entityPrefixForOrder}车型`, '火车')
    orderData.train_ticket_number = get(orderForm, `${entityPrefixForOrder}取票号`)
    orderData.train_site_type = get(orderForm, `${entityPrefixForOrder}座位类型`) || orderForm?.trainSeat
    orderData.train_entrance = get(orderForm, `${entityPrefixForOrder}检票口`)
    orderData.taxiUrl = get(orderForm, `${entityPrefixForOrder}taxiUrl`, '')
  }
  return { ...tripData, ...orderData, order: !!orderForm }
}

export const formatFlightData = (originData: OrderProps) => {
  const { entityPrefixForOrder, orderForm } = originData
  const tripData = commonTripData(originData)
  const orderData: any = {}
  if (orderForm) {
    orderData.flightChange = get(orderForm, `${entityPrefixForOrder}航变类型`, '') === '0'
    orderData.cancelFlight = get(orderForm, `${entityPrefixForOrder}航变类型`, '') === '1'
    orderData.historyOrder = get(orderForm, `${entityPrefixForOrder}历史订单`, '')
      ? get(orderForm, `${entityPrefixForOrder}历史订单`, '')[0]
      : undefined
    orderData.date = get(orderForm, `${entityPrefixForOrder}出发时间`, '') || orderForm?.startTime
    orderData.startTime = get(orderForm, `${entityPrefixForOrder}出发时间`, '') || orderForm?.startTime
    orderData.endTime = get(orderForm, `${entityPrefixForOrder}到达时间`, '') || orderForm?.endTime
    orderData.from_city = get(orderForm, `${entityPrefixForOrder}出发机场`, '') || orderForm?.departureAirport
    orderData.to_city = get(orderForm, `${entityPrefixForOrder}到达机场`, '') || orderForm?.arrivalAirport
    orderData.flight_number = get(orderForm, `${entityPrefixForOrder}航班号`, '') || orderForm?.airlineCompany
    orderData.airline_company = get(orderForm, `${entityPrefixForOrder}航空公司`, '') || orderForm?.flightNumber
    orderData.flight_type = get(orderForm, `${entityPrefixForOrder}飞机型号`, '')
    orderData.have_meal = get(orderForm, `${entityPrefixForOrder}有无餐食`, '')
    orderData.have_price = get(orderForm, `${entityPrefixForOrder}折扣`, '') || orderForm?.discount
    orderData.have_stop = get(orderForm, `${entityPrefixForOrder}经停`, '')
    orderData.stop_city = get(orderForm, `${entityPrefixForOrder}经停城市`, '')
    orderData.stop_time = get(orderForm, `${entityPrefixForOrder}经停停留时长`, '')
    orderData.flightStatus = get(orderForm, `${entityPrefixForOrder}航变类型`, '')
    orderData.orgDateTime = orderData.cancelFlight ? orderData.startTime : orderData.historyOrder?.orgDateTime
    orderData.arrDateTime = orderData.cancelFlight ? orderData.endTime : orderData.historyOrder?.arrDateTime
    orderData.taxiUrl = get(orderForm, `${entityPrefixForOrder}taxiUrl`, '')
    orderData.spendTime = get(orderForm, `${entityPrefixForOrder}耗时`, '')
    const carrierCode = get(orderForm, `${entityPrefixForOrder}航空公司编码`, 'default')
    orderData.carrierCode = airlineCodeList.includes(carrierCode) ? carrierCode : 'default'
    if (i18n.currentLocale === 'en-US') {
      orderData.from_city = get(orderForm, `${entityPrefixForOrder}出发机场编码`, '') || get(orderForm, `${entityPrefixForOrder}出发机场`, '') || orderForm?.departureAirport
      orderData.to_city = get(orderForm, `${entityPrefixForOrder}到达机场编码`, '') || get(orderForm, `${entityPrefixForOrder}到达机场`, '') || orderForm?.arrivalAirport
    }
  }
  return { ...tripData, ...orderData, order: !!orderForm }
}

export const formatTaxiData = (originData: OrderProps) => {
  const { entityPrefixForOrder, orderForm } = originData
  const tripData = commonTripData(originData)
  const orderData: any = {}
  if (orderForm) {
    orderData.from_city =
      get(orderForm, `${entityPrefixForOrder}实际出发地点`, '') ||
      get(orderForm, `${entityPrefixForOrder}出发地点`, '') ||
      orderForm?.realGetOnDeparture
    orderData.to_city =
      get(orderForm, `${entityPrefixForOrder}实际到达地点`, '') ||
      get(orderForm, `${entityPrefixForOrder}目的地点`, '') ||
      orderForm?.realGetOffArrive
    orderData.date = get(orderForm, `${entityPrefixForOrder}出发时间`, '') || orderForm?.startTime
    orderData.car_number = get(orderForm, `${entityPrefixForOrder}车牌号`, '') || orderForm?.carNumber
    orderData.car_type = get(orderForm, `${entityPrefixForOrder}车型`, '')
    orderData.driver_nubmer = get(orderForm, `${entityPrefixForOrder}司机电话`, '')
    orderData.car_use_type = get(orderForm, `${entityPrefixForOrder}预约类型`, '用车') || orderForm?.travelMode
    orderData.driver_name = get(orderForm, `${entityPrefixForOrder}司机称谓`, '')
    orderData.taxiUrl = get(orderForm, `${entityPrefixForOrder}taxiUrl`, '')
    orderData.car_cat = get(orderForm, `${entityPrefixForOrder}车辆型号`, '')
    orderData.car_color = get(orderForm, `${entityPrefixForOrder}车辆颜色`, '')
    orderData.car_catagories = get(orderForm, `${entityPrefixForOrder}车型`, '') || orderForm?.vehicleType
    orderData.orderStatus = get(orderForm, `${entityPrefixForOrder}订单状态`, '')
  }
  return { ...tripData, ...orderData, order: !!orderForm }
}

export const formatHotelData = (originData: OrderProps) => {
  const { entityPrefixForOrder, orderForm } = originData
  const tripData = commonTripData(originData)
  const orderData: any = {}
  if (orderForm) {
    orderData.startTime =
      get(orderForm, `${entityPrefixForOrder}入住日期`) ||
      get(orderForm, `${entityPrefixForOrder}入住时间`) ||
      orderForm?.startTime
    orderData.endTime =
      get(orderForm, `${entityPrefixForOrder}离店日期`) ||
      get(orderForm, `${entityPrefixForOrder}离店时间`) ||
      orderForm?.endTime
    orderData.city = get(orderForm, `${entityPrefixForOrder}住宿地`)
      ? JSON.parse(get(orderForm, `${entityPrefixForOrder}住宿地`))[0]['label']
      : ''
    orderData.room_type =
      get(orderForm, `${entityPrefixForOrder}房型`, '') || get(orderForm, `${entityPrefixForOrder}房间名称`, '')
    orderData.hotel_name = get(orderForm, `${entityPrefixForOrder}酒店名称`, '') || orderForm?.hotelName
    orderData.night_number = get(orderForm, `${entityPrefixForOrder}间夜数`, '1') || orderForm?.roomDays
    orderData.room_number = get(orderForm, `${entityPrefixForOrder}房间数量`, '1') || orderForm?.roomQuantity
    orderData.trip_number = get(orderForm, `${entityPrefixForOrder}入住人数`)
    orderData.have_meal = get(orderForm, `${entityPrefixForOrder}早餐`)
    orderData.hotel_adress = get(orderForm, `${entityPrefixForOrder}酒店地址`) || orderForm?.hotelAddress
  }
  return { ...tripData, ...orderData, order: !!orderForm }
}

export const formatFoodData = (originData: OrderProps) => {
  const { entityPrefixForOrder, orderForm } = originData
  const tripData = commonTripData(originData)
  const orderData: any = {}
  if (orderForm) {
    orderForm.date = get(orderForm, `${entityPrefixForOrder}订单日期`, '')
    orderForm.from_city = get(orderForm, `${entityPrefixForOrder}实际出发地点`)
      ? JSON.parse(get(orderForm, `${entityPrefixForOrder}实际出发地点`))[0]['label']
      : ''
    orderForm.to_city = get(orderForm, `${entityPrefixForOrder}实际到达地点`)
      ? JSON.parse(get(orderForm, `${entityPrefixForOrder}实际到达地点`))[0]['label']
      : ''
    orderForm.car_number = get(orderForm, `${entityPrefixForOrder}车牌号`, '')
    orderForm.car_type = get(orderForm, `${entityPrefixForOrder}车型`, '')
  }
  return { ...tripData, ...orderData, order: !!orderForm }
}

export const formatCommonData = (originData: OrderProps) => {
  const { data, entityPrefixForOrder, entityPrefixForTrip, upgradeToMicroservice } = originData
  const form = upgradeToMicroservice ? data : data?.form
  const date =
    get(form, `${entityPrefixForTrip}行程日期`, '') ||
    get(form, `${entityPrefixForTrip}入住日期`, '') ||
    get(form, `${entityPrefixForOrder}订单日期`, '') || form?.startTime
  const startTime = get(form, `${entityPrefixForTrip}行程日期`, '') || get(form, `${entityPrefixForTrip}入住日期`, '') || form?.startTime
  const endTime = get(form, `${entityPrefixForTrip}离店日期`, '') || form?.endTime
  let from_city = get(form, `${entityPrefixForTrip}出发地`)
    ? JSON.parse(get(form, `${entityPrefixForTrip}出发地`))[0]['label']
    : ''
  let to_city = get(form, `${entityPrefixForTrip}目的地`)
    ? JSON.parse(get(form, `${entityPrefixForTrip}目的地`))[0]['label']
    : ''

  if (upgradeToMicroservice) {
    const from_city_str = get(form, `originCity`) ? get(form, `originCity`)[0]?.name : ''
    from_city = from_city_str?.split('/')?.pop()
    const to_city_str = get(form, `arrivalCity`) ? get(form, `arrivalCity`)[0]?.name : ''
    to_city = to_city_str?.split('/')?.pop()
  }
  return { date, from_city, to_city, startTime, endTime }
}

export const track = (params: any) => {
  api.invokeService('@common:set:track', params)
}

export const goThirdResource = async (TK: string, params: any, otherParams?: any) => {
  const otherP = otherParams || {}
  const { platform, checked, taxiUrl, isTaxi, orderId, isNew } = otherP
  const result = await Promise.all([
    api.invokeService('@mall:get:travel:intent', { type: TK }),
    api.invokeService('@mall:get:mall:auth:query', { type: TK })
  ])
  let items = result[0] && result[0].items ? result[0].items : []
  const authQuery = result[1] || ''
  items.forEach((i: any) => {
    if (taxiUrl && i?.realm === 'MALL') {
      i.source = taxiUrl
      i.sourceType = 'OPEN_LINK'
    }
    if (isTaxi && orderId && i?.realm === 'MALL') {
      i.source = i.source + `${orderId}`
    }
    if (!i.source.includes('token')) {
      if (i.source.endsWith('wcar')) {
        i.source = i.source.replace('wcar', 'wmhcar')
        i.sourceType = 'OPEN_LINK'
      }
      i.source = generateQs(i.source, { ...params, ...qs.parse(authQuery) })
    }
    if (platform) {
      const power = i.powers && i.powers.length ? i.powers[0] : undefined
      i.disabled = checked && platform.length > 1 ? !platform.includes(power) : false
    }
  })

  if (isNew && taxiUrl) {
    items = items.filter(i => i?.realm === 'MALL')
  }
  api.thirdResources.deleteByType(TK)
  api.thirdResources.add(items)
  if (items.length > 0) {
    const services = {
      token: () => {
        return new Promise((resolve, reject) => {
          setTimeout(() => resolve({ RD: { c: 3 } }), 1000)
        })
      }
    }
    const result2 = await api.request({
      type: TK,
      services: services
    })
  } else {
    // 判断是否提供订购渠道
    return api.open('@home5:ShowMallInfoModal')
  }
}
export const handleOrderClick = (data: any, isNew?: boolean, entityPrefixForOrder?: string) => {
  if (isNew) {
    const taxiUrl = data ? get(data?.form, `${entityPrefixForOrder}taxiUrl`) : null
    const type = get(data?.form, `订单类型`)
    if (type === '用车') {
      api.invokeService('@common:set:track', {
        key: 'mytrips_onthego_click',
        actionName: '查看用车订单点击'
      })
      return goThirdResource(`BUTTON_TAXI_ORDER_DETAIL`, {}, { taxiUrl })
    } else {
      const orderId = get(data?.form, `${entityPrefixForOrder}订单号`, '')
      const orderType = type === '飞机' ? 'FLIGHT' : type === '火车' ? 'TRAIN' : 'HOTEL'
      const TK = `BUTTON_${orderType}_ORDER_DETAIL` || 'BUTTON_TRAVEL_ORDER'
      const orderState = get(data?.form, `${entityPrefixForOrder}订单状态`, '')
      const params = getTypeUrl(orderType, orderId, orderState)
      return goThirdResource(TK, params)
    }
  } else if (!entityPrefixForOrder) {
    const type = data?.orderType?.toUpperCase()
    const platform = data?.bookingPlatform
    const orderId = data?.initialOrderNo
    const orderStatus = data?.orderChildren?.[0]
    const TK = `BUTTON_${type}_ORDER_DETAIL` || 'BUTTON_TRAVEL_ORDER'
    const params = getTypeUrl(type, orderId, orderStatus)
    return goThirdResource(TK, params, { isTaxi: type === 'TAXI', orderId })
  }
  const type = get(data, 'entity.type', '')
  const parentId = get(data, 'entity.parentId')
  const platform = get(data, `E_${parentId}_订票平台`, '')
  const white = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI']
  const status = data[Object.keys(data).find(o => !!o.endsWith('订单状态')) || '']
  const statusList = ['退票', '退订', '退订/离店'] // @i18n-ignore
  const isRefund = statusList.includes(status) && data?.useCount < data?.totalCount
  if (platform === '合思商城' && white.includes(type)) {
    if (type !== 'TRAIN' && isRefund) {
      return false
    }
    const orderId = get(data, `E_${parentId}_订单号`, '')
    const orderType = get(data, `E_${parentId}_订单状态`, '')
    const TK = `BUTTON_${type}_ORDER_DETAIL` || 'BUTTON_TRAVEL_ORDER'
    const params = getTypeUrl(type, orderId, orderType)
    return goThirdResource(TK, params, { isTaxi: type === 'TAXI', orderId })
  } else {
    return false
  }
}

const getTypeUrl = (type: string, orderId: string, orderType: string) => {
  if (type === 'TRAIN') {
    return { orderNo: orderId, orderType: orderType === '改签' ? 2 : orderType === '退票' ? 3 : 1 }
  }
  if (type === 'HOTEL') {
    return { detailId: orderId }
  }
  return { orderId, language: i18n.currentLocale }
}

const generateQs = (link: string, params: any) => {
  const str = qs.stringify(params)
  const type = /\?/.test(link) ? '&' : '?'
  return `${link}${type}${str}`
}

export function getDiffDay(startTime: number, endTime: number) {
  if (startTime && endTime) {
    const diff = moment(endTime)
      .startOf('date')
      .diff(moment(startTime).startOf('date'), 'days')
    return diff ? diff : false
  }
  return false
}

export const airlineCodeList = ["AQ", "A6", "9H", "8L", "3U", "CA", "CN", "CZ", "DR", "DZ", "EU", "FM", "FU",
  "BK", "GY", "GT", "GS", "GJ", "G5", "HO", "Y8", "SC", "UQ", "KN", "HU", "GX",
  "YI", "TV", "RY", "QW", "PN", "OQ", "NS", "MF", "MU", "LT", "KY", "JR", "ZH",
  "JD", "9C"]
