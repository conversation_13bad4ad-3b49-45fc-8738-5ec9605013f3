/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2022-03-21 17:16:40
 */
import React, { useEffect, useState } from 'react'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Button } from '@hose/eui-mobile'
import { T } from '@ekuaibao/i18n'
import { getDiffDay } from './formatHelper'
import { Fetch } from '@ekuaibao/fetch'
import { CardProps } from "./index"
import { TwoToneGeneralTrain, FilledGeneralTrain } from '@hose/eui-icons';
const { getWeek, getDiff } = api.require('@bill/elements/datalink/dataLinkUtil')

export default (props: CardProps) => {
    const { onBuy, dataSource, toOrderDetail } = props
    const { date, train_type, train_number, train_site, from_city, to_city, startTime, endTime, train_ticket_number, train_site_type, train_entrance, order, title, code, isNormalView, from, taxiUrl } = dataSource
    const [translateObj, setTranslateObj] = useState({ from_city, to_city, train_site })
    useEffect(() => {
        api.invokeService('@common:get:translate', {
            from_city,
            to_city,
            train_site,
            train_entrance
        }).then((result: any) => {
            setTranslateObj(result)
        })
    }, [dataSource.from_city])
    const addOneDay = getDiffDay(startTime, endTime)
    const format = i18n.currentLocale === 'en-US' ? "MM.DD" : 'MM月DD日'
    const dateStr = `${moment(date).format(format)} ${getWeek(date)}`
    return (
        <div className={style[isNormalView || from === 'next' ? 'trip_items_card_all' : 'trip_items_card']}>
            <div className="top_view">
                <div className="l_view">
                    <div className="title-view">
                        <TwoToneGeneralTrain fontSize={22} />
                        <span className='title'>{i18n.get('火车')}</span>
                    </div>
                    <div className={from === 'next' ? 'info_view noMaxWidth' : 'info_view'}>
                        {from === 'next' && order ? (
                            <div className="train_info_o">
                                {dateStr && <span>{dateStr}</span>}
                                {train_number && <><span className="line"></span><span>{train_number}</span></>}
                                {train_entrance && <><span className="line"></span><span>{translateObj.train_entrance}</span></>}
                                {train_site && <><span className="line"></span><span>{translateObj.train_site}</span></>}
                            </div>
                        ) : (
                            <>
                                <div className="info_o">
                                    {`${translateObj.from_city} - ${translateObj.to_city}`}
                                </div>
                                <div className="date">
                                    {dateStr}
                                </div>
                            </>
                        )}
                    </div>
                </div>
                {from !== 'next' && <div className="r_view">
                    <Button
                        category={isNormalView ? "secondary" : "ghost"} theme="highlight"
                        onClick={(e: any) => {
                            e?.stopPropagation()
                            if (order) {
                                toOrderDetail && toOrderDetail(true)
                            } else {
                                onBuy && onBuy('TRAIN')
                            }
                        }}
                    >
                        <T name={order ? '查看订单' : '买车票'} />
                    </Button>
                </div>}
            </div>
            {title && code && isNormalView ?
                <div className={style['application_info']}>
                    <span>{title}</span>
                    <span className="line"></span>
                    <span>{code}</span>
                </div> : null
            }
            {order && from === 'next' && (
                <>
                    <div className="bottom_view">
                        <div className="city_info from">
                            {addOneDay && <div className="add"></div>}
                            <div className="time">{moment(startTime).format('HH:mm')}</div>
                            <div className="city">{translateObj.from_city}</div>
                        </div>
                        <div className="use_time">
                            <div>{getDiff(startTime, endTime)}</div>
                            <div className="check_time">
                                <FilledGeneralTrain className="showIcon" />
                            </div>
                        </div>
                        <div className="city_info to">
                            <div className={addOneDay ? "time addOneDay" : "time"}>
                                {addOneDay && <div className="add">{`+${addOneDay}`}</div>}
                                {moment(endTime).format('HH:mm')}
                            </div>
                            <div className="city right">{translateObj.to_city}</div>
                        </div>
                    </div>
                    {
                        taxiUrl && (
                            <div className={style['bottom_btn_view']}>
                                <span className="text">{i18n.get('一键叫车省心省力')}</span>
                                <Button
                                    category="secondary" theme="highlight"
                                    onClick={(e: any) => {
                                        e?.stopPropagation()
                                        onBuy && onBuy('TAXI')
                                    }}
                                >
                                    <T name={'去车站'} />
                                </Button>
                            </div>
                        )
                    }
                </>)
            }
        </div >)

}