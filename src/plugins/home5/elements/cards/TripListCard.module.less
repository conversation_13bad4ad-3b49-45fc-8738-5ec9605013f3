.card_trip_list {
  display: flex;
  justify-content: space-between;
  border-radius: 0.16rem;
  background-color: #ffffff;
  transition: 0.1s all ease-in-out;
  flex-direction: column;
  :global {
    .eui-card-header-title {
      font: var(--eui-font-head-b2);
      color: var(--eui-text-title);
    }
    .card_list {
      width: 100%;
      display: flex;
      flex-direction: column;
      & > div {
        padding-bottom: 0.16rem;
      }
      & > div:last-child {
        padding-bottom: 0;
      }
      .no_travel {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
  }
}

