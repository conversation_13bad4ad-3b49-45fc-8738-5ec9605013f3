@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.home5-guide-wrap {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: @color-white-1;
  .btn-wrap {
    display: flex;
    justify-content: center;
    width: 100%;
    position: absolute;
    bottom: 1.12rem;
    left: 0;
    flex-direction: column;
    align-items: center;
    .btn-tips {
      .font-size-1;
      margin-top: @space-5;
      font-weight: 400;
      color: @color-black-3;
    }
    .guide-btn {
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32px;
      font-weight: 500;
      color: @color-white-1;
      width: 320px;
      height: 96px;
      background: linear-gradient(315deg,var(--brand-6) 0%,var(--brand-5) 100%);
      box-shadow: 0px 0px 32px 0px @color-brand-4,0px 4px 16px 0px @color-brand-5;
      border-radius: 48px;
    }
  }
  .btn-bottom {
    padding-bottom: @space-4;
    width: 100%;
    background: @color-white-4;
    box-shadow:0px -2px 16px 0px rgba(29,43,61,0.06);
    position: fixed;
    bottom: 0;
    text-align: center;
    .guide-btn {
      margin: @space-4 @space-7 0;
      height: 80px;
      cursor: pointer;
      font-size: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      color: @color-white-1;
      background: linear-gradient(315deg,var(--brand-6) 0%,var(--brand-5) 100%);
      box-shadow: 0px 0px 32px 0px @color-brand-4,0px 4px 16px 0px @color-brand-5;
      border-radius: 48px;
    }
    .btn-tips {
      .font-size-1;
      margin-top: @space-2;
      font-weight: 400;
      color: @color-black-3;
    }
  }

  .guide-index {
    display: flex;
    width: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-left: @space-7; //24px
    .guide-img {
      width: 100%;
      height: 854px;
      background-image: url(../images/home5-guide.png);
      background-position: inherit;
      background-size: 100%;
      background-repeat: no-repeat;
      padding-left: @space-7; //24px
      position: relative;
      margin-bottom: 145px;
      .guide-old-home {
        cursor: pointer;
        position: absolute;
        top: 24px;
        left: -10px;
        font-size: 24px;
        font-weight: 400;
        color: @color-black-3;
      }
    }
  }

  .guide-rule-wrap, .guide-items-wrap {
    padding: 0 @space-7;
    position: relative;
    h1 {
      margin: @space-9 0;
      font-size: 48px;
      font-weight: 500;
      color: @color-black-1;
    }
    .guide-title {
      margin-top: 48px;
      margin-bottom: 80px;
      color: #000000;
      .guide-ekb {
        font-weight: 200;
        font-size: 60px;
      }
      .guide-role {
        font-weight: 600;
        font-size: 54px;
      }
    }
    .guide-mask{
      position: fixed;
      top: 0;
      left: 0;
      background: rgba(255,255,255,0);
      width: 100%;
      height: 100%;
    }
  }

  .bottom-rule {
    position: fixed;
    bottom: 0.16rem;
    width: 100%;
    text-align: center;
  }
  .bottom-tips {
    .font-size-1;
    font-weight: 400;
    color: @color-black-3;
  }
  .scroll {
    overflow: auto;
    height: 100%;
  }
  .guide-items-wrap {
    padding-bottom: 160px;
    width: 100px;
    white-space: nowrap;
    overflow: hidden;
    h1 {
      margin: @space-7 0 0 0;
    }
    .avatar {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: @space-7 0;
      > img {
        width: 320px;
        height: 256px;
      }
    }
  }
  .animate {
    width: 100%;
    opacity: 1;
    left: 0%;
  }

}

.guide-font {
  position: absolute;
  width: 100%;
  padding-left: 10%;
  bottom: 150px;
  div {
    font-size: 80px;
    font-weight: 500;
    color: @color-black-1;
  }
  div:last-child {
    font-size: 32px;
    font-weight: 400;
    color: @color-black-3;
  }
}
.protocol-modal-wrap{
  width: 75% !important;
  height: 51% !important;
  .am-modal-content{
    padding-top: .32rem !important;
    border-radius: .16rem !important;
    display: flex;
    flex: 1;
    flex-direction: column;
  }
  .am-modal-header{
    .am-modal-title{
      font-size:.36rem !important;
      color: #1D2B3D;
      font-weight: 500;
    }
  }
  .am-modal-body{
    padding: 0 .32rem .32rem !important;
    text-align: left;
    .protocol-content{
      overflow-y: auto;
    }
  }
  .am-modal-footer{
    padding-top: .32rem !important;
  }
  .am-modal-button{
    height: .92rem !important;
    font-size: .32rem !important;
    line-height: .92rem !important;
    font-weight: 400;
  }
}