import React, { Component } from 'react'
import lessClass from './HomeAd.module.less'
import { app as api } from '@ekuaibao/whispered'
const YouJiang = api.require<any>('@images/youjiang.gif')
const YouJinag_Close = api.require<any>('@images/youjiang_close.png')

interface Props {
  [props: string]: any
}

interface State {
  showFloatAd: boolean
  url: string
}

/**
 * @description 首页调查问卷
 * @Creator 鲍帅朋
 * @Date 2021-11-08
 */
export default class HomeAd extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      url: '',
      showFloatAd: false // 是否显示漂浮的浮窗
    }
  }

  componentDidMount() {
    const userInfo = api.getState('@common').me_info
    const paramObj = {
      companyId: userInfo.staff.corporationId.id,
      companyName: userInfo.staff.corporationId.name,
      userName: userInfo.staff.name,
      phoneNum: userInfo.staff.cellphone,
      id: userInfo.staff.id
    }

    const _url = `https://www.surveyplus.cn/lite/1009553322968064?companyId=${paramObj.companyId}&userName=${paramObj.userName}&externalUserId=${paramObj.id}&platform=${window.__PLANTFORM__}`
    this.setState({ url: _url }, () => {
      this.homAdShow()
    })
  }

  /**
   * 是否显示首页的漂浮广告
   */
  homAdShow = () => {
    const _homeAdLocalCloseLastTime = localStorage.getItem('home_ad_close_last_time')
    if (!_homeAdLocalCloseLastTime) {
      this.setState({
        showFloatAd: true
      })
    } else {
      const _now = Date.now()
      const _cha = _now - Number(_homeAdLocalCloseLastTime)
      const _resultCha = _cha / (1000 * 60 * 60)
      if (_resultCha > 48) {
        this.setState({
          showFloatAd: true
        })
      }
    }
  }

  closeHomeAd = () => {
    localStorage.setItem('home_ad_close_last_time', Date.now() + '')
    this.setState({
      showFloatAd: false
    })
  }

  openLink = () => {
    let link = this.state.url
    // console.log('this.state.url', this.state.url)
    if (window.__PLANTFORM__ === 'FEISHU') {
      link = encodeURI(link)
    }
    api.invokeService('@layout:open:link', link)
  }

  render() {
    const { showFloatAd } = this.state

    if (showFloatAd && !window.PLATFORMINFO?.isLocalization) {
      return (
        <div className={lessClass['homeAdPannel']}>
          <div onClick={this.openLink}>
            <img src={YouJiang} alt="" />
          </div>
          <div>
            <img onClick={this.closeHomeAd} src={YouJinag_Close} alt="" />
          </div>
        </div>
      )
    } else {
      return null
    }
  }
}
