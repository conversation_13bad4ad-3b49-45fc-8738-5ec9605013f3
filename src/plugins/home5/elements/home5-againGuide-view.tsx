/**
 * Created by <PERSON><PERSON> on 2019/3/1.
 */

// @ts-ignore
import styles from './home5-againGuide-view.module.less'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { IHomeTemplateVersion } from '../homePage/HomeCardManage/Types'
const EKBIcon = api.require<any>('@elements/ekbIcon')
export interface Props {
  homePageVersion: IHomeTemplateVersion
}

export default function Home5AgainGuideView(props: Props) {
  // tslint:disable-next-line:prefer-const
  let { homePageVersion } = props

  const handleUpdateTemp = (isUpdate: boolean) => {
    if (isUpdate) {
      api.go('/home5-preview-setting', false)
    }
  }
  
  const isUpdate = homePageVersion?.isHaveNewVersion
  return (
    <ul className={styles['home5AgainGuideView-wrap']}>
      <li className="home5AgainGuideView-item" onClick={() => handleUpdateTemp(isUpdate)}>
          <div className="home5AgainGuideView-item-content-wrap">
            <span>{i18n.get('管理员模版')}</span>
            {isUpdate ? (
              <div className="new-temp-layout">
                <div className="new-temp-tag"></div>
                <EKBIcon name="#EDico-right-default" />
              </div>
            ) : (
              <span className="temp-unset">{i18n.get('无可用模板')}</span>
            )}
          </div>
        </li>
    </ul>
  )
}
