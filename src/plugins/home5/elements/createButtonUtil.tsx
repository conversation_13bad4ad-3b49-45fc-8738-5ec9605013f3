import { app, app as api } from '@ekuaibao/whispered'
import { noSpecificationAlert } from '../../../lib/util'
import { enableNewBillMode } from '../../../lib/featbit'

/**
 * @param _this
 * @param isOpenAssociation 是否是钉钉场景群
 */
export function createNewBill(_this: any, isOpenAssociation?: boolean) {
  _this.setState({ loading: true })
  api.invokeService('@home:save:specification:after:filtered', {})
  const Universal = app.getState()['@common']?.powers?.Universal
  if (Universal) {
    return api.open('@home5:StandardHome5CreateModal', { params: { hiddenFooter: isOpenAssociation } })
  }
  if (window.isNewHome) {
    if (enableNewBillMode()) {
      return api.go(`/new-bill${isOpenAssociation ? '?hiddenFooter=true' : ''}`)
    }
    return api.open('@home5:Home3CreateModal', { params: { hiddenFooter: isOpenAssociation } })
  }
  return api.invokeService('@home:get:specification:with:version').then((res: any) => {
    _this.setState({ loading: false })
    if (!res.specification_group.length) return noSpecificationAlert()
    return api.open('@home:HomeCreateModal', { data: res.specification_group })
  })
}
