//variables
@size : 160px;
@white: white;

//cube only
.cube-folding {
  width: @size;
  height: @size;
  display: inline-block;
  transform: rotate(45deg);
  font-size: 0;

  span {
    position: relative;
    width: @size/2;
    height: @size/2;
    transform: scale(1.1);
    display: inline-block;

    &::before {
      content: '';
      background-color: @white;
      position: absolute;
      left: 0;
      top: 0;
      display: block;
      width: @size/2;
      height: @size/2;
      -moz-transform-origin: 100% 100%;
      -ms-transform-origin: 100% 100%;
      -webkit-transform-origin: 100% 100%;
      transform-origin: 100% 100%;
      animation: folding 2.50s infinite linear both;
    }
  }

  .leaf2 {
    transform: rotateZ(90deg) scale(1.1);

    &::before {
      animation-delay: .30s;
      background-color: darken(@white, 5%);
    }
  }

  .leaf3 {
    transform: rotateZ(270deg) scale(1.1);

    &::before {
      animation-delay: .90s;
      background-color: darken(@white, 5%);
    }
  }

  .leaf4 {
    transform: rotateZ(180deg) scale(1.1);

    &::before {
      animation-delay: .60s;
      background-color: darken(@white, 10%);
    }
  }
}

//animation
@keyframes folding {

  0%,
  10% {
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }

  25%,
  75% {
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }

  90%,
  100% {
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}

@keyframes text {

  0%,
  10% {
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }

  25%,
  75% {
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }

  90%,
  100% {
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}

//with loading text and shadow
.cube-wrapper {
  position: fixed;
  left: 50%;
  top: 50%;
  margin-top: -@size;
  margin-left: -@size;
  width: @size*2;
  height: @size*2;
  text-align: center;

  //shadow
  &:after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: -40px;
    margin: auto;
    width: @size*1.8;
    height: 12px;
    background-color: rgba(black, .1);
    filter: blur(4px);
    border-radius: 100%;
    z-index: 1;
    animation: shadow 0.5s ease-in infinite alternate;
  }

  .loading {
    font-size: 36px;
    letter-spacing: 0.1em;
    display: block;
    color: @white;
    position: relative;
    top: @size/2;
    z-index: 2;
    animation: text 0.5s ease-in infinite alternate;
    font-weight: 500;
  }
}

@keyframes text {
  100% {
    top: (@size/2) + 10;
  }
}

@keyframes shadow {
  100% {
    bottom: -36px;
    width: @size*2;
  }
}

////page styles

.home5-loading-wrap {
  min-height: 100%;
  background: #00c6ff;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to left, var(--brand-base), var(--brand-base));
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to left, #59ddec, #0ab3c7);
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

  h1 {
    font-size: 56px;
    display: block;
    text-align: center;
    color: #fff;
    padding: 100px 40px;
    line-height: 72px;
  }
}