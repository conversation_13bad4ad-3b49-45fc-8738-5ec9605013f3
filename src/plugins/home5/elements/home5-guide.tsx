import { app } from '@ekuaibao/whispered'
import './home5-guide.less'
import guideAttention from './guide-role-attention.module.less'
import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import GuideCard from './guide-cards/GuideCard'
import GuideItem from './guide-cards/GuideItem'
// import EkbIcon from '../../../elements/ekbIcon'
const EkbIcon = app.require('@elements/ekbIcon')
import get from 'lodash/get'
import { Modal } from 'antd-mobile'
import ProtocolContWrap from './protocol/ProtocolContWrap'
import { isOPG } from '../../../hosting/Utils'

// const SVG_AVATAR_NULL = require('../../../images/avatar-null.svg')
const SVG_AVATAR_NULL = app.require('@images/avatar-null.svg')

interface Props {
  guideQuestionnaires: any[]
  me_info: any
}

interface States {
  step: number
  ruleData: any
  itemSelected: boolean
  animate: boolean
  animate1: boolean
}

interface Map {
  [key: string]: any
}

@EnhanceTitleHook(' ')
@EnhanceConnect((state: any) => ({
  me_info: state['@common'].me_info
}))
@connect((store: any) => ({
  isReaded: get(store, '<EMAIL>'),
  guideQuestionnaires: get(store.states['@home5'], 'guideQuestionnaires', [])
}))
export default class Home5Guide extends Component<Props, States> {
  private map: Map
  constructor(props: Props) {
    super(props)
    this.state = {
      step: 0,
      ruleData: [],
      itemSelected: false,
      animate: true,
      animate1: false,
      modalShow: true
    }
    this.map = {
      // 0: this.renderGuideIndex,
      0: this.renderSelectRule,
      1: this.renderSelectItemForRule
    }
  }

  componentDidMount() {
    api.store.dispatch('@home5/getQuestionnaires')()
    api.invokeService('@layout:change:header:title', ' ')
    if (isOPG()) {
      setTimeout(() => {
        console.log(api.getState()['@common'], 'api.getState()')
        const { userId, cellphone } = api.getState()['@common'].me_info.staff
        // 调用接口判断是否阅读协议
        api.store.dispatch('@home5/getProtocolIsOrNotRead')({
          userId,
          cellPhone: cellphone,
          email: '',
          userName: ''
        })
      }, 300)
    }
  }

  nextStep = (data?: any) => {
    const { step } = this.state
    this.setState({ step: step + 1, ...data })
  }

  startNewFeature = () => {
    this.sendShenceData('versionV5', { actionName: i18n.get('开启新体验') })
    this.nextStep()
    setTimeout(() => {
      this.setState({ animate: true })
    }, 0)
  }

  selectRule = (data: any) => {
    this.sendShenceData('roleV5', { actionName: i18n.get('选择角色') })
    this.nextStep({ ruleData: data })
    setTimeout(() => {
      this.setState({ animate1: true })
    }, 0)
  }

  selectItem = (data: any) => {
    data.itemSelected = !data.itemSelected
    this.forceUpdate()
  }

  gotoHome = () => {
    const { ruleData = [] } = this.state
    const roleName = ruleData.name
    const codes: string[] = []
    const questionnaireItemvalues: string[] = []
    ruleData &&
      ruleData.questionnaireItems.forEach((e: any) => {
        if (e.itemSelected === true) {
          codes.push(e.code)
          questionnaireItemvalues.push(e.value)
        }
      })
    this.sendShenceData('enterV5', { actionName: questionnaireItemvalues, roleName: roleName })
    const data = { id: ruleData.id, codes }
    api.store.dispatch('@homePage/setQuestionnaires')(data, () => {
      window.isNewHome = true
      api.go('/home5', true)
    })
  }

  sendShenceData = (targetName: string, data: any) => {
    const platform = window.PLATFORM_FEATURE
    const { me_info } = this.props
    api.track(targetName, {
      ...data,
      staffId: get(me_info, 'staff.id'),
      time: new Date(),
      $manufacturer: platform.version.manufactory || '',
      $model: platform.version.model || '',
      $os: platform.version.os || ''
    })
  }

  gotoOldHome = () => {
    this.sendShenceData('versionV5', { actionName: i18n.get('继续使用旧版') })
    window.isNewHome = false
    api.go('/home', true)
  }

  // 第一页
  renderGuideIndex = () => {
    return (
      <>
        {window.__PLANTFORM__ !== 'HUAWEI' && (
          <div className="guide-index">
            <div className="guide-img">
              <span className="guide-old-home" onClick={this.gotoOldHome}>
                <EkbIcon name="#EDico-left-default" />
                {i18n.get('继续使用旧版')}
              </span>
            </div>
          </div>
        )}
        <div className="btn-wrap">
          <div className="guide-font">
            <div>{i18n.get('你想要')}</div>
            <div>{i18n.get('的首页来了')}</div>
            <div>{i18n.get('便捷体验，为你定制')}</div>
          </div>
          <div className="guide-btn" onClick={this.startNewFeature}>
            {i18n.get('开启新体验')}
          </div>
        </div>
      </>
    )
  }

  // 第二页 选角色
  renderSelectRule = () => {
    const { guideQuestionnaires = [], isReaded = 1 } = this.props
    const { animate, modalShow } = this.state
    return (
      <div className={`guide-rule-wrap ${animate ? 'animate' : ''}`}>
        <div className="guide-title">
          <div className="guide-ekb">{i18n.get('欢迎来到易快报')}</div>
          <div className="guide-role">{i18n.get('你的角色是:')}</div>
        </div>
        {guideQuestionnaires.map((v: any) => {
          return <GuideCard key={v.id} data={v} onClick={this.selectRule} />
        })}
        {!isOPG() ? null : isReaded === 1 ? null : (
          <>
            <div className="guide-mask" onClick={this.showModal}></div>
            <Modal
              className="protocol-modal-wrap"
              visible={modalShow}
              transparent
              maskClosable={false}
              title="用户协议"
              footer={[
                {
                  text: '暂不使用',
                  onPress: () => {
                    this.handleNot()
                  }
                },
                {
                  text: '同意',
                  onPress: () => {
                    this.handleOk()
                  }
                }
              ]}
            >
              {this.protocolContent()}
            </Modal>
          </>
        )}
      </div>
    )
  }

  // 第三页 选关注点
  renderSelectItemForRule = () => {
    const { ruleData = {}, animate1 } = this.state
    return (
      <div className="w-100p scroll">
        <div className={`guide-items-wrap ${animate1 ? 'animate' : ''}`}>
          <h1>{i18n.get('你平时会关注?')}</h1>
          <div className="avatar">
            <img src={ruleData.avatar || SVG_AVATAR_NULL} />
          </div>
          <div className={guideAttention['role-attention-items']}>
            {ruleData &&
              ruleData.questionnaireItems.map((v: any) => {
                return <GuideItem key={v.code} data={v} onClick={this.selectItem} />
              })}
          </div>
        </div>
        <div className="btn-bottom">
          <div className="guide-btn" onClick={this.gotoHome}>
            {i18n.get('开启易快报')}
          </div>
        </div>
      </div>
    )
  }

  showModal = () => {
    this.setState({ modalShow: true })
  }

  closeModal = () => {
    this.setState({ modalShow: false })
  }

  handleOk = () => {
    const { userId, cellphone } = api.getState()['@common'].me_info.staff
    this.closeModal()
    api.store.dispatch('@home5/confirmAgreement')({
      userId,
      cellPhone: cellphone,
      email: '',
      userName: ''
    })
  }

  handleNot = () => {
    this.closeModal()
  }

  protocolContent = () => {
    return (
      <div className="protocol-content">
        <ProtocolContWrap></ProtocolContWrap>
      </div>
    )
  }

  render() {
    const { step } = this.state
    if (!this.props.guideQuestionnaires) {
      return null
    }
    return <div className="home5-guide-wrap">{this.map[step]()}</div>
  }
}
