@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.role-attention-items {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;

  :global {
    .attention-item {
      display: block;
      height: 80px;
      border-radius: 50px;
      width: 48%;
      margin-bottom: 16px;
      background-color: @color-white-1;
      border-color: rgba(29, 43, 61, 0.09);
      color: rgba(29, 43, 61, 1);
      transform: translate3d(0, 0, 0);
    }

    .attention-item-selected {
      background: linear-gradient(315deg, var(--brand-6) 0%, var(--brand-5) 100%);
      border: none;
      color: #fff;
    }
  }
}