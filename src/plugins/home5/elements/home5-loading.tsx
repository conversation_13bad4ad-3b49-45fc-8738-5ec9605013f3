import './home5-loading.less'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'

export default class Home5Loading extends React.Component<any, any> {
  componentDidMount() {
    setTimeout(() => {
      api.go('/home5', true)
    }, 3000)
  }

  render() {
    return (
      <div className="home5-loading-wrap">
        <h1>
          {i18n.get('稍等片刻')}
          <br />
          {i18n.get('新首页马上与你见面')}
        </h1>
        <div className="cube-wrapper">
          <div className="cube-folding">
            <span className="leaf1" />
            <span className="leaf2" />
            <span className="leaf3" />
            <span className="leaf4" />
          </div>
          <span className="loading" data-name="Loading">
            {i18n.get('正在测算数据中')}
          </span>
        </div>
      </div>
    )
  }
}
