/*
 * @Description: 体验问卷悬浮框组件
 * @Creator: sheng<PERSON><PERSON>
 * @Date: 2023-07-04 11:52:10
 */
import React, { useState, useEffect, Fragment } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { fnQuestionSensorTrack } from '../util/track'
import styles from './home5-question-float.module.less'
import question_float from '../images/question_float.png'
import close_float from '../images/close_float.png'

interface Props {
  userInfo?: any
}

const Home5QuestionFloat: React.FC<Props> = (props: Props) => {
  const { userInfo = {} } = props
  const [showImg, setShowImg] = useState<boolean>(false)

  const corpId = userInfo?.staff?.corporationId?.id
  const staffId = userInfo?.staff?.id
  const value: any = localStorage.getItem('show_question_float')
  const show_question_float: any = value ? JSON.parse(value) : []

  useEffect(() => {
    if (corpId && !show_question_float.includes(corpId)) {
      setShowImg(true)
    }
  }, [userInfo])

  const handleClick = () => {
    fnQuestionSensorTrack('Homepagefloatingwindow', i18n.get('点击悬浮窗'))
    const link = `https://www.xmplus.cn/lite/4395579051886592?externalUserId=${staffId}&_corpId=${corpId}`
    api.invokeService('@layout:open:link', link)
  }

  const handleClose = () => {
    fnQuestionSensorTrack('Homepagefloatingwindow', i18n.get('关闭悬浮窗'))
    localStorage.setItem('show_question_float', JSON.stringify(show_question_float.concat(corpId)))
    setShowImg(false)
  }

  return (
    <Fragment>
      {showImg ? (
        <div className={styles['question-float']}>
          <img className={styles['question-float-img']} src={question_float} alt="" onClick={handleClick} />
          <img className={styles['question-float-close']} src={close_float} alt="" onClick={handleClose} />
        </div>
      ) : null}
    </Fragment>
  )
}

export default Home5QuestionFloat
