import { app } from '@ekuaibao/whispered'
/**************************************
 * Created By LinK On 2019/3/4 16:28.
 **************************************/

import React from 'react'
import styles from './GuideCard.module.less'
// const SVG_AVATAR_NULL = require('../../../../images/avatar-null.svg')
const SVG_AVATAR_NULL = app.require('@images/avatar-null.svg')
const EKBIcon: any = app.require('@elements/ekbIcon')

export interface Props {
  data: any
  onClick: (data: any) => void
}

export default function GuideCard(props: Props) {
  const { data = {}, onClick } = props

  function onCardClick() {
    onClick && onClick(data)
  }

  return (
    <div className={styles['guide-card-wrap']} onClick={onCardClick}>
      <div className="guide-box">
        <img className="avatar" src={data.avatar || SVG_AVATAR_NULL} />
        <div className="rule-text">
          <div className="rule">{i18n.get(data.name)}</div>
          <div className="description">{i18n.get(data.describe)}</div>
        </div>
      </div>
      <EKBIcon className="icon" name="#EDico-right-default" />
    </div>
  )
}
