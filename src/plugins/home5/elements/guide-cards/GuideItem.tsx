/**************************************
 * Created By LinK On 2019/3/4 16:28.
 **************************************/

import React from 'react'

export interface Props {
  data: any
  onClick: (data: any) => void
}

export default function GuideItem(props: Props) {
  const { data = {}, onClick } = props
  function clickItem() {
    onClick && onClick(data)
  }

  return (
    <button onClick={clickItem} className={`attention-item ${data.itemSelected ? 'attention-item-selected' : ''}`}>
      {i18n.get(data.value)}
    </button>
  )
}
