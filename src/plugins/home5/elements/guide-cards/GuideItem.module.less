@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.guide-item-wrap, .guide-selected-item-wrap {
  width: 100%;
  height: 100px;
  border-radius: 8px;
  border: 2px solid rgba(29,43,61,0.09);
  transform: translate3d(0,0,0);
  margin-bottom: @space-5;
  background-color: @color-white-1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding-right: @space-7;

  :global {
    .icon-size {
      width: 36px;
      height: 36px;
      margin-left: 38px;
      color: @color-black-2;
    }
    .icon-select {
      width: 40px;
      height: 40px;
      color:rgba(29,43,61,0.15);
    }
    .title {
      margin-left: 16px;
      font-size: 32px;
      font-weight: 500;
      color: @color-black-1;
    }
    .attention-item {
      width: 156px;
      height: 40px;
      background: linear-gradient(315deg,var(--brand-6) 0%,var(--brand-5) 100%);
      border-radius: 20px;
    }
  }
}

.guide-selected-item-wrap {
  border: 2px solid @color-brand-2;
  :global {
    .icon-size, .title, .icon-select {
      color: @color-brand-2;
    }
  }
}
