@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.guide-card-wrap {
  &:active {
    box-shadow: none;
    background-color: @color-bg-2;
  }
  width: 100%;
  height: 180px;
  display: flex;
  align-items: center;
  border-radius: 20px;
  margin-bottom: 64px;
  background-color: #F7F8FA;
  cursor: pointer;
  justify-content: space-between;

  :global {
    .guide-box {
      display: flex;
      align-items: center;
      .avatar {
        width: 250px;
        height: 168px;
      }
      .rule-text {
        margin: @space-7 0 @space-7 @space-3;
        .rule {
          font-size: 30px;
          font-weight: 600;
          color: #1D2129;
        }
        .description {
          font-size: 24px;
          color: #4E5969;
        }
      }
    }

    .icon {
      color: #666666;
      opacity: 0.64;
      width: 24px;
      height: 20px;
      margin-right: 54px;
    }
  }
}
