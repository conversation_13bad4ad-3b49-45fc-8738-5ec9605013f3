@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.home5AgainGuideView-wrap {
  padding: 0 0 @space-12;
  list-style: none;
  overflow: auto;
  max-height: 100%;
  background: #f7f9fe;
  :global {
    .home5AgainGuideView-item {
      list-style: none;
      height: 112px;
      display: flex;
      flex-direction: row;
      align-items: center;
      background-color: @color-white-1;
      .home5AgainGuideView-item-content-wrap {
        flex: 1;
        height: 100%;
        padding: 0 @space-6;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px solid rgba(39, 46, 59, 0.08);
        .icon {
          color: @color-black-4;
        }
        span {
          user-select: none;
          font-weight: 400;
          font-size: 28px;
          line-height: 40px;
          color: #272e3b;
        }
        .temp-unset {
          font-weight: 400;
          font-size: 28px;
          line-height: 40px;
          color: rgba(39, 46, 59, 0.4);
        }
        .new-temp-layout {
          display: flex;
          flex-direction: row;
          align-items: center;
          .new-temp-tag {
            width: 16px;
            height: 16px;
            background-color: #F53F3F;
            border-radius: 50%;
            margin-right: 28px;
          }
        }
      }
    }
  }
}

.home5AgainGuideView-actionSheet-wrap {
  :global {
    div {
      color: @color-black-2;
    }
    .am-action-sheet-cancel-button {
      color: @color-black-2;
    }
  }
}
