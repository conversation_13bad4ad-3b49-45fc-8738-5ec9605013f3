import { app } from '@ekuaibao/whispered'
import React from 'react'
import { SortableElement, SortableHandle } from 'react-sortable-hoc'
const EKBIcon = app.require('@elements/ekbIcon')
import { Home5CardTypeText } from '../staticUtil'
import { cardMap } from '../util/card_map'
import SETTINGSVG from "../images/home-setting.svg"
import MOVESVG from "../images/move.svg"
import { getCardLabel } from '../homePage/HomeCardManage/utils/helps'

const DragHandle = SortableHandle(() => (
  <div className="homeEditableView-icon-wrap">
    <img src={MOVESVG} />
  </div>
))

//  需要隐藏设置按钮的菜单卡片
// const configBtnsShouldHidden = ['approve', 'completed', 'approved', 'paymentHistory', 'quickExpense']
const configBtnsShouldHidden = ['approve', 'quickExpense']

const EkbSortableItem = SortableElement(({ value, description, fnMinusCard, fnEditCard }: any) => {
  const { dynamicSupportValue, showType, type, code, sourceType, actions } = value
  let label = cardMap(value.id).label || getCardLabel(value)
  label = label === '我的常用' ? i18n.get(label) : label
  const isGroup = type === 'GROUP'
  let text
  if (isGroup) {
    text = description
  } else {
    text = (dynamicSupportValue ? i18n.get('动态显示、') : i18n.get('始终显示、')) + Home5CardTypeText()[showType]
  }
  let handleEdit = _ => fnEditCard(value)
  let showSettingBtn = true
  if (configBtnsShouldHidden.includes(code) || sourceType === "CUSTOM") {
    handleEdit = _ => { }
    showSettingBtn = false
  }
  return (
    <li className="homeEditableView-sortable-item">
      <div className="homeEditableView-sortable-item-action-wrap" onClick={_ => fnMinusCard(value)}>
        <EKBIcon name="#EDico-minus-circle" />
      </div>
      <div className="homeEditableView-sortable-item-content-wrap" onClick={handleEdit}>
        <div className="homeEditableView-sortable-item-content-left">
          <div className="homeEditableView-sortable-item-content-left-title">
            <span>{label}</span>
            {isGroup && <div>{i18n.get('分组')}</div>}
          </div>
          <span className="homeEditableView-sortable-item-content-desc">{text}</span>
        </div>
        <div className="homeEditableView-sortable-item-content-right">
          {showSettingBtn && (<img src={SETTINGSVG} />)}
          <DragHandle />
        </div>
      </div>
    </li>
  )
})

export interface Props {
  cardList: any[]
  list: any[]
  fnMinusCard: Function
  fnEditCard: Function
  fnSetState: Function
}

export default function Home5DragView(props: Props) {
  const { cardList, list, fnMinusCard, fnEditCard } = props

  const getDescription = (card: any) =>
    cardList
      .filter(c => c.pid === card.id)
      .map(c => getCardLabel(c))
      .join(i18n.get('、'))

  if (!list.length) {
    return (
      <div className="homeEditableView-blank">
        <p>{i18n.get('暂无在首页中显示的卡片')}</p>
        <p>{i18n.get('请从下方卡片中选择添加')}</p>
      </div>
    )
  }

  return (
    // index是拖拽组件必要参数，勿动
    list.map((value, index) => (
      <EkbSortableItem
        key={index}
        value={value}
        description={getDescription(value)}
        index={index}
        fnMinusCard={fnMinusCard}
        fnEditCard={fnEditCard}
      />
    ))
  )
}
