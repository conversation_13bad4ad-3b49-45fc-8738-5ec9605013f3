import React from 'react'
import styles from './mc-error.module.less'
import { T } from '@ekuaibao/i18n'
import error from './images/mc-error.png'
import { Button } from '@hose/eui-mobile'
import { toast } from '../../lib/util'
import qs from 'qs'
import { Fetch } from '@ekuaibao/fetch'
import { session } from '@ekuaibao/session-info'
import { app } from '@ekuaibao/whispered'

export default class MCError extends React.Component {
  state = { flag: true }
  getCorpId = () => {
    const { corpId } = qs.parse(window.location.search.slice(1))
    const existGP = (corpId && corpId.includes('~GP')) || Fetch.ekbCorpId.includes('~GP')
    return existGP ? corpId || Fetch.ekbCorpId : corpId ? `${corpId}~GP` : `${Fetch.ekbCorpId}~GP`
  }

  handleRefresh = async () => {
    const targetCorpId = this.getCorpId()
    const result = await Fetch.GET('/api/v1/group/client/task/queryInitTaskStatus', {
      targetCorpId: targetCorpId
    })
    const status = result?.value?.status
    if (status === 'SUCCESS') {
      Fetch.ekbCorpId = `${targetCorpId}`
      session.set('user', {
        accessToken: Fetch.accessToken,
        corpId: Fetch.ekbCorpId
      })
      app.go('/new-homepage')
    } else if (status === 'ERROR') {
      toast.fail(i18n.get('开通失败，请重试'))
      this.setState({ flag: false })
    }
  }

  handleRetry = async () => {
    const targetCorpId = this.getCorpId()
    const retryResult = await Fetch.GET('/api/v1/group/client/task/resetInitTask', {
      targetCorpId: targetCorpId
    })
    if (retryResult?.value) {
      this.setState({ flag: true })
    }
  }

  render() {
    return (
      <div className={styles['mc-error-wrapper']}>
        <div className="img">
          <img src={error} />
        </div>
        <div className="title">
          <T name="「轻共享平台」建设中" />
        </div>
        <div className="info mb-8">
          <T name="尊敬的用户：" />
        </div>
        <div className="info">
          <T name="易快报「轻共享平台」建设中，在此期间将无法使用相关服务。给大家造成不便请多包涵。" />
        </div>
        <div className="info">
          <Button
            className="btn-refresh"
            size="large"
            category="primary"
            onClick={this.state.flag ? this.handleRefresh : this.handleRetry}
          >
            <T name={this.state.flag ? '刷新' : '重试'} />
          </Button>
        </div>
      </div>
    )
  }
}
