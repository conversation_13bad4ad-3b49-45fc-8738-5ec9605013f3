@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.homeEditableView-actionBar {
  position: relative;
  display: block;
  background-color: @color-white-1;
  padding: @space-4;
  height: 116px;
  .shadow-3;
  :global {
    * {
      user-select: none;
    }
    .am-button {
      user-select: none;
      display: flex;
      align-items: center;
      justify-content: center;
      .font-size-3;
      .font-weight-3;
    }
  }
}

.homeEditableView-wrap {
  position: relative;
  overflow: auto;
  background: #f7f9fe;
  flex: 1;
  :global {
    * {
      user-select: none;
    }
    .homeEditableView-header {
      .homeEditableView-title;
      display: flex;
      background: #f7f9fe;
      align-items: center;
      justify-content: space-between;
      span {
        &:last-child {
          color: @color-brand;
        }
      }
      .add-group-icon {
        color: @color-brand;
        cursor: pointer;
        font-size: 32px;
        margin-right: 16px;
      }
    }
    .homeEditableView-title {
      .font-size-1;
      .font-weight-2;
      user-select: none;
      color: #999999;
      padding: @space-7 @space-6 @space-4;
    }
  }
}
