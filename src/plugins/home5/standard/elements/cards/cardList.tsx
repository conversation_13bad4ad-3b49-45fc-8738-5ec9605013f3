import React from 'react'
import styles from './cardList.module.less'
import { CardProps, CodeType, IMalCard } from '../../../home5.store'
import MyBillListCard from './mybill/cardList_myBill'

interface CardListProps {
  data: CardProps
  [propsName: string]: any
  me_info?: any
  isHoseMall?: boolean,
  mallCardDataList?: IMalCard[]
}

export default class CardList extends React.Component<CardListProps, any> {
  renderContent = () => {
    const { data } = this.props
    if (data.code === CodeType.myBill) {
      // @ts-ignore
      return <MyBillListCard data={data} />
    }
    return <></>
  }

  render() {
    const { data } = this.props
    const { detail } = data || {}
    if (data.dynamicSupportValue && detail) {
      const { value } = detail.prompt
      if (!value) return null
    }
    return <div className={styles['cardList-wrap']}>{this.renderContent()}</div>
  }
}
