import React from 'react'
import styles from './cardAllData.module.less'
import { CardProps } from '../../../home5.store'
import AuditPending from './auditpending/cardListAuditPending'
interface CardListProps {
  data: CardProps
  [propsName: string]: any
}

export default class CardAllData extends React.Component<CardListProps> {
  renderContent = () => {
    const { data } = this.props
    return <div className={styles['cardAllData-wrap']}><AuditPending data={data} /></div>
  }
  render() {
    return this.renderContent()
  }
}
