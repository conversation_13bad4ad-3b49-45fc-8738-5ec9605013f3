/*
 * @Description:
 * @Creator: chencan<PERSON>han
 * @Date: 2021-07-19 10:48:41
 */
/**
 * @description 即将出发的行程
 * <AUTHOR>
 */
import { app } from '@ekuaibao/whispered'
import React, { useState, useEffect } from 'react'
import styles from './UpComingTrip.module.less'
import { T } from '@ekuaibao/i18n'
import TripItem from './TripCardItem'
import { get } from 'lodash'
import TripErrorBoundry from './TripErrorBoundary'
const EKBIcon: any = app.require('@elements/ekbIcon')

type IProps = {
  data: any
  title: string
  recentTripData: any
  isOrder: boolean
  tripAssistData: any[]
  prefixMap: {
    tripPrefix: string
    orderPrefix: string
  }
}

export default function UpComingTrip(props: IProps) {
  useEffect(() => {
    app.invokeService('@common:set:track', {
      key: 'mytrips_next_view',
      actionName: '即将开始行程卡片的pv'
    })
  }, [])
  const onGoTripListClick = () => {
    const { id, detail } = props?.data || {}
    const dataLinkEntity = get(detail, 'dataLinkEntity')
    if (!id || !dataLinkEntity) {
      return
    }
    const groupType = get(dataLinkEntity, 'platformId.groupType', '')
    const type = get(dataLinkEntity, 'platformId.type', '')
    app
      .require<any>('@importDataLink/utils')
      .call()
      .then(({ fnAikeIsSavePhone }: any) => {
        fnAikeIsSavePhone({ groupType, type }).then((res: any) => {
          console.log(res, 'resresresresres')
          if (res.isOpenNext) {
            const fields = dataLinkEntity.fields
            const obj: any = fields.find((line: any) => line.name.endsWith('_name'))
            const key = obj && obj.name
            const codeName = get(
              fields.find((item: any) => item.name.endsWith('_code')),
              'label'
            )
            const placeholder = codeName ? `${i18n.get('请输入名称或')}${codeName}` : i18n.get('搜索')
            const selectedKey = type === 'TRAVEL_MANAGEMENT' ? 'notStart' : 'all'
            app.go(
              `/mine/dataLinkList/${id}/${key}/${encodeURIComponent(placeholder)}/${selectedKey}/${type}/${true}`,
              false
            )
          }
        })
      })
    app.invokeService('@common:set:track', {
      key: 'mytrips_all_view',
      actionName: '行程集合页pv',
      from: 'next'
    })
  }
  let hiddenAction = false
  if (props?.recentTripData) {
    const t = get(props?.recentTripData?.form, `订单类型`)
    hiddenAction = t === '用车'
  }

  const rednerHoseTravelTips = (text: string = '无需垫资，无需开票，点此预订'): JSX.Element => (
    <div className="up_coming_trip_content_hose_tips">
      <span className="up_coming_trip_content_hose_tips_text">{text}</span>
      <span className="up_coming_trip_content_hose_tips_badge"></span>
    </div>
  )

  return (
    <TripErrorBoundry>
      <div className={styles['up_coming_trip']}>
        <div className="up_coming_trip_header">
          <div className="header_title">
            <T name={hiddenAction ? '出行中' : props?.title} />
          </div>
          <div className="header_action">
            {!hiddenAction && (
              <div className="action" onClick={onGoTripListClick}>
                <T name="行程" />
                <EKBIcon className="icon-right-standard" name="#ico-7-right" />
              </div>
            )}
          </div>
        </div>
        <div className="up_coming_trip_content">
          <TripItem
            isNew
            from={'next'}
            isOrder={props?.isOrder}
            otherData={props?.tripAssistData}
            data={props?.recentTripData}
            entityPrefixForOrder={props?.prefixMap?.orderPrefix}
            entityPrefixForTrip={props?.prefixMap?.tripPrefix}
            onGoTripListClick={onGoTripListClick}
          />
        </div>
      </div>
    </TripErrorBoundry>
  )
}
