import React from 'react'
import styles from './StandardHomepage.module.less'
import { app as api } from '@ekuaibao/whispered'
import { onCardClick } from '../../../elements/cards/url'
const { fnScanClick } = api.require('@home/elements/util')
const EKBIcon: any = api.require('@elements/ekbIcon')
const HOME_LOAN = require('../../../images/standard/home-loan.png')
const HOME_REQUSITION = require('../../../images/standard/home-requsition.png')
type IProps = {
  standardTrack?: (key: string, options: any) => void
}

const Title: React.FC<IProps> = (props) => {
  const handleClick = () => {
    props.standardTrack('homeQuickEntryModule', { souce: 'home', actionType: 'take_tickets' })
    fnScanClick()
  }
  return (
    <div className="homepage-title">
      <div className="entry">快捷入口</div>
      <div className="scan">
        <EKBIcon className="icon" name="#ico-7-icon_scan" />
        <span className="name" onClick={handleClick}>智能识票</span>
      </div>
    </div>
  )
}

const MyRequsition: React.FC<IProps> = (props) => {
  const handleClick = () => {
    const data = {
      code: 'myRequisition'
    }
    props.standardTrack('homeQuickEntryModule', { souce: 'home', actionType: 'my_apply_items' })
    onCardClick(api, data, true)
  }
  return (
    <div className="homepage-event-item" onClick={handleClick}>
      <img className="event-item-icon" src={HOME_REQUSITION} />
      <div className="event-item-content">
        <div className="event-item-title">{i18n.get('我的申请事项')}</div>
        <div className="event-item-desc">{i18n.get('可以在此查看我的申请')}</div>
      </div>
      <EKBIcon className="icon-right-standard" name="#ico-7-right" />
    </div>
  )
}

const MyLoan: React.FC<IProps> = (props) => {
  const handleClick = () => {
    const data = {
      code: 'myLoan'
    }
    props.standardTrack('homeQuickEntryModule', { souce: 'home', actionType: 'my_loan' })
    onCardClick(api, data, true)
  }
  return (
    <div className="homepage-event-item" onClick={handleClick}>
      <img className="event-item-icon" src={HOME_LOAN} />
      <div className="event-item-content">
        <div className="event-item-title">{i18n.get('我的借款')}</div>
        <div className="event-item-desc">{i18n.get('可以在此查看我的借款')}</div>
      </div>
      <EKBIcon className="icon-right-standard" name="#ico-7-right" />
    </div>
  )
}

const StandardHomepage: React.FC<IProps> = (props) => {
  return (
    <div className={styles['standard-homepage-wrapper']}>
      <Title {...props} />
      <MyRequsition {...props}/>
      <MyLoan {...props}/>
    </div>
  )
}

export default StandardHomepage
