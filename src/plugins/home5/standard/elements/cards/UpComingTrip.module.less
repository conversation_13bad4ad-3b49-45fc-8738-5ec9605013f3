@import '~@eku<PERSON>bao/eui-styles/less/token.less';

.up_coming_trip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  margin: 24px;
  padding: 24px 6px 6px;
  border-radius: 20px;
  background-color: #ffffff;
  transition: 0.1s all ease-in-out;

  :global {
    .up_coming_trip_header {
      display: flex;
      justify-content: space-between;
      width: 100%;
      align-items: center;
      padding: 0 16px;
      .header_title {
        font-weight: 600;
        font-size: 32px;
        color: #142234;

        mix-blend-mode: normal;
        opacity: 0.96;
      }

      .header_action {
        display: flex;
        color: #e5e5e5;

        .action {
          font-weight: 600;
          font-size: 26px;
          color: #4E5969;
          display: flex;
          align-items: center;
        }
      }
    }

    .up_coming_trip_content {
      margin-top: 24px;
      width: 100%;
    }

    .threejiao {
      margin-left: 110px;
      float: left;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 4px;
      border-color: transparent #cccccc transparent transparent;
      transform: rotate(180deg);
    }

    .up_coming_trip_content_hose_tips {
      position: relative;
      background: #FFF7F0;
      height: 0.68rem;
      line-height: 0.68rem;
      text-align: center;
      border-radius: 0.41rem;
      margin-left: auto;
      padding: 0 0.36rem;

      &_text {
        color: #E06E00;
        font-size: 0.24rem;
      }

      &_badge {
        display: inline;
        position: absolute;
        top: -0.3rem;
        right: 0.73rem;
        width: 0;
        height: 0;
        border: 0.15rem solid transparent;
        border-bottom: 0.15rem solid #FFF7F0;
      }
    }
  }
}
