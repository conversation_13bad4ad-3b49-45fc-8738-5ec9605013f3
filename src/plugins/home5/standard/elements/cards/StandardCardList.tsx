import React, { useEffect, useState } from 'react'
import styles from './StandardCardList.module.less'
import { app as api } from '@ekuaibao/whispered'
import { standardTrack } from '../../../staticUtil'
const TEMP_EXPENSE = require('../../../images/standard/template-expense.png')
const TEMP_LOAN = require('../../../images/standard/template-loan.png')
const TEMP_TRAVEL_REQ = require('../../../images/standard/template-travel-requsition.png')
const TEMP_TRAVEL = require('../../../images/standard/template-travel.png')

type IProps = {
  layer?: any
  standardTrack?: (key: string, options: any) => void
  source?: string
}

const StandardCardList: React.FC<IProps> = props => {
  const [travelRequsition, setTravelRequsition] = useState()
  const [travelExpense, setTravelExpense] = useState()
  const [expense, setExpense] = useState()
  const [loan, setLoan] = useState()

  useEffect(() => {
    api.invokeService('@home:get:specification:with:version').then(async res => {
      res = res.specification_list
      if (res?.length) {
        const specificationGroupsList = res
        const presetSpecifications: any[] = []
        specificationGroupsList.forEach(item => {
          if (item?.id?.includes('_preset')) {
            presetSpecifications.push(item)
          }
        })
        const loan = presetSpecifications.find(el => el?.id?.includes('_personLoan_preset'))
        const expense = presetSpecifications.find(el => el?.id?.includes('_dailyExpense_preset'))
        const travelExpense = presetSpecifications.find(el => el?.id?.includes('_tripExpense_preset'))
        const travelRequsition = presetSpecifications.find(el => el?.id?.includes('_tripApply_preset'))
        travelRequsition && setTravelRequsition(travelRequsition)
        travelExpense && setTravelExpense(travelExpense)
        expense && setExpense(expense)
        loan && setLoan(loan)
      }
    })
  }, [])

  const handleClickCard = (item: any) => {
    api.invokeService('@home:save:specification', item).then(() => {
      api
        .invokeService('@home:save:specification:after:filtered', {
          type: 'filterFormType',
          formType: item.type,
          specifications: []
        })
        .then(() => {
          props.layer && props.layer.emitCancel()
          if (props.source === 'popup') {
            standardTrack('homeMyBillsModule', {
              souce: 'popup',
              actionType: `click-{${item.id}}-{${item.name}}`
            })
          } else {
            props?.standardTrack &&
              props.standardTrack('homeNewBillsModule', {
                souce: 'home',
                billType: item.type
              })
          }

          api.go(`/bill/${item.type}/from/external`)
        })
    })
  }

  return (
    <div className={styles['homepage-card-list']}>
      {travelRequsition && (
        <div
          className="card-list-item"
          onClick={() => handleClickCard(travelRequsition)}
          style={{ background: 'linear-gradient(180deg, #4988FD 0%, #91B7FF 100%)' }}
        >
          <div className="card-item-title text-nowrap-ellipsis">{i18n.get('差旅申请')}</div>
          <div className="card-item-content">{i18n.get('透明、灵活')}</div>
          <div className="ta-r">
            <img className="card-item-icon" src={TEMP_TRAVEL_REQ} />
          </div>
        </div>
      )}
      {travelExpense && (
        <div
          className="card-list-item"
          onClick={() => handleClickCard(travelExpense)}
          style={{ background: 'linear-gradient(180deg, #49CFBC 0%, #96E2D8 100%)' }}
        >
          <div className="card-item-title text-nowrap-ellipsis">{i18n.get('差旅报销')}</div>
          <div className="card-item-content">{i18n.get('准确、便捷')}</div>
          <div className="ta-r">
            <img className="card-item-icon" src={TEMP_TRAVEL} />
          </div>
        </div>
      )}
      {expense && (
        <div
          className="card-list-item"
          onClick={() => handleClickCard(expense)}
          style={{ background: 'linear-gradient(180deg, #46CDDA 0%, #9BE4EB 100%)' }}
        >
          <div className="card-item-title text-nowrap-ellipsis">{i18n.get('日常报销')}</div>
          <div className="card-item-content">{i18n.get('交通、招待')}</div>
          <div className="ta-r">
            <img className="card-item-icon" src={TEMP_EXPENSE} />
          </div>
        </div>
      )}
      {loan && (
        <div
          className="card-list-item"
          onClick={() => handleClickCard(loan)}
          style={{ background: 'linear-gradient(180deg, #9A74E1 0%, #CDB9F3 100%)' }}
        >
          <div className="card-item-title text-nowrap-ellipsis">{i18n.get('借款')}</div>
          <div className="card-item-content">{i18n.get('因公、因私')}</div>
          <div className="ta-r">
            <img className="card-item-icon" src={TEMP_LOAN} />
          </div>
        </div>
      )}
    </div>
  )
}

export default StandardCardList
