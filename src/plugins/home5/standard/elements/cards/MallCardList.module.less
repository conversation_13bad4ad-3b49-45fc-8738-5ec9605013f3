@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.mallCardWrapper {
  margin: 24px;
  .mallCardContent {
    padding: 24px;
    width: 100%;
    background: #ffffff;
    border-radius: 20px;
    .title {
      font-weight: 500;
      font-size: 32px;
      color: #142234;
      opacity: 0.96;
      &:active {
        background-color: @color-bg-2;
      }
    }
    .wrapper {
      display: flex;
      align-items: center;
      margin-top: 24px;
      flex-wrap: wrap;
      justify-content: space-between;
      .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        height: 204px;
        border-radius: 16px;
        flex: 1;
        margin-right: 16px;
        img {
          width: 0.6rem;
          height: 0.6rem;
          margin-bottom: 0.16rem;
        }
        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;
          text-align: center;
          font-size: 24px;
          color: #142234;
          mix-blend-mode: normal;
          opacity: 0.76;
        }
      }
      .item:last-child {
        margin-right: 0 !important;
      }
      .flight {
        background: #F7FCFF;
      }
      .hotel {
        background: #FCFAFF;
      }
      .train {
        background: #F8FDFC;
      }
      .taxi {
        background: #FFFBFB;
      }
    }
  }
}
