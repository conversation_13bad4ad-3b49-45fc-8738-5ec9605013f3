/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-12-23 16:53:05
 */
import React from 'react'
import { formatTrainData, formatFlightData, formatTaxiData, formatHotelData, formatFoodData, formatCommonData } from './formatHelper'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { getOrder, goThirdResource, handleOrderClick, track } from './formatHelper'
import TaxiCard from './TaxiCard'
import FoodCard from './FoodCard'
import TrainCard from './TrainCard'
import CommonCard from './CommonCard'
import HotelCard from './HotelCard'
import TripMultipleCard from './TripMultipleCard'
import FlightCard from './FlightCard'
import RecentCard from './RecentCard'
import moment from 'moment'
import { taxi } from '../../../../../importOCR/configTemplate'

const { TemplateKind } = api.require('@bill/elements/datalink/datalink-detail-modal/types')
interface Props {
    data: any;
    otherData: any[];
    entityPrefixForTrip: any;
    entityPrefixForOrder: any;
    from: string;
    isOrder: boolean
    isNew: boolean
    onGoTripListClick: Function
}



export default (props: Props) => {
    const { data, otherData, entityPrefixForTrip, from, isOrder, entityPrefixForOrder, isNew, onGoTripListClick } = props
    const order = getOrder({ data, entityPrefixForTrip, entityPrefixForOrder, isOrder, isNew })
    const form = data?.form
    const type = order ? get(order?.form, `订单类型`) : get(data?.form, `行程类型`)
    const onBuy = async (type: any) => {
        const taxiUrl = order ? get(order?.form, `${entityPrefixForOrder}taxiUrl`) : null
        track({
            key: 'mytrips_mall_click',
            actionName: '订购按钮点击量',
            from,
            type
        })
        const applyId = get(form, `${entityPrefixForTrip}原始单据`, '') || '' // 申请单 id
        const tripId = data?.id === 'virtual' ? '' : data?.id // 行程 id
        const white = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI', 'FOOD']
        if (!white.includes(type)) {
            return
        }
        const TK = type ? `BUTTON_${type}_ORDER` : 'BUTTON_TRAVEL_ORDER'
        const config = applyId ? await api.invokeService('@bill:get:getFlowDetailInfo', applyId) : {}
        const tripPlatform = get(config, 'value.form.specificationId.configs', []).find(
            (i: any) => i.ability == 'tripPlatform'
        )
        const checked = get(tripPlatform, 'checked')
        const platform = get(tripPlatform, 'platform') || []
        const params = { applyId, tripId }
        goThirdResource(TK, params, { checked, taxiUrl, platform: platform.join(','), isNew })
    }

    const handleOnClick = async () => {
        if (order) {
            const id = get(order, 'id')
            if (isNew) {
                return handleOrderClick(order, isNew, entityPrefixForOrder)
            }

            const { value } = await api.invokeService('@bill:get:datalink:template:byId', {
                entityId: id,
                type: TemplateKind.DETAIL
            })
            const dataLink = get(value, 'data.dataLink')
            const flag = handleOrderClick(dataLink)
            if (flag === false) {
                api.open('@bill:DataLinkEntityTripOrderDetailModal', {
                    field: get(value, 'data.dataLink.entity.fields', []),
                    value: value,
                    title: i18n.get('订单详情')
                })
                api.invokeService('@common:set:track', {
                    key: 'mytrips_order_view',
                    actionName: '订单详情页pv',
                    from
                })
            }
        } else {
            api.go(`/tripToDetail/${data?.id}`)
            api.invokeService('@common:set:track', {
                key: 'mytrips_detail_view',
                actionName: '行程详情页pv',
                from
            })
        }
    }

    if (!data && from === 'next') {
        return <TripMultipleCard otherData={otherData} onBuy={onBuy} onGoTripListClick={onGoTripListClick} />
    }
    // const arriveDate = get(form, `${entityPrefixForOrder}到达时间`)
    // const now = moment().add(30, 'minutes').valueOf()
    // if (now > arriveDate && (type === '飞机' || type === '火车') && from === 'next') {
    //     const dataSource: any = type === '飞机' ? formatFlightData({ data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew }) : formatTrainData({ data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew })
    //     const number = dataSource['flight_number'] || dataSource['train_number']
    //     return <RecentCard type={type} number={number} onBuy={onBuy} onGoTripListClick={onGoTripListClick} {...props} />
    // }

    if (type === '打车' || type === '用车') {
        const dataSource = formatTaxiData({ data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew })
        return (
            <div onClick={() => handleOnClick()}>
                <TaxiCard dataSource={dataSource} onBuy={onBuy} otherData={otherData} onGoTripListClick={onGoTripListClick} />
            </div>
        )
    }
    if (type === '飞机') {
        const dataSource = formatFlightData({ data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew })
        return (
            <div onClick={() => handleOnClick()}>
                <FlightCard dataSource={dataSource} onBuy={onBuy} otherData={otherData} onGoTripListClick={onGoTripListClick} isNew={isNew} />
            </div>
        )
    }

    if (type === '火车') {
        const dataSource = formatTrainData({ data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew })
        return (
            <div onClick={() => handleOnClick()}>
                <TrainCard dataSource={dataSource} onBuy={onBuy} otherData={otherData} onGoTripListClick={onGoTripListClick} />
            </div>
        )
    }

    if (type === '酒店') {
        const dataSource = formatHotelData({ data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew })
        return (
            <div onClick={() => handleOnClick()}>
                <HotelCard dataSource={dataSource} onBuy={onBuy} otherData={otherData} onGoTripListClick={onGoTripListClick} />
            </div>
        )
    }

    // if (type === '餐补') {
    //     const dataSource = formatFoodData({ data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew })
    //     return (
    //         <div onClick={() => handleOnClick()}>
    //             <FoodCard dataSource={dataSource} onBuy={onBuy} otherData={otherData} onGoTripListClick={onGoTripListClick} />
    //         </div>
    //     )
    // }
    const dataSource = formatCommonData({ data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew })
    return (
        <div onClick={() => handleOnClick()}>
            <CommonCard dataSource={dataSource} onBuy={onBuy} otherData={otherData} onGoTripListClick={onGoTripListClick} />
        </div>
    )
}

