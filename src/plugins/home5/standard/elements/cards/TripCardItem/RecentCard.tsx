/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-11-07 13:08:10
 */
import React from 'react'
import style from './TripItemCard.module.less'
import { TripFooter } from './TripMultipleCard'
const TRAIN = require('../../../../images/train_new.svg')
const FLIGHT = require('../../../../images/flight_new.svg')
const HOTEL = require('../../../../images/hotel_new.svg')
const TAXI = require('../../../../images/car_new.svg')
const FOOD = require('../../../../images/food_new.svg')
const ARROW = require('../../../../images/arrow_trip.png')

const TRAVEL_TYPE: any = {
    飞机: FLIGHT,
    酒店: HOTEL,
    火车: TRAIN,
    用车: TAXI,
    餐补: FOOD
}
export default (props: any) => {
    const { type, onBuy, number } = props
    return <div>
        <div className={style['trip_items_card_recent']}>
            <div className="l1">
                <img src={TRAVEL_TYPE[type]} alt="" className="mr-16" />
                <span>{type === '飞机' ? i18n.get(`FlightArrived{__k0}`, { __k0: number }) : i18n.get(`TrainArrived{__k0}`, { __k0: number })}</span>
            </div>
            <a className="action" onClick={() => onBuy('TAXI')}>
                <span>{i18n.get(`立即用车`)}</span>
                <img src={ARROW} alt="" className="mr-16" />
            </a>
        </div>
        <TripFooter {...props} />
    </div>
}