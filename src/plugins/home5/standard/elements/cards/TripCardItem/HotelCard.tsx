/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-11-22 18:35:46
 */
import React from 'react'
import { <PERSON>Footer } from './TripMultipleCard'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Button } from 'antd-mobile'
import { T } from '@ekuaibao/i18n'
import { Fetch } from '@ekuaibao/fetch'
const { getWeek } = api.require('@bill/elements/datalink/dataLinkUtil')
const HOTEL = require('../../../../images/standard/hotel.svg')
const VECTOR = require('../../../../images/standard/vector.png')

interface Props {
    onBuy: Function;
    isOrder: boolean;
    dataSource: any;
    bg: string
}
export default (props: Props) => {
    const { onBuy, dataSource } = props
    const { hotel_name, city, room_type, trip_people, room_number, startTime, endTime, trip_number, have_meal, hotel_adress, order } = dataSource
    let days
    if (order) {
        const diff = Math.abs(endTime - startTime);
        days = Math.ceil(diff / (1000 * 3600 * 24));
    }
    const format = Fetch.lang === 'en-US' ? "MM.DD" : 'MM月DD日'
    return (
        <div className={style['trip_items_card']}>
            <div className={`top_view ${!order ? 'top_view_car_not_order' : ''}`}>
                <div className="l_view">
                    <img src={HOTEL} alt="" />
                    <div className="info_view">
                      {
                        order ?
                          <div className="date">
                            {`酒店 ${moment(startTime).format(format)} - ${moment(endTime).format(format)} ${i18n.get(`共{__k0}晚`, { __k0: days })}`}
                          </div> :
                          <>
                            <div className="date_not_order">
                              {startTime === endTime
                                ? `酒店 ${moment(startTime).format(format)} ${getWeek(startTime)}`
                                : `酒店 ${moment(startTime).format(format)} - ${moment(endTime).format(format)}`}
                            </div>
                            <div className="info_not_order">232333{city}</div>
                          </>
                      }
                        {/*<div className="date">*/}
                            {/*{order*/}
                                {/*? `${moment(startTime).format(format)} ${getWeek(startTime)}`*/}
                                {/*: startTime === endTime*/}
                                    {/*? `${moment(startTime).format(format)} ${getWeek(startTime)}`*/}
                                    {/*: `${moment(startTime).format(format)} - ${moment(endTime).format(format)}`}*/}
                        {/*</div>*/}
                    </div>
                </div>
              {
                !order && <div className="r_view">
                  <Button
                    className="btn"
                    onClick={(e: any) => {
                      e?.stopPropagation()
                      onBuy && onBuy(order ? 'TAXI' : 'HOTEL')
                    }}
                  >
                    <T name="预定酒店" />
                  </Button>
                </div>
              }
            </div>
            {order && (
                <>
                  <div className="info_hotel">{hotel_name}</div>
                  <div className="bottom_view_hotel">
                    <div className="desc_hotel">
                      {room_type && <span className="text">{room_type}</span>}
                      {room_number && <span className="text">{`${room_number}间`}</span>}
                      {have_meal && <span className="text">{have_meal}</span>}
                    </div>
                    <div className="text">
                      <span className="label">{i18n.get('地址：')}</span>
                      <span className="descp">{hotel_adress}</span>
                    </div>
                  </div>
                  <div
                    className="btn"
                    onClick={(e: any) => {
                      e?.stopPropagation()
                      onBuy && onBuy(order ? 'TAXI' : 'HOTEL')
                    }}
                  >
                    <img className="car_img" src={VECTOR} alt="" />
                    <T name="打车去酒店" />
                  </div>
                </>
            )}
            {/*<TripFooter {...props} />*/}
        </div>
    )

}