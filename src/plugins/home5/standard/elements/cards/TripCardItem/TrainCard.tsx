/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-12-10 17:57:53
 */
import React from 'react'
import { <PERSON>Footer } from './TripMultipleCard'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Button } from 'antd-mobile'
import { T } from '@ekuaibao/i18n'
import { getDiffDay } from './formatHelper'
import { Fetch } from '@ekuaibao/fetch'
const { getWeek, getDiff } = api.require('@bill/elements/datalink/dataLinkUtil')
const TRAIN = require('../../../../images/standard/train.svg')
const TOLINE = require('../../../../images/standard/rectangle.svg')
const VECTOR = require('../../../../images/standard/vector.png')

interface Props {
  onBuy: Function;
  isOrder: boolean;
  dataSource: any;

}
export default (props: Props) => {
  const { onBuy, dataSource } = props
  const { date, train_type, train_number, train_site, from_city, to_city, startTime, endTime, train_ticket_number, train_site_type, train_entrance, order } = dataSource
  const addOneDay = getDiffDay(startTime, endTime)
  let t = train_site
  if (train_site) {
    t = train_site.split(',')
  }
  const format = i18n.currentLocale === 'en-US' ? "MM.DD" : 'MM月DD日'
  return (
    <div className={style['trip_items_card']}>
      <div className={`top_view ${!order ? 'top_view_car_not_order' : ''}`}>
        <div className="l_view">
          <img src={TRAIN} alt="" />
          <div className="info_view">
            {
              order ?
                <div className="date">
                  {`${i18n.get('火车')} ${moment(date).format(format)} ${train_number}`}
                </div> :
                <>
                  <div className="date_not_order">
                    {`${i18n.get('火车')} ${moment(date).format(format)} ${getWeek(date)}`}
                  </div>
                  <div className="info_not_order">
                    {`${from_city} - ${to_city}`}
                  </div>
                </>
            }

          </div>
        </div>
        {
          !order &&
          <div className="r_view">
            <Button
              className="btn"
              onClick={(e: any) => {
                e?.stopPropagation()
                onBuy && onBuy(order ? 'TAXI' : 'TRAIN')
              }}
            >
              <T name={order ? '去火车站' : '买火车票'} />
            </Button>
          </div>
        }
      </div>
      {
        order && (
          <div className="bottom_view">
            <div className="city_info from">
              {addOneDay && <div className="add"></div>}
              <div className="time">{moment(startTime).format('HH:mm')}</div>
              <div className="city">{from_city}</div>
            </div>
            <div className="use_time">
              <div>{getDiff(startTime, endTime)}</div>
              <img src={TOLINE} alt="" />
            </div>
            <div className="city_info to">
              {addOneDay && <div className="add"></div>}
              <div className="time">
                {moment(endTime).format('HH:mm')}
              </div>
              <div className="city">{to_city}</div>
            </div>
          </div>
        )
      }
      {order &&
        <>
          <div className="desc desc-train">
            {train_ticket_number && <span className="text">{i18n.get('取票号')} <span className="text-weight">{train_ticket_number}</span></span>}
            {/*{train_entrance && <span className="text">{i18n.get('检票口')}{train_entrance}</span>}*/}
            {train_site && <span className="text">座位 <span className="text-weight">{t.map(v => v)}</span></span>}
          </div>
          <div
            className="btn"
            onClick={(e: any) => {
              e?.stopPropagation()
              onBuy && onBuy(order ? 'TAXI' : 'TRAIN')
            }}
          >
            <img className="car_img" src={VECTOR} alt="" />
            <T name="打车去火车站" />
          </div>
        </>
      }
      {/*<TripFooter {...props} />*/}
    </div >)

}