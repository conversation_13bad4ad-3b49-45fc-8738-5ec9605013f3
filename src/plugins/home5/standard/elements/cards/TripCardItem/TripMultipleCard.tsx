/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-11-15 18:48:13
 */
import React from 'react'
import style from './TripItemCard.module.less'
import { T } from '@ekuaibao/i18n'
import { Button } from 'antd-mobile'
import { get } from 'lodash'

const TRAIN = require('../../../../images/standard/train.svg')
const FLIGHT = require('../../../../images/standard/flight.svg')
const HOTEL = require('../../../../images/standard/hotel.svg')
const TAXI = require('../../../../images/standard/car.svg')


const TRAVEL_TYPE: any = {
    飞机: FLIGHT,
    酒店: HOTEL,
    火车: TRAIN,
    用车: TAXI
}
export const TripFooter = (props: any) => {
    const { otherData, onGoTripListClick } = props
    if (!otherData || !otherData.length) return null
    const typeMap: any = {}
    otherData.forEach((v: any) => typeMap[get(v, `form.行程类型`, '')] = get(v, `form.行程类型`, ''))
    return <div className={style['trip_items_card_other']} onClick={(e: any) => {
        e?.stopPropagation()
        onGoTripListClick && onGoTripListClick()
    }}>
        <div className="top_view_other">
            <div className="l_view_other">
                <div className="l1">
                    {Object.keys(typeMap).map((v: string, index: number) => <img className={`img${index + 1}`} key={TRAVEL_TYPE[v]} src={TRAVEL_TYPE[v]} alt="" />)}
                </div>
                <div className="other_view_other">
                    {i18n.get(`您有{__k0}条即将开始的行程待订购`, { __k0: otherData.length })}
                </div>
            </div>
            <div className="r_view_other">
                <Button
                    className="btn"
                    onClick={(e: any) => {
                        e?.stopPropagation()
                        onGoTripListClick && onGoTripListClick()
                    }}
                >
                    <T name={'立即订购'} />
                </Button>
            </div>
        </div>
    </div>
}

const Card = (props: any) => {
    const { otherData, onGoTripListClick } = props
    if (!otherData || !otherData.length) return null
    const typeMap: any = {}
    otherData.forEach((v: any) => typeMap[get(v, `form.行程类型`, '')] = get(v, `form.行程类型`, ''))
    return <div className={style['trip_items_card_mix']} onClick={(e: any) => {
        e?.stopPropagation()
        onGoTripListClick && onGoTripListClick()
    }}>
        <div className="top_view">
            <div className="l_view">
                <div className="l1">
                    {Object.keys(typeMap).map((v: string, index: number) => <img className={`img${index + 1}`} key={TRAVEL_TYPE[v]} src={TRAVEL_TYPE[v]} alt={TRAVEL_TYPE[v]} />)}
                </div>
                <div className="other_view">
                    {i18n.get(`您有{__k0}条即将开始的行程待订购`, { __k0: otherData.length })}
                </div>
            </div>
            <div className="r_view">
                <Button
                    className="btn"
                    onClick={(e: any) => {
                        e?.stopPropagation()
                        onGoTripListClick && onGoTripListClick()
                    }}
                >
                    <T name={'立即订购'} />
                </Button>
            </div>
        </div>
    </div>
}

export default Card