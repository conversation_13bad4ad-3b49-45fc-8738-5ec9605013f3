/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-11-04 14:02:21
 */
import React from 'react'
import { TripFooter } from './TripMultipleCard'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'

const { getWeek } = api.require('@bill/elements/datalink/dataLinkUtil')
const COMMON = require('../../../../images/common_new.svg')

interface Props {
    onBuy: Function;
    isOrder: boolean;
    dataSource: any;

}
export default (props: Props) => {
    const { dataSource } = props
    const { date, from_city, to_city, startTime, endTime, order } = dataSource
    return (
        <div className={style['trip_items_card']}>
            <div className="top_view">
                <div className="l_view">
                    <img src={COMMON} alt="" />
                    <div className="info_view_no_btn">
                        <div className="date">
                            {startTime === endTime || !endTime
                                ? `${moment(date).format('MM月DD日')} ${getWeek(date)}`
                                : `${moment(startTime).format('MM月DD日')} - ${moment(endTime).format('MM月DD日')}`}
                        </div>
                        <div className="info_o">{(from_city && to_city) ? `${from_city} - ${to_city}` : from_city}</div>
                    </div>
                </div>
            </div>
            <TripFooter {...props} />
        </div>
    )

}