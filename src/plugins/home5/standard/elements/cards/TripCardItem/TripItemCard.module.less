.trip_items_card {
  display: flex;
  flex-direction: column;
  border-radius: 20px 20px 0px 0px;
  background-color: #F3F9FF;
  :global {
    .desc-taxi {
      margin: 24px 32px 32px 76px !important;
    }
    .desc-train {
      justify-content: space-between;
    }
    .desc {
      color: #4E5969;
      display: flex;
      font-size: 24px;
      align-items: center;
      margin: 40px 0 40px 76px;
      .text-nowrap {
        text-align: center;
        white-space: nowrap;
      }
      .text-m-r {
        margin-right: 48px;
      }
      .text-taxi {
        text-align: center;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .line {
        margin-left: 16px;
        margin-right: 16px;
        color: #d9dbde;
      }
      .text {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 48px;
        .flight_type {
          margin-left: 4px;
        }
        .text-weight {
          font-weight: 600;
          font-size: 28px;
          color: #4E5969;
        }
      }
    }
    .date_not_order {
      color: #142234;
      font-weight: 600;
      font-size: 28px;
      width: 84%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .info_not_order {
      font-weight: 400;
      font-size: 28px;
      color: #142234;
      opacity: 0.76;
      width: 84%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .info_view_no_btn {
      width: 70%;
    }
    .top_view_car_not_order {
      margin-bottom: 34px !important;
      .l_view_car,.l_view {
        width: 70%;
      }
    }
    .top_view_car {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 28px 28px 0 24px;
      .l_view_car {
        display: flex;
        .l1 {
          display: flex;
        }
        img {
          height: 0.4rem;
          width: 0.4rem;
        }
        .mr-16 {
          margin-right: 16px;
        }
        .info_view_car {
          margin-left: 18px;
          width: 100%;
          .date_car {
            font-weight: 600;
            font-size: 24px;
            color: #202B3B;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .info_o_car {
            font-weight: 600;
            font-size: 28px;
            color: #1D2129;
            width: 80%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .info_view_no_btn {
          margin-left: 0.24rem;
          .date {
            opacity: 0.76;
            font-size: 0.28rem;
            font-weight: 600;
            color: #142234;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 5.5rem;
          }
          .info_o {
            opacity: 0.76;
            font-size: 0.28rem;
            color: #142234;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 5.5rem;
          }
        }
      }
      .r_view {
        .btn {
          font-size: 28px;
          color: #fff;
          height: 64px;
          width: 168px;
          background: linear-gradient(180deg, rgba(47, 196, 220, 0.88) 0%, rgba(74, 208, 230, 0.88) 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          position: initial;
          font-weight: 400;
        }
      }
    }
    .top_view {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 28px 28px 0 24px;
      .l_view {
        display: flex;
        img {
          height: 0.4rem;
          width: 0.4rem;
        }
        .mr-16 {
          margin-right: 16px;
        }
        .info_view {
          margin-left: 18px;
          width: 100%;
          .date {
            font-weight: 600;
            font-size: 24px;
            color: #202B3B;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .r_view {
        .btn {
          font-size: 28px;
          color: #fff;
          height: 64px;
          width: 168px;
          background: linear-gradient(180deg, rgba(47, 196, 220, 0.88) 0%, rgba(74, 208, 230, 0.88) 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          position: initial;
          font-weight: 400;
        }
      }
    }
    .info_o {
      font-weight: 600;
      font-size: 40px;
      color: #1D2129;
      margin: 34px 38px 40px 76px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .info_hotel {
      font-weight: 600;
      font-size: 40px;
      color: #1D2129;
      margin: 20px 38px 20px 76px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .bottom_view {
      display: flex;
      position: relative;
      padding: 0px 36px 0px 76px;
      .city_info {
        flex: 1;
        .add {
          top: -20px;
          font-size: 24px;
          color: #f5222d;
          right: -24px;
          height: 32px;
        }
        .time {
          font-weight: 600;
          font-size: 40px;
          color: #1D2129;
        }
        .city {
          width: 100%;
          font-size: 24px;
          line-height: 100%;
          color: #8F959D;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
      }
      .from {
        width: 40%;
      }
      .to {
        text-align: right;
      }
      .use_time {
        color: #8F959D;
        font-size: 24px;
        white-space: nowrap;
        width: 124px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        img {
          width: 112px;
        }
      }
    }
    .car_img {
      width: 30px;
      margin-right: 12px;
    }
    .bottom_view_hotel {
      display: flex;
      position: relative;
      flex-direction: column;
      align-items: flex-start;
      margin: 0px 10px 46px 74px;
      font-style: normal;
      font-weight: normal;
      font-size: 24px;
      color: #4E5969;
      .desc_hotel {
        display: flex;
        .text {
          margin-right: 48px;
        }
      }
      .label {
        width: 84px;
      }
      .descp {
        flex: 1;
      }
      .text {
        display: flex;
        align-items: flex-start;
      }
      .city {
        width: 100%;
      }
    }
    .bottom_view_car {
      background: #fff;
      border-radius: 12px;
      //margin-top: 0.2rem;
      margin: 20px 32px 0px 76px;
      display: flex;
      align-items: center;
      position: relative;
      padding: 32px;

      .icon_part {
        margin-right: 18px;
        .start_point {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #94da06;
        }
        .line {
          margin-top: 2px;
          margin-bottom: 2px;
          width: 2px;
          height: 36px;
          background: rgba(56, 46, 49, 0.05);
          margin-left: 2px;
        }
        .end_point {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #ff9d07;
        }
      }
      .city {
        font-weight: 500;
        font-size: 24px;
        color: rgba(20, 34, 52, 0.92);
        width: 72%;
        .text {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .phone_part {
        position: absolute;
        right: 32px;
        .back {
          background: #48CD7D;
          width: 64px;
          height: 64px;
          border-radius: 64px;
          position: relative;
          margin-left: 4px;
          img {
            height: 26px;
            position: absolute;
            top: 20px;
            left: 20px;
          }
        }
        .text {
          font-size: 24px;
          color: #142234;
          opacity: 0.44;
          margin-top: 8px;
          text-align: center;
        }
      }
    }
    .other_view {
      margin-top: 0.2rem;
      margin-left: 0.65rem;
      color: rgba(20, 34, 52, 0.44);
      span {
        color: #e5e5e5;
      }
    }
    .btn {
      font-weight: 600;
      font-size: 30px;
      color: #fff;
      height: 88px;
      background:  linear-gradient(180deg, rgba(47, 196, 220, 0.88) 0%, rgba(74, 208, 230, 0.88) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      position: initial;
      border-radius: 0px 0px 20px 20px;
    }
  }
}

.trip_items_card_mix {
  display: flex;
  flex-direction: column;
  :global {
    .top_view {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #F3F9FF;
      border-radius: 20px;
      padding: 32px;
      .l_view {
        display: flex;
        flex-direction: column;
        .l1 {
          display: flex;
          height: 40px;
          position: relative;
          .img1 {
            position: absolute;
            left: 0px;
            z-index: 1;
          }
          .img2 {
            position: absolute;
            left: 24px;
            z-index: 2;
          }
          .img3 {
            position: absolute;
            left: 48px;
            z-index: 3;
          }
          .img4 {
            position: absolute;
            left: 72px;
            z-index: 4;
          }
        }
        img {
          height: 0.4rem;
          width: 0.4rem;
        }
        .other_view {
          font-weight: 400;
          margin-top: 16px;
          color: #142234;
          font-size: 28px;
          opacity: 0.76;
        }
        .mr-16 {
          margin-right: 16px;
        }
      }
      .r_view {
        .btn {
          font-size: 28px;
          color: #fff;
          height: 64px;
          width: 168px;
          background: linear-gradient(180deg, rgba(47, 196, 220, 0.88) 0%, rgba(74, 208, 230, 0.88) 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          position: initial;
        }
      }
    }
  }
}

.trip_items_card_other {
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  border-top: 2px solid #e5e5e5;
  padding-top: 32px;
  :global {
    .top_view_other {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #F3F9FF;
      border-radius: 20px;
      padding: 32px;
      .l_view_other {
        display: flex;
        flex-direction: column;
        .l1 {
          display: flex;
          height: 40px;
          position: relative;
          .img1 {
            position: absolute;
            left: 0px;
            z-index: 1;
          }
          .img2 {
            position: absolute;
            left: 24px;
            z-index: 2;
          }
          .img3 {
            position: absolute;
            left: 48px;
            z-index: 3;
          }
          .img4 {
            position: absolute;
            left: 72px;
            z-index: 4;
          }
        }
        img {
          height: 0.4rem;
          width: 0.4rem;
        }
        .other_view_other {
          margin-top: 16px;
          color: #142234;
          opacity: 76%;
        }
        .mr-16 {
          margin-right: 16px;
        }
      }

      .r_view_other {
        .btn {
          font-size: 28px;
          color: #fff;
          height: 64px;
          width: 168px;
          background: linear-gradient(180deg, rgba(47, 196, 220, 0.88) 0%, rgba(74, 208, 230, 0.88) 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          position: initial;
        }
      }
    }
  }
}

.trip_items_card_recent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  :global {
    .l1 {
      display: flex;
      color: #142234;
      img {
        height: 0.4rem;
        width: 0.4rem;
        margin-right: 16px;
      }
    }

    .action {
      color: var(--brand-base);
      position: relative;
      width: 136px;
      img {
        position: absolute;
        top: 6px;
        right: 0;
      }
    }
  }
}
