/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-12-07 10:56:27
 */
import React from 'react'
import { <PERSON>Footer } from './TripMultipleCard'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Button } from 'antd-mobile'
import { T } from '@ekuaibao/i18n'
const TOLINE = require('../../../../images/standard/rectangle.svg')
const { getWeek, getDiff } = api.require('@bill/elements/datalink/dataLinkUtil')
const FLIGHT = require('../../../../images/standard/flight.svg')
const VECTOR = require('../../../../images/standard/vector.png')
import { getDiffDay } from './formatHelper'
import { Fetch } from '@ekuaibao/fetch'
interface Props {
    onBuy: Function;
    isOrder: boolean;
    dataSource: any;
    isNew: boolean;
    otherData: any[];
    bg: string
}
export default (props: Props) => {
    const { onBuy, dataSource, isNew } = props
    const { date, airline_company, flight_number, from_city, to_city, startTime, endTime, flight_type, have_price, have_meal, have_stop, order } = dataSource
    const addOneDay = getDiffDay(startTime, endTime)
    const format = Fetch.lang === 'en-US' ? "MM.DD" : 'MM月DD日'
    return (
        <div className={style['trip_items_card']}>
            <div className={`top_view ${!order ? 'top_view_car_not_order' : ''}`}>
                <div className="l_view">
                    <img src={FLIGHT} alt="" />
                    <div className="info_view">
                      {
                        order ?
                          <div className="date">
                            {`飞机 ${moment(date).format(format)}`}
                            &nbsp;
                            {`${airline_company} ${flight_number}`}
                          </div> :
                          <>
                            <div className="date_not_order">
                              {`飞机 ${moment(date).format(format)} ${getWeek(date)}`}
                            </div>
                            <div className="info_not_order">
                              {`${from_city}-${to_city}`}
                            </div>
                          </>
                      }
                        {/*<div className="info_o">{order ? `${airline_company} ${flight_number}` : `${from_city}-${to_city}`}</div>*/}
                    </div>
                </div>
              {
                !order && <div className="r_view">
                  <Button
                    className="btn"
                    onClick={(e: any) => {
                      e?.stopPropagation()
                      onBuy && onBuy(order ? 'TAXI' : 'FLIGHT')
                    }}
                  >
                    <T name="买飞机票" />
                  </Button>
                </div>
              }
            </div>
            {order ? (
                <div className="bottom_view">
                    <div className="city_info from">
                        {addOneDay && <div className="add"></div>}
                        <div className="time">{moment(startTime).format('HH:mm')}</div>
                        <div className="city">{from_city}</div>
                    </div>
                    <div className="use_time">
                        <div>{getDiff(startTime, endTime)}</div>
                        <img src={TOLINE} alt="" />
                        {/*{have_stop && <div>{have_stop !== "0" && i18n.get('经停')}</div>}*/}
                    </div>
                    <div className="city_info to">
                        {/*{addOneDay && <div className="add">{`+${addOneDay}${i18n.get('天')}`}</div>}*/}
                        {addOneDay && <div className="add"></div>}
                        <div className="time">
                            {moment(endTime).format('HH:mm')}
                        </div>
                        <div className="city right">
                            {to_city}
                        </div>
                    </div>
                </div>
            ) : null}
          {order &&
            <>
              <div className="desc">
                {have_price && isNew && <span className="text">{have_price}</span>}
                {have_meal && <span className="text">{have_meal}</span>}
                {flight_type && <span className="text">机型 <span className="flight_type">{flight_type}</span></span>}
              </div>
              <div
                className="btn"
                onClick={(e: any) => {
                  e?.stopPropagation()
                  onBuy && onBuy(order ? 'TAXI' : 'FLIGHT')
                }}
              >
                <img className="car_img" src={VECTOR} alt="" />
                <T name="打车去机场" />
              </div>
            </>
          }
          {/*<TripFooter {...props} />*/}
        </div>
    )

}