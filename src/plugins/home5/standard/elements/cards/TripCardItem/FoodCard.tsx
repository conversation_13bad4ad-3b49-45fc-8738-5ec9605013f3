/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-10-28 17:38:55
 */
import React from 'react'
import { TripFooter } from './TripMultipleCard'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Fetch } from '@ekuaibao/fetch'

const { getWeek, getDiff } = api.require('@bill/elements/datalink/dataLinkUtil')
const FOOD = require('../../../../images/food_new.svg')

interface Props {
    onBuy: Function;
    isOrder: boolean;
    dataSource: any;

}
export default (props: Props) => {
    const { dataSource } = props
    const { date, from_city, to_city, startTime, endTime, order } = dataSource
    const format = Fetch.lang === 'en-US' ? "MM.DD" : 'MM月DD日'
    return (
        <div className={style['trip_items_card']}>
            <div className="top_view">
                <div className="l_view">
                    <img src={FOOD} alt="" />
                    <div className="info_view_no_btn">
                        <div className="date">
                            {startTime === endTime
                                ? `${moment(date).format(format)} ${getWeek(date)}`
                                : `${moment(startTime).format(format)} - ${moment(endTime).format(format)}`}
                        </div>
                        <div className="info_o">{order ? `${from_city} - ${to_city}` : from_city}</div>
                    </div>
                </div>
            </div>
            <TripFooter {...props} />
        </div>
    )

}