import { get, isString } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import qs from 'qs'
import moment from 'moment'
import { Fetch } from '@ekuaibao/fetch'
export const getOrder = ({ data, entityPrefixForTrip, entityPrefixForOrder, isOrder, isNew }) => {
    let order
    if (isNew) {
        order = isOrder ? data : false
    } else {
        const form = data?.form
        let tempArray = get(form, `${entityPrefixForTrip}订单`, [])
        if (!tempArray.length) {
            tempArray = get(form, `${entityPrefixForOrder}订单`, [])
        }
        const orderArray = tempArray.filter((e: any) => !isString(e))
        order = orderArray.length > 0 ? orderArray[0] : false
    }
    return order
}

function commonTripData(originData: any) {
    const { data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew } = originData
    const tripForm = data?.form
    const order = getOrder({ data, entityPrefixForTrip, isOrder, isNew, entityPrefixForOrder })
    const orderForm = order ? order.form : get(data, 'form')
    let date = get(tripForm, `${entityPrefixForTrip}行程日期`, '') || get(tripForm, `${entityPrefixForTrip}入住日期`, '')
    let startTime = get(tripForm, `${entityPrefixForTrip}行程日期`, '') || get(tripForm, `${entityPrefixForTrip}入住日期`, '')
    let endTime = get(tripForm, `${entityPrefixForTrip}离店日期`, '')
    let from_city = get(tripForm, `${entityPrefixForTrip}出发地`)
        ? JSON.parse(get(tripForm, `${entityPrefixForTrip}出发地`))[0]['label']
        : ''
    let to_city = get(tripForm, `${entityPrefixForTrip}目的地`)
        ? JSON.parse(get(tripForm, `${entityPrefixForTrip}目的地`))[0]['label']
        : ''

    return { date, startTime, endTime, from_city, to_city, orderForm, order }
}

export const formatTrainData = (originData: any) => {
    const { entityPrefixForOrder } = originData
    let { date, startTime, endTime, from_city, to_city, order, orderForm } = commonTripData(originData)
    let train_site = ''
    let train_number = ''
    let train_type = ''
    let train_ticket_number = ''
    let train_site_type = ''
    let train_entrance = ''
    let taxiUrl = ''
    if (order) {
        date = get(orderForm, `${entityPrefixForOrder}出发时间`, '')
        startTime = get(orderForm, `${entityPrefixForOrder}出发时间`, '')
        endTime = get(orderForm, `${entityPrefixForOrder}到达时间`, '')
        from_city = get(orderForm, `${entityPrefixForOrder}出发车站`, '')
        to_city = get(orderForm, `${entityPrefixForOrder}到达车站`, '')
        // 火车席位 火车车次 车型
        train_site = get(orderForm, `${entityPrefixForOrder}火车席位`, '') || get(orderForm, `${entityPrefixForOrder}坐席`, '')
        train_number = get(orderForm, `${entityPrefixForOrder}火车车次`, '') || get(orderForm, `${entityPrefixForOrder}车次`, '')
        train_type = get(orderForm, `${entityPrefixForOrder}车型`, '火车')
        train_ticket_number = get(orderForm, `${entityPrefixForOrder}取票号`)
        train_site_type = get(orderForm, `${entityPrefixForOrder}座位类型`)
        train_entrance = get(orderForm, `${entityPrefixForOrder}检票口`)
        taxiUrl = get(orderForm, `taxiUrl`)
    }
    return { date, train_type, train_number, train_site, from_city, to_city, startTime, endTime, train_ticket_number, train_site_type, train_entrance, taxiUrl, order }
}


export const formatFlightData = (originData: any) => {
    const { entityPrefixForOrder } = originData
    let { date, startTime, endTime, from_city, to_city, order, orderForm } = commonTripData(originData)
    let flight_number = ''
    let airline_company = ''
    let airport_name = ''
    let flight_type = ''
    let have_meal = ''
    let have_price = ''
    let have_stop = ''
    if (order) {
        date = get(orderForm, `${entityPrefixForOrder}出发时间`, '')
        startTime = get(orderForm, `${entityPrefixForOrder}出发时间`, '')
        endTime = get(orderForm, `${entityPrefixForOrder}到达时间`, '')
        from_city = get(orderForm, `${entityPrefixForOrder}出发机场`, '')
        to_city = get(orderForm, `${entityPrefixForOrder}到达机场`, '')
        flight_number = get(orderForm, `${entityPrefixForOrder}航班号`, '')
        airline_company = get(orderForm, `${entityPrefixForOrder}航空公司`, '')
        flight_type = get(orderForm, `${entityPrefixForOrder}飞机型号`, '')
        have_meal = get(orderForm, `${entityPrefixForOrder}有无餐食`, '')
        have_price = get(orderForm, `${entityPrefixForOrder}折扣`, '')
        have_stop = get(orderForm, `${entityPrefixForOrder}经停`, '')
    }
    return { date, airline_company, flight_number, from_city, to_city, startTime, endTime, airport_name, flight_type, have_meal, have_price, have_stop, order }
}


export const formatTaxiData = (originData: any) => {
    const { entityPrefixForOrder } = originData
    let { date, startTime, endTime, from_city, to_city, order, orderForm } = commonTripData(originData)
    let car_number = ''
    let car_type = ''
    let driver_nubmer = ''
    let car_use_type = ''
    let driver_name = ''
    let taxiUrl = ''
    let car_cat = ''
    let car_color = ''
    let car_catagories = ''
    if (order) {
        from_city = get(orderForm, `${entityPrefixForOrder}实际出发地点`, '') || get(orderForm, `${entityPrefixForOrder}出发地点`, '')
        to_city = get(orderForm, `${entityPrefixForOrder}实际到达地点`, '') || get(orderForm, `${entityPrefixForOrder}目的地点`, '')
        date = get(orderForm, `${entityPrefixForOrder}出发时间`, '')
        // 车牌号 车型
        car_number = get(orderForm, `${entityPrefixForOrder}车牌号`, '')
        car_type = get(orderForm, `${entityPrefixForOrder}车型`, '')
        driver_nubmer = get(orderForm, `${entityPrefixForOrder}司机电话`, '')
        car_use_type = get(orderForm, `${entityPrefixForOrder}预约类型`, '用车')
        driver_name = get(orderForm, `${entityPrefixForOrder}司机称谓`, '')
        taxiUrl = get(orderForm, `${entityPrefixForOrder}taxiUrl`, '')
        car_cat = get(orderForm, `${entityPrefixForOrder}车辆型号`, '')
        car_color = get(orderForm, `${entityPrefixForOrder}车辆颜色`, '')
        car_catagories = get(orderForm, `${entityPrefixForOrder}车型`, '')

    }
    return { date, car_number, car_type, from_city, to_city, startTime, endTime, driver_nubmer, car_use_type, driver_name, car_catagories, car_cat, car_color, order }
}

export const formatHotelData = (originData: any) => {
    const { data, entityPrefixForOrder, entityPrefixForTrip, isOrder, isNew } = originData
    const tripForm = data?.form
    const order = getOrder({ data, entityPrefixForTrip, isOrder, isNew, entityPrefixForOrder })
    const orderForm = order ? order.form : get(data, 'form')
    let startTime = get(tripForm, `${entityPrefixForTrip}入住日期`)
    let endTime = get(tripForm, `${entityPrefixForTrip}离店日期`)
    let city = get(tripForm, `${entityPrefixForTrip}住宿地`)
        ? JSON.parse(get(tripForm, `${entityPrefixForTrip}住宿地`))[0]['label']
        : ''
    let room_type = ''
    let hotel_name = ''
    let night_number = ''
    let room_number = ''
    let trip_people = ''
    let trip_number = ''
    let have_meal = ''
    let hotel_adress = ''
    if (order) {
        startTime = get(orderForm, `${entityPrefixForOrder}入住日期`) || get(orderForm, `${entityPrefixForOrder}入住时间`)
        endTime = get(orderForm, `${entityPrefixForOrder}离店日期`) || get(orderForm, `${entityPrefixForOrder}离店时间`)
        city = get(orderForm, `${entityPrefixForOrder}住宿地`)
            ? JSON.parse(get(orderForm, `${entityPrefixForOrder}住宿地`))[0]['label']
            : ''
        // 房型   酒店名称 酒店地址 间夜数 房间数量 出行人
        room_type = get(orderForm, `${entityPrefixForOrder}房型`, '') || get(orderForm, `${entityPrefixForOrder}房间名称`, '')
        hotel_name = get(orderForm, `${entityPrefixForOrder}酒店名称`, '')
        night_number = get(orderForm, `${entityPrefixForOrder}间夜数`, '1')
        room_number = get(orderForm, `${entityPrefixForOrder}房间数量`, '1')
        trip_people = get(orderForm, `${entityPrefixForOrder}出行人`, [])
        trip_number = get(orderForm, `${entityPrefixForOrder}入住人数`)
        have_meal = get(orderForm, `${entityPrefixForOrder}早餐`)
        hotel_adress = get(orderForm, `${entityPrefixForOrder}酒店地址`)

    }
    return { hotel_name, city, room_type, trip_people, room_number, startTime, endTime, have_meal, trip_number, hotel_adress, order }
}

export const formatFoodData = (originData: any) => {
    const { entityPrefixForOrder } = originData
    let { date, startTime, endTime, from_city, to_city, order, orderForm } = commonTripData(originData)
    let car_number = ''
    let car_type = ''
    if (order) {
        date = get(orderForm, `${entityPrefixForOrder}订单日期`, '')
        from_city = get(orderForm, `${entityPrefixForOrder}实际出发地点`)
            ? JSON.parse(get(orderForm, `${entityPrefixForOrder}实际出发地点`))[0]['label']
            : ''
        to_city = get(orderForm, `${entityPrefixForOrder}实际到达地点`)
            ? JSON.parse(get(orderForm, `${entityPrefixForOrder}实际到达地点`))[0]['label']
            : ''
        // 车牌号 车型
        car_number = get(orderForm, `${entityPrefixForOrder}车牌号`, '')
        car_type = get(orderForm, `${entityPrefixForOrder}车型`, '')
    }
    return { date, from_city, to_city, startTime, endTime, order }
}

export const formatCommonData = (originData: any) => {
    const { data, entityPrefixForTrip, entityPrefixForOrder } = originData
    const form = get(data, 'form')
    let date = get(form, `${entityPrefixForTrip}行程日期`, '') || get(form, `${entityPrefixForTrip}入住日期`, '') || get(form, `${entityPrefixForOrder}订单日期`, '')
    let startTime = get(form, `${entityPrefixForTrip}行程日期`, '') || get(form, `${entityPrefixForTrip}入住日期`, '')
    let endTime = get(form, `${entityPrefixForTrip}离店日期`, '')
    let from_city = get(form, `${entityPrefixForTrip}出发地`)
        ? JSON.parse(get(form, `${entityPrefixForTrip}出发地`))[0]['label']
        : ''
    let to_city = get(form, `${entityPrefixForTrip}目的地`)
        ? JSON.parse(get(form, `${entityPrefixForTrip}目的地`))[0]['label']
        : ''
    return { date, from_city, to_city, startTime, endTime }
}




export const track = (params: any) => {
    api.invokeService('@common:set:track', params)
}

export const goThirdResource = async (TK: string, params: any, otherParams?: any) => {
    let otherP = otherParams || {}
    const { platform, checked, taxiUrl, isTaxi, orderId, isNew } = otherP
    const result = await Promise.all([
        api.invokeService('@mall:get:travel:intent', { type: TK }),
        api.invokeService('@mall:get:travel:intent:jwt', { type: TK })
    ])
    let items = result[0] && result[0].items ? result[0].items : []
    const token = result[1] ? result[1].id : ''
    items.forEach((i: any) => {
        if (taxiUrl && i?.realm === 'MALL') {
            i.source = taxiUrl
            i.sourceType = 'OPEN_LINK'
        }
        if (isTaxi && orderId && i?.realm === 'MALL') {
            i.source = i.source + `${orderId}`
        }
        if (!i.source.includes('token')) {
            if (i.source.endsWith('wcar')) {
                i.source = i.source.replace('wcar', 'wmhcar')
                i.sourceType = 'OPEN_LINK'
            }
            i.source = generateQs(i.source, { ...params, token })
        }
        if (!i.source.includes('ekbAccessToken') && Fetch.accessToken) {
            i.source += `&ekbAccessToken=${Fetch.accessToken}`
        }
        if (platform) {
            const power = i.powers && i.powers.length ? i.powers[0] : undefined
            i.disabled = checked && platform.length > 1 ? !platform.includes(power) : false
        }
    })

    if (isNew && taxiUrl) {
        items = items.filter(i => i?.realm === 'MALL')
    }
    api.thirdResources.deleteByType(TK)
    api.thirdResources.add(items)
    if (items.length > 0) {
        const services = {
            token: () => {
                return new Promise((resolve, reject) => {
                    setTimeout(() => resolve({ RD: { c: 3 } }), 1000)
                })
            }
        }
        const result2 = await api.request({
            type: TK,
            services: services
        })
    } else {
        // 判断是否提供订购渠道
        return api.open('@home5:ShowMallInfoModal')
    }
}
export const handleOrderClick = (data: any, isNew?: boolean, entityPrefixForOrder?: string) => {
    if (isNew) {
        const taxiUrl = data ? get(data?.form, `${entityPrefixForOrder}taxiUrl`) : null
        const type = get(data?.form, `订单类型`)
        if (type === '用车') {
            api.invokeService('@common:set:track', {
                key: 'mytrips_onthego_click',
                actionName: '查看用车订单点击',
            })
            return goThirdResource(`BUTTON_TAXI_ORDER_DETAIL`, {}, { taxiUrl })
        } else {
            const orderId = get(data?.form, `${entityPrefixForOrder}订单号`, '')
            const orderType = type === '飞机' ? "FLIGHT" : type === '火车' ? "TRAIN" : "HOTEL"
            const TK = `BUTTON_${orderType}_ORDER_DETAIL` || 'BUTTON_TRAVEL_ORDER'
            const orderState = get(data?.form, `${entityPrefixForOrder}订单状态`, '')
            const params = getTypeUrl(orderType, orderId, orderState)
            return goThirdResource(TK, params)
        }

    }
    const type = get(data, 'entity.type', '')
    const parentId = get(data, 'entity.parentId')
    const platform = get(data, `E_${parentId}_订票平台`, '')
    const white = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI']
    const status = data[Object.keys(data).find(o => !!o.endsWith('订单状态')) || '']
    const statusList = ['退票', '退订', '退订/离店'] // @i18n-ignore
    const isRefund = statusList.includes(status) && data?.useCount < data?.totalCount
    if (platform === '合思商城' && white.includes(type)) {
        if (type !== 'TRAIN' && isRefund) {
            return false
        }
        const orderId = get(data, `E_${parentId}_订单号`, '')
        const orderType = get(data, `E_${parentId}_订单状态`, '')
        const TK = `BUTTON_${type}_ORDER_DETAIL` || 'BUTTON_TRAVEL_ORDER'
        const params = getTypeUrl(type, orderId, orderType)
        return goThirdResource(TK, params, { isTaxi: type === 'TAXI', orderId })

    } else {
        return false
    }
}

const getTypeUrl = (type: string, orderId: string, orderType: string) => {
    if (type === 'TRAIN') {
        return { orderNo: orderId, orderType: orderType === '改签' ? 2 : orderType === '退票' ? 3 : 1 }
    }
    if (type === 'HOTEL') {
        return { detailId: orderId }
    }
    return { orderId }
}

const generateQs = (link: string, params: any) => {
    const str = qs.stringify(params)
    const newLink = /\?/.test(link) ? `${link}&` : `${link}?`
    return `${newLink}${str}`
}

export function getDiffDay(startTime: number, endTime: number) {
    if (startTime && endTime) {
        const diff = moment(endTime).startOf('date').diff(moment(startTime).startOf('date'), 'days');
        return diff ? diff : false
    }
    return false
}