/*
 * @Author: z<PERSON><PERSON><PERSON> 
 * @Date: 2021-10-28 16:22:38 
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-12-29 11:58:35
 */
import React from 'react'
import { <PERSON>Footer } from './TripMultipleCard'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import style from './TripItemCard.module.less'
import { Button } from 'antd-mobile'
import { T } from '@ekuaibao/i18n'
import { Fetch } from '@ekuaibao/fetch'

const TelPhoneCall = api.require('@elements/TelPhoneCall')
const { getWeek, getDiff } = api.require('@bill/elements/datalink/dataLinkUtil')
const TAXI = require('../../../../images/standard/car.svg')
const PHONE = require('../../../../images/standard/phone.svg')

interface Props {
    onBuy: Function;
    dataSource: any;

}
export default (props: Props) => {
    const { onBuy, dataSource } = props
    const { date, car_number, car_type, from_city, to_city, startTime, endTime, driver_nubmer, car_use_type, driver_name, car_catagories, car_cat, car_color, order } = dataSource
    const format = Fetch.lang === 'en-US' ? "MM.DD" : 'MM月DD日'
    return (
        <div className={style['trip_items_card']}>
            <div className={`top_view_car ${!order && !['DING_TALK', 'WEIXIN'].includes(window.__PLANTFORM__) ? 'top_view_car_not_order' : ''
              }`}>
                <div className="l_view_car">
                    <img src={TAXI} alt="" />
                    <div className="info_view_car">
                      {
                        order ?
                          <>
                            <div className="date_car">
                              {`打车 ${moment(date).format('YYYY年MM月DD日 HH:mm')}`}
                            </div>
                            <div className="info_o_car">{`${car_use_type} ${car_catagories}`}</div>
                          </>
                          :
                          <>
                            <div className="date_not_order">
                              {startTime === endTime
                                ? `打车 ${moment(startTime).format(format)} ${getWeek(startTime)}`
                                : `打车 ${moment(startTime).format(format)} - ${moment(endTime).format(format)}`}
                            </div>
                            <div className="info_not_order">{from_city}</div>
                          </>
                      }
                    </div>
                </div>
                {!order && !['DING_TALK', 'WEIXIN'].includes(window.__PLANTFORM__) && (
                    <div className="r_view">
                        <Button
                            className="btn"
                            onClick={(e: any) => {
                                e?.stopPropagation()
                                onBuy && onBuy('TAXI')
                            }}
                        >
                            <T name="一键叫车" />
                        </Button>
                    </div>
                )}
            </div>
          {order && (
            <div className="bottom_view_car">
              <div className="icon_part">
                <div className="start_point" />
                <div className="line" />
                <div className="end_point" />
              </div>
              <div className="city">
                <div className="text">{from_city}</div>
                <div className="text">{to_city}</div>
              </div>
              {driver_nubmer && <div className="phone_part">
                <TelPhoneCall phoneNumber={driver_nubmer}>
                  <div className="back"><img src={PHONE} /></div>
                  <div className="text">{driver_name}</div>
                </TelPhoneCall>
              </div>}
            </div>
          )}
            {order && <div className="desc desc-taxi">
                {car_number && <span className="text-nowrap text-m-r">{car_number}</span>}
                {car_cat && <span className="text-taxi text-m-r">{car_cat}</span>}
                {car_color && <span className="text-taxi">{car_color}</span>}
            </div>}
            {/*<TripFooter {...props} />*/}
        </div >
    )

}