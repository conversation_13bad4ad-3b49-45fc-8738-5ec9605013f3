import React, { FC, useRef } from 'react'
import styles from './MallCardList.module.less'
import { app as api } from '@ekuaibao/whispered'
import { IMalCard } from '../../../home5.store'
import { Fetch } from '@ekuaibao/fetch'
interface IProps {
  meInfo: any
  list: IMalCard[]
  standardTrack?: (key: string, options: any) => void
}

const CardTypesTrack: any = {
  taxi: 'taxi',
  flight: 'internal_plane_ticket',
  hotel: 'internal_hotel',
  train: 'internal_train_ticket'
}

const MallCard: FC<IProps> = ({ meInfo, list, standardTrack }) => {
  const tokenId = useRef()
  if (!meInfo.staff) {
    return null
  }
  const { staff } = meInfo
  const { name: userName, id: userId } = staff
  const { name: corpName, id: corpId } = staff?.corporationId || {}

  const fnGetParamsStr = async () => {
    const staffSetting = Fetch.staffSetting || {}
    const language = i18n.currentLocale || staffSetting.language
    let version = window.APPLICATION_VERSION
    // @ts-ignore
    let codePushVersion = window.CODEPUSH_VERSION ? window.CODEPUSH_VERSION : ''
    const token = await fnGetToken()
    return `token=${token}&language=${language}&version=${version}&codePushVersion=${codePushVersion}&refPlatform=${
      window.__PLANTFORM__
    }&refTime=${Date.now()}&corpId=${Fetch.ekbCorpId}&ekbAccessToken=${Fetch.accessToken || ''}`
  }

  const fnGetToken = async () => {
    if (!!tokenId.current) return tokenId.current
    const result = await api.invokeService('@mall:get:travel:intent:jwt', { type: 'MENU_MALL' })
    const token = result?.id ?? ''
    tokenId.current = token
    return token
  }

  const handleClickTrack = async (item: IMalCard, cardType: string) => {
    const { url } = item
    const type = /\?/.test(url) ? '&' : '?'
    const paramsStr = await fnGetParamsStr()
    api.invokeService('@layout:open:link', `${url}${type}${paramsStr}`)
    standardTrack('homeMallEntryModule', { souce: 'home', actionType: CardTypesTrack[cardType] })
  }
  return (
    <>
      {list?.length ? (
        <div className={styles.mallCardWrapper}>
          <div className={styles.mallCardContent}>
            <div className={styles.title}>合思商城</div>
            <div className={styles.wrapper}>
              {list.map(item => {
                let color = '#FFFBFB'
                let cardType = 'taxi'
                if (item?.url?.endsWith('/wflight')) {
                  color = '#F7FCFF'
                  cardType = 'flight'
                } else if (item?.url?.endsWith('/whotel')) {
                  color = '#FCFAFF'
                  cardType = 'hotel'
                } else if (item?.url?.endsWith('/wtrain')) {
                  color = '#F8FDFC'
                  cardType = 'train'
                }
                return (
                  <div
                    key={item.title}
                    className={`${styles.item}`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleClickTrack(item, cardType)}
                  >
                    <img src={item.picture} alt="" />
                    <span>{item.title}</span>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      ) : null}
    </>
  )
}

export default MallCard
