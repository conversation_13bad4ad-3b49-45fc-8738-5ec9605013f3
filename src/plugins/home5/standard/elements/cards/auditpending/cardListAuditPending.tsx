import { app } from '@ekuaibao/whispered'
/**************************************
 * Created By LinK On 2019/3/5 14:20.
 **************************************/
import React from 'react'
import styles from './cardListAuditPending.module.less'
//@ts-ignore
import { cardDefaultValue, CardProps } from '../../../../home5.store'
import { MyBillCardItemProps } from '../mybill/cardList_myBill'
import {  ListCardHeader } from '../../view-util'
import { get } from 'lodash'
import { getDateDiff } from '../../../../../../lib/util'
import { mapForCodeToPath, standardTrack } from '../../../../staticUtil'
import { app as api } from '@ekuaibao/whispered'
import { getStaffShowByConfig } from '../../../../../../components/utils/fnDataLinkUtil'

interface DataItemProps extends StringAnyProps {
  title: string
  count: number
  filter: string
}

export interface AuditDataProps {
  [propName: number]: DataItemProps
}

interface StaffProps extends StringAnyProps {
  name: string
  id: string
  avatar: string
}

interface FormProps extends StringAnyProps {
  title: string
  submitterId: StaffProps
}

interface FlowProps extends StringAnyProps {
  form: FormProps
}

export interface AuditItemProps extends StringAnyProps {
  flowId: FlowProps
}

interface AuditPendingProps extends StringAnyProps {
  data: CardProps
  hideAvatar?: boolean
}

const auditPendingDefultValue = {
  ...cardDefaultValue,
  detail: { list: new Array<MyBillCardItemProps>(), prompt: { value: 0 }, data: new Array<DataItemProps>() }
}

export const ListItem = (props: {
  el: AuditItemProps
  idx: number
  hideAvatar: boolean
  onClick?: Function
  searchValue?: string
  approveShowState?: boolean
  extraClassName?: string
}) => {
  const EKBIcon = app.require('@elements/ekbIcon')
  const Money = app.require('@elements/puppet/StandardMoney')
  const isUrgent = api.require('@bill/bill-content/bill-item.isUrgent')

  const { el, idx, onClick, extraClassName = '' } = props
  const title = get(el, 'flowId.form.title', '')
  const submitter = get(el, 'flowId.form.submitterId', '')
  const submitDate = get(el, 'flowId.form.submitDate', '')
  const form = get(el, 'flowId.form', '')
  const formType = get(el, 'flowId.formType', '')

  const amount = form.amount ? form.amount : form[(formType === 'payment' ? 'pay' : formType) + 'Money'] || null
  const handleGotoDetail = (data: AuditItemProps) => {
    api.invokeService('@home:save:specification').then(() => {
      api.go('/approve/approving/expense/' + data.id + '/approving', false)
    })
  }
  const logs = get(el, 'flowId.logs', [])
  const urgent = typeof el.isUrgent === 'boolean' ? el.isUrgent : isUrgent(logs)

  return (
    <div
      key={idx}
      className={`${styles['billList-wrap']} ${idx === 0 ? styles['filter-wrap'] : ''} ${extraClassName}`}
      onClick={onClick || handleGotoDetail.bind(this, el)}
    >
      <div className="bill-content">
        <div className="bill-content-top">
          <div className={`bill-state-mark bill-blue`} />
          <span className="bill-name">{getStaffShowByConfig(submitter)}</span>
          <span>{getDateDiff(submitDate)}</span>
          <span>{i18n.get('提交')}</span>
        </div>
        <div className="bill-content-middle">
          <span>
            {urgent && <EKBIcon name="#EDico-expedited" className="urgent-color" />}
            <span>{title}</span>
          </span>
          <span>
            {amount && Number(amount.standard) !== 0 ? (
              <Money value={amount} showShorthand={true} />
            ) : (
              i18n.get('暂无金额')
            )}
          </span>
        </div>
      </div>
    </div>
  )
}

export default class CardListAuditPending extends React.Component<AuditPendingProps, any> {
  static defaultProps = {
    data: auditPendingDefultValue,
    hideAvatar: false
  }

  handleAuditPending = (item?: AuditItemProps) => {
    const { data } = this.props
    //通过 filter (条件)区分是否点击的是查看全部
    if (item && item.filter) {
      const filter = JSON.parse(item.filter)
      const filterType = item.isUrgent ? 'all' : filter.sceneIndex
      api.invokeService('@approve:setSelectBillsType', filterType)
    } else {
      api.invokeService('@approve:setSelectBillsType', 'all')
    }
    standardTrack('homeForMyApprovalModule', { souce: 'home', actionType: 'more_bills' })
    api.go(`/${mapForCodeToPath[data.code]}`, false)
  }

  handleGotoDetail = (data: AuditItemProps) => {
    api.invokeService('@home:save:specification').then(() => {
      api.go('/approve/approving/expense/' + data.id + '/approving', false)
    })
  }

  renderBillList = () => {
    const NOT_BILLS = app.require('@images/not-bills.svg')
    const { data, hideAvatar } = this.props
    const list = data?.list || []
    return (
      <>
        {list.length > 0 ? (
          list.map((el: AuditItemProps, idx: number) => (
            <ListItem el={el} idx={idx} key={idx} hideAvatar={hideAvatar} />
          ))
        ) : (
          <div className={styles['not-bills']}>
            <img src={NOT_BILLS} />
            {i18n.get('暂无待我审批的单据')}
          </div>
        )}
      </>
    )
  }
  render() {
    return (
      <>
        <ListCardHeader click={this.handleAuditPending} title={i18n.get('待我审批')} />
        {this.renderBillList()}
      </>
    )
  }
}
