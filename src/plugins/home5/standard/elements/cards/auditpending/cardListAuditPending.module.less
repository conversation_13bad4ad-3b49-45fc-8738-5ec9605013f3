@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '../../../../../../styles/layout.less';

.filter-wrap {
  display: flex;
  justify-content: flex-start;
  padding: 0 24px;
  flex: 1;
  :global {
    .filter-item {
      &:last-child {
        margin-right: 0;
      }
      &:active {
        transform: scale(0.97);
        box-shadow: none;
        background-color: @color-bg-2;
      }
      &.isUrgent {
        background: @color-error-2;
        .shadow-error-3;
        color: @color-white-1;
        .filter-item-bottom {
          color: @color-white-1;
        }
      }
      &.custom {
        background: @color-inform-2;
        color: @color-white-1;
        .filter-item-bottom {
          color: @color-white-1;
        }
      }
      width: 33%;
      height: 176px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-right: @space-4;
      background: @color-white-1;
      //transition: .2s ease all;
      .shadow-black-2;
      border-radius: @radius-3;
      justify-content: center;
      padding-left: @space-6;
      .filter-item-top,
      .filter-item-bottom {
        .font-weight-2;
      }
      .filter-item-top {
        .font-size-6;
      }
      .filter-item-bottom {
        .font-size-2;
        color: @color-black-2;
        .word-break();
        width: 140px;
      }
    }
  }
}

.billList-wrap {
  display: flex;
  justify-content: flex-start;
  padding: 0 24px 24px;
  &:active {
    background-color: @color-bg-2;
  }
  > img {
    width: 48px;
    height: 48px;
    margin-top: @space-5;
    border-radius: @radius-2;
  }

  :global {
    .bill-content {
      display: flex;
      flex: 1;
      flex-direction: column;
      padding: @space-5 24px;
      overflow: hidden;
      height: 164px;
      background: #F7F8FA;
      border-radius: 20px;
      justify-content: center;
      .bill-content-top,
      .bill-content-middle,
      .bill-content-bottom {
        display: flex;
        justify-content: space-between;
        color: @color-black-1;
      }
      .bill-state-mark {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: @radius-3;
        margin-right: @space-4;
      }
      .bill-blue {
        background-color: #1890FF;
      }
      .bill-content-text {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .auto-create-type {
          width: 112px;
          height: 40px;
          background: rgba(29, 43, 61, 0.06);
          border-radius: 8px;
          font-size: 24px;
          font-weight: 400;
          text-align: center;
          color: rgba(29, 43, 61, 0.5);
          line-height: 40px;
          margin-left: 16px;
        }
      }
      .bill-content-warining {
        .font-size-1;
        margin-top: @space-1;
        color: @color-error-2;
      }
      .bill-content-top {
        display: flex;
        align-items: center;
        justify-content: left;
        font-size: 24px;
        color: #142234;
        .bill-name {
          margin-right: 6px;
        }
      }
      .bill-content-middle {
        margin: @space-2 0;
        padding-left: 32px;
        font-size: 28px;
        font-weight: 500;
        color: #142234;
        opacity: 0.92;
      }
      .bill-content-bottom {
        .font-weight-2;
        .font-size-2;
        .word-break();
        color: @color-black-3;
        display: inline-block;
        max-width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}

.not-bills {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: @space-6;
  min-height: 212px;
  .font-weight-2;
  .font-size-2;
  color: @color-black-4;
  > img {
    margin-bottom: @space-5;
  }
}
