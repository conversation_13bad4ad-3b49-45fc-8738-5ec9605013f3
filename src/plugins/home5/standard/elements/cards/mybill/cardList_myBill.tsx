import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import { get } from 'lodash'

import { cardDefaultValue } from '../../../../home5.store'
import { mapForCodeToPath, standardTrack } from '../../../../staticUtil'
import { ListCardEmpty, ListCardHeader } from '../../view-util'

import { MyBillCardItemProps, MyBillCardProps } from './cardList_myBill.types'
export * from './cardList_myBill.types'

const myBillCardDefultValue = {
  ...cardDefaultValue,
  detail: { list: new Array<MyBillCardItemProps>(), prompt: { value: 0 } }
}

import { CardList_BillItem } from './cardList_myBill.CardList_BillItem'
export { CardList_BillItem }

import { CardList_Bill } from './cardList_myBill.CardList_Bill'
export { CardList_Bill }

import { BillQuickEntrance } from './cardList_myBill.BillQuickEntrance'
export { BillQuickEntrance }

class CardList_MyBill extends Component<MyBillCardProps, any> {
  public static defaultProps = {
    data: myBillCardDefultValue,
    paidList: new Array<any>()
  }

  componentDidMount() {
    // 获取已完成单据
    // api.store.dispatch('@home5/getArchivedAndPaidList')()
  }

  handleMyBillAll = () => {
    const { data } = this.props
    standardTrack('homeMyBillsModule', { souce: 'home', actionType: 'more_bills' })
    api.go(`/${mapForCodeToPath[data.code]}`, false)
  }

  renderBillList = () => {
    const { data } = this.props
    const list = get(data, 'list') || []
    return <CardList_Bill list={list} />
  }

  render() {
    const { data } = this.props
    const value = data.list?.length
    return (
      <>
        <ListCardHeader title={i18n.get('我的单据')} click={this.handleMyBillAll} />
        {!!value ? this.renderBillList() : <ListCardEmpty title={i18n.get('暂无未完成单据')} />}
      </>
    )
  }
}

export default CardList_MyBill
