import React from 'react'
import { CardProps } from '../../../../home5.store'

export enum PlanType {
  nomal = 'NOMAL',
  countersign = 'COUNTERSIGN'
}

export enum OperatorType {
  person = 'PERSON',
  system = 'SYSTEM',
  ebot = 'EBOT'
}

interface SpecificationProps extends StringAnyProps {
  name: string
  id: string
}

interface StaffProps extends StringAnyProps {
  name: string
}

interface FlowProps {
  id: string
  state: string
  payingFailure: boolean
}

interface FormProps {
  title: string
  type: string
  amount: string
  specification: SpecificationProps
}

interface PlanProps {
  type: PlanType
  urgent: boolean
  nodes: NodeProps[]
  taskId: string
}

interface NodeProps {
  id: string
}

interface OperatorProps {
  count: number
  type: OperatorType
  staff: StaffProps
}

interface OperatorIdProps {
  name: string
}

export interface logsProps extends StringAnyProps {
  action: string
  operatorId: OperatorIdProps
}

export interface MyBillCardItemProps {
  flow: FlowProps
  form: FormProps
  plan: PlanProps
  operator: OperatorProps
  logs: logsProps[]
  needOperatorFormPlan: boolean
}

export interface BillItemProps {
  data: MyBillCardItemProps
  searchValue?: string
  needOperatorFormPlan?: boolean
  isAlwaysPrint?: boolean
}

export interface MyBillCardProps {
  data: any[]
  getArchivedAndPaidList: () => void
  paidList: any[]
  [propsName: string]: any
}
