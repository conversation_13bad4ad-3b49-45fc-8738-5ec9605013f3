/***************************************************
 * Created by nanyuantingfeng on 2020/6/3 16:36. *
 ***************************************************/

import { get } from 'lodash'
import { billStateMap } from '../../../../staticUtil'
import { app as api } from '@ekuaibao/whispered'
import Highlighter from 'react-highlight-words'
import { getMyBillApproveStatus } from '../../../../util/myBIllStatus'
import styles from './cardList_myBill.module.less'
import React from 'react'
import { app } from '@ekuaibao/whispered'

const EKBIcon = app.require('@elements/ekbIcon')
const Money = app.require('@elements/puppet/StandardMoney')
const isUrgent = app.require('@bill/bill-content/bill-item.isUrgent')

import { BillItemProps } from './cardList_myBill.types'

export const CardList_BillItem = function(props: BillItemProps) {
  const { data, searchValue, needOperatorFormPlan, isAlwaysPrint = false } = props
  // const { flow, form, plan } = data
  // const { state, id, payingFailure = false } = flow
  // const { urgent } = plan
  // const { type, title, amount, specification } = form
  // const { color } = billStateMap[state]

  // 兼容2种数据格式
  const state = get(data, 'flow.state') || get(data, 'state')
  const id = get(data, 'flow.id') || get(data, 'id')
  const payingFailure = get(data, 'flow.payingFailure', false)
  const urgent = get(data, 'plan.urgent') || isUrgent(get(data, 'logs', []))
  const type = get(data, 'form.type') || get(data, 'formType')
  const title = get(data, 'form.title')
  const isRiskWarning = get(data, 'calcRiskWarning')
  const code = get(data, 'form.code', '')
  const amount =
    get(data, 'form.amount') || get(data, ['form', `${type === 'payment' ? 'pay' : type}Money`, 'standard'])
  const currencySymbol =
    get(data, 'form.amount.standardSymbol') ||
    get(data, ['form', `${type === 'payment' ? 'pay' : type}Money`, 'standardSymbol'])
  const specification = get(data, 'form.specification') || get(data, 'form.specificationId')
  const { color } = billStateMap()[state]
  const auto = get(data, 'flow.systemGeneration')
  const alterFlag = get(data, 'flow.alterFlag') >= '1'
  function handleClickItem() {
    const params = { id, formType: type, state, isAlwaysPrint }

    const configsList = get(data, 'form.specification.configs')
    const loanMoney = get(data, 'form.amount.standard')
    const stateLoan = get(data, 'flow.state')
    const getId = get(data, 'id')
    const isLoan =
      configsList &&
      configsList.find((item: { ability: string }) => {
        return item.ability === 'loan'
      })
    if (isLoan && loanMoney > 0 && (stateLoan === 'paid' || stateLoan === 'archived')) {
      api
        .invokeService('@bill:get:loanpackage:by:flowId', getId)
        .then(() => {
          api.invokeService('@home:save:specification').then(() => {
            api.invokeService('@home:click:bill', params, 'homePage')
          })
        })
        .catch((err: any) => {
          api.invokeService('@home:save:specification').then(() => {
            api.invokeService('@home:click:bill', params, 'homePage')
          })
        })
    } else {
      api.invokeService('@home:save:specification').then(() => {
        api.invokeService('@home:click:bill', params, 'homePage')
      })
    }
  }

  let t = title || i18n.get('[无标题]')
  if (searchValue) {
    t = (
      <Highlighter
        highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
        searchWords={[searchValue]}
        textToHighlight={t}
      />
    )
  }

  const { name, str } = getMyBillApproveStatus({
    flow: { state },
    form: data.form,
    plan: data.plan || {},
    operator: data.operator || { staff: {} },
    needOperatorFormPlan
  } as any)

  return (
    <div key={id} className={styles['cardList_BillItem-wrap']} onClick={handleClickItem}>
      <div className="cardList_BillItem">
        <div className="cardList_BillItem-left">
          <div className="cardList_BillItem-log">
            <div className={`BillItem-State-mark ${color}`} />
            {!!name && <span className={`operator-span ${color === 'red' ? 'select-red' : ''}`}>{name}</span>}
            <span className={color === 'red' ? 'select-red' : ''}>{str}</span>
          </div>
          <div className="cardList_BillItem-content">
            <div className="cardList_BillItem-title">
              <span>{t}</span>
              {/*{(isRiskWarning && isRiskWarning !== null && isRiskWarning.status === 'HAVE') ? <><EKBIcon name="#EDico-plaint-circle" style={{color:'#fa8c16',marginRight: 5}} /> <span>{t}</span></>  : <span>{t}</span>}*/}
            </div>
            <div className="cardList_BillItem-right">
              {amount && Number(amount) !== 0 ? (
                <Money
                  value={amount}
                  showShorthand={true}
                  currencySymbol={currencySymbol}
                  className="cardList_BillItem-money"
                />
              ) : (
                <span className="cardList_BillItem-no-money">{i18n.get('暂无金额')}</span>
              )}
            </div>
          </div>
          {/*<div className="cardList_BillItem-bottom">*/}
            {/*<div className="cardList_BillItem-bottom-left">*/}
              {/*<div className="cardList_BillItem-Specification">{specification.name}</div>*/}
              {/*<div className="cardList_BillItem-code">{code}</div>*/}
              {/*{window.isNewHome && get(data, 'flow.state') === 'paid' && (*/}
                {/*<div className="cardList_BillItem-status">{i18n.get('未确认')}</div>*/}
              {/*)}*/}
              {/*{payingFailure && <div className="cardList_BillItem-warining">{i18n.get('支付失败')}</div>}*/}
            {/*</div>*/}
            {/*{auto && <div className="auto-create-type">自动创建</div>}*/}
            {/*{alterFlag && <div className="auto-create-type">变更</div>}*/}
          {/*</div>*/}
        </div>
      </div>
    </div>
  )
}

export default CardList_BillItem
