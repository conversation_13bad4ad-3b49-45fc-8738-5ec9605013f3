import styles from './cardList_myBill.module.less'
import { CardList_BillItem } from './cardList_myBill.CardList_BillItem'
import React from 'react'
import { MyBillCardItemProps } from './cardList_myBill'

/***************************************************
 * Created by nanyuantingfeng on 2020/6/3 16:36. *
 ***************************************************/

export const CardList_Bill = ({
  list,
  style,
  searchValue
}: {
  list: MyBillCardItemProps[]
  style?: any
  searchValue?: string
}) => {
  return (
    <div className={styles['cardList_myBill-content']} style={style}>
      {list.map((el: MyBillCardItemProps, idx: number) => {
        return <CardList_BillItem key={idx} data={el} searchValue={searchValue} />
      })}
    </div>
  )
}


export default CardList_Bill
