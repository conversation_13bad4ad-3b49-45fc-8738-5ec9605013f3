import './CompletedAndUnConfirmedSeg.less'
import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import { SegmentedControl } from 'antd-mobile'
import EnhanceTitleHook from '../../../../../../lib/EnhanceTitleHook'
const MessageListView = api.require("@home/elements/MessageListView")
const ArchivedView = api.require("@mine/mine-archived")

interface Props {
  type: string
  params: { type: string }
  segmentActiveKey: number
}

interface States {
  tabActiveKey: number
  unConfirmedTitle: string
}
@EnhanceTitleHook(i18n.get('已完成单据'))
@connect((store: any) => ({
  segmentActiveKey: store.states['@home5']?.segmentActiveKey
}))
export default class CompletedAndUnconfirmedSeg extends Component<Props, States> {
  constructor(props: Props) {
    super(props)
    this.state = {
      tabActiveKey:
        props.segmentActiveKey === undefined ? (props.params.type === 'paidList' ? 1 : 0) : props.segmentActiveKey,
      unConfirmedTitle: i18n.get('未确认单据')
    }
  }

  componentWillMount() {
    api.watch('refresh:segment:count', this.refreshCount)
  }

  componentWillUnmount() {
    api.un('refresh:segment:count', this.refreshCount)
  }

  refreshCount = (title: string) => {
    this.setState({ unConfirmedTitle: title })
  }

  handleOnChange = (target: any) => {
    this.setState({ tabActiveKey: target.nativeEvent.selectedSegmentIndex }, () => {
      api.store.dispatch('@home5/setSegmentActiveKey')(this.state.tabActiveKey)
    })
  }

  renderContent = () => {
    const { tabActiveKey } = this.state
    const { params } = this.props
    if (tabActiveKey === 0) {
      return <ArchivedView isAlwaysPrint />
    } else {
      return <MessageListView params={params} isAlwaysPrint />
    }
  }

  render() {
    const { tabActiveKey, unConfirmedTitle } = this.state
    return (
      <div className="segment-main">
        <div className="segment-wrap">
          <SegmentedControl
            className="segment"
            values={[i18n.get('全部单据'), unConfirmedTitle]}
            selectedIndex={tabActiveKey}
            onChange={this.handleOnChange}
          />
        </div>
        {this.renderContent()}
      </div>
    )
  }
}
