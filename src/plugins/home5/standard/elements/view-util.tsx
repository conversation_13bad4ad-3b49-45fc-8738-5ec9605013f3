import { app } from '@ekuaibao/whispered'
import React from 'react'
import styles from './view-util.module.less'
const EKBIcon: any = app.require('@elements/ekbIcon')

type noon = () => void

interface ListCardProps {
  title: string
  click?: noon
}

interface EmptyProps {
  title: string
}
interface ListCardBottomProps extends ListCardProps {}
interface ListCardHeaderProps extends ListCardProps {}
const ListCardHeader: React.FunctionComponent<ListCardHeaderProps> = (props: any) => {
  const { title, click } = props
  return (
    <div className={styles.ListCard_Header} onClick={click}>
      <div className={styles.title}>{title}</div>
      <div className={styles.more}>{i18n.get('更多单据')} <EKBIcon className="icon-right-standard" name="#ico-7-right" /></div>
    </div>
  )
}
const ListCardBottom: React.FunctionComponent<ListCardBottomProps> = (props: any) => {
  const { title, click, ...arg } = props
  return (
    <div {...arg} className={styles.ListCard_Bottom} onClick={click}>
      <span>{title}</span>
    </div>
  )
}

const ListCardEmpty: React.FunctionComponent<EmptyProps> = (props: any) => {
  const { title } = props
  const SVG_EMPTY = app.require('@images/not-bills.svg')
  return (
    <div className={styles.ListCard_Empty}>
      <img src={SVG_EMPTY} alt="" />
      <span>{title}</span>
    </div>
  )
}

export { ListCardHeader, ListCardBottom, ListCardEmpty }
