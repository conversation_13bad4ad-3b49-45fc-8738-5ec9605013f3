@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.ListCard_Bottom {
  user-select: none;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: @space-5 0 @space-6;
  color: @color-inform-1;
  .font-size-2;
  .font-weight-2;
  &:active {
    background-color: @color-bg-2;
  }
}

.ListCard_Header {
  user-select: none;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &:active {
   background-color: @color-bg-2;
  }
  .title {
    font-weight: 500;
    font-size: 32px;
    color: #142234;
  }
  .more {
    color: #4E5969;
    font-weight: 600;
    font-size: 26px;
    display: flex;
    align-items: center;
  }
}
.ListCard_Empty {
  margin: @space-9 auto @space-8 auto;
  display: flex;
  flex-direction: column;
  span {
    margin-top: @space-6;
    .font-size-2;
    .font-weight-2;
    color: @color-black-4;
  }
}
