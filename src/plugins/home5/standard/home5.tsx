// @ts-ignore
import { connect } from '@ekuaibao/mobx-store'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { addMapJsApi } from '../../../lib/mapjsapi'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import HomeContainer from '../../home-container'
import styles from './home5.module.less'
import { toast, fixWeixinPrint, getUrlParamString, generateQs } from '../../../lib/util'
import { UIContainer as Container } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { get } from 'lodash'
import { session } from '@ekuaibao/session-info'
import StandardHomepage from './elements/cards/StandardHomepage'
import StandardCardList from './elements/cards/StandardCardList'
import UpComingTrip from './elements/cards/UpComingTrip'
import CardList from './elements/cards/cardList'
import CardAllData from './elements/cards/cardAllData'
import MallCardList from './elements/cards/MallCardList'
import Home5QuestionFloat from '../elements/home5-question-float'
import { standardTrack } from '../staticUtil'
// TODO: 标准版首页刷新
import { PullToRefresh } from '@hose/eui-mobile'
import qs from 'qs'

interface Props {
  renderFooter: Function
  marketBannerHadInit: any
  cardList: any[]
  isFirstLoadCardList?: boolean
}

const entryMap = {
  dingtalk: 'dingtalk',
  qywx: 'qywx',
  wxgzh: 'wxgzh'
}

const urlParams = qs.parse(location.search.slice(1))

// @ts-ignore
@EnhanceTitleHook(() => i18n.get(`易快报@@${Fetch.ekbCorpId}`, null, () => i18n.get('易快报')))
// @ts-ignore
@connect(store => ({
  marketBannerHadInit: store.states['@marketADBanner'].marketBannerHadInit,
  groupedCardList: store.states['@home5'].groupedCardList,
  standardCardListAuditPending: store.states['@home5'].standardCardListAuditPending,
  standardCardListMyBill: store.states['@home5'].standardCardListMyBill,
  logoUrl: store.states['@home5'].logoUrl,
  isFirstLoadCardList: store.states['@home5'].isFirstLoadCardList,
  title: store.states['@home5'].title,
  showTripCard: store.states['@home5'].showTripCard,
  menuTripData: store.states['@home5'].menuTripData,
  recentTripData: store.states['@home5'].recentTripData,
  tripAssistData: store.states['@home5'].tripAssistData,
  corpList: store.states['@home5']?.corpList || [],
  mallCardDataList: store.states['@home5']?.mallCardDataList,
  isOrder: store.states['@home5']?.isOrder,
  prefixMap: store.states['@home5'].prefixMap
}))
@EnhanceConnect((state: any) => ({
  me_info: state['@common'].me_info || {},
  isNewMall: state['@common'].powers.newMall
}))
export default class Home5 extends HomeContainer {
  constructor(props: Props) {
    super(props)
    this.state = {
      selectedCorp: null,
      selectedCorpId: ''
    } as any
    if (!this.props.marketBannerHadInit) {
      api.store.dispatch('@marketADBanner/changeValueForMarketBannerHadInit')()
    }

    // trackHome()

    api.store.dispatch('@home5/getCardList')()
    api.store.dispatch('@home5/getStandardCardListAuditPending')()
    api.store.dispatch('@home5/getStandardCardListMyBill')()
  }

  componentDidMount() {
    const { isNewMall } = this.props
    // 处理微信打印后，不能记住页面状态的问题
    fixWeixinPrint()
    sessionStorage.setItem('num', '2') // 修改携程商旅在当前webview打开，第二次进不去的问题

    if (window.__PLANTFORM__ === 'APP' || window.PLATFORMINFO?.thirdPartSwithCorp) {
      api.store.dispatch('@home5/getCorporations')()
    }
    const { standardCurrency } = api.getState()['@common']
    if (!standardCurrency.symbol) {
      // 原生登录时，有些请求401，需要再请求一次
      // api.dataLoader('@common.departments').reload()
      // api.dataLoader('@common.standardCurrency').reload()
      // api.invokeService('@home:get:specification:with:version')
    }

    const hasMYCARBUSINESS = api.getState('@common').powers.MYCARBUSINESS
    if (hasMYCARBUSINESS) {
      api.invokeService('@mycarbusiness:get:position:getMyCarBusinessPower').then((data: any) => {
        if (data.value.active) {
          setTimeout(addMapJsApi, 0)
          api.invokeService('@mycarbusiness:get:position:getRoute')
        }
      })
    }
    // api.dataLoader('@common.payerInfo').load()
    // api.invokeService('@common:get:organization:config')
    const corpStyle = get(api, 'sdk.staffSetting.corpStyle')
    corpStyle && api.store.dispatch('@home5/setCorpStyle')(corpStyle)
    this.trackLoginTime()

    if (isNewMall) {
      api.store.dispatch('@home5/getMallCardData')()
    }
  }

  trackLoginTime = () => {
    const trackType = localStorage.getItem('trackType')
    const loginTimeStart = localStorage.getItem('loginTimeStart')
    const loginCheckTime = new Date().getTime() - Number(loginTimeStart)
    if (loginCheckTime < 1000 * 60) {
      if (trackType === 'loginPage' && loginTimeStart) {
        api.track('loginHomepage', {
          actionName: '用户点击登陆至首页时长统计', // @i18n-ignore
          loginCheckTime: loginCheckTime,
          decice: 'applet'
        })
      } else if (trackType === 'enterprisesPage' && loginTimeStart) {
        api.track('midLandingToHomepage', {
          actionName: '用户点击选择企业至登陆至首页时长统计', // @i18n-ignore
          loginCheckTime: loginCheckTime,
          decice: 'applet'
        })
      }
    }
    localStorage.removeItem('trackType')
    localStorage.removeItem('loginTimeStart')
  }
  corpOnClick = (corp: any) => {
    const selectedCorpId = corp.id
    this.setState({ selectedCorpId, selectedCorp: corp }, () => {
      if (Fetch.ekbCorpId === this.state.selectedCorpId) return api.go(-1)

      if (this.state.selectedCorp.sourceChannel === 'V1') {
        let url = this.state.selectedCorp.sourceId
        var token = getUrlParamString(url, 'token')
        token = decodeURIComponent(token as string)
        api.store
          .dispatch('@home5/getV1UserInfo')({ token: token })
          .then((data: any) => {
            let json = data.id
            json = JSON.parse(json)
            json.token = token
            if (json.code === 100) {
              api.invokeService('@layout:login:v1', { loginData: json })
              return
            } else {
              toast.info(i18n.get('登录失效，请重新登录'))
              // @ts-ignore
              api.gotoLoginPage()
            }
          })
          .catch((e: any) => {
            toast.info(e.message)
            return
          })
      }

      if (this.state.selectedCorp.sourceChannel === 'GROUP') {
        localStorage.setItem('wereRedirectToGroupApp', 'true')
        return (location.href = this.state.selectedCorp.sourceId)
      }

      if (this.state.selectedCorp.sourceChannel === 'SHARED') {
        const shared_url = generateQs(this.state.selectedCorp.sourceId, {fromApp: true})
        localStorage.setItem('wereRedirectToSharedApp', 'true')
        localStorage.setItem('SHARED_URL', shared_url)
        location.href = shared_url
        return
      }

      Fetch.ekbCorpId = this.state.selectedCorpId
      api.store.dispatch('@home5/switchcorporationForLog')(this.state.selectedCorpId)
      const params = window.IS_SMG
        ? Fetch.makeUrlParams(
          {
            ekbCorpId: Fetch.ekbCorpId
          },
          ['corpId', 'ekbCorpId']
        )
        : Fetch.makeUrlParams(
          {
            corpId: Fetch.ekbCorpId,
            ekbCorpId: Fetch.ekbCorpId,
            wxCorpId: Fetch.wxCorpId
          },
          ['corpId', 'ekbCorpId']
        )

      // 解决一财移动端主页上的企业, 没有根据 url上的 cropId选中问题
      if (urlParams['entry'] === entryMap.dingtalk && urlParams['urlCorpId'] && window.__PLANTFORM__ === 'APP') {
        sessionStorage && sessionStorage.setItem('isChangeCorp', 'true')
      }
      session.set('user', {
        accessToken: Fetch.accessToken,
        corpId: Fetch.ekbCorpId
      })
      setTimeout(() => {
        location.replace('?' + params)
      }, 1000)
    })
  }
  refresh0 = () => {
    return new Promise(resolve => {
      api.store.dispatch('@home5/getCardList')(() => {
        resolve()
      })
    })
  }
  refresh1 = () => {
    return new Promise(resolve => {
      api.store.dispatch('@home5/getStandardCardListAuditPending')(() => {
        resolve()
      })
    })
  }
  refresh2 = () => {
    return new Promise(resolve => {
      api.store.dispatch('@home5/getStandardCardListMyBill')(() => {
        resolve()
      })
    })
  }
  sleep = (time = 1000) => {
    return new Promise((res) => {
      setTimeout(() => {
        res()
      }, time)
    })
  }

  render() {
    const {
      groupedCardList,
      standardCardListMyBill,
      standardCardListAuditPending,
      isFirstLoadCardList,
      showTripCard,
      recentTripData,
      menuTripData,
      title,
      me_info,
      isNewMall,
      mallCardDataList,
      isOrder,
      tripAssistData,
      prefixMap
    } = this.props
    const myBill = standardCardListMyBill
    const auditPending = standardCardListAuditPending
    return (
      <div className="inertial-rolling h-100-percent w-100p h-100p">
        <PullToRefresh
          onRefresh={async () => {
            await Promise.race([Promise.all([this.refresh0(), this.refresh1(), this.refresh2()]), this.sleep(30000)])
          }}
        >
        <div className={styles['standard-home5-view-wrapper']}>
          <div className="home5-view-content-wrap inertial-rolling h-100-percent">
            <div className="home5-view-content">
              <StandardCardList standardTrack={standardTrack}/>
              <StandardHomepage standardTrack={standardTrack}/>
              {showTripCard ? (
                <UpComingTrip
                  isOrder={isOrder}
                  title={title}
                  data={menuTripData}
                  recentTripData={recentTripData}
                  tripAssistData={tripAssistData}
                  prefixMap={prefixMap}
                />
              ) : null}
              {(mallCardDataList && me_info) && <MallCardList meInfo={me_info} list={mallCardDataList} standardTrack={standardTrack} />}
              {myBill && <CardList data={myBill}/>}
              {auditPending && <CardAllData data={auditPending}/>}
            </div>
          </div>
          </div>
        </PullToRefresh>
        <Container name="@new-feature:modal" />
        {/* <Home5QuestionFloat userInfo={me_info} /> */}
      </div>
    )
  }
}
