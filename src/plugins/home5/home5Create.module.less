@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-mobile/less/coverage.less';

.home5-create-page {
  overflow-y: hidden;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  :global {
    .home5-create-search {
      height: 96px;
      background-color: transparent;
      margin-bottom: 10px;
      .shadow-1;
      .am-search-input {
        background: rgba(20, 34, 52, 0.06);
        border-radius: 0.28rem;
      }
    }
  }
}

.home5-create-content {
  overflow: auto;
  height: 100%;
  background-color: #ffffff;
  flex: 1;
  :global {
    .am-accordion {
      -webkit-overflow-scrolling: touch;
      padding-bottom: 100px;
    }
    .am-accordion-header {
      text-align: left;
      .lighter {
        .font-size-3;
        .font-weight-3;
      }
      i.arrow {
        width: @space-5 !important;
        height: @space-5 !important;
      }
    }
    .am-accordion-content-box {
      overflow: hidden;
      padding: 16px 8px;
    }
    .clearfix {
      width: 100%;
      height: 0;
      float: left;
    }
    .spec-item {
      width: 50%;
      float: left;
      padding: 8px;
    }
    .spec-item-wrapper {
      display: flex;
      align-items: center;
      position: relative;
      padding: 16px 0 16px 16px;
      border-radius: @radius-1;
    }
    .spec-item-wrapper:active {
      background-color: @color-bg-2;
    }
    .spec-item-icon-bg {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background-color: @color-bg-1;
      opacity: 0.1;
      align-self: flex-start;
      display: none;
    }
    .spec-item-icon {
      width: 44px;
      height: 44px;
    }
    .spec-item-title {
      .font-size-2;
      flex: 1;
      text-align: left;
      padding-left: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
    }
  }
}
