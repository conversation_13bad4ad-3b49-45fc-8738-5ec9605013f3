import { app } from '@ekuaibao/whispered'
import moment from 'moment'

const track = (trackName: string, params: any) => {
  app.track?.(trackName, params)
}

export const fnQuestionSensorTrack = (trackName: string, actionName: string) => {
  // 悬浮框问卷埋点
  track(trackName, fnGetQuestionSensorParams(actionName))
}

const fnGetQuestionSensorParams = (actionName: string) => {
  // 悬浮框问卷埋点参数
  const userInfo = app.getState()['@common']?.me_info ?? {}
  return {
    actionName,
    triggerTime: moment().valueOf(),
    corpId: userInfo?.staff?.corporationId?.id,
    staffId: userInfo?.staff?.id
  }
}

export const fnAddReimbursementSensorTrack = (trackName: string) => {
  // “+号”-“发起报销”埋点
  track(trackName, fnGetAddReimbursementSensorParams())
}

const fnGetAddReimbursementSensorParams = () => {
  // “+号”-“发起报销”埋点参数
  const userInfo = app.getState()['@common']?.me_info ?? {}
  return {
    enterprise_id: userInfo?.staff?.corporationId?.id
  }
}
