/**
 *  Created by pw on 2022/12/7 12:18 AM.
 */
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'

export function handleBillClickItem(dataProps: any = {}) {
  const { id, formType, state, isAlwaysPrint, data } = dataProps
  const params = { id, formType, state, isAlwaysPrint }

  const configsList = get(data, 'form.specification.configs')
  const loanMoney = get(data, 'form.amount.standard')
  const stateLoan = get(data, 'flow.state')
  const getId = get(data, 'id')
  const isLoan =
    configsList &&
    configsList.find((item: { ability: string }) => {
      return item.ability === 'loan'
    })
  api.invokeService('@bill:update:flow:append:info', { needConfigButton: true })
  if (isLoan && loanMoney > 0 && (stateLoan === 'paid' || stateLoan === 'archived')) {
    api
      .invokeService('@bill:get:loanpackage:by:flowId', getId)
      .then(() => {
        api.invokeService('@home:save:specification').then(() => {
          api.invokeService('@home:click:bill', params, 'homePage')
        })
      })
      .catch((err: any) => {
        api.invokeService('@home:save:specification').then(() => {
          api.invokeService('@home:click:bill', params, 'homePage')
        })
      })
  } else {
    api.invokeService('@home:save:specification').then(() => {
      api.invokeService('@home:click:bill', params, 'homePage')
    })
  }
}

export default { handleBillClickItem }
