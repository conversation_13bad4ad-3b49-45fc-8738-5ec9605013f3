import React, { FC, ReactNode } from 'react'
// @ts-ignore
import { Skeleton } from '@ekuaibao/eui-mobile'
import {
  SkeletonIconNav,
  SkeletonNormalList,
  SkeletonPhotoText,
  SkeletonParagraph,
  SkeletonTitle
} from '@hose/eui-mobile'
import styles from './SkeletonList.module.less'
interface Props {
  length: number
  showImage?: boolean
  style?: any
}

export const SkeletonList: FC<Props> = props => {
  const skeletonArr: ReactNode[] = []
  let style
  for (let i = 0; i < props.length; i++) {
    style = i === 0 ? { marginTop: 0 } : {}
    skeletonArr.push(
      <div key={i} className="skeleton-wrap" style={style}>
        <Skeleton avatar={true} title={true} paragraph={{ row: 1 }} />
      </div>
    )
  }
  return <>{skeletonArr}</>
}
export const SkeletonListEUI: FC<Props> = props => {
  const skeletonArr: ReactNode[] = []
  const { length = 4, style = {} } = props
  for (let i = 0; i < length; i++) {
    skeletonArr.push(
      <div key={i} className="skeletonItemEUI">
        <SkeletonTitle animated />
        <SkeletonParagraph animated />
      </div>
    )
  }
  return (
    <div className={styles.skeletonListWrapperEUI} style={style}>
      {skeletonArr}
    </div>
  )
}

export const HomeSkeletonList = () => {
  return (
    <div className={styles.skeletonListWrapper}>
      <div className={styles.skeletonItem}>
        <SkeletonIconNav showTitle />
      </div>
      <div className={styles.skeletonItem}>
        <SkeletonPhotoText showTitle showImage />
      </div>
      <div className={styles.skeletonItem}>
        <SkeletonNormalList showAction showImage showText />
      </div>
    </div>
  )
}

export default SkeletonListEUI
