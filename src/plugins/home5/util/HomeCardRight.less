@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.home-card-right-wrapper {
  .font-size-3;
  .font-weight-2;
  position: relative;
  display: flex;
  align-items: center;

  .home-card-right-title {
    font-weight: 400;
    font-size:28px;
    line-height: 40px;
    margin-right: @space-1;
    color: rgba(39, 46, 59, 0.8);
  }
  .home-card-list-right-icon {
    color: var(--eui-icon-n1);
    cursor: pointer;
  }

  .home-card-right-number {
    font-weight: 400;
    font-size: 28px;
    line-height: 40px;
    display: flex;
    align-items: center;
    color: var(--eui-text-title);
    background-color: var(--eui-bg-base);
    padding: 0 12px;
    text-align: center;
    height: 36px;
    min-width: 36px;
    border-radius: 36px;
  }

  .card-simple-money {
    span {
      user-select: none;
      color: @color-black-1;
      .font-size-3 !important;
      .font-weight-3;
    }
  }
  .cardSimple-running {
    padding: 0 @space-4;
    border-radius: @radius-4;
    color: @color-white-1;
    background-color: @color-inform-1;
    .font-size-2;
    .font-weight-2;
  }
}
