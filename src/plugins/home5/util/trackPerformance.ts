/**
 *  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/4/14 下午17:37.
 */

import { app } from '@ekuaibao/whispered'

export function trackHome() {
  setTimeout(() => {
    let o_performance = sessionStorage['performance']
    if (o_performance) {
      o_performance = o_performance && JSON.parse(o_performance)
      if (window.performance) {
        const {
          // @ts-ignore
          domComplete = 0,
          domInteractive = 0,
          navigationStart = 0,
          domContentLoadedEventEnd = 0,
          loadEventEnd = 0
        } = performance?.timing
        o_performance['domtree'] = domComplete - domInteractive
        o_performance['domready'] = domContentLoadedEventEnd - navigationStart
        o_performance['onload'] = loadEventEnd - navigationStart
        const me_info = app.getState()['@common']?.me_info
        const corpId = me_info?.staff?.corporationId?.id
          ? me_info?.staff?.corporationId?.id
          : me_info?.staff?.corporationId
        const staffId = me_info?.staff?.id
        const instance: any = app
        instance.track('homeperformance', {
          actionName: '首页加载渲染时长',
          corpId,
          staffId,
          ...o_performance
        })
      }
    }
  }, 3000)
}
