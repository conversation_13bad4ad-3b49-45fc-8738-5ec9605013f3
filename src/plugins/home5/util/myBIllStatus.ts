import { MyBillCardItemProps, OperatorType, PlanType } from '../elements/cards/mybill/cardList_myBill'
import { billStateMap } from '../staticUtil'
import get from 'lodash/get'
import { getV } from '../../../lib/help'
import { getDisplayName, getStaffShowByConfig } from "../../../components/utils/fnDataLinkUtil";

export function getMyBillApproveStatus(
  myBillItem: MyBillCardItemProps
): { name: string; str: string; staffAdditionInfo?: string } {
  const { flow, plan, operator, needOperatorFormPlan, useStaffName, needStaffObject } = myBillItem
  const { state } = flow
  const { type, staff } = operator
  const { label } = billStateMap()[state] || {}
  let str = label
  let name = ''
  let staffAdditionInfo = ''
  if (type === OperatorType.person && !!staff) {
    const staffInfo = getStaffInfo(staff, needStaffObject)
    name = staffInfo?.name
    staffAdditionInfo = staffInfo?.additionInfo
  }
  if (!type && useStaffName && !!staff) {
    const staffInfo = getStaffInfo(staff, needStaffObject)
    name = staffInfo?.name
    staffAdditionInfo = staffInfo?.additionInfo
  }
  if (type === OperatorType.ebot) {
    name = 'ebot'
    str = i18n.get('驳回单据')
  }
  if (type === OperatorType.system) {
    name = ''
    str = i18n.get('超时自动驳回')
  }
  const counterSign = parseCounterSign(plan, operator)
  if (typeof counterSign !== 'boolean') {
    name = ''
    str = i18n.get('会签中', { count: counterSign })
  } else if (needOperatorFormPlan) {
    const { taskId, nodes } = plan
    const currentNode = nodes?.find(node => node.id === taskId)
    const staffInfo = getStaffInfo(currentNode?.approver, needStaffObject)
    name = getDisplayName(staffInfo)
    staffAdditionInfo = staffInfo?.additionInfo
  }

  return { name, str, staffAdditionInfo }
}

const getStaffInfo = (staff: any, needObject: boolean) => {
  if (needObject) {
    return getStaffShowByConfig(staff, needObject)
  }
  return { name: getStaffShowByConfig(staff) }
}

const parseCounterSign = (plan: any, operator: any) => {
  const taskId = getV(plan, 'taskId')
  const type = getV(plan, 'type', '')
  if (type.trim().toLowerCase() === 'countersign') {
    return operator.count
  }
  if (!taskId) {
    return false
  }

  const nodes = (plan || {}).nodes
  if (!nodes || !nodes.length) {
    return false
  }

  for (const node of nodes) {
    if (node && node.id === taskId) {
      const type = (node.type || '').trim().toLowerCase()
      if (type === 'countersign') {
        if (node.counterSigners) {
          return node.counterSigners.length
        } else {
          return ''
        }
      }
    }
  }
  return false
}
