import { mapForCodeToIcon } from '../staticUtil'

/**
 * 非常大的比重数值，用于在前端为卡片做比重排序时把没配置的卡片置底
 * 所以请保证这个数值大于下面 map 中数据的 shadowWeight 的值
 */
export const VERY_HEAVY_WEIGHT = 90000
/**
 * 根据后端的卡片 id 映射对应的数据配置
 */
export const cardMap = (cardId: string) => {
  // @ts-ignore
  const map: { [key: string]: any } = {
    myLoan: {
      label: window.IS_SMG ? i18n.get('我的预支') : i18n.get('我的借款'),
      icon: mapForCodeToIcon.myLoan,
      shadowWeight: 100
    },
    myRequisition: {
      label: i18n.get('申请事项'),
      icon: mapForCodeToIcon.myRequisition,
      shadowWeight: 200
    },
    myBill: {
      label: i18n.get('我的单据'),
      icon: mapForCodeToIcon.myBill,
      shadowWeight: 300
    },
    auditPending: {
      label: i18n.get('待我审批'),
      icon: mapForCodeToIcon.auditPending,
      shadowWeight: 400
    },
    auditPayment: {
      label: i18n.get('待我支付'),
      icon: mapForCodeToIcon.auditPayment,
      shadowWeight: 500
    },
    waitSending: {
      label: i18n.get('待我寄送'),
      icon: mapForCodeToIcon.waitSending,
      shadowWeight: 600
    },
    receiveExpress: {
      label: i18n.get('待我收单'),
      icon: mapForCodeToIcon.receiveExpress,
      shadowWeight: 700
    },
    auditCarbonCopy: {
      label: i18n.get('抄送我的'),
      icon: mapForCodeToIcon.auditCarbonCopy,
      shadowWeight: 800
    },
    todoPrint: {
      label: i18n.get('待我打印'),
      icon: mapForCodeToIcon.todoPrint,
      shadowWeight: 900
    },
    privateCar: {
      label: i18n.get('用车补贴'),
      icon: mapForCodeToIcon.privateCar,
      shadowWeight: 1000
    },
    waitInvoiceDetail: {
      label: i18n.get('待开票费用'),
      icon: mapForCodeToIcon.waitInvoiceDetail,
      shadowWeight: 700
    },
    recordExpends: {
      label: i18n.get('随手记'),
      icon: mapForCodeToIcon.recordExpends,
      shadowWeight: 1100
    },
    approvePermission: {
      label: i18n.get('协同审批'),
      icon: mapForCodeToIcon.approvePermission,
      shadowWeight: 1302
    },
    orderConfirm: {
      label: i18n.get('订单确认'),
      icon: mapForCodeToIcon.orderConfirm,
      shadowWeight: 1302
    },
    approved: {
      label: i18n.get('已审批'),
      icon: mapForCodeToIcon.orderConfirm,
      shadowWeight: 1302
    },
    paymentHistory: {
      label: i18n.get('还款记录'),
      icon: mapForCodeToIcon.orderConfirm,
      shadowWeight: 1302
    },
    completed: {
      label: i18n.get('已完成'),
      icon: mapForCodeToIcon.orderConfirm,
      shadowWeight: 1302
    }
  }

  return map[cardId] || {}
}
