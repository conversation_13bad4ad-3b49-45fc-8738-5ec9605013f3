import { mapForCodeToPath } from '../staticUtil'
import { CodeType } from '../home5.store'
import get from 'lodash/get'
import { app } from '@ekuaibao/whispered'
import { Dialog, Toast } from '@hose/eui-mobile'
import { Fetch, Resource } from '@ekuaibao/fetch'
import { formatFeeTypes } from '../elements/e-card-expense-page'
import res from 'antd-mobile-icons/es/AaOutline'
import { goAliPay } from './ECardUtil'
const extendDetailsByPlatformId = new Resource('/api/v1/datalink/appExtend/getAppExtendFeeTypeIdsByPlatformId')
const controlResource = new Resource('/api/ecard/control/v1')
const datalink = new Resource('/api/v2/datalink')

export function jumpRouter(data: any) {
  if (data?.actions?.[0]?.url) {
    const url = encodeURIComponent(data?.actions?.[0]?.url)
    const link = `${location.origin}/api/authorization/v1/redirectWithCode?redirectUrl=${url}&corpId=${Fetch.ekbCorpId}&accessToken=${Fetch.accessToken}`
    let iframe = true
    if (window.isWxWork || window.isWebchat) {
      iframe = false
    }
    return app.invokeService('@layout:open:link', link, false, false, iframe)
  }
  switch (data.code) {
    case 'privateCar':
      fnClickMyCarBusiness(data)
      break
    case 'auditPending':
    case 'auditPayment':
    case 'waitSending':
    case 'receiveExpress':
    case 'auditCarbonCopy':
    case 'myBill':
    case 'approvePermission':
      app
        .invokeService('@approve:set:select:billsType', '')
        .then(() => app.go(`/${mapForCodeToPath[data.code]}`, false))
      break
    case CodeType.dataLinkEntity:
      const { id, detail } = data || {}
      const dataLinkEntity = get(detail, 'dataLinkEntity')
      if (!id || !dataLinkEntity) {
        return
      }
      const groupType = get(dataLinkEntity, 'platformId.groupType', '')
      const type = get(dataLinkEntity, 'platformId.type', '')
      app
        .require<any>('@importDataLink/utils')
        .call()
        .then(({ fnAikeIsSavePhone }: any) => {
          fnAikeIsSavePhone({ groupType, type }).then((res: any) => {
            if (res.isOpenNext) {
              const fields = dataLinkEntity.fields
              const obj: any = fields.find((line: any) => line.name.endsWith('_name'))
              const key = obj && obj.name
              const codeName = get(
                fields.find((item: any) => item.name.endsWith('_code')),
                'label'
              )
              const placeholder = codeName ? `${i18n.get('请输入名称或')}${codeName}` : i18n.get('搜索')
              const selectedKey = type === 'TRAVEL_MANAGEMENT' ? 'notBuy' : 'all'
              app.go(
                `/mine/dataLinkList/${id}/${key}/${encodeURIComponent(placeholder)}/${selectedKey}/${type}/${true}`,
                false
              )
            }
          })
        })
      app.invokeService('@common:set:track', {
        key: 'mytrips_all_view',
        actionName: '行程集合页pv',
        from: 'simple'
      })
      break
    case 'recordExpends':
      app.go('/record-expends', false)
      break
    case 'quickExpense':
      app.go('/quick-expense', false)
      break
    case CodeType.approve:
      app.store.dispatch('@home5/redirectApproveOA')()
      break
    case CodeType.completed: // 已完成
      app.go('/bills-segment/Archived')
      break
    case CodeType.approved: // 已审批
      app.go('/mine/approved')
      break
    case CodeType.paymentHistory: //  还款记录
      app.go('/mine/loan')
      break
    case 'customMessage':
      app.go(`/message-center/${data.code}`, false)
      break
    case CodeType.eCard:
      goToECardPage()
      break
    case 'allTodo': // 全部待办
      app.go('/allTodo')
      break
    default:
      app.go(`/${mapForCodeToPath[data.code]}`, false)
  }
}

// 私车公用跳转逻辑
export function fnClickMyCarBusiness(data: any) {
  const myCarBusinessPower = app.getState()['@mycarbusiness'].myCarBusinessPower
  const privateData = get(data, 'detail.data')

  const { isFrist, state, hasRecord, haveStartPower, message } = privateData || myCarBusinessPower
  if (isFrist) {
    // 说明是第一次进来
    // 然后可以在公告页面拿note进行展示
    app.go('/routenote')
  } else if (state === 'RUNNING' || state === 'END') {
    const wayPointsConfig = privateData?.wayPointsConfig || myCarBusinessPower?.wayPointsConfig
    if (haveStartPower !== undefined && !haveStartPower && !wayPointsConfig) {
      Dialog.alert({ title: i18n.get('提示'), content: message })
      return
    }
    // 未生成明细的的，去进行行程记录调整
    app.go('recordingtrip')
  } else {
    // 去记录列表(要判断能否产生新的记录 haveStartPower)
    if (!hasRecord) {
      return app.go('/recordingtrip')
    }
    app.go('routelist')
  }
}

export function getCountStr(code: string, count: number): string {
  if (code === 'waitInvoiceDetail' || count === 0) {
    // 因为待开票费用统计的是明细，范围的count是单据的个数，所以不显示个数
    return ''
  }
  return count > 99 ? '99+' : `${count}`
}

export const goToECardPage = async () => {
  const ownerId = app.getState()['@common'].me_info?.staff?.id
  const { value } = await app.invokeService('@common:check:ecard:state', { ownerId })
  if (!['NORMAL', 'ACTIVATING'].includes(value.state)) {
    Dialog.alert({
      title: i18n.get('温馨提示'),
      content: i18n.get(value.msg || '您还没有开通易商卡权限,请联系管理员开通权限。'),
      confirmText: i18n.get('知道了')
    })
  } else {
    app.go(`/e-card-expense-page`)
  }
}

export const selectECardFeetype = (requisitionId?: string, setQrDisable?: any) => {
  setQrDisable?.(true)
  const join = {
    join: `platformId,platformId,/v2/datalink/platform`,
    join$1: 'parentId,parentId,/v2/datalink/entity'
  }
  datalink.GET('/entity/autoExpense/getEBussCardEntityList', { ...join }).then(res => {
    const data = res?.items || []
    const entityData = data.filter((value: any) => value?.active)
    const platformId = entityData?.[0]?.platformId?.id
    extendDetailsByPlatformId.GET('', { platformId }).then(result => {
      const syncFeeTypeIds = result?.items || []
      app
        .dataLoader('@common.feetypes')
        .load()
        .then((res: any) => {
          const feeTypes = syncFeeTypeIds.length === 0 ? res.data : formatFeeTypes(res.map, syncFeeTypeIds)
          setQrDisable?.(false)
          app.open('@feetype:SelectFeeTypeModal', {
            feetype: feeTypes,
            currentFeeType: undefined,
            updateCurrentFeeType: (feeType: any) => updateCurrentFeeType(feeType, requisitionId)
          })
        })
    })
  })
}
export const selectFeetypeAliPay = async (id: string, pageType: 1 | 2) => {
  const feetypes = await app.dataLoader('@common.feetypes').load()
  const feetype = feetypes.data
  app.open('@feetype:SelectFeeTypeModal', { feetype, needRecommend: true }).then((feeType: any) => {
    console.log('==========>feeType', feeType)
    aliPayScanOrQrcode(id, pageType, feeType)
  })
}
export const aliPayScanOrQrcode = (id: string, pageType: 1 | 2, feeType?: any) => {
  // 1 展码付 2 扫一扫
  Fetch.POST('/api/engine/hdt/api/v1/alipay/enterprise/getCorpCodePaPage', null, {
    body: { id, pageType }
  }).then(res => {
    const url = res?.data
    if (url) {
      goAliPay(url)
    } else {
      Toast.show({
        content: res?.errMessage || i18n.get('获取失败，请稍后重试'),
        icon: 'fail'
      })
    }
  })
}

const updateCurrentFeeType = (feeType: any, requisitionId?: string) => {
  return new Promise((resolve, reject) => {
    controlResource
      .GET('/feeIncField', { feeTypeId: feeType.id })
      .then(() => {
        resolve('')
        if (requisitionId) {
          app.go(`/e-card-expense-page/${feeType.id}?requisitionId=${requisitionId}`)
        } else {
          app.go(`/e-card-expense-page/${feeType.id}`)
        }
      })
      .catch(e => {
        Dialog.alert({ content: e.errorMessage })
        reject()
      })
  })
}
