/**
 *  Created by pw on 2022/11/2 5:18 PM.
 */
export enum CodeType {
  nu = '',
  myBill = 'myBill',
  myLoan = 'myLoan',
  myRequisition = 'myRequisition',
  auditPending = 'auditPending',
  auditPayment = 'auditPayment',
  waitSending = 'waitSending',
  receiveExpress = 'receiveExpress',
  auditCarbonCopy = 'auditCarbonCopy',
  todoPrint = 'todoPrint',
  privateCar = 'privateCar',
  recordExpends = 'recordExpends',
  authorizationManage = 'authorizationManage',
  dataLinkEntity = 'dataLinkEntity',
  approve = 'approve', // charge: OA审批
  mall = 'MALL',
  completed = 'completed', // 已完成
  approved = 'approved', // 已审批
  paymentHistory = 'paymentHistory', // 还款记录
  waitInvoiceDetail = 'waitInvoiceDetail',
  approvePermission = 'approvePermission',
  orderConfirm = 'orderConfirm',
  eCard2 = 'eCard2',
  allTodo = 'allTodo'
}

export const noNeedSummaryCards = [
  CodeType.dataLinkEntity,
  CodeType.completed,
  CodeType.approved,
]
