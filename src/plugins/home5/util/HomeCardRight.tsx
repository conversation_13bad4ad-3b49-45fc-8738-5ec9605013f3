import React from 'react'
import './HomeCardRight.less'
import { getCountStr } from './SimpleCardUtil'
import { app } from '@ekuaibao/whispered'
import { T } from '@ekuaibao/i18n'
import { ICard } from '../homePage/HomeCardManage/Types'
import { OutlinedDirectionRight, OutlinedTipsAdd } from '@hose/eui-icons'
const Money = app.require<any>('@elements/puppet/Money')

interface Props {
  label?: string
  card?: ICard
  hasRight?: boolean
  onClick?: () => void
  isAdd?: boolean
}

const HomeCardRightPart = function(props: Props) {
  const { label, card, hasRight, onClick, isAdd } = props
  const { detail, code } = card || {}
  const handleOnClick = () => {
    if (onClick) {
      onClick()
    }
  }

  if (label?.length) {
    return (
      <div className="home-card-right-wrapper" onClick={handleOnClick}>
        <div className="home-card-right-title">
          <T name={label} />
        </div>
        {hasRight && isAdd && <OutlinedTipsAdd fontSize={16} className="home-card-list-right-icon" />}
        {hasRight && !isAdd && <OutlinedDirectionRight fontSize={16} className="home-card-list-right-icon" />}
      </div>
    )
  }

  if (!detail) {
    return null
  }

  if (code === 'privateCar') {
    const state = detail?.data?.state
    if (state === 'RUNNING') {
      return (
        <div className="home-card-right-wrapper" onClick={handleOnClick}>
          <div className="cardSimple-running">{i18n.get('行程中')}</div>
        </div>
      )
    }
  }

  const { type, value } = detail.prompt || {}
  if (type === 'NUMBER') {
    const count = getCountStr(code, value)
    return (
      <div className="home-card-right-wrapper" onClick={handleOnClick}>
        {count && <div className="home-card-right-number">{getCountStr(code, value)}</div>}
        <OutlinedDirectionRight className="home-card-list-right-icon" fontSize={16} />
      </div>
    )
  } else if (type === 'MONEY') {
    return (
      <div className="home-card-right-wrapper" onClick={handleOnClick}>
        <Money value={value} showShorthand={true} className="cardSimple-money" />
      </div>
    )
  } else if (detail.hasOwnProperty('dataLinkCount')) {
    return (
      <div className="home-card-right-wrapper" onClick={handleOnClick}>
        {!!(detail.dataLinkCount && detail.dataLinkCount !== '0') && (
          <div className="home-card-right-number">{detail.dataLinkCount}</div>
        )}
        <OutlinedDirectionRight className="home-card-list-right-icon" fontSize={16} />
      </div>
    )
  }

  return (
    <div className="home-card-right-wrapper" onClick={handleOnClick}>
      <span>{value}</span>
    </div>
  )
}

export default HomeCardRightPart
