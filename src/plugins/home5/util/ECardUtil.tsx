import { app } from '@ekuaibao/whispered'
import { Toast, ImageUploadItem } from '@hose/eui-mobile'
import moment from 'moment'
import Compressor from 'compressorjs'
import { Fetch, Resource } from '@ekuaibao/fetch'
import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types'
import { addMapJsApi, addMarkerFGeo, getGeolocation, getMapObj } from '../../../lib/mapjsapi'
import { eCardExpenseLocation } from '../../../lib/dataflux/mapLogEvent'
import uploadFile, { uploaderMinio } from '../../basic-elements/signature/uploadFile'
import { FeeType, RequisitionType } from '../type'
import { parseShowValue2SaveValue } from '../../../lib/formatDetail'
import { logEvent } from '../../../lib/dataflux'
import { isFunction } from '@ekuaibao/helpers'
import { setValidateError } from '../../feetype/feetype.action'
const datalink = new Resource('/api/v2/datalink')
const extendDetailsByPlatformId = new Resource('/api/v1/datalink/appExtend/getAppExtendFeeTypeIdsByPlatformId')
import ALIPAYICON from '../images/alipay.png'
import PINGANICON from '../images/pingan.png'
const { getValidateErrorByShow } = app.require('@bill/utils/billUtils') as any
const mapId = 'map-container-expense-page'
import { alert } from '../../../lib/util'
import { Platform } from '../staticUtil'
import qs from 'qs'
import { getControlBehavior, ControlBehaviorProps } from '@hose/control'

export const getCurrentAddress = () => {
  return new Promise((resolve, reject) => {
    addMapJsApi().then(() => {
      const map = getMapObj(mapId)
      const geolocation = getGeolocation(map)
      addMarkerFGeo(geolocation)
        .then(res => {
          let addr = ''
          if (res.info === 'OK') {
            const { city, province, district, street, streetNumber } = res?.regeocode?.addressComponent || {}
            addr = `${province}${city}${district}${street}${streetNumber}`
          }
          resolve({ mapInfo: res, addr })
        })
        .catch(error => {
          eCardExpenseLocation({ error })
          reject(error)
        })
    })
  })
}

const fileToImg = (file: File) => {
  return new Promise(resolve => {
    let reader = new FileReader()
    reader.addEventListener('load', () => {
      let img = new Image()
      img.src = reader.result as string
      img.addEventListener('load', () => resolve(img))
    })
    reader.readAsDataURL(file)
  })
}

// 压缩图片方法 （中间件）
const compressor = (file: File, defaultQuality = 0.3) => {
  return new Promise(resolve => {
    new Compressor(file, {
      quality: defaultQuality,
      success(result) {
        console.log('压缩后: ', (result.size / 1024 / 1024).toFixed(1), 'M')
        resolve(result)
      },
      error(err) {
        // 压缩报错的话 返回原图片
        resolve(file)
      }
    })
  })
}

const lrzFile = async (origin: File) => {
  const file = await compressor(origin)
  return file
}

const imgToCanvas = (img: CanvasImageSource) => {
  let canvas = document.createElement('canvas')
  // @ts-ignore
  canvas.width = img.width
  // @ts-ignore
  canvas.height = img.height
  let ctx = canvas.getContext('2d')
  ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
  return canvas
}

const watermark = (canvas: HTMLCanvasElement, text: string[]) => {
  const font = 10 * (canvas.width / 320) * 1.5 + 'px'
  const mTop = 10 * (canvas.width / 320) * 1.5
  return new Promise(resolve => {
    const ctx = canvas.getContext('2d')
    // 设置填充字号和字体，样式
    ctx.font = `${font} 宋体`
    ctx.fillStyle = '#FFFFFF'
    // 设置右对齐
    ctx.textAlign = 'left'
    // 在指定位置绘制文字，这里指定距离右上角20坐标的地方
    text.forEach((item, index) => {
      if (item) {
        ctx.fillText(item, 20, canvas.height - mTop * index++)
      }
    })
    resolve(canvas)
  })
}

const fn2Blob = (canvas: HTMLCanvasElement) => {
  return new Promise(resolve => {
    canvas.toBlob(resolve)
  })
}

export const fnHandleUpload = async ({ setUploadPhoto, address }: any, file?: File) => {
  setUploadPhoto(true)
  const lrzPhoto: any = await lrzFile(file)
  const img = await fileToImg(lrzPhoto)
  const canvas = imgToCanvas(img as CanvasImageSource)
  const text = [address, moment().format(`YYYY-MM-DD HH:mm:ss`), '合思-易商卡']
  const canvasWaterMark = (await watermark(canvas, text)) as HTMLCanvasElement
  return fn2Blob(canvasWaterMark).then(async (blob: File) => {
    const res = window.IS_STANDALONE ? await uploaderMinio(blob) : await uploadFile(blob, file.name)
    const result = res?.items?.[0]
    setUploadPhoto(false)
    if (result && result.error) {
      Toast.show(i18n.get('上传失败！'))
      return null
    } else {
      return { url: result.url, thumbnailUrl: result.thumbUrl, key: result.key }
    }
  })
}

export const fnHandleBefore = (file: File) => {
  if (!/^(.*)\.(png|jpg|jpeg)$/i.test(file.name)) {
    Toast.show('请选择格式为 png、jpg、jpeg 的图片')
    return null
  }
  if (file.size > 1024 * 1024 * 20) {
    Toast.show('请选择小于 20M 的图片')
    return null
  }
  if (file.size === 0) {
    return null
  }
  return file
}

export const fnGetFeeTypeByECardConfig = async () => {
  const join = {
    join: `platformId,platformId,/v2/datalink/platform`,
    join$1: 'parentId,parentId,/v2/datalink/entity'
  }
  const datalinkRes = await datalink.GET('/entity/autoExpense/getEBussCardEntityList', { ...join })
  const data = datalinkRes?.items || []
  const entityData = data.filter((value: any) => value?.active)
  const platformId = entityData?.[0]?.platformId?.id
  const result = await extendDetailsByPlatformId.GET('', { platformId })
  return result?.items || []
}

export const fnGetFinishParams = ({ res, deviceId, address, staff, mapInfo }: any) => {
  const values: {
    requisitionValue: RequisitionType
    feeValue: FeeType
    fileList: ImageUploadItem[]
    [key: string]: any
  } = res[0]
  const {
    feeValue,
    requisitionValue: { id: requisitionInfoId, name: requisitionInfoName } = {},
    fileList = [],
    eCardAmount,
    ...others
  } = values
  const extend: Record<string, any> = {}
  const baseDataPropertiesMap = app.getState('@common.baseDataProperties.baseDataPropertiesMap') || {}
  Object.keys(others).forEach(key => {
    const value = others[key]
    const field = baseDataPropertiesMap[key] as GlobalFieldIF
    if (field.dataType.type === 'date') {
      extend[key] = moment(value[0]).valueOf()
    } else if (field.dataType.type === 'dateRange') {
      const start = moment(value[0]).valueOf()
      const end = moment(value[1]).valueOf()
      extend[key] = { start, end }
    } else {
      extend[key] = value
    }
  })
  const feeTypeFormValue = res[1].feeTypeForm
  const feeTypeForm = {
    feeTypeForm: parseShowValue2SaveValue(feeTypeFormValue),
    feeTypeId: res[1].feeTypeId.id,
    specificationId: res[1].specificationId.id
  }
  const feeTypeName = res[1].feeTypeId.name
  const fileKey = fileList.map(it => it.key)
  const addressComponent = {
    ...mapInfo?.regeocode?.addressComponent,
    lat: mapInfo?.position?.lat,
    lng: mapInfo?.position?.lng
  }
  const params = {
    requisitionInfoId,
    feeTypeForm,
    feeTypeName,
    requisitionInfoName,
    fileKey,
    deviceId,
    extend,
    location: address,
    addressComponent,
    amount: {
      standard: eCardAmount,
      standardStrCode: 'CNY',
      standardNumCode: '156',
      standardSymbol: '¥',
      standardUnit: '元',
      standardScale: 2
    }
  }
  logEvent('e-card-initiate-payment', {
    staffName: staff?.name,
    company: staff?.corporationId?.name,
    CostName: feeTypeName,
    ConsumptionAmount: eCardAmount,
    ApplicationItemName: requisitionInfoName,
    PaymentTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
  })
  return params
}

export const handleSaveECardExpense = (formBus: any) => {
  return new Promise((resolve, reject) => {
    formBus &&
      isFunction(formBus.getValueWithValidate) &&
      formBus
        .getValueWithValidate(1)
        .catch((e: any) => {
          formBus.invoke('get:feetype:components').then((components: any) => {
            let arr = getValidateErrorByShow(components, Object.keys(e))
            if (!arr.length) {
              setValidateError({ detail: Object.keys(e) })
            }
            reject(e)
          })
        })
        .then((res: any) => {
          const value = formBus.$extraParseDetail(res)
          resolve(value)
        })
  })
}

export function sendData(data: any) {
  // RN升级后调用的方法，兼容以前没升级的用户保留以前的方法
  if (!!window.ReactNativeWebView && window.ReactNativeWebView !== 'undefined') {
    window.ReactNativeWebView.postMessage(JSON.stringify(data), '*')
  } else {
    window.postMessage(JSON.stringify(data), '*')
  }
}

export const PayTypeMap: any = {
  PA_TOKEN: { icon: PINGANICON, title: '平安银行', key: 'PA_TOKEN' },
  ALIPAY: { icon: ALIPAYICON, title: '支付宝', key: 'ALIPAY' }
}

export function goAliPay(url: string) {
  if (window.isWxWork) {
    alert(i18n.get('当前平台暂不支持此功能'), i18n.get('提示'))
    return
  }
  const link = `https://render.alipay.com/p/s/i?${url}`
  const { eCardOpenPlatform = '' } = qs.parse(window.location.search.slice(1))
  if ((eCardOpenPlatform === Platform.app || window.__PLANTFORM__ === 'APP') && !window.isPC) {
    app.invokeService('@layout:open:view:szkd', { url: link })
  } else {
    app.invokeService('@layout:open:link', link, false, true, false)
  }
}

export const fnGetControlBehavior = async (staff: any, amount: any) => {
  const data: ControlBehaviorProps = {
    corpId: staff?.corporationId?.id,
    staffId: staff?.id,
    accessToken: Fetch?.accessToken,
    context: {
      amount
    }
  }
  const result: any = await getControlBehavior(data)
  return result?.data
}
