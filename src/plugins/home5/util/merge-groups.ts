/***************************************************
 * Created by nanyuantingfeng on 2020/11/5 16:01. *
 ***************************************************/
/**
 * 将列表中带有相同 pid 字段的元素组成一个 group 元素放入列表，并沿用第一个元素的 weight 属性
 * @param list
 */
export function mergeGroups<T>(list: T[]): T[] {
  const groupIds: any = {}
  const result: T[] = []
  for (const item of list) {
    if (item.type === 'GROUP') {
      if (groupIds[item.id]) {
        item.cards = groupIds[item.id]
      }
      groupIds[item.id] = item
      result.push(item)
    } else if (item.pid) {
      if (groupIds[item.pid]) {
        if (groupIds[item.pid].id) {
          groupIds[item.pid].cards = [].concat(groupIds[item.pid].cards || []).concat(item)
        } else {
          groupIds[item.pid] = [].concat(groupIds[item.pid] || []).concat(item)
        }
      } else {
        groupIds[item.pid] = [item]
      }
    } else {
      result.push(item)
    }
  }

  return result.sort((a, b) => a.weight - b.weight)
}
