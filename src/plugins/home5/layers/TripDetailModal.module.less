@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '../../../styles/theme.less';

.trip-detail-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: @fill-body;
  :global {
    .top-view {
      background: @color-white-1;
      padding: @space-7;
      display: flex;
      flex-direction: column;
      .title {
        text-align: left;
        .font-weight-3;
        .font-size-2;
        color: @color-black-1;
        margin-bottom: @space-6;
      }
      .info {
        display: flex;
        flex-direction: row;
        margin-top: @space-7;
        .info-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          .item-label {
            .font-weight-2;
            .font-size-2;
            color: @color-black-3;
          }
          .item-value {
            .font-weight-2;
            .font-size-3;
            white-space: nowrap;
            margin-top: @space-4;
            color: @color-black-1;
          }
          &:first-child {
            .item-value {
              .font-weight-3;
            }
          }
        }
      }
    }
    .bottom-view {
      background: @color-white-1;
      display: flex;
      flex-direction: column;
      margin-top: @space-5;
      padding: @space-7;
      align-items: flex-start;
      .title {
        .font-weight-2;
        .font-size-2;
        color: @color-black-3;
      }
      .content-top {
        margin-top: @space-6;
        margin-bottom: @space-2;
        color: @color-black-1;
        .font-weight-3;
        .font-size-3;
      }
      .content {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        .font-weight-2;
        .font-size-2;
        color: @color-black-2;
        .content-item {
          margin-top: @space-2;
        }
      }
    }
  }
}
