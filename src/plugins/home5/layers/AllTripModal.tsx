import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager-mobile'
import { planeAndTrainCard, hotelCard } from '../elements/cards/TripCard'
import styles from './AllTripModal.module.less'
// import { TripDate } from '../../bill/elements/datalink/TripView'
import { connect } from '@ekuaibao/mobx-store'
import { app as api } from '@ekuaibao/whispered'
const TripDate = api.require("@bill/elements/datalink/TripView")
import { getTripCardTitle } from '../staticUtil'
import moment from 'moment'
// import { getWeek } from '../../bill/elements/datalink/dataLinkUtil'
const { getWeek } = api.require("@bill/elements/datalink/dataLinkUtil")
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import { Fetch } from '@ekuaibao/fetch'

const hotelCardView = (trip: any, type: string) => {
  const isHotel = type === 'HOTEL'
  const isFood = type === 'FOOD'
  return (
    <>
      <div className="card-header-hotel">
        <span>{getTripCardTitle(trip, true).left}</span>
        {hotelCard(trip)}
      </div>
      <TripDate
        isFood={isFood}
        disable={true}
        isHotel={isHotel}
        startDate={trip[Object.keys(trip).find(o => !!o.endsWith('入住日期'))]} // @i18n-ignore
        endDate={trip[Object.keys(trip).find(o => !!o.endsWith('离店日期'))]} // @i18n-ignore
        style={{ marginTop: 12 }}
      />
    </>
  )
}
const otherCardView = (type: string, trip: any) => {
  return (
    <>
      <div className="card-header">
        <span>{getTripCardTitle(trip, false).left}</span>
        <span>{getTripCardTitle(trip, false).right}</span>
      </div>
      {planeAndTrainCard(type, trip, true)}
    </>
  )
}
export const tripCard = (type: string, trip: any, i?: number, onClick?: any) => {
  return (
    <div className={styles['trip-card-item']} key={`item${i}`} onClick={() => onClick && onClick(type, trip)}>
      {type === 'HOTEL' || type === 'FOOD' ? hotelCardView(trip, type) : otherCardView(type, trip)}
    </div>
  )
}

interface Props {
  tripType: any
  trips: any
  layer: any
}

interface States {}

@connect(store => ({
  trips: store.states['@home5'].trips,
  tripType: store.states['@home5'].tripType
}))
@EnhanceTitleHook(i18n.get('我的行程'))
export default class AllTripModal extends Component<Props, States> {
  constructor(props: Props) {
    super(props)
  }
  componentDidMount() {
    api.store.dispatch('@home5/getMyTrips')(9999)
  }
  handleCancel = () => {
    this.props.layer.emitOk('cancel')
  }
  componentWillUnmount() {
    api.invokeService(
      '@layout:change:header:title',
      i18n.get(`易快报@@${Fetch.ekbCorpId}`, null, () => i18n.get('易快报'))
    )
  }
  handleOk = () => {
    this.props.layer.emitOk('')
  }
  renderTripGroup = (date: string, list: any, tripType: any, index?: number) => {
    return (
      <div key={`group${index}`}>
        <div className="date-group">{date}</div>
        {list &&
          list.map((trip: any, i: number) => {
            const type = tripType[trip.dataLink.entityId]
            return tripCard(type, trip.dataLink, i, this.handleOnClick)
          })}
      </div>
    )
  }
  handleOnClick = (type: string, value: any) => {
    api.open('@home5:TripDetailModal', { value, type })
  }

  getGroup = (trips: any[]) => {
    const tripsMap: any = {}
    trips.forEach(item => {
      const value = item.dataLink
      const startDate =
        value[Object.keys(value).find(o => !!o.endsWith('出发时间'))] || // @i18n-ignore
        value[Object.keys(value).find(o => !!o.endsWith('入住日期'))] // @i18n-ignore
      const date = `${moment(startDate).format('YYYY年MM月DD日')} ${getWeek(startDate)}`
      if (tripsMap[date]) {
        tripsMap[date].push(item)
      } else {
        tripsMap[date] = [item]
      }
    })
    return tripsMap
  }

  render() {
    const { trips = [], tripType } = this.props
    if (trips.length === 0 || !tripType) {
      return null
    }
    const tripGroup = this.getGroup(trips)
    return (
      <div className={styles['all-trip-wrapper']}>
        <div className="title">{i18n.get('我的行程')}</div>
        {Object.keys(tripGroup).map((date, index) => {
          return this.renderTripGroup(date, tripGroup[date], tripType, index)
        })}
      </div>
    )
  }
}
