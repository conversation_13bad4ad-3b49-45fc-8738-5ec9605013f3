import React, { useEffect } from 'react'
import styles from './full-screen-modal.module.less'
import { app } from '@ekuaibao/whispered'

export interface FullScreenModalProps {
  renderChildren: (closeHandler: () => void) => React.ReactNode
  viewMode: 'landscape' | 'portrait',
  /**
   * 全屏时，点击返回按钮的回调
  */
  onGoBack?: () => void  // 按道理这个监听应该是业务的逻辑
}

function getSafeAreaInsetBottom() {
  const tempElement = document.createElement('div')
  tempElement.style.cssText =
    'position: absolute; top: 0; left: 0; width: 0; height: 0; padding-bottom: env(safe-area-inset-bottom);'
  document.body.appendChild(tempElement)
  const safeAreaInsetBottom = parseFloat(getComputedStyle(tempElement).paddingBottom)
  document.body.removeChild(tempElement)
  return safeAreaInsetBottom
}

const FullScreenModal = ({ renderChildren, viewMode = 'portrait', layer, onGoBack }: FullScreenModalProps & { layer: any }) => {
  const currentSafeAreaValue = getSafeAreaInsetBottom()
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight - currentSafeAreaValue
  const isLandScape = viewMode === 'landscape'
  const offset = windowHeight - windowWidth

  const translateOffset = {
    transform: `translate(${isLandScape ? -offset / 2 : 0}px, ${isLandScape ? offset / 2 : 0}px) rotate(${
      isLandScape ? 90 : 0
    }deg)`
  }

  const handleClose = () => {
    layer?.emitCancel()
  }

  useEffect(() => {
    const _backControlId = Math.random().toString()
    app.backControl.create(_backControlId, () => {
      handleClose()
      onGoBack?.()
    })

    return () => {
      app.backControl.remove(_backControlId)
    }
  }, [])

  return (
    <div>
      <div
        style={{
          width: isLandScape ? windowHeight : windowWidth,
          height: isLandScape ? windowWidth : windowHeight,
          ...translateOffset
        }}
        className={styles['full-screen']}
      >
        {renderChildren(handleClose)}
      </div>
    </div>
  )
}

export default FullScreenModal
