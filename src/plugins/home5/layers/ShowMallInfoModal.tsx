import React, { Component } from 'react'
import styles from './ShowMallInfoModal.module.less'
import { Button } from 'antd-mobile'
import { app as api } from '@ekuaibao/whispered'
const url = "https://mall-app.ekuaibao.com/wportal/sanke/noticeEntryStatic"
export default class ShowMallInfoModal extends Component<any, any> {
    render() {
        return <div className={styles.showMall}>
            <div className="header">
                {i18n.get("温馨提示")}
            </div>
            <div className="content">
                {i18n.get("感谢关注，您的合思商城订购权限暂未开通，可以通过联系贵司管理员开通。")}
            </div>
            <Button type={"primary"} className="btn" onClick={() => { this.props.onOk() }}>{i18n.get("确定")}</Button>
            <Button className="btn-v" onClick={() => {
                api.invokeService('@common:set:track', {
                    key: 'mytrips_moreabtmall_click',
                    actionName: '查看申请事项点击',
                })
                api.invokeService('@layout:open:link', url)
            }}>{i18n.get("点击了解合思商城")}</Button>
        </div>
    }
}