export default [
  {
    key: 'CardEditModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      wrapClassName: 'popup-modal-border-radius'
    },
    getComponent: () => import('./cardEditModal')
  },
  {
    key: 'CustomModal',
    enhancer: 'modal',
    enhancerOptions: {
      width: 311,
      visible: true,
      transparent: true,
      animated: false,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper'
    },
    getComponent: () => import('./CustomModal')
  },
  {
    key: 'CardGroupEditModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      wrapClassName: 'popup-modal-border-radius'
    },
    getComponent: () => import('./card_group_edit_modal')
  },
  {
    key: 'Home3CreateModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'modal-popup-wrapper home3-create-modal'
    },
    getComponent: () => import('./home3-create-modal')
  },
  {
    key: 'Home5CreateModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'modal-popup-wrapper home5-create-modal'
    },
    getComponent: () => import('./home5-create-modal')
  },
  {
    key: 'StandardHome5CreateModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'modal-popup-wrapper'
    },
    getComponent: () => import('./standard-home5-create-modal')
  },
  {
    key: 'Home5PayeeInfo',
    getComponent: () => import('../elements/home5-payee-info')
  },
  {
    key: 'AllTripModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      closable: false,
      wrapClassName: 'full-screen-modal'
    },
    getComponent: () => import('./AllTripModal')
  },
  {
    key: 'TripDetailModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      closable: false,
      wrapClassName: 'full-screen-modal'
    },
    getComponent: () => import('./TripDetailModal')
  },
  {
    key: 'ShowMallInfoModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: true,
      animated: true,
      visible: true,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper',
      width: 340
    },
    getComponent: () => import('./ShowMallInfoModal')
  },
  {
    key: 'RuleSettingModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: true,
      animated: true,
      visible: true,
      closable: false,
      className: 'fix-ekb-enhance-modal-clean-wrapper',
      width: 340
    },
    getComponent: () => import('./RuleSettingModal')
  },
  {
    key: 'SelectActiveCreditRule',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'modal-popup-wrapper select-credit-rule-modal'
    },
    getComponent: () => import('./SelectActiveCreditRule')
  },
  {
    key: 'EUIPopup',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'modal-eui-style-popup-wrapper',
      style: {
        maxHeight: '80vh',
        borderRadius: '10px 10px 0 0'
      }
    },
    getComponent: () => import('./eui-popup')
  },
  {
    key: 'fullscreenModal',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      style: {
        height: '100%',
        borderRadius: '0'
      }
    },
    getComponent: () => import('./full-screen-modal')
  },
  {
    key: 'SpecificationSelector',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'modal-eui-style-popup-wrapper',
      style: {
        maxHeight: '80vh',
        borderRadius: '10px 10px 0 0'
      }
    },
    getComponent: () => import('../new-bill/home3-create-modal')
  },
]
