import React, { Component } from 'react'
import { connect } from '@ekuaibao/mobx-store'
import { Switch, Checkbox } from 'antd-mobile'
import styles from './cardEditModal.module.less'
//@ts-ignore
import SVG_SIMPLE from './images/simple.svg'
//@ts-ignore
import SVG_LIST from './images/list.svg'
//@ts-ignore
import SVG_DATABASE from './images/database.svg'
// @ts-ignore
import SVG_MSG from './images/msg.svg'
import { get } from 'lodash'
import { CodeType } from '../home5.store'
import { ICardMetaData } from '../homePage/HomeCardManage/Types'

interface Props extends StringAnyProps {
  cardConfigs: any
  data: any
  layer: any
  cardMetaData: ICardMetaData[]
}

@connect(state => ({
  cardConfigs: state.states['@home5'].cardConfigs,
  cardMetaData: state.states['@homePage'].cardConfigs
}))
export default class CardEditModal extends Component<Props, StringAnyProps> {
  showTypeMap: StringAnyProps
  constructor(props: Props) {
    super(props)
    const { data, cardConfigs = [], cardMetaData = [] } = props
    const cardMetas = cardConfigs?.length ? cardConfigs : cardMetaData
    const config = cardMetas?.find((el: StringAnyProps) => el.code === data.code)
    this.state = { config, data }
    this.showTypeMap = {
      SIMPLE: { img: SVG_SIMPLE, label: i18n.get('简版') },
      LIST: { img: SVG_LIST, label: i18n.get('列表') },
      DATABASE: { img: SVG_DATABASE, label: i18n.get('数据统计') },
      ALLDATA: { img: SVG_LIST, label: i18n.get('完整信息') }
    }
  }

  handleCancel = () => {
    this.props.layer.emitOk('cancel')
  }

  handleOk = () => {
    const { data } = this.state
    this.props.layer.emitOk(data)
  }

  componentWillUnmount() {
    this.props.layer.emitOk('cancel')
  }

  renderActionBar = (data: any) => {
    return (
      <div className="cardEditModal-actionBar">
        <div className="cardEditModal-actionBar-btn grayout" onClick={this.handleCancel}>
          {i18n.get('取消')}
        </div>
        <span className="cardEditModal-title">{i18n.get(data.label)}</span>
        <div className="cardEditModal-actionBar-btn" onClick={this.handleOk}>
          {i18n.get('完成')}
        </div>
      </div>
    )
  }

  handleClickSwitch = (value: boolean) => {
    let { data } = this.state
    data.dynamicSupportValue = value
    this.setState(data)
  }
  handleClickTravelRemindSwitch = (value: boolean) => {
    let { data } = this.state
    data.travelRemindValue = !value
    this.setState(data)
  }

  renderDynamicLine = () => {
    const { config, data } = this.state
    if (!config || !config.dynamicSupport) return null
    return (
      <div className="cardEditModal-dynamicLine-wrap">
        <div className="cardEditModal-dynamicLine">
          <span className="cardEditModal-dynamicLine-title">{i18n.get('只在有数据时展示')}</span>
          <Switch color="var(--brand-base)" checked={data.dynamicSupportValue} onChange={this.handleClickSwitch} />
        </div>
        <div className="cardEditModal-dynamicLine-tip">{i18n.get('卡片仅在有数据时展示')}</div>
      </div>
    )
  }
  renderTravelRemindLine = () => {
    const { config, data } = this.state
    if (!config || !config?.travelRemind) return null
    const type = get(data, 'detail.dataLinkEntity.platformId.type', '')
    if (config?.code === 'dataLinkEntity' && type !== 'TRAVEL_MANAGEMENT') return null
    // !data?.travelRemindValue 这个地方是为了 默认显示出现提示是开的(为了不改数据库), 所以这个地方逻辑变反了
    return (
      <div className="cardEditModal-dynamicLine-wrap" style={{ marginTop: '0.38rem' }}>
        <div className="cardEditModal-dynamicLine">
          <span className="cardEditModal-dynamicLine-title">{i18n.get('即将出行提醒')}</span>
          <Switch
            color="var(--brand-base)"
            checked={!data?.travelRemindValue}
            onChange={this.handleClickTravelRemindSwitch}
          />
        </div>
      </div>
    )
  }

  handleClickCheckBox = (value: string) => {
    let { data } = this.state
    data.showType = value
    this.setState(data)
  }

  renderShowType = () => {
    const { config, data } = this.state
    const { showType } = data
    const list = (config && config.showTypeList.slice()) || []
    let { showTypeMap } = this
    if (list.length < 2) return null
    const type = get(data, 'detail.dataLinkEntity.platformId.type', '')
    if (config?.code === 'dataLinkEntity' && type !== 'TRAVEL_MANAGEMENT') return null
    return (
      <div className="cardEditModal-showType-wrap">
        <div className="cardEditModal-showType-title">{i18n.get('展示方式')}</div>
        <div className="cardEditModal-showType-box">
          {list.map((el: any, idx: number) => {
            const type = showTypeMap[el]
            if (!type) return null
            return (
              <div className="cardEditModal-showType-item" key={idx} onClick={_ => this.handleClickCheckBox(el)}>
                <img className="cardEditModal-showType-item-img" src={type.img} />
                <span className="cardEditModal-showType-item-title">{type.label}</span>
                <Checkbox checked={el === showType} />
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  renderMallCardEditInfo = (data: any) => {
    const { code } = data
    if (code !== CodeType.mall) return null
    return (
      <div className="mall-card-edit-info">
        <img className="cardEditModal-showType-item-img" src={SVG_MSG} />
        <span>{i18n.get('抱歉～此组件暂不能被编辑')}</span>
      </div>
    )
  }

  render() {
    const { data } = this.props
    return (
      <div className={styles['cardEditModal-wrapper']}>
        {this.renderActionBar(data)}
        {this.renderDynamicLine()}
        {this.renderTravelRemindLine()}
        {this.renderShowType()}
        {this.renderMallCardEditInfo(data)}
      </div>
    )
  }
}
