@import '~@ekuaibao/eui-styles/less/token-mobile.less';

:global {
  .home3-create-modal {
    .am-modal-content {
      border-radius: 16px 16px 0 0 !important;

      .modal-popup-content {
        background-color: var(--eui-bg-base);
  
        .modal-action-bar {
          height: 88px;
          line-height: 88px;
          background-color: var(--eui-bg-base);
          
          .modal-action-bar-btn {
            font: var(--eui-font-body-r1);
            color: var(--eui-text-caption);
            line-height: 88px;
          }
        }
        .modal-action-bar-title {
          font: var(--eui-font-head-b1);
          color: var(--eui-text-title);
        }
        
        .am-modal.am-modal-popup.am-modal-popup-slide-up {
          height: 1280px;
        }
      }
    }
  }
}

.home3-creact-modal-search {
  @height: 88px;
  height: @height;
  background-color: var(--eui-bg-body);
  padding: 26px;
  margin: 24px 24px 0;
  box-shadow: 0px 4px 16px 4px rgba(29, 33, 41, 0.02), 0px 4px 8px rgba(29, 33, 41, 0.02), 0px 2px 4px -4px rgba(29, 33, 41, 0.02);
  border-radius: 8px;
  display: flex;
  align-items: center;
  z-index: 1;

  :global {
    .search-icon {
      margin-right: 16px;
      color: var(--eui-icon-n3);
    }
    .eui-input {
      font: var(--eui-font-body-r1);
      color: var(--eui-text-disabled);
    }
  }
}

.home3-re-edit-bill {
  padding: 24px 26px 0;
  box-shadow: 0px 4px 16px 4px rgba(29, 33, 41, 0.02), 0px 4px 8px rgba(29, 33, 41, 0.02), 0px 2px 4px -4px rgba(29, 33, 41, 0.02);
  border-radius: 8px;
  z-index: 1;

  :global {
    .content {
      background-color: #F7F8FF;
      display: flex;
      justify-content: space-between;
      padding: 20px 26px;
      position: relative;
      border-radius: 8px;

      .pre-edit-bg {
        position: absolute;
        left: 0;
        top: 0;
        border-radius: 8px;
      }

      .text {
        color: var(--eui-primary-pri-500);
        .warning-icon {
          margin-right: 18px;
        }

        &+div svg{
          color: var(--eui-icon-n1);
        }
      }
    }
  }
}

.home3-creact-modal-content {
  overflow: auto;
  height: 100%;
  flex: 1;
  padding-top: 12px;
  :global {
    .accordion {
      -webkit-overflow-scrolling: touch;
      padding-top: 10px;
      padding-bottom: 100px;

      .accordion-item {
        margin: 24px;
        background-color: #FFFFFF;
        box-shadow: 0px 4px 16px 4px rgba(29, 33, 41, 0.02), 0px 4px 8px rgba(29, 33, 41, 0.02), 0px 2px 4px -4px rgba(29, 33, 41, 0.02);
        border-radius: 8px;
  
        &:first-child {
          margin-top: 0;
        }
        .accordion-header {
          text-align: left;
          height: 80px;
          line-height: 80px;
          padding: 0 26px;
          position: relative;

          .lighter {
            font: var(--eui-font-body-b1);
            color: var(--eui-text-title);
          }

          .direction-icon {
            position: absolute;
            right: 30px;
            top: 0px;
            color: var(--eui-icon-n1);
          }
        }
        .accordion-content {
          overflow: hidden;
          padding: 16px 8px;
        }
      }
      
      .accordion-skeleton {
        padding: 0 24px;

        .accordion-skeletonItem{
          margin-bottom: 24px;
          padding: 32px 16px 0;
          background-color: var(--eui-bg-body);
          box-shadow: 0px 4px 16px 4px rgba(29, 33, 41, 0.02), 0px 4px 8px rgba(29, 33, 41, 0.02), 0px 2px 4px -4px rgba(29, 33, 41, 0.02);
          border-radius: 8px;

          .accordion-skeletonHang{
            display: flex;
            justify-content: space-between;

          }
        }
        
      }
    }

    .clearfix {
      width: 100%;
      height: 0;
      float: left;
    }
    .spec-item {
      width: 50%;
      float: left;
      padding: 8px;
    }
    .spec-item-wrapper {
      display: flex;
      align-items: center;
      position: relative;
      padding: 16px 0 16px 16px;
      border-radius: @radius-1;
    }
    .spec-item-wrapper:active {
      background-color: @color-bg-2;
    }
    .spec-item-icon-bg {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background-color: @color-bg-1;
      opacity: 0.1;
      align-self: flex-start;
      display: none;
    }
    .spec-item-icon {
      width: 44px;
      font-size: 32px;
      position: relative;
      top: 4px;
    }
    .spec-item-title {
      font: var(--eui-font-body-r1);
      color: var(--eui-text-caption);
      flex: 1;
      text-align: left;
      padding-left: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
    }
  }
}

.home3-creact-modal-footer {
  width: 100%;
  height: 120px;
  display: flex;
  justify-content: center;
  position: relative;
  .shadow-3;
  :global {
    .func-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      background-color: #ffffff;
      transition: .2s ease-in-out background-color;
      &:hover {
        background-color: @color-bg-1;
      }
      .func-item-icon {
        position: relative;
        margin: @space-2 0;
        .icon {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          &:first-child {
            color: @color-black-2;
          }
          &:last-child {
            color: @color-brand;
          }
        }
      }
      .func-item-name {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-title);
        white-space: nowrap;
      }
    }
  }
}
