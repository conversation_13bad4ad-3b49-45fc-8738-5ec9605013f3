@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.home5-creact-modal-search {
  @height: 64px;
  @height2: @height / 2;
  height: @height * 1.5;
  background-color: #ffffff;
  .shadow-1;
}

.home5-creact-modal-content {
  overflow: auto;
  height: 100%;
  flex: 1;
  :global {
    .am-accordion {
      -webkit-overflow-scrolling: touch;
      padding-top: 10px;
      padding-bottom: 100px;
    }
    .am-accordion-header {
      text-align: left;
      .lighter {
        .font-size-3;
        .font-weight-3;
      }
      i.arrow {
        width: @space-5 !important;
        height: @space-5 !important;
      }
    }
    .am-accordion-content-box {
      overflow: hidden;
      padding: 16px 8px;
    }
    .item-title {
      text-align: left;
      margin: 0 0 20px 30px;
      font-weight: 600;
      font-size: 24px;
      color: rgba(46, 49, 50, 0.8);
    }
    .spec-item {
      background: #F7F8FA;
      border-radius: 16px;
      padding: 0 32px 0 26px;
      display: flex;
      align-items: center;
      height: 140px;
      margin: 0 30px 16px;
      .event-item-icon {
        width: 40px;
        height: 40px;
      }
      .event-item-content {
        flex: 1;
        text-align: left;
        .event-item {
          display: flex;
          align-items: center;
          .event-item-title {
            margin-left: 16px;
            color: #4E5969;
            font-weight: 600;
            font-size: 26px;
          }
          .event-spec-item-icon {
            font-size: 32px;
          }
        }

        .event-item-desc {
          color: #A9AEB8;
          font-size: 24px;
          margin-left: 50px;
          width: 528px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .right-icon {
        color: #142234;
        opacity: 0.64;
        font-size: 24px;
      }
    }
  }
}
