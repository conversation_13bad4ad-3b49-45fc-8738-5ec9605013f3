import React, { PureComponent } from 'react'
import styles from './standard-home5-create-modal.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { SearchBar, Accordion } from 'antd-mobile'
import { debounce, isEqual, get } from 'lodash'
const EKBIcon = api.require('@elements/ekbIcon')
import StandardCardList from './../standard/elements/cards/StandardCardList'
import { standardTrack } from './../staticUtil'
import approve_not from './images/approve-not.png'

interface StateType {
  isRoute: boolean
  searchText: string
  activeKeys: string[]
  specification_group: any[]
}

@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].me_info.staff,
  specification_list: state['@home'].specificationWithVersion.specification_list,
  specification_group: state['@home'].specificationWithVersion.specification_group,
  specification_recently: state['@home'].specification_recently,
  specification_map: state['@home'].specificationWithVersion.specification_map,
  assiciationSpecifications: state['@home'].assiciationSpecifications, // 钉钉管理群模板
}))
export default class Home5CreateModal extends PureComponent<any, Partial<StateType>> {
  constructor(props: any) {
    super(props)
    this.state = {
      isRoute: props?.params?.isRoute,
      hiddenFooter: props?.params?.hiddenFooter,
      searchText: '',
      activeKeys: [],
      specification_group: [],
      assiciationSpecifications: [] // 钉钉关联群模板
    }
    
    api.invokeService('@home:get:specification:recently')
    if (!props?.params?.hiddenFooter) {
      api.dataLoader('@home.specificationWithVersion').load()
    } else {
      api.invokeService("@home:get:association:specifications", 'requisition')
    }
    api.invokeService('@layout:set:header:icon', { showIcon: false })
  }

  prevTitle: any = api.invokeService('@layout:get:header:title')

  componentDidMount() {
    const { hiddenFooter, searchText } = this.state
    api.invokeService('@layout:change:header:title', i18n.get('新建单据'))
    if (hiddenFooter) {
      api.invokeService("@home:get:association:specifications", 'requisition').then(() => {
        this.onSubmit(searchText)
      })
    } else {
      api
        .dataLoader('@home.specificationWithVersion')
        .reload()
        .then(() => {
          this.onSubmit(searchText)
        })
    }
  }

  componentWillUnmount() {
    api.invokeService('@layout:set:header:icon', { showIcon: true })
    api.invokeService('@layout:change:header:title', this.prevTitle)
  }

  componentWillReceiveProps(nextProps: any) {
    if (!isEqual(nextProps.specification_recently, this.props.specification_recently)) {
      this.onSubmit(this.state.searchText)
    }
  }

  handleCancel = () => {
    api.emit('header:left:change', false)
    this.props.layer.emitCancel()
  }

  handleOK(spec: any) {
    const { hiddenFooter } = this.state

    return () => {
      const { specification_map } = this.props
      const specification = spec.components ? spec : specification_map[spec.id]
      api.invokeService('@home:save:specification', specification).then(() => {
        api
          .invokeService('@home:save:specification:after:filtered', {
            type: 'filterFormType',
            formType: specification.type,
            specifications: []
          })
          .then(() => {
            standardTrack('homeMyBillsModule', {
              souce: 'popup',
              actionType: `click-{${specification.id}}-{${specification.name}}`
            })
            // hiddenFooter 用来判断是否是 钉钉关联群
            if (hiddenFooter) {
              api.go(`/bill/isOpenAssociation/${specification.type}/${true}`)
              setTimeout(() => {
                let preHashs = [`#/bill/isOpenAssociation/${specification.type}/${true}`]
                preHashs = preHashs.map(hash => ({
                  pathname: hash.slice(1),
                  search: location.search,
                  state: location.state
                }))
                api.useHistory({
                  initialEntries: preHashs,
                  initialIndex: preHashs.length
                })
              }, 0)
            } else {
              api.go(`/bill/${specification.type}`)
            }
            this.props.layer.emitCancel()
          })
      })
    }
  }

  onSubmit = (value: string = '') => {
    const activeKeys: string[] = []
    const searchList: any[] = []
    const specification_group = get(this.props, 'specification_group') || []
    const specification_recently = get(this.props, 'specification_recently') || []
    const hasValue = !!value.trim().length
    const { hiddenFooter } = this.state
    const assiciationSpecifications = get(this.props, 'assiciationSpecifications') || []

    if (hiddenFooter) {
      const specifications = assiciationSpecifications.filter((spec: any) => spec.name.includes(value))
      this.setState({
        assiciationSpecifications: specifications
      })
      return
    }

    specification_group.forEach((group: any) => {
      const { id, name } = group
      let { specifications = [] } = group
      if (!specifications.length) {
        return
      }
      activeKeys.push(id)
      if (hasValue && name.includes(value)) {
        searchList.push(group)
      } else {
        if (hasValue) {
          specifications = specifications.filter((spec: any) => spec.name.includes(value))
        }
        specifications.length && searchList.push({ ...group, specifications })
      }
    })

    if (!hasValue && specification_recently.length) {
      activeKeys.unshift('recently')
      searchList.unshift({
        id: 'recently',
        name: i18n.get('最近使用'),
        specifications: specification_recently.slice(0, 4)
      })
    }

    this.setState({
      activeKeys,
      specification_group: searchList
    })
  }

  onChange = debounce((value: string) => {
    this.setState({ searchText: value })
    this.onSubmit(value)
  }, 300)

  switchPanel = (key: string[]) => {
    this.setState({ activeKeys: key })
  }

  renderHighlight = (value: string) => {
    return (
      <div className="item-title">
        {value || i18n.get('[无标题]')}
      </div>
    )
  }
  renderItem = (spec: any, index: number) => {
    const Icon: any = EKBIcon
    return (
      <div className="spec-item" key={spec.id} onClick={this.handleOK(spec)}>

        <div className="event-item-content">
          <div className="event-item">
            <Icon className="event-spec-item-icon" name={spec.icon} />
            <div className="event-item-title">{spec.name}</div>
          </div>
          <div className="event-item-desc">{spec.description}</div>
        </div>
        <Icon className="icon-right-standard" name="#ico-7-right" />
      </div>
    )
  }

  renderContent() {
    const { activeKeys, specification_group } = this.state
    let flag = false
    const group = specification_group.map(group => {
      group.specifications = group.specifications.filter(v => v.type !== 'settlement' && v.type !== 'reconciliation')
      return group
    }).filter(v => {
      const isGroup = !v?.id?.includes('_presetGroup') && v.specifications.length
      if(isGroup) {
        flag = true
      }
      return isGroup
    })
    return (
      <>
        {
          flag ?
          <div className={styles['home5-creact-modal-content']}>
            {group.map((group: any) => (
              <>
                {this.renderHighlight(group.name)}
                {group.specifications.map(this.renderItem)}
              </>
            ))}
          </div> :
          <div className="modal-popup-not">
            <div className="modal-popup-not-title">默认单据不够用？</div>
            <div className="modal-popup-not-explain">管理员可在 web 端新建更多单据模板</div>
            <img className="modal-popup-not-img" src={approve_not} />
          </div>
        }
      </>
    )
  }

  render() {
    const { isRoute } = this.state
    const { layer } = this.props
    return (
      <div className={`modal-popup-content ${isRoute ? styles['is-route-page'] : ''}`}>
        {isRoute ? void 0 : <div className="modal-action-bar">
          <div className="standard-modal-action-bar-btn" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </div>
          <span className="standard-modal-action-bar-title">{i18n.get('新建单据')}</span>
        </div>}
        <StandardCardList layer={layer} standardTrack={standardTrack} source="popup"/>
        {this.renderContent()}
      </div>
    )
  }
}
