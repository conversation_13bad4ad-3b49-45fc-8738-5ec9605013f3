import styles from './CustomModal.module.less'
import React, { PureComponent } from 'react'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager-mobile'
import { Button } from 'antd-mobile'

interface Props {
  customTitle: string
  info: string
  cancelText: string
  okText: string
}

export default class CustomModal extends PureComponent<Props & ILayerProps> {
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOk = () => {
    this.props.layer.emitOk('ok')
  }

  render() {
    const { customTitle, info, cancelText, okText } = this.props
    return (
      <div className={styles['custom-modal-wrapper']}>
        <span className="custom-modal-title">{i18n.get(customTitle)}</span>
        <span className="custom-modal-content">{i18n.get(info)}</span>
        <div className="custom-modal-button-layout">
          <Button className="modal-button" onClick={this.handleCancel}>
            {i18n.get(cancelText)}
          </Button>
          <Button className="modal-button" onClick={this.handleOk} type="primary">
            {i18n.get(okText)}
          </Button>
        </div>
      </div>
    )
  }
}
