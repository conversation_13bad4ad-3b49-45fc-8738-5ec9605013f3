/*
Author: <PERSON> (<EMAIL>)
RuleSettingModal.tsx (c) 2022
Desc: description
Created:  3/3/2022, 5:05:26 PM
Modified: 3/7/2022, 3:01:16 PM
*/
import React, { PureComponent } from 'react'
import styles from './RuleSettingModal.module.less'
import { Button } from 'antd-mobile'
import classNames from 'classnames'
import { app } from '@ekuaibao/whispered'


interface IProps {
    layer: any
    name?: React.ReactNode
}

interface IState {
    appShow?: {
        showMode?: "horizontal" | 'vertical'
        formFontSize?: "small" | "standard" | "big"
        forbidStaffModify?: boolean
    }
}

export default class RuleSettingModal extends PureComponent<IProps, IState> {
    state = {
        appShow: {}
    }
    componentDidMount() {
        this.init()
    }
    init = () => {
        const me = app.getState()['@common'].me_info?.staff
        let appShow = null
        let localShow = null
        try {
            appShow = JSON.parse(localStorage.getItem('170043_appShow'))
        } catch { }
        try {
            localShow = JSON.parse(localStorage.getItem(`${me.id}_appShow`))
        } catch { }
        const defaultShow = appShow?.forbidStaffModify ? appShow : localShow || appShow
        this.setState({
            appShow: defaultShow
        })
    }

    handleChange = (key: string, value: string) => {
        let { appShow } = this.state
        appShow = {
            ...appShow,
            [key]: value
        }
        this.setState({
            appShow
        })
    }

    handleUpdate = () => {
        const me = app.getState()['@common'].me_info?.staff
        localStorage.setItem(`${me.id}_appShow`, JSON.stringify(this.state.appShow))
        this.props.layer.emitOk({})
    }

    render() {
        const { appShow } = this.state
        const { name } = this.props
        return <div className={styles["rule-setting-modal"]}>
            {name && <h1 className={styles.title}>{name}</h1>}
            <p className={styles.header}>展示方式</p>
            <ul className={styles.box}>
                <li className={classNames(styles.tag, { [styles.active]: appShow?.showMode == 'vertical' })} onClick={() => this.handleChange('showMode', 'vertical')}>上下展示</li>
                <li className={classNames(styles.tag, { [styles.active]: appShow?.showMode == 'horizontal' })} onClick={() => this.handleChange('showMode', 'horizontal')}>左右展示</li>
            </ul>
            <p className={styles.header}>表单字号</p>
            <ul className={styles.box}>
                <li className={classNames(styles.tag, { [styles.active]: appShow?.formFontSize == 'small' })} onClick={() => this.handleChange('formFontSize', 'small')}>小号</li>
                <li className={classNames(styles.tag, { [styles.active]: appShow?.formFontSize == 'standard' })} onClick={() => this.handleChange('formFontSize', 'standard')}>标准</li>
                <li className={classNames(styles.tag, { [styles.active]: appShow?.formFontSize == 'big' })} onClick={() => this.handleChange('formFontSize', 'big')}>大号</li>
            </ul>
            <Button type='primary' onClick={this.handleUpdate}>确定</Button>
        </div >
    }
}