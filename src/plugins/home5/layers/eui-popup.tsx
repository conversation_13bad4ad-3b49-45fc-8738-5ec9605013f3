import React, { ReactNode } from 'react';
import { SearchBar } from '@hose/eui-mobile';
import { OutlinedTipsClose } from '@hose/eui-icons';
import classNames from 'classnames';
import { SearchBarProps } from '@hose/eui-mobile/es/components/search-bar';
import styles from './eui-popup.module.less';

interface EUIPopupProps {
  heading: ReactNode;
  searchable?: boolean;
  onSearch?: (value: string) => Promise<any>;
  searchBarProps?: Partial<SearchBarProps>;
  render: ReactNode | ((onClose: () => void) => React.ReactNode);
  layer: any
}

const EUIPopup = ({
  heading,
  searchable = false,
  onSearch,
  searchBarProps,
  render,
  layer,
}: EUIPopupProps) => {
  const [, setForceUpdate] = React.useState({});

  const onSearchChange = async (value: string) => {
    await onSearch?.(value);
    setForceUpdate({});
  }

  const handleClose = () => {
    layer?.emitCancel();
  }

  return <div className={styles['eui-popup-wrapper']}>
    <div className={styles['header']}>
      <OutlinedTipsClose className={styles.cancel} onClick={handleClose}  />
      <div className={styles.title}>{heading}</div>
    </div>
    {searchable &&
      <div className={styles['search-bar']}>
        <SearchBar
          placeholder={i18n.get('请输入')}
          showCancelButton
          onChange={onSearchChange}
          {...searchBarProps}
        />
      </div>
    }
    <div className={classNames(styles.content, { [styles['searchable']]: searchable })}>
      { typeof render === 'function' ? render(handleClose) : render }
    </div>
  </div>
}

export default EUIPopup;