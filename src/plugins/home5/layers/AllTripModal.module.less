@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '../../../styles/theme.less';

.all-trip-wrapper {
  display: flex;
  flex-direction: column;
  background: @fill-body;
  padding: @space-7;
  :global {
    .title {
      text-align: left;
      .font-weight-3;
      .font-size-5;
      color: @color-black-1;
    }
    .date-group {
      text-align: left;
      .font-weight-3;
      .font-size-2;
      color: @color-black-1;
      margin-top: @space-7;
    }
  }
}
.trip-card-item {
  box-sizing: border-box;
  padding: @space-6;
  border: 0;
  border-radius: @radius-3;
  background-color: @color-white-1;
  .shadow-black-2;
  display: flex;
  flex-direction: column;
  margin-top: @space-5;
  :global {
    .card-header {
      display: flex;
      padding-bottom: @space-5;
      flex-direction: row;
      .font-weight-3;
      .font-size-4;
      text-align: left;
      color: @color-black-1;
      justify-content: space-between;
      border-bottom: 2px dashed rgba(29, 43, 61, 0.15);
    }
    .card-header-hotel {
      .card-header;
      flex-direction: column;
    }
  }
}
