@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.cardEditModal-wrapper {
  flex: 1;
  background-color: @color-bg-2;
  min-width: 0;
  :global {
    .cardEditModal-actionBar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: @color-white-1;
      height: 96px;
      margin-bottom: @space-6;
      min-width: 0;
      .cardEditModal-title {
        .font-size-4;
        .font-weight-3;
        color: @color-black-1;
        overflow: hidden;
        white-space: nowrap;
        min-width: 0;
        text-overflow: ellipsis;
      }
      .cardEditModal-actionBar-btn {
        color: @color-brand-2;
        padding: 0 @space-6;
        .font-size-3;
        height: 96px;
        line-height: 96px;
        white-space: nowrap;

        &.grayout {
          color: @color-black-4;
        }
      }
    }
    .cardEditModal-dynamicLine-wrap {
      // margin-bottom: @space-7;
      .cardEditModal-dynamicLine-tip {
        text-align: left;
        margin-top: @space-2;
        padding: 0 @space-6;
        .font-size-2;
        color: @color-black-2;
        .font-weight-2;
      }
      .cardEditModal-dynamicLine {
        height: 96px;
        padding: 0 @space-6;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: @color-white-1;
        .am-switch {
          input[type='checkbox']:checked + .checkbox {
            background: @color-brand;
          }
        }
        .cardEditModal-dynamicLine-title {
          .font-size-3;
          .font-weight-2;
          color: @color-black-1;
        }
      }
    }
    .cardEditModal-showType-wrap {
      margin-top: @space-7;
      .cardEditModal-showType-title {
        padding: 0 @space-6;
        margin-bottom: @space-2;
        text-align: left;
        color: @color-black-2;
        .font-size-2;
        .font-weight-2;
      }
      .cardEditModal-showType-box {
        display: flex;
        flex-direction: row;
        padding: @space-6 0;
        background-color: @color-white-1;
        .cardEditModal-showType-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .cardEditModal-showType-item-img {
            width: 128px;
          }
          .cardEditModal-showType-item-title {
            margin: 0 @space-2;
            color: @color-black-1;
            .font-size-3;
            .font-weight-2;
          }
        }
      }
    }
    .mall-card-edit-info {
      background: #fff;
      width: 100%;
      position: fixed;
      left: 0;
      right: 0;
      top: 0.96rem;
      bottom: 0;
      display: flex;
      align-items: center;
      flex-direction: column;
      padding-top: 2rem;
      span {
        font-size: 0.28rem;
        color: rgba(29, 43, 61, 0.8);
        margin-top: .4rem;
      }
    }
  }
}
