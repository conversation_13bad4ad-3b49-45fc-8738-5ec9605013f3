@import '~@ekuaibao/eui-styles/less/token-mobile.less';

:global {
  .select-credit-rule-modal {
    .am-modal-content {
      border-radius: 16px 16px 0 0 !important;

      .modal-popup-content {
        background-color: var(--eui-bg-base);
  
        .modal-action-bar {
          height: 88px;
          line-height: 88px;
          background-color: var(--eui-bg-base);
          
          .modal-action-bar-btn {
            font: var(--eui-font-body-r1);
            color: var(--eui-text-caption);
            line-height: 88px;
          }
        }
        .modal-action-bar-title {
          font: var(--eui-font-head-b1);
          color: var(--eui-text-title);
        }
        
        .am-modal.am-modal-popup.am-modal-popup-slide-up {
          height: 1280px;
        }
      }
    }
  }
}

.home3-re-edit-bill {
  padding: 24px 26px 0;
  box-shadow: 0px 4px 16px 4px rgba(29, 33, 41, 0.02), 0px 4px 8px rgba(29, 33, 41, 0.02), 0px 2px 4px -4px rgba(29, 33, 41, 0.02);
  border-radius: 8px;
  z-index: 1;

  :global {
    .content {
      background-color: #F7F8FF;
      display: flex;
      justify-content: space-between;
      padding: 20px 26px;
      position: relative;
      border-radius: 8px;

      .pre-edit-bg {
        position: absolute;
        left: 0;
        top: 0;
        border-radius: 8px;
      }

      .text {
        color: var(--eui-primary-pri-500);
        .warning-icon {
          margin-right: 18px;
        }

        &+div svg{
          color: var(--eui-icon-n1);
        }
      }
    }
  }
}

.selectActiveCreditRuleContent {
  overflow: auto;
  height: 100%;
  flex: 1;
  padding-top: 12px;
  :global {
    .accordion {
      -webkit-overflow-scrolling: touch;
      padding-top: 10px;
      padding-bottom: 100px;

      .accordion-item {
        margin: 24px;
        background-color: #FFFFFF;
        box-shadow: 0px 4px 16px 4px rgba(29, 33, 41, 0.02), 0px 4px 8px rgba(29, 33, 41, 0.02), 0px 2px 4px -4px rgba(29, 33, 41, 0.02);
        border-radius: 8px;
  
        &:first-child {
          margin-top: 0;
        }
        .accordion-header {
          text-align: left;
          height: 80px;
          line-height: 80px;
          padding: 0 26px;
          position: relative;

          .lighter {
            font: var(--eui-font-body-b1);
            color: var(--eui-text-title);
          }

          .direction-icon {
            position: absolute;
            right: 30px;
            top: 0px;
            color: var(--eui-icon-n1);
          }
        }
        .accordion-content {
          overflow: hidden;
          padding: 16px 8px;
        }
      }
    }

    .clearfix {
      width: 100%;
      height: 0;
      float: left;
    }
    .credit-item {
      width: 50%;
      float: left;
      padding: 8px;
    }
    .credit-item-wrapper {
      display: flex;
      align-items: center;
      position: relative;
      padding: 16px 0 16px 16px;
      border-radius: @radius-1;
    }
    .credit-item-wrapper:active {
      background-color: @color-bg-2;
    }
    .credit-item-icon-bg {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background-color: @color-bg-1;
      opacity: 0.1;
      align-self: flex-start;
      display: none;
    }
    .credit-item-title {
      font: var(--eui-font-body-r1);
      color: var(--eui-text-caption);
      flex: 1;
      text-align: left;
      padding-left: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
    }
  }
}