// @i18n-ignore-all
import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager-mobile'
import styles from './TripDetailModal.module.less'
import { tripCard } from './AllTripModal'
import <PERSON>hance<PERSON><PERSON><PERSON>Hook from '../../../lib/EnhanceTitleHook'
import { app as api } from '@ekuaibao/whispered'

const getInfoList = (value: any, type: string) => {
  if (type === 'TRAIN') {
    return [
      { label: i18n.get('座位号'), value: value[Object.keys(value).find(o => !!o.endsWith('座位号'))] },
      { label: i18n.get('坐席'), value: value[Object.keys(value).find(o => !!o.endsWith('火车席位'))] }
      // { label: '检票口', value: value[Object.keys(value).find(o => !!o.endsWith('检票口'))] }  //暂时不需要，订单对象里面没有定义这个字段
    ]
  } else {
    return [
      { label: i18n.get('座位号'), value: value[Object.keys(value).find(o => !!o.endsWith('座位号'))] },
      { label: i18n.get('舱型'), value: value[Object.keys(value).find(o => !!o.endsWith('舱位类型'))] },
      { label: i18n.get('餐食'), value: value[Object.keys(value).find(o => !!o.endsWith('餐食'))] },
      { label: i18n.get('机型'), value: value[Object.keys(value).find(o => !!o.endsWith('机型'))] }
    ]
  }
}

// secretStr('123456789', 3, 2) -> '123****89
const secretStr = (value: any, startNum: number, endNum: number) => {
  if (!value) {
    return ''
  }
  if (startNum + endNum < value.length) {
    return value.substring(0, startNum) + '****' + value.substring(value.length - endNum)
  } else {
    return value
  }
}

interface States {}

interface Props {
  value: any
  layer: any
  type: string
}

@EnhanceTitleHook(i18n.get('行程详情'))
export default class TripDetailModal extends Component<Props, States> {
  constructor(props: Props) {
    super(props)
  }

  handleCancel = () => {
    this.props.layer.emitOk('cancel')
  }

  handleOk = () => {
    this.props.layer.emitOk('')
  }

  renderInfoItem = (item: any, index: number) => {
    return (
      <div className="info-item" key={index}>
        <div className="item-label">{item.label}</div>
        <div className="item-value">{item.value}</div>
      </div>
    )
  }
  componentWillUnmount() {
    api.invokeService('@layout:change:header:title', i18n.get('我的行程'))
  }
  render() {
    const { value, type } = this.props
    if (!value) {
      return null
    }
    const isHotel = type === 'HOTEL'
    const traveler = value[Object.keys(value).find(o => !!o.endsWith('出行人'))]
    const list = isHotel
      ? [traveler.map((item: any) => item.name).join(i18n.get('、'))]
      : [
          i18n.get(`证件号 {__k0}`, {
            __k0: secretStr(value[Object.keys(value).find(o => !!o.endsWith('证件信息'))], 6, 4)
          }),
          i18n.get(`手机号 {__k0}`, { __k0: secretStr(traveler[0].cellphone, 3, 4) })
        ]
    return (
      <div className={styles['trip-detail-wrapper']}>
        <div className="top-view">
          {!isHotel && (
            <div className="title">{`${type === 'FLIGHT' ? i18n.get('客票号') : i18n.get('取票号')} ${
              value[Object.keys(value).find(o => !!o.endsWith('票号'))]
            }`}</div>
          )}
          {tripCard(type, value)}
          {!isHotel && (
            <div className="info">
              {getInfoList(value, type).map((item, index) => {
                return this.renderInfoItem(item, index)
              })}
            </div>
          )}
        </div>
        <div className="bottom-view">
          <div className="title">{isHotel ? i18n.get('入住人') : i18n.get('同行人')}</div>
          <div className="content-top">
            {isHotel ? value[Object.keys(value).find(o => !!o.endsWith('房型'))] : traveler[0].name}
          </div>
          <div className="content">
            {list.map((item, index) => {
              return (
                <div className="content-item" key={index}>
                  {item}
                </div>
              )
            })}
          </div>
        </div>
      </div>
    )
  }
}
