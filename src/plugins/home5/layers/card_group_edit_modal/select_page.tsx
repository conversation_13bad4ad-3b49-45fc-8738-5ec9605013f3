import React, { Component } from 'react'
import { Checkbox } from 'antd-mobile'
import { Card } from './types'
import { hasInArray } from './util'
import { VERY_HEAVY_WEIGHT, cardMap } from '../../util/card_map'
import { getCardLabel } from '../../homePage/HomeCardManage/utils/helps'
import './select_page.less'

interface Props {
  cards: Card[]
  selectedCards: Card[]
  onSelect: (cards: Card[]) => void
  reverseInjection: (onCancel: Function, onSelect: Function) => void
}

interface States {
  selectedCards: {
    [id: string]: boolean
  }
}

export default class CardGroupEditSelectPage extends Component<Props, States> {
  constructor(props: Props) {
    super(props)

    this.state = { selectedCards: {} }
    props.reverseInjection(this.handleCancel, this.handleConfirm)
  }

  handleClickCheckbox = (card: Card) => {
    const { selectedCards } = this.state
    this.setState({
      selectedCards: {
        ...selectedCards,
        [card.id]: !this.isActive(card)
      }
    })
  }

  handleConfirm = () => {
    const { cards, onSelect } = this.props
    const selectedCards = cards.filter(this.isActive)
    onSelect(selectedCards)
    this.clearSelection()
  }

  handleCancel = () => {
    this.clearSelection()
  }

  countSelectedCards = () => {
    const { cards = [] } = this.props
    let count = 0
    for (const card of cards) {
      if (this.isActive(card)) {
        count += 1
      }
    }

    return count
  }

  isActive = (card: Card) => {
    const activeRecord = this.state.selectedCards[card.id]
    if (typeof activeRecord === 'boolean') {
      return activeRecord
    }

    return hasInArray(this.props.selectedCards, c => c.id === card.id)
  }

  /**
   * 等切换动画完成了再清除
   */
  clearSelection = () => setTimeout(() => this.setState({ selectedCards: {} }), 300)

  render() {
    const { cards } = this.props
    // const selectCount = this.countSelectedCards()
    // const countHintText = selectCount ? `(${selectCount})` : null

    return (
      <>
        <div className={'cardGroupEditSelectPage-wrap'}>
          <div className="cardGroupEditSelectPage-header">{i18n.get('请选择将要分组展示的卡片')}</div>
          <div className="cardGroupEditSelectPage-content">
            {cards
              // 前端做写死的排序，注释掉后可解除
              .sort(
                (a, b) =>
                  (cardMap(a.id).shadowWeight || VERY_HEAVY_WEIGHT) - (cardMap(b.id).shadowWeight || VERY_HEAVY_WEIGHT)
              )
              .map((card: Card, idx: number) => (
                <div
                  key={idx}
                  className={`cardGroupEditSelectPage-content-items ${this.isActive(card) ? 'active' : ''}`}
                  onClick={() => this.handleClickCheckbox(card)}
                >
                  <Checkbox checked={this.isActive(card)} />
                  <div className="cardGroupEditSelectPage-content-items-label">
                    <div>{cardMap(card.id).label || card.label || getCardLabel(card as any)}</div>
                  </div>
                </div>
              ))}
          </div>
        </div>
        {/*<div className="cardGroupEditSelectPage-footer" onClick={this.handleConfirm}>*/}
        {/*  {i18n.get('确 定')}{' '}{countHintText}*/}
        {/*</div>*/}
      </>
    )
  }
}
