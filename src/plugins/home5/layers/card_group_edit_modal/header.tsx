import React from 'react'
// import EKBIcon from '../../../../elements/ekbIcon'
import './header.less'

interface Props {
  stage: 'add' | 'edit' | 'select'
  disabled: boolean
  onBack: () => void
  onCancel: () => void
  onSubmit: () => void
  onSelect: () => void
}

const noop = () => {}

const Header = ({
  stage = 'add',
  disabled = false,
  onBack = noop,
  onCancel = noop,
  onSubmit = noop,
  onSelect = noop
}: Props) => {
  const titleMap = {
    add: i18n.get('添加卡片组'),
    edit: i18n.get('编辑卡片组'),
    select: i18n.get('选择分组卡片')
  }

  const disableStyle = stage !== 'select' && disabled ? 'disabled' : ''

  return (
    <div className="cardGroupEditModal-header">
      <span className="cardGroupEditModal-header-back">
        <span onClick={stage === 'select' ? onBack : onCancel}>{i18n.get('取消')}</span>
      </span>
      <span className="cardGroupEditModal-header-title font-size-5 font-weight-3">{titleMap[stage]}</span>
      <span className={`cardGroupEditModal-header-close ${disableStyle}`}>
        <div onClick={stage === 'select' ? onSelect : disabled ? noop : onSubmit}>
          {stage === 'select' ? i18n.get('确定') : i18n.get('保存')}
          {/*<EKBIcon name="#EDico-close-default" style={{ fontSize: 20 }} />*/}
        </div>
      </span>
    </div>
  )
}

export default Header
