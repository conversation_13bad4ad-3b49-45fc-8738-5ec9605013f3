@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.cardGroupEditModal-header {
  display: block;
  position: relative;
  flex: 0;
  text-align: center;
  height: 96px;
  color: @color-brand-2;
  .font-size-3;
  line-height: 96px;

  .cardGroupEditModal-header-title {
    .font-size-3;
    .font-weight-2;
    display: inline-block;
    text-align: center;
    color: @color-black-1;
  }

  .cardGroupEditModal-header-back {
    position: absolute;
    display: inline-block;
    line-height: normal;
    top: 50%;
    left: @space-6;
    transform: translateY(-50%);
    color: @color-black-3;
  }

  .cardGroupEditModal-header-close {
    position: absolute;
    display: inline-block;
    line-height: normal;
    top: 50%;
    right: @space-6;
    transform: translateY(-50%);

    &.disabled {
      color: @color-black-4;
    }
  }
}
