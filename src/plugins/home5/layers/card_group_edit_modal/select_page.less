@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.cardGroupEditSelectPage-wrap {
  flex: 1;
  overflow: auto;
  * {
    user-select: none;
  }
  .cardGroupEditSelectPage-header {
    .font-size-2;
    color: @color-black-3;
    padding: @space-7 0 @space-4 @space-6;
    text-align: left;
    background-color: @color-bg-2;
  }
  .cardGroupEditSelectPage-content {
    background-color: @color-white-1;
    margin-bottom: @space-7;
    .cardGroupEditSelectPage-content-items {
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 96px;
      .am-checkbox-wrapper {
        flex-shrink: 0;
        margin: 0 @space-6;
        .am-checkbox {
          width: 48px;
          height: 48px;
          .am-checkbox-inner {
            width: 48px;
            height: 48px;
            &::after {
              top: 6px;
              right: 16px;
            }
          }
        }
      }
      .cardGroupEditSelectPage-content-items-label {
        flex: 1;
        display: flex;
        height: 100%;
        justify-content: flex-start;
        align-items: center;
        border-bottom: 2px solid @color-line-1;
        .font-size-3;
        .font-weight-3;
        color: @color-black-1;
        div {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          padding-right: @space-6;
        }
      }
    }
    .cardGroupEditSelectPage-content-items:active {
      background-color: @color-bg-2;
    }
  }
}
