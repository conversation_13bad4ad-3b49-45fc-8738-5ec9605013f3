import { app } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager-mobile'
import * as mobx from 'mobx'
import styles from './modal.module.less'
import EditPage from './edit_page'
import SelectPage from './select_page'
import ModalHeader from './header'
import { Card } from './types'
// import EKBIcon from '../../../../elements/ekbIcon'
const EKBIcon = app.require('@elements/ekbIcon')

interface Props {
  pid?: string // 当前卡片组的 id, 当 pid 为空时表示是新增卡片组
  label?: string // 当前卡片组的 label
  // 后端返回的展示卡片和未展示卡片
  cardList: Card[] // 注：后端返回的数据是平铺的，需要根据 pid 过滤当前分组的数据
  cardBacklogList: Card[]
  layer: any // 外部注入的
}

interface State {
  label: string
  selecting: boolean
  selectedCards: Card[]
}

export default class CardGroupEditModal extends Component<Props, State> {
  DEFAULT_LABEL = i18n.get('未命名分组')
  onCancel?: Function
  onSelect?: Function

  constructor(props: Props) {
    super(props)
    this.state = {
      label: props.label || this.DEFAULT_LABEL,
      selecting: false,
      selectedCards: this.initSelectedCards()
    }
  }

  initSelectedCards() {
    const { pid } = this.props
    if (!pid) {
      return []
    }

    return (mobx.toJS(this.props.cardList) as Card[])
      .filter(card => card.pid === pid)
      .sort((a, b) => a.weight - b.weight)
  }

  handleCancel = () => {
    this.props.layer.emitOk('cancel')
  }

  handleOk = () => {
    this.props.layer.emitOk({
      data: this.state.selectedCards,
      label: this.state.label || this.DEFAULT_LABEL
    })
  }

  componentWillUnmount(): void {
    this.handleCancel()
  }

  handleChangeLabel = (newLabel: string) => {
    this.setState({ label: newLabel.slice(0, 20) })
  }

  handleNavBack = () => {
    this.setState({ selecting: false })
    this.onCancel && this.onCancel()
  }

  handleSelect = () => {
    this.onSelect && this.onSelect()
  }

  renderHeader = () => {
    const { selecting } = this.state
    let title = ''
    if (!selecting) {
      if (this.props.pid) {
        title = i18n.get('编辑卡片组')
      } else {
        title = i18n.get('添加卡片组')
      }
    } else {
      title = i18n.get('选择分组卡片')
    }

    return (
      <div className="cardGroupEditModal-header">
        <span className="cardGroupEditModal-header-back">
          {selecting && (
            <span onClick={this.handleNavBack}>
              <EKBIcon name="#EDico-left-default" style={{ fontSize: 20 }} />
            </span>
          )}
        </span>
        <span className="cardGroupEditModal-header-title font-size-5 font-weight-3">{title}</span>
        <span className="cardGroupEditModal-header-close">
          {!selecting && (
            <div onClick={this.handleCancel}>
              <EKBIcon name="#EDico-close-default" style={{ fontSize: 20 }} />
            </div>
          )}
        </span>
      </div>
    )
  }

  handleChangePageToSelect = () => {
    this.setState({ selecting: true })
  }

  /**
   * 选择卡片页操作『增加以下卡片』
   * @param cards 新增卡片
   */
  onSelectCards = (cards: Card[]) => {
    this.setState({
      selectedCards: cards,
      selecting: false
    })
  }

  canSubmit = () => !!this.state.selectedCards.length

  render() {
    const { pid } = this.props
    const backlogCards = mobx.toJS(this.props.cardBacklogList) as Card[]
    const listCards = mobx.toJS(this.props.cardList) as Card[]
    const { selecting, selectedCards, label } = this.state
    // 待选卡片包括所有未分组的卡片
    const moreCards = listCards
      // 已显示的未分组卡片
      .filter(card => (!card.pid || card.pid === pid) && card.type !== 'GROUP')
      // 未显示的未分组卡片
      .concat(backlogCards || [])
    // 过滤掉已经选择的卡片
    // .filter(card => !hasInArray<Card>(selectedCards, c => c.id === card.id ))

    // 关于滑动：使用并排的2个 div 根据 selecting 做了 translate 处理
    return (
      <div className={styles['cardGroupEditModal-wrapper']}>
        <ModalHeader
          stage={selecting ? 'select' : pid ? 'edit' : 'add'}
          disabled={!this.canSubmit()}
          onBack={this.handleNavBack}
          onCancel={this.handleCancel}
          onSubmit={this.handleOk}
          onSelect={this.handleSelect}
        />
        <div className="cardGroupEditModal-content">
          <div className="cardGroupEditModal-slider" style={{ transform: selecting ? 'translateX(-50%)' : 'none' }}>
            <div className="cardGroupEditModal-front-wrapper">
              <EditPage
                fnChangePageToSelect={this.handleChangePageToSelect}
                selectedCards={selectedCards}
                more={moreCards.length > selectedCards.length}
                onSort={(newList: Card[]) => this.setState({ selectedCards: newList })}
                onSubmit={this.handleOk}
                label={label}
                onChangeLabel={this.handleChangeLabel}
              />
            </div>
            <div className="cardGroupEditModal-select-wrapper">
              <SelectPage
                cards={moreCards}
                selectedCards={selectedCards}
                onSelect={this.onSelectCards}
                reverseInjection={(onCancel, onSelect) => {
                  this.onCancel = onCancel
                  this.onSelect = onSelect
                }}
              />
            </div>
          </div>
        </div>
      </div>
    )
  }
}
