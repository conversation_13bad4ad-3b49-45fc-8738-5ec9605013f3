import { app } from '@ekuaibao/whispered'
import React, { Component } from 'react'
const EKBIcon = app.require<any>('@elements/ekbIcon')
import './edit_page.less'

import { arrayMove, SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc'
import HomeCardIcon from '../../homePage/HomeCardManage/elements/HomeCardIcon'
import { getCardLabel } from '../../homePage/HomeCardManage/utils/helps'
import { ICard } from '../../homePage/HomeCardManage/Types'

const EkbSortableContainer = SortableContainer(({ children, cName }: { children: React.ReactNode; cName: string }) => (
  <ul className={cName}>{children}</ul>
))

const EkbSortableItem = SortableElement(
  SortableHandle(({ onRemove, value }: { onRemove: (foo: any) => any; value: ICard }) => (
    <li className="cardGroupEditModal-content-card">
      <div className="cardGroupEditModal-content-icon">
        <span className="cardGroupEditModal-badge" onClick={onRemove}>
          <EKBIcon name="#EDico-minus-circle" style={{ fontSize: 20 }} />
        </span>
        <HomeCardIcon card={value} fontSize={38} />
      </div>
      <div className="cardGroupEditModal-content-desc font-size-2">{getCardLabel(value)}</div>
    </li>
  ))
)

interface Props {
  selectedCards: ICard[]
  more: boolean
  onSort: (list: ICard[]) => void
  onSubmit: () => void
  fnChangePageToSelect: (foo: any) => void
  label: string
  onChangeLabel: (newLabel: string) => void
}

export default class CardGroupEditFrontPage extends Component<Props> {
  ref: React.RefObject<HTMLDivElement>

  constructor(props: Props) {
    super(props)

    this.ref = React.createRef()
  }

  /**
   * 交换两个元素的位置
   * @param oldIndex {number} 被移动的元素的原位置
   * @param newIndex {number} 被移动的元素的新位置
   */
  onSortEnd = ({ oldIndex, newIndex }: { oldIndex: number; newIndex: number }) =>
    this.props.onSort(arrayMove(this.props.selectedCards, oldIndex, newIndex))

  /**
   * 把当前卡片移除列表
   * @param card {Card} 当前卡片数据
   */
  onRemoveCard = (card: ICard) => this.props.onSort(this.props.selectedCards.filter(c => c.id !== card.id))

  renderCardGroup() {
    const { fnChangePageToSelect, selectedCards, more } = this.props
    return (
      <EkbSortableContainer
        cName="cardGroupEditModal-content-edit-page"
        onSortEnd={this.onSortEnd}
        axis={'xy'}
        distance={5}
        helperContainer={() => this.ref.current}
      >
        {selectedCards.map((card: ICard, index: number) => (
          <EkbSortableItem key={index} value={card} onRemove={() => this.onRemoveCard(card)} index={index} />
        ))}
        {more ? (
          <li key="lastKey" className="cardGroupEditModal-content-card" onClick={fnChangePageToSelect}>
            <div className="cardGroupEditModal-content-icon icon-brand add-icon-border">
              <EKBIcon name="#EDico-plus-default" style={{ fontSize: 24 }} />
            </div>
          </li>
        ) : null}
      </EkbSortableContainer>
    )
  }

  canSubmit = () => !!this.props.selectedCards.length

  render() {
    const { label, onChangeLabel } = this.props
    return (
      <div ref={this.ref} className="cardGroupEditModal-frontPage">
        <div className="cardGroupEditModal-title-wrapper">
          <div className="cardGroupEditModal-title font-size-2">
            <span className="cardGroupEditModal-name-title">{i18n.get('分组名称')}</span>
            <input
              type="text"
              className="font-size-3"
              value={label === '我的常用' ? i18n.get(label) : label}
              onChange={e => onChangeLabel(e.target.value)}
            />
          </div>
        </div>
        <div className="cardGroupEditModal-detail">
          <div className="cardGroupEditModal-detail-title font-size-3 font-weight-3">{i18n.get('分组详情')}</div>
          {this.renderCardGroup()}
        </div>
      </div>
    )
  }
}
