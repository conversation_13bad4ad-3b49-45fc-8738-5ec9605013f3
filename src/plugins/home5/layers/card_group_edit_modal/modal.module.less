@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.cardGroupEditModal-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;

  :global {
    * {
      -webkit-overflow-scrolling: touch;
      min-width: 0;
      user-select: none;
    }

    input {
      user-select: text;
    }

    .cardGroupEditModal-content {
      display: block;
      position: relative;
      flex: 1;
      overflow: hidden;

      .cardGroupEditModal-slider {
        width: 200%;
        height: 100%;
        overflow: hidden;
        display: flex;
        align-items: stretch;
        transition: transform 0.3s ease-in-out;

        .cardGroupEditModal-front-wrapper {
          flex: 1;
          display: flex;
          align-items: stretch;
          flex-direction: column;
          background-color: @color-white-1;
        }

        .cardGroupEditModal-select-wrapper {
          flex: 1;
          display: flex;
          align-items: stretch;
          flex-direction: column;
          padding-bottom: @space-6;
        }
      }
    }
    .cardGroupEditSelectPage-footer {
      z-index: 1;
      width: 100%;
      height: 112px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: @color-white-1;
      color: @color-brand;
      .shadow-3;
    }
  }
}
