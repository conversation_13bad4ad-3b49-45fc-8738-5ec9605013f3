@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.cardGroupEditModal-content-edit-page {
  z-index: 1000;
  display: flex;
  flex-wrap: wrap;
  * {
    user-select: none;
  }

  .cardGroupEditModal-content-card {
    width: 25%;
    display: inline-block;
    text-align: center;
    margin-bottom: @space-5;
    .cardGroupEditModal-content-icon {
      height: 0;
      position: relative;
      // border-radius: @radius-5;
      margin-bottom: @space-4;
      // background: @color-bg-2;
      width: 80px;
      line-height: 80px;
      padding-bottom: 80px;
      display: inline-block;
      svg {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      &.icon-brand {
        color: @color-brand;
        background-color: transparent;
        border: 2px dashed @color-brand;
      }
    }
    .add-icon-border{
      border-radius: 24px
    }

    .cardGroupEditModal-content-desc {
      .font-size-2 !important;
      margin: 0 -4px;
      overflow:hidden;
      text-overflow:ellipsis;
      display:-webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp:3;
      color: @color-black-2;
    }

    .cardGroupEditModal-badge {
      position: absolute;
      display: inline-block;
      top: 0;
      z-index: 10;
      right: 0;
      color: @color-error;
    }
  }
}

.cardGroupEditModal-frontPage {
  background: @color-white-1;
  text-align: left;
  overflow-x: hidden;
  overflow-y: auto;

  .cardGroupEditModal-title-wrapper {
    background: @color-bg-2;
    padding: @space-5 0;

    .cardGroupEditModal-title {
      .font-size-2;
      display: block;
      height: 156px;
      box-sizing: border-box;
      padding: @space-5 @space-6;
      color: @color-black-2;
      background: @color-white-1;

      input {
        .font-size-3;
        display: block;
        height: 48px;
        width: 100%;
        line-height: 48px;
        padding: 0;
        border: 0;
        color: @color-black-1;
      }
    }
    .cardGroupEditModal-name-title {
      .font-size-3;
      .font-weight-3;
      color: @color-black-1;
      padding-bottom: @space-5;
    }
  }

  .cardGroupEditModal-detail {
    background: @color-white-1;
    box-sizing: border-box;

    .cardGroupEditModal-detail-title {
      .font-size-3;
      .font-weight-3;
      color: @color-black-1;
      padding-bottom: @space-5;
      padding-top: @space-7;
      margin-left: @space-6;
    }
  }

  .cardGroupEditModal-confirm {
    display: block;
    flex: 0;
    box-sizing: border-box;
    height: 116px;
    line-height: 116px;
    text-align: center;
    .shadow-black-3;
    background-color: @color-white-1;
    color: @color-brand;

    span {
      line-height: normal;
      vertical-align: middle;
    }

    &.disabled {
      color: @color-black-4;
    }
  }
}
