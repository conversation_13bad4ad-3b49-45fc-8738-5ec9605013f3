import React, { PureComponent } from 'react'
import styles from './home5-create-modal.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { Accordion } from 'antd-mobile'
import { SearchBar } from '@hose/eui-mobile'
import Highlighter from 'react-highlight-words'
import { debounce, isEqual, get } from 'lodash'
const EKBIcon = api.require('@elements/ekbIcon')
import { getFuncDetail } from '../staticUtil'
import { fnAddReimbursementSensorTrack } from '../util/track'
import { startOpenFlowPerformanceStatistics } from '../../../lib/flowPerformanceStatistics'
const getSpecificationIcon = api.require<any>('@elements/specificationIcon')
const { getSpecificationName } = api.require<any>('@bill/utils/billUtils')

interface StateType {
  isRoute: boolean
  searchText: string
  activeKeys: string[]
  specification_group: any[]
}

@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].me_info.staff,
  specification_list: state['@home'].specificationWithVersion.specification_list,
  specification_group: state['@home'].specificationWithVersion.specification_group,
  specification_recently: state['@home'].specification_recently,
  specification_map: state['@home'].specificationWithVersion.specification_map,
  assiciationSpecifications: state['@home'].assiciationSpecifications // 钉钉管理群模板
}))
export default class Home5CreateModal extends PureComponent<any, Partial<StateType>> {
  constructor(props: any) {
    super(props)
    this.state = {
      isRoute: props?.params?.isRoute,
      hiddenFooter: props?.params?.hiddenFooter,
      searchText: '',
      activeKeys: [],
      specification_group: [],
      assiciationSpecifications: [] // 钉钉关联群模板
    }

    api.invokeService('@home:get:specification:recently')
    if (!props?.params?.hiddenFooter) {
      api.dataLoader('@home.specificationWithVersion').load()
    } else {
      api.invokeService('@home:get:association:specifications', 'requisition')
    }
    api.invokeService('@layout:set:header:icon', { showIcon: false })
  }

  prevTitle: any = api.invokeService('@layout:get:header:title')

  componentDidMount() {
    const { hiddenFooter, searchText } = this.state
    api.invokeService('@layout:change:header:title', i18n.get('新建单据'))
    if (hiddenFooter) {
      api.invokeService('@home:get:association:specifications', 'requisition').then(() => {
        this.onSubmit(searchText)
      })
    } else {
      api
        .dataLoader('@home.specificationWithVersion')
        .reload()
        .then(() => {
          this.onSubmit(searchText)
        })
    }
  }

  componentWillUnmount() {
    api.invokeService('@layout:set:header:icon', { showIcon: true })
    api.invokeService('@layout:change:header:title', this.prevTitle)
  }

  componentWillReceiveProps(nextProps: any) {
    if (!isEqual(nextProps.specification_recently, this.props.specification_recently)) {
      this.onSubmit(this.state.searchText)
    }
  }

  handleCancel = () => {
    api.emit('header:left:change', false)
    this.props.layer.emitCancel()
  }

  handleOK(spec: any) {
    const { hiddenFooter } = this.state

    return () => {
      const { specification_map } = this.props
      const specification = spec.components ? spec : specification_map[spec.id]

      console.log('[[---------------------------------]]')
      console.log(spec)

      if (spec.type === 'expense') {
        fnAddReimbursementSensorTrack('add_reimbursement')
      }
      api.invokeService('@home:save:specification', specification).then(() => {
        api
          .invokeService('@home:save:specification:after:filtered', {
            type: 'filterFormType',
            formType: specification.type,
            specifications: []
          })
          .then(() => {
            // hiddenFooter 用来判断是否是 钉钉关联群
            if (hiddenFooter) {
              api.go(`/bill/isOpenAssociation/${specification.type}/${true}`)
              setTimeout(() => {
                let preHashs = [`#/bill/isOpenAssociation/${specification.type}/${true}`]
                preHashs = preHashs.map(hash => ({
                  pathname: hash.slice(1),
                  search: location.search,
                  state: location.state
                }))
                api.useHistory({
                  initialEntries: preHashs,
                  initialIndex: preHashs.length
                })
              }, 0)
            } else {
              api.go(`/bill/${specification.type}`)
            }
            startOpenFlowPerformanceStatistics()
            this.props.layer.emitCancel()
          })
      })
    }
  }

  onSubmit = (value: string = '') => {
    const activeKeys: string[] = []
    const searchList: any[] = []
    const specification_group = get(this.props, 'specification_group') || []
    const specification_recently = get(this.props, 'specification_recently') || []
    const hasValue = !!value.trim().length
    const { hiddenFooter } = this.state
    const assiciationSpecifications = get(this.props, 'assiciationSpecifications') || []

    if (hiddenFooter) {
      const specifications = assiciationSpecifications.filter((spec: any) => spec.name.includes(value))
      this.setState({
        assiciationSpecifications: specifications
      })
      return
    }

    specification_group.forEach((group: any) => {
      const { id, name } = group
      let { specifications = [] } = group
      if (!specifications.length) {
        return
      }
      activeKeys.push(id)
      if (hasValue && name.includes(value)) {
        searchList.push(group)
      } else {
        if (hasValue) {
          specifications = specifications.filter((spec: any) => spec.name.includes(value))
        }
        specifications.length && searchList.push({ ...group, specifications })
      }
    })

    if (!hasValue && specification_recently.length) {
      activeKeys.unshift('recently')
      searchList.unshift({
        id: 'recently',
        name: i18n.get('最近使用'),
        specifications: specification_recently.slice(0, 4)
      })
    }

    this.setState({
      activeKeys,
      specification_group: searchList
    })
  }

  onChange = debounce((value: string) => {
    this.setState({ searchText: value })
    this.onSubmit(value)
  }, 300)

  switchPanel = (key: string[]) => {
    this.setState({ activeKeys: key })
  }

  renderHighlight = (value: string) => {
    return (
      <Highlighter
        className="lighter"
        searchWords={[this.state.searchText]}
        textToHighlight={value || i18n.get('[无标题]')}
        highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
        autoEscape={true}
      />
    )
  }

  renderItem = (spec: any, index: number) => {
    const IconCompo = getSpecificationIcon(spec.icon)
    const name = getSpecificationName(spec)
    return (
      <div key={index}>
        <div className="spec-item" key={index} onClick={this.handleOK(spec)}>
          <div className="spec-item-wrapper pos-r">
            <div className="spec-item-icon-bg" style={{ backgroundColor: spec.color }} />
            <IconCompo className="spec-item-icon" style={{ color: spec.color }} />
            <div className="spec-item-title">{this.renderHighlight(name)}</div>
          </div>
        </div>
        {index % 2 ? <div className="clearfix" key={`c${index}`} /> : null}
      </div>
    )
  }
  renderSearch() {
    return (
      <div className={styles['home5-creact-modal-search']}>
        <SearchBar showCancelButton placeholder={i18n.get('搜索')} onSearch={this.onSubmit} onChange={this.onChange} />
      </div>
    )
  }

  renderContent() {
    const { activeKeys, specification_group } = this.state
    const group = specification_group
      .map(group => {
        group.specifications = group.specifications.filter(v => v.type !== 'settlement' && v.type !== 'reconciliation')
        return group
      })
      .filter(v => v.specifications.length)
    return (
      <div className={styles['home5-creact-modal-content']}>
        <Accordion activeKey={activeKeys} onChange={this.switchPanel}>
          {group.map((group: any) => (
            <Accordion.Panel key={group.id} header={this.renderHighlight(group.name)}>
              {group.specifications.map(this.renderItem)}
            </Accordion.Panel>
          ))}
        </Accordion>
      </div>
    )
  }

  renderAssociationContent() {
    const { assiciationSpecifications } = this.state

    return (
      <div className={styles['home5-creact-modal-content']}>
        {assiciationSpecifications.map((spec: any, index: number) => {
          return this.renderItem(spec, index)
        })}
      </div>
    )
  }

  renderFooter() {
    const Icon: any = EKBIcon
    const funcData = getFuncDetail()
    return (
      <div className={styles['home5-creact-modal-footer']}>
        {funcData.map((el, idx) => (
          <div
            className="func-item"
            key={idx}
            onClick={() => {
              el.fn()
              this.handleCancel()
            }}
          >
            <div className="func-item-icon">
              <Icon name={el.icon1} />
              <Icon name={el.icon2} />
            </div>
            <span className="func-item-name">{el.name}</span>
          </div>
        ))}
      </div>
    )
  }

  render() {
    const { isRoute, hiddenFooter } = this.state
    return (
      <div className={`modal-popup-content ${isRoute ? styles['is-route-page'] : ''}`}>
        {isRoute ? (
          void 0
        ) : (
          <div className="modal-action-bar">
            <div className="modal-action-bar-btn" onClick={this.handleCancel}>
              {i18n.get('取消')}
            </div>
            <span className="modal-action-bar-title">{i18n.get('新建单据')}</span>
          </div>
        )}
        {this.renderSearch()}
        {hiddenFooter ? this.renderAssociationContent() : this.renderContent()}
        {!isRoute && !hiddenFooter && this.renderFooter()}
      </div>
    )
  }
}
