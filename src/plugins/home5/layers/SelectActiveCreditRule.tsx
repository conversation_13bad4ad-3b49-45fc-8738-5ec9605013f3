import React, { Component } from 'react';
import styles from './SelectActiveCreditRule.module.less'
import { OutlinedDirectionRight } from '@hose/eui-icons';
import { cloneDeep } from 'lodash';

class SelectActiveCreditRule extends Component<any, any> {
    constructor(props: any) {
        super(props)
        this.state = { collapseMap: {} }
    }
    handleCancel = () => {
        const { layer } = this.props
        layer.emitCancel()
    }
    handleOK = (el: any) => {
        const { layer } = this.props
        layer.emitOk(el)
    }
    handleAccordionCollapse = (groupId: string) => {
        this.setState(({ collapseMap = {} as any }) => {
            const collapseMapClone = cloneDeep(collapseMap) as any
            collapseMapClone[groupId] = collapseMap[groupId] === undefined ? false : !collapseMap[groupId]
            return {
                collapseMap: collapseMapClone
            }
        })
    }
    renderActiveCreditRuleWithGroups = () => {
        const { collapseMap = {} } = this.state
        const { params: { activeCreditRuleWithGroups } } = this.props
        return (<div className={styles.selectActiveCreditRuleContent}>
            <div className="accordion">
                {activeCreditRuleWithGroups.map((item: any) => {
                    const { ruleGroup: { id = '', name = '' }, rules = [] } = item
                    const isNotCollapsed = collapseMap[id] || collapseMap[id] === undefined
                    return (
                        <div className="accordion-item" key={id}>
                            <div className='accordion-header' onClick={() => this.handleAccordionCollapse(id)}>
                                <span className="lighter">
                                    {name}
                                </span>
                                <div className="direction-icon">
                                    <OutlinedDirectionRight rotate={isNotCollapsed ? -90 : 90} />
                                </div>
                            </div>
                            {isNotCollapsed && (<div className="accordion-content">
                                {rules.map(this.renderItem)}
                            </div>)}
                        </div>
                    )
                })}
            </div>
        </div>)
    }
    renderItem = (it: any, index: number) => {
        const { params: { detailReadable } } = this.props
        const { id, label, score } = it
        const fontColor = score >= 0 ? { color: '#00B42A' } : { color: '#FF7D00' }
        const scoreValue = score >= 0 ? `+${score}` : score
        return (
            <div key={id}>
                <div className="credit-item" key={index} onClick={() => this.handleOK(it)}>
                    <div className="credit-item-wrapper pos-r">
                        <div className="credit-item-icon-bg" />
                        <div className="credit-item-title">
                            <span className="lighter">
                                {label}
                            </span>
                        </div>
                        {detailReadable && <div className="note-option-score" style={fontColor}>{scoreValue}</div>}
                    </div>
                </div>
                {index % 2 ? <div className="clearfix" key={`c${index}`} /> : null}
            </div>
        )
    }
    render() {
        return (
            <div className={`modal-popup-content`}>
                <div className="modal-action-bar">
                    <div className="modal-action-bar-btn" onClick={this.handleCancel}>
                        {i18n.get('取消')}
                    </div>
                    <span className="modal-action-bar-title">{i18n.get('请选择信用批注')}</span>
                </div>
                {this.renderActiveCreditRuleWithGroups()}
            </div>
        )
    }
}
export default SelectActiveCreditRule