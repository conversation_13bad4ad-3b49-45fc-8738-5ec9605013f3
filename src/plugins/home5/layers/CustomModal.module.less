@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.custom-modal-wrapper {
  display: flex;
  flex-direction: column;
  padding: 64px 48px 48px;
  align-items: flex-start;
  :global {
    .custom-modal-title {
      font-weight: 400;
      font-size: 28px;
      line-height: 40px;
      color: rgba(39, 46, 59, 0.8);
      margin-bottom: @space-4;
    }
    .custom-modal-content {
      font-weight: 500;
      font-size: 28px;
      line-height: 40px;
      color: #272e3b;
      margin-bottom: @space-8;
    }
    .custom-modal-button-layout {
      display: flex;
      flex-direction: row;
      width: 100%;
      justify-content: space-between;
      .modal-button {
        width: 128px * 2;
        border-radius: 8px;
        font-weight: 400;
        font-size: 30px;
      }
    }
  }
}
