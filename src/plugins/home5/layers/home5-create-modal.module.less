@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.home5-creact-modal-search {
  padding: 12px 24px;
  background-color: #ffffff;
  .shadow-1;
}

.home5-creact-modal-content {
  overflow: auto;
  height: 100%;
  flex: 1;

  :global {
    .am-accordion {
      -webkit-overflow-scrolling: touch;
      padding-top: 10px;
      padding-bottom: 100px;
    }

    .am-accordion-header {
      text-align: left;

      .lighter {
        .font-size-3;
        .font-weight-3;
      }

      i.arrow {
        width: @space-5 !important;
        height: @space-5 !important;
      }
    }

    .am-accordion-content-box {
      overflow: hidden;
      padding: 16px 8px;
    }

    .clearfix {
      width: 100%;
      height: 0;
      float: left;
    }

    .spec-item {
      width: 50%;
      float: left;
      padding: 8px;
    }

    .spec-item-wrapper {
      display: flex;
      align-items: center;
      position: relative;
      padding: 16px 0 16px 16px;
      border-radius: @radius-1;
    }

    .spec-item-wrapper:active {
      background-color: @color-bg-2;
    }

    .spec-item-icon-bg {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background-color: @color-bg-1;
      opacity: 0.1;
      align-self: flex-start;
      display: none;
    }

    .spec-item-icon {
      width: 44px;
    }

    .spec-item-title {
      .font-size-2;
      flex: 1;
      text-align: left;
      padding-left: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
    }
  }
}

.home5-creact-modal-footer {
  width: 100%;
  height: 120px;
  display: flex;
  justify-content: center;
  position: relative;
  .shadow-3;

  :global {
    .func-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      background-color: #ffffff;
      transition: .2s ease-in-out background-color;

      &:hover {
        background-color: @color-bg-1;
      }

      .func-item-icon {
        position: relative;
        width: 48px;
        height: 48px;
        margin: @space-2 0;

        .icon {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;

          &:first-child {
            color: @color-black-2;
          }

          &:last-child {
            color: @color-brand;
          }
        }
      }

      .func-item-name {
        .font-size-1;
        .font-weight-3;
        color: @color-black-1;
      }
    }
  }
}