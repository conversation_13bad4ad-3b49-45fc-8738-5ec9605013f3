import React, { PureComponent } from 'react'
import styles from './home3-create-modal.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { Input, SkeletonTitle } from '@hose/eui-mobile'
import Highlighter from 'react-highlight-words'
import { debounce, isEqual, get, cloneDeep } from 'lodash'
import { FilledTipsWarning, OutlinedDirectionRight, OutlinedEditSearch } from '@hose/eui-icons'
import { getFuncDetail } from '../staticUtil'
import { getBillKey } from '../../../lib/util'
import { updateDetailIndex } from '../../bill/utils/billUtils'
import { fnAddReimbursementSensorTrack } from '../util/track'
const getSpecificationIcon = api.require<any>('@elements/specificationIcon')
const { related } = api.require<any>('@components/utils/Related')
const DRAFT_ID = getBillKey()
const PreEditBg = api.require<any>('@images/pre-edit-bg')
const EmptyWidget = api.require<any>('@home5/EmptyWidget')
import { SpecificationGroup } from '@ekuaibao/ekuaibao_types'
import { startOpenFlowPerformanceStatistics } from '../../../lib/flowPerformanceStatistics'
const { getSpecificationName } = api.require<any>('@bill/utils/billUtils')

interface StateType {
  isRoute: boolean
  hiddenFooter: boolean
  searchText: string
  activeKeys: string[]
  specification_group: any[]
  assiciationSpecifications: any[]
  collapseMap: any
  isLoading?: boolean
}

@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].me_info.staff,
  specification_list: state['@home'].specificationWithVersion.specification_list,
  specification_group: state['@home'].specificationWithVersion.specification_group,
  specification_recently: state['@home'].specification_recently,
  specification_map: state['@home'].specificationWithVersion.specification_map,
  assiciationSpecifications: state['@home'].assiciationSpecifications, // 钉钉管理群模板
  autoExpenseForbiddenFeaturePower: state['@common'].powers.autoExpenseForbiddenFeature
}))
export default class home3CreateModal extends PureComponent<any, Partial<StateType>> {
  constructor(props: any) {
    super(props)
    this.state = {
      isRoute: props?.params?.isRoute,
      hiddenFooter: props?.params?.hiddenFooter,
      searchText: '',
      activeKeys: [],
      specification_group: [],
      assiciationSpecifications: [], // 钉钉关联群模板
      collapseMap: {},
      isLoading: true
    }

    api.invokeService('@home:get:specification:recently')
    if (!props?.params?.hiddenFooter) {
      api.dataLoader('@home.specificationWithVersion').load()
    } else {
      api.invokeService('@home:get:association:specifications', 'requisition')
    }
    api.invokeService('@layout:set:header:icon', { showIcon: false })
  }

  prevTitle: any = api.invokeService('@layout:get:header:title')

  componentDidMount() {
    const { hiddenFooter, searchText } = this.state
    api.invokeService('@layout:change:header:title', i18n.get('新建单据'))
    if (hiddenFooter) {
      api.invokeService('@home:get:association:specifications', 'requisition').then(() => {
        this.onSubmit(searchText)
      })
    } else {
      api
        .dataLoader('@home.specificationWithVersion')
        .reload()
        .then(() => {
          this.onSubmit(searchText)
        })
    }
  }

  componentWillUnmount() {
    api.invokeService('@layout:set:header:icon', { showIcon: true })
    api.invokeService('@layout:change:header:title', this.prevTitle)
  }

  componentWillReceiveProps(nextProps: any) {
    if (!isEqual(nextProps.specification_recently, this.props.specification_recently)) {
      this.onSubmit(this.state.searchText)
    }
  }

  handleCancel = () => {
    api.emit('header:left:change', false)
    this.props.layer.emitCancel()
  }

  handleOK(spec: any) {
    const { hiddenFooter } = this.state

    return () => {
      // 点击
      const ls = localStorage.getItem(DRAFT_ID)
      if (ls) {
        related.clearRelateMap()
        localStorage.removeItem(DRAFT_ID)
      }
      const { specification_map } = this.props
      const specification = spec.components ? spec : specification_map[spec.id]

      console.log('-------------------------------------------------')
      console.log(spec)

      if (spec.type === 'expense') {
        fnAddReimbursementSensorTrack('add_reimbursement')
      }
      api.invokeService('@home:save:specification', specification).then(() => {
        api
          .invokeService('@home:save:specification:after:filtered', {
            type: 'filterFormType',
            formType: specification.type,
            specifications: []
          })
          .then(() => {
            // hiddenFooter 用来判断是否是 钉钉关联群
            if (hiddenFooter) {
              api.go(`/bill/isOpenAssociation/${specification.type}/${true}`)
              setTimeout(() => {
                let preHashs = [`#/bill/isOpenAssociation/${specification.type}/${true}`]
                preHashs = preHashs.map(hash => ({
                  pathname: hash.slice(1),
                  search: location.search,
                  state: location.state
                }))
                api.useHistory({
                  initialEntries: preHashs,
                  initialIndex: preHashs.length
                })
              }, 0)
            } else {
              api.go(`/bill/${specification.type}/from/createModal`)
            }
            startOpenFlowPerformanceStatistics()
            this.props.layer.emitCancel()
          })
      })
    }
  }

  fnFilterSpecification = (specificationGroups: SpecificationGroup[] = []) => {
    const { autoExpenseForbiddenFeaturePower } = this.props
    if (!autoExpenseForbiddenFeaturePower) {
      return specificationGroups
    }
    return specificationGroups.filter(specification => {
      specification.specifications = specification.specifications.filter(specification => {
        return !(specification.type === 'expense' || specification.type === 'loan')
      })
      return !!specification.specifications.length
    })
  }

  onSubmit = debounce((value: string = '') => {
    const activeKeys: string[] = []
    const searchList: any[] = []
    const specification_group = get(this.props, 'specification_group') || []
    const specification_recently = get(this.props, 'specification_recently') || []
    const hasValue = !!value.trim().length
    const { hiddenFooter } = this.state
    const assiciationSpecifications = get(this.props, 'assiciationSpecifications') || []

    if (hiddenFooter) {
      const specifications = assiciationSpecifications.filter((spec: any) => spec.name.includes(value))
      this.setState({
        assiciationSpecifications: specifications
      })
      return
    }

    specification_group.forEach((group: any) => {
      const { id } = group
      const groupName = getSpecificationName(group)
      let { specifications = [] } = group
      if (!specifications.length) {
        return
      }
      activeKeys.push(id)
      if (hasValue && groupName.includes(value)) {
        searchList.push(group)
      } else {
        if (hasValue) {
          specifications = specifications.filter((spec: any) => {
            const speName = getSpecificationName(spec)
            return speName.includes(value)
          })
        }
        specifications.length && searchList.push({ ...group, specifications })
      }
    })

    if (!hasValue && specification_recently.length) {
      activeKeys.unshift('recently')
      searchList.unshift({
        id: 'recently',
        name: i18n.get('最近使用'),
        specifications: specification_recently.slice(0, 4)
      })
    }

    this.setState({
      activeKeys,
      specification_group: this.fnFilterSpecification(searchList),
      isLoading: false
    })
  }, 300)

  onChange = (value: string) => {
    this.setState({ searchText: value })
    this.onSubmit(value)
  }

  switchPanel = (key: string[]) => {
    this.setState({ activeKeys: key })
  }

  renderHighlight = (value: string) => {
    return (
      <Highlighter
        className="lighter"
        searchWords={[this.state.searchText]}
        textToHighlight={value || i18n.get('[无标题]')}
        highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
        autoEscape={true}
      />
    )
  }

  renderItem = (spec: any, index: number) => {
    const IconCompo = getSpecificationIcon(spec.icon)
    const name = getSpecificationName(spec)
    return (
      <div key={index}>
        <div className="spec-item" key={index} onClick={this.handleOK(spec)}>
          <div className="spec-item-wrapper pos-r">
            <div className="spec-item-icon-bg" style={{ backgroundColor: spec.color }} />
            <IconCompo className="spec-item-icon" style={{ color: spec.color }} />
            <div className="spec-item-title">{this.renderHighlight(name)}</div>
          </div>
        </div>
        {index % 2 ? <div className="clearfix" key={`c${index}`} /> : null}
      </div>
    )
  }
  renderSearch() {
    return (
      <div className={styles['home3-creact-modal-search']}>
        <div className="search-icon">
          <OutlinedEditSearch />
        </div>
        <Input
          clearable={true}
          value={this.state.searchText}
          placeholder={i18n.get('搜索单据模板')}
          onChange={this.onChange}
          onClear={() => this.onChange('')}
        />
      </div>
    )
  }

  renderReEditBill() {
    const handleClick = () => {
      const ls = localStorage.getItem(DRAFT_ID)
      this.props.layer.emitCancel()
      const value = updateDetailIndex(JSON.parse(ls))
      const {
        specificationId: { type }
      } = value
      api.go(`/bill/localStorage/${type}`, false)
      startOpenFlowPerformanceStatistics()
    }

    return (
      <div className={styles['home3-re-edit-bill']} onClick={handleClick.bind(this)}>
        <div className="content">
          <img className="pre-edit-bg" src={PreEditBg} />
          <div className="text">
            <span className="warning-icon">
              <FilledTipsWarning fontSize={14} />
            </span>
            {i18n.get('你上一次的编辑还未完成，点击可继续编辑')}
          </div>
          <div>
            <OutlinedDirectionRight />
          </div>
        </div>
      </div>
    )
  }

  handleAccordionCollapse = (groupId: string) => {
    this.setState(({ collapseMap = {} }) => {
      const collapseMapClone = cloneDeep(collapseMap)
      collapseMapClone[groupId] = collapseMap[groupId] === undefined ? false : !collapseMap[groupId]
      return {
        collapseMap: collapseMapClone
      }
    })
  }

  renderContent() {
    const { collapseMap = {}, specification_group, isLoading } = this.state
    const group = specification_group
      .map(group => {
        group.specifications = group.specifications.filter(
          v => v.type !== 'settlement' && v.type !== 'reconciliation' && !v.id?.includes('system:对账单')
        )
        return group
      })
      .filter(v => v.specifications.length)
    return (
      <div className={styles['home3-creact-modal-content']}>
        <div className="accordion">
          {!isLoading ? (
            group.length !== 0 ? (
              group.map((group: any) => {
                const isNotCollapsed = collapseMap[group.id] || collapseMap[group.id] === undefined
                const groupName = getSpecificationName(group)
                return (
                  <div className="accordion-item" key={group.id}>
                    <div className="accordion-header" onClick={() => this.handleAccordionCollapse(group.id)}>
                      {this.renderHighlight(groupName)}
                      <div className="direction-icon">
                        <OutlinedDirectionRight rotate={isNotCollapsed ? -90 : 90} />
                      </div>
                    </div>
                    {isNotCollapsed && (
                      <div className="accordion-content">{group.specifications.map(this.renderItem)}</div>
                    )}
                  </div>
                )
              })
            ) : (
              <EmptyWidget size={100} type="noCentent" />
            )
          ) : (
            <div className="accordion-skeleton">{Array.from({ length: 3 }, _ => this.renderSkeleton())}</div>
          )}
        </div>
      </div>
    )
  }

  renderSkeleton() {
    return (
      <div className="accordion-skeletonItem">
        <div className="accordion-skeletonHang">
          <SkeletonTitle animated />
          <SkeletonTitle animated />
        </div>
        <div className="accordion-skeletonHang">
          <SkeletonTitle animated />
          <SkeletonTitle animated />
        </div>
      </div>
    )
  }

  renderAssociationContent() {
    const { assiciationSpecifications } = this.state
    return (
      <div className={styles['home3-creact-modal-content']}>
        {assiciationSpecifications.length !== 0 ? (
          assiciationSpecifications.map((spec: any, index: number) => {
            return this.renderItem(spec, index)
          })
        ) : (
          <EmptyWidget size={100} type="noCentent" />
        )}
      </div>
    )
  }

  renderFooter() {
    const funcData = getFuncDetail()
    return (
      <div className={styles['home3-creact-modal-footer']}>
        {funcData.map((el, idx) => {
          const IconCompo = el.icon
          return (
            <div
              className="func-item"
              key={idx}
              onClick={() => {
                el.fn()
                this.handleCancel()
              }}
            >
              <div className="func-item-icon">
                <IconCompo fontSize={28} />
              </div>
              <span className="func-item-name">{el.name}</span>
            </div>
          )
        })}
      </div>
    )
  }

  renderCreatingContent = () => {
    const { isRoute, hiddenFooter } = this.state
    const ls = localStorage.getItem(DRAFT_ID)
    return <>
      {this.renderSearch()}
      {ls && ls !== 'undefined' && !hiddenFooter && this.renderReEditBill()}
      {hiddenFooter ? this.renderAssociationContent() : this.renderContent()}
      {!isRoute && !hiddenFooter && this.renderFooter()}
    </>

  }

  render() {
    const { isRoute } = this.state
    return (
      <div className={`modal-popup-content ${isRoute ? styles['is-route-page'] : ''}`}>
        {isRoute ? (
          void 0
        ) : (
          <div className="modal-action-bar">
            <div className="modal-action-bar-btn" onClick={this.handleCancel}>
              {i18n.get('取消')}
            </div>
            <span className="modal-action-bar-title">{i18n.get('新建单据')}</span>
          </div>
        )}
        {this.renderCreatingContent()}
      </div>
    )
  }
}
