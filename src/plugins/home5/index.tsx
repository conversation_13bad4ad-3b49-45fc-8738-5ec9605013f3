/*
 * @Author: Onein
 * @Date: 2019-02-26 16:22:00
 * @Last Modified by: Onein
 * @Last Modified time: 2020-12-Fr 12:06:11
 */
import { app as api } from '@ekuaibao/whispered'
import loadable from '@loadable/component'
import { localStorageSet } from '../../lib/util'

export default [
  {
    id: '@home5',
    store: { key: '@home5', value: () => require('./home5.store').default },
    'get:data:delay'() {
      return Promise.all([
        api.dataLoader('@common.isWhiteList').load(),
        api.dataLoader('@common.departments').reload(),
        // api.invokeService('@home:get:specification:with:version'),
        api.dataLoader('@common.payerInfo').load(),
        api.invokeService('@common:get:organization:config'),
        api.invokeService('@common:get:organization:staffIds:visibility'),
        api.store.dispatch('@home5/getArchivedAndPaidList')(),
      ]).catch((e) => {
        console.warn(e)
      })
    }
  },
  {
    id: '@homePage',
    store: { key: '@homePage', value: () => require('../home5/homePage/HomeCardManage/HomeCardManage.store').default }
  },
  {
    point: '@@layers',
    prefix: '@home5',
    onload: () => require('./layers').default
  },
  {
    point: '@@layers',
    prefix: '@@system',
    onload: () => require('./remote-layers').default
  },
  {
    path: '/home5',
    dependencies: ['@common', '@auth-check', '@home5', '@homePage'],
    ref: '/',
    exact: true,
    isMainLayout: true, //todo fix_tabber
    onfirst: async (app: any) => {
      await app.store.dynamic('@marketADBanner', () => require('./marketBanner.store').default).load('@marketADBanner')
    },
    onload: async (app: any) => {
      const CACHE_KEY = 'isStandardEntries';
      const selectEntries = (isStandard: boolean) => isStandard ? import('./standard/home5') : import('./homePage');
      const cacheFlag = async () => {
        const powers = await app.dataLoader('@common.powers').load();
        if (powers?.powerCodeMap) {
          localStorageSet(CACHE_KEY, powers.powerCodeMap.includes('118888') ? '118888' : '0')
        }
        return powers && powers.powerCodeMap && powers.powerCodeMap.includes('118888')
      }

      const isStandard = await cacheFlag();
      return selectEntries(isStandard);
    },
    onbefore: async (app: any, next: Function) => {
      api.invokeService('@layout:set:header:right', { text: i18n.get('客服'), show: true })

      if (window.__PLANTFORM__ !== 'KD_CLOUD' && window.__PLANTFORM__ !== 'APP') {
        api.invokeService('@auth-check:check:auth')
      }

      // APP 兼容 tmc 独立版显zzz示
      if (window.__PLANTFORM__ === 'APP') {
        api
          .dataLoader('@common.powers')
          .load()
          .then((powers: any) => {
            if (powers && powers.TMC) {
              app.go('/mall', true)
            }
          })
      }

      next()
    }
  },
  {
    path: '/mc-error',
    ref: '/',
    exact: true,
    onload: () => import('./mc-error')
  },
  {
    path: '/shared-error',
    ref: '/',
    exact: true,
    onload: () => import('./shared-error')
  },
  {
    path: '/homeEditableView',
    ref: '/',
    exact: true,
    onload: () => import('./homeEditableView')
  },
  {
    path: '/billList',
    ref: '/',
    exact: true,
    dependencies: ['@common', '@auth-check', '@home5'],
    cache: true,
    livePath: [
      '/bill/:type/:id',
      '/bills-segment/:type',
      '/detail/:id',
      '/detail/:id/:sourcePage',
      '/detail/:id/:carbonCopyReadType/:carbonCopyType/:carbonCopyId/:sourcePage',
      '/paiddetail/:id/:isAlwaysPrint/:sourcePage',
      '/submitter/:ownerId/:flowId',
      '/flow-plan-readonly/:type/:id',
      '/flow-plan-readonly/:type/:id/:isReadOnly',
      '/flow-report-detail/:flowId/:budgetId/:fromNodeId/:nodeId/:periodTime',
      '/used-detail',
      '/submitterLoanDetail/:id'
    ],
    onReappear: () => {
      api.emit('update:cache:view:data')
    },
    onload: () => import('./billList')
  },
  {
    path: '/home5Create/:tppBusinessType',
    ref: '/',
    exact: true,
    dependencies: ['@common', '@auth-check', '@home5', '@homePage'],
    onload: () => import('./home5Create')
  },
  {
    path: '/home5-guide',
    ref: '/',
    exact: true,
    onload: () => import('./elements/home5-guide'),
    dependencies: ['@home5', '@homePage']
    // store 的注册放在一个地方, 使用dependencies关联,
    // 不要在每一个地方都写一遍.
  },
  {
    path: '/home5-loading',
    ref: '/',
    onload: () => import('./elements/home5-loading'),
    dependencies: ['@home5', '@homePage']
  },
  {
    path: '/home5-payee-info',
    ref: '/',
    onload: () => import('./elements/home5-payee-info')
  },
  {
    path: '/bills-segment/:type',
    cache: true,
    livePath: [
      '/paiddetail/:id/:isAlwaysPrint/:sourcePage',
      '/flow-plan-readonly/:type/:id',
      '/flow-plan-readonly/:type/:id/:isReadOnly',
      '/flow-report-detail/:budgetRootId/:flowId/:budgetId/:fromNodeId/:nodeId/:periodTime',
      '/used-detail'
    ],
    ref: '/',
    onload: () => import('./elements/cards/mybill/CompletedAndUnConfirmedSeg')
  },
  {
    path: '/bills-create/:isRoute',
    ref: '/',
    onload: () => import('./layers/home5-create-modal')
  },
  {
    path: '/home5-preview-setting',
    ref: '/',
    exact: true,
    onload: () => import('./elements/home5-preview-setting')
  },
  {
    path: '/e-card-expense-page',
    ref: '/',
    exact: true,
    onload: () => import('./elements/e-card-expense-page')
  },
  {
    path: '/e-card-expense-page/:feetypeId',
    ref: '/',
    exact: true,
    onload: () => import('./elements/e-card-expense-page')
  },
  {
    path: '/new-bill',
    ref: '/',
    exact: true,
    onload: () => import('./new-bill/home3-create-page')
  },
  {
    resource: '@home5',
    value: {
      ['elements/cards/mybill/cardList_myBill']: loadable(() => import('./elements/cards/mybill/cardList_myBill')),
      ['elements/cards/mybill/cardList_myBill.BillQuickEntrance']: loadable(() =>
        import('./elements/cards/mybill/cardList_myBill.BillQuickEntrance')
      ),
      ['elements/cards/mybill/cardList_myBill.CardList_Bill']: loadable(() =>
        import('./elements/cards/mybill/cardList_myBill.CardList_Bill')
      ),
      ['elements/cards/mybill/cardList_myBill.CardList_BillItem']: loadable(() =>
        import('./elements/cards/mybill/cardList_myBill.CardList_BillItem')
      ),
      ['elements/cards/auditpending/cardListAuditPending']: loadable(() =>
        import('./elements/cards/auditpending/cardListAuditPending')
      ),
      ['elements/cards/auditpending/CardListItem']: loadable(() =>
        import('./elements/cards/auditpending/CardListItem')
      ),
      ['elements/cards/TripItemCard']: loadable(() => import('./elements/cards/TripCardItem')),
      ['elements/cards/TripErrorBoundary']: loadable(() => import('./elements/cards/TripErrorBoundary')),
      ['images/car.svg']: require('./images/car.svg'),
      ['images/flight.svg']: require('./images/flight.svg'),
      ['images/hotel.svg']: require('./images/hotel.svg'),
      ['images/train.svg']: require('./images/train.svg'),
      ['images/no_travel.svg']: require('./images/no_travel.svg'),
      ['elements/cards/paymentReview/CardListItem']: loadable(() =>
        import('./elements/cards/paymentReview/CardListItem')
      ),
      ['elements/cards/cardItems/BillList']: loadable(() => import('./elements/cards/cardItems/BillList')),
      ['EmptyWidget']: loadable(() => import('./homePage/HomeCardManage/elements/EmptyWidget')),
      ['util/billItemHelper']: require('./util/billItemHelper'),
      ['elements/cards/cardItems/BillItem']: loadable(() => import('./elements/cards/cardItems/index')),
      ['SkeletonList']: loadable(() => import('./util/SkeletonList')),
      ['todoList/item']: loadable(() => import('./homePage/HomeCardManage/elements/TodoItem')),
      ['ECardDisabled']: loadable(() => import('./homePage/HomeCardManage/ECardDisabled')),
      ['ECardEnable']: loadable(() => import('./homePage/HomeCardManage/ECardEnable')),
      ['TripCardItem/formatHelper']: require('./elements/cards/TripCardItem/formatHelper')
    }
  }
]
