/**
 * Created by <PERSON><PERSON> on 2019/2/27.
 */

import { uuid } from '@ekuaibao/helpers'
// @ts-ignore
import styles from './homeEditableView.module.less'
import './elements/home5-drag-view.less'
import React from 'react'
import <PERSON>hance<PERSON><PERSON>leHook from '../../lib/EnhanceTitleHook'
import Home5DragView from './elements/home5-drag-view'
import Home5BacklogView from './elements/home5-backlog-view'
import Home5AgainGuideView from './elements/home5-againGuide-view'
import { arrayMove } from 'react-sortable-hoc'
import { app as api } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import sortBy from 'lodash/sortBy'
import clone from 'lodash/clone'
import { Button } from '@hose/eui-mobile'
import { hasInArray } from './layers/card_group_edit_modal/util'
import * as mobx from 'mobx'
import { SortableContainer } from 'react-sortable-hoc'
import { CodeType, IMalCard, mallCardDataLength } from './home5.store'
import { EnhanceConnect } from '@ekuaibao/store'
const EkbSortableContainer = SortableContainer(({ children, cName }: any) => <ul className={cName}>{children}</ul>)

export interface Props {
  cardList: any[]
  cardBacklogList: any[]
  mallCardDataList: IMalCard[]
  mallCardDataListNew: IMalCard[]
  isNewMall: boolean
  homePageVersion: any
  editPersionSelectCards?: any[]
  editPersionUnSelectCards?: any[]
}

interface State {
  cardList: any[]
  cardBacklogList: any[]
  blur: Boolean
  buttonLoading:boolean
}
// @ts-ignore
@EnhanceTitleHook(i18n.get('编辑首页'))
// @ts-ignore
@connect(store => ({
  cardList: store.states['@home5'].cardList,
  cardBacklogList: store.states['@home5'].cardBacklogList,
  editPersionSelectCards: store.states['@homePage'].editPersionSelectCards,
  editPersionUnSelectCards: store.states['@homePage'].cardBacklogList,
  mallCardDataList: store.states['@home5']?.mallCardDataList || [],
  homePageVersion: store.states['@homePage']?.hasNewHomeTemplateVersion,
  mallCardDataListNew: store.states['@homePage']?.mallCardDataList || []
}))
@EnhanceConnect((state: any) => ({
  isNewMall: state['@common'].powers.newMall,
}))
export default class HomeEditableView extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props)
    const { cardList = [], cardBacklogList = [], editPersionSelectCards, editPersionUnSelectCards } = props
    const selectCards = cardList?.length ? cardList : editPersionSelectCards
    const unSelectCards = cardBacklogList?.length ? cardBacklogList : editPersionUnSelectCards
    this.state = { cardList: selectCards, cardBacklogList: unSelectCards, blur: false ,buttonLoading:false }
  }

  componentDidMount() {
    api.store.dispatch('@homePage/getCardMetaData')()
    api.store.dispatch('@homePage/checkHomePageVersion')()
  }

  onSortEnd = (params: { oldIndex: number; newIndex: number }) => {
    const { cardList } = this.state
    const { oldIndex, newIndex } = params
    let oldRealIndex = 0
    let newRealIndex = 0
    const displayedList = cardList.filter(card => !card.pid)
    for (let i = 0; i < cardList.length; i++) {
      if (cardList[i].id === displayedList[oldIndex].id) {
        oldRealIndex = i
      }
      if (cardList[i].id === displayedList[newIndex].id) {
        newRealIndex = i
      }
    }
    this.setState({
      cardList: arrayMove(cardList, oldRealIndex, newRealIndex)
    })
  }

  handleSubmit = () => {
    const { cardList } = this.state
    this.setState({buttonLoading:true})
    let cardListClone = clone(cardList.slice())
    cardListClone = cardListClone
      .filter(card => !card.pid)
      .map((card, idx) => {
        // delete card.detail
        return {
          ...mobx.toJS(card),
          weight: (idx + 1) * 100 + 1000,
          detail: undefined
        }
      })
      .concat(cardListClone.filter(card => card.pid))
      .map(card => {
        card.selected = true
        return card
      })
      api.store.dispatch('@homePage/updatePersonalHomePage')(cardListClone, () => {
        this.handleCancel()
      })
  }

  handleCancel = () => {
    api.go(-1, false)
  }

  handleAdd = (card: any) => {
    card.pid = '' // with caution
    card.selected = true
    let { cardList, cardBacklogList } = this.state
    cardList.push(card)
    const index = cardBacklogList.findIndex(el => el.id === card.id)
    cardBacklogList.splice(index, 1)
    cardBacklogList = sortBy(cardBacklogList, item => item.weight)
    this.setState({ cardList, cardBacklogList })
  }

  handleMinus = (card: any) => {
    card.pid = ''
    const { cardList, cardBacklogList } = this.state
    const isGroup = card.type === 'GROUP'
    // 移除 card 和它可能存在的子卡片
    const list = cardList.filter(c => c.id !== card.id && c.pid !== card.id)
    const backlog = cardBacklogList
      .concat(isGroup ? cardList.filter(c => c.pid === card.id) : [card])
      .sort((a, b) => a.weight - b.weight)

    this.setState({ cardList: list, cardBacklogList: backlog })
  }

  handleOpenEditModal = (data: any) => {
    if (data.type === 'GROUP') {
      this.handleOpenEditGroupModal(data.id, data.label)
      return
    }
    this.setState({ blur: true }, () => {
      api.open('@home5:CardEditModal', { data: clone(data) }).then((card: any) => {
        if (card === 'cancel') {
          return this.setState({ blur: false })
        }
        let { cardList } = this.state
        const index = cardList.findIndex(el => el.id === card.id)
        cardList[index] = card
        this.setState({ cardList, blur: false })
      })
    })
  }

  handleOpenEditGroupModal = (pid?: string, groupLabel?: string) => {
    let { cardList, cardBacklogList } = this.state
    const showMallCard = this.getShowMallCard()
    cardList = showMallCard ? cardList : cardList.filter(item => item.code !== CodeType.mall)
    cardBacklogList = showMallCard ? cardBacklogList : cardBacklogList.filter(item => item.code !== CodeType.mall)
    this.setState({ blur: true }, () => {
      api
        .open('@home5:CardGroupEditModal', { pid, label: groupLabel, cardList, cardBacklogList })
        .then((result: { data: any[]; label: string } | 'cancel') => {
          if (result === 'cancel') {
            return this.setState({ blur: false })
          }
          // 数据处理步骤：
          // 若 pid 为空，表示新增卡片分组，在原 cardList 中加入新分组数据即可与修改卡片的逻辑相似
          // 1. 新增卡片时，在 cardList 中插入新分组的信息（需求上要求插入在段首）
          // 2. 所有根卡片 = 抽取所有没有 pid 的根卡片，重排 weight (这批卡片会展示在『显示卡片』里)
          // 3. 所有子卡片 = cardList 中所有未编辑卡片组的子卡片 + 编辑后的子卡片（这批卡片趁现在重排 weight 并填充 pid）
          // 4. 剩余 backlog = (cardList 中淘汰的卡片) + (原 backlog 中捡剩的卡片)
          // 5. 新 cardList = 所有根卡片.concat(所有子卡片)

          // 展开变量
          const { data, label } = result
          let enlargedList = mobx.toJS(cardList) // mobx 下的 Observable Array 和各种展开操作冲突，所以这里处理下

          // 1. 插入新分组
          if (!pid) {
            pid = uuid(8)
            enlargedList = [
              {
                id: pid,
                label,
                type: 'GROUP',
                icon: '',
                color: '',
                showType: 'LIST',
                deviceType: 'MOBILE',
                selected: true,
                pid: '',
                dynamicSupportValue: false,
                code: pid
              },
              ...mobx.toJS(cardList)
            ]
          }

          // 2. 会被显示的根节点卡片们（尤其包括卡片组）
          const rootCards = enlargedList
            // 所有不在分组中的卡片
            .filter(card => !card.pid && !hasInArray(data, c => c.id === card.id))
            // 重排 weight, 更新 label
            .reduce((acc, card) => {
              if (card.id === pid) {
                card.label = label
              }

              card.weight = (acc.length + 1) * 100 + 1000
              acc.push(card)
              return acc
            }, [])

          // 3. 隐藏在卡片组中的卡片们
          const leafCards = enlargedList
            // 未被编辑的卡片组的子卡片
            .filter(card => card.pid && card.pid !== pid)
            // 新增/编辑 出来的新子卡片，需要先重排 weight 并填充 pid
            .concat(
              data.reduce((acc, card) => {
                card.weight = (acc.length + 1) * 100 + 1000 // 重排 weight
                card.pid = pid // 新增卡片组的时候，子卡片并没有这个信息
                acc.push(card)
                return acc
              }, [])
            )

          // 4. 新剩余卡片
          const backlog = enlargedList
            // 原 cardList 中被剔除的卡片
            .filter(
              card => card.pid === pid && !hasInArray(data, c => c.id === card.id) // 原来在卡片分组中 // 现在不在分组中
            )
            // 上面的卡片清除 pid 信息
            // （注：这里的 map 和上面的 filter 可以使用一个 reduce 处理完，但是分开写可读性好一些，数据量少，性能没什么影响）
            .map(card => {
              card.pid = ''
              return card
            })
            // 原 backlog 中未被添加的卡片还保留
            .concat(cardBacklogList.filter(card => !hasInArray(data, c => c.id === card.id)))

          // 5. 新 cardList
          const list = rootCards.concat(leafCards)

          this.setState({ cardList: list, cardBacklogList: backlog, blur: false })
        })
    })
  }

  renderActionBar = () => {
    const {buttonLoading} = this.state
    return (
      <div className={styles['homeEditableView-actionBar']}>
        <Button size="middle" loading={buttonLoading} loadingText={i18n.get("加载中...")} block onClick={this.handleSubmit}>
          {i18n.get('完成')}
        </Button>
      </div>
    )
  }

  getShowMallCard = () => {
    const {  isNewMall, mallCardDataListNew } = this.props
    return mallCardDataListNew.length >= mallCardDataLength && isNewMall
  }

  render() {
    let { cardList, cardBacklogList, blur } = this.state
    const { homePageVersion } = this.props
    let className = styles['homeEditableView-wrap'] + ' homeEditableView-sortable-wrap inertial-rolling'
    if (blur) {
      className += ' filter-blur'
    }
    const showMallCard = this.getShowMallCard()
    cardList = showMallCard ? cardList : cardList.filter(item => item.code !== CodeType.mall)
    cardBacklogList = showMallCard ? cardBacklogList : cardBacklogList.filter(item => item.code !== CodeType.mall)
    return (
      <>
        <EkbSortableContainer onSortEnd={this.onSortEnd} lockAxis={'y'} cName={className} useDragHandle>
          <Card
            title={
              <>
                <span>{i18n.get('显示卡片')}</span>
                <div onClick={() => this.handleOpenEditGroupModal()}>
                  <span className="add-group-icon">+</span>
                  <span>{i18n.get('添加分组')}</span>
                </div>
              </>
            }
          >
            <Home5DragView
              cardList={cardList}
              list={cardList.filter(card => !card.pid)}
              fnSetState={this.setState}
              fnMinusCard={this.handleMinus}
              fnEditCard={this.handleOpenEditModal}
            />
          </Card>
          <Card title={i18n.get('隐藏卡片')}>
            <Home5BacklogView list={cardBacklogList} fnAddCard={this.handleAdd} />
          </Card>
          <Card title={i18n.get('设置')}>
            <Home5AgainGuideView
              homePageVersion={homePageVersion}
            />
          </Card>
        </EkbSortableContainer>
        {this.renderActionBar()}
      </>
    )
  }
}

interface CardProps {
  title: React.ReactNode
}

const Card: React.FC<CardProps> = ({ title, children }) => {
  return (
    <>
      <div className="homeEditableView-header">{title}</div>
      {children}
    </>
  )
}
