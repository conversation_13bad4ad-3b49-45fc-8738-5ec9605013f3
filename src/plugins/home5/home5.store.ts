/**************************************
 * Created By LinK On 2019/3/4 15:29.
 **************************************/

import { action } from '@ekuaibao/mobx-store'
import { QuerySelect } from 'ekbc-query-builder'
import { get } from 'lodash'
import { computed, observable, runInAction, toJS } from 'mobx'
import { app as api } from '@ekuaibao/whispered'
import { Resource } from '@ekuaibao/fetch'
import { AuditDataProps, AuditItemProps } from './elements/cards/auditpending/cardListAuditPending'
import { MyBillCardItemProps } from './elements/cards/mybill/cardList_myBill'
import { Home5CardType } from './staticUtil'
import { Fetch } from '@ekuaibao/fetch'
import { mergeGroups } from './util/merge-groups'
import { session } from '@ekuaibao/session-info'
import moment from 'moment'
const TRAVEL_CACHE = 'TRAVEL_CACHE_NEW'
const getCards = new Resource('/api/menu/v2/menus/byDeviceType')
const businesssummary = new Resource('/api/menu/v2/businesssummary')
const getConfigs = new Resource('/api/menu/v2/menuconfigs')
const changeStaffMenus = new Resource('/api/menu/v2/staffMenus')
const questionnaires = new Resource('/api/menu/v2/questionnaires')
const banners = new Resource('/api/menu/v2/banners')
const myTrip = new Resource('/api/v2/datalink/searchTravelManagementDataLink')
const myTripAssist = new Resource('/api/v2/datalink')
const myTripType = new Resource('/api/v2/datalink/entity')
const agreement = new Resource('/api/v2/staff/agreement')
const vorganization = new Resource('/api/v1/organization/')
const users = new Resource('/api/app/account/users/')
const myTripAssistV2 = new Resource('/api/v2/datalink/searchAboutToTravelTrip')
const hoseMallOrder = new Resource('/api/tpp/v2/hoseMall/order/searchAboutToTravelOrder')
const approveOA = new Resource('/api/extension/approve/login')
const mallCardData = new Resource('/api/message/v1/messageCenterConfig/getOperation')
const travelAssist = new Resource('/api/tpp/v2/travelAssist')
const travelManage = new Resource('/api/tpp/v2/travelManagement/getTravelManagementConfig')
const clusterCorpations = new Resource('/api/v1/group/client/permission/corporations')

export enum CodeType {
  nu = '',
  myBill = 'myBill',
  myLoan = 'myLoan',
  myRequisition = 'myRequisition',
  auditPending = 'auditPending',
  auditPayment = 'auditPayment',
  waitSending = 'waitSending',
  receiveExpress = 'receiveExpress',
  auditCarbonCopy = 'auditCarbonCopy',
  todoPrint = 'todoPrint',
  privateCar = 'privateCar',
  recordExpends = 'recordExpends',
  authorizationManage = 'authorizationManage',
  dataLinkEntity = 'dataLinkEntity',
  approve = 'approve', // charge: OA审批
  mall = 'MALL',
  completed = 'completed', // 已完成
  approved = 'approved', // 已审批
  paymentHistory = 'paymentHistory', // 还款记录
  waitInvoiceDetail = 'waitInvoiceDetail',
  approvePermission = 'approvePermission',
  orderConfirm = 'orderConfirm',
  eCard = 'eCard2'
}

export enum PromptType {
  number = 'NUMBER',
  money = 'MONEY'
}

const noon = (data?: any) => { }

export interface PromptData {
  value: number | string
  type?: PromptType
}

export interface CardDataProps {
  code?: CodeType
  data?: any | AuditDataProps
  list?: MyBillCardItemProps[] | AuditItemProps[] | any[]
  prompt?: PromptData
}

export interface CardProps extends StringAnyProps {
  code: CodeType
  dynamicSupportValue: boolean
  id: string
  label: string
  showType?: Home5CardType
  weight?: number
  detail?: CardDataProps
}

interface Questionnaires {
  id: string
  codes: string[]
}

interface Agreement {
  userId: string
  cellPhone: string
}

interface CorpWord {
  variable: string
  replaceKey?: string
  language: string
}

interface SetUpObject {
  logoUrl: string
  logoCustom: boolean
  wordCustom: boolean
  corpWords?: CorpWord[]
  brandName?: string
  logoUrlimg?: string
}

export interface IMalCard {
  picture: string
  title: string
  url: string
}

export const cardDefaultValue = {
  code: CodeType.nu,
  dynamicSupportValue: false,
  id: '',
  label: ''
}

// 首页商城卡片为1的时候显示
export const mallCardDataLength: number = 1

function getFileId(key: string) {
  return Fetch.GET(`/api/v1/attachment/attachments/$${key}`)
}

function setTitle(el: any, prefix: string) {
  const type = get(el, `form.订单类型`, '用车')
  const vMap: any = {
    '飞机': '飞往',
    '火车': '前往',
    '酒店': '入住',
    '用车': '出行'
  }
  let title = '即将' + vMap[type]

  const toCity = get(el, type === '酒店' ? `form.${prefix}出发城市` : `form.${prefix}到达城市`, '')
  const startTime = get(el, type === '酒店' ? `form.${prefix}入住时间` : `form.${prefix}出发时间`, '')
  let startStr = '即将'

  const orderStatus = get(el, `form.${prefix}订单状态`)

  if (type === '酒店' && orderStatus === '已入住') {
    startStr = '当前'
  }

  if (startTime && ['火车', '酒店'].includes(type)) {
    const startDay = moment(Number(startTime)).format('YYYY-MM-DD')
    const daysDiff = moment(startDay).diff(moment().format('YYYY-MM-DD'), 'days')
    const timeDiff = moment(Number(startTime)).diff(moment(), 'minutes')
    const timeH = Math.floor(timeDiff / 60) % 24
    const timeM = timeDiff % 60
    if (timeM > 0) {
      startStr = timeM + '分钟后'
    }
    if (timeH > 0) {
      startStr = timeH + '小时后'
    }
    if (daysDiff > 0) {
      startStr = daysDiff === 1 ? '明天' : daysDiff + '天后'
    }
  }

  if (type && toCity && startStr) {
    title = startStr + vMap[type] + toCity
  }
  return title
}

export default class Home5Store {
  @observable
  paidList: any[]

  @observable
  corpList: any[] = []

  @observable
  externalCorpList: any[] = []

  @observable
  archivedList: any[]

  @observable
  cardList: CardProps[] = []
  _cardList: any[] = []
  @observable isFirstLoadCardList = true

  @observable
  showTripCard = false
  @observable
  entityPrefixForOrder: any = null
  @observable
  entityPrefixForTrip: any = null

  @observable
  menuTripData: any = null // menuTripData 出行 item 数据

  @observable
  title: string = '即将出行' // 即将出行卡 标题

  @observable
  recentTripData: any = null // 即将出行的数据

  @observable
  tripData: any = [] // 行程数据

  _cardListSelected: any[]

  @computed get isUserConfig() {
    return this._cardList.length > 0
  }

  @observable
  standardCardListAuditPending: any

  @observable
  standardCardListMyBill: any

  @observable
  cardBacklogList: any[]

  @observable
  cardConfigs: any[]

  @observable
  guideQuestionnaires: any[]

  @observable
  banners: any[]

  @observable
  payeeInfo: any

  @observable
  segmentActiveKey: number

  @observable
  trips: any[]

  @observable
  tripType: any[]

  @observable
  corpStyle: SetUpObject

  @observable
  logoUrl: string

  @observable
  isReaded: number = 1

  @observable
  tripAssistData: any = []

  isOrder: boolean = false

  orderData: any = []

  @observable
  mallCardDataList: IMalCard[] = []

  @observable
  home5BannerData: any[] = []

  @observable
  clusterCorpList: any[] = []

  @observable
  homePageVersion: any = undefined

  @action async getArchivedAndPaidList() {
    const result = await api.invokeService('@common:search:bill', {
      filterBy: this.filterSystemBillStr(),
      orderBy: [{ value: 'updateTime', order: 'DESC' }]
    })

    const arr = result.items || []
    const paidList = arr.filter((el: any) => el.flow.state === 'paid')
    const archivedList = arr.filter((el: any) => el.flow.state === 'archived')

    runInAction(() => {
      this.paidList = paidList
      this.archivedList = archivedList
    })
  }

  // charge: OA审批
  // 跳转至OA
  @action async redirectApproveOA() {
    const result = await approveOA.POST('')
    const url = get(result, 'id')
    if (url) {
      api.invokeService('@layout:open:link', url)
    }
  }

  @action async getCorporations(data?: any) {
    const result = await vorganization.GET('corporations/andCount', data)
    const corpList = result.items || []
    const externalCorpList = result.externalItems || []
    runInAction(() => {
      this.corpList = corpList
      this.externalCorpList = externalCorpList
    })
  }

  // 获取集群企业列表
  @action async getClusterCorporations(data?: any) {
    const result = await clusterCorpations.GET('', data)
    const corpList = result.items || []
    // return corpList
    runInAction(() => {
      this.clusterCorpList = corpList
    })
  }

  @action async getV1UserInfo(data?: any) {
    const result = await users.GET('userInfoByToken', data)
    return result
  }

  // 记录切换企业行为
  @action async switchcorporationForLog(corpId: string) {
    return await Fetch.POST(`/api/v1/organization/staffs/switchcorporation/$${corpId}`)
  }

  @action // 清空卡片列表
  clearCardList(fn = noon) {
    this.cardList = []
    fn && fn()
  }

  @action // 因数据统计 UI 暂时下架,so 查询 count 请求暂时不再发送
  async getCountByFilter(param: any, filterBy: string, fn = noon) {
    const query = new QuerySelect()
      .filterBy('type != "permit"')
      .filterBy('state == "APPROVING"')
      .filterBy(filterBy)
      .value()
    const res = await businesssummary.POST('/filterBy', query)

    const auditPending = this._cardList.find((item: any) => {
      return item.code === param.code
    })
    if (auditPending && auditPending.detail) {
      auditPending.detail.data.forEach((element: any, idx: number) => {
        if (idx === param.idx) {
          return (element.count = res.count || 0)
        }
      })
    }
    fn && fn(res.count || 0)
    return this.dealCardList([], noon, fn)
  }

  // 行程卡片
  @action
  async getNewTripData(card: any, dataLink: any, callBack: (card: any) => void) {
    const query: any = {
      query: {
        orderBy: [{ value: 'createTime', order: 'DESC' }]
      },
      limit: { start: 0, count: 3 },
      filter: 'notBuy'
    }
    try {
      const res = this.upgradeToMicroservice
        ? await myTripAssist.POST('/searchForTripAssistMic', query)
        : await myTripAssist.POST('/searchForTripAssist', query)
      const { total, data, entityPrefixForTrip, entityPrefixForOrder } = res
      dataLink.dataLinkCount = total
      card.detail = dataLink
      if (callBack) {
        callBack(card)
      }
      this.entityPrefixForOrder = entityPrefixForOrder
      this.entityPrefixForTrip = entityPrefixForTrip
      this.tripData = data?.slice(0, 3)
      this.menuTripData = card
      this.dealCardList(this._cardList)
    } catch (error) { }
  }

  upgradeToMicroservice: boolean

  fetchTripData = async () => {
    if (this.upgradeToMicroservice) {
      return await Promise.all([travelAssist.GET('/aboutToTravel'), hoseMallOrder.GET()])
    } else {
      return await Promise.all([myTripAssistV2.POST(''), hoseMallOrder.GET()])
    }
  }

  @observable
  prefixMap = {
    tripPrefix: '',
    orderPrefix: ''
  }

  // 出行提醒卡片
  @action
  async getHomeTripAssist(card: any) {
    if (card?.travelRemindValue) {
      this.showTripCard = false
      return
    }
    try {
      const [trip, order] = await this.fetchTripData()
      const { data, prefix } = trip
      const { data: orderData, prefix: orderPrefix } = order
      this.orderData = orderData
      this.prefixMap = { tripPrefix: prefix, orderPrefix }
      const cache = session?.user?.get(TRAVEL_CACHE) || {}
      let flag = false

      if (orderData?.length) {
        for (const el of orderData) {
          if (!cache[el.id]) {
            this.title = setTitle(el, orderPrefix)
            this.isOrder = true
            this.showTripCard = true
            this.recentTripData = el
            this.tripAssistData = data
            flag = true
            break
          }
        }
      }
      if (!data?.length && !orderData?.length) {
        session?.user?.remove(TRAVEL_CACHE)
      }
      if (!flag) {
        this.showTripCard = false
      }
    } catch (error) {
      console.log(error)
    }
  }

  @action
  async closeUpComingTrip() {
    this.showTripCard = false
    const old_cache = session?.user?.get(TRAVEL_CACHE)
    const obj: any = {}
    if (this.recentTripData) {
      obj[this.recentTripData.id] = true
    }
    if (this.tripAssistData?.length) {
      this.tripAssistData.forEach((v: any) => {
        obj[v.id] = true
      })
    }
    session?.user?.set(TRAVEL_CACHE, { ...old_cache, ...obj })
    const cache = session?.user?.get(TRAVEL_CACHE)
    for (const el of this.orderData) {
      if (!cache[el.id]) {
        this.isOrder = true
        this.title = '即将出行'
        this.showTripCard = true
        this.recentTripData = el
        break
      }
    }
  }

  private async ___multi_fetch_businessSummary(__items_business_summary: any[], handler: (res: any) => void) {
    const codes: string[] = __items_business_summary.map((card: { code: any }) => card.code)

    const __has_recordExpends = !!__items_business_summary.find(
      (line: { code: CodeType }) => line.code === CodeType.recordExpends
    )

    // 长度判断 `4` 发一次请求
    if (!__has_recordExpends || __items_business_summary.length <= 4) {
      const res0 = await businesssummary.POST('', { deviceType: 'MOBILE', codes: codes })
      handler(res0)
      return
    }

    // 长度判断 大于 `4` , 分两次请求
    const codes_no_recordExpends = __items_business_summary
      .filter((line: { code: CodeType }) => line.code !== CodeType.recordExpends)
      .map((card: { code: any }) => card.code)

    // 第一次 截取前四个,
    const res1 = await businesssummary.POST('', { deviceType: 'MOBILE', codes: codes_no_recordExpends.slice(0, 4) })
    handler(res1)

    // 第二次 使`随手记` + `其他`
    const res2 = await businesssummary.POST('', {
      deviceType: 'MOBILE',
      codes: codes_no_recordExpends.slice(4).concat(CodeType.recordExpends)
    })
    handler(res2)
  }

  @action
  async getStandardCardListAuditPending(fn = noon) {
    const result = await businesssummary.POST('', { deviceType: 'MOBILE', codes: ['auditPending'] })
    this.standardCardListAuditPending = result?.items?.length ? result.items[0] : {}
    fn && fn()
  }
  @action
  async getStandardCardListMyBill(fn) {
    const result = await businesssummary.POST('/desktop/mybill', { deviceType: 'MOBILE', codes: ['myBill'] })
    this.standardCardListMyBill = result?.items?.length ? result.items[0] : {}
    fn && fn()
  }

  @action // 获取卡片及其详情
  async getCardList(fn = noon, reloadFilter = noon) {
    // 因为如果没有更新列表项, 每次的请求结果都可以预期
    // 不必重新请求
    if (!this._cardList || !this._cardList.length) {
      // 获取卡片
      const { items = [] } = await getCards.GET('/$deviceType', { deviceType: 'MOBILE' })
      this._cardList = items
    }

    const __items_selected = this._cardList.filter((line: { selected: boolean }) => line.selected)

    // 防止从其页面退回时候,重新渲染骨架屏
    if (!this.cardList || !this.cardList.length) this.dealCardList(this._cardList)

    this.isFirstLoadCardList = false
    this.dealCardList(this._cardList, reloadFilter, fn)

    const __items_business_summary = __items_selected.filter(
      (line: { code: CodeType }) => line.code !== CodeType.dataLinkEntity
    )

    await this.___multi_fetch_businessSummary(__items_business_summary, res => {
      const details: CardDataProps[] = res.items || []

      details.forEach((element: CardDataProps) => {
        const el = this._cardList.find((el: CardProps) => element.code === el.code)
        if (el) el.detail = element
      })

      this.dealCardList(this._cardList, reloadFilter, fn)
    })

    // 获取业务对象的数量
    const dataLinkEntityIds = this._cardList
      .filter((line: { code: CodeType }) => line.code === CodeType.dataLinkEntity)
      .map((card: { id: any }) => card.id)

    api.invokeService('@common:get:data:delay')
    if (!dataLinkEntityIds.length) {
      return
    }
    const res1 = await businesssummary.GET('/dataLinkEntity', {
      dataLinkEntityIds: dataLinkEntityIds.join(','),
      join: 'dataLinkEntityId,dataLinkEntity,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform'
    }).catch(err => {
      console.log('[ GET businesssummary.dataLinkEntity err ] >', err)
    })

    const dataLinkDetails = res1?.items || []
    const resp = await travelManage.GET('', { type: 'travelUpgrade' })
    this.upgradeToMicroservice = resp?.value?.upgradeToMicroservice || false
    dataLinkDetails.forEach(async (line: { dataLinkEntityId: string }) => {
      const el = this._cardList.find(el => line.dataLinkEntityId === el.id)
      if (el) {
        const type = get(line, 'dataLinkEntity.platformId.type', '')
        if (type === 'TRAVEL_MANAGEMENT') {
          delete (line as any).dataLinkCount
          this.getNewTripData(el, line) // 行程卡片
          this.getHomeTripAssist(el) // 即将出行卡片
        } else {
          el.detail = line
        }
      }
    })
    this.dealCardList(this._cardList, reloadFilter, fn)
  }

  @action dealCardList(details?: CardDataProps[], reloadFilter?: any, fn?: any) {
    this.cardList = this._cardList.filter(el => el.selected)
    this.cardBacklogList = this._cardList.filter(el => !el.selected)
    reloadFilter && reloadFilter(this.cardList.find((item: any) => item.code === 'auditPending'))
    fn && fn()
  }

  @computed get groupedCardList() {
    if (!this.cardList || this.isFirstLoadCardList === true) return null
    return mergeGroups(toJS(this.cardList))
  }

  @action // 获取卡片配置数组
  async getCardConfigs() {
    const res = await getConfigs.GET()
    const configs = get(res, 'value.configs') || []
    this.cardConfigs = configs.slice()
  }

  @action // 修改菜单
  changeCardList(cardList: CardProps[], fn = noon) {
    changeStaffMenus.PUT('', cardList).then(() => {
      //清空列表,以便于  getCardList 重新获取详情
      this._cardList = []
      this.showTripCard = false
      fn && fn()
    })
  }

  @action
  async getCardBacklogList(fn = noon) {
    const cardBacklogList = await getCards.GET('/$deviceType', { deviceType: 'MOBILE', backlog: true })
    this.cardBacklogList = cardBacklogList.items || []
    fn && fn()
  }

  @action // 获取用户引导页页面中的角色数据
  async getQuestionnaires() {
    const guide = await questionnaires.GET()
    this.guideQuestionnaires = guide.items || []
  }

  @action // 保存用户选的角色并拿到配置列表刷新数据
  async setQuestionnaires(data: Questionnaires, fn = noon) {
    const cardList = await questionnaires.POST('', data)
    // this.cardList = cardList.items || []
    // 清楚当前的缓存, 以便于重新拉取 getCardList
    this._cardList = cardList.items || []
    // 重新渲染骨架屏
    this.isFirstLoadCardList = true
    fn && fn()
  }

  @action // 拉取 banners
  async getBanners() {
    const bannersInfo = await banners.GET()
    this.banners = bannersInfo.items || []
  }

  @action //获取我的行程
  async getMyTrips(count) {
    const myTripList = await myTrip.POST('', { limit: { start: 0, count } })
    this.trips = get(myTripList, 'items.data', [])
  }

  @action //获取行程类型
  async getTripType() {
    const tripTypeMap = await myTripType.GET(`/$travel/$order`, { travel: 'TRAVEL_MANAGEMENT', order: 'ORDER' })
    this.tripType = get(tripTypeMap, 'value')
  }
  @action //set segmentActiveKey
  async setSegmentActiveKey(activeKey: number) {
    this.segmentActiveKey = activeKey
  }

  @action
  async setCorpStyle(SetUpObject: SetUpObject) {
    this.corpStyle = SetUpObject
    const { logoCustom, logoUrl } = SetUpObject
    if (logoCustom && !this.logoUrl && logoUrl) {
      const url = await getFileId(logoUrl).then((res: any) => {
        return res?.value || ''
      })
      this.logoUrl = url
    }
  }

  @action
  async getProtocolIsOrNotRead(query: Agreement) {
    const res = await agreement.POST('/read/agreement', query)
    this.isReaded = res.count
  }

  @action
  async confirmAgreement(query: Agreement) {
    await agreement.POST('/confirm/agreement', query)
    this.isReaded = 1
  }

  filterSystemBillStr() {
    const userInfo = api.getState('@common.me_info')
    const corpId = userInfo?.staff?.corporationId?.id
    return `INSTR(form.specificationId,"${corpId}:system:对账单")==0`
  }

  @action
  async getMallCardData() {
    const result = await mallCardData.POST('', { source: 'APP', mallEntrance: true })
    this.mallCardDataList = result.items || []
  }

  @action
  setHome5BannerData(data) {
    this.home5BannerData = data
  }

  // 兼容历史数据
  @action
  setHome5CardList(cardList: CardProps[], cardBacklogList: CardProps[]) {
    console.log('=====start--log=====')
    console.log("asdhjaksjldhajkhsdkjashdkjahsdkjahsdkljashdkjlashdkljashdkj")
    console.log(toJS(cardList))
    console.log('=====end--log=====')
    this.cardList = cardList
    this.cardBacklogList = cardBacklogList
  }
}
