@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.shared-error-wrapper{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width:100%;
  height: 100%;
  :global{
    .shared-error-progress{
      display: flex;
      flex-direction: column;
      align-items: center;
      .app-logo-icon{
        display: block;
        width: 80px;
        margin: auto;
      }
      .progress-gradient{
        position: absolute;
        width: 0;
        height: 0;
      }
      .progress-circle-content {
        width: 196px;
        height: 196px;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .progress-text{
        .font-size-1;
        line-height: 30px;
        color: #2555FF;
        .font-weight-2;
        margin-top: @space-6;
      }
    }
    .shared-error-content{
      margin-top: @space-7;
      .content-info{
        .font-size-1;
        .font-weight-2;
        text-align: center;
        color: rgba(29, 33, 41, 0.7);
        margin-bottom: @space-2;
        .connect-admin{
          color: #2555FF;
        }
      }
    }
  }
}
