import React, { PureComponent } from 'react'
import styles from './home5Create.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { SearchBar, Accordion } from 'antd-mobile'
import Highlighter from 'react-highlight-words'
import { debounce } from 'lodash'
import EnhanceTit<PERSON>Hook from '../../lib/EnhanceTitleHook'
import {startOpenFlowPerformanceStatistics} from '../../lib/flowPerformanceStatistics'
const BillEmpty = api.require<any>('@bill/bill-content/bill-empty')
const getSpecificationIcon = api.require<any>('@elements/specificationIcon')

interface StateType {
  searchText: string
  activeKeys: string[]
  specification_group: any[]
}

@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].me_info.staff,
  specification_group: state['@home'].specificationWithVersion.specification_group || [],
  specification_recently: state['@home'].specification_recently || [],
  specification_map: state['@home'].specificationWithVersion.specification_map
}))
// @ts-ignore
@EnhanceTitleHook(i18n.get('新建单据'))
export default class home5Create extends PureComponent<any, Partial<StateType>> {
  constructor(props: any) {
    super(props)
    this.state = {
      searchText: '',
      activeKeys: [],
      specification_group: []
    }
    api.dataLoader('@common.departments').load()
    api.invokeService('@home:get:specification:recently').then(() => {
      this.onSubmit(this.state.searchText)
    })

    api
      .dataLoader('@home.specificationWithVersion')
      .load()
      .then(() => {
        this.onSubmit(this.state.searchText)
      })
  }

  handleOK(spec: any) {
    return () => {
      const { specification_map } = this.props
      const specification = spec.components ? spec : specification_map[spec.id]
      api.invokeService('@home:save:specification', specification).then(() => {
        api
          .invokeService('@home:save:specification:after:filtered', {
            type: 'filterFormType',
            formType: specification.type,
            specifications: []
          })
          .then(() => {
            api.go(`/bill/${specification.type}/from/external`)
            startOpenFlowPerformanceStatistics()
          })
      })
    }
  }

  filterByParams = (spec: any) => {
    const { specification_map = {}, params = {} } = this.props
    const currentSpec = spec.components ? spec : specification_map[spec?.id]
    const { type, configs } = currentSpec || {}

    const { tppBusinessType } = params || {}
    // 过滤易商卡单据模板
    if (tppBusinessType === 'ecard') {
      return type === 'loan' || (type === 'requisition' && configs.find((config: any) => config.ability === 'loan'))
    }
    // 过滤易商卡单据模板Token模式
    if (tppBusinessType === 'ecard_token') {
      return type === 'requisition' && configs.find((config: any) => config.allowECardOfflineAuthorized)
    }
    // 过滤商城申请单
    if (tppBusinessType !== 'null') {
      return type === 'requisition' && configs.find((config: any) => config.tppBusinessType === tppBusinessType)
    }

    // 默认全部申请单
    return type === 'requisition'
  }

  onSubmit = (value = '') => {
    let { specification_group, specification_recently } = this.props
    const activeKeys: string[] = []
    const searchList: any[] = []
    const hasValue = !!value.trim().length

    specification_group = specification_group.map((group: any) => {
      const specifications = group.specifications.filter(this.filterByParams)
      return { ...group, specifications }
    })

    specification_recently = specification_recently.filter(this.filterByParams)

    specification_group.forEach((group: any) => {
      let { specifications = [] } = group
      if (!specifications.length) return
      const { id, name } = group
      activeKeys.push(id)
      if (hasValue && name.includes(value)) {
        searchList.push(group)
      } else {
        if (hasValue) {
          specifications = specifications.filter((spec: any) => spec.name.includes(value))
        }
        specifications.length && searchList.push({ ...group, specifications })
      }
    })

    if (!hasValue && specification_recently.length) {
      activeKeys.unshift('recently')
      searchList.unshift({
        id: 'recently',
        name: i18n.get('最近使用'),
        specifications: specification_recently.slice(0, 4)
      })
    }

    this.setState({
      activeKeys,
      specification_group: searchList
    })
  }

  onChange = debounce((value: string) => {
    this.setState({ searchText: value })
    this.onSubmit(value)
  }, 300)

  switchPanel = (key: string[]) => {
    this.setState({ activeKeys: key })
  }

  renderHighlight = (value: string) => {
    return (
      <Highlighter
        className="lighter"
        searchWords={[this.state.searchText]}
        textToHighlight={value || i18n.get('[无标题]')}
        highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
        autoEscape={true}
      />
    )
  }

  renderItem = (spec: any, index: number) => {
    const IconCompo = getSpecificationIcon(spec.icon)
    return (
      <div key={index}>
        <div className="spec-item" key={index} onClick={this.handleOK(spec)}>
          <div className="spec-item-wrapper pos-r">
            <IconCompo style={{ color: spec.color }} />
            <div className="spec-item-title">{this.renderHighlight(spec.name)}</div>
          </div>
        </div>
        {index % 2 ? <div className="clearfix" key={`c${index}`} /> : null}
      </div>
    )
  }

  renderSearch() {
    return (
      <SearchBar
        className="home5-create-search"
        placeholder={i18n.get('搜索')}
        onSubmit={this.onSubmit}
        onChange={this.onChange}
        onClear={this.onChange}
      />
    )
  }

  renderContent() {
    const { activeKeys, specification_group } = this.state
    const group = specification_group
      .map(group => {
        group.specifications = group.specifications.filter(
          (v: any) => v.type !== 'settlement' && v.type !== 'reconciliation'
        )
        return group
      })
      .filter(v => v.specifications.length)
    return (
      <div className={styles['home5-create-content']}>
        {!!group.length ? (
          <Accordion activeKey={activeKeys} onChange={this.switchPanel}>
            {group.map((group: any) => (
              <Accordion.Panel key={group.id} header={this.renderHighlight(group.name)}>
                {group.specifications.map(this.renderItem)}
              </Accordion.Panel>
            ))}
          </Accordion>
        ) : (
          <BillEmpty emptyText={i18n.get('暂无可用模板, 请联系管理员')} />
        )}
      </div>
    )
  }

  render() {
    return (
      <div className={styles['home5-create-page']}>
        {this.renderSearch()}
        {this.renderContent()}
      </div>
    )
  }
}
