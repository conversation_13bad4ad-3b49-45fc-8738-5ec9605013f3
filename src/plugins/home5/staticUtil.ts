import { app as api } from '@ekuaibao/whispered'
import { addMapJsApi } from '../../lib/mapjsapi'
import {
  LineSurfaceGeneralJourney,
  LineSurfaceGeneralScan,
  LineSurfaceGeneralApplyLine,
  LineSurfaceGeneralTax,
  LineSurfaceGeneralBillH
} from '@hose/eui-icons'
import moment from 'moment'
import { enableHidingFinishedBills } from '../../lib/featbit/utils'

export const mapForCodeToPath: StringStringProps = {
  myLoan: 'loanpackage',
  myRequisition: 'requisition',
  myBill: 'billList',
  auditPending: 'approve',
  auditPayment: 'paying',
  waitSending: 'express',
  receiveExpress: 'receive',
  auditCarbonCopy: 'carboncopy',
  todoPrint: 'home/messagelist/printList',
  authorizationManage: 'permitapprove',
  waitInvoiceDetail: 'waitInvoiceDetail',
  privateCar: 'recordingtrip',
  approvePermission: 'approvePermission',
  waitReviewing: 'paymentReview',
  orderConfirm: 'orderConfirm',
  allTodo: 'allTodo'
}

export const mapForCodeToIcon: StringStringProps = {
  myBill: '#EDico-index-items1',
  myLoan: '#EDico-borrow',
  myRequisition: '#EDico-ndex-application1',
  auditPending: '#EDico-index-approve1',
  todoPrint: '#EDico-index-print1',
  auditPayment: '#EDico-index-payment1',
  waitSending: '#EDico-index-send1',
  receiveExpress: '#EDico-index-receive1',
  authorizationManage: '#EDico-cost-impower',
  privateCar: '#EDico-car',
  auditCarbonCopy: '#EDico-index-copyto1',
  dataLinkEntity: '#EDico-index-data1',
  waitInvoiceDetail: '#EDico-index-invoicecost1',
  approve: '#EDico-link3',
  completed: '#EDico-yiwancheng',
  approved: '#EDico-yishenpi',
  paymentHistory: '#EDico-huankuanjilu',
  recordExpends: '#EDico-record3',
  approvePermission: '#EDico-a-Group6',
  customMessage: '#EDico-a-Group10',
  waitReviewing: '#EDico-Frame',
  orderConfirm: '#EDico-index-data1',
  quickExpense: '#EDico-a-Group6'
}

export const moneyMap: StringStringProps = {
  expense: 'expenseMoney',
  loan: 'loanMoney',
  requisition: 'requisitionMoney'
}

export const formTypeMap: StringStringProps = {
  expense: i18n.get('报销'),
  loan: i18n.get('借款'),
  requisition: i18n.get('申请'),
  custom: i18n.get('基础')
}

export const formTypeIconClassMap: StringStringProps = {
  expense: 'blue',
  loan: 'yellow',
  requisition: 'green',
  custom: 'brand'
}

export const billStateMap = (): StringAnyProps => ({
  draft: { label: i18n.get('待提交'), color: 'orange' },
  pending: { label: i18n.get('提交中'), color: 'blue' },
  approving: { label: i18n.get('审批中'), color: 'blue' },
  sending: { label: i18n.get('待寄送'), color: 'blue' },
  receiving: { label: i18n.get('待收单'), color: 'blue' },
  receivingExcep: { label: i18n.get('收单异常'), color: 'red' },
  rejected: { label: i18n.get('已驳回'), color: 'red' },
  paying: { label: i18n.get('待支付'), color: 'blue' },
  paid: { label: i18n.get('已完成'), color: 'green' },
  archived: { label: i18n.get('已完成'), color: 'green' },
  nullify: { label: i18n.get('已作废'), color: 'red' }
})

export const getFilterList = () => {
  const formTypes = [
    { label: i18n.get('全部模板'), type: 'all' },
    { label: i18n.get('报销单'), type: 'expense' },
    { label: i18n.get('借款单'), type: 'loan' },
    { label: i18n.get('申请单'), type: 'requisition' }
  ]
  if (api.getState()['@common'].powers?.BasicDocument) {
    //基础单据charge
    Array.prototype.push.apply(formTypes, [
      { label: i18n.get('付款单'), type: 'payment' },
      { label: i18n.get('通用审批单'), type: 'custom' }
    ])
  }
  if (api.getState()['@common'].powers?.RECEIPT_DOCUMENT) {
    //基础单据charge
    Array.prototype.push.apply(formTypes, [{ label: i18n.get('收款单'), type: 'receipt' }])
  }
  const stateChildren = [
    { label: i18n.get('全部状态'), type: 'all' },
    { label: i18n.get('待提交'), type: 'draft' },
    { label: i18n.get('审批中'), type: 'approving' },
    { label: i18n.get('已驳回'), type: 'rejected' },
    { label: i18n.get('待收单'), type: 'receiving' },
    { label: i18n.get('待寄送'), type: 'sending' },
    { label: i18n.get('待支付'), type: 'paying' }
    // { label: i18n.get('已完成(待确认)'), type: 'paid' },
    // { label: i18n.get('已完成(已确认)'), type: 'archived' }
  ]

  const entrustLists = [
    { label: i18n.get('全部委托'), type: 'ALL_DELEGATE' },
    { label: i18n.get('委托我的'), type: 'DELEGATE_TO_ME' },
    { label: i18n.get('我委托的'), type: 'MY_DELEGATE' }
  ]

  if (api.getState()['@common'].powers?.Express) {
    //收单异常
    stateChildren.push({ label: i18n.get('收单异常'), type: 'receivingExcep' })
  }

  if (enableHidingFinishedBills()) {
    stateChildren.push({ label: i18n.get('已完成'), type: 'paid' })
  }

  return [
    {
      type: 'state',
      label: enableHidingFinishedBills() ? i18n.get('审批状态') : i18n.get('提交状态'),
      children: stateChildren
    },
    {
      type: 'formType',
      label: i18n.get('模板类型'),
      children: formTypes
    },
    {
      type: 'entrust',
      label: i18n.get('委托提单'),
      children: entrustLists
    }
  ]
}

export const TabbarSelectedIcon: string[] = [
  '#EDico-home-fill',
  '#EDico-data-fill',
  '#EDico-bag-fill',
  '#EDico-person-fill'
]

export const Home5Menus = (isNewMall: boolean, standardVersion: boolean) => {
  if (standardVersion) {
    //开通标准版优先返回标准版
    return [
      {
        label: () => i18n.get('首页'),
        id: 'home5',
        href: '/home5',
        icon: '#ico-7-icon_index_normal',
        selectedIcon: '#ico-7-icon_index_press'
      },
      {
        label: () => i18n.get('报表'),
        id: 'reports5',
        icon: '#ico-7-icon_sheet_normal',
        href: '/reports5',
        selectedIcon: '#ico-7-icon_sheet_press'
      },
      {
        label: () => '',
        id: 'add',
        href: '/',
        icon: '#ico-7-icon_add'
      },
      {
        label: () => i18n.get('商城'),
        id: 'mall',
        href: isNewMall ? '/mallIframe' : '/mall',
        icon: '#ico-7-icon_mall_normal',
        selectedIcon: '#ico-7-icon_mall_press'
      },
      {
        label: () => i18n.get('我的'),
        id: 'mine',
        href: '/mine',
        icon: '#ico-7-icon_user_normal',
        selectedIcon: '#ico-7-icon_user_press'
      }
    ]
  }

  //开通新首页
  return [
    {
      label: () => i18n.get('首页'),
      id: 'home5',
      href: '/home5',
      icon: '#EDico-home',
      selectedIcon: '#EDico-home-fill'
    },
    {
      label: () => i18n.get('报表'),
      id: 'reports5',
      icon: '#EDico-data',
      href: '/reports5',
      // menus: '@reports',
      selectedIcon: '#EDico-data-fill'
    },
    {
      label: () => '',
      id: 'add',
      href: '/',
      icon: '#EDico-close-default'
    },
    {
      label: () => i18n.get('订购'),
      id: 'mall',
      href: isNewMall ? '/mallIframe' : '/mall',
      icon: '#EDico-bag',
      selectedIcon: '#EDico-bag-fill'
    },
    {
      label: () => i18n.get('我的'),
      id: 'mine',
      href: '/mine',
      icon: '#EDico-person1',
      selectedIcon: '#EDico-person-fill'
    }
  ]
}

export function getFuncDetail() {
  const { info: check_enter } = api.require('@mine/invoice-check/check-enter')
  const { info: check_number } = api.require('@mine/invoice-check/check-number')
  const { fnScanClick, checkPayerInfo, fnClickMyCarBusiness, fnCreateRecordExpends } = api.require(
    '@home/elements/util'
  )

  const arr = [
    {
      icon: LineSurfaceGeneralJourney,
      icon1: '#EDico-route-add-a',
      icon2: '#EDico-route-add-b',
      name: i18n.get('开始行程'),
      fn: fnClickMyCarBusiness
    },
    {
      icon: LineSurfaceGeneralScan,
      icon1: '#EDico-scan-a',
      icon2: '#EDico-scan-b',
      name: i18n.get('扫一扫'),
      fn: fnScanClick
    },
    {
      icon: LineSurfaceGeneralApplyLine,
      icon1: '#EDico-input-a',
      icon2: '#EDico-input-b',
      name: i18n.get('手录发票'),
      fn: () => checkPayerInfo(check_enter.onClick)
    },
    {
      icon: LineSurfaceGeneralTax,
      icon1: '#EDico-search-a_',
      icon2: '#EDico-search-b',
      name: i18n.get('校验税号'),
      fn: () => checkPayerInfo(check_number.onClick)
    },
    {
      icon: LineSurfaceGeneralBillH,
      icon1: '#EDico-record11',
      icon2: '#EDico-record22',
      name: i18n.get('随手记'),
      fn: () => fnCreateRecordExpends()
    }
  ]
  const myCarBusinessPower = api.getState()['@mycarbusiness'].myCarBusinessPower
  const { active = false, state } = myCarBusinessPower
  if (!(active || state === 'RUNNING')) {
    arr.splice(0, 1)
    setTimeout(addMapJsApi, 0)
  }
  return arr
}

// 卡片类型
export enum Home5CardType {
  GROUP = 'GROUP',
  list = 'LIST',
  simple = 'SIMPLE',
  alldata = 'ALLDATA',
  database = 'DATABASE',
  emptygroup = 'EMPTY_GROUP' // 默认没有分组的卡片都在空分组里面
}
export enum Platform {
  /** 飞书 */
  feishu = 'feishu',
  /** 钉钉 */
  dingtalk = 'dingtalk',
  /** 企业微信 */
  qyweixin = 'qyweixin',
  /** 易快报 react-native 版本 */
  app = 'app',
  /** 未知平台 （包括浏览器） */
  unknown = 'unknown',
  /** 云之家 */
  cloudHub = 'cloudHub',
  /** uni app */
  uni = 'uni',
  /** 广发私有化走的 hybrid 入口 */
  hybridGF = 'hybridGF',
}
// 卡片类型名称
export const Home5CardTypeText = (): { [propName: string]: string } => {
  return {
    LIST: i18n.get('详情'),
    SIMPLE: i18n.get('概览'),
    ALLDATA: i18n.get('完整信息'),
    DATABASE: i18n.get('数据统计')
  }
}
export const getTripCardTitle = (data: any, isHotel: boolean) => {
  const { getWeek } = api.require('@bill/elements/datalink/dataLinkUtil')

  if (isHotel) {
    const left = data[Object.keys(data).find(o => o.endsWith('酒店名称'))] // @i18n-ignore
    return { left, right: '' }
  } else {
    const fromCity = data[Object.keys(data).find(o => o.endsWith('出发地'))] // @i18n-ignore
    const toCity = data[Object.keys(data).find(o => o.endsWith('到达地'))] // @i18n-ignore
    const left = `${fromCity && JSON.parse(fromCity)[0].label}-${toCity && JSON.parse(toCity)[0].label}`
    const startDate = data[Object.keys(data).find(o => o.endsWith('出发时间'))] // @i18n-ignore
    const right = `${moment(startDate).format('MM月DD日')} ${getWeek(startDate)}`
    return { left, right }
  }
}

export const diffTime = (start: number, end: number) => {
  const time = end - start
  // 计算出相差天数
  const days = Math.floor(time / (24 * 3600 * 1000))
  // 计算出小时数
  const leave1 = time % (24 * 3600 * 1000) // 计算天数后剩余的毫秒数
  const hours = Math.floor(leave1 / (3600 * 1000))
  // 计算相差分钟数
  const leave2 = leave1 % (3600 * 1000) // 计算小时数后剩余的毫秒数
  const minutes = Math.floor(leave2 / (60 * 1000))
  // 计算相差秒数
  // const leave3 = leave2 % (60 * 1000) //计算分钟数后剩余的毫秒数
  // const seconds = Math.round(leave3 / 1000)

  if (days) {
    return `${days}d${hours}h${minutes}m`
  } else if (hours) {
    return `${hours}h${minutes}m`
  } else {
    return `${minutes}m`
  }
}
export const getTripCardData = (type: string, value: any) => {
  const { getWeek } = api.require('@bill/elements/datalink/dataLinkUtil')
  const startDate = value[Object.keys(value).find(o => !!o.endsWith('出发时间'))] // @i18n-ignore
  const endDate = value[Object.keys(value).find(o => !!o.endsWith('到达时间'))] // @i18n-ignore
  if (type === 'TRAIN') {
    return {
      leftTop: value[Object.keys(value).find(o => !!o.endsWith('出发车站'))], // @i18n-ignore
      leftBottom: moment(startDate).format('hh:mm'),
      leftInfo: `${moment(startDate).format('MM月DD日')} ${getWeek(startDate)}`,
      centerTop: value[Object.keys(value).find(o => !!o.endsWith('火车车次'))], // @i18n-ignore
      centerBottom: diffTime(startDate, endDate),
      rightTop: value[Object.keys(value).find(o => !!o.endsWith('到达车站'))], // @i18n-ignore
      rightBottom: moment(endDate).format('hh:mm'),
      rightInfo: `${moment(endDate).format('MM月DD日')} ${getWeek(endDate)}`,
      // @i18n-ignore
      bottom: `${value[Object.keys(value).find(o => !!o.endsWith('车型'))]} ${
        value[Object.keys(value).find(o => !!o.endsWith('火车席位'))] // @i18n-ignore
      }`
    }
  } else {
    return {
      leftTop: value[Object.keys(value).find(o => !!o.endsWith('出发机场'))], // @i18n-ignore
      leftBottom: moment(startDate).format('hh:mm'),
      leftInfo: '起飞',
      centerTop: value[Object.keys(value).find(o => !!o.endsWith('航班号'))], // @i18n-ignore
      centerBottom: diffTime(startDate, endDate),
      rightTop: value[Object.keys(value).find(o => !!o.endsWith('到达机场'))], // @i18n-ignore
      rightBottom: moment(endDate).format('hh:mm'),
      rightInfo: '到达',
      // @i18n-ignore
      bottom: `${value[Object.keys(value).find(o => !!o.endsWith('航空公司'))]} ${
        value[Object.keys(value).find(o => !!o.endsWith('舱位类型'))] // @i18n-ignore
      }`
    }
  }
}

export const standardTrack = (key: string, options: any) => {
  // @ts-ignore
  api.track(key, {
    ...options,
    source: window.__PLANTFORM__,
    platForm: window.isAndroid ? 'Android' : 'ios',
    appVersion: api.getState()['@common']?.powers?.Universal,
    staffId: api.getState()['@common'].me_info?.staff?.corporationId?.userId,
    corpName: api.getState()['@common'].me_info?.staff?.corporationId?.name,
    corpId: api.getState()['@common'].me_info?.staff?.corporationId?.id
  })
}

/**
 * 我的页面所有埋点
 * @param key 英文名称
 * @param options
 */
export const minePageTrack = (key: string, options: any) => {
  const me_info = api.getState()['@common'].me_info || {}
  // @ts-ignore
  api.track(key, {
    ...options,
    source: window.__PLANTFORM__,
    staffId: me_info.staff?.corporationId?.userId,
    corpId: me_info.staff?.corporationId?.id,
    corpName: me_info.staff?.corporationId?.name,
    platForm: window.isAndroid ? 'Android' : 'ios'
  })
}
