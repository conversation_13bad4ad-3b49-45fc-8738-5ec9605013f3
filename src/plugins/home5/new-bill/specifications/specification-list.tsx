import React, { useState } from 'react'
import { SpecificationItem } from './item'
import { OutlinedDirectionRight } from '@hose/eui-icons'
import { getSpecificationName } from '../../../bill/utils/billUtils'
import { List } from '@hose/eui-mobile'
import classNames from 'classnames'
import { app as api } from '@ekuaibao/whispered'
import styles from '../home3-create-content.module.less'
const EmptyWidget = api.require<any>('@home5/EmptyWidget')

interface SpecificationListProps {
  groups: any[]
  onSelect: (spec: any, e: React.MouseEvent<HTMLDivElement>) => void
  renderHighlight: (value: string) => React.ReactNode
  emptyStyle?: React.CSSProperties
}

export const SpecificationList = ({ groups, onSelect, renderHighlight, emptyStyle }: SpecificationListProps) => {
  const [collapseMap, setCollapseMap] = useState<any>({})

  const handleAccordionCollapse = (groupId: string) => {
    setCollapseMap((prev: any) => ({ ...prev, [groupId]: !prev[groupId] }))
  }

  return groups.length !== 0 ? (
    <>
      {groups.map((group: any) => {
        const isCollapsed = collapseMap[group.id]
        const groupName = getSpecificationName(group)
        return (
          <div className={styles['home3-creact-content']}>
            <div className="accordion">
              <div
                className={classNames('accordion-item', { 'accordion-item-collapsed': isCollapsed })}
                key={group.id}
              >
                <div className="accordion-header" onClick={() => handleAccordionCollapse(group.id)}>
                  {renderHighlight(groupName)}
                  <div className="direction-icon">
                    <OutlinedDirectionRight rotate={isCollapsed ? -90 : 90} />
                  </div>
                </div>
                {!isCollapsed && (
                  <List className="accordion-content" style={{ '--border-top': '0 none', '--border-bottom': '0 none' }}>
                    {group.specifications.map((spec: any) => (
                      <SpecificationItem specification={spec} renderHighlight={renderHighlight} onSelect={onSelect} />
                    ))}
                  </List>
                )}
              </div>
            </div>
          </div>
        )
      })}
    </>
  ) : (
    <EmptyWidget
      type="noCentent"
      backgroundColor="var(--eui-bg-body)"
      bottom={0}
      style={emptyStyle}
    />
  )
}
