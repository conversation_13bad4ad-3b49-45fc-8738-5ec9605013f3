import React from "react";
import { getSpecificationName } from "../../../bill/utils/billUtils";
import { app as api } from '@ekuaibao/whispered'
import { RectContainer } from "../rect-container";
import { List } from "@hose/eui-mobile";
const getSpecificationIcon = api.require<any>('@elements/specificationIcon')

interface SpecificationItemProps {
  specification: any,
  onSelect: (spec: any, e: React.MouseEvent<HTMLDivElement>) => void
  renderHighlight: (value: string) => React.ReactNode
}


export const SpecificationItem = ({ specification: spec, renderHighlight, onSelect }: SpecificationItemProps) => {
  const IconCompo = getSpecificationIcon(spec.icon)
    const name = getSpecificationName(spec)
    const prefixIcon = <RectContainer color={spec.color}>
      <IconCompo style={{ color: spec.color, fontSize: 16, marginTop: 0 }} />
    </RectContainer>
    return (
      <List.Item prefixIcon={prefixIcon}>
        <div className='spec-item-title' onClick={(e) => onSelect(spec, e)}>
          { renderHighlight(name) }
        </div>
      </List.Item>
    )
}