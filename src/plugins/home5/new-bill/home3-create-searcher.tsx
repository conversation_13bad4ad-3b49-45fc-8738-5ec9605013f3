import { OutlinedEditSearch } from "@hose/eui-icons";
import React from "react";
import { Input } from "@hose/eui-mobile";
import styles from './home3-create-content.module.less'


export const SearchBar = ({ value, onChange }: { value: string, onChange: (value: string) => void }) => {
  return (
    <div className={styles['home3-creact-modal-search']}>
      <div className="search-icon">
        <OutlinedEditSearch />
      </div>
      <Input
        clearable={true}
        value={value}
        placeholder={i18n.get('搜索单据模板')}
        onChange={onChange}
        onClear={() => onChange('')}
      />
    </div>
  )
}