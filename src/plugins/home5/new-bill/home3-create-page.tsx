import React, { PureComponent } from 'react'
import styles from './home3-create-page.module.less'
import { app as api } from '@ekuaibao/whispered'
import Home3CreateContent from './home3-create-content'

export default class Home3CreatePage extends PureComponent<any> {
  prevTitle: any = api.invokeService('@layout:get:header:title')

  componentDidMount() {
    api.invokeService('@layout:change:header:title', i18n.get('新建单据'))
    api.invokeService('@layout:set:header:icon', { showIcon: false })
  }

  componentWillUnmount() {
    api.invokeService('@layout:set:header:icon', { showIcon: true })
    api.invokeService('@layout:change:header:title', this.prevTitle)
  }

  render() {
    return (
      <div className={styles['home3-create-page']}>
        <Home3CreateContent
          location={this.props.location}
          isModal={false}
        />
      </div>
    )
  }
}
