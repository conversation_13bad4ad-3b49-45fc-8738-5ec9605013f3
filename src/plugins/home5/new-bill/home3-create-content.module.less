@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.home3-create-content {
  background-color: var(--eui-bg-body);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  flex-grow: 1;

  &.home3-create-content-empty {
    height: 100%;

    .home3-create-content-inner {
      background-color: var(--eui-bg-body);
    }
  }

  &.home3-create-content-hidden-footer {
    .home3-create-content-wrapper {
      padding-bottom: 0;
      height: calc(100% - 84px);
    }
  }
}

.home3-create-content-modal {
  .header {
    display: flex;
    padding: 18px 32px;
    position: relative;
    margin-bottom: 8px;

    .cancel {
      position: absolute;
      left: 32px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 48px;
      color: var(--eui-icon-n1);
    }
    .title {
      width: 100%;
      text-align: center;
      color: var(--eui-text-title);
      font: var(--eui-font-head-b2);
    }
  }

  .home3-create-content-wrapper {
    padding-bottom: 0;
    height: calc(100% - 192px);
  }
}

.home3-creact-modal-search {
  @height: 72px;
  height: @height;
  background-color: var(--eui-transparent-n900-5);
  padding: 26px;
  margin: 24px 24px 0;
  box-shadow: 0px 4px 16px 4px rgba(29, 33, 41, 0.02), 0px 4px 8px rgba(29, 33, 41, 0.02), 0px 2px 4px -4px rgba(29, 33, 41, 0.02);
  border-radius: 8px;
  display: flex;
  align-items: center;
  z-index: 1;

  :global {
    .search-icon {
      margin-right: 16px;
      color: var(--eui-icon-n3);
    }
    .eui-input {
      font: var(--eui-font-body-r1);
      color: var(--eui-text-disabled);
    }
  }
}

.home3-re-edit-bill {
  padding-top: 24px;
}

.home3-create-content-wrapper {
  padding-bottom: 120px;
  height: 100%;
  background-color: var(--eui-bg-body);
  padding-top: 12px;
  overflow: auto;

  .home3-create-content-inner {
    background-color: var(--eui-bg-base);
    height: 100%;
    overflow-y: auto;
  }
}

.home3-creact-content {
  flex: 1;
  margin-bottom: 16px;
  &:last-child {
    margin-bottom: 0;
  }
  :global {
    .accordion {
      -webkit-overflow-scrolling: touch;
      background-color: var(--eui-bg-base);
      .accordion-item {
        margin-bottom: 16px;
        &:last-child {
          margin-bottom: 0;
        }
        &.accordion-item-collapsed {
          .accordion-header {
            padding-bottom: 10px;
            box-sizing: content-box;
          }
        }

        .spec-item-title {
          color: var(--eui-text-title);
          font: var(--eui-font-head-r1);
        }

        .accordion-header {
          text-align: left;
          height: 80px;
          line-height: 80px;
          padding: 0 26px;
          position: relative;
          padding-top: 10px;
          background-color: var(--eui-bg-body);

          .lighter {
            font: var(--eui-font-head-b1);
            color: var(--eui-text-title);
          }

          .direction-icon {
            position: absolute;
            right: 30px;
            top: 10px;
            color: var(--eui-icon-n2);
            font-size: 32px;
          }
        }
      }
    }

  }
}


.accordion-skeleton {
  padding: 32px 32px 0;
  display: flex;
  flex-direction: column;
  background-color: var(--eui-bg-body);

  .accordion-skeleton-title {
    :global {
      .eui-skeleton.eui-skeleton-title {
        margin-bottom: 0;
      }
    }
  }

  .content-skeleton-item {
    display: flex;
    align-items: center;
    height: 80px;
    gap: 24px;
    padding: 32px 0;
    box-sizing: content-box;

    .content-skeleton-item-image {
      display: inline-block;
      width: 80px;
      height: 80px;
    }
    .content-skeleton-item-text {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex-grow: 1;

      .content-skeleton-item-text-title {
        width: 200px;
        height: 36px;
      }
      .content-skeleton-item-text-desc {
        width: 100%;
        height: 28px;
      }
    }
  }
}


.home3-creact-modal-footer {
  width: 100%;
  height: 120px;
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 0;
  .shadow-3;
  :global {
    .func-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      background-color: var(--eui-bg-body);
      // transition: .2s ease-in-out background-color;
      // &:hover {
      //   background-color: @color-bg-1;
      // }
      .func-item-icon {
        position: relative;
        margin: @space-2 0;
        .icon {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          &:first-child {
            color: @color-black-2;
          }
          &:last-child {
            color: @color-brand;
          }
        }
      }
      .func-item-name {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-title);
        white-space: nowrap;
      }
    }
  }
}
