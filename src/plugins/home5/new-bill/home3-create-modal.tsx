import React, { useMemo, useState } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { SpecificationList } from './specifications/specification-list'
import Highlighter from 'react-highlight-words'
import { SearchBar } from './home3-create-searcher'
import styles from './home3-create-content.module.less'
import clx from 'classnames'
import { getInitialGroups } from './utils'
import { OutlinedTipsClose } from '@hose/eui-icons'


interface SpecificationSelectorProps {
  groups: any[]
  layer: any
  expenseSpecAfterFiltered: {
    type: string
    specifications: any[]
    formType: string
  }
}

const SpecificationSelector = ({ groups = [], layer, expenseSpecAfterFiltered }: SpecificationSelectorProps) => {
  const [searchText, setSearchText] = useState('')
  const initialGroups = useMemo(() => getInitialGroups(groups, expenseSpecAfterFiltered), [groups, expenseSpecAfterFiltered])
  const [filteredGroups, setFilteredGroups] = useState(initialGroups)


  const onSelect = (spec: any) => {
    layer.emitOk(spec)
  }
  const renderHighlight = (value: string) => {
    return <Highlighter textToHighlight={value} searchWords={[searchText]} highlightStyle={{ color: 'var(--eui-primary-pri-500)', background: 'none' }} autoEscape={true} />
  }
  const onSearch = async (value: string) => {
    setSearchText(value)
    const result = []
    for (const group of initialGroups) {
      const specifications = group.specifications.filter((spec: any) => {
        return spec.name.toLowerCase().includes(value.toLowerCase())
      })
      if (specifications.length) {
        result.push({
          ...group,
          specifications
        })
      }
    }
    setFilteredGroups(result)
  }

  const handleClose = () => {
    layer?.emitCancel()
  }

  return (
    <div className={clx(
      styles['home3-create-content'],
      styles['home3-create-content-modal'],
      {
        [styles['home3-create-content-empty']]: !filteredGroups.length,
      }
    )}>
      <div className={styles['header']}>
        <OutlinedTipsClose className={styles.cancel} onClick={handleClose}  />
        <div className={styles.title}>{i18n.get('选择单据模板')}</div>
      </div>
      <SearchBar value={searchText} onChange={onSearch} />
      <div className={clx(styles['home3-create-content-wrapper'])}>
        <div className={styles['home3-create-content-inner']}>
          <SpecificationList groups={filteredGroups} onSelect={onSelect} renderHighlight={renderHighlight} />
        </div>
      </div>
    </div>
  )
}

export default EnhanceConnect((state: any) => ({
  expenseSpecAfterFiltered: state['@home'].expenseSpecAfterFiltered
}))(SpecificationSelector)