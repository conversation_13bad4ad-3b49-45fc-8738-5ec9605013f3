import React, { PureComponent } from 'react'
import styles from './home3-create-content.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { List, NoticeBar, Space, SkeletonTextList, Skeleton } from '@hose/eui-mobile'
import Highlighter from 'react-highlight-words'
import { debounce, isEqual, get } from 'lodash'
import { getFuncDetail } from '../staticUtil'
import { getBillKey } from '../../../lib/util'
import { updateDetailIndex } from '../../bill/utils/billUtils'
import { fnAddReimbursementSensorTrack } from '../util/track'
import qs from 'qs'
const { related } = api.require<any>('@components/utils/Related')
const DRAFT_ID = getBillKey()
const EmptyWidget = api.require<any>('@home5/EmptyWidget')
import { SpecificationGroup } from '@ekuaibao/ekuaibao_types'
import { startOpenFlowPerformanceStatistics } from '../../../lib/flowPerformanceStatistics'
import clx from 'classnames'
const { getSpecificationName } = api.require<any>('@bill/utils/billUtils')
import { SpecificationList } from './specifications/specification-list'
import { SpecificationItem } from './specifications/item'
import { SearchBar } from './home3-create-searcher'

interface StateType {
  hiddenFooter: boolean
  searchText: string
  activeKeys: string[]
  specification_group: any[]
  assiciationSpecifications: any[]
  collapseMap: any
  isLoading?: boolean
  billInProcess: boolean
}

interface Props {
  userInfo?: any
  specification_list?: any[]
  specification_group?: any[]
  specification_recently?: any[]
  specification_map?: any
  assiciationSpecifications?: any[]
  autoExpenseForbiddenFeaturePower?: boolean
  hiddenFooter?: boolean
  onCancel?: () => void
  onSpecificationSelect?: (spec: any) => void
  isModal?: boolean
  location?: any
  layer?: any
}

@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].me_info.staff,
  specification_list: state['@home'].specificationWithVersion.specification_list,
  specification_group: state['@home'].specificationWithVersion.specification_group,
  specification_recently: state['@home'].specification_recently,
  specification_map: state['@home'].specificationWithVersion.specification_map,
  assiciationSpecifications: state['@home'].assiciationSpecifications, // 钉钉管理群模板
  autoExpenseForbiddenFeaturePower: state['@common'].powers.autoExpenseForbiddenFeature
}))
export default class Home3CreateContent extends PureComponent<Props, Partial<StateType>> {
  constructor(props: Props) {
    super(props)
    const hiddenFooter = props.hiddenFooter || !!qs.parse(props.location?.search || '', { ignoreQueryPrefix: true })?.hiddenFooter
    this.state = {
      hiddenFooter,
      searchText: '',
      activeKeys: [],
      specification_group: [],
      assiciationSpecifications: [], // 钉钉关联群模板
      collapseMap: {},
      isLoading: true,
      billInProcess: !!localStorage.getItem(DRAFT_ID)
    }

    this.loadInitialData()
  }

  loadInitialData = () => {
    const { hiddenFooter } = this.state
    api.invokeService('@home:get:specification:recently')
    if (!hiddenFooter) {
      api.dataLoader('@home.specificationWithVersion').load()
    } else {
      api.invokeService('@home:get:association:specifications', 'requisition')
    }
  }

  componentDidMount() {
    const { hiddenFooter, searchText } = this.state
    if (hiddenFooter) {
      api.invokeService('@home:get:association:specifications', 'requisition').then(() => {
        this.onSubmit(searchText)
      })
    } else {
      api
        .dataLoader('@home.specificationWithVersion')
        .reload()
        .then(() => {
          this.onSubmit(searchText)
        })
    }
  }

  componentWillReceiveProps(nextProps: Props) {
    if (!isEqual(nextProps.specification_recently, this.props.specification_recently)) {
      this.onSubmit(this.state.searchText)
    }
  }

  handleCancel = () => {
    if (this.props.onCancel) {
      this.props.onCancel()
    } else if (this.props.layer) {
      this.props.layer.emitCancel()
    }
  }

  handleOK = (spec: any) => {
    const { hiddenFooter } = this.state

    if (this.props.onSpecificationSelect) {
      this.props.onSpecificationSelect(spec)
      return
    }

    // 点击
    const ls = localStorage.getItem(DRAFT_ID)
    if (ls) {
      related.clearRelateMap()
      localStorage.removeItem(DRAFT_ID)
    }
    const { specification_map } = this.props
    const specification = spec.components ? spec : specification_map[spec.id]


    if (spec.type === 'expense') {
      fnAddReimbursementSensorTrack('add_reimbursement')
    }
    api.invokeService('@home:save:specification', specification).then(() => {
      api
        .invokeService('@home:save:specification:after:filtered', {
          type: 'filterFormType',
          formType: specification.type,
          specifications: []
        })
        .then(() => {
          // hiddenFooter 用来判断是否是 钉钉关联群
          if (hiddenFooter) {
            api.go(`/bill/isOpenAssociation/${specification.type}/${true}`, true)
            setTimeout(() => {
              let preHashs = [`#/bill/isOpenAssociation/${specification.type}/${true}`]
              api.useHistory({
                initialEntries: preHashs,
                initialIndex: preHashs.length
              })
            }, 0)
          } else {
            api.go(`/bill/${specification.type}/from/createModal`, true)
          }
          startOpenFlowPerformanceStatistics()
          this.handleCancel()
        })
    })
  }

  fnFilterSpecification = (specificationGroups: SpecificationGroup[] = []) => {
    const { autoExpenseForbiddenFeaturePower } = this.props
    if (!autoExpenseForbiddenFeaturePower) {
      return specificationGroups
    }
    return specificationGroups.filter(specification => {
      specification.specifications = specification.specifications.filter(specification => {
        return !(specification.type === 'expense' || specification.type === 'loan')
      })
      return !!specification.specifications.length
    })
  }

  onSubmit = debounce((value: string = '') => {
    const activeKeys: string[] = []
    const searchList: any[] = []
    const specification_group = get(this.props, 'specification_group') || []
    const specification_recently = get(this.props, 'specification_recently') || []
    const hasValue = !!value.trim().length
    const { hiddenFooter } = this.state
    const assiciationSpecifications = get(this.props, 'assiciationSpecifications') || []

    if (hiddenFooter) {
      const specifications = assiciationSpecifications.filter((spec: any) => spec.name.includes(value))
      this.setState({
        assiciationSpecifications: specifications
      })
      return
    }

    specification_group.forEach((group: any) => {
      const { id } = group
      const groupName = getSpecificationName(group)
      let { specifications = [] } = group
      if (!specifications.length) {
        return
      }
      activeKeys.push(id)
      if (hasValue && groupName.includes(value)) {
        searchList.push(group)
      } else {
        if (hasValue) {
          specifications = specifications.filter((spec: any) => {
            const speName = getSpecificationName(spec)
            return speName.includes(value)
          })
        }
        specifications.length && searchList.push({ ...group, specifications })
      }
    })

    if (!hasValue && specification_recently.length) {
      activeKeys.unshift('recently')
      searchList.unshift({
        id: 'recently',
        name: i18n.get('最近使用'),
        specifications: specification_recently.slice(0, 4)
      })
    }

    this.setState({
      activeKeys,
      specification_group: this.fnFilterSpecification(searchList),
      isLoading: false
    })
  }, 300)

  onChange = (value: string) => {
    this.setState({ searchText: value })
    this.onSubmit(value)
  }

  switchPanel = (key: string[]) => {
    this.setState({ activeKeys: key })
  }

  renderHighlight = (value: string) => {
    return (
      <Highlighter
        className="lighter"
        searchWords={[this.state.searchText]}
        textToHighlight={value || i18n.get('[无标题]')}
        highlightStyle={{ color: 'var(--eui-primary-pri-500)', background: 'none' }}
        autoEscape={true}
      />
    )
  }

  renderSearch() {
    return <SearchBar value={this.state.searchText} onChange={this.onChange} />
  }

  handleNoRemind = () => {
    localStorage.removeItem(DRAFT_ID)
    this.setState({
      billInProcess: false
    })
  }

  renderReEditBill() {
    const handleClick = () => {
      const ls = localStorage.getItem(DRAFT_ID)
      const value = updateDetailIndex(JSON.parse(ls))
      const {
        specificationId: { type }
      } = value
      api.go(`/bill/localStorage/${type}`, true)
      startOpenFlowPerformanceStatistics()
    }

    return (
      <div className={styles['home3-re-edit-bill']}>
        <NoticeBar
          extra={
            <Space style={{ '--gap': '16px' }}>
              <span onClick={this.handleNoRemind}>{i18n.get('不再提醒2')}</span>
              <span onClick={handleClick.bind(this)}>{i18n.get('打开草稿')}</span>
            </Space>
          }
          content={i18n.get('继续编辑上次的草稿')}
          color="info"
        />
      </div>
    )
  }

  getValidGroups = () => {
    const { specification_group } = this.state
    return specification_group
      .map((group: any) => {
        group.specifications = group.specifications.filter(
          (v: any) => v.type !== 'settlement' && v.type !== 'reconciliation' && !v.id?.includes('system:对账单')
        )
        return group
      })
      .filter((v: any) => v.specifications.length)
  }

  renderContent(group: any[]) {
    const { isLoading } = this.state
    return (<>
      {!isLoading ? <SpecificationList
        groups={group}
        onSelect={this.handleOK}
        renderHighlight={this.renderHighlight}
        emptyStyle={{
          width: 'auto',
          height: 'auto',
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)'
        }}
      /> : this.renderSkeleton()}
      </>
    )
  }

  renderSkeletonItem = () => {
    return (
      <div className={styles['content-skeleton-item']}>
        <Skeleton className={styles['content-skeleton-item-image']} animated />
        <div className={styles['content-skeleton-item-text']}>
          <Skeleton className={styles['content-skeleton-item-text-title']} animated />
          <Skeleton className={styles['content-skeleton-item-text-desc']} animated />
        </div>
      </div>
    )
  }

  renderSkeleton() {
    return (
      <div className={styles['accordion-skeleton']}>
        <SkeletonTextList className={styles['accordion-skeleton-title']} showTitle animated />
        {new Array(8).fill(0).map((_, index) => this.renderSkeletonItem())}
      </div>
    )
  }

  renderAssociationContent() {
    const { assiciationSpecifications } = this.state
    return (
      <div className={styles['home3-creact-content']}>
        {assiciationSpecifications.length !== 0 ? (
          <List className="accordion-content" style={{ '--border-top': '0 none', '--border-bottom': '0 none' }}>
            {assiciationSpecifications.map((spec: any) => {
              return <SpecificationItem key={spec.id} specification={spec} onSelect={this.handleOK} renderHighlight={this.renderHighlight} />
            })}
          </List>
        ) : (
          <EmptyWidget size={100} type="noCentent" />
        )}
      </div>
    )
  }

  renderFooter() {
    const funcData = getFuncDetail()
    return (
      <div className={styles['home3-creact-modal-footer']}>
        {funcData.map((el, idx) => {
          const IconCompo = el.icon
          return (
            <div
              className="func-item"
              key={idx}
              onClick={() => {
                el.fn()
                this.handleCancel()
              }}
            >
              <div className="func-item-icon">
                <IconCompo fontSize={28} />
              </div>
              <span className="func-item-name">{el.name}</span>
            </div>
          )
        })}
      </div>
    )
  }

  render() {
    const { hiddenFooter, billInProcess, isLoading } = this.state
    const group = this.getValidGroups()

    return (
      <div className={clx(
        styles['home3-create-content'],
        {
          [styles['home3-create-content-empty']]: !isLoading && !group.length,
          [styles['home3-create-content-hidden-footer']]: hiddenFooter,
        }
      )}>
        {this.renderSearch()}
        {billInProcess && !hiddenFooter && this.renderReEditBill()}
        <div className={clx(styles['home3-create-content-wrapper'])}>
          <div className={styles['home3-create-content-inner']}>
            {hiddenFooter ? this.renderAssociationContent() : this.renderContent(group)}
          </div>
        </div>
        {!hiddenFooter && this.renderFooter()}
      </div>
    )
  }
}
