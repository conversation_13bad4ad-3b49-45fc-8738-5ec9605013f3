import React from 'react'
import styles from './rect-container.module.less'

interface RectIconContainerProps {
  color: string
  children: React.ReactNode
  style?: React.CSSProperties
}

export const RectContainer = ({ color, children, style }: RectIconContainerProps) => {

  const hexToRgba = (hex: string, alpha: number) => {
    const [r, g, b] = hex.match(/\w\w/g)!.map(c => parseInt(c, 16))
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
  }

  return <span className={styles['rect-icon-container']} style={{ backgroundColor: hexToRgba(color, 0.09), ...style } }>
    {children}
  </span>
}