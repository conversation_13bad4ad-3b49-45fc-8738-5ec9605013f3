const fnFilterSpec = (data: any[], specIds: any[], formType?: any) => {
  const result = []
  if (formType) {
    for (const section of data) {
      section.specifications = section.specifications.filter((spe: any) => spe.type === formType)
      if (section.specifications.length) {
        result.push(section)
      }
    }
    return result
  }
  for (const section of data) {
    section.specifications = section.specifications.filter((spe: any) => !!~specIds.indexOf(spe.originalId))
    if (section.specifications.length) {
      result.push(section)
    }
  }
  return result
}


export const getInitialGroups = (groups: any[], specObj: any) => {
  let specArr = groups.map(section => {
    section.specifications = section.specifications.filter((spe: any) => !spe?.id?.includes('system:对账单'))
    return section
  })
  if (specObj.type === 'byExpenseLink') {
    specArr = fnFilterSpec(groups, specObj.specifications)
  }
  if (specObj.type === 'filterFormType') {
    specArr = fnFilterSpec(groups, null, specObj.formType)
  }
  if (specObj.type === 'bySupply') {
    specArr = fnFilterSpec(groups, specObj.specifications)
  }
  return specArr
}
