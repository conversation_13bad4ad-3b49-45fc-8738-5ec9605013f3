/**************************************
 * Created By LinK On 2019/8/27 16:55.
 **************************************/

import { observable } from 'mobx'
import { action } from '@ekuaibao/mobx-store'

export default class MarketBannerStore {
  @observable //记录banner是否正在首页展示
  bannerShowInHomePage: boolean

  @observable // 记录banner是否该在首页展示
  bannerShouldShowInHomePage: boolean = true

  @observable
  setting: any

  @observable
  bannerData: any

  @observable // 活动盒子配置请求只需发送一次，记录配置请求是否已经发送
  marketBannerHadInit: boolean

  @action
  changeValueForMarketBannerHadInit() {
    const inProductionMode = process.env.NODE_ENV === 'production'
    const appKey = inProductionMode
      ? 'fd45750dd020da8e338817dce4741269'
      : '7fe8e836921493cba9afc767c65c7233'
    const appSecret = inProductionMode
      ? '570ed01958392b2b1f3eeac69a59026a'
      : '1f4279f517a8d007cbafff0942bfe6d2'

    // 配置
    window.masdk && window.masdk.init({
      // debug: !inProductionMode,
      debug: false,
      appKey,
      appSecret
    })

    this.marketBannerHadInit = true
  }

  @action
  changeBannerStatusInHomePage(value: boolean) {
    this.bannerShowInHomePage = value
  }

  @action
  bannerShouldNotShowInHomePage() {
    this.bannerShouldShowInHomePage = false
  }

  @action
  saveBannerData(value: boolean) {
    this.bannerData = value
  }
}
