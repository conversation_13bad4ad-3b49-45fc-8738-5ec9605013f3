import React from 'react'
import './homePage.less'
import { connect } from '@ekuaibao/mobx-store'
import { provider } from '@ekuaibao/react-ioc'
import { EnhanceConnect } from '@ekuaibao/store'
import ChangeCorp from './ChangeCorp'
import HomeCardManage from './HomeCardManage'
import { CorporationIF, MeIF } from '@ekuaibao/ekuaibao_types'
import { fixWeixinPrint, getCompareVersion } from '../../../lib/util'
import { app as api, UIContainer as Container } from '@ekuaibao/whispered'
import { addMapJsApi } from '../../../lib/mapjsapi'
import { get } from 'lodash'
import { ChangeCorpStore } from './ChangeCorp/ChangeCorp.store'
import UpComingTrip from '../elements/cards/UpComingTrip'
import Home5QuestionFloat from '../elements/home5-question-float'
// @ts-ignore
import classnames from 'classnames'
import EditHome from './HomeCardManage/elements/EditHome'
import HomeContainer from '../../home-container'
import EnhanceTitleHook from '../../../lib/EnhanceTitleHook'
import { Fetch } from '@ekuaibao/fetch'
const HomeNoticeBar = api.require<any>('@elements/HomeBanner/HomeNoticeBar')
const HomeBanner = api.require<any>('@elements/HomeBanner/HomeBanner')
import { PullToRefresh } from '@hose/eui-mobile'
import questionnaireConfig from '../../../lib/questionnaireConfig'
import { Questionnaire } from '@hose/eui-mobile'
import { EnhanceLayerManager } from '@ekuaibao/enhance-layer-manager-mobile'
import { getBoolVariation } from '../../../lib/featbit'

// TODO: 新版首页刷新
interface Props {
  me_info: MeIF
  logoUrl: string
  corpList: CorporationIF[]
  isNewMall: boolean
  showTripCard?: boolean
  home5BannerData?: any[]
}
// @ts-ignore
@EnhanceTitleHook(() => i18n.get(`易快报@@${Fetch.ekbCorpId}`, null, () => i18n.get('易快报')))
@((provider as any)([ChangeCorpStore.NAME, ChangeCorpStore]))
@((connect as any)((store: any) => ({
  marketBannerHadInit: store.states['@marketADBanner'].marketBannerHadInit,
  logoUrl: store.states['@home5'].logoUrl,
  corpList: store.states['@home5']?.corpList || [],
  externalCorpList: store.states['@home5']?.externalCorpList || [],
  clusterCorpList: store.states['@home5']?.clusterCorpList || [],
  home5BannerData: store.states['@home5'].home5BannerData,
  showTripCard: store.states['@home5'].showTripCard,
  groupedCardList: store.states['@homePage'].groupedCardList
})))
@((EnhanceConnect as any)((state: any) => ({
  me_info: state['@common'].me_info || {},
  isNewMall: state['@common'].powers.newMall
})))
// @ts-ignore
export default class HomePage extends HomeContainer<Props> {
  /**
   * 调查问卷的嵌入
   */
  initSurvey = () => {
    const setTimeoutFn = setTimeout(() => {
      Questionnaire.initSurvey({
        sid: questionnaireConfig?.home?.sid,
        channelId: questionnaireConfig?.home?.channelId,
        externalUserId: api.getState()['@common'].me_info?.staff?.userId,
        externalCompanyId: api.getState()['@common'].me_info?.staff?.corporationId?.id,
        width: '345px'
      })
      clearTimeout(setTimeoutFn)
    }, 5000)
  }

  async componentDidMount() {
    api.store.dispatch('@homePage/fetchCardWithCache')()
    fixWeixinPrint()
    sessionStorage.setItem('num', '2') // 修改携程商旅在当前webview打开，第二次进不去的问题
    if (window.__PLANTFORM__ === 'APP' || window.PLATFORMINFO?.thirdPartSwithCorp) {
      api.store.dispatch('@home5/getCorporations')()
      if (window.PLATFORMINFO?.clusterURL) {
        api.store.dispatch('@home5/getClusterCorporations')()
      }
    }
    const corpStyle = get(api, 'sdk.staffSetting.corpStyle')
    corpStyle && api.store.dispatch('@home5/setCorpStyle')(corpStyle)
    // @ts-ignore
    this.props.isNewMall && api.store.dispatch('@homePage/getMallCardData')()
    this.initCompareVersion()
    this.initSurvey()
  }

  initCompareVersion = async () => {
    const result: any = await getCompareVersion(window.APPLICATION_VERSION)
    api.emit('tabular:version:change', result)
  }

  refresh = () => {
    return new Promise(resolve => {
      api.store.dispatch('@homePage/resetHomePageCard')(
        () => {
          resolve()
        },
        true,
        true
      )
    })
  }
  sleep = (time = 1000) => {
    return new Promise(res => {
      setTimeout(() => {
        res()
      }, time)
    })
  }

  render() {
    // @ts-ignore
    const {
      me_info,
      logoUrl,
      corpList = [],
      externalCorpList = [],
      showTripCard,
      isNewMall,
      groupedCardList
    } = this.props

    return (
      <div className="inertial-rolling h-100-percent w-100p h-100p">
        <PullToRefresh
          onRefresh={async () => {
            await Promise.race([this.refresh(), this.sleep(30000)])
          }}
        >
          <div className="home-page-wrapper inertial-rolling h-100-percent w-100p h-100p">
            <HomeNoticeBar
              className={'noticeBar-wrapper-home-page'}
              me_info={me_info}
            />
            <ChangeCorp me_info={me_info} logoUrl={logoUrl} corpList={corpList} externalCorpList={externalCorpList} />
            <HomeBanner className={`home-page-banner-container`} showDots={true} me_info={me_info} />
            <div className={classnames('home-page-card-container')}>
              {showTripCard && <UpComingTrip isNewHome={true} />}
              <HomeCardManage isNewMall={isNewMall} me_info={me_info} groupedCardList={groupedCardList} />
              <EditHome />
            </div>
          </div>
        </PullToRefresh>
        <Container name="@new-feature:modal" />
      </div>
    )
  }
}
