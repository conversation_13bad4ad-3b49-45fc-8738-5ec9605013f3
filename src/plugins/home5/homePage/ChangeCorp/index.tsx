import React, { useState, useEffect } from 'react'
import './index.less'
import { get } from 'lodash'
import { SearchBar, Popup, Tabs, ErrorBlock, Toast, Dialog } from '@hose/eui-mobile'
import { Fetch } from '@ekuaibao/fetch'
import { app, app as api } from '@ekuaibao/whispered'
import { useInstance } from '@ekuaibao/react-ioc'
import { useObserver } from 'mobx-react-lite'
import * as util from '../../../../lib/util'
import { MeIF, CorporationIF } from '@ekuaibao/ekuaibao_types'
import { ChangeCorpStore } from './ChangeCorp.store'
import { highLightSpan } from '../../../../lib/util/highLightSpan'
import { CorpTabsData } from '../../../../lib/util'
const ASC = app.require<any>('@images/icon-asc.svg')
const DSC = app.require<any>('@images/icon-dsc.svg')
const EKBIcon = app.require<any>('@elements/ekbIcon')
import { OutlinedTipsDone } from '@hose/eui-icons'
import { getBoolVariation } from "../../../../lib/featbit";

interface Props {
  me_info: MeIF
  logoUrl: string
  corpList: CorporationIF[]
  externalCorpList: CorporationIF[]
}

// todo---获取企业列表也应该放到这个组件里面

export default function ChangeCorp(props: Props) {
  const { me_info, logoUrl, corpList = [], externalCorpList = [] } = props
  const changeCorpStore = useInstance<ChangeCorpStore>(ChangeCorpStore.NAME)
  const [ShowTab, setShowTab] = useState<boolean>(false)
  const [ActiveKey, setActiveKey] = useState<string>(CorpTabsData[0].key)
  const [SearchStr, setSearchStr] = useState<string>('')
  const [AllCount, setAllCount] = useState(0)
  const [CorpName, setCorpName] = useState()

  const getList = () => {
    let list = corpList
    setAllCount(corpList ? corpList.length : 0)
    if (corpList.length > 0 && externalCorpList.length > 0) {
      setShowTab(true)
      setAllCount(corpList.length + externalCorpList.length)
      list = corpList
    } else if (externalCorpList.length > 0) {
      list = externalCorpList
      setAllCount(externalCorpList.length)
    }
    return list
  }

  const [corpModalVisible, setCorpModalVisible] = useState(false)

  useEffect(() => {
    changeCorpStore.init(getList())
    changeCorpStore.corpList = getList()
    changeCorpStore.optionCorps = getList()
  }, [corpList, externalCorpList])

  useEffect(() => {
    if (me_info?.staff?.corporationId) {
      setCorpName(changeCorpStore.handI18nName(me_info?.staff?.corporationId))
    }
  }, [me_info, changeCorpStore])

  const corporation = get(me_info, 'staff.corporationId') || { name: '' } // 无此节点会报错

  const corpOnClick = (e: any, corp: any) => {
    e.preventDefault()
    if (ActiveKey === 'ext') {
      Toast.show({ icon: 'fail', content: i18n.get('移动端暂不支持供应商协同门户功能，请在电脑端登录后进行操作。') })
      return
    }
    const urlParams = new URLSearchParams(window.location.search)
    const isOem = urlParams.get('type') === 'oem'
    Fetch.GET('/api/oem/v1/corporation/oemCheck', { oem: isOem ,selectedCorpId: corp.id})
      .then(res => {
        if (res === false) {
          //没有权限进入企业
          Dialog.alert({
            iconType: 'warn',
            title: '企业受控',
            content: '该企业登录受控，请从第三方进入'
          })
        } else {
          changeCorpStore.handleCorpChange(corp)
          setCorpModalVisible(false)
        }
      })
      .catch(() => {
        changeCorpStore.handleCorpChange(corp)
        setCorpModalVisible(false)
      })
  }

  const showModel = (e: any) => {
    e.preventDefault()
    console.log('-------',getBoolVariation('haf-22-idp_login', false))
    // TODO: 埋点
    if (!corpModalVisible) {
      api.track('Home_switch_enterprise', {
        actionName: '首页-切换企业',
        decice: 'applet',
        source: window.__PLANTFORM__,
        userId: api.getState()['@common'].me_info?.staff?.userId,
        corName: api.getState()['@common'].me_info?.staff?.corporationId?.name
      })
    }
    setCorpModalVisible(!corpModalVisible)
  }

  const stopModelPropagation = (e: any) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const corporationVersionKeyMap: Record<string, any> = util.SELECT_CORP_VERSION_MAP()

  const handleSearch = (val: any) => {
    setSearchStr(val)
    const value = val.trim().toLowerCase()
    changeCorpStore.handleSearch(value)
  }

  const handleSort = () => {
    changeCorpStore.sortStatus = changeCorpStore.sortStatus === 'asc' ? 'dsc' : 'asc'
  }

  /**
   * 切换 tab
   * @param key
   */
  const tabChange = (key: string) => {
    setActiveKey(key)
    let _corpList = []
    if (key === CorpTabsData[0].key) {
      _corpList = corpList
    } else {
      _corpList = externalCorpList
    }
    changeCorpStore.init(_corpList)
    changeCorpStore.corpList = _corpList
    changeCorpStore.optionCorps = _corpList
  }

  return useObserver(() => (
    <>
      {AllCount <= 1 ? (
        <></>
      ) : (
        <div className="change-corp-wrapper" onClick={showModel}>
          {logoUrl && <img className="home5-logo" src={logoUrl} />}
          <div className="home-page-title-wrapper">
            <div className="text-ellipsis">{CorpName}</div>
            <EKBIcon name="#EDico-caret-down" className="title-down" />
          </div>
          <Popup
            closeOnMaskClick={true}
            visible={corpModalVisible}
            radius
            title={i18n.get('切换企业')}
            bodyStyle={{
              height: ShowTab ? '50vh' : '40vh'
            }}
            className="change-corp-popup"
            onClose={() => setCorpModalVisible(false)}
          >
            <>
              {ShowTab && (
                <Tabs activeKey={ActiveKey} onChange={tabChange}>
                  {CorpTabsData.map(item => {
                    return <Tabs.Tab title={item.label} key={item.key} />
                  })}
                </Tabs>
              )}
              <div className={['change-corp-popup-content', ShowTab ? 'has-tab-content' : ''].join(' ')}>
                <div className="change-corp-popup-search" onClick={stopModelPropagation}>
                  {/*<div className="sort" onClick={handleSort}>*/}
                  {/*  <img src={changeCorpStore.sortStatus === 'asc' ? ASC : DSC} alt="" className="icon" />*/}
                  {/*</div>*/}
                  {AllCount > 5 && (
                    <div className="search">
                      <SearchBar placeholder={i18n.get('搜索企业名称')} onChange={handleSearch} />
                    </div>
                  )}
                </div>
                <div className={['change-corp-popup-list', AllCount > 5 ? 'has-search-list' : ''].join(' ')}>
                  {changeCorpStore.optionCorps.length && changeCorpStore.optionCorps.length > 0 ? (
                    changeCorpStore.optionCorps.map((corp: any, index: number) => (
                      <div
                        className={['corp-popup-item', corp.corporation.id === Fetch.ekbCorpId ? 'active' : ''].join(
                          ' '
                        )}
                        key={index}
                        onClick={e => corpOnClick(e, corp.corporation)}
                      >
                        <span className="text-ellipsis">
                          {highLightSpan(changeCorpStore.handI18nName(corp.corporation), SearchStr)}
                          {highLightSpan(
                            changeCorpStore.hasCorpVersion
                              ? corporationVersionKeyMap[corp.corporation.sourceChannel]
                              : '',
                            SearchStr
                          )}
                        </span>
                        {corp.corporation.id === Fetch.ekbCorpId && <OutlinedTipsDone />}
                      </div>
                    ))
                  ) : (
                    <ErrorBlock className={'search-empty'} status="empty" title={i18n.get('暂无数据')} />
                  )}
                </div>
              </div>
            </>
          </Popup>
        </div>
      )}
    </>
  ))
}
