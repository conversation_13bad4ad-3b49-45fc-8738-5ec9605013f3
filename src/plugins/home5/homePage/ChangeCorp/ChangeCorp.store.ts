/**
 *  Created by pw on 2022/10/31 5:22 PM.
 */
import { CorporationIF } from '@ekuaibao/ekuaibao_types'
import { app } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { computed, observable, reaction } from 'mobx'
import { toast, getUrlParamString, detectClientBrowser } from '../../../../lib/util'
import Cookies from 'js-cookie'
import { session } from '@ekuaibao/session-info'
import qs from 'qs'
import { getBoolVariation } from "../../../../lib/featbit";

const urlParams = qs.parse(location.search.slice(1))

const entryMap = {
  dingtalk: 'dingtalk',
  qywx: 'qywx',
  wxgzh: 'wxgzh'
}

export class ChangeCorpStore {
  static NAME = Symbol.for('CHANGE_CORP_BANNER')

  @observable sortStatus: string = 'asc' // 排序状态
  @observable corpList: CorporationIF[] = []
  @observable optionCorps: CorporationIF[] = []
  private selectCorpId: string = ''

  init(corps: CorporationIF[] = []) {
    this.corpList = corps
    this.optionCorps = corps
    reaction(
      () => [this.sortStatus],
      () => {
        this.handleSort()
      }
    )
  }

  @computed get hasCorpVersion(): boolean {
    return this.corpList.filter((v: any) => v.corporation.sourceChannel === 'GROUP').length > 0
  }

  async handleCorpChange(corp: CorporationIF) {
    if (corp.sourceChannel === 'V1') {
      const url = corp.sourceId
      let token = getUrlParamString(url, 'token')
      token = decodeURIComponent(token as string)
      app.store
        .dispatch('@home5/getV1UserInfo')({ token: token })
        .then((data: any) => {
          let json = data.id
          json = JSON.parse(json)
          json.token = token
          if (json.code === 100) {
            app.invokeService('@layout:login:v1', { loginData: json })
            return
          } else {
            toast.info(i18n.get('登录失效，请重新登录'))
            // @ts-ignore
            api.gotoLoginPage()
          }
        })
        .catch((e: any) => {
          toast.info(e.message)
          return
        })
    }
    if (getBoolVariation('haf-22-idp_login', false) && app.sdk?.switchCorporation) {
      session.remove('user')
      const staff = app.getState()['@common'].me_info?.staff
      return app.sdk.switchCorporation(corp, staff.id)
    }else {
      app.sdk?.logout?.()
    }
    const selectedCorpId: string = corp.id
    // 判断密码是否过期(应用内)
    const checkPwdExpire = await app.invokeService('@account5:password:expire:modal', {
      corpId: selectedCorpId,
      checkInApplication: true
    })
    if (!checkPwdExpire) {
      return
    }

    if (this.selectCorpId === corp.id) {
      return app.go(-1)
    }

    this.selectCorpId = corp.id

    Fetch.ekbCorpId = corp.id
    if (!window.PLATFORMINFO?.clusterURL) {
      app.store.dispatch('@home5/switchcorporationForLog')(selectedCorpId)
    }

    const params = window.IS_SMG
      ? Fetch.makeUrlParams(
          {
            ekbCorpId: Fetch.ekbCorpId
          },
          ['corpId', 'ekbCorpId']
        )
      : Fetch.makeUrlParams(
          {
            corpId: Fetch.ekbCorpId,
            ekbCorpId: Fetch.ekbCorpId,
            wxCorpId: Fetch.wxCorpId
          },
          ['corpId', 'ekbCorpId']
        )

    // 解决一财移动端主页上的企业, 没有根据 url上的 cropId选中问题
    if (urlParams['entry'] === entryMap.dingtalk && urlParams['urlCorpId'] && window.__PLANTFORM__ === 'APP') {
      sessionStorage && sessionStorage.setItem('isChangeCorp', 'true')
    }
    // 灰度方案的兜底策略，在登录后选择企业的时候，将cropId存到cookie
    const grayDomain =
      detectClientBrowser().indexOf('DingTalk') !== -1
        ? window.location.host
        : document.domain
            .split('.')
            .slice(-2)
            .join('.')
    Cookies.set('corpId', Fetch.ekbCorpId, { path: '', domain: grayDomain, expires: 365 })
    session.set('user', {
      accessToken: Fetch.accessToken,
      corpId: Fetch.ekbCorpId
    })
    setTimeout(() => {
      if (window.PLATFORMINFO?.clusterURL) {
        location.href = `${window.PLATFORMINFO?.clusterURL}tenant/login?corporationId=${Fetch.ekbCorpId}&${params}`
      } else {
        location.replace('?' + params)
      }
    }, 1000)
  }

  handleSearch(val: string = '') {
    const value = val.trim().toLowerCase()
    const filterOptions: CorporationIF[] = []
    const reg = new RegExp(value)
    this.corpList.forEach((item: any) => {
      const name = this.handI18nName(item.corporation)
      // 如果字符串中不包含目标字符会返回-1
      if (name.toLowerCase().match(reg)) {
        filterOptions.push(item)
      }
    })
    this.optionCorps = filterOptions
  }

  handleSort() {
    this.optionCorps = this.fnSortKey(this.corpList, 'nameSpell', this.sortStatus)
  }

  /**
   * 企业英文名称
   * @param corp 
   * @returns 
   */
  handI18nName(corp: any) {
    if(window?.i18n?.currentLocale === 'en-US' && corp?.enName) {
      return corp?.enName
    }
    return corp?.name
  }

  fnSortKey(arr: any[], key: string | any, order: string) {
    return arr.sort(function(a, b) {
      const value1 = a?.corporation[key]
      const value2 = b?.corporation[key]
      if (order === 'asc') {
        return value2.localeCompare(value1)
      } else {
        return value1.localeCompare(value2)
      }
    })
  }
}