@import '../../../../styles/layout.less';
@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-mobile/less/coverage.less';

.change-corp-wrapper {
  height: 88px;
  flex-shrink: 0;
  &:active {
    transform: scale(1);
    background-color: @color-bg-2;
  }

  .font-size-4;
  .font-weight-3;
  display: flex;
  align-items: center;
  padding: @space-5 @space-6;
  background-color: @color-white-1;
  transition: 0.1s all ease-in-out;

  .home5-logo {
    overflow: hidden;
    border-radius: 50%;
    margin-right: @space-4;
    width: @space-7;
    height: @space-7;
  }

  .home-page-title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    .text-ellipsis {
      max-width: 90%;
      font-weight: 500;
      font-size: 28px;
      color: rgba(39, 46, 59, 0.8);
    }

    .title-down {
      margin-left: @space-2;
      width: 30px;
      height: 30px;
      color: rgba(39, 46, 59, 0.8);
    }
  }
}

.change-corp-popup{
  .eui-popup-body-content{
    padding: 0 !important;
    overflow: hidden;
  }
}

.change-corp-popup-search {
  .search {
    padding: 0.16rem 0.32rem;
  }
  .sort {
    width: 0.68rem;
    height: 0.68rem;
    .icon {
      width: 0.48rem;
      height: 0.48rem;
      margin-top: 7px;
    }
  }
}

.has-tab-list {
  height: calc(100% - 1.88rem) !important;
}

.search-empty{
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}

.change-corp-popup-content{
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  &.has-tab-content {
    height: calc(100% - 0.84rem) !important;
  }
}

.change-corp-popup-list {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  .corp-popup-item:last-child{
    margin-bottom: 8px;
  }

  &.has-search-list {
    height: calc(100% - 1.04rem) !important;
  }

  .corp-popup-item {
    height: 0.96rem;
    padding: 0.16rem 0.32rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.16rem;
    background: var(--eui-bg-float);
    font-size: 0.32rem;
    font:var(--eui-font-head-r1);
    color: var(--eui-text-title);
    font-weight: 400;

    &:active{
      background-color: var(--eui-fill-pressed);
      border-radius: 4px;
    }

    &.active {
      color: var(--eui-primary-pri-500) !important;
      font-weight: 500;
    }

    .text-ellipsis {
      max-width: 90%;
      >span{
        color: var(--eui-primary-pri-500) !important
      }
    }

    .checkedIcon {
      margin-left: @space-4;
      color: @color-brand-2;
    }
  }
  .search-null {
    margin: 0 auto;
    padding: 0.3rem;
    width: 100%;
    height: 5.2rem;
    text-align: center;
    font-size: 0.16rem;
  }
}
