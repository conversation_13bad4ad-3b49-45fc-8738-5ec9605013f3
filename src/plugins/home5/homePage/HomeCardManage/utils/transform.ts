/**
 *  Created by pw on 2022/11/11 12:02 AM.
 */
import { get } from 'lodash'
import { app } from '@ekuaibao/whispered'
import moment from 'moment'
import { FlowTypeEnum } from './enums'
import { billStateMap } from '../../../staticUtil'
import { getDateDiff } from '../../../../../lib/util'
import { getStaffShowByConfig } from '../../../../../components/utils/fnDataLinkUtil'

const isUrgent = app.require<any>('@bill/bill-content/bill-item.isUrgent')

export function transformData(data: any, flowType: FlowTypeEnum = FlowTypeEnum.Flow): any {
  const prefix = flowType === FlowTypeEnum.Flow ? '' : 'flowId.'
  const state = get(data, `${prefix}flow.state`)
  const id = get(data, `${prefix}flow.id`) || get(data, 'flow.id') || get(data, 'id')
  const payingFailure = get(data, `${prefix}flow.payingFailure`, false)
  const urgent =
    get(data, `${prefix}plan.urgent`) || isUrgent(get(data, `${prefix}logs`, [])) || isUrgent(get(data, 'logs', []))
  const type = get(data, `${prefix}form.type`) || get(data, `${prefix}formType`) || get(data, 'formType')
  const title = get(data, `${prefix}form.title`)
  const isRiskWarning = get(data, `${prefix}calcRiskWarning`)
  const code = get(data, `${prefix}form.code`, '')
  const amount =
    get(data, `${prefix}form.amount`) ||
    get(data, ['flowId', `form`, `${type === 'payment' ? 'pay' : type}Money`, 'standard']) ||
    get(data, `form.amount`) ||
    get(data, ['form', `${type === 'payment' ? 'pay' : type}Money`, 'standard'])
  const currencySymbol =
    get(data, `${prefix}form.amount.standardSymbol`) ||
    get(data, ['flowId', `form`, `${type === 'payment' ? 'pay' : type}Money`, 'standardSymbol']) ||
    get(data, 'form.amount.standardSymbol') ||
    get(data, ['form', `${type === 'payment' ? 'pay' : type}Money`, 'standardSymbol'])
  const standardStrCode =
    get(data, `${prefix}form.amount.standardStrCode`) ||
    get(data, ['flowId', `form`, `${type === 'payment' ? 'pay' : type}Money`, 'standardStrCode']) ||
    get(data, 'form.amount.standardStrCode') ||
    get(data, ['form', `${type === 'payment' ? 'pay' : type}Money`, 'standardStrCode'])
  const specification =
    get(data, `${prefix}form.specification`) ||
    get(data, 'form.specification') ||
    get(data, `${prefix}form.specificationId`) ||
    get(data, 'form.specificationId')

  const auto = get(data, `${prefix}flow.systemGeneration`)
  const alterFlag = get(data, `${prefix}flow.alterFlag`) >= '1'
  const subsidyGeneration = get(data, `${prefix}flow.subsidyGeneration`) === 'surplus'
  const date =
    get(data, `${prefix}flow.expenseDate`) ||
    get(data, `flow.expenseDate`) ||
    get(data, 'expenseDate') ||
    get(data, `${prefix}form.submitDate`, '')
  let dateStr = ''
  if (date) {
    dateStr = getDateDiff(date)
  }
  let color = ''
  if (flowType === FlowTypeEnum.Flow) {
    const stateObject = billStateMap()[state]
    color = stateObject?.color
  }
  let staff = get(data, 'operator.staff', {})
  if (flowType === FlowTypeEnum.Backlog) {
    staff = get(data, 'flowId.form.submitterId', {})
  }

  const sourceSign = get(data, `${prefix}flow.sourceSign`, '')

  return {
    id,
    type,
    state,
    payingFailure,
    urgent,
    title,
    isRiskWarning,
    code,
    amount,
    currencySymbol,
    standardStrCode,
    specification,
    color,
    auto,
    alterFlag,
    subsidyGeneration,
    dateStr,
    staff,
    sourceSign
  }
}
