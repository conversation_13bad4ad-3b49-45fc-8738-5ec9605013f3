/**
 *  Created by pw on 2022/11/18 12:07 PM.
 */
import { Fetch } from '@ekuaibao/fetch'
import { ICard, IGroup } from '../Types'

export function getCardLabel(card: ICard | IGroup): string {
  const language = Fetch.staffSetting?.language
  let label = ''
  switch (language) {
    case 'zh-CN':
      label = card.cnLabel 
      break
    case 'en-US':
      label = card.enLabel
      break
    default:
      label = card.label
      break
  }
  // 自定义业务对象没有cnLable和enLabel
  label = label?.length ? label : i18n.get(card.label)
  return label
}
