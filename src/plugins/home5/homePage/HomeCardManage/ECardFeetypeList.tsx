import React from 'react'
import styles from './ECardFeetypeList.module.less'

interface Props {
  onFeetypeClick: (item: any) => void
  feetypeList: any[]
}
export const ECardFeetypeList: React.FC<Props> = props => {
  const { onFeetypeClick, feetypeList } = props

  return (
    <div className={styles['ecard-feetype-list-wrapper']}>
      {feetypeList.length > 0 && <div className="ecard-content-info">{i18n.get('点击我的常用消费，快捷使用')}</div>}
      <div className="ecard-content-feetype-wrapper">
        {feetypeList.map(item => {
          return (
            <div className="ecard-content-feetype-item" onClick={() => onFeetypeClick(item)}>
              <img style={{ backgroundColor: item.color }} className="ecard-content-feetype-item-img" src={item.icon} />
              <span className="ecard-content-feetype-item-label">{item.name}</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}
