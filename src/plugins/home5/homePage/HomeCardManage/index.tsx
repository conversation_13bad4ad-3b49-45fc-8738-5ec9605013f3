import React from 'react'
import { connect } from '@ekuaibao/mobx-store'
import './index.less'
import { useObserver } from 'mobx-react-lite'
import { HomeSkeletonList } from '../../util/SkeletonList'
import { Home5CardType } from '../../staticUtil'
import { HomeCardGroup } from './HomeCardGroup'
import { HomeCardList } from './CardList'
import { AllDataCard } from './AllDataCard'
import { HomeCardDatabase } from './HomeCardDatabase'
import { HomeCardSimple } from './HomeCardSimple'
import { MeIF } from '@ekuaibao/ekuaibao_types'
import { ICard, IGroup, IMalCard } from './Types'
import { HomeCardECard } from './HomeCardECard'

interface Props {
  isNewMall: boolean
  me_info?: MeIF
  groupedCardList?: IGroup[]
  mallCardDataList?: IMalCard[]
  editPersionSelectCards?: ICard[]
  editPersionUnSelectCards?: ICard[]
  isFirstLoadCardList?: boolean
  paidList?: any[]
  refreshKey?: any
}

const HomeCardManage: React.FC<Props> = props => {
  const {
    isNewMall,
    me_info,
    groupedCardList,
    mallCardDataList,
    editPersionSelectCards = [],
    editPersionUnSelectCards,
    isFirstLoadCardList,
    paidList,
    refreshKey
  } = props

  return useObserver(() => {
    if (isFirstLoadCardList && !groupedCardList?.length) {
      return (
        <div className={'home-page-manage-container'}>
          <HomeSkeletonList />
        </div>
      )
    }
    return (
      <div className={'home-page-manage-container'}>
        {groupedCardList?.map(group => {
          if (group.groupType === Home5CardType.GROUP) {
            return (
              <HomeCardGroup
                key={group.id}
                group={group}
                isNewMall={isNewMall}
                cardList={editPersionSelectCards}
                mallCardDataList={mallCardDataList}
                cardBacklogList={editPersionUnSelectCards}
              />
            )
          }
          if (group.groupType === Home5CardType.emptygroup) {
            return group.cards.map(card => {
              if (card.showType === Home5CardType.list) {
                return (
                  <HomeCardList
                    key={card.id}
                    card={card}
                    me_info={me_info}
                    mallCardDataList={mallCardDataList}
                    isHoseMall={isNewMall}
                    paidList={paidList}
                  />
                )
              }
              if (card.showType === Home5CardType.alldata) {
                return <AllDataCard key={card.id} card={card} />
              }
              if (card.showType === Home5CardType.database) {
                return <HomeCardDatabase key={card.id} card={card} />
              }
              if (card.code === 'eCard2') {
                //易商卡2.0卡片
                return <HomeCardECard key={card.id} card={card} refreshKey={refreshKey} />
              }
              return <HomeCardSimple key={card.id} card={card} />
            })
          }
          return null
        })}
      </div>
    )
  })
}

@((connect as any)((store: any) => ({
  cardList: store.states['@homePage'].cardList,
  cardBacklogList: store.states['@homePage'].cardBacklogList,
  isFirstLoadCardList: store.states['@homePage'].isFirstLoadCardList,
  editPersionSelectCards: store.states['@homePage'].editPersionSelectCards,
  editPersionUnSelectCards: store.states['@homePage'].editPersionUnSelectCards,
  paidList: store.states['@home5'].paidList,
  mallCardDataList: store.states['@homePage']?.mallCardDataList,
  refreshKey: store.states['@homePage'].refreshKey
})))
export default class HomeCardMangeView extends React.Component<Props> {
  render() {
    return <HomeCardManage {...this.props} />
  }
}
