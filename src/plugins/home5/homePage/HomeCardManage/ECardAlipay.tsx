import React, { useEffect, useState } from 'react'
import styles from './ECardAlipay.module.less'
import { OutlinedEditCopy, OutlinedGeneralQr, OutlinedGeneralScan } from '@hose/eui-icons'
import { ActionPanel, Button, Divider, Form, Input, Popup, Tag, Toast } from '@hose/eui-mobile'
import ALIPAY_LOGO from '../../images/alipay_logo.png'
import ALIPAY_BG from '../../images/alipay_bg.png'
import { ECardFeetypeList } from './ECardFeetypeList'
import { Fetch } from '@ekuaibao/fetch'
import { Action } from '@hose/eui-mobile/es/components/action-panel'
import { CardInfo } from './HomeCardECard'
import { aliPayScanOrQrcode, selectFeetypeAliPay } from '../../util/SimpleCardUtil'
import { app } from '@ekuaibao/whispered'
// @ts-ignore
import { CopyToClipboard } from 'react-copy-to-clipboard'

interface Props {
  eCardState: CardInfo
  refreshKey?: any
  cardType: string
}
export const ECardAlipay: React.FC<Props> = props => {
  const { refreshKey, eCardState, cardType } = props
  const [feetypeList, setFeetypeList] = useState([])
  const [visible, setVisible] = useState(false)
  const [actionPanelVisible, setActionPanelVisible] = useState(false)
  const [acticonType, setActionType] = useState('')
  const [form] = Form.useForm()

  useEffect(() => {
    getFeetypeList()
  }, [refreshKey])

  const getFeetypeList = () => {
    // 获取最近使用的费用类型列表，最多展示三个，没有的话不展示
    Fetch.GET('/api/v1/ecard/often/feeType', { cardType }).then((res: any) => {
      const list = res?.items?.slice(0, 3) ?? []
      setFeetypeList(list.length > 3 ? list.slice(0, 3) : list)
    })
  }
  const handleGoAlipay = () => {
    form.validateFields().then(values => {
      Fetch.POST('/api/engine/hdt/api/v1/alipay/enterprise/staffOpenAlipay', null, {
        body: {
          id: eCardState?.id,
          alipayAcc: values.alipayAccount,
          openCorpId: Fetch.ekbCorpId
        }
      }).then((res: any) => {
        const activeUrl = res?.data?.activeUrl
        if (activeUrl) {
          setVisible(false)
          app.invokeService('@layout:open:link', activeUrl, false, true, false)
        } else {
          Toast.show({
            content: res?.errMessage || i18n.get('授权失败，请稍后重试'),
            icon: 'fail'
          })
        }
      })
    })
  }
  const renderPopupInfo = () => {
    return (
      <Popup
        visible={visible}
        title={i18n.get('开卡账户绑定')}
        showCloseButton
        destroyOnClose
        onMaskClick={() => {
          setVisible(false)
        }}
        radius
        onClose={() => {
          setVisible(false)
        }}
      >
        <div className={styles['ecard-alipay-popup-content-wrapper']}>
          <div className="ecard-alipay-popup-content-top">
            <img className="ecard-alipay-popup-content-top-icon" src={ALIPAY_LOGO} />
            <span className="ecard-alipay-popup-content-top-text">{i18n.get('支付宝企业码')}</span>
          </div>
          <Form form={form}>
            <Form.Item
              name="alipayAccount"
              label={i18n.get('支付宝账户')}
              rules={[{ required: true, message: i18n.get('请输入支付宝账户') }]}
            >
              <Input border placeholder={i18n.get('请输入')} maxLength={60} />
            </Form.Item>
          </Form>
          <div className="ecard-alipay-popup-content-tips">
            <div className="ecard-alipay-popup-content-tips-title">{i18n.get('操作提示')}</div>
            <ul className="ecard-alipay-popup-content-tips-item">
              <li>{i18n.get('打开支付宝，进入【我的】页面')}</li>
              <li style={{ marginTop: 8 }}>{i18n.get('查看支付宝账户，复制粘贴填入此处完成绑定')}</li>
            </ul>
          </div>
          <Button size="large" block onClick={handleGoAlipay}>
            {i18n.get('去支付宝授权')}
          </Button>
        </div>
      </Popup>
    )
  }
  const handleOnClick = () => {
    setVisible(true)
  }
  if (!['NORMAL', 'ACTIVATING'].includes(eCardState?.state)) {
    return (
      <div className={styles['ecard-alipay-wrapper']}>
        {renderPopupInfo()}
        <div className="ecard-alipay-not-activated">
          <img src={ALIPAY_LOGO} className="ecard-alipay-not-activated-icon" />
          <div className="ecard-alipay-not-activated-right">
            <div className="ecard-alipay-not-activated-right-title-wrapper">
              <div className="ecard-alipay-not-activated-right-title">{i18n.get('支付宝企业码')}</div>
              <Tag color="neu" fill="outline" size="small">
                {i18n.get('适用于个人垫付')}
              </Tag>
            </div>
            <div className="ecard-alipay-not-activated-right-info">
              <span>{i18n.get('自动记账')}</span>
              <Divider direction="vertical" style={{ width: 1, height: 10, margin: '0 4px' }} />
              <span>{i18n.get('全网回票')}</span>
              <Divider direction="vertical" style={{ width: 1, height: 10, margin: '0 4px' }} />
              <span>{i18n.get('自动报销')}</span>
            </div>
          </div>
        </div>
        <img className="ecard-alipay-normal-bg" src={ALIPAY_BG} />
        {eCardState?.state !== 'LOG_OFF' && (
          <Button block category="secondary" size="small" theme="highlight" onClick={handleOnClick}>
            {i18n.get('去开卡')}
          </Button>
        )}
      </div>
    )
  }

  const onFeetypeClick = async (feetype: any) => {}

  const handleScan = () => {
    // 第一期先不做费用类型选择，直接显示二维码，等支付宝支持费用类型的时候再打开这个功能
    // setActionPanelVisible(true)
    // setActionType('scan')
    aliPayScanOrQrcode(eCardState.id, 2)
  }

  const handleShowQRCode = () => {
    // 第一期先不做费用类型选择，直接显示二维码，等支付宝支持费用类型的时候再打开这个功能
    // setActionPanelVisible(true)
    // setActionType('qrcode')
    aliPayScanOrQrcode(eCardState.id, 1)
  }
  const actions: Action[] = [
    { text: '去填写', key: 'yes' },
    { text: '暂不填写，直接支付', key: 'no' }
  ]
  const fnSacn = (key: string | number) => {
    if (key === 'yes') {
      selectFeetypeAliPay(eCardState.id, 2)
    } else if (key === 'no') {
      aliPayScanOrQrcode(eCardState.id, 2)
    }
  }
  const fnShowQRCode = (key: string | number) => {
    if (key === 'yes') {
      selectFeetypeAliPay(eCardState.id, 1)
    } else if (key === 'no') {
      aliPayScanOrQrcode(eCardState.id, 1)
    }
  }
  const handleActionPanelSelect = (action: Action) => {
    setActionPanelVisible(false)
    if (acticonType === 'scan') {
      fnSacn(action.key)
    } else if (acticonType === 'qrcode') {
      fnShowQRCode(action.key)
    }
  }
  const renderActionPanel = () => {
    return (
      <ActionPanel
        extra={i18n.get('是否需要填写费用类型')}
        cancelText="取消"
        visible={actionPanelVisible}
        actions={actions}
        onAction={handleActionPanelSelect}
        onClose={() => setActionPanelVisible(false)}
      />
    )
  }
  const handleCopy = () => {
    Toast.show({
      icon: 'success',
      content: i18n.get('复制成功')
    })
  }

  return (
    <div className={styles['ecard-alipay-wrapper']}>
      {renderActionPanel()}
      <div className="ecard-alipay-normal">
        <div className="ecard-alipay-normal-top">
          <img src={ALIPAY_LOGO} className="ecard-alipay-normal-top-icon" />
          <div className="ecard-alipay-normal-top-right">
            <div className="ecard-alipay-normal-title-wrapper">
              <div className="ecard-alipay-normal-title">{i18n.get('支付宝企业码')}</div>
              <Tag color="neu" fill="outline" size="small">
                {i18n.get('适用于个人垫付')}
              </Tag>
            </div>
            <div className="ecard-alipay-normal-info">
              <span>{i18n.get('自动记账')}</span>
              <Divider direction="vertical" style={{ width: 1, height: 10, margin: '0 4px' }} />
              <span>{i18n.get('全网回票')}</span>
              <Divider direction="vertical" style={{ width: 1, height: 10, margin: '0 4px' }} />
              <span>{i18n.get('自动报销')}</span>
            </div>
          </div>
        </div>
        <div className="ecard-alipay-normal-features">
          <div className="ecard-alipay-normal-features-item" style={{ marginRight: 12 }} onClick={handleScan}>
            <OutlinedGeneralScan fontSize={20} color="var(--eui-icon-n1)" style={{ marginRight: 8 }} />
            {i18n.get('扫一扫')}
          </div>
          <div className="ecard-alipay-normal-features-item" onClick={handleShowQRCode}>
            <OutlinedGeneralQr fontSize={20} color="var(--eui-icon-n1)" style={{ marginRight: 8 }} />
            {i18n.get('展码付')}
          </div>
        </div>
        <Divider dashed style={{ margin: '12px 0' }} />
        <div className="ecard-alipay-normal-email">
          <Tag
            style={{
              '--background-color': 'var(--eui-primary-pri-100',
              '--text-color': 'var(--eui-primary-pri-600)'
            }}
          >
            {i18n.get('收票邮箱')}
          </Tag>
          <span className="ecard-alipay-normal-email-content">{eCardState?.receiveInvoiceEmail}</span>
          <CopyToClipboard text={eCardState?.receiveInvoiceEmail}>
            <OutlinedEditCopy fontSize={12} className="email-copy" onClick={handleCopy} />
          </CopyToClipboard>
        </div>
      </div>
      <img className="ecard-alipay-normal-bg" src={ALIPAY_BG} />
      {feetypeList.length > 0 && <ECardFeetypeList feetypeList={feetypeList} onFeetypeClick={onFeetypeClick} />}
    </div>
  )
}
