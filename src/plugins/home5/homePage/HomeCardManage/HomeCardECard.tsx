import React, { useEffect, useState } from 'react'
import { Card, Space } from '@hose/eui-mobile'
import { ICard } from './Types'
import { getCardLabel } from './utils/helps'
import styles from './HomeCardECard.module.less'
import { app } from '@ekuaibao/whispered'
import { OutlinedDirectionLeft, OutlinedDirectionRight } from '@hose/eui-icons'
import { ECard2 } from './ECard2'
import { ECardAlipay } from './ECardAlipay'

export interface CardInfo {
  id: string
  cardNo: string
  state: string
  cardType: string
  receiveInvoiceEmail: string | null
}

interface Props {
  card: ICard
  refreshKey?: any
}
export const HomeCardECard: React.FC<Props> = props => {
  const { card, refreshKey } = props
  const [eCardState, setECardState] = useState<any>()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [components, setComponents] = useState([])
  const totalLength = components.length

  useEffect(() => {
    fnGetECardState()
  }, [refreshKey])

  useEffect(() => {
    if (eCardState) {
      const componentList: any[] = []
      const list = eCardState.data || []
      list.forEach((item: CardInfo) => {
        if (item.cardType === 'ALIPAY_PA') {
          componentList.push(
            <ECardAlipay key={'ECardAlipay'} cardType={item.cardType} eCardState={item} refreshKey={refreshKey} />
          )
        } else if (item.cardType === 'ALIPAY') {
          componentList.push(
            <ECard2 key={'ECard2'} cardType={item.cardType} eCardState={item} refreshKey={refreshKey} />
          )
        }
      })
      setComponents(componentList)
    }
  }, [eCardState])

  const fnGetECardState = async () => {
    const meInfo = await app.dataLoader('@common.me_info').load()
    const userInfo = meInfo?.staff
    if (!userInfo) return
    const res = await app.invokeService('@common:get:staff:card:info', {
      openUserId: userInfo.id,
      openCorpId: userInfo.corporationId?.id
    })
    setECardState(res)
  }

  const title = getCardLabel(card)

  const onLeftClick = () => {
    if (currentIndex === 0) return
    setCurrentIndex(currentIndex - 1)
  }
  const onRightClick = () => {
    if (currentIndex === totalLength - 1) return
    setCurrentIndex(currentIndex + 1)
  }
  const renderExtra = () => {
    if (totalLength > 1) {
      // 多张卡片的时候展示左右切换按钮
      return (
        <Space style={{ '--gap': '16px' }}>
          <OutlinedDirectionLeft
            fontSize={16}
            onClick={onLeftClick}
            color={currentIndex === 0 ? 'var(--eui-icon-disabled)' : 'var(--eui-icon-n2)'}
          />
          <OutlinedDirectionRight
            fontSize={16}
            onClick={onRightClick}
            color={currentIndex === totalLength - 1 ? 'var(--eui-icon-disabled)' : 'var(--eui-icon-n2)'}
          />
        </Space>
      )
    }
    return null
  }

  if (totalLength === 0) {
    return null
  }

  return (
    <div className={styles['home-card-ecard-container']}>
      <Card title={`${title}${totalLength > 1 ? `(${totalLength})` : ''}`} extra={renderExtra()}>
        {components[currentIndex]}
      </Card>
    </div>
  )
}
