import React, { useRef } from 'react'
import { app } from '@ekuaibao/whispered'
import { get } from 'lodash'
import './MyBillList.less'
import { Button } from '@hose/eui-mobile'
const EKBIcon = app.require<any>('@elements/ekbIcon')
import { OutlinedGeneralApproval } from '@hose/eui-icons'
import { enableHidingFinishedBills } from '../../../../../lib/featbit/utils'

interface Props {
  paidList: any[]
}

const BillCompleteEntrance: React.FC<Props> = props => {
  const { paidList = [] } = props
  const ref = useRef<HTMLDivElement>(null)

  const handleOnClick = () => {
    app.go('/bills-segment/paidList', false)
    app.store.dispatch('@home5/setSegmentActiveKey')("allFinish")
  }

  if (enableHidingFinishedBills()) {
    return null
  }

  if (!paidList?.length) {
    return (
      <div className="bill-complete-entrance-wrapper" onClick={handleOnClick}>
        <div className="bill-complete-entrance-left-wrapper">
          <OutlinedGeneralApproval className="bill-complete-entrance-left-icon" fontSize={16} />
          <div className={'bill-complete-entrance-left'}>{i18n.get(`查看已完成单据`)}</div>
        </div>
        <EKBIcon name="#EDico-right-default" className={'bill-complete-entrance-icon'} />
      </div>
    )
  }
  const count = paidList.length > 99 ? '99+' : paidList.length

  const handleComplete = () => {
    const ids = paidList.map(item => get(item, 'flow.id'))
    app.store.dispatch('@home5/setSegmentActiveKey')("unConfirmed")
    app
      .invokeService('@approve:do:confirm', ids, { name: 'freeflow.archive' })
      .then(() => {
        app.store.dispatch('@home5/getArchivedAndPaidList')()
      })
      .catch(() => {
        app.store.dispatch('@home5/getArchivedAndPaidList')()
      })
  }

  return (
    <div className="bill-complete-entrance-wrapper" onClick={handleOnClick}>
      <div className="bill-complete-entrance-left-wrapper">
        <OutlinedGeneralApproval className="bill-complete-entrance-left-icon" fontSize={16} />
        <div className={'bill-complete-entrance-left'}>{i18n.get(`你有 {__k0} 张单据审批通过`, { __k0: count })}</div>
      </div>
      <Button category="secondary" className="bill-complete-entrance-button" theme="highlight" onClick={handleComplete}>
        {i18n.get('全部确认')}
      </Button>
    </div>
  )
}

export default BillCompleteEntrance
