import React from 'react'
import { ICard } from '../Types'
import './index.less'
import { CodeType } from '../../../util/card.enum'
import MyBillList from './MyBillList'
import { MeIF } from '@ekuaibao/ekuaibao_types'
import { IMalCard } from '../Types'
import AuditPendingCard from '../AllDataCard/AuditPendingCard'
import MallCard from '../elements/MallCard'
import { mallCardDataLength } from '../HomeCardGroup'
import TodoListCard from "./TodoListCard";
interface Props {
  card: ICard
  isHoseMall?: boolean
  me_info?: MeIF
  mallCardDataList?: IMalCard[]
  paidList?: any[]
}

export const HomeCardList: React.FC<Props> = props => {
  const { card, me_info, mallCardDataList, paidList, isHoseMall } = props
  if (card.dynamicSupportValue && card.detail) {
    const { value } = card.detail.prompt
    if (!value) {
      return null
    }
  }
  const renderContent = () => {
    if (card.code === CodeType.myBill) {
      return <MyBillList card={card} paidList={paidList} />
    }
    if (card.code === CodeType.auditPending) {
      return <AuditPendingCard card={card} />
    }
    if (card.code === CodeType.allTodo) {
      return <TodoListCard card={card} />
    }
    if (card.code === CodeType.mall && isHoseMall && mallCardDataList.length >= mallCardDataLength) {
      return <MallCard meInfo={me_info} card={card} mallCardDataList={mallCardDataList} />
    }
    return <></>
  }

  return <>{renderContent()}</>
}
