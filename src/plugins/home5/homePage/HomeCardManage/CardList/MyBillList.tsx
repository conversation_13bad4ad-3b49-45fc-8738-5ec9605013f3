import React from 'react'
import { ICard } from '../Types'
import { Card } from '@hose/eui-mobile'
import { app } from '@ekuaibao/whispered'
import './MyBillList.less'
import { mapForCodeToPath } from '../../../staticUtil'
import BillCompleteEntrance from './BillCompleteEntrance'
import { get } from 'lodash'
import { CardListBill } from '../elements/CardListBill'
import HomeCardRightPart from '../../../util/HomeCardRight'
import { getCardLabel } from '../utils/helps'
import EmptyWidget from '../elements/EmptyWidget'

interface Props {
  card: ICard
  paidList?: any[]
}

export const MyBillList: React.FC<Props> = props => {
  const { card, paidList } = props

  const handleLookAll = () => {
    app.go(`/${mapForCodeToPath[card.code]}`, false)
  }

  return (
    <div className="home-card-list-container">
      <Card
        title={getCardLabel(card)}
        extra={<HomeCardRightPart hasRight={true} label={i18n.get('全部')} onClick={handleLookAll} />}
      >
        <BillCompleteEntrance paidList={paidList} />
        {!!card?.detail?.prompt?.value ? (
          <Card_Bill card={card} />
        ) : (
          <EmptyWidget size={100} type="myBill" tips="暂无单据" />
        )}
      </Card>
    </div>
  )
}

interface ICardBill {
  card: ICard
}

const Card_Bill: React.FC<ICardBill> = props => {
  const { card } = props
  const list = get(card, 'detail.list', [])
  if (!list?.length) {
    return null
  }

  const handleClickItem = (item: any = {}) => {
    const { id, type, state, isAlwaysPrint, data } = item
    const params = { id, formType: type, state, isAlwaysPrint }

    const configsList = get(data, 'form.specification.configs')
    const loanMoney = get(data, 'form.amount.standard')
    const stateLoan = get(data, 'flow.state')
    const getId = get(data, 'id')
    const isLoan =
      configsList &&
      configsList.find((item: { ability: string }) => {
        return item.ability === 'loan'
      })
    app.invokeService('@bill:update:flow:append:info', { needConfigButton: true })
    if (isLoan && loanMoney > 0 && (stateLoan === 'paid' || stateLoan === 'archived')) {
      app
        .invokeService('@bill:get:loanpackage:by:flowId', getId)
        .then(() => {
          app.invokeService('@home:save:specification').then(() => {
            app.invokeService('@home:click:bill', params, 'homePage')
          })
        })
        .catch((err: any) => {
          app.invokeService('@home:save:specification').then(() => {
            app.invokeService('@home:click:bill', params, 'homePage')
          })
        })
    } else {
      app.invokeService('@home:save:specification').then(() => {
        app.invokeService('@home:click:bill', params, 'homePage')
      })
    }
  }

  return <CardListBill list={list} onClickItem={handleClickItem} />
}

export default MyBillList
