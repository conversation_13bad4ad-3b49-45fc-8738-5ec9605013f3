import React from 'react'
import { Card } from '@hose/eui-mobile'
import { ICard } from '../Types'
import { getCardLabel } from '../utils/helps'
import HomeCardRightPart from '../../../util/HomeCardRight'
import EmptyWidget from '../elements/EmptyWidget'
import { app } from '@ekuaibao/whispered'
import TodoItem from '../elements/TodoItem'
import styles from './TodoListCard.module.less'

interface Props {
  card: ICard
}

export const TodoListCard: React.FC<Props> = ({ card }) => {
  const handleLookAll = () => {
    app.go('/allTodo')
  }
  return (
    <div className="home-card-list-container">
      <Card
        title={getCardLabel(card)}
        extra={<HomeCardRightPart hasRight={true} label={i18n.get('全部')} onClick={handleLookAll} />}
      >
        {!!card?.detail?.prompt?.value ? (
          <TodoList card={card} />
        ) : (
          <EmptyWidget size={100} type="smallNoContent" tips="暂无数据" />
        )}
      </Card>
    </div>
  )
}
export const TodoList: React.FC<Props> = ({ card }) => {
  return (
    <div className={styles['card-todo-list-wrapper']}>
      {card?.detail?.list?.map((item: any) => {
        return <TodoItem className="todo-item" item={item} key={item.taskId} />
      })}
    </div>
  )
}

// export const TodoItem: React.FC<{ item: any }> = ({ item }) => {
//   const isUrgent = item?.priority === 'Urgent'
//   const title = item?.body?.title
//   const subTitle = item?.description
//   const time = moment(item?.updateTime).format('YYYY-MM-DD HH:mm')
//   return (
//     <div className={styles['todo-item']}>
//       <div className="todo-item-title">
//         {isUrgent && <span className="urgent">[急]</span>}
//         {title}
//       </div>
//       <div className="todo-item-subtitle">{subTitle}</div>
//       <div className="todo-item-time">{time}</div>
//     </div>
//   )
// }
export default TodoListCard
