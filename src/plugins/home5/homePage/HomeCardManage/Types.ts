import { CodeType } from "../../util/card.enum";

/**
 *  Created by pw on 2022/11/2 4:46 PM.
 */
export interface IAvailableRange {
  fullVisible: boolean
  chargeCodes: string[]
  permissions?: any
  visibility?: any
  budgetEditAuthority: boolean
  menuPermission: boolean
  expansionCenter: boolean
}

export interface ICard {
  pipeline: number
  grayver: string
  version: number
  active: boolean
  createTime: any
  updateTime: any
  id: string
  deviceType: string
  label: string
  icon: string
  color: string
  showType: string
  selected: boolean
  pid: string
  condition: any
  weight: number
  dynamicSupportValue: boolean
  travelRemindValue: boolean
  type: string
  code: CodeType,
  availableRange: IAvailableRange
  menuShowSimpleInfo?: any
  childrenIndexId: any[]
  remind: string
  businessSummary: boolean
  sourceType?: any
  source?: any
  detail?: any
  cards?: ICard[]
  sophons: Sophon[]
  cnLabel?:string
  enLabel?:string
}

export interface ICardBusinesssummaryList {
  id: string
  type: string
  logId: number
  state: string
  active: boolean
  flowId: any
  grayver: string
  ownerId: string
  version: number
  isUrgent: boolean
  nodeName: string
  pipeline: number
  crossCorp: boolean
  createTime: any
  updateTime: any
  rejectAlert: boolean
  corporationId: string
  rejectEndTime: number
  remindEndTime: number
  addCountReport: boolean
  autoApproveType: string
  dataCorporationId?: any
  sourceCorporationId?: any
}

export interface IPrompt {
  value: number
  type: string
}

export interface ICardBusinesssummary {
  code: string
  title?: any
  list: ICardBusinesssummaryList[]
  prompt: IPrompt
  data: any
}

export interface IMalCard {
  picture: string
  title: string
  url: string
}

interface StaffProps extends StringAnyProps {
  name: string
  id: string
  avatar: string
}

interface FormProps extends StringAnyProps {
  title: string
  submitterId: StaffProps
}

interface FlowProps extends StringAnyProps {
  form: FormProps
}

export interface AuditItemProps extends StringAnyProps {
  flowId: FlowProps
}

export interface IMalCard {
  picture: string
  title: string
  url: string
}

export interface IVisibility {
  fullVisible: boolean
  staffs: any[]
  roles: any[]
  departments: any[]
  departmentsIncludeChildren: boolean
}

export interface Sophon {
  ability: string
}

export interface IAvailableRange {
  fullVisible: boolean
  chargeCodes: string[]
  permissions?: any
  visibility?: any
  budgetEditAuthority: boolean
  menuPermission: boolean
  expansionCenter: boolean
}

export interface IGroup {
  id: string
  groupType: string
  label: string
  weight: number
  cnLabel: string
  enLabel?: any
  icon: string
  showType: string
  groupLayout?: string
  cards: ICard[]
}

export interface IPage {
  pageLayout: string
  groups: IGroup[]
}

export interface IHomePageCardResponse {
  pipeline: number
  grayver: string
  version: number
  active: boolean
  createTime: number
  updateTime: number
  nameSpell: string
  code: string
  corporationId: string
  sourceCorporationId?: any
  dataCorporationId?: any
  id: string
  name: string
  visibility: IVisibility
  forPerson: boolean
  personVersionId?: any
  type: string
  description: string
  ownerId: string
  editorId: string
  page: IPage
  mustUpdateTemplate: boolean
  state: string
  publishedTime: number
}

export interface ICardMetaData {
  code: string
  showTypeList: string[]
  dynamicSupport: boolean
  travelRemind?: boolean
  weight: number
}

export interface IHomeTemplateVersion {
  isHaveNewVersion: boolean
  homePageTemplateVersionId: string
  homePageTemplate: IHomePageCardResponse
  templateName: string
  publishedTime: number
}

export interface IQuestionnaires {
  id: string
  codes: string[]
}
