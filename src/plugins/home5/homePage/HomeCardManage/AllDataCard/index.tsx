import React from 'react'
import './index.less'
import { CodeType } from '../../../util/card.enum'
import { ICard } from '../Types'
import AuditPendingCard from './AuditPendingCard'
import { get } from 'lodash'
import TripListCard from '../../../elements/cards/TripListCard'
import { HomeCardSimple } from '../HomeCardSimple'
interface Props {
  card: ICard
}

export const AllDataCard: React.FC<Props> = props => {
  const { card } = props
  if (card.dynamicSupportValue && card.detail) {
    const value = card.detail?.prompt?.value
    if (!value) {
      return null
    }
  }
  if (card.code === CodeType.auditPending) {
    return <AuditPendingCard card={card} />
  }
  const renderContent = () => {
    if (card.code === CodeType.dataLinkEntity) {
      const dataLinkEntity = get(card, 'detail.dataLinkEntity')
      const type = get(dataLinkEntity, 'platformId.type', '')
      if (type === 'TRAVEL_MANAGEMENT') {
        // 只有行程管理才展示
        return <TripListCard key={card.id} data={card} />
      } else {
        return <HomeCardSimple key={card.id} card={card} />
      }
    }
    return <HomeCardSimple key={card.id} card={card} />
  }

  return <div className={'home-card-all-data-container'}>{renderContent()}</div>
}
