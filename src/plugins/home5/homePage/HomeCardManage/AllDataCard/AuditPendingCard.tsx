import React, { useEffect } from 'react'
import { Card } from '@hose/eui-mobile'
import './AuditPendingCard.less'
import { AuditItemProps, ICard } from '../Types'
import { app } from '@ekuaibao/whispered'
import { mapForCodeToPath } from '../../../staticUtil'
import { FlowTypeEnum } from '../utils/enums'
import { CardListBill } from '../elements/CardListBill'
import HomeCardRightPart from '../../../util/HomeCardRight'
import { getCardLabel } from '../utils/helps'
import EmptyWidget from '../elements/EmptyWidget'
import {startOpenFlowPerformanceStatistics} from '../../../../../lib/flowPerformanceStatistics'

interface Props {
  card: ICard
}

export const AuditPendingCard: React.FC<Props> = props => {
  const { card } = props

  const handleClick = (item?: AuditItemProps) => {
    // 通过 filter (条件)区分是否点击的是查看全部
    if (item && item.filter) {
      const filter = JSON.parse(item.filter)
      const filterType = item.isUrgent ? 'all' : filter.sceneIndex
      app.invokeService('@approve:setSelectBillsType', filterType)
    } else {
      app.invokeService('@approve:setSelectBillsType', 'all')
    }
    app.go(`/${mapForCodeToPath[card.code]}`, false)
  }
  const { list = [] } = card?.detail || {}
  const handleClickItem = (item: any = {}) => {
    app.invokeService('@home:save:specification').then(() => {
      app.go('/approve/approving/expense/' + item.id + '/approving', false)
      startOpenFlowPerformanceStatistics()
    })
  }

  const renderContent = () => {
    if (!list?.length) {
      return <EmptyWidget size={100} type="audit" tips="暂无审批" />
    }
    return <CardListBill list={list} onClickItem={handleClickItem} flowType={FlowTypeEnum.Backlog} />
  }

  return (
    <div className="audit-pending-card-container">
      <Card
        title={getCardLabel(card)}
        extra={<HomeCardRightPart hasRight={true} label={i18n.get('全部')} onClick={() => handleClick()} />}
      >
        {renderContent()}
      </Card>
    </div>
  )
}

export default AuditPendingCard
