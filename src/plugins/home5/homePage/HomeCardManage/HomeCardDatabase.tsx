import React from 'react'
import { ICard } from './Types'
import { CodeType } from '../../util/card.enum'
import AuditPendingCard from './AllDataCard/AuditPendingCard'
import { HomeCardSimple } from './HomeCardSimple'
interface Props {
  card: ICard
}

export const HomeCardDatabase: React.FC<Props> = props => {
  const { card } = props
  const { detail } = card || {}
  if (card.dynamicSupportValue && detail) {
    const { value } = detail.prompt
    if (!value) {
      return null
    }
  }

  if (card.code === CodeType.auditPending) {
    return <AuditPendingCard key={card.id} card={card} />
  }
  return <HomeCardSimple key={card.id} card={card} />
}
