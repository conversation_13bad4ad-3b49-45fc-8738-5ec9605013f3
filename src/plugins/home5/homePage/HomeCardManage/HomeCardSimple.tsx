import React, { useEffect } from "react";
import './HomeCardSimple.less'
import { Card } from '@hose/eui-mobile'
import { ICard } from './Types'
import { jumpRouter } from '../../util/SimpleCardUtil'
import HomeCardRightPart from '../../util/HomeCardRight'
import { CodeType } from '../../util/card.enum'
import HomeCardIcon from './elements/HomeCardIcon'
import { getCardLabel } from './utils/helps'
import MyLoanCard from './elements/MyLoanCard'
import { addMapJsApi } from "../../../../lib/mapjsapi";
import { app } from "@ekuaibao/whispered";

interface Props {
  card: ICard
}

export const HomeCardSimple: React.FC<Props> = props => {
  const { card } = props

  const handleRightClick = () => {
    jumpRouter(card)
  }

  if (card.code === CodeType.myLoan) {
    return <MyLoanCard data={card} />
  }

  if (card.dynamicSupportValue && card.detail) {
    const { value } = card.detail.prompt || {}
    if (!value && card.detail?.dataLinkCount < 1) {
      return null
    }
  }
  let title = getCardLabel(card)
  if (title && ['行程', 'Travel'].includes(title)) {
    title = i18n.get('待订购')
  }

  useEffect(() => {
    if (card?.code === 'privateCar') {
      setTimeout(addMapJsApi, 0)
    }
  },[card.code])

  return (
    <Card
      radiusSize={'middle'}
      className={'home-page-card home-page-simple-card'}
      title={
        <div className="home-card-title-wrapper">
          <HomeCardIcon card={card} fontSize={24} />
          <span className="title">{title}</span>
        </div>
      }
      extra={<HomeCardRightPart card={card} onClick={handleRightClick} />}
      onHeaderClick={handleRightClick}
    />
  )
}
