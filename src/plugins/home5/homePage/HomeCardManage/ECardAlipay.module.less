@import '~@eku<PERSON>bao/eui-styles/less/token-mobile.less';

.ecard-alipay-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  :global {
    .ecard-alipay-normal {
      width: 100%;
      border-radius: 16px;
      background: linear-gradient(90deg, #E9F2FF 2.65%, #C9E0FF 99.38%);
      padding: 24px 32px;
      display: flex;
      flex-direction: column;
      .ecard-alipay-normal-top {
        display: flex;
        flex-direction: row;
        margin-bottom: 24px;
        align-items: center;
        .ecard-alipay-normal-top-icon {
          width: 64px;
          height: 64px;
          margin-right: 16px;
        }
        .ecard-alipay-normal-top-right {
          display: flex;
          flex-direction: column;
          .ecard-alipay-normal-title-wrapper {
            display: flex;
            flex-direction: row;
            align-items: center;
            .ecard-alipay-normal-title {
              margin-right: 16px;
              color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
              font: var(--eui-font-head-b1);
            }
          }
          .ecard-alipay-normal-info {
            margin-top: 4px;
            display: flex;
            flex-direction: row;
            align-items: center;
            color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
            font: var(--eui-font-note-r2);
          }
        }
      }
      .ecard-alipay-normal-features {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        .ecard-alipay-normal-features-item {
          display: flex;
          padding: 16px 24px;
          justify-content: center;
          flex: 1;
          align-items: center;
          border-radius: 40px;
          background: var(--eui-static-white-90, rgba(255, 255, 255, 0.9));
          font: var(--eui-font-body-r1);
          color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
        }
      }
      .ecard-alipay-normal-email {
        display: flex;
        flex-direction: row;
        align-items: center;
        .ecard-alipay-normal-email-content {
          margin-left: 16px;
          margin-right: 8px;
          color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
          font: var(--eui-font-note-r2);
        }
        .email-copy {
          color: var(--eui-icon-n2);
        }
      }
    }
    .ecard-alipay-normal-bg {
      position: absolute;
      right: 0;
      top: 0;
      width: 108px;
      height: 108px;
    }
    .ecard-alipay-not-activated {
      display: flex;
      padding: 24px 32px;
      width: 100%;
      flex-direction: row;
      margin-bottom: 16px;
      border-radius: 16px;
      background: linear-gradient(93deg, #f2f3f5 1.94%, #e5e6eb 100.34%);
      align-items: center;
      .ecard-alipay-not-activated-icon {
        width: 80px;
        height: 80px;
        margin-right: 16px;
      }
      .ecard-alipay-not-activated-right {
        display: flex;
        flex-direction: column;
        .ecard-alipay-not-activated-right-title-wrapper {
          display: flex;
          flex-direction: row;
          align-items: center;
          margin-bottom: 4px;
          .ecard-alipay-not-activated-right-title {
            color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
            font: var(--eui-font-head-b1);
            margin-right: 16px;
          }
        }
        .ecard-alipay-not-activated-right-info {
          display: flex;
          flex-direction: row;
          align-items: center;
          color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
          font: var(--eui-font-note-r2);
        }
      }
    }
  }
}
.ecard-alipay-popup-content-wrapper {
  display: flex;
  flex-direction: column;
  :global {
    .ecard-alipay-popup-content-top {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 48px;
      .ecard-alipay-popup-content-top-icon {
        width: 120px;
        height: 120px;
        margin-bottom: 24px;
        margin-top: 32px;
      }
      .ecard-alipay-popup-content-top-text {
        color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
        font: var(--eui-font-head-b1);
      }
    }
    .eui-list-body {
      border-top: none;
      border-bottom: none;
      .eui-list-item {
        padding-left: 0px;
        padding-right: 0px;
        .eui-list-item-content-main {
          padding: 24px 0;
        }
      }
    }
    .ecard-alipay-popup-content-tips {
      border-radius: 12px;
      background: var(--eui-bg-float-overlay, #f7f8fa);
      display: flex;
      flex-direction: column;
      padding: 24px;
      margin-bottom: 56px;
      .ecard-alipay-popup-content-tips-title {
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
        font: var(--eui-font-body-b1);
      }
      .ecard-alipay-popup-content-tips-item {
        list-style-type: disc; // 显示实心圆点
        padding-left: 40px;
        color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
        font: var(--eui-font-body-r1);
        margin-top: 16px;
      }
    }
    .eui-form-item-label {
      color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
      font: var(--eui-font-body-b1) !important;
    }
  }
}
