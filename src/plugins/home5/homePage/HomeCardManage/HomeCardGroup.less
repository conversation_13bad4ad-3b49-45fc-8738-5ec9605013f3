@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.home-group-card-wrapper {
  margin-bottom: @space-5;
  position: relative;

  .eui-card-header-title {
    font: var(--eui-font-head-b2);
    color: var(--eui-text-title);
  }
  .home-group-card-swiper {
    .eui-swiper-indicator {
      bottom: 0px;
    }
  }
  .home-group-card-item {
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    box-sizing: border-box;
    text-align: center;
    &:hover {
      cursor: pointer;
    }
    &:active {
      background-color: @color-bg-2;
      border-radius: @radius-2;
    }
    .card-group-badge {
      .font-size-1;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      position: absolute;
      box-sizing: border-box;
      top: 0;
      right: 0;
      transform: translate(45%, -45%);
      background-color: #f4526b;
      color: @color-white-1;
      height: 40px;
      min-width: 40px;
      padding: 0 @space-3;
      border-radius: 20px;
      text-align: center;
      z-index: 10;
    }
    .ekb-badge-text {
      height: 40px;
      min-width: 40px;
      left: unset;
      right: -10px;
      border: 0;
    }
    .card-group-avatar {
      position: relative;
      height: 0;
      display: block;
      width: 80px;
      padding-bottom: 80px;
      line-height: 80px;
      margin: 0 auto @space-3;
      svg,
      img {
        display: inline-block;
        vertical-align: middle;
      }
      .home-card-red-dot {
        top: -10px;
        right: -20px;
        height: 40px;
        min-width: 40px;
        border-radius: 40px;
        font: var(--eui-font-note-b2);
        background-color: var(--eui-function-danger-500);
        border: 4px solid #ffffff;
      }
    }
    .card-group-desc {
      width: 100%;
      padding: 0 @space-1;
      font-weight: 400;
      color: var(--eui-text-title);
      font-size: 24px;
      overflow:hidden;
      text-overflow:ellipsis;
      display:-webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp:3;
    }
  }
  .home-group-card-indicator {
    position: absolute;
    left: 0;
    right: 0;
    bottom: @space-6;
    display: flex;
    justify-content: center;
    .indicator-wrapper {
      height: @space-2;
      display: flex;
      align-items: center;
      background-color: #ebecee;
      border-radius: 6px;
      justify-content: center;
      .indicator-dot {
        height: 100%;
        width: @space-5;
      }
      .indicator-dot-active {
        border-radius: 6px;
        background-color: #4b4752;
      }
    }
  }
  .home-group-card-right {
    display: flex;
    .home-group-card-right-title {
      .font-size-2;
      margin-right: @space-1;
      color: rgba(29, 33, 41, 0.7);
    }
    .open-edit-group-icon {
      height: 0.4rem;
      color: rgba(29, 33, 41, 0.7);
      transform: rotate(45deg);
      cursor: pointer;
    }
  }
}
