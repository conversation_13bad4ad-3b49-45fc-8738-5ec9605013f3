@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.ecard-container {
  :global {
    .ecard-content-wrapper {
      width: 100%;
      height: 212px;
      background: linear-gradient(90deg, #E9F2FF 2.65%, #C0CFEA 99.38%);
      border-radius: 16px;
      position: relative;

      .ecard-content-cover {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        padding: 0 32px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: right;
        background-image: url('../../images/hose_bg.png');

        .ecard-content-item-wrapper-top {
          display: flex;
          width: 100%;
          align-items: center;
          justify-content: space-between;

          .ecard-content-left {
            display: flex;
            flex-direction: row;
            align-items: center;

            .ecard-content-left-logo {
              width: 64px;
              height: 64px;
              margin-right: 16px;
            }

            .ecard-content-left-info {
              display: flex;
              flex-direction: column;

              .ecard-content-left-info-title {
                color: var(--eui-text-title);
                font: var(--eui-font-head-b1);
              }
            }
          }
        }

        .ecard-content-item-wrapper-bottom {
          padding-left: 80px;
          display: flex;
          width: 100%;
          margin-top: 8px;
          align-items: center;
          justify-content: space-between;

          .ecard-content-left-info-amount {
            display: flex;
            flex-direction: column;
            color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
            font: var(--eui-font-note-r2);

            .ecard-content-left-info-amount-money-wrapper {
              display: flex;
              align-items: center;

              .ecard-content-left-info-amount-money {
                font: var(--eui-num-head-b1);
                color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
              }
            }
          }

          .ecard-content-button {
            padding: 8px 24px;
            border-width: 0;
          }
        }
      }
    }
  }
}

.popup-content-wrapper {
  display: flex;
  padding: 16px 0;
  flex-direction: column;
  height: 932px;

  :global {
    .popup-content-title {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
      font: var(--eui-font-head-b1);
      padding-bottom: 24px;

      .title {
        color: var(--eui-text-caption);
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
      }

      .money {
        color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
        font-size: 48px;
        font-style: normal;
        font-weight: 500;

        .symbol {
          font-size: 32px;
          font-weight: 400;
        }
      }
    }

    .line {
      width: 100%;
      height: 1px;
      background-color: var(--eui-line-divider-default);
    }

    .popup-content-info {
      border-radius: 12px;
      padding-top: 32px;
      overflow-y: hidden;
      display: flex;
      flex-direction: column;

      .popup-content-info-title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        color: var(--eui-text-caption);
        font: var(--eui-font-head-b1);
      }

    }

    .money-content {
      display: flex;
      width: 100%;
      flex-direction: column;
      padding: 24px;
      margin-bottom: 8px;
      border-radius: 16px;
      background: var(--eui-bg-body-overlay, #F7F8FA);
      color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
      font-size: 28px;
      font-style: normal;
      font-weight: 400;
      line-height: 40px;

      .money-item {
        display: flex;
        justify-content: space-between;
      }

      .mb12 {
        margin-bottom: 12px;
      }
    }
  }
}

.popup-content-info-list-wrapper {
  flex: 1;
  height: 100%;
  overflow-y: auto;

  :global {
    .popup-content-info-list {
      display: flex;
      flex-direction: row;
      margin-top: 24px;
      justify-content: space-between;
      color: var(--eui-text-caption);
      font: var(--eui-font-body-r1);

      .popup-content-info-list-left {
        flex: 1;
        padding-right: 12px;
      }
    }

  }
}

.quota-management-list-wrapper {
  flex: 1;
  overflow-y: auto;

  :global {
    .item {
      display: flex;
      flex-direction: row;
      margin-bottom: 24px;
      justify-content: space-between;
      color: var(--eui-text-caption);
      font: var(--eui-font-body-r1);

      .left {
        flex: 1;
        padding-right: 12px;
      }
    }
  }
}

.tab-content {
  overflow: hidden;
  height: 100%;

  :global {
    .eui-tabs {
      height: 100%;

      .eui-tabs-content {
        height: calc(100% - 108px);
        padding: 24px 0;
      }
    }
  }
}