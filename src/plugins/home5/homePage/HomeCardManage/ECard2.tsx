import React, { useEffect, useRef, useState } from 'react'
import { Button, Dialog } from '@hose/eui-mobile'
import styles from './ECard2.module.less'
import { app } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { ECardDisabled } from './ECardDisabled'
import { ECardEnable } from './ECardEnable'
import { logEvent } from '../../../../lib/dataflux'
import { fnGetControlBehavior } from '../../util/ECardUtil'
import { getBoolVariation } from '../../../../lib/featbit'
import { DialogShowHandler } from '@hose/eui-mobile/es/components/dialog'
import { ECardFeetypeList } from './ECardFeetypeList'
import { CardInfo } from './HomeCardECard'

interface Props {
  refreshKey?: any
  eCardState?: CardInfo
  cardType: string
}
export const ECard2: React.FC<Props> = props => {
  const { refreshKey, eCardState, cardType } = props
  const [feetypeList, setFeetypeList] = useState([])

  const handler = useRef<DialogShowHandler>()

  useEffect(() => {
    getFeetypeList()
  }, [refreshKey])

  const getFeetypeList = () => {
    //获取最近使用的费用类型列表，最多展示三个，没有的话不展示
    Fetch.GET('/api/v1/ecard/often/feeType', { cardType }).then((res: any) => {
      const list = res?.items?.slice(0, 3) ?? []
      setFeetypeList(list.length > 3 ? list.slice(0, 3) : list)
    })
  }
  const onFeetypeClick = async (feetype: any) => {
    const staff = app.getState()['@common'].me_info?.staff
    logEvent('e-card-select-feetype', {
      sceneName: '首页卡片快捷入口',
      feeTypeId: feetype?.id,
      CostName: feetype?.name,
      staffName: staff?.name,
      company: staff?.corporationId?.name
    })
    const overdueModel = getBoolVariation('risk-control-flag')
    if (overdueModel) {
      const result: any = await fnGetControlBehavior(staff, 0)
      switch (result?.behavior) {
        case 'FORBID':
          Dialog.alert({ content: result?.msg || '风控禁止提交' })
          return
        case 'ADDITIONAL':
          handler.current = Dialog.show({
            title: '风险提示',
            content: result?.msg || '风控风险提示',
            closeOnAction: true,
            primarySecondaryActions: [
              {
                key: 'confirm',
                text: '确定',
                category: 'primary',
                onClick: () => {
                  handler.current?.close()
                  fnGotoECardExpensePage(feetype)
                  return
                }
              },
              {
                key: 'close',
                text: '取消',
                category: 'secondary',
                onClick: () => {
                  handler.current?.close()
                }
              }
            ]
          })
          return
        default:
          fnGotoECardExpensePage(feetype)
          return
      }
    } else {
      fnGotoECardExpensePage(feetype)
    }
  }

  const fnGotoECardExpensePage = (feetype: any) => {
    app.go(`/e-card-expense-page/${feetype.id}`)
  }

  if (!['NORMAL', 'ACTIVATING'].includes(eCardState?.state)) {
    //开通易商卡2.0但是当前员工没有开卡的时候展示
    const getInfo = () => {
      if (eCardState?.state === 'PROCESSING') {
        return i18n.get('尚未开卡，请点击下方按钮进行易商卡开通')
      } else {
        return i18n.get('暂无使用权限，请联系管理员进行权限开通')
      }
    }
    return (
      <div className={styles['ecard2-wrapper']}>
        <div className="ecard-content-info">{getInfo()}</div>
        <ECardDisabled />
        {eCardState?.state === 'PROCESSING' && (
          <Button
            block
            theme="highlight"
            onClick={async () => {
              const url = await app.invokeService('@mine:get:wallet:url')
              app.invokeService('@layout:open:link', url)
            }}
            size="small"
            category="secondary"
            style={{ marginTop: 11 }}
          >
            {i18n.get('开通易商卡')}
          </Button>
        )}
      </div>
    )
  }
  return (
    <div className={styles['ecard2-wrapper']}>
      <ECardEnable eCardState={eCardState} refreshKey={refreshKey} />
      {feetypeList.length > 0 && <ECardFeetypeList feetypeList={feetypeList} onFeetypeClick={onFeetypeClick} />}
    </div>
  )
}
