import React, { useEffect, useRef, useState } from 'react'
import { app } from '@ekuaibao/whispered'
import { Button, Dialog, Ellipsis, Popup, Tabs } from '@hose/eui-mobile'
import styles from './ECardEnable.module.less'
import LOGO from '../../images/logo.png'
import { OutlinedDirectionRight } from '@hose/eui-icons'
import { selectECardFeetype } from '../../util/SimpleCardUtil'
import { Fetch } from '@ekuaibao/fetch'
import { OutlinedTipsInfo } from '@hose/eui-icons'
import moment from 'moment'
import { fnGetControlBehavior } from '../../util/ECardUtil'
import { getBoolVariation } from '../../../../lib/featbit'
import { DialogShowHandler } from '@hose/eui-mobile/es/components/dialog'

const EmptyWidget: any = app.require('@home5/EmptyWidget')

const map: any = {
  REAL_SALE_REAL_COMPENSATION: '实销实补',
  AUTOMATIC_RECOVERY: '自动恢复'
}

interface Props {
  eCardState: any
  onClick?: () => void
  refreshKey?: any
}

export const ECardEnable: React.FC<Props> = props => {
  const { onClick, refreshKey } = props
  const [qrDisable, setQrDisable] = useState(false)
  const [visible, setVisible] = useState(false)
  const [requisitionInfo, setRequisitionInfo] = useState(undefined)

  const handler = useRef<DialogShowHandler>()

  useEffect(() => {
    Fetch.GET('/api/form/v3/requisition/info/requisitionInfoList').then(res => {
      setRequisitionInfo(res?.value)
    })
  }, [refreshKey])

  const handleRightClick = async () => {
    const overdueModel = getBoolVariation('risk-control-flag')
    if (overdueModel) {
      const userInfo = app.getState()['@common'].me_info.staff
      const result: any = await fnGetControlBehavior(userInfo, 0)
      switch (result?.behavior) {
        case 'FORBID':
          Dialog.alert({ content: result?.msg || '风控禁止提交' })
          return
        case 'ADDITIONAL':
          handler.current = Dialog.show({
            title: '风险提示',
            content: result?.msg || '风控风险提示',
            closeOnAction: true,
            primarySecondaryActions: [
              {
                key: 'confirm',
                text: '确定',
                category: 'primary',
                onClick: () => {
                  handler.current?.close()
                  fnOpenClick()
                  return
                }
              },
              {
                key: 'close',
                text: '取消',
                category: 'secondary',
                onClick: () => {
                  handler.current?.close()
                }
              }
            ]
          })
          return
        default:
          fnOpenClick()
          return
      }
    } else {
      fnOpenClick()
    }
  }

  const fnOpenClick = () => {
    if (!qrDisable) {
      if (onClick) {
        onClick()
      } else {
        selectECardFeetype(undefined, setQrDisable)
      }
    }
  }

  const handleShowAmountInfo = () => {
    setVisible(true)
  }

  const handleInfoClick = (key: string) => {
    let content = ''
    switch (key) {
      case 'amount':
        content =
          '循环额度是一笔可重复使用的信用限额，员工在不超过该额度的情况下，可以多次消费，周期结束后补充此额度，具体以公司规则为准。'
        break
      case 'residualAmount':
        content = '剩余循环额度=发放额度-已消费额度。'
        break
      case 'model':
        content =
          requisitionInfo?.quotaManagement?.model === 'REAL_SALE_REAL_COMPENSATION'
            ? '实销实补是指易商卡消费在报销审核通过后，自动恢复额度。'
            : '自动恢复是指到达额度重置日期时，员工无需报销即可自动恢复额度。'
        break
      default:
        content = ''
    }

    Dialog.alert({ content })
  }

  const renderApplication = () => {
    return (
      <div className={styles['popup-content-info-list-wrapper']}>
        {requisitionInfo?.infos?.map((item: any) => {
          return (
            <div className="popup-content-info-list">
              <div className="popup-content-info-list-left">
                <Ellipsis direction="end" content={item.title} />
              </div>
              <span>{i18n.get(`￥${item.amount}`)}</span>
            </div>
          )
        })}
      </div>
    )
  }

  const renderQuotaManagement = () => {
    const { quotaManagement } = requisitionInfo || {}
    const resetDate = quotaManagement?.resetDate || {}
    const beginTime = moment(quotaManagement?.beginTime).format('YYYY-MM-DD')
    const endTime = moment(quotaManagement?.endTime).format('YYYY-MM-DD')
    let resetData = ''
    switch (resetDate?.type) {
      case 'YEAR':
        resetData =
          resetDate?.day === -1 ? `每年${resetDate?.month}月月末` : `每年${resetDate?.month}月${resetDate?.day}号`
        break
      case 'MONTH':
        resetData = resetDate?.day === -1 ? '每月月末' : `每月${resetDate?.day}号`
        break
      case 'WEEK':
        resetData = `每周星期${resetDate?.week}`
        break
      default:
        resetData = ''
    }
    return (
      <div className={styles['quota-management-list-wrapper']}>
        <div className="item">
          <div className="left">
            循环额度
            <OutlinedTipsInfo style={{ marginLeft: 2 }} fontSize={12} onClick={() => handleInfoClick('amount')} />
          </div>
          <div className="right">{`￥${quotaManagement?.amount}`}</div>
        </div>
        <div className="item">
          <div className="left">
            剩余循环额度
            <OutlinedTipsInfo
              style={{ marginLeft: 2 }}
              fontSize={12}
              onClick={() => handleInfoClick('residualAmount')}
            />
          </div>
          <div className="right">{`￥${quotaManagement?.residualAmount}`}</div>
        </div>
        <div className="item">
          <div className="left">额度有效期</div>
          <div className="right">{`${beginTime}至${endTime}`}</div>
        </div>
        {quotaManagement?.model === 'AUTOMATIC_RECOVERY' && (
          <div className="item">
            <div className="left">额度重置日期</div>
            <div className="right">{resetData}</div>
          </div>
        )}
        <div className="item">
          <div className="left">模式</div>
          <div className="right">
            {i18n.get(`${map[quotaManagement?.model]}`)}
            <OutlinedTipsInfo style={{ marginLeft: 2 }} fontSize={12} onClick={() => handleInfoClick('model')} />
          </div>
        </div>
      </div>
    )
  }

  const renderPopContent = () => {
    const { quotaManagement, infos } = requisitionInfo || {}
    const hasRequisition = !!infos?.length
    const hasQuota = !!quotaManagement?.model
    if (hasRequisition && !hasQuota) {
      return (
        <>
          <div className="line" />
          <div className="popup-content-info">
            <div className="popup-content-info-title">
              <span>{i18n.get('申请额度')}</span>
            </div>
            {renderApplication()}
          </div>
        </>
      )
    } else if (!hasRequisition && hasQuota) {
      return (
        <div>
          <div className="line" />
          <div className="popup-content-info">
            <div className="popup-content-info-title">
              <span>{i18n.get('循环额度')}</span>
            </div>
            {renderQuotaManagement()}
          </div>
        </div>
      )
    } else if (hasRequisition && hasQuota) {
      return (
        <>
          <div className="money-content">
            <div className="money-item mb12">
              <div className="left">剩余申请额度</div>
              <div className="right">{`￥${requisitionInfo?.requisitionAmount ?? 0}`}</div>
            </div>
            <div className="money-item">
              <div className="left">剩余循环额度</div>
              <div className="right">{`￥${requisitionInfo?.quotaManagement?.residualAmount ?? 0}`}</div>
            </div>
          </div>
          <div className={styles['tab-content']}>
            <Tabs>
              <Tabs.Tab title="申请额度" key="fruits">
                {renderApplication()}
              </Tabs.Tab>
              <Tabs.Tab title="循环额度" key="vegetables">
                {renderQuotaManagement()}
              </Tabs.Tab>
            </Tabs>
          </div>
        </>
      )
    } else {
      return <EmptyWidget size={100} tips={i18n.get('暂无额度')} />
    }
  }

  const renderPopupInfo = () => {
    return (
      <Popup
        visible={visible}
        title={i18n.get("剩余额度")}
        showCloseButton
        onMaskClick={() => {
          setVisible(false)
        }}
        radius
        onClose={() => {
          setVisible(false)
        }}
      >
        <div className={styles['popup-content-wrapper']}>
          <div className="popup-content-title">
            <span className="title">{i18n.get('剩余总额')}</span>
            <span className="money">
              <span className="symbol">¥</span>
              {`${requisitionInfo?.total ?? 0}`}
            </span>
          </div>
          {renderPopContent()}
        </div>
      </Popup>
    )
  }

  return (
    <div className={styles['ecard-container']}>
      {renderPopupInfo()}
      <div className="ecard-content-wrapper">
        <div className="ecard-content-cover">
          <div className="ecard-content-item-wrapper-top">
            <div className="ecard-content-left">
              <img className="ecard-content-left-logo" src={LOGO} />
              <div className="ecard-content-left-info">
                <div className="ecard-content-left-info-title">{i18n.get('易商卡企业付')}</div>
              </div>
            </div>
          </div>
          <div className="ecard-content-item-wrapper-bottom">
            <div className="ecard-content-left-info-amount">
              <span>{i18n.get(`剩余额度`)}</span>
              <div className="ecard-content-left-info-amount-money-wrapper" onClick={handleShowAmountInfo}>
                <span className="ecard-content-left-info-amount-money">{`¥${requisitionInfo?.total ?? 0}`}</span>
                <OutlinedDirectionRight fontSize={12} style={{ marginLeft: 4 }} />
              </div>
            </div>
            <Button
              shape="rounded"
              category="primary"
              className="ecard-content-button"
              size="mini"
              onClick={handleRightClick}
            >
              {i18n.get('去使用')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
export default ECardEnable
