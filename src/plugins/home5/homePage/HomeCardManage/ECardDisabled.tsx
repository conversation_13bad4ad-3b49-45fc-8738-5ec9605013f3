import React from 'react'
import styles from './ECardDisabled.module.less'
import LOGO from '../../images/logo.png'

interface Props {
  isOpened?: boolean
  eCardState?: any
}

export const ECardDisabled: React.FC<Props> = props => {
  const { isOpened = false } = props
  return (
    <div className={styles['ecard-container-disabled']}>
      <div className={`ecard-content-wrapper${isOpened ? '-enable' : ''}`}>
        <div className="ecard-content-cover">
          <div className="ecard-content-left">
            <img className="ecard-content-left-logo" src={LOGO} />
            <div className="ecard-content-left-info">
              <div className="ecard-content-left-info-title">{i18n.get('易商卡企业付')}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
export default ECardDisabled
