/**
 *  Created by pw on 2022/11/16 5:27 PM.
 */
import { Fetch, Resource } from '@ekuaibao/fetch'
import { app } from '@ekuaibao/whispered'
import { computed, observable, toJS } from 'mobx'
import {
  ICard,
  ICardBusinesssummary,
  IMalCard,
  IHomePageCardResponse,
  IGroup,
  ICardMetaData,
  IHomeTemplateVersion,
  IQuestionnaires
} from './Types'
import { action } from '@ekuaibao/mobx-store'
const menuAction = new Resource('/api/menu/v2')
const menuActionV7 = new Resource('/api/menu/v7')
const travelManage = new Resource('/api/tpp/v2/travelManagement/getTravelManagementConfig')
const mallCardData = new Resource('/api/message/v1/messageCenterConfig')
const changeStaffMenus = new Resource('/api/menu/v2/staffMenus')
import { CodeType, noNeedSummaryCards } from '../../util/card.enum'
import { get } from 'lodash'
import { uuid } from '@ekuaibao/helpers'
import { Home5CardType } from '../../staticUtil'
import { cacheFirst } from '../../../../lib/help'
import { localStorageSet } from '../../../../lib/util'

export class HomeCardManageStore {
  allCards: ICard[] = []
  @observable cardList: ICard[] = []
  @observable cardBacklogList: ICard[] = []
  @observable mallCardDataList: IMalCard[] = []
  @observable isFirstLoadCardList = true
  @observable showTripCard = false
  @observable homepageResponse: IHomePageCardResponse = {} as IHomePageCardResponse
  @observable hasNewHomeTemplateVersion: IHomeTemplateVersion = {} as IHomeTemplateVersion
  @observable refreshKey = new Date().getTime().toString()
  public upgradeToMicroservice: boolean

  public editPersionSelectCards: ICard[] = []
  public editPersionUnSelectCards: ICard[] = []
  public cardConfigs: ICardMetaData[] = []

  @computed get groupedCardList() {
    if (!this.cardList.length || this.isFirstLoadCardList === true) {
      return []
    }
    return this.homepageResponse.page.groups.reduce((result, group) => {
      if (group.cards?.length) {
        const cards = group.cards.filter(card => card.selected)
        result.push({ ...group, cards })
      } else {
        result.push(group)
      }
      return result
    }, [])
  }

  @computed get groupedPreviewCardList() {
    if (!this.hasNewHomeTemplateVersion?.homePageTemplate) {
      return []
    }
    return this.hasNewHomeTemplateVersion?.homePageTemplate?.page?.groups?.reduce((result, group) => {
      if (group.cards?.length) {
        const cards = group.cards.filter(card => card.selected)
        result.push({ ...group, cards })
      } else {
        result.push(group)
      }
      return result
    }, [])
  }

  @action
  async fetchCardWithCache() {
    if (!this.allCards?.length) {
      cacheFirst({
        key: '/home/<USER>/byDeviceType/$deviceType',
        fetchJob: async () => {
          try { await menuActionV7.GET('/home/<USER>/checkMustUpdateTemplate', '', undefined, {skipCache: true}) } catch (error) {}
          return await menuActionV7.GET(
            '/home/<USER>/byDeviceType/$deviceType',
            { deviceType: 'MOBILE' },
            undefined,
            { skipCache: true }
          )
        },
        action: (rsp) => {
          const { value } = rsp || {};
          if (value) {
            this.updateHomePageCard(this.fnFilterForbiddenMenu(value))
            this.dealResonseCard(true, undefined, undefined)
          }
          return !!value
        },
        shouldReacion: (oldRsp, newRsp) => newRsp?.value && newRsp.value.version !== oldRsp.value?.version
      })
    } else {
      this.dealResonseCard(true, undefined, undefined)
    }
  }

  @action
  async fetchCard(
    needRequestBusinessSummary: boolean = true,
    reloadFilter?: (card: ICard) => void,
    fn?: () => void,
    notResetCardsData = false
  ) {
    if (!this.allCards?.length || notResetCardsData) {
      if (needRequestBusinessSummary) {
        try {
          // 获取版本并应用
          const { value } = await menuActionV7.GET('/home/<USER>/checkMustUpdateTemplate', '', undefined, {
            skipCache: true
          })
        } catch (error) {
          console.log('-error--获取版本-强制更新-', error)
        }
      }
      const { value } = await menuActionV7.GET(
        '/home/<USER>/byDeviceType/$deviceType',
        { deviceType: 'MOBILE' },
        undefined,
        { skipCache: true }
      )
      // 老逻辑继续走fetchCard，只需同步一下缓存即可
      localStorageSet('/home/<USER>/byDeviceType/$deviceType', JSON.stringify({ value }))
      this.updateHomePageCard(this.fnFilterForbiddenMenu(value))
    }
    this.dealResonseCard(needRequestBusinessSummary, reloadFilter, fn)
  }

  fnFilterForbiddenMenu(responseData: IHomePageCardResponse) {
    const autoExpenseForbiddenFeaturePower = app.getState()['@common'].powers.autoExpenseForbiddenFeature
    if (!autoExpenseForbiddenFeaturePower) {
      return responseData
    }
    const blackListMap: Record<string, string> = {
      waitInvoiceDetail: 'waitInvoiceDetail'
    }
    responseData.page.groups.forEach(group => {
      group.cards = group.cards.filter(card => {
        return !blackListMap[card.id]
      })
    })
    return responseData
  }

  @action updateHomePageCard(responseData: IHomePageCardResponse) {
    this.homepageResponse = responseData
    this.homepageResponse.page.groups = this.sortCard(this.homepageResponse?.page?.groups) as IGroup[]
    this.homepageResponse.page.groups.forEach(group => {
      group.cards = this.sortCard(group.cards) as ICard[]
    })
    const tempAllCards = responseData.page.groups.reduce((result: ICard[], group: IGroup) => {
      if (group.cards?.length) {
        result = result.concat(group.cards)
      }
      return result
    }, [] as ICard[])
    this.allCards = tempAllCards?.map(card => {
      const oldCard = this.allCards?.find(oldCard => oldCard?.code === card?.code)
      if (oldCard?.detail) {
        card.detail = oldCard?.detail
      }
      return card
    })
  }

  @action async dealResonseCard(
    needRequestBusinessSummary: boolean = true,
    reloadFilter?: (card: ICard) => void,
    fn?: () => void
  ) {
    const __items_selected = this.allCards.filter((line: { selected: boolean }) => line.selected)
    // 防止从其页面退回时候,重新渲染骨架屏
    if (!this.allCards || !this.allCards.length) {
      this.dealCardList()
    }

    this.isFirstLoadCardList = false
    this.dealCardList(reloadFilter, fn)

    if (!needRequestBusinessSummary) {
      return
    }

    const __items_business_summary = __items_selected.filter(line => !noNeedSummaryCards.includes(line.code))

    if (__items_business_summary.length) {
      const cardCodeMap: Record<string, ICard> = this.allCards.reduce((result, card) => {
        result[card.code] = card
        return result
      }, {} as Record<string, ICard>)
      await this.___multi_fetch_businessSummary(__items_business_summary, res => {
        const details: ICardBusinesssummary[] = res.items || []
        details.forEach(element => {
          const el = cardCodeMap[element.code]
          if (el) {
            el.detail = element
          }
        })

        this.dealCardList(reloadFilter, fn)
      })
    }
    // 获取业务对象的数量
    const dataLinkEntityIds = this.allCards
      .filter(line => line.code === CodeType.dataLinkEntity)
      .map((card: { id: any }) => card.id)

    this.loadCommonDelayData()
    if (!dataLinkEntityIds.length) {
      return
    }
    if (dataLinkEntityIds.length) {
      const dataLinkEntityRes = await menuAction.GET('/businesssummary/dataLinkEntity', {
        dataLinkEntityIds: dataLinkEntityIds.join(','),
        join: 'dataLinkEntityId,dataLinkEntity,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform'
      })
      const dataLinkDetails = dataLinkEntityRes.items || []
      const cardIdMap: Record<string, ICard> = this.allCards.reduce((result, card) => {
        result[card.id] = card
        return result
      }, {} as Record<string, ICard>)
      for (const line of dataLinkDetails) {
        const el = cardIdMap[line.dataLinkEntityId]
        if (el) {
          const type = get(line, 'dataLinkEntity.platformId.type', '')
          if (type === 'TRAVEL_MANAGEMENT') {
            delete line.dataLinkCount
            app.store.dispatch('@home5/getNewTripData')(el, line, (card: ICard) => {
              this.dealDatalinkCard(card)
            })
            app.store.dispatch('@home5/getHomeTripAssist')(el)
          } else {
            el.detail = line
          }
        }
      }
    }
    const travelManageRep = await travelManage.GET('', { type: 'travelUpgrade' })
    this.upgradeToMicroservice = travelManageRep?.value?.upgradeToMicroservice || false
    this.dealCardList(reloadFilter, fn)
    this.homepageResponse.page.groups = this.homepageResponse.page.groups.slice()
  }

  @action dealCardList(reloadFilter?: (card: ICard) => void, fn?: () => void) {
    this.cardList = this.allCards.filter(el => el.selected)
    this.cardBacklogList = this.allCards.filter(el => !el.selected)
    const getKey = (card: ICard) => {
      if (card.code === 'dataLinkEntity') {
        return card.id
      }
      return card.code
    }
    if (reloadFilter) {
      const auditPending = this.cardList.find((item: any) => item.code === 'auditPending')
      reloadFilter(auditPending)
    }
    const map = this.cardList.reduce<any>((result, card) => {
      result[getKey(card)] = card
      return result
    }, {})
    this.homepageResponse.page.groups.forEach(group => {
      group.cards.forEach(card => {
        card.detail = map[getKey(card)]?.detail
      })
    })
    this.dealCardDataForEditPersionCard()
    fn && fn()
  }

  @action dealDatalinkCard(datalinkCard: ICard) {
    this.homepageResponse.page.groups.forEach(group => {
      group.cards.forEach(card => {
        if (card.id === datalinkCard.id) {
          card.detail = datalinkCard.detail
        }
      })
    })
    this.homepageResponse.page.groups = this.homepageResponse.page.groups.slice()
  }

  /** 处理卡片为了编辑个人卡片 */
  dealCardDataForEditPersionCard() {
    const cardList: ICard[] = toJS(this.homepageResponse.page.groups).reduce<any>((result: ICard[], group: IGroup) => {
      if (group.groupType === Home5CardType.GROUP) {
        result.push({ ...group, selected: true, type: Home5CardType.GROUP } as any)
        group.cards.forEach(card => {
          result.push({ ...card, pid: group.id })
        })
      }
      if (group.groupType === Home5CardType.emptygroup) {
        result = result.concat(group.cards)
      }
      return result
    }, [] as ICard[])
    this.editPersionSelectCards = cardList.filter(el => el.selected)
    this.editPersionUnSelectCards = cardList.filter(el => !el.selected)
  }

  private async ___multi_fetch_businessSummary(__items_business_summary: ICard[], handler: (res: any) => void) {
    const codes: string[] = __items_business_summary.map(card => card.code)

    const __has_recordExpends = !!__items_business_summary.find(line => line.code === CodeType.recordExpends)

    // 长度判断 `4` 发一次请求
    if (!__has_recordExpends || __items_business_summary.length <= 4) {
      const res0 = await menuAction.POST('/businesssummary', { deviceType: 'MOBILE', codes: codes })
      handler(res0)
      return
    }

    // 长度判断 大于 `4` , 分两次请求
    const codes_no_recordExpends = __items_business_summary
      .filter(line => line.code !== CodeType.recordExpends)
      .map(card => card.code).concat(CodeType.recordExpends)

    // 第一次 截取前四个
    // 第二次 使`随手记` + `其他`
    const result = await Promise.all([menuAction.POST('/businesssummary', {
      deviceType: 'MOBILE',
      codes: codes_no_recordExpends.slice(0, 4)
    }), menuAction.POST('/businesssummary', {
      deviceType: 'MOBILE',
      codes: codes_no_recordExpends.slice(4)
    })])
    const response = formatResult(result)
    handler(response)
  }

  @action // 修改菜单
  async changeCardList(cardList: ICard[]) {
    return changeStaffMenus.PUT('', cardList).then(() => {
      // 清空列表,以便于  getCardList 重新获取详情
      this.allCards = []
      this.showTripCard = false
    })
  }

  @action
  async getMallCardData() {
    const result = await mallCardData.POST('/getOperation', { source: 'APP', mallEntrance: true })
    this.mallCardDataList = result.items || []
  }

  loadCommonDelayData() {
    app.invokeService('@common:get:data:delay')
  }

  @action async updatePersonalHomePage(
    cards: ICard[],
    successCallBack: () => void,
    needRequestBusinessSummary: boolean = false
  ) {
    const groups: IGroup[] = []
    const groupMap: Record<string, IGroup> = {}

    const defaultGroup = (group: IGroup = {} as IGroup) => {
      return {
        id: uuid(10),
        groupType: Home5CardType.emptygroup,
        label: i18n.get('空分组'),
        cnLabel: '',
        icon: '',
        showType: 'LIST',
        groupLayout: 'TILE',
        ...group
      }
    }

    cards.forEach((card, index) => {
      const defaultPid = `${Home5CardType.emptygroup}_${index}`
      const pid = card.pid?.length ? card.pid : card.type === Home5CardType.GROUP ? card.id : defaultPid
      let group = groupMap[pid]
      if (!group) {
        if (pid === defaultPid) {
          group = defaultGroup({ weight: card.weight } as IGroup)
        } else {
          group = defaultGroup({
            id: card.id,
            groupType: Home5CardType.GROUP,
            label: card.label,
            cnLabel: card.label,
            icon: card.icon,
            showType: card.showType,
            weight: card.weight
          } as IGroup)
          // 使用已经存在分组作为分割，重新切换空分组
        }
        group.cards = []
        groupMap[pid] = group
        groups.push(group)
      }
      if (card.type !== Home5CardType.GROUP) {
        group.cards.push(card)
      }
    })
    const tempData = toJS(this.homepageResponse)
    const data = {
      ...tempData,
      forPerson: true,
      name: this.homepageResponse.name,
      page: { pageLayout: this.homepageResponse.page.pageLayout, groups }
    }
    if (!data.id) {
      // 如果有为空的id删除字段
      delete data.id
    }
    await menuActionV7.POST('/home/<USER>/savepagetemplate', data)
    await this.resetHomePageCard(null, needRequestBusinessSummary)
    if (successCallBack) {
      successCallBack()
    }
  }

  @action async resetHomePageCard(
    successCallBack?: () => {},
    needRequestBusinessSummary = false,
    notResetCardsData = false
  ) {
    this.refreshKey = new Date().getTime().toString()
    if (!notResetCardsData) {
      this.allCards = []
    }
    await this.fetchCard(needRequestBusinessSummary, undefined, undefined, notResetCardsData)
    if (successCallBack) {
      console.log('[ successCallBack ] >')
      successCallBack()
    }
  }

  @action async getCardMetaData() {
    const { items } = await menuActionV7.GET('/metadata/getSimpleMetaData')
    this.cardConfigs = items || []
  }

  @action
  async checkHomePageVersion() {
    const result = await menuActionV7.GET('/home/<USER>/checkversion', '', undefined, { skipCache: true })
    this.hasNewHomeTemplateVersion = result?.value
  }

  @action
  async useHomePageVersion(homePageTemplateId: string, homePageTemplateVersionId: string, successCallBack?: () => {}) {
    await menuActionV7.POST('/home/<USER>/saveforpersion', { homePageTemplateId, homePageTemplateVersionId })
    await this.checkHomePageVersion()
    await this.resetHomePageCard()
    if (successCallBack) {
      successCallBack()
    }
  }

  @action // 保存用户选的角色并拿到配置列表刷新数据
  async setQuestionnaires(data: IQuestionnaires, fn = () => {}) {
    await menuActionV7.POST('/questionnaires', data)
    await this.resetHomePageCard()
    // 重新渲染骨架屏
    this.isFirstLoadCardList = true
    fn && fn()
  }

  sortCard(list: Array<{ weight: number }>) {
    return list.sort((a, b) => a.weight - b.weight)
  }
}

const chunkArray = (array = [], size) => {
  const result = []
  for (let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size))
  }
  return result
}

const formatResult = (result = []) => {
  return result.reduce((result, res) => {
    result.items = [...result.items, ...res.items]
    return result
  }, {items: []})
}

chunkArray([], 3)

export default HomeCardManageStore
