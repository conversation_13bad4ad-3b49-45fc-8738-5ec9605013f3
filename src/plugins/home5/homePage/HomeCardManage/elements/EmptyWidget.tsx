import React, { FC } from 'react'
import {
  IllustrationSmallNoBilling,
  IllustrationMiddleNoBilling,
  IllustrationSmallNoContent,
  IllustrationSmallNoApproval,
  IllustrationMiddleNoTrip,
  IllustrationMiddleNoOrder,
  IllustrationSmallNoTrip,
  IllustrationMiddleRecycleBin,
  IllustrationMiddleNoContent,
  IllustrationMiddlePrivateCarPublic,
  IllustrationMiddleUnauthorized,
  IllustrationMiddleInput
} from '@hose/eui-icons'
import styles from './EmptyWidget.module.less'
import { T } from '@ekuaibao/i18n'

interface IProps {
  size?: number
  tips?: string //空状态提示问题
  infos?: string[] //有时候空状态的提示问题下面有一些提示信息
  type: string
  bottom?: number | string //paddingBottom底部的距离
  backgroundColor?: string //背景颜色
  style?: React.CSSProperties
}

const Icons = (type: string, fontSize: number) => {
  switch (type) {
    case 'myBill':
      return <IllustrationSmallNoBilling fontSize={fontSize} />
    case 'audit':
      return <IllustrationSmallNoApproval fontSize={fontSize} />
    case 'billList':
      return <IllustrationMiddleNoBilling fontSize={fontSize} />
    case 'tripList':
      return <IllustrationMiddleNoTrip fontSize={fontSize} />
    case 'orderList':
      return <IllustrationMiddleNoOrder fontSize={fontSize} />
    case 'noCentent':
      return <IllustrationMiddleNoContent fontSize={fontSize} />
    case 'noTrip':
      return <IllustrationSmallNoTrip fontSize={fontSize} />
    case 'recycle':
      return <IllustrationMiddleRecycleBin fontSize={fontSize} />
    case 'privateCar':
      return <IllustrationMiddlePrivateCarPublic fontSize={fontSize} />
    case 'unauthorized':
      return <IllustrationMiddleUnauthorized fontSize={fontSize} />
    case 'Input':
      return <IllustrationMiddleInput fontSize={fontSize} />
    default:
      return <IllustrationSmallNoContent fontSize={fontSize} />
  }
}

const EmptyWidget: FC<IProps> = props => {
  const { size = 200, tips = i18n.get('暂无数据'), type, bottom, infos = [], backgroundColor, style: PropsStyle = {} } = props
  const isLarge = size === 200
  const paddingBottom = bottom ?? (isLarge ? 80 : 0)
  let style = PropsStyle
  if (isLarge) {
    style = { ...style, backgroundColor: backgroundColor ?? 'var(--eui-bg-base)' } //空状态添加一个背景色
  }

  const getTipClassName = () => {
    if (!isLarge) {
      return 'empty-widget-tips-top-small'
    } else if (infos.length > 0) {
      return 'empty-widget-tips-top'
    } else {
      return 'empty-widget-tips-bottom'
    }
  }

  //列表的时候用的200的大图，空状态的时候有时候不居中，下面空出来80的空白
  //目前只有size只有200和100，列表的时候用200，卡片空状态用100，大图的空状态提示文字的颜色和小图空状态提示文字的颜色不一样
  return (
    <div className={styles['empty-widget']} style={style}>
      {Icons(type, size)}
      <div style={{ paddingBottom }} className="empty-widget-tips">
        <span className={getTipClassName()}>
          <T name={tips} />
        </span>
        {infos.length > 0 &&
          infos.map((info, index) => {
            return (
              <span key={index} className="empty-widget-tips-bottom">
                <T name={info} />
              </span>
            )
          })}
      </div>
    </div>
  )
}

export default EmptyWidget
