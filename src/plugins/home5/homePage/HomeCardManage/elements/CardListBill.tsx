/**
 *  Created by pw on 2022/11/3 8:25 PM.
 */
import React from 'react'
import './CardListBill.less'
import { get } from 'lodash'
import { app } from '@ekuaibao/whispered'
import Highlighter from 'react-highlight-words'
import { getMyBillApproveStatus } from '../../../util/myBIllStatus'
import { FlowTypeEnum } from '../utils/enums'
import { transformData } from '../utils/transform'
import { SourceSignEnum } from '../../../../../lib/enums'
const { getSpecificationName } = app.require<any>('@bill/utils/billUtils')


const EKBIcon = app.require<any>('@elements/ekbIcon')
const Money = app.require<any>('@elements/puppet/Money')

interface Props {
  list: any[]
  searchValue?: string
  flowType?: FlowTypeEnum
  onClickItem?: (data: any) => void
}

export const CardListBill: React.FC<Props> = props => {
  const { list = [], searchValue, flowType, onClickItem } = props
  if (!list.length) {
    return null
  }

  return (
    <div className="card-list-bill-container">
      {list.map((el: any, idx: number) => {
        return (
          <CardList_BillItem
            key={idx}
            data={el}
            searchValue={searchValue}
            flowType={flowType}
            onClickItem={onClickItem}
          />
        )
      })}
    </div>
  )
}

interface ICardListBillItem {
  data: any
  searchValue?: string
  needOperatorFormPlan?: boolean
  isAlwaysPrint?: boolean
  flowType?: FlowTypeEnum
  onClickItem?: (data: any) => void
}

export const CardList_BillItem: React.FC<ICardListBillItem> = props => {
  const { data, searchValue, needOperatorFormPlan, isAlwaysPrint = false, flowType, onClickItem } = props

  const {
    id,
    state,
    payingFailure,
    urgent,
    title,
    isRiskWarning,
    code,
    amount,
    color,
    auto,
    alterFlag,
    subsidyGeneration,
    dateStr,
    type,
    standardStrCode,
    staff,
    specification,
    sourceSign
  } = transformData(data, flowType)

  let t = title || i18n.get('[无标题]')
  if (searchValue) {
    t = (
      <Highlighter
        highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
        searchWords={[searchValue]}
        textToHighlight={t}
      />
    )
  }

  const handleClickItem = () => {
    if (onClickItem) {
      onClickItem({ id, formType: type, type, state, isAlwaysPrint, data })
    }
  }

  const operator = data.operator ? data.operator : staff ? { staff } : { staff: {} }
  const { name, str, staffAdditionInfo } = getMyBillApproveStatus({
    flow: { state },
    form: data.form,
    plan: data.plan || {},
    operator,
    needOperatorFormPlan,
    useStaffName: flowType === FlowTypeEnum.Backlog,
    needStaffObject: true
  } as any)

  return (
    <div className="card-bill-item-wrapper-new" onClick={handleClickItem}>
      <div className={'bill-info-title-wrapper'}>
        <div className={'left'}>
          <div className="bill-info-title">
            {urgent && <div className="bill-info-urgent">{i18n.get('[急]')}</div>}
            {payingFailure && <div className="bill-info-pay-failure">{`[${i18n.get('支付失败')}]`}</div>}
            {(auto || subsidyGeneration) && <div className="bill-info-tag-placeholder">[自动创建]</div>}
            {alterFlag && <div className="bill-info-tag-placeholder">[变更]</div>}
            {sourceSign === SourceSignEnum.SYSTEM && (
              <div className="bill-info-tag-placeholder">{`[${i18n.get('系统')}]`}</div>
            )}
            {isRiskWarning && isRiskWarning.status === 'HAVE' && (
              <EKBIcon name="#EDico-plaint-circle" style={{ color: '#fa8c16', marginRight: 5 }} />
            )}
            <span>{t}</span>
          </div>
        </div>
        {str && (
          <div className="right">
            <div className={`bill-state bill-state-${color}`}>{str}</div>
          </div>
        )}
      </div>
      {flowType !== FlowTypeEnum.Backlog && state !== 'draft' && !!name?.length ? (
        <div className="bill-info-staff-wrapper">
          <div className="bill-info-staff">{`${name}`}</div>
          {!!staffAdditionInfo?.length && <div className="bill-info-addition-staff">{`(${staffAdditionInfo})`}</div>}
          {!!str?.length && <div className="approve-desc">{str}</div>}
        </div>
      ) : null}
      {flowType === FlowTypeEnum.Backlog && !!name?.length ? (
        <div className="bill-info-staff-wrapper">
          <div className="bill-info-staff">{`${name}`}</div>
          {!!staffAdditionInfo?.length && <div className="bill-info-addition-staff">{`(${staffAdditionInfo})`}</div>}
          {!!specification && (
            <div className="approve-specification">
              <span className="mood-words">{i18n.get('的')}</span>
              {getSpecificationName(specification)}
            </div>
          )}
        </div>
      ) : null}
      <div className="bill-info">
        <div className="bill-info-code">
          {dateStr}
          {' ' + code}
          {window.isNewHome && get(data, 'flow.state') === 'paid' && (
            <span className="cardList_BillItem-status">{i18n.get('未确认')}</span>
          )}
        </div>
        <div className="bill-info-money-wrapper">
          {amount && Number(amount) !== 0 ? (
            <Money value={amount} showShorthand={true} currencySymbol={standardStrCode} className="bill-info-money" />
          ) : (
            <span className="bill-info-money-no-money">{i18n.get('暂无金额')}</span>
          )}
        </div>
      </div>
    </div>
  )
}
