@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.my-loan-card-wrapper {
  display: flex;
  width: 100%;
  flex-direction: column;
  :global {
    .my-loan-card-info {
      display: flex;
      font: var(--eui-font-note-r2);
      flex-direction: row;
      color: var(--eui-text-caption);
      justify-content: space-between;
    }
    .my-loan-card-money {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .my-loan-card-money-left {
        font: var(--eui-num-display-b1);
        color: var(--eui-primary-pri-500);
      }
      .my-loan-card-money-right {
        font: var(--eui-num-display-b1);
        color: var(--eui-text-placeholder);
      }
    }
    .my-loan-card-progress {
      height: @space-2;
      background: var(--eui-bg-float-base);
      width: 100%;
      margin-top: @space-2;
      margin-bottom: @space-6;
      span {
        height: @space-2;
        display: block;
        border-radius: 0px;
        position: relative;
        background-color: var(--eui-primary-pri-500);
      }
    }
    .my-loan-card-bottom {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-bottom: 10px;
      .mr-16 {
        margin-right: @space-4;
      }
      .ml-16 {
        margin-left: @space-4;
      }
      .my-loan-card-bottom-item {
        height: 53px * 2;
        background: var(--eui-bg-body-overlay);
        border-radius: @space-3;
        display: flex;
        flex: 1;
        flex-direction: column;
        padding: @space-3 @space-4;
        .card-item-label {
          font: var(--eui-font-note-r2);
          color: var(--eui-text-caption);
          margin-bottom: @space-1;
        }
        .card-item-money {
          font: var(--eui-num-head-b1);
          color: var(--eui-text-caption);
        }
      }
    }
  }
}
