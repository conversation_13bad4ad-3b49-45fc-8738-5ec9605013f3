import React from 'react'
import { app } from '@ekuaibao/whispered'
import { T } from '@ekuaibao/i18n'
import './EditHome.less'
import { IHomeTemplateVersion } from '../Types'

interface Props {
  hasNewHomeTemplateVersion?: IHomeTemplateVersion
}

const EditHome: React.FC<Props> = () => {
  return (
    <div className="home-page-edit" onClick={() => app.go('/homeEditableView', false)}>
      <T name="自定义我的首页" />
    </div>
  )
}

export default EditHome
