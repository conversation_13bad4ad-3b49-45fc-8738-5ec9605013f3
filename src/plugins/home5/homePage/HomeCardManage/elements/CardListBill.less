@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-bill-item-wrapper-new {
  margin-top: @space-4;
  padding: @space-4 @space-5;
  display: flex;
  flex-direction: column;
  position: relative;
  border-radius: 12px;
  background-color: var(--eui-bg-body-overlay);
  &:active {
    padding: 0 -32px;
    background-color: @color-bg-1;
  }
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    .bill-info-money-wrapper {
      bottom: 0;
    }
  }
  .bill-info-title-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 100px;
    justify-content: space-between;
    .left {
      display: flex;
      width: 100%;
      .bill-info-title {
        font: var(--eui-font-body-b1);
        color: var(--eui-text-title);
        overflow: hidden;
        flex: 1;
        display: flex;
        align-items: center;
        .bill-info-expedited {
          color: @color-error-1;
          margin-right: @space-2;
          flex-shrink: 0;
        }
        span {
          user-select: none;
          color: @color-black-1;
          .font-size-3;
          .font-weight-3;
          .ellipsis();
        }
      }
      .bill-info-pay-failure {
        flex-shrink: 0;
        margin-right: @space-2;
        font: var(--eui-font-body-r1);
        color: var(--eui-function-danger-500);
      }
      .bill-info-urgent {
        flex-shrink: 0;
        margin-right: @space-2;
        font: var(--eui-font-body-r1);
        color: var(--eui-function-danger-500);
      }
      .bill-info-tag-placeholder {
        flex-shrink: 0;
        margin-right: @space-2;
        font: var(--eui-font-body-r1);
        color: var(--eui-text-placeholder);
      }
    }
    .right {
      position: absolute;
      right: 0;
      top: 0;
      display: flex;
      border-radius: 0 4px;
      .operator-span {
        display: inline-block;
        max-width: 240px;
      }
      .bill-state {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-white);
        padding: 2px 8px;
        border-top-right-radius: 12px;
        border-bottom-left-radius: 8px;
      }
      .bill-state-red {
        //驳回
        color: var(--eui-function-danger-600);
        background: var(--eui-function-danger-100);
      }
      .bill-state-blue {
        //审批中
        color: var(--eui-function-info-600);
        background: var(--eui-function-info-100);
      }
      .bill-state-orange {
        //待提交
        color: var(--eui-decorative-neu-600);
        background: var(--eui-decorative-neu-100);
      }
      .bill-state-green {
        //已完成
        color: var(--eui-function-success-600);
        background: var(--eui-function-success-100);
      }
    }
  }
  .bill-info-staff-wrapper {
    margin-top: @space-4;
    display: flex;
    .bill-info-staff {
      font: var(--eui-font-note-b2);
      color: var(--eui-text-link-normal);
    }
    .bill-info-addition-staff {
      margin-left: @space-2;
      font: var(--eui-font-note-r2);
      color: var(--eui-text-link-normal);
    }
    .approve-desc {
      margin-left: @space-2;
      font: var(--eui-font-note-r2);
      color: var(--eui-text-title);
    }
    .approve-specification {
      margin-left: @space-2;
      font: var(--eui-font-note-r2);
      color: var(--eui-text-title);
      .mood-words {
        margin-right: @space-2;
      }
    }
  }

  .bill-info {
    margin-top: @space-4;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .bill-info-code {
      font: var(--eui-font-note-r2);
      color: var(--eui-text-placeholder);
      .cardList_BillItem-status {
        .font-size-1;
        background-color: @color-warning-5;
        color: @color-warning-1;
        padding: 0 @space-4;
        border-radius: @radius-1;
        height: 44px;
      }
    }
    .bill-info-money-wrapper {
      .bill-info-money {
        font: var(--eui-num-body-b1);
        color: var(--eui-text-title);
        .currency {
          font: var(--eui-font-note-r2);
          color: var(--eui-text-title);
        }
      }
      .bill-info-money-no-money {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-placeholder);
        user-select: none;
      }
    }
  }
}
