import React, { FC } from 'react'
import HomeCardRightPart from '../../../util/HomeCardRight'
import { getCardLabel } from '../utils/helps'
import styles from './MyLoanCard.module.less'
import { app as api } from '@ekuaibao/whispered'
import { Card } from '@hose/eui-mobile'
import { mapForCodeToPath } from '../../../staticUtil'
import { CodeType } from '../../../home5.store'
import { thousandBitSeparator } from '../../../../../components/utils/fnThousandBitSeparator'
interface IProps {
  data: any
}

const MyLoanCard: FC<IProps> = (props: IProps) => {
  const { data = {} } = props
  const { detail = {} } = data
  const { data: loanData } = detail
  const { remain = 0, repayment = 0, reserved = 0, total = 0 } = loanData || {}

  const handleOnClick = () => {
    if (data.code === CodeType.myLoan) {
      api.go(`/${mapForCodeToPath[data.code]}/${remain}/${repayment}/${reserved}/${total}`, false)
    } else {
      api.go(`/${mapForCodeToPath[data.code]}`, false)
    }
  }
  // remain: 4589   剩余借款
  // repayment: 550 已还款
  // reserved: 5    还款中
  // total: 5144
  return (
    <div className="home-card-list-container">
      <Card
        title={`${getCardLabel(data)}(CNY)`}
        extra={<HomeCardRightPart hasRight={true} label={i18n.get('全部')} onClick={handleOnClick} />}
      >
        <div className={styles['my-loan-card-wrapper']}>
          <div className="my-loan-card-info">
            <span>{i18n.get('还款中')}</span>
            <span>{i18n.get('待还款')}</span>
          </div>
          <div className="my-loan-card-money">
            <span className="my-loan-card-money-left">{thousandBitSeparator(reserved.toFixed(2))}</span>
            <span className="my-loan-card-money-right">{thousandBitSeparator(remain.toFixed(2))}</span>
          </div>
          <div className="my-loan-card-progress">
            <span style={{ width: `${(reserved / (reserved + remain)) * 100}%` }}></span>
          </div>
          <div className="my-loan-card-bottom">
            <div className="my-loan-card-bottom-item mr-16">
              <span className="card-item-label">{i18n.get('已还款')}</span>
              <span className="card-item-money">{thousandBitSeparator(repayment.toFixed(2))}</span>
            </div>
            <div className="my-loan-card-bottom-item ml-16">
              <span className="card-item-label">{i18n.get('借款总额')}</span>
              <span className="card-item-money">{thousandBitSeparator(total.toFixed(2))}</span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default MyLoanCard
