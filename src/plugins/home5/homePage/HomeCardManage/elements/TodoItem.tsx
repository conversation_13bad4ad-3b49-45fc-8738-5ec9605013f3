import React from 'react'
import moment from 'moment/moment'
import styles from './TodoItem.module.less'
import { app } from '@ekuaibao/whispered'

interface Props {
  item: {
    body: {
      title: string
      form: { [key: string]: any }
    }
    description: string
    priority: 'Urgent' | 'Normal'
    updateTime: string
  }
  className?: string
}

export const TodoItem: React.FC<Props> = ({ item, className }) => {
  const handleClick = async () => {
    const res = await app.invokeService('@bill:get:flow:by:code', {
      code: item.body?.forms?.flowCode
    })
    const { backLogId, flow } = res.value
    if (backLogId) {
      app.go(`/approve/${flow.state}/${flow.formType}/${backLogId}`)
    } else {
      app.invokeService('@home:click:bill', flow, 'homePage')
    }
  }
  const isUrgent = item?.priority === 'Urgent'
  const title = item?.body?.title
  const subTitle = item?.description
  const time = moment(item?.updateTime).format('YYYY-MM-DD HH:mm')
  return (
    <div className={`${styles['todo-item']} ${className}`} onClick={handleClick}>
      <div className="todo-item-title">
        {isUrgent && <span className="urgent">[急]</span>}
        {title}
      </div>
      <div className="todo-item-subtitle">{subTitle}</div>
      <div className="todo-item-time">{time}</div>
    </div>
  )
}
export default TodoItem
