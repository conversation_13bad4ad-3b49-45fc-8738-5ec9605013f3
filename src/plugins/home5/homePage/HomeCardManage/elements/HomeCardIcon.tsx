import React from 'react'
import {
  TwoToneGeneralApply,
  TwoToneGeneralApproval,
  TwoToneGeneralBill,
  TwoToneGeneralCc,
  TwoToneGeneralCustom,
  TwoToneGeneralInvoicing,
  TwoToneGeneralLoan,
  TwoToneGeneralNote,
  TwoToneGeneralPrint,
  TwoToneGeneralReceipt,
  TwoToneGeneralSend,
  TwoToneGeneralToPaid,
  TwoToneGeneralTrip,
  TwoToneGeneralVehicle,
  TwoToneGeneralApproved,
  TwoToneGeneralPaymenthis,
  TwoToneGeneralCompleted,
  TwoToneGeneralWalletMe,
  TwoToneGeneralTodo
} from '@hose/eui-icons'
import { ICard } from '../Types'

const DEFAULT_FONT_SIZE = 32

const HomeCardIcon: React.FC<{ card: ICard; fontSize?: number }> = ({ card, fontSize = DEFAULT_FONT_SIZE }) => {
  if (card.sourceType === 'CUSTOM') {
    return <img src={card?.icon} alt="" width={24} height={24} />
  }
  if (card.code === 'dataLinkEntity' && card.detail?.dataLinkEntity?.platformId?.type === 'TRAVEL_MANAGEMENT') {
    //我的行程业务对象
    return <TwoToneGeneralTrip fontSize={fontSize} />
  }
  switch (card.code) {
    case 'myRequisition': // 申请事项
      return <TwoToneGeneralNote fontSize={fontSize} />
    case 'auditPayment': // 待我支付
      return <TwoToneGeneralToPaid fontSize={fontSize} />
    case 'myBill': // 我的单据
      return <TwoToneGeneralBill fontSize={fontSize} />
    case 'auditPending': // 待我审批
      return <TwoToneGeneralApproval fontSize={fontSize} />
    case 'waitSending': // 待我寄送
      return <TwoToneGeneralSend fontSize={fontSize} />
    case 'receiveExpress': // 待我收单
      return <TwoToneGeneralReceipt fontSize={fontSize} />
    case 'myLoan': // 我的借款
      return <TwoToneGeneralLoan fontSize={fontSize} />
    case 'auditCarbonCopy': // 抄送我的
      return <TwoToneGeneralCc fontSize={fontSize} />
    case 'waitInvoiceDetail': // 待开票费用
      return <TwoToneGeneralInvoicing fontSize={fontSize} />
    case 'todoPrint': // 待我打印
      return <TwoToneGeneralPrint fontSize={fontSize} />
    case 'recordExpends': // 随手记
      return <TwoToneGeneralApply fontSize={fontSize} />
    case 'dataLinkEntity': // 行程
      return <TwoToneGeneralCustom fontSize={fontSize} />
    case 'privateCar': // 私车公用
      return <TwoToneGeneralVehicle fontSize={fontSize} />
    case 'completed': // 已完成 -- 没有提供
      return <TwoToneGeneralCompleted fontSize={fontSize} />
    case 'approved': // 已审批 -- 没有提供
      return <TwoToneGeneralApproved fontSize={fontSize} />
    case 'paymentHistory': // 还款记录 -- 没有提供
      return <TwoToneGeneralPaymenthis fontSize={fontSize} />
    case 'eCard2':
      return <TwoToneGeneralWalletMe fontSize={fontSize} />
    case 'allTodo': // 代办中心
      return <TwoToneGeneralTodo fontSize={fontSize} />
    default:
      return <TwoToneGeneralCustom fontSize={fontSize} />
  }
}

export default HomeCardIcon
