import React from 'react'
import { app } from '@ekuaibao/whispered'
import { T } from '@ekuaibao/i18n'
import './CardRight.less'
import { ICard } from '../Types'
import { getCountStr } from '../../../util/SimpleCardUtil'
const EKBIcon = app.require<any>('@elements/ekbIcon')
const Money = app.require<any>('@elements/puppet/Money')
interface Props {
  label?: string
  card?: ICard
  onClick?: () => void
}

const CardRight: React.FC<Props> = props => {
  const { label, card, onClick } = props

  return (
    <div
      className="home-page-card-right"
      onClick={() => {
        onClick && onClick()
      }}
    >
      {label && (
        <div className="home-card-right-title">
          <T name={label} />
        </div>
      )}
      <EKBIcon name={'#EDico-right-default'} className="home-card-list-right-icon" />
    </div>
  )
}

export default CardRight
