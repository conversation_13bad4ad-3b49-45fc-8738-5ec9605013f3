import React, { FC, useRef } from 'react'
import { getCardLabel } from '../utils/helps'
import styles from './MallCard.module.less'
import { Card } from '@hose/eui-mobile'
import { ICard } from '../Types'
import { Fetch } from '@ekuaibao/fetch'
import { IMalCard } from '../../../home5.store'
import { app as api } from '@ekuaibao/whispered'

interface IProps {
  card: ICard
  meInfo: any
  mallCardDataList: any[]
}

const MallCard: FC<IProps> = (props: IProps) => {
  const { card, meInfo, mallCardDataList } = props
  const tokenId = useRef()

  const fnGetParamsStr = async () => {
    const staffSetting = Fetch.staffSetting || {}
    const language = i18n.currentLocale || staffSetting.language
    let version = window.APPLICATION_VERSION
    // @ts-ignore
    let codePushVersion = window.CODEPUSH_VERSION ? window.CODEPUSH_VERSION : ''
    const authQuery = await api.invokeService('@mall:get:mall:auth:query', { type: 'MENU_MALL' })
    return `${authQuery}&language=${language}&version=${version}&codePushVersion=${codePushVersion}&refPlatform=${
      window.__PLANTFORM__
    }&refTime=${Date.now()}&corpId=${Fetch.ekbCorpId}`
  }

  const getClickName = (url: string) => {
    const urlArr = url.split('/')
    return urlArr[urlArr.length - 1] || url
  }

  const handleClickTrack = async (item: IMalCard) => {
    const { title, url } = item
    const type = /\?/.test(url) ? '&' : '?'
    const paramsStr = await fnGetParamsStr()
    const clickName = getClickName(url)
    let iframe = true
    if (window.isWxWork || window.isWebchat) {
      iframe = false
    }
    api.invokeService('@layout:open:link', `${url}${type}${paramsStr}`, undefined, undefined, iframe)
    const { staff } = meInfo
    const { id: userId } = staff
    const { id: corpId } = staff?.corporationId || {}
    // @ts-ignore
    api.track(clickName, {
      actionName: i18n.get(title),
      userId,
      corpId,
      source: window.__PLANTFORM__,
      dataSource: 'EKB'
    })
  }
  return (
    <div className="home-card-list-container">
      <Card title={getCardLabel(card)}>
        <div className={styles['mall-card-wrapper']}>
          {mallCardDataList.map(item => {
            return (
              <div key={item.title} className={'item'} onClick={() => handleClickTrack(item)}>
                <img src={item.picture} alt="" />
                <span>{i18n.get(item.title)}</span>
              </div>
            )
          })}
        </div>
      </Card>
    </div>
  )
}

export default MallCard
