/**
 *  Created by pw on 2022/11/10 8:23 PM.
 */
import React from 'react'
import './OverviewInfoView.less'

interface OverviewInfoViewProps {
  count: number
  title?: string
  imageSrc?: string
  onClick?: () => void
}

export const OverviewInfoView: React.FC<OverviewInfoViewProps> = props => {
  const { count, title, onClick } = props
  if (!props.count) {
    return null
  }
  const handleOnClick = () => {
    if (onClick) {
      onClick()
    }
  }
  return (
    <div className="overview-info-view-container" onClick={handleOnClick}>
      <div className="title">{count}</div>
      {title?.length && <div className={'sub-title'}>{title}</div>}
    </div>
  )
}
