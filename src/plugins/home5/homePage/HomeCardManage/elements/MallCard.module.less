@import '~@ekuaibao/eui-styles/less/token-mobile.less';
@import '~@ekuaibao/eui-styles/less/token-mobile-2.0.less';

.mall-card-wrapper {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  :global {
    .item {
      display: flex;
      flex-direction: column;
      padding: 16px 0;
      align-items: center;
      overflow: hidden;
      &:hover {
        cursor: pointer;
      }
      &:active {
        background-color: @color-bg-2;
        border-radius: @radius-2;
      }
      img {
        width: 38px * 2;
        height: 38px * 2;
        margin-bottom: @space-3;
      }
      span {
        font-weight: 400;
        font-size: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        color: var(--eui-text-title);
        text-align: center;
      }
    }
  }
}
