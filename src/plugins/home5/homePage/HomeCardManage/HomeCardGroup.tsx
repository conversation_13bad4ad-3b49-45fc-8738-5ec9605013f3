import React, { useEffect, useState, ReactElement } from 'react'
import './HomeCardGroup.less'
import { Card, Swiper, Grid } from '@hose/eui-mobile'
import { ICard, IGroup, IMalCard } from './Types'
import { app } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { cardMap } from '../../util/card_map'
import { onCardClick } from '../../elements/cards/url'
import classNames from 'classnames'
import { CodeType } from '../../util/card.enum'
import { hasInArray } from '../../layers/card_group_edit_modal/util'
import { toJS } from 'mobx'
import HomeCardRightPart from '../../util/HomeCardRight'
import HomeCardIcon from './elements/HomeCardIcon'
import { getCardLabel } from './utils/helps'
const RedDot = app.require<any>('@elements/ekb-badge')

interface Props {
  group: IGroup
  isNewMall: boolean
  mallCardDataList: IMalCard[]
  cardList: ICard[]
  cardBacklogList: ICard[]
}
const Default_Column_Count = 4 // 默认列
const Default_One_Screen_Count = 8 // 每一屏展示的个数
export const mallCardDataLength: number = 1 // 首页商城卡片为1的时候显示

export const HomeCardGroup: React.FC<Props> = props => {
  const { group, isNewMall, mallCardDataList, cardList, cardBacklogList } = props
  const [groups, setGroups] = useState<ICard[][]>([])

  const totalScreen = Math.ceil(group?.cards?.length / Default_One_Screen_Count)
  useEffect(() => {
    const _groups = []
    for (let index = 0; index < totalScreen; index++) {
      const list = group.cards.slice(index * Default_One_Screen_Count, Default_One_Screen_Count * (index + 1))
      _groups.push(list)
    }
    setGroups(_groups)
  }, [group?.cards])

  const handleEditCard = (pid: string, groupLabel: string) => {
    const showMallCard = mallCardDataList.length >= mallCardDataLength && isNewMall
    const cards = showMallCard ? cardList : cardList.filter(item => item.code !== CodeType.mall)
    const cardBacklogs = showMallCard ? cardBacklogList : cardBacklogList.filter(item => item.code !== CodeType.mall)
    app
      .open('@home5:CardGroupEditModal', { pid, label: groupLabel, cardList: cards, cardBacklogList: cardBacklogs })
      .then(async (result: { data: ICard[]; label: string } | 'cancel') => {
        if (result === 'cancel') {
          return
        }
        const { data, label } = result
        const enlargedList = toJS(cards)

        // 2. 会被显示的根节点卡片们（尤其包括卡片组）
        const rootCards = enlargedList
          // 所有不在分组中的卡片
          .filter(card => !card.pid && !hasInArray(data, c => c.id === card.id))
          // 重排 weight, 更新 label
          .reduce((acc, card) => {
            if (card.id === pid) {
              card.label = label
            }
            card.weight = (acc.length + 1) * 100 + 1000
            acc.push(card)
            return acc
          }, [])

        // 3. 隐藏在卡片组中的卡片们
        const leafCards = enlargedList
          // 未被编辑的卡片组的子卡片
          .filter(card => card.pid && card.pid !== pid)
          // 新增/编辑 出来的新子卡片，需要先重排 weight 并填充 pid
          .concat(
            data.reduce((acc, card) => {
              card.weight = (acc.length + 1) * 100 + 1000 // 重排 weight
              card.pid = pid // 新增卡片组的时候，子卡片并没有这个信息
              acc.push(card)
              return acc
            }, [])
          )

        // 5. 新 cardList
        const list = rootCards.concat(leafCards)
        const updateCards = list
          .map((card, idx) => {
            // delete card.detail
            return {
              ...toJS(card),
              weight: (idx + 1) * 100 + 1000,
              detail: undefined
            }
          })
          .map(card => {
            card.selected = true
            return card
          })
        app.store.dispatch('@homePage/updatePersonalHomePage')(updateCards, null, true)
      })
  }

  if (!groups?.length) {
    return null
  }

  return (
    <div className="home-group-card-wrapper">
      <Card
        title={getCardLabel(group)}
        extra={
          <HomeCardRightPart
            hasRight={true}
            label={i18n.get('编辑')}
            onClick={() => handleEditCard(group.id, group.label)}
          />
        }
      >
        <Swiper
          indicator={groups.length <= 1 ? () => null : undefined}
          className={classNames('home-group-card-swiper')}
        >
          {groups.map((cards, index) => {
            return (
              <Swiper.Item key={index}>
                <Grid columns={Default_Column_Count} gap={12}>
                  {cards.map(card => {
                    const badge = getBadge(card)
                    const cardConfig = cardMap(card.id)
                    return (
                      <Grid.Item key={card.id}>
                        <span
                          className="home-group-card-item"
                          key={card.id}
                          onClick={() => onCardClick(app, card, isNewMall)}
                        >
                          <span className="card-group-avatar">
                            <HomeCardIcon card={card} fontSize={38} />
                            <RedDot isCorner text={badge} className={'home-card-red-dot'} />
                          </span>
                          <span className="card-group-desc">{cardConfig.label || getCardLabel(card)}</span>
                        </span>
                      </Grid.Item>
                    )
                  })}
                </Grid>
              </Swiper.Item>
            )
          })}
        </Swiper>
      </Card>
    </div>
  )
}

/**
 * 根据后端给的 card.detail 决定要在 badge 上显示什么数值
 * 注：这里假设后端给的特定字段是 number 类型的
 * @param card
 */
const getBadge = (card: ICard): number => {
  let bage = 0
  const badgeType = get(card, ['detail', 'prompt', 'type'])
  // v1 只在 NUMBER 时显示（目前遇到的数据中有 MONEY 类型的，不在 group 内的卡片上显示）
  if (badgeType === 'NUMBER') {
    bage = get(card, ['detail', 'prompt', 'value'])
  }

  // v2 自定义的情况下，这个数字也要显示
  // 这里利用 JS 的特性，把 dataLinkCount === 0 的情况踢给了默认 return null
  if (card?.detail?.dataLinkCount) {
    bage = Number(card?.detail?.dataLinkCount)
  }

  return bage
}

interface IPageIndicator {
  color?: string
  current?: number
  total: number
}

const PageIndicator: React.FC<IPageIndicator> = props => {
  const { total, current = 0 } = props
  const dots: ReactElement[] = []
  for (let i = 0; i < total; i++) {
    dots.push(
      <div
        key={i}
        className={classNames(`indicator-dot`, {
          [`indicator-dot-active`]: current === i
        })}
      />
    )
  }
  return (
    <div style={{ width: total * 12 }} className={'indicator-wrapper'}>
      {dots}
    </div>
  )
}
