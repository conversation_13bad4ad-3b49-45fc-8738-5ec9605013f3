@import '~@ekuaibao/eui-styles/less/token-mobile.less';
.ecard-container-disabled {
  :global {
    .ecard-content-wrapper {
      width: 100%;
      height: 136px;
      border-radius: 16px;
      position: relative;
      background: linear-gradient(93deg, #F2F3F5 1.94%, #E5E6EB 100.34%);
      .ecard-content-cover {
        position: absolute;
        top: 0;
        right: 24px;
        bottom: 0;
        left: 0;
        padding: 0 32px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: right;
        background-image: url('../../images/e-card-mask-disabled.png');
        .ecard-content-left {
          display: flex;
          flex-direction: row;
          align-items: center;
          .ecard-content-left-logo {
            width: 64px;
            height: 64px;
            margin-right: 16px;
          }
          .ecard-content-left-info {
            display: flex;
            flex-direction: column;
            .ecard-content-left-info-title {
              color: var(--eui-text-caption);
              font: var(--eui-font-body-b1);
            }
          }
        }
      }
    }
    .ecard-content-wrapper-enable {
      .ecard-content-wrapper();
      background: linear-gradient(96deg, #e8f1ff 1.86%, #b7c5de 135.93%);
      .ecard-content-cover {
        background-image: url('../../images/hose_bg_small.png');
        .ecard-content-left {
          .ecard-content-left-info {
            .ecard-content-left-info-title {
              color: var(--eui-text-title);
            }
          }
        }
      }
    }
  }
}
