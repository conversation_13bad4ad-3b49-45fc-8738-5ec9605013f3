@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.ecard-feetype-list-wrapper {
  display: flex;
  width: 100%;
  flex-direction: column;
  :global {
    .ecard-content-info {
      margin-top: 16px;
      margin-bottom: 8px;
      color: var(--eui-text-caption);
      font: var(--eui-font-body-r1);
    }
    .ecard-content-feetype-wrapper {
      display: grid;
      gap: 16px;
      grid-template-columns: repeat(3, minmax(68px, 1fr));
      .ecard-content-feetype-item {
        background-color: var(--eui-bg-body-overlay);
        border-radius: 8px;
        padding: 16px 20px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .ecard-content-feetype-item-img {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 12px;
          flex-shrink: 0;
        }
        .ecard-content-feetype-item-label {
          color: var(--eui-text-title);
          font: var(--eui-font-note-r2);
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
}
