/***************************************************
 * Created by nany<PERSON>ingfeng on 2020/2/20 15:42. *
 ***************************************************/
import React from 'react'
import { RemoteComponent, ErrorBoundary } from '@ekuaibao/3rd_resources'

export default class RemoteComponentLayer extends React.Component<any> {
  getResult(): any {
    return null
  }

  render() {
    const { resource, requestData } = this.props
    return (
      <ErrorBoundary>
        <RemoteComponent {...resource} requestData={requestData} fallback={'........'} />
      </ErrorBoundary>
    )
  }
}
