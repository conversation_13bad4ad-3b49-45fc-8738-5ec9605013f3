export default [
  {
    key: 'SelectOneResource',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      maskClosable: true,
      closable: false,
      popup: true,
      animationType: 'slide-up',
      wrapClassName: 'select-one-modal-popup-wrapper'
    },
    getComponent: () => import('./SelectOneResourceLayer'),
    // maskClosable: true
    animationType: 'slide-up',
  },
  {
    key: 'RemoteHTML',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      closable: false,
      wrapClassName: 'full-screen-modal'
    },
    getComponent: () => import('./RemoteHTMLLayer'),
    timeout: 500
  },
  {
    key: 'RemoteComponent',
    enhancer: 'modal',
    enhancerOptions: {
      transparent: false,
      animated: true,
      visible: true,
      closable: false,
      wrapClassName: 'full-screen-modal'
    },
    getComponent: () => import('./RemoteComponentLayer'),
    maskClosable: false
  }
]
