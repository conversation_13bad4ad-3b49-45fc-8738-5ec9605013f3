.trip-action-part {
  display: flex;
  padding-top: 32px;
  flex-wrap: wrap;
  :global {
    .icon-wrapper {
      width: 25%;
      height: 200px;
      img {
        width: 100px;
        height: 100px;
      }
      .dis {
        opacity: 0.5;
      }
      .title {
        font-size: 24px !important;
      }
    }
  }
}

.hose-trip {
  height: 156px;
  background: #eff6ff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  margin: 12px 48px;
  position: relative;
  :global {
    .icon {
      width: 76px;
      height: 76px;
      margin-left: 40px;
    }
    .title-wrapper {
      display: block;
      text-align: left;
      margin-left: 32px;
      flex: 1;
      .title {
        color: #333333;
        font-size: 30px;
        font-weight: 500;
      }
      .desc {
        color: #666666;
        font-size: 26px;
      }
    }
    .right {
      width: 80px;
      text-align: left;
    }
  }
}

.other-title {
  text-align: left;
  padding-left: 48px;
  padding-top: 48px;
  font-size: 26px !important;
  color: #666666;
}

.errorBlock {
  height: 100% !important;
}