/***************************************************
 * Created by nanyuantingfeng on 2020/2/20 14:41. *
 ***************************************************/
import React from 'react'
// @ts-ignore
import SVG_CHECKED from '../layers/images/checked.svg'
import styles from "./SelectOneResourceLayer.module.less"

import ctrip from './images/ctrip.png'
import didi from './images/didi.png'
import chailvyihao from './images/chailvyihao.png'
import mall from './images/hose-icon-new.png'
import ali from './images/ali.png'
import datang from './images/datang.png'
import right from './images/right-icon.svg'
import geely from './images/geely_travel.png'
import tc_trip from './images/TC_TRIP.jpeg'
import { ErrorBlock } from '@hose/eui-mobile'
import { IllustrationSmallNoContent } from '@hose/eui-icons'
import { ISource } from '../../../lib/mallSourceAuth'
const ICON_MAP: any = {
  "差旅壹号": chailvyihao,
  "合思商城": mall,
  "阿里商旅": ali,
  "大唐商旅": datang,
  "携程商旅": ctrip,
  "阿里商旅(H5)": ali,
  "滴滴企业版": didi,
  "吉利商旅": geely,
  "同程商旅": tc_trip,
}
export default class SelectOneResourceLayer extends React.Component<{ resources: ISource[]; layer: any }, any> {
  state = { value: -1, filter: false }
  async getResult() {
    return this.props.resources[Number(this.state.value)]
  }

  handleInputChange = (item: any, value: number) => {
    const { disabled } = item
    if (disabled) {
      return
    }
    this.setState({ value }, () => {
      this.props.layer.emitOk()
    })
  }
  componentDidMount() {
    const disabledItem = this.props.resources.find((item: any) => item.disabled)
    if (disabledItem) {
      this.setState({ filter: true })
    }
  }
  handleCancel = () => {
    this.props.layer.emitCancel()
  }
  renderTypeItem = (item: any, index: number) => {
    const { title, label, disabled } = item
    const { value } = this.state
    return (
      <div style={disabled ? { display: 'flex', flexDirection: 'row', color: "#B3B8BE" } : { display: 'flex', flexDirection: 'row' }}>
        <div style={{ flex: 1 }}>{title || label}</div>
        {value === index && <img src={SVG_CHECKED} style={{ width: 20, height: 20 }} />}
      </div>
    )
  }
  render() {
    "EDico-ali-travel"
    "EDico-trader"
    "EDico-xc-travel"
    const { resources } = this.props
    const { filter } = this.state
    const hoseMall: any = resources.find((v: any) => v.realm === 'MALL' && !v.disabled)
    const hoseMallIdx: number = resources.findIndex((v: any) => v.realm === 'MALL')
    const resourcesCopy = this.props.resources.map((item, index) => ({
      ...item,
      extraKey: index // 新增临时key属性，为每个对象保留其被过滤前的索引值
    }));
    return (
      <div className="modal-popup-content">
        <div className="modal-action-bar">
          <span className="modal-action-bar-title">{i18n.get('请选择商城')}</span>
          <div className="modal-action-bar-btn" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </div>
        </div>
        {resources.filter(item => !item.disabled).length == 0 && (
          <ErrorBlock className={styles['errorBlock']} status="empty" title="暂无可用的订购平台" image={<IllustrationSmallNoContent fontSize={100} />} />
        )}
        {hoseMall && <div className={styles["hose-trip"]} onClick={() => hoseMall.disabled ? null : this.handleInputChange(hoseMall, hoseMallIdx)}>
          <img src={ICON_MAP[hoseMall.title]} alt="" className={"icon"} />
          <div className="title-wrapper" key={hoseMall.realm}>
            <div className="title">{i18n.get('合思商旅')}</div>
            <div className="desc">无需垫资，无需开票，点此预订</div>
          </div>
          <div className="right">
            <img src={right} alt="" />
          </div>
        </div>}
        {resources.some(item => item.realm === 'Mall' && !item.disabled) && <div className={styles["other-title"]}>{i18n.get("第三方商城：")}</div>}
        <div className={styles["trip-action-part"]}>
          {resourcesCopy.filter(item => !item.disabled).map((item: any) => {
            const { title, label, disabled, power, realm } = item
            if (item.realm === 'MALL') {
              return null
            }
            return <div className="icon-wrapper" key={realm} onClick={() => disabled ? null : this.handleInputChange(item, item.extraKey)}>
              <img src={ICON_MAP[title]} alt="" className={disabled ? "dis" : ""} />
              <div className="title">{title || label}</div>
            </div>
          }
          )}
        </div>
      </div>
    )
  }
}