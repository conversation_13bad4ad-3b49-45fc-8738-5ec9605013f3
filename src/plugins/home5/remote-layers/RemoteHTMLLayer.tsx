/***************************************************
 * Created by nany<PERSON>ingfeng on 2020/2/20 15:42. *
 ***************************************************/
import React from 'react'
import { RemoteHTML } from '@ekuaibao/3rd_resources'
import './RemoteHTMLLayer.less'

export default class RemoteHTMLLayer extends React.Component<{ resource: any; requestData: any[]; layer: any }> {
  ref = React.createRef<any>()

  getResult() {
    // return this.ref.current.getResult() //如果iframe加载的html没有对接endpoint的话会拿不到返回值，导致对话框关闭不了，所以暂时先不拿返回值了
    return ''
  }

  handleGetResult = () => {
    this.props.layer.emitOk()
  }

  render() {
    const { resource, requestData } = this.props
    return (
      <div style={{ width: '100%', height: '100%' }}>
        <RemoteHTML ref={this.ref} {...resource} requestData={requestData} />
      </div>
    )
  }
}
