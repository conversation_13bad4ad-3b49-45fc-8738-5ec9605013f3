/*
 * @Author: Onein
 * @Date: 2019-02-26 17:18:27
 * @Last Modified by: xingdev
 * @Last Modified time: 2021-04-29 14:36:15
 */
@import '../../styles/layout.less';
@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.home5-view-wrapper {
  height: 100%;
  background: @color-white-1;
  overflow: auto;

  :global {
    .loading {
      width: 100%;
      height: 100%;
      background-color: @color-black-4;
      display: flex;
      align-items: center;
    }

    .home5-view-content-wrap {
      min-height: 100%;
      padding-bottom: @space-12;
      margin-bottom: 2px; // 解决轮播图造成的样式问题
      display: flex;
      flex-direction: column;
      background: @color-bg-2;

      .home5-view-content {
        flex: 1;
        margin-top: 16px;

        .corp-card-wrap {
          &:active {
            transform: scale(1);
            background-color: @color-bg-2;
          }

          display: flex;
          align-items: center;
          margin: @space-5 @space-6;
          padding: @space-6 @space-5 @space-6 @space-6;
          border-radius: @radius-3;
          .font-size-4;
          .font-weight-3;
          background-color: @color-white-1;
          transition: 0.1s all ease-in-out;

          .title-down {
            margin-left: @space-2;
            width: 30px;
            height: 30px;
          }

          .home5-logo {
            overflow: hidden;
            border-radius: 50%;
            margin-right: @space-4;
            width: 48px;
            height: 48px;
          }

          .text-ellipsis {
            max-width: 90%;
          }
        }
      }
    }

    .skeleton-wrap {
      overflow: hidden;
      margin: @space-5 @space-5 0;
      border-radius: @radius-4;
      background-color: @color-white-1;
      .shadow-black-4;
    }

    .home5-view-editBtn-wrap {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .home5-view-editBtn-text {
        user-select: none;
        .font-size-2;
        color: @color-inform-1;
        color: var(--brand-base);
        margin-top: @space-4;
        .font-weight-2;
      }

      .home5-view-editBtn {
        &:active {
          transform: scale(1);
          box-shadow: none;
          background-color: @color-bg-2;
        }

        display: flex;
        align-items: center;
        justify-content: center;
        width: 112px;
        height: 112px;
        border-radius: 50%;
        margin-top: @space-8;
        background-color: @color-white-1;
        transition: 0.1s all ease-in-out;
        .shadow-black-2;

        .icon {
          color: @color-black-2;
          .icon-size-5;
        }
      }
    }
  }
}

.home5-view-no-wrapper {
  height: 100%;
  background: @color-white-1;
  overflow: auto;

  :global {
    .loading {
      width: 100%;
      height: 100%;
      background-color: @color-black-4;
      display: flex;
      align-items: center;
    }

    .home5-view-content-wrap {
      min-height: 100%;
      padding-bottom: @space-12;
      display: flex;
      flex-direction: column;
      background: @color-bg-2;

      .home5-view-content {
        flex: 1;
      }
    }

    .skeleton-wrap {
      overflow: hidden;
      margin: @space-5 @space-5 0;
      border-radius: @radius-4;
      background-color: @color-white-1;
      .shadow-black-4;
    }

    .home5-view-editBtn-wrap {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .home5-view-editBtn-text {
        user-select: none;
        .font-size-2;
        color: @color-black-2;
        color: var(--brand-base);
        margin-top: @space-4;
        .font-weight-2;
      }

      .home5-view-editBtn {
        &:active {
          transform: scale(1);
          box-shadow: none;
          background-color: @color-bg-2;
        }

        display: flex;
        align-items: center;
        justify-content: center;
        width: 112px;
        height: 112px;
        border-radius: 50%;
        margin-top: @space-8;
        background-color: @color-white-1;
        transition: 0.1s all ease-in-out;
        .shadow-black-2;

        .icon {
          color: @color-black-2;
          .icon-size-5;
        }
      }
    }
  }
}

.corp-popup-search{
  display: flex;
  margin: 0 0 .3rem 0;
  border-bottom: 2px solid var(--gray-23);
  :global{
    .search{
      flex: 1;
      padding: 0 12px .3rem;
      .am-search{
        height: .68rem;
        .am-search-input{
          height: .68rem;
          .am-search-synthetic-ph{
            height: .68rem;
            line-height: .68rem;
          }
          .am-search-clear{
            top: 4px;
          }
        }
        .am-search-input input[type=search]{
          height: .68rem;
        }
      }
    }
    .sort{
      width: .68rem;
      height: .68rem;
      .icon{
        width: .48rem;
        height: .48rem;
        margin-top: 7px;
      }
    }
  }
}


.corp-popup-list {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 520px;
  margin-bottom: @space-8;
  :global {
    .corp-popup-item {
      padding-bottom: @space-6;
      display: flex;
      align-items: center;
      justify-content: center;

      &:last-child {
        padding-bottom: 0;
      }

      .font-size-3;

      &:active {
        background-color: @color-bg-3;
      }

      .text-ellipsis {
        max-width: 90%;
      }

      .checkedIcon {
        margin-left: @space-4;
        color: @color-brand-2;
        flex-shrink: 0;
      }
    }
    .search-null{
      margin: 0 auto;
      padding: .3rem;
      width: 100%;
      height: 5.2rem ;
      text-align: center;
      font-size: .16rem;
    }
  }
}