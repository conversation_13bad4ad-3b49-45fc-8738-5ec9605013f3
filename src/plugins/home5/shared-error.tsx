import React from 'react'
import styles from './shared-error.module.less'
import { T } from '@ekuaibao/i18n'
import { Fetch } from '@ekuaibao/fetch'
import { app as api } from '@ekuaibao/whispered'
import ProgressCircle from './elements/progressCircle'
import { LinearGradientComponent } from './elements/linear-gradient'
const app_logo_gif = require('./images/app_logo.gif')

export default class SharedError extends React.Component {
  state = { percent: 0 }
  percentTimer: any = null
  logoutTimer: any = null
  fnFormatProgressPercentage(percent: any) {
    let percentages = []
    let remainObj = {
      percent: percent / 100,
      color: 'url("#progressGradient")',
    }
    percentages.push(remainObj)
    let percentageInfo = {
      currentPercent: remainObj.percent,
      percents: percentages
    }
    return percentageInfo
  }
  componentDidMount() {
    this.getPercent()
     // @ts-ignore
     this.percentTimer = setInterval(this.getPercent, 30000)
  }
  componentWillUnmount() {
    clearInterval(this.percentTimer)
    this.percentTimer = null
    clearTimeout(this.logoutTimer)
    this.logoutTimer = null
  }
  getPercent = async () => {
    const result = await Fetch.GET('/api/shared/dataMigrating/percent', {
      corporationId: Fetch.ekbCorpId,
    })
    if (result?.value) {
      let percent: any = result.value.split('%')[0] ?? '0'
      percent = Number(percent)
      if (percent >= 100) {
        clearInterval(this.percentTimer)
        this.percentTimer = null
        this.logoutTimer = setTimeout(()=>{
          api.invokeService('@layout:action:logout').then(() => {
            // @ts-ignore
            api.gotoLoginPage()
          })
        },2000)
      }
      this.setState({ percent })
    }
  }

  render() {
    const { percent } = this.state
    return (
      <div className={styles['shared-error-wrapper']}>
        <div className="shared-error-progress">
          <div className='progress-circle-content'>
            <ProgressCircle className="circle" strokeWidth={5} percentageInfo={this.fnFormatProgressPercentage(percent)}>
              <img className="app-logo-icon" src={app_logo_gif} alt="" />
              <LinearGradientComponent
                className="progress-gradient"
                id="progressGradient"
                stopColorMap={{ '0%': 'rgba(83, 121, 255, 0.76)', '100%': '#0E3DE4' }}
              />
            </ProgressCircle>
          </div>
          <span className="progress-text">
            <T name={percent >= 100 ? '数据升级完成，即将为你跳转至集团版登录页' : `${percent}%`} />
          </span>
        </div>
        <div className="shared-error-content">
          <div className="content-info">
            <T name="易快报集团版即将和你见面，预计需要等待2小时" />
          </div>
          <div className="content-info">
            <T name="如有疑问，请" />
            <span className="connect-admin">
              <T name="联系管理员" />
            </span>
          </div>
        </div>
      </div>
    );
  }
}