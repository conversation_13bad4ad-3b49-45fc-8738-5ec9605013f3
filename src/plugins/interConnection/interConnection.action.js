/**************************************************
 * Created by nanyuanting<PERSON> on 8/23/16 18:01.
 **************************************************/
import key from './key'
import { Resource } from '@ekuaibao/fetch'
const connect = new Resource('/api/connect')

export function getTemplateData(value) {
  return {
    type: key.KEY_GETTEMPLATE_DATA,
    payload: connect.GET(`/template/$id/data`, { ...value }),
  }
}
export function getTemplateById(id) {
  return {
    type: key.KEY_GET_TEMPLATE_BY_ID,
    payload: connect.GET(`/template/$id`, {id }),
  }
}