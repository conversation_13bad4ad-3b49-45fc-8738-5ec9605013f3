/**************************************************
 * Created by nanyuanting<PERSON> on 8/18/16 14:13.
 **************************************************/
import { app as api } from '@ekuaibao/whispered'
import { getTemplateData,getTemplateById } from './interConnection.action'

export default [
  {
    id: '@inter-connection',
    reducer: () => require('./interConnection.reducer').default,
    'get:template:data'(value) {
      return api.dispatch(getTemplateData(value))
    },
    'get:template:by:id'(id) {
      return api.dispatch(getTemplateById(id))
    },
  },
  {
    point: '@@layers',
    prefix: '@inter-connection',
    onload: () => require('./layers').default
  }
]
