/**************************************************
 * Created by nanyuanting<PERSON> on 8/30/16 14:32.
 **************************************************/

import { Reducer } from '@ekuaibao/store'
import key from './key'
const { ID } = key

const reducer = new Reducer(ID, {
})

reducer.handle(key.KEY_GETTEMPLATE_DATA, (state, action) => {
  return { ...state }
})
reducer.handle(key.KEY_GET_TEMPLATE_BY_ID, (state, action) => {
  return { ...state }
})

export default reducer
