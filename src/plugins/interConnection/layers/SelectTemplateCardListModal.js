import { app, app as api } from '@ekuaibao/whispered'
import styles from './SelectTemplateCardListModal.module.less'
import React, { PureComponent } from 'react'
const EKBSearchBar = app.require('@ekb-components/base/EKBSearchBar')
const ListViewWrapper = app.require('@elements/listview/ListViewWrapper')
import { Button, Checkbox } from 'antd-mobile'
import classnames from 'classnames'
import { EnhanceConnect } from '@ekuaibao/store'
const CheckboxItem = Checkbox.CheckboxItem
const AgreeItem = Checkbox.AgreeItem
let timeout

@EnhanceConnect(state => ({
  dataLink: state['@bill'].dataLink
}))
export default class SelectTemplateCardListModal extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      listData: [],
      searchValue: '',
      selectedId: props.selectedId
    }
  }

  componentDidMount() {
    this.fetchData()
  }

  fetchData = (val = '') => {
    const { referenceData } = this.props
    api.invokeService('@inter-connection:get:template:data', { id: referenceData, keyWord: val }).then(res => {
      const data = res?.items || []
      this.setState({ listData: data })
    })
  }

  handleOnSearchChange = value => {
    value = value.trimStart() // 英文中间有空格
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
    this.setState({ searchValue: value, listData: [] }, () => {
      timeout = setTimeout(() => {
        this.fetchData.call(this, value)
      }, 400)
    })
  }

  handleCancel() {
    this.setState({ searchValue: '', listData: [], isAllChecked: false }, () => {
      this.fetchData()
    })
  }

  handleSearchCancel = () => {
    this.handleCancel()
  }

  handleOnSearchClear = () => {
    this.setState({ searchValue: '' })
  }

  handleSelectItemClick = (rowData, disabled) => {
    if (disabled) return
    const { multi } = this.props
    const { selectedId, listData } = this.state
    const { id } = rowData
    if (multi) {
      if (selectedId.indexOf(id) > -1) {
        this.setState({ selectedId: selectedId.filter(item => item !== id), isAllChecked: false })
      } else {
        this.setState({ selectedId: [...selectedId, id], isAllChecked: listData.length === selectedId.length + 1 })
      }
    } else {
      this.props.layer.emitOk({ rowData: [rowData] })
    }
  }
  handleImport = () => {
    const { selectedId, listData } = this.state
    const valueList = listData.filter(item => selectedId.indexOf(item.id) > -1)
    this.props.layer.emitOk({ rowData: valueList })
  }

  row = rowData => {
    const { selectedId = [] } = this.state
    const { multi } = this.props
    const checked = selectedId.indexOf(rowData.id) > -1
    const disabled = multi ? false : checked ? true : false
    const cls = classnames('item-wrapper', { 'bg-checked': checked })
    return (
      <div className={cls}>
        <CheckboxItem
          checked={checked}
          disabled={disabled}
          onChange={this.handleSelectItemClick.bind(this, rowData, disabled)}
        >
          <div>
            {rowData.name}
            {rowData.code ? `(${rowData.code})` : ''}
          </div>
        </CheckboxItem>
      </div>
    )
  }

  renderEmpty() {
    return <div className="datalink-empty">{i18n.get('无可选项')}</div>
  }

  renderListFooter = () => {
    let { isLoading, hasMore } = this.state
    let endStr = hasMore ? i18n.get('加载完毕') : i18n.get('没有更多数据了')
    return <div style={{ padding: 30, textAlign: 'center' }}>{isLoading ? i18n.get('加载更多') : endStr}</div>
  }

  onEndReached = () => {
    let { isLoading, hasMore, listData } = this.state
    if (isLoading || !hasMore) {
      return
    }
    this.setState({ isLoading: true })
    this.getEntityList('', listData.length)
  }

  renderTripOrderBottom = () => {
    const { isAllChecked } = this.state
    return (
      <div className={styles.thirdList_footer}>
        <AgreeItem checked={isAllChecked} onChange={this.handleCheckedAll}>
          {i18n.get('全选')}
        </AgreeItem>
        <Button type="primary" className={styles.thirdList_footer_btn} onClick={this.handleImport}>
          {i18n.get('确定')}
        </Button>
      </div>
    )
  }
  handleCheckedAll = e => {
    const { checked } = e.target
    const { listData } = this.state
    if (checked) {
      this.setState({
        selectedId: listData.map(item => item.id + ''),
        isAllChecked: true
      })
    } else {
      this.setState({
        selectedId: [],
        isAllChecked: false
      })
    }
  }
  render() {
    const { listData, searchValue } = this.state
    const { multi, placeholder } = this.props
    return (
      <div className={styles['dataLink-wrapper']}>
        <div className="search-bar-padding">
          <EKBSearchBar
            value={searchValue}
            placeholder={placeholder}
            onChange={this.handleOnSearchChange}
            onCancel={this.handleSearchCancel}
            onClear={this.handleOnSearchClear}
          />
        </div>
        <div className="datalink-content">
          {listData.length > 0 ? (
            <ListViewWrapper
              style={{ paddingTop: 0 }}
              isPrivateCar={false}
              footer={this.renderListFooter}
              onEndReached={this.onEndReached}
              onEndReachedThreshold={10}
              className="list-wrapper"
              listData={listData}
              itemRender={this.row}
            />
          ) : (
            this.renderEmpty()
          )}
        </div>
        {multi && this.renderTripOrderBottom()}
      </div>
    )
  }
}
