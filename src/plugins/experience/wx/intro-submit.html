<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="https://www.hosecloud.com/favicon.ico">
    <title>让报销成为快事一件</title>
    <script src="zepto.js"></script>
    <script src="intro.js"></script>
    <link rel="stylesheet" href="intro.css">
    <script type="text/javascript">

      // 下列数组中 x 代表呼吸灯中心点的x轴坐标
      // y 代表呼吸灯中心点的y轴坐标
      // title 代表页面下边分页的标题
      // info 代表呼吸灯上方的提示文字
      // url 代表当前引导页面的图片地址
      // 把相对应的数据填入就可以了
      // <br/>为换行标签

      var introArr = [{
        x: 586,
        y: 1048,
        title: '提交报销',
        info: '点击这里',
        url: 'intro-submit-1.png'
      }, {
        x: 180,
        y: 315,
        title: '提交报销',
        info: '点击这里，创建新的报销',
        url: 'intro-submit-2.png'
      }, {
        x: 180,
        y: 1163,
        title: '提交报销',
        info: '点击这里，添加消费明细',
        url: 'intro-submit-3.png'
      }, {
        x: 644,
        y: 1280,
        title: '提交报销',
        info: '填写消费信息后点击“保存”',
        url: 'intro-submit-4.png'
      }, {
        x: 355,
        y: 1168,
        title: '提交报销',
        info: '点击这里可以导入发票信息',
        url: 'intro-submit-5.png'
      },
        {
          x: 405,
          y: 666,
          title: '提交报销',
          info: '点击扫描发票，扫描发票二维码',
          url: 'intro-submit-6.png'
        }, {
          x: 624,
          y: 580,
          title: '提交报销',
          info: '选择需要报销的消费明细，点击生成消费',
          url: 'intro-submit-7.png'

        }, {
          x: 531,
          y: 1283,
          title: '提交报销',
          info: '确认消费类型，点击生成消费明细',
          url: 'intro-submit-8.png'

        },
        {
          x: 146,
          y: 940,
          title: '提交报销',
          info: '点击这里更换收款信息',
          url: 'intro-submit-9.png'

        }, {
          x: 345,
          y: 844,
          title: '提交报销',
          info: '选择报销时的收款信息',
          url: 'intro-submit-10.png'

        }, {
          x: 580,
          y: 1280,
          title: '提交报销',
          info: '报销单填写无误后，点击“提交送审”',
          url: 'intro-submit-11.png'

        }, {
          x: 540,
          y: 1045,
          title: '提交报销',
          info: '报销单将按照设定流程进行流转',
          url: 'intro-submit-12.png'

        }
      ]
    </script>
</head>

<body>
<div id="intro-wrap">
    <div id="intro-con">
        <img draggable="false" id="mobile-bg" src="./images/mobile-bg.png" alt="">
        <div id="intro-context">
            <img draggable="false" id="intro-img" src="" alt="">
            <div id="intro-info"><span></span></div>
            <div id="ball-scale">
                <div></div>
            </div>
        </div>
        <img draggable="false" id="mobile-bg-original" src="./images/mobile-bg.png" alt="" alt="">
    </div>
    <div id="intro-page"><span id="intro-title"></span>(<span id="intro-nowPage"></span>/<span
            id="intro-allPage"></span>)
    </div>
</div>
<div id="mask-layer">
    <div id="modals">
        <img draggable="false" src="./images/study-talent.png" alt="">
        <div id="modals-text">
            <p>您的学习速度超过了90%的人！</p>
            <p>提交报销技能 GET √</p>
            <p>点击返回继续学习</p>
        </div>
    </div>
</div>
</body>

</html>
