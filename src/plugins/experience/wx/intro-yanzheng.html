<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="https://www.hosecloud.com/favicon.ico">
    <title>让报销成为快事一件</title>
    <script src="zepto.js"></script>
    <script src="intro.js"></script>
    <link rel="stylesheet" href="intro.css">
    <script type="text/javascript">

    // 下列数组中 x 代表呼吸灯中心点的x轴坐标
    // y 代表呼吸灯中心点的y轴坐标
    // title 代表页面下边分页的标题
    // info 代表呼吸灯上方的提示文字
    // url 代表当前引导页面的图片地址
    // 把相对应的数据填入就可以了
    // <br/>为换行标签

    var introArr = [
	{
        x: 603,
        y: 1210,
        title: '发票验证',
        info: '点击“我的”，设置开票信息',
        url: 'intro-yanzheng-1.png'
    },{
        x: 147,
        y: 747,
        title: '发票验证',
        info: '选择“企业开票信息”',
        url: 'intro-yanzheng-2.png'
    }, {
        x: 388,
        y: 301,
        title: '发票验证',
        info: '点击“添加开票信息”',
        url: 'intro-yanzheng-3.png'
    }, {
        x: 550,
        y: 1283,
        title: '发票验证',
        info: '填写发票完整信息，点击“保存”',
        url: 'intro-yanzheng-4.png'
    }, {
        x: 555,
        y: 423,
        title: '发票验证',
        info: '选择“扫码查询”后，请对准发票二维码进行扫描',
        url: 'intro-yanzheng-5.png'
    }, {
        x: 580,
        y: 1274,
        title: '发票验证',
        info: '出现校验结果后，可点击“继续校验”，或点击“关闭”结束验证',
        url: 'intro-yanzheng-6.png'
    }, {
        x: 159,
        y: 426,
        title: '发票验证',
        info: '选择“手动校验”',
        url: 'intro-yanzheng-7.png'
    }, {
        x: 372,
        y: 1048,
        title: '发票验证',
        info: '选择“手录发票”',
        url: 'intro-yanzheng-8.png'
    }, {
        x: 372,
        y: 1257,
        title: '发票验证',
        info: '输入正确的发票信息，点击确定',
        url: 'intro-yanzheng-9.png'
    }, {
        x: 580,
        y: 1274,
        title: '发票验证',
        info: '出现校验结果后，可点击“继续校验”，或点击“关闭”结束验证',
        url: 'intro-yanzheng-10.png'
    }, {
    	x: 372,
        y: 1158,
        title: '发票验证',
        info: '选择“仅校验纳税人识别号”',
        url: 'intro-yanzheng-8.png'
    }, {
        x: 373,
        y: 434,
        title: '发票验证',
        info: '输入发票上的纳税人识别号，点击检查',
        url: 'intro-yanzheng-11.png'
   		
    }
    ]
    </script>
</head>

<body>
    <div id="intro-wrap">
        <div id="intro-con">
            <img draggable="false" id="mobile-bg" src="./images/mobile-bg.png" alt="">
            <div id="intro-context">
                <img draggable="false" id="intro-img" src="" alt="">
                <div id="intro-info"><span></span></div>
                <div id="ball-scale"><div></div></div>
            </div>
            <img draggable="false" id="mobile-bg-original" src="./images/mobile-bg.png" alt="" alt="">
        </div>
        <div id="intro-page"><span id="intro-title"></span>(<span id="intro-nowPage"></span>/<span id="intro-allPage"></span>)</div>
    </div>
    <div id="mask-layer">
        <div id="modals">
            <img draggable="false" src="./images/study-talent.png" alt="">
            <div id="modals-text">
                <p>您的学习速度超过了90%的人！</p>
                <p>发票验证技能 GET √</p>
				<p>点击返回继续学习</p>
            </div>
        </div>
    </div>
</body>

</html>
