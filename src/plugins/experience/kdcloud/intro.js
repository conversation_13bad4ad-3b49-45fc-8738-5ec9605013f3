$(function() {
  var i = 0
  var introWrap = $('#intro-wrap')
  var introCon = $('#intro-con')
  var mobileBg = $('#mobile-bg')
  var introContext = $('#intro-context')
  var introImg = $('#intro-img')
  var introInfo = $('#intro-info')
  var introTouch = $('#ball-scale')
  var introTitle = $('#intro-title')
  var introNowPage = $('#intro-nowPage')
  var introAllPage = $('#intro-allPage')
  introImg.attr('src', './images/' + introArr[i].url)

  window.onload = function() {
    if (mobileBg.width() > 0) {
      introCon.css({
        width: mobileBg.width()
      })
    }
    var scaleFactor = $('#mobile-bg').height() / $('#mobile-bg-original').height()
    introContext.css({
      top: scaleFactor * 270,
      left: scaleFactor * 72,
      width: scaleFactor * introImg.width(),
      height: scaleFactor * introImg.height()
    })
    introImg.css({
      height: '100%',
      visibility: 'visible'
    })
    mobileBg.css('visibility', 'visible')

    function introFun() {
      if (i < introArr.length) {
        introImg.attr('src', './images/' + introArr[i].url)
        introInfo.find('span').html(introArr[i].info)
        introTitle.html(introArr[i].title)
        introNowPage.html(i + 1)
        introAllPage.html(introArr.length)

        if (scaleFactor * introArr[i].y - introInfo.height() - 30 < 0) {
          introInfo
            .removeClass('animated zoomIn')
            .css({
              top: scaleFactor * introArr[i].y + 30,
              visibility: 'visible'
            })
            .addClass('animated zoomIn')
        } else {
          introInfo
            .removeClass('animated zoomIn')
            .css({
              top: scaleFactor * introArr[i].y - introInfo.height() - 30,
              visibility: 'visible'
            })
            .addClass('animated zoomIn')
        }
        introTouch.css({
          display: 'block',
          left: scaleFactor * introArr[i].x - introTouch.width() / 2,
          top: scaleFactor * introArr[i].y - introTouch.height() / 2
        })
        i++
      } else {
        $('#mask-layer').show()
        $('#modals-button').click(function() {
          if (window.close()) {
            window.close()
          } else {
            history.go(-1)
          }
        })
      }
    }

    introFun()

    introTouch.click(function() {
      introFun()
    })
  }
})
