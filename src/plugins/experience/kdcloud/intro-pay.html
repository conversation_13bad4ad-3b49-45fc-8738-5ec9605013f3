<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="https://www.hosecloud.com/favicon.ico">
    <title>让报销成为快事一件</title>
    <script src="zepto.js"></script>
    <script src="intro.js"></script>
    <link rel="stylesheet" href="intro.css">
    <script type="text/javascript">

      // 下列数组中 x 代表呼吸灯中心点的x轴坐标
      // y 代表呼吸灯中心点的y轴坐标
      // title 代表页面下边分页的标题
      // info 代表呼吸灯上方的提示文字
      // url 代表当前引导页面的图片地址
      // 把相对应的数据填入就可以了
      // <br/>为换行标签

      var introArr = [
        {
          x: 152,
          y: 1210,
          title: '出纳支付',
          info: '点击“审批”',
          url: 'intro-pay-1.png'
        }, {
          x: 212,
          y: 1078,
          title: '出纳支付',
          info: '点击“待支付”，进入待支付列表',
          url: 'intro-pay-2.png'
        }, {
          x: 259,
          y: 362,
          title: '出纳支付',
          info: '选择需要支付的单据',
          url: 'intro-pay-3.png'
        }, {
          x: 619,
          y: 1286,
          title: '出纳支付',
          info: '确认单据信息，核对无误后点击“支付”',
          url: 'intro-pay-4.png'
        }, {
          x: 551,
          y: 983,
          title: '出纳支付',
          info: '确认付款银行、支付方式无误后点击“确认”',
          url: 'intro-pay-5.png'
        }
      ]
    </script>
</head>

<body>
<div id="intro-wrap">
    <div id="intro-con">
        <img draggable="false" id="mobile-bg" src="./images/mobile-bg.png" alt="">
        <div id="intro-context">
            <img draggable="false" id="intro-img" src="" alt="">
            <div id="intro-info"><span></span></div>
            <div id="ball-scale">
                <div></div>
            </div>
        </div>
        <img draggable="false" id="mobile-bg-original" src="./images/mobile-bg.png" alt="" alt="">
    </div>
    <div id="intro-page"><span id="intro-title"></span>(<span id="intro-nowPage"></span>/<span
            id="intro-allPage"></span>)
    </div>
</div>
<div id="mask-layer">
    <div id="modals">
        <img draggable="false" src="./images/study-talent.png" alt="">
        <div id="modals-text">
            <p>您的学习速度超过了90%的人！</p>
            <p>出纳支付技能 GET √</p>
            <p>点击返回继续学习</p>
        </div>
    </div>
</div>
</body>

</html>
