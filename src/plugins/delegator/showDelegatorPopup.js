/**************************************************
 * Created by nanyuanting<PERSON> on 31/07/2017 14:46.
 **************************************************/
import React from 'react'
import showPopup from '../../lib/popup/ShowPopup'

function wrapper(delegators, close) {
  return (
    <div className="select-delegator-wrapper">
      <div className="color-gray-light3 select-delegator-label">{i18n.get('选择提交人')}</div>
      <div className="select-delegator-list-wrapper">
        <div
          className="select-delegator-title"
          onClick={() => {
            close('self')
          }}
        >
          <span className="select-delegator-text flex">{i18n.get('为自己提交')}</span>
        </div>
        {delegators.map(item => {
          let { id, name, onClick } = item
          return (
            <div key={id} className="select-delegator-title" onClick={onClick}>
              <span className="select-delegator-text flex">{i18n.get('帮{__k0}提交', { __k0: name })}</span>
            </div>
          )
        })}
      </div>
      <div className="color-black-light select-delegator-label" onClick={() => close('cancel')}>
        {i18n.get('取消')}
      </div>
    </div>
  )
}

export default function(delegators) {
  return showPopup(delegators, wrapper)
}
