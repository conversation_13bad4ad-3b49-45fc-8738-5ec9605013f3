import './EkbMallOrderList.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { EnhanceLayer } from '@ekuaibao/enhance-layer-manager-mobile'
import { app } from '@ekuaibao/whispered'
//import LayerContainer from '../basic-elements/layer-container'
const LayerContainer = app.require("@basic-elements/layer-container")
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
// import ThirdImportList from '../thirdImport/thirdImport-list'
const ThirdImportList = app.require("@third-import/thirdImport-list")
// import { orderTypeMap } from '../thirdImport/thirdImport-maping'
const orderTypeMapFn = app.require("@third-import/thirdImport-order-type-map")

@EnhanceConnect(state => ({
  platFromList: state['@third-import'].platFromList
}))
@EnhanceTitleHook(props => i18n.get(i18n.get('导入') + orderTypeMapFn()[props.orderType]))
export default class EkbMallOrderList extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      loadingFinished: false
    }
    props.overrideGetResult(this.getResult)
  }

  componentWillMount = () => {}

  getResult = () => {
    for (let order of this.orders) {
      delete order.ekbChecked
    }
    return this.orders
  }

  getOrders = results => {
    this.orders = results
    this.props.layer.emitOk()
  }

  render() {
    let { orderImported = [], deleteOrders = [], orderType } = this.props
    return (
      <LayerContainer>
        <div className="expense-mall-wrapper">
          <ThirdImportList
            getOrders={this.getOrders}
            orderImported={orderImported}
            deleteOrders={deleteOrders}
            orderType={orderType}
          />
        </div>
      </LayerContainer>
    )
  }
}
