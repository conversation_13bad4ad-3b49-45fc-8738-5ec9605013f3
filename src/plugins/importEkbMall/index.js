import { app } from '@ekuaibao/whispered'
/**
 *  Created by z<PERSON><PERSON><PERSON>
 */

// import { fnOpenOrder } from './utils'

// import SVG_IMPORT_HOTEL from '../../images/import-menu-hotel.svg'
// const SVG_IMPORT_HOTEL = app.require('@images/import-menu-hotel.svg')
// import SVG_IMPORT_FLIGHT from '../../images/import-menu-flight.svg'
// const SVG_IMPORT_FLIGHT = app.require('@images/import-menu-flight.svg')
// import SVG_IMPORT_TRAIN from '../../images/import-menu-train.svg'
// const SVG_IMPORT_TRAIN = app.require('@images/import-menu-train.svg')

const flight = () => ({
  icon: app.require('@images/import-menu-flight.svg'),
  title: i18n.get('机票'),
  type: 'flight',
  group: 'ekbMall',
  async onClick(...args) {
    const { fnOpenOrder } = await import('./utils')
    return fnOpenOrder('flight', ...args).then(result => {
      return { type: 'didi', data: result }
    })
  }
})

const train = () => ({
  icon: app.require('@images/import-menu-train.svg'),
  title: i18n.get('火车'),
  type: 'train',
  group: 'ekbMall',
  async onClick(...args) {

    const { fnOpenOrder } = await import('./utils')

    return fnOpenOrder('train', ...args).then(result => {
      return { type: 'didi', data: result }
    })
  }
})

const hotel = () => ({
  icon: app.require('@images/import-menu-hotel.svg'),
  title: i18n.get('酒店'),
  type: 'hotel',
  group: 'ekbMall',
  async onClick(...args) {

    const { fnOpenOrder } = await import('./utils')

    return fnOpenOrder('hotel', ...args).then(result => {
      return { type: 'didi', data: result }
    })
  }
})

//合思商城机酒火
const hose_mall_hotel = () => ({
  icon: app.require('@images/import-menu-hotel.svg'),
  title: i18n.get('酒店'),
  type: 'hose_mall_hotel',
  group: 'ekbMall',
  async onClick(...args) {
    const { fnOpenOrder } = await import('./utils')

    return fnOpenOrder('hose_mall_hotel', ...args).then(result => {
      return { type: 'didi', data: result }
    })
  }
})

const hose_mall_flight = () => ({
  icon: app.require('@images/import-menu-flight.svg'),
  title: i18n.get('机票'),
  type: 'hose_mall_flight',
  group: 'ekbMall',
  async onClick(...args) {
    const { fnOpenOrder } = await import('./utils')
    return fnOpenOrder('hose_mall_flight', ...args).then(result => {
      return { type: 'didi', data: result }
    })
  }
})

const hose_mall_train = () => ({
  icon: app.require('@images/import-menu-train.svg'),
  title: i18n.get('火车'),
  type: 'hose_mall_train',
  group: 'ekbMall',
  async onClick(...args) {
    const { fnOpenOrder } = await import('./utils')
    return fnOpenOrder('hose_mall_train', ...args).then(result => {
      return { type: 'didi', data: result }
    })
  }
})

export default [
  {
    id: '@importEkbMall',
    reducer: () => require('./importEkbMall.reducer').default
  },

  {
    point: '@bill:third:import',
    onload: () => [flight(), train(), hotel(), hose_mall_hotel(), hose_mall_flight(), hose_mall_train()]
  },

  {
    point: '@@layers',
    prefix: '@importEkbMall',
    onload: () => require('./layers').default
  }
]
